# Waller Maciel: esse arquivo de propriedades deve armazenar informa\u00E7\u00F5es onde, anteriormente,
# estava sendo definidos em Classes (.class), obrigando uma compila\u00E7\u00E3o nova para isso.
# Vide constantes em SuperControle.java:
# - Cada propriedade aqui tem que possuir uma constante com o seu nome!
# As keywords com @ (arroba) n\u00E3o sobrescritas pelo Script de deployment da aplica\u00E7\u00E3o,
# portanto, muita aten\u00E7\u00E3o ao alter\u00E1-las!
urlWikiRaiz = https://wiki.pactosolucoes.com.br/content/index.php
urlBaseConhecimento = https://pactosolucoes.com.br/ajuda/conhecimento/
urlWiki = https://wiki.pactosolucoes.com.br/content/index.php/ZillyonWeb:
urlWikiCRM = https://wiki.pactosolucoes.com.br/content/index.php/CRMWeb:
urlWikiCE = https://wiki.pactosolucoes.com.br/content/index.php/Central_de_Eventos:
urlWikiFIN = https://wiki.pactosolucoes.com.br/content/index.php/FinanceiroWeb:
urlWikiSO = https://wiki.pactosolucoes.com.br/content/index.php/ZillyonWeb:Sorteio
urlWikiES = https://wiki.pactosolucoes.com.br/content/index.php/ZillyonWeb:Cadastros:Controle_de_Estoque
urlWikiCON =https://wiki.pactosolucoes.com.br/content/index.php/ZillyonWeb:Cadastros:Gerador_de_Consulta
urlWikiGST = https://wiki.pactosolucoes.com.br/content/index.php/Gest\u00E3oStudio:
urlWikiVersoes = https://wiki.pactosolucoes.com.br/content/index.php/ZillyonWeb:Zillyon:Vers%C3%B5es
urlWikiVersaoAtual = https://wiki.pactosolucoes.com.br/content/index.php/ZillyonWeb:Zillyon:Vers%C3%B5es
urlUtilsZAcesso = http://app.pactosolucoes.com.br/ZAW_Utils
pathImagensEmail = /opt/imgs_email
pathLogsZAcesso = @DIR_ZAW_LOGS@
pathDataSourceZAcesso = @DIR_ZAW_DATASOURCE@
pathUtilsZAcesso = @DIR_ZAW_UTILS@
urlDataSourceZAcesso = @ZAW_DATASOURCE@
urlNotificacaoAcesso = @ZAW_URL_NOTF_ACESSO@
urlOamdEmpresas= @URL_OAMD_EMPRESAS@
urlMidiaSocial=@URL_MIDIA_SOCIAL@
#urlApiApp=@URL_API_APP@
urlApiApp=https://app-do-aluno-unificado.web.app/geral/pushService
urlIntegradorOamd= @URL_IOF@
DISCOVERY_URL=@DISCOVERY_URL@
path-id-persona=@AUTH_SECRET_PERSONA_PATH@
AUTH_SECRET_PERSONA_PATH=@AUTH_SECRET_PERSONA_PATH@
AUTH_SECRET_PATH=@AUTH_SECRET_PATH@
AUTH_SECRET_ZW_PATH=@AUTH_SECRET_ZW_PATH@
KEYWORD_PATH=@KEYWORD_PATH@
instanciasNotificar = @ZW_INSTANCIAS@
urlObterBanners = @ZW_BANNERS@
usuarioHTTPAcesso = ZillyonAcessoWeb
senhaHTTPAcesso = 6X?AY@B({N
senhaPactoBr=G]DKG61M
senhaPactoBrTeste=123
RECORRENCIA_USER_PASSWORD=6J+3;-[xd@I.:]Z

#PRODUCAO NFSE
urlNFSe = @URL_NFSE@
#urlNFSe = http://desenv-luiz:8443/pactonfse/soap/IPactoNFSE

#PRODUCAO NFSE CONF
urlNFSeRestAdmin = @nfse-rest-admin-path@

#PRODUCAO NFSE REST
urlNFSeRest = @URL_NFSE_REST@
#urlNFSe = http://desenv-luiz:8443/pactonfse/soap/IPactoNFSE

#PRODUCAO FINANCEIRO PACTO
urlFinanceiroPacto=@URL_FINANCEIRO_PACTO@
#urlFinanceiroPacto=http://desenv-luiz:8443/financeiroWS/soap/IFinanceiroWS

urlBoletosFinanceiroPacto=https://pacto-blt.s3-sa-east-1.amazonaws.com/
urlSolicitacao=http://app.pactosolucoes.com.br/ucp/atendimento
urlOamd=http://host.docker.internal:8202/NewOAMD
urlOamdSegura=@URL_OAMD_SEGURA@
urlTreino=@URL_TREINO@
urlSuporte=@URL_SUPORTE@
#urlTreino=http://***************:8084/TreinoWeb
urlTreinoGooglePlay=https:\/\/play.google.com/store/apps/details?id=com.pacto
urlTreinoAppleStore=http:\/\/itunes.apple.com/us/app/treino/id862662527
urlStudioCalendar=http://app.pactosolucoes.com.br/studiocalendar/
googleClientID=403136198769.apps.googleusercontent.com
urlLogin=@URL_LOGIN@
redirectLoginServidorLocal=@REDIRECT_LOGIN_SERV_LOCAL@
#ZAW - Queue: true/false responsavel por enfileirar os registros de acesso ou nao
enableZawQueue=@ZAW_QUEUE@
cookieFailover=@COOKIE_FAIL@
#enableZawQueue=true

#SFTP - TESTE
#hostSFTP = app.pactosolucoes.com.br
#portSFTP = 22
#userSFTP = gqs
#pwdSFTP = gqs@pacto
#diretorioRemotoTIVIT_OUT = /opt/ZW_SFTP_TESTE/gqs/OUT
#diretorioRemotoTIVIT_IN = /opt/ZW_SFTP_TESTE/gqs/IN

#diretorioLocalAnt = D:/ant/
diretorioLocalAnt = /opt/deploy/ant/
diretorioLocalTIVIT = /opt/ZW_TIVIT/
diretorioLocalDownloadTIVIT = down/
diretorioLocalUploadTIVIT = up/
emailsNotificarValidacaoBI=<EMAIL>,<EMAIL>
emailSquadAdmAbrirTicket=@EMAIL_SQUAD_ADM_OPEN_TICKET@
uriInicial = /faces/tela1.jsp
uriPlano = /faces/planoCons.jsp
uriTurma = /faces/turmaCons.jsp
uriConfiguracao = /faces/configuracaoSistemaForm.jsp
uriPrecadastro = /faces/preCadastro.jsp
uriCadastros = /faces/telaModulo.jsp
uriClientes = /faces/clientesNav.jsp?page=clientes
uriCliente = /faces/clienteNav.jsp?page=cliente&matricula=
uriColaborador = /faces/colaboradorCons.jsp
uriUsuario = /faces/usuarioCons.jsp
uriPerfil = /faces/perfilAcessoCons.jsp
uriPessoa = /faces/pessoaCons.jsp
uriCanal = /faces/minhaContaPacto.jsp
uriStore = /faces/minhaContaPacto.jsp
uriBI = /faces/indexRelatorio.jsp
uriAgenda = /faces/pages/estudio/indexEstudio.jsp
uriFinan = /faces/pages/finan/telaInicialFinan.jsp
uriBIFinan = /faces/pages/finan/relatoriosMobile.jsp
uriCRM = /faces/telaInicialCRM.jsp
uriNota = /faces/notaFiscal.jsp
uriVendaAvulsa = /faces/vendaAvulsaForm.jsp
uriCarteiras = /faces/telaInicialCarteiras.jsp
uriHistoricoBV = /faces/questionarioClienteCRMForm.jsp
uriBICRM=/faces/telaBICRM.jsp
uriBIMobile=/faces/detalheBI.jsp
#validarIps = false
empresasPermitidasAlterarDataBase=866338e1a2d8b25f8aedd24fb64dc967
dataLimiteEmpresaAlterarDataBase=10/06/2017
diretorioArquivos = @DIR_ZW_ARQ@
diretorioFotos = /opt/zw-photos/
fotosParaNuvem=@FOTOS_NUVEM@
typeMidiasService=@TIPO_MIDIA@
urlFotosNuvem=@URL_FOTOS_NUVEM@
urlNFENuvem=@URL_NFE_NUVEM@
myFaqUrlBase=@MY_FAQ_URL@
myFaqEmpresas=@MY_FAQ_EMPRESAS@
validOAMD=@VALIDAR_USUARIO_OAMD@
urlModuloNFSe=@URL_MODULO_NFSE@
myUpUrlBase=@MY_URL_UP_BASE@
loadInstancesFromCloud=@LOAD_INSTANCES_CLOUD@
prefixoIarUsuariontanciasCloud=@PREFIXO_INSTANCIAS_CLOUD@
AWSAccessKeyId=@AWSAccessKeyId@
AWSSecretKey=@AWSSecretKey@
AWSRegion=@AWSRegion@
integracaoWiki=@INTEGRACAO_WIKI@
liberarAcessoFatorZW=false
urlMedidor=@URL_MEDIDOR@
buscarConhecimentoUCP=@BUSCAR_CONHECIMENTO_UCP@
validarVersaoBD=@VALIDAR_VERSAO_BD@
validarBloqueioOutros=@VALIDAR_BLOQUEIO_OUTROS@
emailComercialPacto=@EMAIL_COMERCIAL_PACTO@
smtpEmailRobo=@SMTP_EMAIL_ROBO@
smtpEmailNoReply=@SMTP_EMAIL_NOREPLY@
smtpLoginRobo=@SMTP_LOGIN_ROBO@
smtpSenhaRobo=@SMTP_SENHA_ROBO@
smtpConexaoSeguraRobo=@SMTP_CONEXAOSEGURA_ROBO@
smtpServerRobo=@SMTP_SERVER_ROBO@
iniciarTLS=@INICIAR_TLS@
arrayCaixasPostaisSFTP=@ARRAY_CAIXAS_POSTAIS_SFTP@
arrayCaixasPostaisExtratoSFTP=@ARRAY_CAIXAS_POSTAIS_EXTRATO_SFTP@
serverSFTPSlave=@SERVER_SFTP_SLAVE@
habilitarLembreteSolicitacoes=@HABILITAR_LEMBRETE_SOLICITACOES@
urlDashBoard= @URL_DASHBOARD@
useBounceService=@USE_BOUNCE_SERVICE@
#Vindi
urlApiVindiProducao=https://app.vindi.com.br:443/api/v1
urlApiVindiSandbox=https://sandbox-app.vindi.com.br/api/v1
nomeProdutoPadraoVindi=PARCELA COBRANCA
#Cielo
urlApiCieloRequisicaoProducao=https://api.cieloecommerce.cielo.com.br
urlApiCieloConsultaProducao=https://apiquery.cieloecommerce.cielo.com.br
urlApiCieloRequisicaoSandbox=https://apisandbox.cieloecommerce.cielo.com.br
urlApiCieloConsultaSandbox=https://apiquerysandbox.cieloecommerce.cielo.com.br
#Rede
urlApiRedeProducao=https://api.userede.com.br/erede/v1
urlApiRedeSandbox=https://api.userede.com.br/desenvolvedores/v1
urlApiRedeConciliacaoProducao=https://api.userede.com.br/redelabs
clientIdRede=128115fb-e118-416a-a827-fc56e11a6b88
clientSecretRede=ptu7GP7UPa
userNameRede=<EMAIL>
passwordRede=I2A7S1y:hw__
#Getnet
urlApiGetnetProducao=https://api.getnet.com.br
getNetECommerceOrgIdProducao=k8vif92e
urlApiGetnetSandbox=https://api-sandbox.getnet.com.br
getNetECommerceOrgIdSandbox=1snn5n9w
utilizarIpServidorGetnet=true
#Stone
usarMockStone=false
urlApiStoneProducao=https://e-commerce.stone.com.br
urlApiStoneSandbox=https://sandbox-auth-integration.stone.com.br
urlApiStoneOnlineConciliationV1=https://conciliation.stone.com.br/v1/merchant/
urlApiStoneOnlineConciliationV2=https://conciliation.stone.com.br/conciliation-file/v2.2/
urlApiStoneConnect=https://api.pagar.me/core/v5
#Mundipagg
urlApiMundipagg=https://api.mundipagg.com/core/v1
#Pagar.me
urlApiPagarme=https://api.pagar.me/1
serviceRefererNamePagarMePacto=60a9659a7b8016001905ab77
#Pagar.me v5 (Stone)
urlApiStone_V5=https://api.pagar.me/core/v5
#Stripe
urlApiStripe=https://api.stripe.com/v1
#Caixa Transações Online
urlApiCaixaPagamentosRequisicaoProducao=https://esitef.softwareexpress.com.br/e-sitef/api
urlApiCaixaPagamentosRequisicaoSandbox=https://esitef-homologacao.softwareexpress.com.br/e-sitef/api
#PagBank
urlApiPagBankProducao=https://api.pagseguro.com/
urlApiPagBankSandbox=https://sandbox.api.pagseguro.com/
urlApiConnectPagBankProducao=https://connect.pagbank.com.br/
urlApiConnectPagBankSandbox=https://connect.sandbox.pagbank.com.br/
clientIdAplicacaoPactoPagbankProducao=2394bf72-3806-44f4-9fdc-7ea7de9d3efe
clientSecretAplicacaoPactoPagbankProducao=b3f5473d-58c8-4fb2-96bd-47391cbb1b93
clientIdAplicacaoPactoPagbankSandbox=1ae392a6-08e1-4866-896d-f06261800d3e
clientSecretAplicacaoPactoPagbankSandbox=a06928f8-e499-4af3-8b69-c6946c13cdc1
scopeUtilizarAutorizarConnectPagbank=payments.read+payments.create+payments.refund+accounts.read+payments.split.read+checkout.create+checkout.view+checkout.update
redirectUriConnectPagBank=@REDIRECT_URI_CONNECT_PAGBANK@
tokenContaPactoPagBankSandbox=ff031376-90e9-4b95-8f0a-cab58b3ea8be02e2509a4e46b5f4f4f23eedfce67b011222-8aae-42ff-9d45-1147ed260c08
tokenContaPactoPagBankProducao=05a0b56c-ef8d-43ae-abcf-455aa8dc0995c617a9884032b81f3e5ad425f27e3971d357-4d38-4a54-9f90-faf66de14292

authenticationKeyCappta=2845E967A929484DA0E790871DC3C557
urlApiChatGoogleCappta=https://chat.googleapis.com/v1/spaces/AAAAdHIrmHE/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=eu_WWUoBkE5f7wOqOHx3ghQ5zDw2XhFHHpxu8M0T9xI
cpftestevendasonline=368.933.923-54
apresentarHotjar=@APRESENTAR_HOTJAR@
utilizarSinteticoMs=@UTILIZAR_SINTETICO_MS@
urlRecursoEmpresa = @URL_RECURSO_EMPRESA@
usarUrlRecursoEmpresa = @USAR_URL_RECURSO_EMPRESA@
enviarEmailVendasOnline = @ENVIAR_EMAIL_VENDAS_ONLINE@
urlZWAUTO=@URL_ZW_AUTO@
urlAPI=@URL_API@
urlGameGeral=https://game.pactosolucoes.com.br/core
#urlAPIGymPass=https://tlsapi.gympass.com
urlAPIGymPass=https://api.gympass.com
urlGymPassMs=https://ms1.pactosolucoes.com.br/gympass
urlGoGood=@URL_GOGOOD@
goGoodAppId=@GOGOOD_APP_ID@
goGoodAppSecret=@GOGOOD_APP_SECRET@
urlMockZWServer=http://mock.pactosolucoes.com.br:8082/app8/mock
urlAPIGympass=https://api.gympass.com/transacoes/validar_numero.json
#urlMockZWServer=http://localhost:8080/tronco-novo/mock
integraProtheus=@INTEGRA_PROTHEUS@
validarInadimplencia=@VALIDAR_INAD@
urlValidarInadimplencia=@URL_INAD@
tokenValidarInadimplencia=@TOKEN_INAD@
urlIntegracaoProtheus=@URL_PROTHEUS@
urlAplicacao=@URL_APLICACAO@
#urlServicoNotaFiscal=https://nfe.pactosolucoes.com.br/servico-nota-fiscal
urlServicoNotaFiscal=@URL_SERV_NOTA_FISCAL@
urlVendasOnline=@URL_VENDAS_ONLINE@
urlBaseOptIn=@URL_BASE_OPTIN@
urlDocumentacaoApi=@URL_DOC_API@
urlSiteApp=@URL_SITE_APP@
urlAplicacaoCentral=https://app.pactosolucoes.com.br/app
urlAPIGeoitdDev=http://geoitddev.geocom.com.uy:8557/v2/ITDService/
urlAPIGeoitd=http://geoitd.geocom.com.uy:8557/v2/itdservice/
bannerretro=false
chavesdesconsiderarretro=3a42debb1531138d8cb3b934ef030625_teste
GCLOUD_API_KEY=@GCLOUD_API_KEY@
GERAR_PRINTS_ACOES_USUARIO=@GERAR_PRINTS_ACOES_USUARIO@
LIMITE_PRINTS_ACOES_USUARIO=@LIMITE_PRINTS_ACOES_USUARIO@
URL_HTTPS_PLATAFORMA_PACTO=@URL_HTTPS_PLATAFORMA_PACTO@
URL_HTTP_PLATAFORMA_PACTO=@URL_HTTP_PLATAFORMA_PACTO@
URL_HTTP_ZW_HOMOLOGACAO=@URL_HTTP_ZW_HOMOLOGACAO@
URL_HTTPS_ZW_HOMOLOGACAO=@URL_HTTPS_ZW_HOMOLOGACAO@
clientApplicationKeyConciliation=574fafe6-e3fd-43f4-b712-cf4cc5625c3a
secretKeyConciliation=YWQzMGQyNTgtYzg3OC00MDlhLTk2MGUtZTg2ODE1MWY5ZWU0
xAuthorizationRawData=PACTO_ZW
debugJDBC=@DEBUG_JDBC@
atualizacaoCadastral=true
stoneOpenBankProducao=@STONE_OPEN_BANK_PRODUCAO@
VERSAO_SISTEMA=@VERSAO_SISTEMA@
API_OAMD_KEY=61bf899f65af7da766b473fb69fe0e17e67d8f919a91d094d73ed06ae074f10c2e3bf80a0a065eb57f80de7bdc43e363bf
tempoAguardarRemessa=20000
#Exemplo horarioEnvioRemessaGetnet "10:11-22:23" das 10 horas at\u00E9 as 11 horas e das 22 horas at\u00E9 as 23 horas
horarioEnvioRemessaGetnet=
ativarGoogleAnalytics=@ATIVAR_GOOGLE_ANALYTICS@
ativarWeHelp=@ATIVAR_WEHELP@
ativarAnuncioVitio=@ATIVAR_ANUNCIO_VITIO@
URL_SERVICO_INTEGRACAO_SENDY=https://integrador-sendy.pactosolucoes.com.br/api/
#URL_SERVICO_INTEGRACAO_SENDY=http://localhost:8080/api/

EMAIL_PACTO_SENDY=<EMAIL>
BUSCAR_REMOTO_EXTRATO_RETORNO_REMESSA=@BUSCAR_REMOTO_EXTRATO_RETORNO_REMESSA@
enviarEmailErroCreditoDCC=false
urlIntegracoesService=@URL_INTEGRACOES_SERVICE@
urlIntegracoesMS=@URL_SERVICO_INTEGRACOES_MS@
ambienteDesenvolvimentoTeste=@AMBIENTE_DESENVOLVIMENTO_TESTE@
ambienteSwarmParaSendy=teste
tokenMailgun=@TOKEN_MAIL_GUN@
domainMail=@DOMAIN_MAIL@
#PIX BB
urlApiPixBBSandbox=https://api.sandbox.bb.com.br/pix/v1
urlApiPixBBProducao=https://api.bb.com.br/pix/v1
urlApiPixBBAuthSandbox=https://oauth.sandbox.bb.com.br/oauth/token
urlApiPixBBAuthProducao=https://oauth.bb.com.br/oauth/token
urlApiPixBBSandboxV2=https://api.sandbox.bb.com.br/pix/v2
urlApiPixBBProducaoV2=https://api-pix.bb.com.br/pix/v2
#PIX Bradesco
urlApiPixBradescoSandbox=https://qrpix-h.bradesco.com.br
urlApiPixBradescoProducao=https://qrpix.bradesco.com.br
urlApiPixBradescoAuthSandbox=https://qrpix-h.bradesco.com.br/oauth/token
urlApiPixBradescoAuthProducao=https://qrpix.bradesco.com.br/oauth/token
urlApiWebhookPixBradesco=https://qrpix.bradesco.com.br/v1/spi/webhook
#PIX Inter
urlApiPixInterProducao=https://cdpj.partners.bancointer.com.br/pix/v2
urlApiPixInterOAuthProducao=https://cdpj.partners.bancointer.com.br/oauth/v2
#Pix Itau
urlApiPixItauProducao=https://secure.api.itau/pix_recebimentos/v2
urlApiPixItauSandbox=https://devportal.itau.com.br/sandboxapi/itau-ep9-gtw-pix-recebimentos-ext-v2/v2
urlApiPixItauAuth=https://sts.itau.com.br/api/oauth/token
urlApiPixItauAuthSandbox=https://sandbox.devportal.itau.com.br/api/oauth/jwt
#API Pcert Pacto
urlAPIPcertPacto=https://pcert.pactosolucoes.com.br
#PIX Santander
urlApiPixSantanderSandbox=https://trust-pix-h.santander.com.br/api
urlApiPixSantanderProducao=https://trust-pix.santander.com.br/api
urlApiPixSantanderAuthSandbox=https://trust-pix-h.santander.com.br/oauth/token
urlApiPixSantanderAuthProducao=https://trust-pix.santander.com.br/oauth/token
#DCC PagoLivre
urlApiPagoLivreProducao=https://api.pagolivre.com.br/api/v4
urlApiPagoLivreSandbox=https://api.sbx.pagolivre.com.br/api/v4
urlApiGatewayPagoLivreProducao=https://gateway.pagolivre.com.br/api
urlApiGatewayPagoLivreSandbox=https://gateway.sbx.pagolivre.com.br/api
urlApiPagoLivreConciliacaoProducao=https://api.pagolivre.com.br/api/v2
tokenPagoLivreProducao=****************************************************************************************************************************
tokenPagoLivreSandbox=********************************************************************************************************************************
tokenGatewayPagoLivreProducao=d4510814-a5eb-4690-a582-198071fa2c10
tokenGatewayPagoLivreSandbox=8e2286b5-0660-4bf9-aedb-79f2d28e7323
chaveCriptSenhaUserPagoLivre=PaGoLiVrE
#DCC PagoLivre - FacilitePay
tokenPagoLivreFacilitePayProducao=********************************************************************************************************************************
tokenPagoLivreFacilitePaySandbox=********************************************************************************************************************************
#DCC PinBank
urlApiPinBankProducao=https://pinbank.com.br/services/api
urlApiPinBankSandbox=https://dev.pinbank.com.br/services/api
#DCC One Payments
urlApiOnePayment=https://onepayment.transactiongateway.com/api/transact.php
urlApiOnePaymentQuery=https://secure.nmi.com/api/query.php
#DCC Ceopag
urlApiCeopagProducaoPortal=https://portal-api.aditum.com.br
urlApiCeopagProducaoGateway=https://payment.aditum.com.br
urlApiCeopagProducaoReconciliation=https://reconciliation-api.aditum.com.br
urlApiCeopagProducaoWebhook=https://webhook.aditum.com.br
urlApiCeopagProducaoAuth=https://portal.aditum.com.br
urlApiCeopagSandboxPortal=https://portal-dev.aditum.com.br
urlApiCeopagSandboxGateway=https://payment-dev.aditum.com.br
urlApiCeopagSandboxReconciliation=https://reconciliation-dev.aditum.com.br
urlApiCeopagSandboxWebhook=https://webhook-dev.aditum.com.br
urlApiCeopagSandboxAuth=https://dashboard-dev.aditum.com.br

#Boleto ITAU
chaveDesencriptItauOnline=itauOnlineEncodedPacto@4InpluaI@
urlApiItauToken=https://sts.itau.com.br/api/oauth/token
urlApiItauRegistro=https://api.itau.com.br/cash_management/v2
urlApiItauWebhook=https://boletos.cloud.itau.com.br/boletos/v3
urlApiItauTokenSandBox=https://sandbox.devportal.itau.com.br/api/oauth/jwt
urlApiItauRegistroSandBox=https://sandbox.devportal.itau.com.br/itau-ep9-gtw-cash-management-ext-v2/v2
#Asaas
urlApiAsaasProducao=https://www.asaas.com/
urlApiAsaasSandbox=https://sandbox.asaas.com/
#Asaas apiKey conta Pai
apiKeyAsaasPactoProducao=$aact_YTU5YTE0M2M2N2I4MTliNzk0YTI5N2U5MzdjNWZmNDQ6OjAwMDAwMDAwMDAwMDAzMDc3OTQ6OiRhYWNoX2JhNjNlYmJjLTRjMzEtNGYyNC05YzUxLWRkMTFlY2ExZDhiYg==
apiKeyAsaasPactoSandbox=$aact_YTU5YTE0M2M2N2I4MTliNzk0YTI5N2U5MzdjNWZmNDQ6OjAwMDAwMDAwMDAwMDAwNTYzNDk6OiRhYWNoX2I4MzQxODQyLWNhZDktNDg2NS04YmIzLWU5NTllODZiZjViMg==

#Boleto CAIXA
urlApiCaixaManutencao=https://barramento.caixa.gov.br/sibar/ManutencaoCobrancaBancaria/Boleto/Externo
urlApiCaixaConsulta=https://barramento.caixa.gov.br/sibar/ConsultaCobrancaBancaria/Boleto

#Boleto Banco Brasil
urlApiBoletoBancoBrasilSandbox=https://api.hm.bb.com.br/cobrancas/v2/boletos
urlApiBoletoBancoBrasilProducao=https://api.bb.com.br/cobrancas/v2/boletos

#Boleto
#essa chave de criptografia \u00E9 compartilhada com a API
chaveCriptoBoleto=p@CT0b0L3To
#Url Jenkins, Mailing
urlJenkins=@URL_JENKINS@
urlMailing=@URL_MAILING@
loggingOutputMascarade=@LOG_OUTPUT_MASCARADE@
logOutputDebug=@LOG_OUTPUT_DEBUG@
biMs=@BI_MS@
bloquearEmpresasNaoTipificadas=@BLOQUEAR_EMPRESAS_NAO_TIPIFICADAS@
#Certificado Privado Pacto
uriCertPrivado=/br/com/pactosolucoes/comuns/util/certificado_pacto.pfx
senhaCertPrivado=2727091f000c5bd978fc
chaveCriptCertPrivado=PaSsCeRt

#PCert Pacto
senhaDecriptPCertPrivadoPfx=15d556ea2cd8

pathPrivateKeyTemplate=@PRIVATE_KEY_TEMPLATE@
enableLastActionTime=@ENABLE_LAST_ACTION_TIME@
#Hotsite
protocoloHttps=https://
dominioHotsite=.hotsite.in
ipServidorHotsite=************

#Pluggy
URL_API_PLUGGY=https://api.pluggy.ai
clientIdPluggy=e58c91c8-dc9b-4181-af90-ae6aa7f73361
clientSecretPluggy=e1e41d0b-4080-49f5-a1f9-6df787452381

#Kobana
URL_API_KOBANA_PRODUCAO=https://api.kobana.com.br/v2
URL_API_KOBANA_SANDBOX=https://api-sandbox.kobana.com.br/v2
TOKEN_PACTO_API_KOBANA_PRODUCAO=yIt7T5U0NBR_0tyIpJvalxh2DGtTsfdtJM3RDAFctxs
TOKEN_PACTO_API_KOBANA_SANDBOX=YEqK15ebbQNitIbvmbnDzwKpHzaRoyiGLiAPvtI1r4M

enableCountdown=@ENABLE_COUNT_DOWN@
enableMenuZwUI=@ENABLE_MENU_ZW_UI@
tokenAcessoAPICliente=@TOKENS_ACESSO_API_CLIENTE@
tokenAcessoAPIApps=@TOKENS_ACESSO_API_APPS@
pontoInterrogacaoMs=https://ms1.pactosolucoes.com.br/pontointerrogacao
negociacaoHabilitado=teste|athosgo|a394e8647be55f2d7a425e2e405ee4c8|810f691394b9cf79d33459a08861f3ed|7f42daeea134b97087191b29c26aeeb5|1799ef116c74cad1dfc5051bc4be3c7a|899b14f87dd9d99e7cbf1be5a20eb3e|e053f391f7e6bd2971ea834203183c15|d8304f3e08449e7d4e44686caf6557d2|dc16c8b86b075c9b55010696970d8a18|9d9b723743333de879ac676964c81e8d|3cf5f2b3194a380ec96d6728587fbe45|235ce79fd417cfcffe66d94107274ad9|afb1189f3eae92b0ebc9d3985c8ecae7|5a574e234d5815adbd0eff00971bbee|622b243ee1a2bcfd464986cd5a513f84|6220ae215a785184197dc921908942f8|9bc9eb67cd1bfb1f4b3f635527ea790e|26cf9dd3650becddb5c971d95fd21457|13fb2657436889b1acadfca3c1790302|88a19d0561af3d4d881b67c3a3805e07|cddc05b1854f0bc329d506f75985af9f|d1fa1cf40bd260ac4e8c241c261d2fb9|21aa51ee56ddda2e38c30628e5ed3774|980aec2daafe468de93f42b4fe2d0471|c89c45cc3aaa799d524f77afa8f78a62|d612b2e0f6d26dadeefe349eca60fb5b|59495f2ed2f8694d2efa7958ad0f30e9|b07003e0f8bb6a470609391e1f9c39cb|3ecbb524e14f3bc61244f9ef83bac01d|1a45d3f2031d59cc6ce026b8de89a6c5|8e061a52796386f0ad8148107aa7377a|84e134da6f97c5a48ed2a5d5945403d3|db578f0b87de74e77f9d7a7e5d799c6b|d700658fa3a080e7b561bb0bd3fde8bb|90db430d528234b70eb19d8dacdb67b8|84fcf98dd001bf5b86619d22120038650
tokenPushMobile=******************
tokenPushMobileAppAluno=@TOKEN_PUSH_MOBILE_APP_ALUNO@
urlAppDoAlunoUnificado=@URL_APP_ALUNO_UNIFICADO@
permitirEnvioNotificacaoPushAula=@PERMITIR_ENVIO_NOTIFICACAO_PUSH_AULA@
tokenApiWagi=@TOKEN_API_WAGI@
tokenApiWagiSandbox=teste
urlServicoGetCardScope=http://localhost:7471/ServiceScope/ControleScopeAPI
markitingBanner=@MARKITING_BANNER
habilitaMarketing=@HABILITA_MARKETING@
habilitaClubeDeBeneficios=@HABILITA_CLUBE_DE_BENEFICIOS@
tempoSegundosExpirarCacheBanners=@TEMPO_SEGUNDOS_EXPIRAR_CACHE_BANNERS@
servidorMemCached=@SERVIDOR_MEMCACHED@
urlMarketingMs=@URL_MARKETING_MS@
uteisEmailSend=@UTEIS_EMAIL_SEND@
qtdLimitePactoPay=@QTD_LIMITE_PACTOPAY@
cancelarBoletoAoCancelarOuEstonarContrato=@CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO@
postmasterMailgun=<EMAIL>
postmasterFrom=<EMAIL>
brandChuckNorris=Chuck Norris
#Total Pass
totalPassApi=https://api.totalpass.com/service/v1/track_usages
totalPassApiValidate=https://api.totalpass.com/service/v1/track_usages/validate
validarTokenApiZW=@VALIDAR_TOKEN_API_ZW@
#Regua de Cobranca - FacilitePay
userMailFacilitePay=<EMAIL>
domainMailFacilitePay=fypay.com.br
passwordMailFacilitePay=@PASSWORD_MAIL_FACILITEPAY@
tempoSegundosExpiracaoTokenOperacao=@TEMPO_SEGUNDOS_EXPIRACAO_TOKEN_OPERACAO@
smsChaveFacilitePay=29deff42a2f1ebf336ee21b93e84491d
smsTokenFacilitePay=hgZtXr21J5yR@vDyZKWpaA==
tokenBitlyFacilitePay=
#Bitly
urlAPIBitlyV4=https://api-ssl.bitly.com/v4
habilitarNicho=@HABILITAR_NICHO@
habilitarCacheInitNicho=@HABILITAR_CACHE_INIT_NICHO@
validadeCacheNichoEmMinutos=@VALIDADE_CACHE_NICHO_EM_MINUTOS@
passWordImportadorTreino=@PASSWORD_IMPORTADOR_TREINO@
habilitarRecursoPadraoTelaCliente=true
enableCompanyWebhook=@ENABLE_COMPANY_WEBHOOK@
enableConciliadora=@ENABLE_CONCILIADORA@
verifyControllersAfterPhase=@VERIFY_CONTROLLERS_AFTER_PHASE@
chaveCriptoImportCc=P@cT0C@rD
tokenImportCc=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJvd25lciI6IlNpc3RlbWFQYWN0byIsInByb2Nlc3MiOiJJbXBvcnRDQyJ9.bagiD_M41DIb_2zM4qd92yV3YAguyvnQ9IXtUS8ChBU
habilitarFuncionalidadesBeta=@HABILITAR_FUNCIONALIDADES_BETA@
minimoNotasSolicitacao=@MINIMO_NOTAS_SOLICITACAO@
urlHubspotAPI=https://api.hubapi.com
maxGpt=@MAX_GPT@
biRiscoChurn=a394e8647be55f2d7a425e2e405ee4c8_acadamelhorsp_3ec289516f552b0b47ce777661bbb6b5_7c5240df40569710cfe2e44f529984b7_cd16aad669f2aa92243b9210b50c5419_f7b07788895d2b2d629c663ff357df8e_cc2bd365ab3e5ae0e2d53a7e33683650_14d8427dc0582233b6db7f47ebcfd4c_2366443b37686f5c012dcca87be2243c_efc2fc0fa6d5926fc12f60fc396a52c3_943c1927c5e14ef533b4d12e27a93136_1785fe760ffec28d848961e6aafc698c_3d3048c33f0666ac4d4898b6efd5896_1893b3fb3a4f0136c09dda36d812a8c1_854df607c1b70e321ae4e28db5b5e3d4_495afa1552fb8735271a281669f11937_a5cf52fb0ff117dddc5ec6c21ea6039
GOOGLE_APPLICATION_CREDENTIALS=/home/<USER>/Workspace/Pacto/Access-Keys/academia-de-verdade-a21ca50b8adb.json

senhaCriptFTP360PasswordServer=ce6daaae508f9a9089324be667ef5bf611c1000077
chaveCriptFTP360PasswordServer=P@cT0C@f36o
URL_ARAGORN_MS=@URL_ARAGORN_MS@
MOCK_ARAGORN_CARDS=true
URL_AUTENTICACAO_MS=@URL_AUTENTICACAO_MS@
URL_ENVIO_ACESSO_INTEG_PRATIQUE=@URL_ENVIO_ACESSO_INTEG_PRATIQUE@
TOKEN_ENVIO_ACESSO_INTEG_PRATIQUE=@TOKEN_ENVIO_ACESSO_INTEG_PRATIQUE@

