<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="FluxoCaixa" language="groovy" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="corFundoRetangulo" mode="Opaque" forecolor="#00FFFF" backcolor="#00FFFF">
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{nivelArvore}.intValue() == 1]]></conditionExpression>
			<style mode="Opaque" forecolor="#C0C0C0" backcolor="#C0C0C0">
				<pen lineColor="#C0C0C0"/>
			</style>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{nivelArvore}.intValue() == 2]]></conditionExpression>
			<style mode="Opaque" forecolor="#CCFFCC" backcolor="#CCFFCC">
				<pen lineColor="#98FB98"/>
			</style>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{nivelArvore}.intValue() == 3]]></conditionExpression>
			<style mode="Opaque" forecolor="#FFFFFF" backcolor="#FFFFFF">
				<pen lineColor="#FFFFFF"/>
			</style>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{nivelArvore}.intValue() == 4]]></conditionExpression>
			<style mode="Opaque" forecolor="#ADD8E6" backcolor="#ADD8E6">
				<pen lineColor="#ADD8E6"/>
			</style>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{nivelArvore}.intValue() == 5]]></conditionExpression>
			<style mode="Opaque" forecolor="#F5DEB3" backcolor="#F5DEB3">
				<pen lineColor="#F5DEB3"/>
			</style>
		</conditionalStyle>
	</style>
	<parameter name="tituloRelatorio" class="java.lang.String"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream"/>
	<parameter name="data1" class="java.lang.String"/>
	<parameter name="itemDataSource" class="net.sf.jasperreports.engine.data.JRBeanCollectionDataSource"/>
	<parameter name="mes1TotalFimPaginaReceitasPrevisto" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaReceitasRealizado" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaInvestimentosDespesasPrevisto" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaInvestimentosDespesasRealizado" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaSemInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaSemInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaComInvestimentosPrevisto" class="java.lang.String"/>
	<parameter name="mes1TotalFimPaginaComInvestimentosRealizado" class="java.lang.String"/>
	<parameter name="saldoFimPaginaReceitas" class="java.lang.String"/>
	<parameter name="saldoFimPaginaDespesas" class="java.lang.String"/>
	<parameter name="saldoFimPaginaInvestimentos" class="java.lang.String"/>
	<parameter name="saldoFimPaginaInvestimentosDespesas" class="java.lang.String"/>
	<parameter name="saldoFimPaginaSemInvestimentos" class="java.lang.String"/>
	<parameter name="saldoFimPaginaComInvestimentos" class="java.lang.String"/>
	<parameter name="variacaoFimPaginaReceitas" class="java.lang.String"/>
	<parameter name="variacaoFimPaginaDespesas" class="java.lang.String"/>
	<parameter name="variacaoFimPaginaInvestimentos" class="java.lang.String"/>
	<parameter name="variacaoFimPaginaInvestimentosDespesas" class="java.lang.String"/>
	<parameter name="variacaoFimPaginaSemInvestimentos" class="java.lang.String"/>
	<parameter name="variacaoFimPaginaComInvestimentos" class="java.lang.String"/>
	<field name="codigoAgrupador" class="java.lang.String"/>
	<field name="nomeAgrupador" class="java.lang.String"/>
	<field name="mes1.totalPrevistoMesString" class="java.lang.String"/>
	<field name="mes1.totalRealizadoMesString" class="java.lang.String"/>
	<field name="totalPrevistoString" class="java.lang.String"/>
	<field name="totalRealizadoString" class="java.lang.String"/>
	<field name="saldoFinalString" class="java.lang.String"/>
	<field name="percPretendidoString" class="java.lang.String"/>
	<field name="nivelArvore" class="java.lang.Integer"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="95" splitType="Stretch">
			<textField>
				<reportElement x="83" y="0" width="471" height="36"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio}]]></textFieldExpression>
			</textField>
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement x="1" y="0" width="82" height="36"/>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField>
				<reportElement x="207" y="40" width="69" height="19"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data1}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="347" y="40" width="39" height="19"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Total]]></text>
			</staticText>
			<staticText>
				<reportElement x="452" y="40" width="32" height="19"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Saldo]]></text>
			</staticText>
			<staticText>
				<reportElement x="510" y="40" width="40" height="19"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Variaç.]]></text>
			</staticText>
			<rectangle>
				<reportElement x="502" y="60" width="53" height="35" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="432" y="60" width="70" height="35" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="362" y="60" width="70" height="35" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="292" y="60" width="70" height="35" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="222" y="60" width="70" height="35" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="152" y="60" width="70" height="35" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="60" width="152" height="35" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="2" y="60" width="150" height="35"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Plano de Contas]]></text>
			</staticText>
			<staticText>
				<reportElement x="153" y="58" width="69" height="35"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="223" y="58" width="68" height="35"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="363" y="59" width="68" height="35"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<staticText>
				<reportElement x="294" y="59" width="67" height="35"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
		</band>
	</pageHeader>
	<detail>
		<band height="20">
			<textField>
				<reportElement style="corFundoRetangulo" x="222" y="0" width="70" height="19"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{nivelArvore}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="corFundoRetangulo" x="152" y="0" width="70" height="19"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{nivelArvore}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="corFundoRetangulo" x="0" y="0" width="152" height="19"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{nivelArvore}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="corFundoRetangulo" x="292" y="0" width="70" height="19"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{nivelArvore}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="corFundoRetangulo" x="362" y="0" width="70" height="19"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{nivelArvore}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="corFundoRetangulo" x="432" y="0" width="70" height="19"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{nivelArvore}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement style="corFundoRetangulo" x="502" y="0" width="53" height="19"/>
				<textElement/>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{nivelArvore}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement mode="Transparent" x="222" y="0" width="70" height="19" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="502" y="0" width="53" height="19" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="292" y="0" width="70" height="19" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="432" y="0" width="70" height="19" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="152" y="0" width="70" height="19" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="362" y="0" width="70" height="19" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="0" y="0" width="152" height="19" forecolor="#999999" backcolor="#999999"/>
				<graphicElement>
					<pen lineStyle="Solid" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="223" y="1" width="67" height="18"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes1.totalRealizadoMesString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="293" y="1" width="67" height="18"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{totalPrevistoString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="433" y="1" width="67" height="18"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{saldoFinalString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="502" y="1" width="51" height="18"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{percPretendidoString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="363" y="2" width="67" height="18"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{totalRealizadoString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="2" y="1" width="148" height="18"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeAgrupador}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="153" y="1" width="67" height="18"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{mes1.totalPrevistoMesString}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="217">
			<staticText>
				<reportElement x="347" y="18" width="39" height="19"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Total]]></text>
			</staticText>
			<staticText>
				<reportElement x="452" y="18" width="32" height="19"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Saldo]]></text>
			</staticText>
			<rectangle>
				<reportElement x="362" y="37" width="70" height="22" forecolor="#FFFFFF" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="432" y="37" width="70" height="22" forecolor="#FFFFFF" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="152" y="37" width="70" height="22" forecolor="#FFFFFF" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="37" width="152" height="22" forecolor="#FFFFFF" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="292" y="37" width="70" height="22" forecolor="#FFFFFF" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="222" y="37" width="70" height="22" forecolor="#FFFFFF" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="223" y="36" width="68" height="24"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<textField>
				<reportElement x="207" y="18" width="69" height="19"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{data1}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="294" y="37" width="67" height="23"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="363" y="37" width="68" height="23"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Realizado]]></text>
			</staticText>
			<rectangle>
				<reportElement x="502" y="37" width="53" height="22" forecolor="#FFFFFF" backcolor="#CCCCCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="153" y="36" width="69" height="24"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Previsto]]></text>
			</staticText>
			<staticText>
				<reportElement x="510" y="18" width="40" height="19"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Variaç.]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="38" width="150" height="22"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Resumo Geral]]></text>
			</staticText>
			<rectangle>
				<reportElement x="432" y="59" width="70" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="222" y="59" width="70" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="152" y="59" width="70" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="502" y="59" width="53" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="362" y="59" width="70" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="292" y="59" width="70" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="59" width="152" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="81" width="152" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="502" y="81" width="53" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="432" y="81" width="70" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="222" y="81" width="70" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="152" y="81" width="70" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="362" y="81" width="70" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="292" y="81" width="70" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="103" width="152" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="502" y="103" width="53" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="432" y="103" width="70" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="222" y="103" width="70" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="152" y="103" width="70" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="362" y="103" width="70" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="292" y="103" width="70" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="125" width="152" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="502" y="125" width="53" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="432" y="125" width="70" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="222" y="125" width="70" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="152" y="125" width="70" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="362" y="125" width="70" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="292" y="125" width="70" height="22" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="147" width="152" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="502" y="147" width="53" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="432" y="147" width="70" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="222" y="147" width="70" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="152" y="147" width="70" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="362" y="147" width="70" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="292" y="147" width="70" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="502" y="169" width="53" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="169" width="152" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="152" y="169" width="70" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="362" y="169" width="70" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="222" y="169" width="70" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="292" y="169" width="70" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="432" y="169" width="70" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="502" y="191" width="53" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="191" width="152" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="152" y="191" width="70" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="362" y="191" width="70" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="222" y="191" width="70" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="292" y="191" width="70" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="432" y="191" width="70" height="22" forecolor="#FFFFFF" backcolor="#CCFFCC"/>
				<graphicElement>
					<pen lineStyle="Double" lineColor="#000000"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="0" y="147" width="150" height="22"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Resultado Econômico]]></text>
			</staticText>
			<staticText>
				<reportElement x="3" y="59" width="147" height="22"/>
				<textElement verticalAlignment="Middle">
					<font isBold="false"/>
				</textElement>
				<text><![CDATA[Receita]]></text>
			</staticText>
			<staticText>
				<reportElement x="3" y="81" width="147" height="22"/>
				<textElement verticalAlignment="Middle">
					<font isBold="false"/>
				</textElement>
				<text><![CDATA[Despesas]]></text>
			</staticText>
			<staticText>
				<reportElement x="3" y="103" width="147" height="22"/>
				<textElement verticalAlignment="Middle">
					<font isBold="false"/>
				</textElement>
				<text><![CDATA[Investimentos]]></text>
			</staticText>
			<staticText>
				<reportElement x="3" y="125" width="147" height="22"/>
				<textElement verticalAlignment="Middle">
					<font isBold="false"/>
				</textElement>
				<text><![CDATA[Investimentos + Despesas]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="169" width="147" height="22"/>
				<textElement verticalAlignment="Middle">
					<font isBold="false"/>
				</textElement>
				<text><![CDATA[Sem Investimentos]]></text>
			</staticText>
			<staticText>
				<reportElement x="2" y="191" width="147" height="22"/>
				<textElement verticalAlignment="Middle">
					<font isBold="false"/>
				</textElement>
				<text><![CDATA[Com Investimentos]]></text>
			</staticText>
			<textField>
				<reportElement x="153" y="61" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaReceitasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="223" y="61" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaReceitasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="153" y="83" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="223" y="83" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="154" y="105" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="223" y="105" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="153" y="127" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaInvestimentosDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="223" y="127" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaInvestimentosDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="153" y="171" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaSemInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="223" y="171" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaSemInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="153" y="192" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaComInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="223" y="193" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaComInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="363" y="193" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaComInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="363" y="61" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaReceitasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="293" y="127" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaInvestimentosDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="293" y="171" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaSemInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="293" y="83" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaDespesasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="363" y="83" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="363" y="105" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="363" y="171" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaSemInvestimentosRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="294" y="105" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="363" y="127" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaInvestimentosDespesasRealizado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="293" y="61" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaReceitasPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="293" y="192" width="67" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{mes1TotalFimPaginaComInvestimentosPrevisto}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="434" y="61" width="65" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{saldoFimPaginaReceitas}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="434" y="83" width="65" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{saldoFimPaginaDespesas}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="434" y="105" width="65" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{saldoFimPaginaInvestimentos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="434" y="127" width="65" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{saldoFimPaginaInvestimentosDespesas}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="434" y="171" width="65" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{saldoFimPaginaSemInvestimentos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="435" y="193" width="65" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{saldoFimPaginaComInvestimentos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="503" y="61" width="50" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{variacaoFimPaginaReceitas}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="503" y="83" width="50" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{variacaoFimPaginaDespesas}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="503" y="105" width="50" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{variacaoFimPaginaInvestimentos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="503" y="127" width="50" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{variacaoFimPaginaInvestimentosDespesas}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="502" y="171" width="50" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{variacaoFimPaginaSemInvestimentos}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="503" y="193" width="50" height="18"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{variacaoFimPaginaComInvestimentos}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
