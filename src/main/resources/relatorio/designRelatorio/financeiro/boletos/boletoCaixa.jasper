¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wî             N           J  S          pppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp   uw   usr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ 'L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wî                 ¤pq ~ q ~ pt line-20pppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xr java.lang.Enum          xpt FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ 2t 
NO_STRETCH  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ 'L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wîpp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ 2t DASHEDsr java.lang.FloatÚíÉ¢Û<ðì F valuexr java.lang.Number¬à  xp?  q ~ /p  wî ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ 2t TOP_DOWNsq ~ !  wî          
      ¸pq ~ q ~ pt line-1ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîpp~q ~ =t SOLIDsq ~ @?  q ~ Fp  wî q ~ Dsq ~ !  wî              x   §pq ~ q ~ pt line-2ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ Lp  wî q ~ Dsq ~ !  wî                 §pq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ Pp  wî q ~ Dsq ~ !  wî                 Épq ~ q ~ pt line-4ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ Tp  wî q ~ Dsq ~ !  wî                 Úpq ~ q ~ pt line-5ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ Xp  wî q ~ Dsq ~ !  wî                 ëpq ~ q ~ pt line-6ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ \p  wî q ~ Dsq ~ !  wî                 üpq ~ q ~ pt line-7ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ `p  wî q ~ Dsq ~ !  wî             y   ¸pq ~ q ~ pt line-8ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ dp  wî q ~ Dsq ~ !  wî                Qpq ~ q ~ pt line-9ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ hp  wî q ~ Dsq ~ !  wî             z  
pq ~ q ~ pt line-10ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ lp  wî q ~ Dsq ~ !  wî             z  pq ~ q ~ pt line-11ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ pp  wî q ~ Dsq ~ !  wî             z  /pq ~ q ~ pt line-12ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ tp  wî q ~ Dsq ~ !  wî             y  @pq ~ q ~ pt line-13ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ xp  wî q ~ Dsq ~ !  wî   "           L   Úpq ~ q ~ pt line-14ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ |p  wî q ~ Dsq ~ !  wî                 Úpq ~ q ~ pt line-15ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~ !  wî              |   ëpq ~ q ~ pt line-16ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~ !  wî   "           í   Úpq ~ q ~ pt line-17ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~ !  wî              Ñ   Úpq ~ q ~ pt line-18ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~ !  wî                |pq ~ q ~ pt line-19ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColorq ~ 'L bottomBorderq ~ L bottomBorderColorq ~ 'L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldt Ljava/lang/Boolean;L isItalicq ~ L 
isPdfEmbeddedq ~ L isStrikeThroughq ~ L isStyledTextq ~ L isUnderlineq ~ L 
leftBorderq ~ L leftBorderColorq ~ 'L leftPaddingq ~ L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ 'L rightPaddingq ~ L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ 'L 
topPaddingq ~ L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ &  wî           B      ¸pq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialsr java.lang.Integerâ ¤÷8 I valuexq ~ A   ppsr java.lang.BooleanÍ rÕúî Z valuexp ppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ L leftPenq ~ ¥L paddingq ~ L penq ~ ¥L rightPaddingq ~ L rightPenq ~ ¥L 
topPaddingq ~ L topPenq ~ ¥xppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ xq ~ 8  wîsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ ­xp    ÿ   ppppq ~ Isq ~ @    q ~ §q ~ §q ~ psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ ©  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ §q ~ §psq ~ ©  wîppppq ~ §q ~ §psr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ ©  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ §q ~ §psr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ ©  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ §q ~ §pppppt 	Helveticapppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ 2t MIDDLEt Local de pagamentosq ~   wî           B      Épq ~ q ~ pt staticText-2ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Åq ~ Åq ~ Âpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Åq ~ Åpsq ~ ©  wîppppq ~ Åq ~ Åpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Åq ~ Åpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Åq ~ Åpppppt 	Helveticappppppppppq ~ ¿t 
BeneficiÃ¡riosq ~   wî           B      Úpq ~ q ~ pt staticText-3ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Øq ~ Øq ~ Õpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Øq ~ Øpsq ~ ©  wîppppq ~ Øq ~ Øpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Øq ~ Øpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Øq ~ Øpppppt 	Helveticappppppppppq ~ ¿t Data do Documentosq ~   wî           K      ëpq ~ q ~ pt staticText-4ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ ëq ~ ëq ~ èpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ ëq ~ ëpsq ~ ©  wîppppq ~ ëq ~ ëpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ ëq ~ ëpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ ëq ~ ëpppppt 	Helveticappppppppppq ~ ¿t NÂº da Conta / Respons.sq ~   wî           K      þpq ~ q ~ pt staticText-5ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ þq ~ þq ~ ûpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ þq ~ þpsq ~ ©  wîppppq ~ þq ~ þpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ þq ~ þpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ þq ~ þpppppt 	Helveticappppppppppq ~ ¿t InstruÃ§Ãµes :sq ~   wî           B   O   Úpq ~ q ~ pt staticText-6ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t NÂº do Documentosq ~   wî           *      Úpq ~ q ~ pt staticText-7ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$q ~!psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$psq ~ ©  wîppppq ~$q ~$psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$pppppt 	Helveticappppppppppq ~ ¿t 
EspÃ©cie Doc.sq ~   wî              Ô   Úpq ~ q ~ pt staticText-8ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7q ~4psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7psq ~ ©  wîppppq ~7q ~7psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7pppppt 	Helveticappppppppppq ~ ¿t Aceitesq ~   wî              ð   Úpq ~ q ~ pt staticText-9ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jq ~Gpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpsq ~ ©  wîppppq ~Jq ~Jpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpppppt 	Helveticappppppppppq ~ ¿t Data do Processamentosq ~   wî           #   O   ëpq ~ q ~ pt 
staticText-10ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]q ~Zpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]psq ~ ©  wîppppq ~]q ~]psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]pppppt 	Helveticappppppppppq ~ ¿t Carteirasq ~   wî           +   ~   ëpq ~ q ~ pt 
staticText-11ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~pq ~mpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~ppsq ~ ©  wîppppq ~pq ~ppsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~ppsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~ppppppt 	Helveticappppppppppq ~ ¿t EspÃ©cie Moedasq ~   wî           0   ­   ëpq ~ q ~ pt 
staticText-12ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t 
Qtde moedasq ~   wî           B   ð   ëpq ~ q ~ pt 
staticText-13ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t Valorsq ~   wî           d  |   ¸pq ~ q ~ pt 
staticText-14ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~©q ~©q ~¦psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~©q ~©psq ~ ©  wîppppq ~©q ~©psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~©q ~©psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~©q ~©pppppt 	Helveticappppppppppq ~ ¿t 
Vencimentosq ~   wî           d  |   Épq ~ q ~ pt 
staticText-15ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¼q ~¼q ~¹psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¼q ~¼psq ~ ©  wîppppq ~¼q ~¼psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¼q ~¼psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¼q ~¼pppppt 	Helveticappppppppppq ~ ¿t  AgÃªncia / CÃ³digo BeneficiÃ¡riosq ~   wî           d  |  @pq ~ q ~ pt 
staticText-16ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ïq ~Ïq ~Ìpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ïq ~Ïpsq ~ ©  wîppppq ~Ïq ~Ïpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ïq ~Ïpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ïq ~Ïpppppt 	Helveticappppppppppq ~ ¿t (=) Valor cobradosq ~   wî           d  |  /pq ~ q ~ pt 
staticText-17ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~âq ~âq ~ßpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~âq ~âpsq ~ ©  wîppppq ~âq ~âpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~âq ~âpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~âq ~âpppppt 	Helveticappppppppppq ~ ¿t (=) Outros acrÃ©scimossq ~   wî           d  |  pq ~ q ~ pt 
staticText-18ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~õq ~õq ~òpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~õq ~õpsq ~ ©  wîppppq ~õq ~õpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~õq ~õpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~õq ~õpppppt 	Helveticappppppppppq ~ ¿t (+) Mora / Juros / Multasq ~   wî           d  |  
pq ~ q ~ pt 
staticText-19ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t (-) Outros deduÃ§Ãµessq ~   wî           d  |   üpq ~ q ~ pt 
staticText-20ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t (-) Desconto / Abatimentosq ~   wî           d  |   ëpq ~ q ~ pt 
staticText-21ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.q ~+psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.psq ~ ©  wîppppq ~.q ~.psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.pppppt 	Helveticappppppppppq ~ ¿t (=) Valor do Documentosq ~   wî           d  |   Úpq ~ q ~ pt 
staticText-22ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Aq ~Aq ~>psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Aq ~Apsq ~ ©  wîppppq ~Aq ~Apsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Aq ~Apsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Aq ~Apppppt 	Helveticappppppppppq ~ ¿t 
Nosso NÃºmerosq ~   wî           !     Spq ~ q ~ pt 
staticText-23ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tq ~Qpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tpsq ~ ©  wîppppq ~Tq ~Tpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tpppppt 	Helveticappppppppppq ~ ¿t Pagador:sq ~   wî           A     qpq ~ q ~ pt 
staticText-24ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~gq ~gq ~dpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~gq ~gpsq ~ ©  wîppppq ~gq ~gpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~gq ~gpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~gq ~gpppppt 	Helveticappppppppppq ~ ¿t Sacador / Avalista :sq ~   wî           9  e  qpq ~ q ~ pt 
staticText-25ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~zq ~zq ~wpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~zq ~zpsq ~ ©  wîppppq ~zq ~zpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~zq ~zpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~zq ~zpppppt 	Helveticappppppppppq ~ ¿t CÃ³digo de baixasq ~   wî           D  L  ~pq ~ q ~ pt 
staticText-26ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t AutenticaÃ§Ã£o mecÃ¢nicasq ~   wî           j    ~pq ~ q ~ pt 
staticText-27ppppq ~ 3ppppq ~ 6  wîpppppt Arialsq ~     	ppsq ~ ¢ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¢q ~¢q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¢q ~¢psq ~ ©  wîppppq ~¢q ~¢psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¢q ~¢psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¢q ~¢pppppt Helvetica-Boldppppppppppq ~ ¿t Ficha de CompensaÃ§Ã£osq ~   wî              ê   ñpq ~ q ~ pt 
staticText-28p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ 2t OPAQUEppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ 2t CENTERq ~¡ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~»q ~»q ~²psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~»q ~»psq ~ ©  wîppppq ~»q ~»psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~»q ~»psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~»q ~»pppppt Helvetica-Boldppppppppppq ~ ¿t Xsr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ 'L bottomBorderq ~ L bottomBorderColorq ~ 'L 
bottomPaddingq ~ L evaluationGroupq ~ +L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ L 
leftBorderq ~ L leftBorderColorq ~ 'L leftPaddingq ~ L lineBoxq ~ L 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ L rightBorderq ~ L rightBorderColorq ~ 'L rightPaddingq ~ L 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ 'L 
topPaddingq ~ L verticalAlignmentq ~ L verticalAlignmentValueq ~ xq ~ #  wî   #       ?     ~sq ~ «    ÿÿÿÿpppq ~ q ~ sq ~ «    ÿ   pppt 	barcode-1pq ~µppq ~ 3ppppq ~ 6  wîp~r )net.sf.jasperreports.engine.type.FillEnum          xq ~ 2t SOLIDsq ~ 8  wîppq ~ Isq ~ @    q ~Ðp  wî         ppppppp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ 2t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt <it.businesslogic.ireport.barcode.BcImage.getBarcodeImage(13,sq ~át banco.codigoBarrassq ~át ,false,false,null,0,0)t java.awt.Imagepp~q ~¸t LEFTpppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ëq ~ëq ~Ðpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ëq ~ëpsq ~ ©  wîppppq ~ëq ~ëpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ëq ~ëpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ëq ~ëpp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ 2t ERRORppppp~r /net.sf.jasperreports.engine.type.ScaleImageEnum          xq ~ 2t 
FILL_FRAMEpppp~q ~ ¾t TOPsq ~ !  wî                pq ~ q ~ pt line-22ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ >sq ~ @?  q ~p  wî q ~ Dsq ~ !  wî                ³pq ~ q ~ pt line-50ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ >sq ~ @?  q ~p  wî q ~ Dsr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ +L evaluationTimeValueq ~ÌL 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ÍL hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullq ~ L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xq ~   wî             {   §pq ~ q ~ pt textField-35ppppq ~ 3ppppq ~ 6  wîpppppppp~q ~¸t RIGHTq ~¡ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~
psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt Helvetica-Boldppppppppppq ~ÿ  wî       ppq ~Úsq ~Ü   	uq ~ß   sq ~át banco.numeroFormattedt java.lang.Stringppppppq ~¡pppsq ~	  wî          p      §pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîppppppppq ~¹q ~¡ppppppppsq ~ ¤psq ~ ¨  wîppppq ~#q ~#q ~"psq ~ °  wîppppq ~#q ~#psq ~ ©  wîppppq ~#q ~#psq ~ µ  wîppppq ~#q ~#psq ~ ¹  wîppppq ~#q ~#pppppt Helvetica-Boldppppppppppp  wî        ppq ~Úsq ~Ü   
uq ~ß   sq ~át banco.linhaDigitavelt java.lang.Stringppppppppppsq ~	  wî   	       t      Ñpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialsq ~     pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~2q ~2q ~/psq ~ °  wîppppq ~2q ~2psq ~ ©  wîppppq ~2q ~2psq ~ µ  wîppppq ~2q ~2psq ~ ¹  wîppppq ~2q ~2pppppppppppppppp~q ~ ¾t BOTTOM  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.cedentet java.lang.Stringppppppppppsq ~	  wî   	       t      Àpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~Aq ~Aq ~?psq ~ °  wîppppq ~Aq ~Apsq ~ ©  wîppppq ~Aq ~Apsq ~ µ  wîppppq ~Aq ~Apsq ~ ¹  wîppppq ~Aq ~Appppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.localPagamentot java.lang.Stringppppppq ~¡pppsq ~	  wî   	        B      âpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~Nq ~Nq ~Lpsq ~ °  wîppppq ~Nq ~Npsq ~ ©  wîppppq ~Nq ~Npsq ~ µ  wîppppq ~Nq ~Npsq ~ ¹  wîppppq ~Nq ~Nppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   
uq ~ß   sq ~át boleto.dataDocumentot java.lang.Stringppppppq ~¡ppt  sq ~	  wî   	        3      âpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pq ~¹pppppppppsq ~ ¤psq ~ ¨  wîppppq ~\q ~\q ~Zpsq ~ °  wîppppq ~\q ~\psq ~ ©  wîppppq ~\q ~\psq ~ µ  wîppppq ~\q ~\psq ~ ¹  wîppppq ~\q ~\ppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.especieDocumentot java.lang.Stringppppppppppsq ~	  wî   	           Ó   âpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pq ~¹pppppppppsq ~ ¤psq ~ ¨  wîppppq ~iq ~iq ~gpsq ~ °  wîppppq ~iq ~ipsq ~ ©  wîppppq ~iq ~ipsq ~ µ  wîppppq ~iq ~ipsq ~ ¹  wîppppq ~iq ~ippppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át 
boleto.aceitet java.lang.Stringppppppppppsq ~	  wî   	           ð   âpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~vq ~vq ~tpsq ~ °  wîppppq ~vq ~vpsq ~ ©  wîppppq ~vq ~vpsq ~ µ  wîppppq ~vq ~vpsq ~ ¹  wîppppq ~vq ~vppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.dataProcessamentot java.lang.Stringppppppppppsq ~	  wî   	        (   O   ópq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pq ~¹pppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.carteirat java.lang.Stringppppppppppsq ~	  wî   	        P  ²   Àpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pq ~pppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.dataVencimentot java.lang.Stringppppppppppsq ~	  wî   	        P  ²   ópq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pq ~pppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.valorBoletot java.lang.Stringppppppppppsq ~	  wî   	             ãpq ~ q ~ pt textField-49ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pq ~pppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~«q ~«q ~¨psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~«q ~«psq ~ ©  wîppppq ~«q ~«psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~«q ~«psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~«q ~«ppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.nossoNumerosq ~át +(sq ~át boleto.dvNossoNumerosq ~át .isEmpty() ? "" : ("-" + sq ~át boleto.dvNossoNumerosq ~át ))t java.lang.Stringppppppq ~¡ppq ~Ysq ~	  wî   	       Ý   %  dpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~Êq ~Êq ~Èpsq ~ °  wîppppq ~Êq ~Êpsq ~ ©  wîppppq ~Êq ~Êpsq ~ µ  wîppppq ~Êq ~Êpsq ~ ¹  wîppppq ~Êq ~Êppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   uq ~ß   
sq ~át (sq ~át boleto.cidadeSacadosq ~át 
==null?"":sq ~át boleto.cidadeSacadosq ~át .trim() + "  " ) + ( sq ~át boleto.ufSacadosq ~át ==null? "" : ( sq ~át boleto.ufSacadosq ~át )+" "+(sq ~át boleto.cepSacadosq ~át .trim() != null ? "CEP: "+sq ~át boleto.cepSacadosq ~át :""))t java.lang.Stringppppppq ~¡pppsq ~	  wî   	        P  ²   Ñpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pq ~pppppppppsq ~ ¤psq ~ ¨  wîppppq ~ïq ~ïq ~ípsq ~ °  wîppppq ~ïq ~ïpsq ~ ©  wîppppq ~ïq ~ïpsq ~ µ  wîppppq ~ïq ~ïpsq ~ ¹  wîppppq ~ïq ~ïppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át  banco.agenciaCodCedenteFormattedt java.lang.Stringppppppppppsq ~	  wî   F       p     pq ~ q ~ pt textField-24ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pq ~épppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ýq ~ýq ~úpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ýq ~ýpsq ~ ©  wîppppq ~ýq ~ýpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ýq ~ýpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ýq ~ýppppppppppppppppq ~ÿ  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.instrucao1t java.lang.Stringppppppq ~¡pppsq ~	  wî   	              ópq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pq ~¹pppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át "R$"t java.lang.Stringppppppppppsq ~	  wî   	        C   N   âpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pq ~¹pppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.noDocumentot java.lang.Stringppppppppppsq ~ !  wî              «   ëpq ~ q ~ pt line-15ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~*p  wî q ~ Dsq ~Ë  wî           J      ¦pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppppq ~.p  wî         pppppppq ~Úsq ~Ü   uq ~ß   sq ~át 
SUBREPORT_DIRsq ~át   + "logos/logo-caixa.png"t java.lang.Stringppppppppppppsq ~ ¤psq ~ ¨  wîppppq ~7q ~7q ~.psq ~ °  wîppppq ~7q ~7psq ~ ©  wîppppq ~7q ~7psq ~ µ  wîppppq ~7q ~7psq ~ ¹  wîppppq ~7q ~7ppq ~úpppppq ~ýpppppsq ~	  wî          p      pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîppppppppq ~¹q ~¡ppppppppsq ~ ¤psq ~ ¨  wîppppq ~>q ~>q ~=psq ~ °  wîppppq ~>q ~>psq ~ ©  wîppppq ~>q ~>psq ~ µ  wîppppq ~>q ~>psq ~ ¹  wîppppq ~>q ~>pppppt Helvetica-Boldppppppppppp  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át banco.linhaDigitavelt java.lang.Stringppppppppppsq ~ !  wî                 pq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Jp  wî q ~ Dsq ~Ë  wî           J      pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppppq ~Np  wî         pppppppq ~Úsq ~Ü   uq ~ß   sq ~át 
SUBREPORT_DIRsq ~át   + "logos/logo-caixa.png"t java.lang.Stringppppppppppppsq ~ ¤psq ~ ¨  wîppppq ~Wq ~Wq ~Npsq ~ °  wîppppq ~Wq ~Wpsq ~ ©  wîppppq ~Wq ~Wpsq ~ µ  wîppppq ~Wq ~Wpsq ~ ¹  wîppppq ~Wq ~Wppq ~úpppppq ~ýpppppsq ~ !  wî              x   pq ~ q ~ pt line-2ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~]p  wî q ~ Dsq ~ !  wî                pq ~ q ~ pt line-1ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ap  wî q ~ Dsq ~	  wî             {   pq ~ q ~ pt textField-35ppppq ~ 3ppppq ~ 6  wîppppppppq ~q ~¡ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~gq ~gq ~epsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~gq ~gpsq ~ ©  wîppppq ~gq ~gpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~gq ~gpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~gq ~gpppppt Helvetica-Boldppppppppppq ~ÿ  wî       ppq ~Úsq ~Ü   uq ~ß   sq ~át banco.numeroFormattedt java.lang.Stringppppppq ~¡pppsq ~ !  wî                'pq ~ q ~ pt line-4ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~{p  wî q ~ Dsq ~   wî           B      pq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t Pagadorsq ~	  wî   	       o      pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   uq ~ß   	sq ~át ((sq ~át boleto.responsavelsq ~át  != null &&  !sq ~át boleto.responsavelsq ~át .isEmpty()) ? sq ~át boleto.responsavelsq ~át  :  sq ~át boleto.nomeSacadosq ~át )t java.lang.Stringppppppppppsq ~ !  wî   <          z   pq ~ q ~ pt line-8ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~¯p  wî q ~ Dsq ~   wî           0   Ý   )pq ~ q ~ pt staticText-7ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¶q ~¶q ~³psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¶q ~¶psq ~ ©  wîppppq ~¶q ~¶psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¶q ~¶psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¶q ~¶pppppt 	Helveticappppppppppq ~ ¿t 
Vencimentosq ~   wî           P  (   )pq ~ q ~ pt staticText-9ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Éq ~Éq ~Æpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Éq ~Épsq ~ ©  wîppppq ~Éq ~Épsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Éq ~Épsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Éq ~Épppppt 	Helveticappppppppppq ~ ¿t Valor do Documentosq ~   wî           B      )pq ~ q ~ pt staticText-3ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Üq ~Üq ~Ùpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Üq ~Üpsq ~ ©  wîppppq ~Üq ~Üpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Üq ~Üpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Üq ~Üpppppt 	Helveticappppppppppq ~ ¿t 
Nosso NÃºmerosq ~   wî           B   t   )pq ~ q ~ pt staticText-6ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ïq ~ïq ~ìpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ïq ~ïpsq ~ ©  wîppppq ~ïq ~ïpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ïq ~ïpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ïq ~ïpppppt 	Helveticappppppppppq ~ ¿t 
Nr. Documentosq ~ !  wî              Ù   )pq ~ q ~ pt line-15ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ÿp  wî q ~ Dsq ~ !  wî             %   )pq ~ q ~ pt line-18ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~p  wî q ~ Dsq ~ !  wî                ;pq ~ q ~ pt line-4ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~p  wî q ~ Dsq ~   wî           B  |   pq ~ q ~ pt staticText-3ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t CPF/CNPJ do Pagadorsq ~   wî           B  |   )pq ~ q ~ pt staticText-3ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~!q ~!q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~!q ~!psq ~ ©  wîppppq ~!q ~!psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~!q ~!psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~!q ~!pppppt 	Helveticappppppppppq ~ ¿t 
Valor Cobradosq ~   wî             |   <pq ~ q ~ pt staticText-3ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~4q ~4q ~1psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~4q ~4psq ~ ©  wîppppq ~4q ~4psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~4q ~4psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~4q ~4pppppt 	Helveticappppppppppq ~ ¿t CPF/CNPJ do BeneficiÃ¡riosq ~ !  wî                Opq ~ q ~ pt line-4ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Dp  wî q ~ Dsq ~ !  wî                cpq ~ q ~ pt line-4ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Hp  wî q ~ Dsq ~   wî           B      <pq ~ q ~ pt staticText-3ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Oq ~Oq ~Lpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Oq ~Opsq ~ ©  wîppppq ~Oq ~Opsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Oq ~Opsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Oq ~Opppppt 	Helveticappppppppppq ~ ¿t 
BeneficiÃ¡riosq ~   wî           e      Rpq ~ q ~ pt staticText-3ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~bq ~bq ~_psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~bq ~bpsq ~ ©  wîppppq ~bq ~bpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~bq ~bpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~bq ~bpppppt 	Helveticappppppppppq ~ ¿t EndereÃ§o do BeneficiÃ¡riosq ~   wî           e      dpq ~ q ~ pt staticText-3ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~uq ~uq ~rpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~uq ~upsq ~ ©  wîppppq ~uq ~upsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~uq ~upsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~uq ~upppppt 	Helveticappppppppppq ~ ¿t !AgÃªncia/CÃ³digo do BeneficiÃ¡riosq ~ !  wî   ;          L   dpq ~ q ~ pt line-8ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~p  wî q ~ Dsq ~ !  wî          I      wpq ~ q ~ pt line-4ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~p  wî q ~ Dsq ~   wî           ¾  N   fpq ~ q ~ pt 
staticText-26ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡pq ~¹q ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t ,AutenticaÃ§Ã£o mecÃ¢nica - Recibo do Pagadorsq ~	  wî   	        c      1pq ~ q ~ pt textField-49ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pq ~pppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~£q ~£q ~ psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~£q ~£psq ~ ©  wîppppq ~£q ~£psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~£q ~£psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~£q ~£ppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.nossoNumerosq ~át +(sq ~át boleto.dvNossoNumerosq ~át .isEmpty() ? "" : ("-" + sq ~át boleto.dvNossoNumerosq ~át ))t java.lang.Stringppppppq ~¡ppq ~Ysq ~	  wî   	        P  (   1pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pq ~pppppppppsq ~ ¤psq ~ ¨  wîppppq ~Âq ~Âq ~Àpsq ~ °  wîppppq ~Âq ~Âpsq ~ ©  wîppppq ~Âq ~Âpsq ~ µ  wîppppq ~Âq ~Âpsq ~ ¹  wîppppq ~Âq ~Âppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü    uq ~ß   sq ~át boleto.valorBoletot java.lang.Stringppppppppppsq ~	  wî   	        E   Ý   1pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pq ~pppppppppsq ~ ¤psq ~ ¨  wîppppq ~Ïq ~Ïq ~Ípsq ~ °  wîppppq ~Ïq ~Ïpsq ~ ©  wîppppq ~Ïq ~Ïpsq ~ µ  wîppppq ~Ïq ~Ïpsq ~ ¹  wîppppq ~Ïq ~Ïppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   !uq ~ß   sq ~át boleto.dataVencimentot java.lang.Stringppppppppppsq ~ !  wî              p   )pq ~ q ~ pt line-15ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Úp  wî q ~ Dsq ~	  wî   	        _   v   1pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pq ~¹pppppppppsq ~ ¤psq ~ ¨  wîppppq ~àq ~àq ~Þpsq ~ °  wîppppq ~àq ~àpsq ~ ©  wîppppq ~àq ~àpsq ~ µ  wîppppq ~àq ~àpsq ~ ¹  wîppppq ~àq ~àppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   "uq ~ß   sq ~át boleto.noDocumentot java.lang.Stringppppppppppsq ~	  wî   	       n      Epq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~íq ~íq ~ëpsq ~ °  wîppppq ~íq ~ípsq ~ ©  wîppppq ~íq ~ípsq ~ µ  wîppppq ~íq ~ípsq ~ ¹  wîppppq ~íq ~íppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   #uq ~ß   sq ~át boleto.cedentet java.lang.Stringppppppppppsq ~	  wî   	       E      mpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~úq ~úq ~øpsq ~ °  wîppppq ~úq ~úpsq ~ ©  wîppppq ~úq ~úpsq ~ µ  wîppppq ~úq ~úpsq ~ ¹  wîppppq ~úq ~úppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   $uq ~ß   sq ~át  banco.agenciaCodCedenteFormattedt java.lang.Stringppppppppppsq ~   wî           =      zpq ~ q ~ pt 
staticText-26ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡pq ~q ~¡ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t 
SAC CAIXA:sq ~   wî           þ   D   zpq ~ q ~ pt 
staticText-26ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t B0800 726 0101 (informaÃ§Ãµes, reclamaÃ§Ãµes, sugestÃµes e elogios)sq ~   wî           ¤      pq ~ q ~ pt 
staticText-26ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡pq ~q ~¡ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.q ~+psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.psq ~ ©  wîppppq ~.q ~.psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.pppppt 	Helveticappppppppppq ~ ¿t 2Para pessoas com deficiÃªncia auditiva ou de fala:sq ~   wî              «   pq ~ q ~ pt 
staticText-26ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Aq ~Aq ~>psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Aq ~Apsq ~ ©  wîppppq ~Aq ~Apsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Aq ~Apsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Aq ~Apppppt 	Helveticappppppppppq ~ ¿t 
0800 726 2492sq ~   wî           ¤      pq ~ q ~ pt 
staticText-26ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡pq ~q ~¡ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tq ~Qpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tpsq ~ ©  wîppppq ~Tq ~Tpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tpppppt 	Helveticappppppppppq ~ ¿t 
Ouvidoria:sq ~   wî              «   pq ~ q ~ pt 
staticText-26ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~gq ~gq ~dpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~gq ~gpsq ~ ©  wîppppq ~gq ~gpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~gq ~gpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~gq ~gpppppt 	Helveticappppppppppq ~ ¿t 
0800 725 7474sq ~   wî          =      pq ~ q ~ pt 
staticText-26ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡pq ~¹q ~¡ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~zq ~zq ~wpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~zq ~zpsq ~ ©  wîppppq ~zq ~zpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~zq ~zpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~zq ~zpppppt 	Helveticappppppppppq ~ ¿t caixa.gov.brsq ~	  wî   	        d     Epq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppp  wî        ppq ~Úsq ~Ü   %uq ~ß   sq ~át cnpjEmpresat java.lang.Stringppppppppppsq ~	  wî   	             pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   &uq ~ß   sq ~át boleto.cpfSacadot java.lang.Stringppppppppppsq ~	  wî   	       ß      [pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~¦q ~¦q ~¤psq ~ °  wîppppq ~¦q ~¦psq ~ ©  wîppppq ~¦q ~¦psq ~ µ  wîppppq ~¦q ~¦psq ~ ¹  wîppppq ~¦q ~¦ppppppppppppppppp  wî        ppq ~Úsq ~Ü   'uq ~ß   sq ~át enderecoEmpresat java.lang.Stringppppppppppsq ~	  wî   	       Ý   %  Qpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~³q ~³q ~±psq ~ °  wîppppq ~³q ~³psq ~ ©  wîppppq ~³q ~³psq ~ µ  wîppppq ~³q ~³psq ~ ¹  wîppppq ~³q ~³ppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   (uq ~ß   sq ~át ((sq ~át boleto.responsavelsq ~át  != null &&  !sq ~át boleto.responsavelsq ~át .isEmpty()) ? sq ~át boleto.responsavelsq ~át  :  sq ~át boleto.nomeSacadosq ~át 
) + " / " + (sq ~át boleto.cpfSacadosq ~át A.replace(".","").replace("-","").length()<12?"CPF: ":"CNPJ: ") + sq ~át boleto.cpfSacadot java.lang.Stringppppppppppsq ~	  wî   	       Ý   %  [pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~1pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~Öq ~Öq ~Ôpsq ~ °  wîppppq ~Öq ~Öpsq ~ ©  wîppppq ~Öq ~Öpsq ~ µ  wîppppq ~Öq ~Öpsq ~ ¹  wîppppq ~Öq ~Öppppppppppppppppq ~8  wî        ppq ~Úsq ~Ü   )uq ~ß   sq ~át boleto.enderecoSacadot java.lang.Stringppppppppppxp  wî  Âpp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xq ~ 2t STRETCHppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ ,L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wî ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ ,L valueClassNameq ~ L valueClassRealNameq ~ xppt bancosr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ ,L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Objectpsq ~öpt banco.numeroFormattedsq ~ùpppt java.lang.Stringpsq ~öpt banco.linhaDigitavelsq ~ùpppt java.lang.Stringpsq ~öpt banco.codigoBarrassq ~ùpppt java.lang.Stringpsq ~öpt boleto.cedentesq ~ùpppt java.lang.Stringpsq ~öpt boleto.localPagamentosq ~ùpppt java.lang.Stringpsq ~öpt boleto.dataDocumentosq ~ùpppt java.lang.Stringpsq ~öpt boleto.especieDocumentosq ~ùpppt java.lang.Stringpsq ~öpt 
boleto.aceitesq ~ùpppt java.lang.Stringpsq ~öpt boleto.dataProcessamentosq ~ùpppt java.lang.Stringpsq ~öpt boleto.carteirasq ~ùpppt java.lang.Stringpsq ~öpt boleto.dataVencimentosq ~ùpppt java.lang.Stringpsq ~öpt boleto.valorBoletosq ~ùpppt java.lang.Stringpsq ~öpt boleto.nomeSacadosq ~ùpppt java.lang.Stringpsq ~öpt boleto.cpfSacadosq ~ùpppt java.lang.Stringpsq ~öpt boleto.nossoNumerosq ~ùpppt java.lang.Stringpsq ~öpt boleto.enderecoSacadosq ~ùpppt java.lang.Stringpsq ~öpt boleto.cepSacadosq ~ùpppt java.lang.Stringpsq ~öpt boleto.cidadeSacadosq ~ùpppt java.lang.Stringpsq ~öpt boleto.ufSacadosq ~ùpppt java.lang.Stringpsq ~öpt  banco.agenciaCodCedenteFormattedsq ~ùpppt java.lang.Stringpsq ~öpt boleto.instrucao1sq ~ùpppt java.lang.Stringpsq ~öpt banco.bancosq ~ùpppt java.lang.Stringpsq ~öpt banco.nossoNumeroFormattedsq ~ùpppt java.lang.Stringpsq ~öpt boleto.dvNossoNumerosq ~ùpppt java.lang.Stringpsq ~öpt boleto.noDocumentosq ~ùpppt java.lang.Stringpsq ~öt boleto.responsavelt boleto.responsavelsq ~ùpppt java.lang.Stringpppt boletour *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ ,L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~ùpppt 
java.util.Mappsq ~ippt 
JASPER_REPORTpsq ~ùpppt (net.sf.jasperreports.engine.JasperReportpsq ~ippt REPORT_CONNECTIONpsq ~ùpppt java.sql.Connectionpsq ~ippt REPORT_MAX_COUNTpsq ~ùpppt java.lang.Integerpsq ~ippt REPORT_DATA_SOURCEpsq ~ùpppt (net.sf.jasperreports.engine.JRDataSourcepsq ~ippt REPORT_SCRIPTLETpsq ~ùpppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~ippt 
REPORT_LOCALEpsq ~ùpppt java.util.Localepsq ~ippt REPORT_RESOURCE_BUNDLEpsq ~ùpppt java.util.ResourceBundlepsq ~ippt REPORT_TIME_ZONEpsq ~ùpppt java.util.TimeZonepsq ~ippt REPORT_FORMAT_FACTORYpsq ~ùpppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~ippt REPORT_CLASS_LOADERpsq ~ùpppt java.lang.ClassLoaderpsq ~ippt REPORT_URL_HANDLER_FACTORYpsq ~ùpppt  java.net.URLStreamHandlerFactorypsq ~ippt REPORT_FILE_RESOLVERpsq ~ùpppt -net.sf.jasperreports.engine.util.FileResolverpsq ~ippt REPORT_TEMPLATESpsq ~ùpppt java.util.Collectionpsq ~ippt REPORT_VIRTUALIZERpsq ~ùpppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~ippt IS_IGNORE_PAGINATIONpsq ~ùpppt java.lang.Booleanpsq ~i ppt cnpjEmpresapsq ~ùpppt java.lang.Stringpsq ~i ppt enderecoEmpresapsq ~ùpppt java.lang.Stringpsq ~i ppt 
SUBREPORT_DIRpsq ~ùpppt java.lang.Stringpsq ~ùpsq ~    w   t ireport.scriptlethandlingt ireport.encodingt ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~ºt 1.8150000000000024q ~¹t UTF-8q ~»t 12q ~¼t 384q ~¸t 0xsr ,net.sf.jasperreports.engine.base.JRBaseQuery      'Ø [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt sqlppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ +L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ +L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ 2t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ 2t NONEppsq ~Ü    uq ~ß   sq ~át new java.lang.Integer(1)q ~ypt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ 2t REPORTq ~ypsq ~Ê  wî   q ~Ðppq ~Óppsq ~Ü   uq ~ß   sq ~át new java.lang.Integer(1)q ~ypt 
COLUMN_NUMBERp~q ~Út PAGEq ~ypsq ~Ê  wî   ~q ~Ït COUNTsq ~Ü   uq ~ß   sq ~át new java.lang.Integer(1)q ~yppq ~Óppsq ~Ü   uq ~ß   sq ~át new java.lang.Integer(0)q ~ypt REPORT_COUNTpq ~Ûq ~ypsq ~Ê  wî   q ~æsq ~Ü   uq ~ß   sq ~át new java.lang.Integer(1)q ~yppq ~Óppsq ~Ü   uq ~ß   sq ~át new java.lang.Integer(0)q ~ypt 
PAGE_COUNTpq ~ãq ~ypsq ~Ê  wî   q ~æsq ~Ü   uq ~ß   sq ~át new java.lang.Integer(1)q ~yppq ~Óppsq ~Ü   uq ~ß   sq ~át new java.lang.Integer(0)q ~ypt COLUMN_COUNTp~q ~Út COLUMNq ~yp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ 2t NULLq ~fp~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ 2t PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ 2t VERTICALpppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ 2t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~úL datasetCompileDataq ~úL mainDatasetCompileDataq ~ xpsq ~½?@     w       xsq ~½?@     w       xur [B¬óøTà  xp  .Êþº¾   .x boleto_1572295780978_478490  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_enderecoEmpresa 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_REPORT_LOCALE parameter_JASPER_REPORT parameter_REPORT_VIRTUALIZER parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_IS_IGNORE_PAGINATION parameter_SUBREPORT_DIR parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES parameter_cnpjEmpresa  parameter_REPORT_RESOURCE_BUNDLE field_boleto46cedente .Lnet/sf/jasperreports/engine/fill/JRFillField; !field_banco46nossoNumeroFormatted field_boleto46enderecoSacado field_boleto46dvNossoNumero field_banco46codigoBarras 'field_banco46agenciaCodCedenteFormatted field_boleto46nomeSacado field_boleto46aceite field_banco46banco field_boleto46valorBoleto field_boleto46especieDocumento field_banco46numeroFormatted field_banco field_boleto46dataVencimento field_boleto46dataProcessamento field_boleto46ufSacado field_boleto46cepSacado field_boleto46dataDocumento field_banco46linhaDigitavel field_boleto46nossoNumero field_boleto46responsavel field_boleto46noDocumento field_boleto46localPagamento field_boleto46cpfSacado field_boleto46cidadeSacado field_boleto46carteira field_boleto46instrucao1 variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code ; <
  >  	  @  	  B  	  D 	 	  F 
 	  H  	  J  	  L 
 	  N  	  P  	  R  	  T  	  V  	  X  	  Z  	  \  	  ^  	  `  	  b  	  d  	  f  	  h  	  j  	  l  	  n  	  p   	  r ! 	  t " 	  v # 	  x $ 	  z % 	  | & 	  ~ ' 	   ( 	   ) 	   * 	   + 	   , 	   - 	   . 	   / 	   0 	   1 	   2 	   3 	   4 	   5 6	   7 6	   8 6	    9 6	  ¢ : 6	  ¤ LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V © ª
  « 
initFields ­ ª
  ® initVars ° ª
  ± enderecoEmpresa ³ 
java/util/Map µ get &(Ljava/lang/Object;)Ljava/lang/Object; · ¸ ¶ ¹ 0net/sf/jasperreports/engine/fill/JRFillParameter » 
REPORT_LOCALE ½ 
JASPER_REPORT ¿ REPORT_VIRTUALIZER Á REPORT_TIME_ZONE Ã REPORT_FILE_RESOLVER Å REPORT_SCRIPTLET Ç REPORT_PARAMETERS_MAP É REPORT_CONNECTION Ë REPORT_CLASS_LOADER Í REPORT_DATA_SOURCE Ï REPORT_URL_HANDLER_FACTORY Ñ IS_IGNORE_PAGINATION Ó 
SUBREPORT_DIR Õ REPORT_FORMAT_FACTORY × REPORT_MAX_COUNT Ù REPORT_TEMPLATES Û cnpjEmpresa Ý REPORT_RESOURCE_BUNDLE ß boleto.cedente á ,net/sf/jasperreports/engine/fill/JRFillField ã banco.nossoNumeroFormatted å boleto.enderecoSacado ç boleto.dvNossoNumero é banco.codigoBarras ë  banco.agenciaCodCedenteFormatted í boleto.nomeSacado ï 
boleto.aceite ñ banco.banco ó boleto.valorBoleto õ boleto.especieDocumento ÷ banco.numeroFormatted ù banco û boleto.dataVencimento ý boleto.dataProcessamento ÿ boleto.ufSacado boleto.cepSacado boleto.dataDocumento banco.linhaDigitavel boleto.nossoNumero	 boleto.responsavel boleto.noDocumento
 boleto.localPagamento boleto.cpfSacado boleto.cidadeSacado boleto.carteira boleto.instrucao1 PAGE_NUMBER /net/sf/jasperreports/engine/fill/JRFillVariable 
COLUMN_NUMBER REPORT_COUNT 
PAGE_COUNT! COLUMN_COUNT# evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable( java/lang/Integer* (I)V ;,
+- getValue ()Ljava/lang/Object;/0
 ä1 java/lang/String3 (it/businesslogic/ireport/barcode/BcImage5 getBarcodeImage I(ILjava/lang/Object;ZZLjava/lang/String;II)Ljava/awt/image/BufferedImage;78
69 java/lang/StringBuffer; valueOf &(Ljava/lang/Object;)Ljava/lang/String;=>
4? (Ljava/lang/String;)V ;A
<B isEmpty ()ZDE
4F  H -J append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;LM
<N toString ()Ljava/lang/String;PQ
<R trimTQ
4U   W  Y CEP: [ R$]
 ¼1 logos/logo-caixa.png`  / b .d replace D(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;fg
4h length ()Ijk
4l CPF: n CNPJ: p evaluateOld getOldValues0
 ät evaluateEstimated 
SourceFile !     3                 	     
               
                                                                                                !     "     #     $     %     &     '     (     )     *     +     ,     -     .     /     0     1     2     3     4     5 6    7 6    8 6    9 6    : 6     ; <  =  ì    *· ?*µ A*µ C*µ E*µ G*µ I*µ K*µ M*µ O*µ Q*µ S*µ U*µ W*µ Y*µ [*µ ]*µ _*µ a*µ c*µ e*µ g*µ i*µ k*µ m*µ o*µ q*µ s*µ u*µ w*µ y*µ {*µ }*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ ¡*µ £*µ ¥±    ¦   Ö 5      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ N   § ¨  =   4     *+· ¬*,· ¯*-· ²±    ¦       Z  [ 
 \  ]  © ª  =  »    W*+´¹ º À ¼À ¼µ A*+¾¹ º À ¼À ¼µ C*+À¹ º À ¼À ¼µ E*+Â¹ º À ¼À ¼µ G*+Ä¹ º À ¼À ¼µ I*+Æ¹ º À ¼À ¼µ K*+È¹ º À ¼À ¼µ M*+Ê¹ º À ¼À ¼µ O*+Ì¹ º À ¼À ¼µ Q*+Î¹ º À ¼À ¼µ S*+Ð¹ º À ¼À ¼µ U*+Ò¹ º À ¼À ¼µ W*+Ô¹ º À ¼À ¼µ Y*+Ö¹ º À ¼À ¼µ [*+Ø¹ º À ¼À ¼µ ]*+Ú¹ º À ¼À ¼µ _*+Ü¹ º À ¼À ¼µ a*+Þ¹ º À ¼À ¼µ c*+à¹ º À ¼À ¼µ e±    ¦   R    e  f $ g 6 h H i Z j l k ~ l  m ¢ n ´ o Æ p Ø q ê r ü s t  u2 vD wV x  ­ ª  =  x    ô*+â¹ º À äÀ äµ g*+æ¹ º À äÀ äµ i*+è¹ º À äÀ äµ k*+ê¹ º À äÀ äµ m*+ì¹ º À äÀ äµ o*+î¹ º À äÀ äµ q*+ð¹ º À äÀ äµ s*+ò¹ º À äÀ äµ u*+ô¹ º À äÀ äµ w*+ö¹ º À äÀ äµ y*+ø¹ º À äÀ äµ {*+ú¹ º À äÀ äµ }*+ü¹ º À äÀ äµ *+þ¹ º À äÀ äµ *+ ¹ º À äÀ äµ *+¹ º À äÀ äµ *+¹ º À äÀ äµ *+¹ º À äÀ äµ *+¹ º À äÀ äµ *+
¹ º À äÀ äµ *+¹ º À äÀ äµ *+¹ º À äÀ äµ *+¹ º À äÀ äµ *+¹ º À äÀ äµ *+¹ º À äÀ äµ *+¹ º À äÀ äµ *+¹ º À äÀ äµ ±    ¦   r       $  6  H  Z  l  ~    ¢  ´  Æ  Ø  ê  ü  " 5 H [ n   § º Í à ó   ° ª  =        `*+¹ º ÀÀµ *+¹ º ÀÀµ *+ ¹ º ÀÀµ ¡*+"¹ º ÀÀµ £*+$¹ º ÀÀµ ¥±    ¦       £  ¤ & ¥ 9 ¦ L § _ ¨ %& '    ) =  H    ÜMª  ×       )   µ   Á   Í   Ù   å   ñ   ý  	    -  ;  I  W  e  s        «  ¹  Ç    ¿  Í  Û  â  ð      @  N    Ô  â  ð  þ      (  6  D  Ì»+Y·.M§»+Y·.M§
»+Y·.M§»+Y·.M§õ»+Y·.M§é»+Y·.M§Ý»+Y·.M§Ñ»+Y·.M§Å
*´ o¶2À4¸:M§­*´ }¶2À4M§*´ ¶2À4M§*´ g¶2À4M§*´ ¶2À4M§u*´ ¶2À4M§g*´ {¶2À4M§Y*´ u¶2À4M§K*´ ¶2À4M§=*´ ¶2À4M§/*´ ¶2À4M§!*´ y¶2À4M§»<Y*´ ¶2À4¸@·C*´ m¶2À4¶G 	I§ »<YK·C*´ m¶2À4¶O¶S¶O¶SM§Å»<Y*´ ¶2À4Ç 	I§ #»<Y*´ ¶2À4¶V¸@·CX¶O¶S¸@·C*´ ¶2À4Ç 	I§ S»<Y*´ ¶2À4¸@·CZ¶O*´ ¶2À4¶VÆ  »<Y\·C*´ ¶2À4¶O¶S§ I¶O¶S¶O¶SM§*´ q¶2À4M§
*´ ¶2À4M§ÿ^M§ø*´ ¶2À4M§ê»<Y*´ [¶_À4¸@·Ca¶O¶SM§É*´ ¶2À4M§»»<Y*´ [¶_À4¸@·Ca¶O¶SM§*´ }¶2À4M§*´ ¶2À4Æ  *´ ¶2À4¶G *´ ¶2À4§ 
*´ s¶2À4M§T»<Y*´ ¶2À4¸@·C*´ m¶2À4¶G 	I§ »<YK·C*´ m¶2À4¶O¶S¶O¶SM§*´ y¶2À4M§ ø*´ ¶2À4M§ ê*´ ¶2À4M§ Ü*´ g¶2À4M§ Î*´ q¶2À4M§ À*´ c¶_À4M§ ²*´ ¶2À4M§ ¤*´ A¶_À4M§ »<Y*´ ¶2À4Æ  *´ ¶2À4¶G *´ ¶2À4§ 
*´ s¶2À4¸@·Cc¶O*´ ¶2À4eI¶iKI¶i¶m¢ 	o§ q¶O*´ ¶2À4¶O¶SM§ *´ k¶2À4M,°    ¦  Z V   °  ² ¸ ¶ Á · Ä » Í ¼ Ð À Ù Á Ü Å å Æ è Ê ñ Ë ô Ï ý Ð  Ô	 Õ Ù Ú Þ- ß0 ã; ä> èI éL íW îZ òe óh ÷s øv ü ý «®¹¼ÇÊ¿ Â$Í%Ð)Û*Þ.â/å3ð4ó89=>"B@CCGNHQLMQÔR×VâWå[ð\ó`þaefjko(p+t6u9yDzG~ÌÏÚ r& '    ) =  H    ÜMª  ×       )   µ   Á   Í   Ù   å   ñ   ý  	    -  ;  I  W  e  s        «  ¹  Ç    ¿  Í  Û  â  ð      @  N    Ô  â  ð  þ      (  6  D  Ì»+Y·.M§»+Y·.M§
»+Y·.M§»+Y·.M§õ»+Y·.M§é»+Y·.M§Ý»+Y·.M§Ñ»+Y·.M§Å
*´ o¶uÀ4¸:M§­*´ }¶uÀ4M§*´ ¶uÀ4M§*´ g¶uÀ4M§*´ ¶uÀ4M§u*´ ¶uÀ4M§g*´ {¶uÀ4M§Y*´ u¶uÀ4M§K*´ ¶uÀ4M§=*´ ¶uÀ4M§/*´ ¶uÀ4M§!*´ y¶uÀ4M§»<Y*´ ¶uÀ4¸@·C*´ m¶uÀ4¶G 	I§ »<YK·C*´ m¶uÀ4¶O¶S¶O¶SM§Å»<Y*´ ¶uÀ4Ç 	I§ #»<Y*´ ¶uÀ4¶V¸@·CX¶O¶S¸@·C*´ ¶uÀ4Ç 	I§ S»<Y*´ ¶uÀ4¸@·CZ¶O*´ ¶uÀ4¶VÆ  »<Y\·C*´ ¶uÀ4¶O¶S§ I¶O¶S¶O¶SM§*´ q¶uÀ4M§
*´ ¶uÀ4M§ÿ^M§ø*´ ¶uÀ4M§ê»<Y*´ [¶_À4¸@·Ca¶O¶SM§É*´ ¶uÀ4M§»»<Y*´ [¶_À4¸@·Ca¶O¶SM§*´ }¶uÀ4M§*´ ¶uÀ4Æ  *´ ¶uÀ4¶G *´ ¶uÀ4§ 
*´ s¶uÀ4M§T»<Y*´ ¶uÀ4¸@·C*´ m¶uÀ4¶G 	I§ »<YK·C*´ m¶uÀ4¶O¶S¶O¶SM§*´ y¶uÀ4M§ ø*´ ¶uÀ4M§ ê*´ ¶uÀ4M§ Ü*´ g¶uÀ4M§ Î*´ q¶uÀ4M§ À*´ c¶_À4M§ ²*´ ¶uÀ4M§ ¤*´ A¶_À4M§ »<Y*´ ¶uÀ4Æ  *´ ¶uÀ4¶G *´ ¶uÀ4§ 
*´ s¶uÀ4¸@·Cc¶O*´ ¶uÀ4eI¶iKI¶i¶m¢ 	o§ q¶O*´ ¶uÀ4¶O¶SM§ *´ k¶uÀ4M,°    ¦  Z V    ¸ Á Ä Í  Ð¤ Ù¥ Ü© åª è® ñ¯ ô³ ý´ ¸	¹½¾Â-Ã0Ç;È>ÌIÍLÑWÒZÖe×hÛsÜvàáåæêë ï«ð®ô¹õ¼ùÇúÊþÿ¿ÂÍ	Ð
ÛÞâåðó!""&@'C+N,Q015Ô6×:â;å?ð@óDþEIJNOS(T+X6Y9]D^GbÌcÏgÚo v& '    ) =  H    ÜMª  ×       )   µ   Á   Í   Ù   å   ñ   ý  	    -  ;  I  W  e  s        «  ¹  Ç    ¿  Í  Û  â  ð      @  N    Ô  â  ð  þ      (  6  D  Ì»+Y·.M§»+Y·.M§
»+Y·.M§»+Y·.M§õ»+Y·.M§é»+Y·.M§Ý»+Y·.M§Ñ»+Y·.M§Å
*´ o¶2À4¸:M§­*´ }¶2À4M§*´ ¶2À4M§*´ g¶2À4M§*´ ¶2À4M§u*´ ¶2À4M§g*´ {¶2À4M§Y*´ u¶2À4M§K*´ ¶2À4M§=*´ ¶2À4M§/*´ ¶2À4M§!*´ y¶2À4M§»<Y*´ ¶2À4¸@·C*´ m¶2À4¶G 	I§ »<YK·C*´ m¶2À4¶O¶S¶O¶SM§Å»<Y*´ ¶2À4Ç 	I§ #»<Y*´ ¶2À4¶V¸@·CX¶O¶S¸@·C*´ ¶2À4Ç 	I§ S»<Y*´ ¶2À4¸@·CZ¶O*´ ¶2À4¶VÆ  »<Y\·C*´ ¶2À4¶O¶S§ I¶O¶S¶O¶SM§*´ q¶2À4M§
*´ ¶2À4M§ÿ^M§ø*´ ¶2À4M§ê»<Y*´ [¶_À4¸@·Ca¶O¶SM§É*´ ¶2À4M§»»<Y*´ [¶_À4¸@·Ca¶O¶SM§*´ }¶2À4M§*´ ¶2À4Æ  *´ ¶2À4¶G *´ ¶2À4§ 
*´ s¶2À4M§T»<Y*´ ¶2À4¸@·C*´ m¶2À4¶G 	I§ »<YK·C*´ m¶2À4¶O¶S¶O¶SM§*´ y¶2À4M§ ø*´ ¶2À4M§ ê*´ ¶2À4M§ Ü*´ g¶2À4M§ Î*´ q¶2À4M§ À*´ c¶_À4M§ ²*´ ¶2À4M§ ¤*´ A¶_À4M§ »<Y*´ ¶2À4Æ  *´ ¶2À4¶G *´ ¶2À4§ 
*´ s¶2À4¸@·Cc¶O*´ ¶2À4eI¶iKI¶i¶m¢ 	o§ q¶O*´ ¶2À4¶O¶SM§ *´ k¶2À4M,°    ¦  Z V  x z ¸~ Á Ä Í Ð Ù Ü å è ñ ô ý 	¡¢¦-§0«;¬>°I±LµW¶Zºe»h¿sÀvÄÅÉÊÎÏ Ó«Ô®Ø¹Ù¼ÝÇÞÊâãç¿èÂìÍíÐñÛòÞöâ÷åûðüó "
@CNQÔ×âå#ð$ó(þ)-.237(8+<6=9ADBGFÌGÏKÚS w    t _1572295780978_478490t 2net.sf.jasperreports.engine.design.JRJavacCompiler