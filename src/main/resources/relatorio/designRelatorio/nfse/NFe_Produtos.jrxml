<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NFe_Produtos" language="groovy" pageWidth="595" pageHeight="14" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="4.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<field name="codigoProduto" class="java.lang.String"/>
	<field name="descricao" class="java.lang.String"/>
	<field name="ncm" class="java.lang.String"/>
	<field name="cfop" class="java.lang.String"/>
	<field name="cst" class="java.lang.String"/>
	<field name="unidade" class="java.lang.String"/>
	<field name="quantidade" class="java.lang.String"/>
	<field name="valorUnitario" class="java.lang.String"/>
	<field name="valorTotal" class="java.lang.String"/>
	<field name="baseICMS" class="java.lang.String"/>
	<field name="valorICMS" class="java.lang.String"/>
	<field name="valorIPI" class="java.lang.String"/>
	<field name="aliquotaICMS" class="java.lang.String"/>
	<field name="aliquotaIPI" class="java.lang.String"/>
	<detail>
		<band height="13" splitType="Prevent">
			<rectangle>
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="25" height="13" isPrintWhenDetailOverflows="true"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToTallestObject" x="25" y="0" width="137" height="13" isPrintWhenDetailOverflows="true"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToTallestObject" x="162" y="0" width="33" height="13" isPrintWhenDetailOverflows="true"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToTallestObject" x="195" y="0" width="24" height="13" isPrintWhenDetailOverflows="true"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToTallestObject" x="219" y="0" width="25" height="13" isPrintWhenDetailOverflows="true"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToTallestObject" x="244" y="0" width="30" height="13" isPrintWhenDetailOverflows="true"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToTallestObject" x="274" y="0" width="26" height="13" isPrintWhenDetailOverflows="true"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToTallestObject" x="300" y="0" width="44" height="13" isPrintWhenDetailOverflows="true"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToTallestObject" x="344" y="0" width="45" height="13" isPrintWhenDetailOverflows="true"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToTallestObject" x="389" y="0" width="36" height="13" isPrintWhenDetailOverflows="true"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToTallestObject" x="425" y="0" width="36" height="13" isPrintWhenDetailOverflows="true"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToTallestObject" x="461" y="0" width="34" height="13" isPrintWhenDetailOverflows="true"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToTallestObject" x="495" y="0" width="44" height="13" isPrintWhenDetailOverflows="true"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement stretchType="RelativeToTallestObject" x="539" y="0" width="42" height="13" isPrintWhenDetailOverflows="true"/>
				<graphicElement>
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="25" height="13" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{codigoProduto}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="26" y="0" width="135" height="13" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToTallestObject" x="162" y="0" width="33" height="13" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{ncm}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToTallestObject" x="195" y="0" width="24" height="13" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cst}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToTallestObject" x="219" y="0" width="25" height="13" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cfop}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToTallestObject" x="244" y="0" width="30" height="13" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{unidade}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToTallestObject" x="274" y="0" width="24" height="13" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{quantidade}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToTallestObject" x="300" y="0" width="43" height="13" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorUnitario}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToTallestObject" x="344" y="0" width="44" height="13" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorTotal}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToTallestObject" x="389" y="0" width="35" height="13" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{baseICMS}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToTallestObject" x="425" y="0" width="35" height="13" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorICMS}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToTallestObject" x="461" y="0" width="33" height="13" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{valorIPI}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToTallestObject" x="495" y="0" width="43" height="13" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{aliquotaICMS}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToTallestObject" x="539" y="0" width="41" height="13" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font size="7" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{aliquotaIPI}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
