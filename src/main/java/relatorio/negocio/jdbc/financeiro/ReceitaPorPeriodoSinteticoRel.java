/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.jdbc.financeiro;

import java.io.File;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.negocio.comuns.financeiro.ReceitaPorPeriodoSinteticoRelTO;
import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;

/**
 * 
 * <AUTHOR>
 */
public class ReceitaPorPeriodoSinteticoRel extends SuperRelatorio {

    private Date dataInicio;
    private Date dataTermino;
    private String ordenacao;
    private Boolean mostraObservacaoreRecebimento;
    private Integer formaPagamento;
    private Boolean visaoDinheiro;
    private Boolean visaoChequeVista;
    private Boolean visaoChequePrazo;
    private Boolean visaoCartaoDebito;
    private Boolean visaoCartaoCredito;
    private Boolean visaoOutros;
    private Boolean visaoDevolucao;
    private Boolean visaoBoleto;
    private Boolean visaoPix;
    private Boolean visaoTransferenciaBancaria;

    public ReceitaPorPeriodoSinteticoRel() throws Exception {
        inicializarParametros();
    }

    public void inicializarParametros() {
        setFormaPagamento(1);
        setDataInicio(negocio.comuns.utilitarias.Calendario.hoje());
        setDataTermino(negocio.comuns.utilitarias.Calendario.hoje());
        setVisaoCartaoCredito(false);
        setVisaoCartaoDebito(false);
        setVisaoChequePrazo(false);
        setVisaoChequeVista(false);
        setVisaoDinheiro(false);
        setVisaoOutros(false);
        setVisaoDevolucao(false);
        setVisaoBoleto(false);
        setVisaoPix(false);
        setVisaoTransferenciaBancaria(false);
    }

    public void validarDados() throws ConsistirException {
        if (getDataInicio() == null) {
            throw new ConsistirException("Não é possível emitir o relátorio. Informe primeiro a Período De Início da Pesquisa.");
        }
        if (getDataTermino() == null) {
            throw new ConsistirException("Não é possível emitir o relátorio. Informe primeiro a Período de Termino da Pesquisa");
        }
        if(Calendario.menor(dataTermino, dataInicio)){
            throw new ConsistirException("A DATA DE INÍCIO de pesquisa deve ser menor que a DATA DE TÉRMINO");
        }
        if (Calendario.diferencaEmMesesInteiro(dataInicio, dataTermino) > 3) {
            throw new ConsistirException("Período de busca deve ter no máximo 3 meses.");
        }
    }

    public List montarDadosReceitaPorPeriodoSinteticoRelVO(int empresa) throws Exception {
        ReceitaPorPeriodoSinteticoRelTO rps = new ReceitaPorPeriodoSinteticoRelTO();
        gerarVisaoBandaRelatorio(rps);
        List<MovPagamentoVO> consulta;
        if (getFormaPagamento() == 1) {
            consulta = getFacade().getMovPagamento().consultarComFiltros(empresa, "","",
                    null, null, "00:00", "23:59", dataInicio, dataTermino, "00:00", "23:59", filtros(rps), "","","", null,null, null,"", null,
                    null, false, false,false, null, false, Uteis.NIVELMONTARDADOS_MOVPAGAMENTOSINTETICO, null, false, null, null, null);
        } else if (getFormaPagamento() == 2) {
            consulta = getFacade().getMovPagamento().consultarComFiltros(empresa, "","",
                    dataInicio, dataTermino, "00:00", "23:59", dataInicio, dataTermino, "00:00", "23:59", filtros(rps),"","","", null,null, null, "", null,
                    null, false, false,false,null, false, Uteis.NIVELMONTARDADOS_MOVPAGAMENTOSINTETICO, null, false, null, null, null);
        } else {
            consulta = getFacade().getMovPagamento().consultarComFiltros(empresa, "","",
                    dataInicio, dataTermino, "00:00", "23:59", null, null, "00:00", "23:59", filtros(rps),"","","", null,null, null,"", null,
                    null, false, false,false,null, false, Uteis.NIVELMONTARDADOS_MOVPAGAMENTOSINTETICO, null, false, null, null, null);
        }
        rps = processaLista(rps, consulta, empresa);
        List listaRetorno = new ArrayList();
        listaRetorno.add(rps);
        return listaRetorno;
    }

    private String filtros(ReceitaPorPeriodoSinteticoRelTO receitaPorPeriodoSinteticoRelTO) {
        StringBuilder str = new StringBuilder();
        boolean separador = false;
        if(receitaPorPeriodoSinteticoRelTO.getVisaoCartaoCredito()) {
            str.append("'CA'"); separador = true;
        }
        if(receitaPorPeriodoSinteticoRelTO.getVisaoCartaoDebito()) {
            str.append(separador ? ", " : "").append("'CD'"); separador = true;
        }
        if(receitaPorPeriodoSinteticoRelTO.getVisaoChequeVista() || receitaPorPeriodoSinteticoRelTO.getVisaoChequePrazo()) {
            str.append(separador ? ", " : "").append("'CH'"); separador = true;
        }
        if(receitaPorPeriodoSinteticoRelTO.getVisaoDinheiro() || receitaPorPeriodoSinteticoRelTO.getVisaoOutros() || receitaPorPeriodoSinteticoRelTO.getVisaoBoleto()) {
            str.append(separador ? ", " : "").append("'AV', 'CC', 'BB', 'CO', 'PD', 'TB', 'PX'"); separador = true;
        }

        return str.toString();
    }

    private ReceitaPorPeriodoSinteticoRelTO processaLista(ReceitaPorPeriodoSinteticoRelTO rps, List<MovPagamentoVO> lista, Integer empresa) throws Exception {
        for (MovPagamentoVO obj : lista) {
            if (obj.getValor().doubleValue() > 0.0) {
                Double valor = obj.getValor(); //obterValorProdutosPagamento(obj.getCodigo());
                if (obj.getFormaPagamento().getTipoFormaPagamento().equals("AV")) {

                    rps.setValorTotalDinheiro(rps.getValorTotalDinheiro() + valor);
                    rps.setQtdeTotalDinheiro(rps.getQtdeTotalDinheiro() + 1);
                    //TRANSFERENCIA BANCARIA
                } else if (obj.getFormaPagamento().getTipoFormaPagamento().equals("TB")) {
                    rps.setValorTotalTransferenciaBancaria(rps.getValorTotalTransferenciaBancaria() + obj.getValor());
                    rps.setQtdeTotalTransferenciaBancaria(rps.getQtdeTotalTransferenciaBancaria() + 1);
                    //PIX
                } else if (obj.getFormaPagamento().getTipoFormaPagamento().equals("PX")) {
                    rps.setValorTotalPix(rps.getValorTotalPix() + obj.getValor());
                    rps.setQtdeTotalPix(rps.getQtdeTotalPix() + 1);
                    //CARTAO DEBITO
                } else if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CD")) {
                    rps.setValorTotalCartaoDebito(rps.getValorTotalCartaoDebito() + valor);
                    rps.setQtdeTotalCartaoDebito(rps.getQtdeTotalCartaoDebito() + 1);
                    //CHEQUE
                } else if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    for (ChequeVO ch : obj.getChequeVOs()) {
                        // se data compensacao dentro do periodo ou se opção de impressão é somente lançamentos
                        if ((Calendario.maiorOuIgual(ch.getDataCompensacao(), dataInicio)
                                && Calendario.menorOuIgual(ch.getDataCompensacao(), dataTermino)
                                && !ch.getSituacao().equals("CA")) || getFormaPagamento().intValue() > 2) {
                            if (ch.getVistaOuPrazo().equals("AV")) {
                                rps.setValorTotalChequeVista(rps.getValorTotalChequeVista() + ch.getValor());
                                rps.setQtdeTotalChequeVista(rps.getQtdeTotalChequeVista() + 1);
                            } else if (ch.getVistaOuPrazo().equals("PR")) {
                                rps.setValorTotalChequePrazo(rps.getValorTotalChequePrazo() + ch.getValor());
                                rps.setQtdeTotalChequePrazo(rps.getQtdeTotalChequePrazo() + 1);
                            }
                        }
                    }
                } else if (obj.getFormaPagamento().getTipoFormaPagamento().equals("BB")) {
                    rps.setValorTotalBoleto(rps.getValorTotalBoleto() + valor);
                    rps.setQtdeTotalBoleto(rps.getQtdeTotalBoleto() + 1);
                    //OUTROS
                } else if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CO") || (obj.getFormaPagamento().getTipoFormaPagamento().equals("CC") && obj.getReciboPagamento().getCodigo() > 0)) {
                    rps.setValorTotalOutros(rps.getValorTotalOutros() + valor);
                    rps.setQtdeTotalOutros(rps.getQtdeTotalOutros() + 1);
                } else if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                    for (CartaoCreditoVO cc : obj.getCartaoCreditoVOs()) {
                        // se data compensacao dentro do periodo ou se opção de impressão é somente lançamentos
                        if ((Calendario.maiorOuIgual(cc.getDataCompensacao(), dataInicio)
                                && Calendario.menorOuIgual(cc.getDataCompensacao(), dataTermino)
                                && !cc.getSituacao().equals("CA")) || getFormaPagamento().intValue() > 2) {
                            rps.setValorTotalCartaoCredito(rps.getValorTotalCartaoCredito() + cc.getValor());
                            rps.setQtdeTotalCartaoCredito(rps.getQtdeTotalCartaoCredito() + 1);
                        }
                    }
                }
            } else if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CC")) {
                rps.setValorTotalOutros(rps.getValorTotalOutros() + obj.getValor());
                rps.setQtdeTotalOutros(rps.getQtdeTotalOutros() + 1);
            }
        }
        obterProdutosDevolucao(rps, empresa);
        rps.setDataIni(Uteis.getData(dataInicio));
        rps.setDataFim(Uteis.getData(dataTermino));
        gerarValorEQtdeTotal(rps);
        return rps;
    }
    
    private void obterProdutosDevolucao(ReceitaPorPeriodoSinteticoRelTO rps, Integer empresa) throws Exception{
    	StringBuilder sql = new StringBuilder();
    	sql.append(" SELECT movconta.valor FROM movconta ");
    	sql.append("  WHERE movproduto IS NOT NULL ");
    	sql.append(" and dataquitacao between  '"+Uteis.getDataHoraJDBC(dataInicio, "00:00:00")+"' AND '"+Uteis.getDataHoraJDBC(dataTermino, "23:59:59")+"' ");
    	if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND empresa = "+empresa);
        }
    	
    	Statement stm = con.createStatement();
        ResultSet dados = stm.executeQuery(sql.toString());
        int qtde = 0;
        while(dados.next()){
        	qtde++;
        	rps.setValorTotalDevolucao(rps.getValorTotalDevolucao() + dados.getDouble("valor"));
        }
        rps.setQtdeTotalDevolucao(qtde);
    }
    
    

    public void gerarVisaoBandaRelatorio(ReceitaPorPeriodoSinteticoRelTO obj) {
        if (getVisaoCartaoCredito() || getVisaoCartaoDebito() || getVisaoChequeVista() || getVisaoChequePrazo() || getVisaoDinheiro() || getVisaoOutros() || getVisaoDevolucao() || getVisaoBoleto() || getVisaoPix() || getVisaoTransferenciaBancaria()) {
            obj.setVisaoCartaoCredito(getVisaoCartaoCredito());
            obj.setVisaoCartaoDebito(getVisaoCartaoDebito());
            obj.setVisaoChequeVista(getVisaoChequeVista());
            obj.setVisaoChequePrazo(getVisaoChequePrazo());
            obj.setVisaoDinheiro(getVisaoDinheiro());
            obj.setVisaoOutros(getVisaoOutros());
            obj.setVisaoDevolucao(getVisaoDevolucao());
            obj.setVisaoBoleto(getVisaoBoleto());
            obj.setVisaoPix(getVisaoPix());
            obj.setVisaoTransferenciaBancaria(getVisaoDinheiro());
        } else {
            obj.setVisaoCartaoCredito(true);
            obj.setVisaoCartaoDebito(true);
            obj.setVisaoChequeVista(true);
            obj.setVisaoChequePrazo(true);
            obj.setVisaoDinheiro(true);
            obj.setVisaoOutros(true);
            obj.setVisaoDevolucao(true);
            obj.setVisaoBoleto(true);
            obj.setVisaoPix(true);
            obj.setVisaoTransferenciaBancaria(true);
        }
    }

    public void gerarValorEQtdeTotal(ReceitaPorPeriodoSinteticoRelTO obj) {
        if (obj.getVisaoCartaoCredito()) {
            obj.setQtdeTotalFinal(obj.getQtdeTotalFinal() + obj.getQtdeTotalCartaoCredito());
            obj.setValorTotalFinal(obj.getValorTotalFinal() + obj.getValorTotalCartaoCredito());
        }
        if (obj.getVisaoCartaoDebito()) {
            obj.setQtdeTotalFinal(obj.getQtdeTotalFinal() + obj.getQtdeTotalCartaoDebito());
            obj.setValorTotalFinal(obj.getValorTotalFinal() + obj.getValorTotalCartaoDebito());
        }
        if (obj.getVisaoChequePrazo()) {
            obj.setQtdeTotalFinal(obj.getQtdeTotalFinal() + obj.getQtdeTotalChequePrazo());
            obj.setValorTotalFinal(obj.getValorTotalFinal() + obj.getValorTotalChequePrazo());
        }
        if (obj.getVisaoChequeVista()) {
            obj.setQtdeTotalFinal(obj.getQtdeTotalFinal() + obj.getQtdeTotalChequeVista());
            obj.setValorTotalFinal(obj.getValorTotalFinal() + obj.getValorTotalChequeVista());
        }
        if (obj.getVisaoDinheiro()) {
            obj.setQtdeTotalFinal(obj.getQtdeTotalFinal() + obj.getQtdeTotalDinheiro() + obj.getQtdeTotalTransferenciaBancaria());
            obj.setValorTotalFinal(obj.getValorTotalFinal() + obj.getValorTotalDinheiro() + obj.getValorTotalTransferenciaBancaria());
        }
        if (obj.getVisaoBoleto()) {
            obj.setQtdeTotalFinal(obj.getQtdeTotalFinal() + obj.getQtdeTotalBoleto());
            obj.setValorTotalFinal(obj.getValorTotalFinal() + obj.getValorTotalBoleto());
        }
        if (obj.isVisaoPix()) {
            obj.setQtdeTotalFinal(obj.getQtdeTotalFinal() + obj.getQtdeTotalPix());
            obj.setValorTotalFinal(obj.getValorTotalFinal() + obj.getValorTotalPix());
        }
        obj.setQtdeTotalEspecie(obj.getQtdeTotalFinal());
        obj.setValorTotalEspecie(obj.getValorTotalFinal());
//        if (obj.getVisaoOutros()) {
//            obj.setQtdeTotalFinal(obj.getQtdeTotalFinal() + obj.getQtdeTotalOutros());
//            obj.setValorTotalFinal(obj.getValorTotalFinal() + obj.getValorTotalOutros());
//        }
        if(obj.getVisaoDevolucao()){
        	obj.setQtdeTotalFinal(obj.getQtdeTotalFinal() + obj.getQtdeTotalDevolucao());
                obj.setValorTotalFinal(obj.getValorTotalFinal() - obj.getValorTotalDevolucao());
        }
        if(obj.getVisaoOutros()){
            obj.setQtdeTotalFinal(obj.getQtdeTotalFinal() + obj.getQtdeTotalOutros());
            obj.setValorTotalFinal(obj.getValorTotalFinal() + obj.getValorTotalOutros());
        }
    }

    public static String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator + getIdEntidadeLocal() + ".jrxml");
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator);
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataTermino() {
        return dataTermino;
    }

    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    public Boolean getMostraObservacaoreRecebimento() {
        return mostraObservacaoreRecebimento;
    }

    public void setMostraObservacaoreRecebimento(Boolean mostraObservacaoreRecebimento) {
        this.mostraObservacaoreRecebimento = mostraObservacaoreRecebimento;
    }

    public String getOrdenacao() {
        return ordenacao;
    }

    public String getOrdenacao_Apresentar() {
        if (ordenacao == null) {
            return "";
        }
        if (ordenacao.equals("NR")) {
            return "Número Recibo";
        }
        if (ordenacao.equals("NA")) {
            return "Nome do Aluno";
        }
        return (ordenacao);
    }

    public void setOrdenacao(String ordenacao) {
        this.ordenacao = ordenacao;
    }

    public static String getIdEntidadeLocal() {
        return ("ReceitaPorPeriodoSinteticoRel");
    }

    public Integer getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(Integer formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public Boolean getVisaoCartaoCredito() {
        return visaoCartaoCredito;
    }

    public void setVisaoCartaoCredito(Boolean visaoCartaoCredito) {
        this.visaoCartaoCredito = visaoCartaoCredito;
    }

    public Boolean getVisaoCartaoDebito() {
        return visaoCartaoDebito;
    }

    public void setVisaoCartaoDebito(Boolean visaoCartaoDebito) {
        this.visaoCartaoDebito = visaoCartaoDebito;
    }

    public Boolean getVisaoDinheiro() {
        return visaoDinheiro;
    }

    public void setVisaoDinheiro(Boolean visaoDinheiro) {
        this.visaoDinheiro = visaoDinheiro;
    }

    public Boolean getVisaoOutros() {
        return visaoOutros;
    }

    public void setVisaoOutros(Boolean visaoOutros) {
        this.visaoOutros = visaoOutros;
    }

    public Boolean getVisaoChequePrazo() {
        return visaoChequePrazo;
    }

    public void setVisaoChequePrazo(Boolean visaoChequePrazo) {
        this.visaoChequePrazo = visaoChequePrazo;
    }

    public Boolean getVisaoChequeVista() {
        return visaoChequeVista;
    }

    public void setVisaoChequeVista(Boolean visaoChequeVista) {
        this.visaoChequeVista = visaoChequeVista;
    }
    
    /**
     * Método que faz a consulta aos pagamentos compensados e retorna valores agrupados por forma de pagamento
     * <AUTHOR>
     * 13/09/2011
     */
    public Map<String, Double> consultarCompensados(Integer empresa) throws Exception{
    	
    	StringBuilder sql = new StringBuilder();
        sql.append("SELECT SUM(valor) as valor, tipoformapagamento, COUNT(codigo) FROM ( \n");
        sql.append("SELECT distinct movpagamento.codigo, \n");
        sql.append("CASE \n");
        sql.append("WHEN fp.tipoformapagamento IN ('CH') \n");
        sql.append("	THEN (SELECT sum(valor) FROM cheque WHERE  movpagamento = movpagamento.codigo and datacompesancao >= '" + Uteis.getDataJDBC(getDataInicio()) + " 00:00:00' and datacompesancao <= '" + Uteis.getDataJDBC(getDataTermino()) + " 23:59:59') \n");

        sql.append("WHEN fp.tipoformapagamento IN ('CA')	 \n");
        sql.append("THEN (SELECT sum(valor) FROM cartaocredito WHERE  movpagamento = movpagamento.codigo and datacompesancao >= '" + Uteis.getDataJDBC(getDataInicio()) + " 00:00:00' and datacompesancao <= '" + Uteis.getDataJDBC(getDataTermino()) + " 23:59:59') \n");

        sql.append("WHEN fp.tipoformapagamento IN ('AV','CD','CC','BB','CO') \n");
        sql.append("THEN movpagamento.valortotal \n");
        sql.append("END AS valor, \n");
        sql.append("fp.tipoformapagamento	 \n");
        sql.append("from movpagamento   \n");
        sql.append("left outer join cliente as cli on cli.pessoa = movpagamento.pessoa   \n");
        sql.append("left outer join movconta as movc on movc.codigo = movpagamento.movconta   \n");
        sql.append("left outer join conta on conta.codigo = movc.conta  \n");
        sql.append("left outer join cheque on movpagamento.codigo = cheque.movpagamento and cheque.situacao not like 'CA'  \n");
        sql.append("left outer join cartaocredito on movpagamento.codigo = cartaocredito.movpagamento AND cartaocredito.situacao not like 'CA' \n");
        sql.append("inner join formapagamento fp on movpagamento.formapagamento = fp.codigo and not fp.somentefinanceiro  \n");
        sql.append("inner join usuario u on u.codigo = movpagamento.responsavelpagamento  \n");
        sql.append("where 1 = 1\n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" and movpagamento.empresa = ").append(empresa).append(" \n");
        }
        sql.append(" and ((movpagamento.datapagamento >= '" + Uteis.getDataJDBC(getDataInicio()) + " 00:00:00'   \n");
        sql.append("and movpagamento.datapagamento <= '" + Uteis.getDataJDBC(getDataTermino()) + " 23:59:59' and fp.tipoformapagamento IN ('AV','CD','CC','BB','CO'))   \n");
        sql.append("or (cheque.datacompesancao >= '" + Uteis.getDataJDBC(getDataInicio()) + " 00:00:00'  and cheque.datacompesancao <= '" + Uteis.getDataJDBC(getDataTermino()) + " 23:59:59')   \n");
        sql.append("or (cartaocredito.datacompesancao >= '" + Uteis.getDataJDBC(getDataInicio()) + " 00:00:00'  and cartaocredito.datacompesancao <= '" + Uteis.getDataJDBC(getDataTermino()) + " 23:59:59'))  \n");
        sql.append(") AS consulta GROUP BY tipoformapagamento \n");

    	ResultSet consulta = criarConsulta(sql.toString(), con);

        Map<String, Double> mapa = new HashMap<String, Double>();
    	//montar os valores no objeto ReceitaPorPeriodoSinteticoRelTO
    	while(consulta.next()){
            mapa.put(consulta.getString("tipoformapagamento"), consulta.getDouble("valor"));
    	}
    	return mapa;
    	
    }

	public void setVisaoDevolucao(Boolean visaoDevolucao) {
		this.visaoDevolucao = visaoDevolucao;
	}

	public Boolean getVisaoDevolucao() {
		return visaoDevolucao;
	}
	
	public Double obterValorProdutosPagamento(Integer codigoMovPagamento) throws Exception{
		StringBuilder sql = new StringBuilder();
    	sql.append(" select SUM(prodParc.valorPago) AS valor from PagamentoMovParcela pgtoMovParc  \n");
    	sql.append(" inner join movProdutoParcela prodParc on prodParc.movparcela = pgtoMovParc.movparcela \n"); 
    	sql.append(" inner join movParcela parc on parc.codigo = pgtoMovParc.movparcela  \n");
    	sql.append(" inner join movProduto movProd  on movProd.Codigo = prodParc.movproduto AND movProd.situacao NOT LIKE 'CA' \n");
    	sql.append(" inner join Produto prod on prod.codigo = movProd.produto  \n");
    	sql.append(" where movProd.totalfinal > 0 and  pgtoMovParc.MovPagamento = "+codigoMovPagamento);
    	
    	
    	ResultSet resultSet = criarConsulta(sql.toString(), con);
    	resultSet.next();
    	return resultSet.getDouble("valor");
	}

    public Boolean getVisaoBoleto() {
        return visaoBoleto;
    }

    public void setVisaoBoleto(Boolean visaoBoleto) {
        this.visaoBoleto = visaoBoleto;
    }

    public Boolean getVisaoTransferenciaBancaria() {
        return visaoTransferenciaBancaria;
    }

    public void setVisaoTransferenciaBancaria(Boolean visaoTransferenciaBancaria) {
        this.visaoTransferenciaBancaria = visaoTransferenciaBancaria;
    }

    public Boolean getVisaoPix() {
        return visaoPix;
    }

    public void setVisaoPix(Boolean visaoPix) {
        this.visaoPix = visaoPix;
    }
}
