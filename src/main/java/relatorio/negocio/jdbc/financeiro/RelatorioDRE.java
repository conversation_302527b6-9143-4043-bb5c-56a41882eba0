package relatorio.negocio.jdbc.financeiro;

import controle.arquitetura.threads.ThreadDemonstrativoFinanceiro;
import negocio.comuns.financeiro.enumerador.TipoEquivalenciaDRE;
import negocio.comuns.financeiro.enumerador.TipoFonteDadosDF;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RelatorioDRE extends SuperRelatorioFinan {

	public RelatorioDRE(){
		listaThreads = new ArrayList<ThreadDemonstrativoFinanceiro>();
	}

	public List<DemonstrativoFinanceiro> gerarDRE(TipoRelatorioDF tipoRelatorio, Calendar dataInicial, Calendar dataFinal, int empresa,List<Integer> listaFiltroCentroCusto,
												  boolean gerarRelatorioUsandoThread, TipoFonteDadosDF tipoFonteDadosDF, boolean usarCE, boolean agruparValorProdutoMMasModalidades,String contas, boolean apresentarDevolucoesRel) throws Exception {
		Connection con = Conexao.getFromSession();
		if(con == null){
			con = Conexao.getConexaoForJ2SE();
		}
		return gerarDREApartirConexao(con,tipoRelatorio,dataInicial,dataFinal,empresa, listaFiltroCentroCusto,gerarRelatorioUsandoThread,tipoFonteDadosDF,usarCE,false, agruparValorProdutoMMasModalidades,contas, apresentarDevolucoesRel);
	}
	public List<DemonstrativoFinanceiro> gerarDREApartirConexao(Connection con ,TipoRelatorioDF tipoRelatorio, Calendar dataInicial, Calendar dataFinal, int empresa, List<Integer> listaFiltroCentroCusto,
																boolean gerarRelatorioUsandoThread, TipoFonteDadosDF tipoFonteDadosDF, boolean usarCE,boolean fecharConexao, boolean agruparValorProdutoMMasModalidades,String contas, boolean apresentarDevolucoesRel) throws Exception{
		this.gerarRelatorioUsandoThread = gerarRelatorioUsandoThread;
		List<MesProcessar> listaMesesProcessar = montarListaMeses(dataInicial, dataFinal);
		Map<String, String> novosCodigos = new HashMap<String, String>();
		List<DemonstrativoFinanceiro> listaDemonstrativo = montarListaDemonstrativoFinaneiro(null, listaMesesProcessar, novosCodigos, true, con);

		criarThreadsDemonstrativoFinanceiro(con, listaDemonstrativo, tipoRelatorio, null, listaMesesProcessar, empresa,
				listaFiltroCentroCusto, tipoFonteDadosDF, true, novosCodigos, usarCE, agruparValorProdutoMMasModalidades, contas);
		executarThreads(tipoRelatorio, listaMesesProcessar, apresentarDevolucoesRel);
		// Ficar em loop até que todas as threads terminam o seu trabalho.
		loopVerificarTerminoDasThreads();
		if(fecharConexao){
			con.close();
		}
		return listaDemonstrativo;
	}
	public List<DemonstrativoFinanceiro> gerarDREFechandoConexao(Connection con ,TipoRelatorioDF tipoRelatorio, Calendar dataInicial, Calendar dataFinal, int empresa, List<Integer> listaFiltroCentroCusto,
																 boolean gerarRelatorioUsandoThread, TipoFonteDadosDF tipoFonteDadosDF, boolean usarCE, boolean agruparValorProdutoMMasModalidades) throws Exception{
		return gerarDREApartirConexao(con,tipoRelatorio,dataInicial,dataFinal,empresa,listaFiltroCentroCusto,gerarRelatorioUsandoThread,tipoFonteDadosDF,usarCE,true, agruparValorProdutoMMasModalidades,null, true);
	}

	public DemonstrativoFinanceiroJSON obterResultadoJSON(List<DemonstrativoFinanceiro> listaDRE) {
		List<DemonstrativoFinanceiro> lista = new ArrayList<DemonstrativoFinanceiro>();
		for (DemonstrativoFinanceiro df : listaDRE) {
			if (df.getTotalTodosMeses() != 0.0 || df.getDre()) {
				df.setPercentual(100 != 0.0 ? (df.getTotalTodosMesesPositivo() / 100) : 0.0);
				lista.add(df);
			}
		}
		double cemPorCento = 100;
		DemonstrativoFinanceiro receita = DemonstrativoFinanceiro.obterTituloEquivalencia(TipoEquivalenciaDRE.RECEITA_BRUTA, lista);
		DemonstrativoFinanceiro custos = DemonstrativoFinanceiro.obterTituloEquivalencia(TipoEquivalenciaDRE.CUSTOS_ESPECIFICOS, lista);
		DemonstrativoFinanceiro lucroBruto = DemonstrativoFinanceiro.obterTituloEquivalencia(TipoEquivalenciaDRE.LUCRO_BRUTO, lista);
		DemonstrativoFinanceiro despesas = DemonstrativoFinanceiro.obterTituloEquivalencia(TipoEquivalenciaDRE.DESPESAS_OPERACIONAIS, lista);
		DemonstrativoFinanceiro lucroOperacional = DemonstrativoFinanceiro.obterTituloEquivalencia(TipoEquivalenciaDRE.LUCRO_OPERACIONAL, lista);
		DemonstrativoFinanceiroJSON resultadoExercicio = new DemonstrativoFinanceiroJSON();
		for (CentroCustosDRE ccd : receita.getListaCentros()) {
			CentroCustosDRE custosDRE = CentroCustosDRE.obter(ccd, custos.getListaCentros());
			CentroCustosDRE lucroBrt = CentroCustosDRE.obter(ccd, lucroBruto.getListaCentros());

			lucroBrt.setTotal(ccd.getTotal() - custosDRE.getTotalPositivo());
			lucroBruto.setTotalTodosMeses(lucroBruto.getTotalTodosMeses() + lucroBrt.getTotal());
		}
		for (CentroCustosDRE ccd : despesas.getListaCentros()) {
			CentroCustosDRE lucroBrt = CentroCustosDRE.obter(ccd, lucroBruto.getListaCentros());
			CentroCustosDRE lucroOp = CentroCustosDRE.obter(ccd, lucroOperacional.getListaCentros());

			lucroOp.setTotal(lucroBrt.getTotal() - ccd.getTotalPositivo());

			lucroOperacional.setTotalTodosMeses(lucroOperacional.getTotalTodosMeses() + lucroOp.getTotal());
		}
		lucroBruto.setPercentual(cemPorCento != 0.0 ? lucroBruto.getTotalTodosMesesPositivo() / cemPorCento : 0.0);
		lucroOperacional.setPercentual(cemPorCento != 0.0 ? lucroOperacional.getTotalTodosMesesPositivo() / cemPorCento : 0.0);
		double pontoEquilibrio = lucroBruto.getTotalTodosMeses() == 0 ? 0.0 :
				(despesas.getTotalTodosMeses() / lucroBruto.getTotalTodosMeses()) * receita.getTotalTodosMeses();
		resultadoExercicio.setPontoEquilibrio(pontoEquilibrio < 0 ? pontoEquilibrio * -1 : pontoEquilibrio);
		double realizadoPeriodo = receita.getTotalTodosMeses();
		resultadoExercicio.setNaoRealizado(realizadoPeriodo - pontoEquilibrio);
		double despesasOp = despesas.getTotalTodosMeses() < 0.0 ? despesas.getTotalTodosMeses() * -1 : despesas.getTotalTodosMeses();
		double resultadoExercicioVal = lucroBruto.getTotalTodosMeses() - despesasOp;
		resultadoExercicio.setResultadoExercicio(resultadoExercicioVal);
		resultadoExercicio.setResultadoExercicioPorc((resultadoExercicioVal * 100) / cemPorCento);
		return resultadoExercicio;
	}

	public static List<DemonstrativoFinanceiro> retornarListaDRE(List<DemonstrativoFinanceiro> listaDF, Map<String, TipoEquivalenciaDRE> equivalencias) throws Exception {
		List<DemonstrativoFinanceiro> listaDRE = new ArrayList<DemonstrativoFinanceiro>();
		DemonstrativoFinanceiro df;
		for (TipoEquivalenciaDRE equivalencia: TipoEquivalenciaDRE.values()){
			df = new DemonstrativoFinanceiro();
			df.setCodigoAgrupador(equivalencia.getCodigoNode());
			df.setNomeAgrupador(equivalencia.getDescricao());
			listaDRE.add(df);
		}
		LISTA : for(DemonstrativoFinanceiro dfLista : listaDF){
			double total = 0.0;
			DemonstrativoFinanceiro dfClone = dfLista.getClone();
			for(MesProcessar mes :dfClone.getListaMeses()){
				total = mes.getTotal();
				if(mes.getTotal() == 0.0)
					continue LISTA;
			}
			TipoEquivalenciaDRE equivalencia = equivalencias.get(dfClone.getCodigoAgrupador());
			if(equivalencia != null){
				dfClone.setCodigoAgrupador(equivalencia.getCodigoNode()+"."+dfClone.getCodigoAgrupador());
				listaDRE.add(dfClone);
				DemonstrativoFinanceiro dfd = new DemonstrativoFinanceiro();
				dfd.setCodigoAgrupador(equivalencia.getCodigoNode());
				int indexOf = listaDRE.indexOf(dfd);
				dfd = listaDRE.get(indexOf);
				if(dfd.getListaMeses().isEmpty()){
					MesProcessar mesProcessar = new MesProcessar();
					mesProcessar.setTotal(mesProcessar.getTotal() + total);
					dfd.getListaMeses().add(mesProcessar);
				}else{
					MesProcessar mesProcessar = dfd.getListaMeses().get(0);
					mesProcessar.setTotal(mesProcessar.getTotal() + total);

				}

			}

		}

		return listaDRE;

	}

}
