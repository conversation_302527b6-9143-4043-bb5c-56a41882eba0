package relatorio.negocio.jdbc.contrato;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.contrato.ComissaoConfiguracaoVO;
import negocio.comuns.contrato.ComissaoGeralConfiguracaoVO;
import negocio.comuns.contrato.ComissaoProdutoConfiguracaoVO;
import negocio.comuns.financeiro.MetaConsultorMesTO;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by glauco on 24/01/14
 */
public class AgrupadorPrincipalComissaoRel extends SuperTO {

    private Integer codigo = null;
    private String nome = "";
    private List<AgrupadorConfiguracaoComissaoRel> listaAgrupadaConfiguracao = new ArrayList<AgrupadorConfiguracaoComissaoRel>();
    private List<MetaConsultorMesTO> listaMetaConsultorMesTO = new ArrayList<MetaConsultorMesTO>();

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public JRDataSource getListaConfiguracao() {
        return new JRBeanArrayDataSource(getListaAgrupadaConfiguracao().toArray());
    }

    public List<AgrupadorConfiguracaoComissaoRel> getListaAgrupadaConfiguracao() {
        return listaAgrupadaConfiguracao;
    }

    public void setListaAgrupadaConfiguracao(List<AgrupadorConfiguracaoComissaoRel> listaAgrupadaConfiguracao) {
        this.listaAgrupadaConfiguracao = listaAgrupadaConfiguracao;
    }

    public String getValorTotal() {
        double valor = getValorConfiguracao();
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(valor);
    }

    public String getValorTotalContratos() {
        double valor = getValorContratos();
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(valor);
    }

    public String getValorTotalProdutos() {
        double valor = getValorProdutos();
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(valor);
    }

    public double getValorConfiguracao() {
        double valor = 0.0;
        for (AgrupadorConfiguracaoComissaoRel conf : listaAgrupadaConfiguracao) {
            valor += conf.getValorConfiguracao();
        }
        return valor;
    }

    public double getValorContratos() {
        double valor = 0.0;
        for (AgrupadorConfiguracaoComissaoRel conf : listaAgrupadaConfiguracao) {
            ComissaoConfiguracaoVO config = conf.getConfiguracao();
            if (config instanceof ComissaoGeralConfiguracaoVO) {
                valor += conf.getValorConfiguracao();
            }
        }
        return valor;
    }

    public double getValorProdutos() {
        double valor = 0.0;
        for (AgrupadorConfiguracaoComissaoRel conf : listaAgrupadaConfiguracao) {
            ComissaoConfiguracaoVO config = conf.getConfiguracao();
            if (config instanceof ComissaoProdutoConfiguracaoVO) {
                valor += conf.getValorConfiguracao();
            }
        }
        return valor;
    }

    public int getQtdContratos() {
        int valor = 0;
        for (AgrupadorConfiguracaoComissaoRel conf : listaAgrupadaConfiguracao) {
            ComissaoConfiguracaoVO config = conf.getConfiguracao();
            if (config instanceof ComissaoGeralConfiguracaoVO) {
                valor += conf.getQtdContratos();
            }
        }
        return valor;
    }

    public int getQtdProdutos() {
        int valor = 0;
        for (AgrupadorConfiguracaoComissaoRel conf : listaAgrupadaConfiguracao) {
            ComissaoConfiguracaoVO config = conf.getConfiguracao();
            if (config instanceof ComissaoProdutoConfiguracaoVO) {
                valor += conf.getQtdContratos();
            }
        }
        return valor;
    }

    public String getValorTotalComissao() {
        double valor = getValorComissao();
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(valor);
    }

    public String getValorTotalComissaoContratos() {
        double valor = getValorComissaoContratos();
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(valor);
    }

    public String getValorTotalComissaoProdutos() {
        double valor = getValorComissaoProdutos();
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(valor);
    }

    public double getValorComissao() {
        double valor = 0.0;
        for (AgrupadorConfiguracaoComissaoRel conf : listaAgrupadaConfiguracao) {
            valor += conf.getValorComissao();
        }
        return valor;
    }

    public double getValorComissaoContratos() {
        double valor = 0.0;
        for (AgrupadorConfiguracaoComissaoRel conf : listaAgrupadaConfiguracao) {
            ComissaoConfiguracaoVO config = conf.getConfiguracao();
            if (config instanceof ComissaoGeralConfiguracaoVO) {
                valor += conf.getValorComissao();
            }
        }
        return valor;
    }

    public double getValorComissaoProdutos() {
        double valor = 0.0;
        for (AgrupadorConfiguracaoComissaoRel conf : listaAgrupadaConfiguracao) {
            ComissaoConfiguracaoVO config = conf.getConfiguracao();
            if (config instanceof ComissaoProdutoConfiguracaoVO) {
                valor += conf.getValorComissao();
            }
        }
        return valor;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public List<MetaConsultorMesTO> getListaMetaConsultorMesTO() {
        return listaMetaConsultorMesTO;
    }

    public void setListaMetaConsultorMesTO(List<MetaConsultorMesTO> listaMetaConsultorMesTO) {
        this.listaMetaConsultorMesTO = listaMetaConsultorMesTO;
    }
}
