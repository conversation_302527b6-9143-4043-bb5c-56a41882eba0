package relatorio.negocio.comuns.contrato;
import java.util.ArrayList;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.contrato.ContratoVO;
import java.util.Date;
import java.util.List;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.HorarioTurmaVO;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;

/**
 * Reponsável por manter os dados da entidade MatriculaAlunoHorarioTurma. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
*/
public class MatriculaAlunoHorarioTurmaRelVO extends SuperVO {
	
    protected Integer codigo;
    protected Integer sequencial;
    protected Date dataInicioMatricula;
    protected Date dataFimMatricula;
    protected ModalidadeVO modalidade;
    protected HorarioTurmaVO horarioTurma;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Contrato </code>.*/
    protected ContratoVO contrato;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Turma </code>.*/
    protected TurmaVO turma;
    
    protected List listaAluno;
    
    protected String data1;
    protected String diaSemana1;
    protected String data2;
    protected String diaSemana2;
    protected String data3;
    protected String diaSemana3;
    protected String data4;
    protected String diaSemana4;
    protected String data5;
    protected String diaSemana5;
    protected String data6;
    protected String diaSemana6;
    protected String data7;
    protected String diaSemana7;
    protected String data8;
    protected String diaSemana8;
    protected String data9;
    protected String diaSemana9;
    protected String data10;
    protected String diaSemana10;
    protected String data11;
    protected String diaSemana11;
    protected String data12;
    protected String diaSemana12;
    protected String data13;
    protected String diaSemana13;
    protected String data14;
    protected String diaSemana14;
    protected String data15;
    protected String diaSemana15;
    protected String data16;
    protected String diaSemana16;
    protected String data17;
    protected String diaSemana17;
    protected String data18;
    protected String diaSemana18;
    protected String data19;
    protected String diaSemana19;
    protected String data20;
    protected String diaSemana20;
    protected String data21;
    protected String diaSemana21;
    protected String data22;
    protected String diaSemana22;
    protected String data23;
    protected String diaSemana23;
    protected String data24;
    protected String diaSemana24;
    protected String data25;
    protected String diaSemana25;
    protected String data26;
    protected String diaSemana26;
    protected String data27;
    protected String diaSemana27;
    protected String data28;
    protected String diaSemana28;
    protected String data29;
    protected String diaSemana29;
    protected String data30;
    protected String diaSemana30;
    protected String data31;
    protected String diaSemana31;

    /**
     * Construtor padrão da classe <code>MatriculaAlunoHorarioTurma</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
    */
    public MatriculaAlunoHorarioTurmaRelVO() {
        super();
        inicializarDados();
    }
     
	
    /**
     * Operação responsável por validar os dados de um objeto da classe <code>MatriculaAlunoHorarioTurmaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
    */
    public static void validarDados(MatriculaAlunoHorarioTurmaRelVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
            }
        if ((obj.getContrato() == null) ||
            (obj.getContrato().getCodigo().intValue() == 0)) { 
            throw new ConsistirException("O campo CONTRATO (Matrícula Aluno Horário Turma) deve ser informado.");
        }
        if ((obj.getTurma() == null) ||
            (obj.getTurma().getCodigo().intValue() == 0)) { 
            throw new ConsistirException("O campo TURMA (Matrícula Aluno Horário Turma) deve ser informado.");
        }
        if (obj.getHorarioTurma().getCodigo().intValue() == 0) { 
            throw new ConsistirException("O campo HORÁRIO TURMA (Matrícula Aluno Horário Turma) deve ser informado.");
        }
    }
     
    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
    */
    public void realizarUpperCaseDados() {
    }
     
    /**
     * Operação reponsável por inicializar os atributos da classe.
    */
    public void inicializarDados() {
        setCodigo( new Integer(0) );
        setSequencial( new Integer(0) );
        setModalidade( new ModalidadeVO() );
        setHorarioTurma( new HorarioTurmaVO() );
        setDataInicioMatricula( negocio.comuns.utilitarias.Calendario.hoje() );
        setDataFimMatricula( negocio.comuns.utilitarias.Calendario.hoje() );
        setContrato( new ContratoVO() );
        setTurma( new TurmaVO() );
        setListaAluno(new ArrayList());
        
        setData1("");setData2("");setData3("");setData4("");setData5("");
        setData6("");setData7("");setData8("");setData9("");setData10("");
        setData11("");setData12("");setData13("");setData14("");setData15("");
        setData16("");setData17("");setData18("");setData19("");setData20("");
        setData21("");setData22("");setData23("");setData24("");setData25("");
        setData26("");setData27("");setData28("");setData29("");setData30("");
        setData31("");
        setDiaSemana1("");setDiaSemana2("");setDiaSemana3("");setDiaSemana4("");setDiaSemana5("");
        setDiaSemana6("");setDiaSemana7("");setDiaSemana8("");setDiaSemana9("");setDiaSemana10("");
        setDiaSemana11("");setDiaSemana12("");setDiaSemana13("");setDiaSemana14("");setDiaSemana15("");
        setDiaSemana16("");setDiaSemana17("");setDiaSemana18("");setDiaSemana19("");setDiaSemana20("");
        setDiaSemana21("");setDiaSemana22("");setDiaSemana23("");setDiaSemana24("");setDiaSemana25("");
        setDiaSemana26("");setDiaSemana27("");setDiaSemana28("");setDiaSemana29("");setDiaSemana30("");
        setDiaSemana31("");
    }

    public TurmaVO getTurma() {
        if (turma == null) {
            turma = new TurmaVO();
        }
        return (turma);
    }
     
    public void setTurma( TurmaVO obj) {
        this.turma = obj;
    }

    public ContratoVO getContrato() {
        if (contrato == null) {
            contrato = new ContratoVO();
        }
        return (contrato);
    }
     
    public void setContrato( ContratoVO obj) {
        this.contrato = obj;
    }

    public Date getDataFimMatricula() {
        return (dataFimMatricula);
    }
     
    public String getDataFimMatricula_Apresentar() {
        return (Uteis.getData(dataFimMatricula));
    }
     
    public void setDataFimMatricula( Date dataFimMatricula ) {
        this.dataFimMatricula = dataFimMatricula;
    }

    public Date getDataInicioMatricula() {
        return (dataInicioMatricula);
    }
     
    public String getDataInicioMatricula_Apresentar() {
        return (Uteis.getData(dataInicioMatricula));
    }
     
    public void setDataInicioMatricula( Date dataInicioMatricula ) {
        this.dataInicioMatricula = dataInicioMatricula;
    }

    public HorarioTurmaVO getHorarioTurma() {
        return (horarioTurma);
    }
     
    public void setHorarioTurma( HorarioTurmaVO horarioTurma ) {
        this.horarioTurma = horarioTurma;
    }

    public ModalidadeVO getModalidade() {
        return (modalidade);
    }
     
    public void setModalidade( ModalidadeVO modalidade ) {
        this.modalidade = modalidade;
    }

    public List getListaAluno() {
        return listaAluno;
    }

    public void setListaAluno(List listaAluno) {
        this.listaAluno = listaAluno;
    }
    
    public JRDataSource getListaAlunos() {
        JRDataSource jr = new JRBeanArrayDataSource(getListaAluno().toArray());   
        return jr;
    }

    public String getData1() {
        return data1;
    }

    public void setData1(String data1) {
        this.data1 = data1;
    }

    public String getDiaSemana1() {
        return diaSemana1;
    }

    public void setDiaSemana1(String diaSemana1) {
        this.diaSemana1 = diaSemana1;
    }

    public String getData2() {
        return data2;
    }

    public void setData2(String data2) {
        this.data2 = data2;
    }

    public String getDiaSemana2() {
        return diaSemana2;
    }

    public void setDiaSemana2(String diaSemana2) {
        this.diaSemana2 = diaSemana2;
    }

    public String getData3() {
        return data3;
    }

    public void setData3(String data3) {
        this.data3 = data3;
    }

    public String getDiaSemana3() {
        return diaSemana3;
    }

    public void setDiaSemana3(String diaSemana3) {
        this.diaSemana3 = diaSemana3;
    }

    public String getData4() {
        return data4;
    }

    public void setData4(String data4) {
        this.data4 = data4;
    }

    public String getDiaSemana4() {
        return diaSemana4;
    }

    public void setDiaSemana4(String diaSemana4) {
        this.diaSemana4 = diaSemana4;
    }

    public String getData5() {
        return data5;
    }

    public void setData5(String data5) {
        this.data5 = data5;
    }

    public String getDiaSemana5() {
        return diaSemana5;
    }

    public void setDiaSemana5(String diaSemana5) {
        this.diaSemana5 = diaSemana5;
    }

    public String getData6() {
        return data6;
    }

    public void setData6(String data6) {
        this.data6 = data6;
    }

    public String getDiaSemana6() {
        return diaSemana6;
    }

    public void setDiaSemana6(String diaSemana6) {
        this.diaSemana6 = diaSemana6;
    }

    public String getData7() {
        return data7;
    }

    public void setData7(String data7) {
        this.data7 = data7;
    }

    public String getDiaSemana7() {
        return diaSemana7;
    }

    public void setDiaSemana7(String diaSemana7) {
        this.diaSemana7 = diaSemana7;
    }

    public String getData8() {
        return data8;
    }

    public void setData8(String data8) {
        this.data8 = data8;
    }

    public String getDiaSemana8() {
        return diaSemana8;
    }

    public void setDiaSemana8(String diaSemana8) {
        this.diaSemana8 = diaSemana8;
    }

    public String getData9() {
        return data9;
    }

    public void setData9(String data9) {
        this.data9 = data9;
    }

    public String getDiaSemana9() {
        return diaSemana9;
    }

    public void setDiaSemana9(String diaSemana9) {
        this.diaSemana9 = diaSemana9;
    }

    public String getData10() {
        return data10;
    }

    public void setData10(String data10) {
        this.data10 = data10;
    }

    public String getDiaSemana10() {
        return diaSemana10;
    }

    public void setDiaSemana10(String diaSemana10) {
        this.diaSemana10 = diaSemana10;
    }

    public String getData11() {
        return data11;
    }

    public void setData11(String data11) {
        this.data11 = data11;
    }

    public String getDiaSemana11() {
        return diaSemana11;
    }

    public void setDiaSemana11(String diaSemana11) {
        this.diaSemana11 = diaSemana11;
    }

    public String getData12() {
        return data12;
    }

    public void setData12(String data12) {
        this.data12 = data12;
    }

    public String getDiaSemana12() {
        return diaSemana12;
    }

    public void setDiaSemana12(String diaSemana12) {
        this.diaSemana12 = diaSemana12;
    }

    public String getData13() {
        return data13;
    }

    public void setData13(String data13) {
        this.data13 = data13;
    }

    public String getDiaSemana13() {
        return diaSemana13;
    }

    public void setDiaSemana13(String diaSemana13) {
        this.diaSemana13 = diaSemana13;
    }

    public String getData14() {
        return data14;
    }

    public void setData14(String data14) {
        this.data14 = data14;
    }

    public String getDiaSemana14() {
        return diaSemana14;
    }

    public void setDiaSemana14(String diaSemana14) {
        this.diaSemana14 = diaSemana14;
    }

    public String getData15() {
        return data15;
    }

    public void setData15(String data15) {
        this.data15 = data15;
    }

    public String getDiaSemana15() {
        return diaSemana15;
    }

    public void setDiaSemana15(String diaSemana15) {
        this.diaSemana15 = diaSemana15;
    }

    public String getData16() {
        return data16;
    }

    public void setData16(String data16) {
        this.data16 = data16;
    }

    public String getDiaSemana16() {
        return diaSemana16;
    }

    public void setDiaSemana16(String diaSemana16) {
        this.diaSemana16 = diaSemana16;
    }

    public String getData17() {
        return data17;
    }

    public void setData17(String data17) {
        this.data17 = data17;
    }

    public String getDiaSemana17() {
        return diaSemana17;
    }

    public void setDiaSemana17(String diaSemana17) {
        this.diaSemana17 = diaSemana17;
    }

    public String getData18() {
        return data18;
    }

    public void setData18(String data18) {
        this.data18 = data18;
    }

    public String getDiaSemana18() {
        return diaSemana18;
    }

    public void setDiaSemana18(String diaSemana18) {
        this.diaSemana18 = diaSemana18;
    }

    public String getData19() {
        return data19;
    }

    public void setData19(String data19) {
        this.data19 = data19;
    }

    public String getDiaSemana19() {
        return diaSemana19;
    }

    public void setDiaSemana19(String diaSemana19) {
        this.diaSemana19 = diaSemana19;
    }

    public String getData20() {
        return data20;
    }

    public void setData20(String data20) {
        this.data20 = data20;
    }

    public String getDiaSemana20() {
        return diaSemana20;
    }

    public void setDiaSemana20(String diaSemana20) {
        this.diaSemana20 = diaSemana20;
    }

    public String getData21() {
        return data21;
    }

    public void setData21(String data21) {
        this.data21 = data21;
    }

    public String getDiaSemana21() {
        return diaSemana21;
    }

    public void setDiaSemana21(String diaSemana21) {
        this.diaSemana21 = diaSemana21;
    }

    public String getData22() {
        return data22;
    }

    public void setData22(String data22) {
        this.data22 = data22;
    }

    public String getDiaSemana22() {
        return diaSemana22;
    }

    public void setDiaSemana22(String diaSemana22) {
        this.diaSemana22 = diaSemana22;
    }

    public String getData23() {
        return data23;
    }

    public void setData23(String data23) {
        this.data23 = data23;
    }

    public String getDiaSemana23() {
        return diaSemana23;
    }

    public void setDiaSemana23(String diaSemana23) {
        this.diaSemana23 = diaSemana23;
    }

    public String getData24() {
        return data24;
    }

    public void setData24(String data24) {
        this.data24 = data24;
    }

    public String getDiaSemana24() {
        return diaSemana24;
    }

    public void setDiaSemana24(String diaSemana24) {
        this.diaSemana24 = diaSemana24;
    }

    public String getData25() {
        return data25;
    }

    public void setData25(String data25) {
        this.data25 = data25;
    }

    public String getDiaSemana25() {
        return diaSemana25;
    }

    public void setDiaSemana25(String diaSemana25) {
        this.diaSemana25 = diaSemana25;
    }

    public String getData26() {
        return data26;
    }

    public void setData26(String data26) {
        this.data26 = data26;
    }

    public String getDiaSemana26() {
        return diaSemana26;
    }

    public void setDiaSemana26(String diaSemana26) {
        this.diaSemana26 = diaSemana26;
    }

    public String getData27() {
        return data27;
    }

    public void setData27(String data27) {
        this.data27 = data27;
    }

    public String getDiaSemana27() {
        return diaSemana27;
    }

    public void setDiaSemana27(String diaSemana27) {
        this.diaSemana27 = diaSemana27;
    }

    public String getData28() {
        return data28;
    }

    public void setData28(String data28) {
        this.data28 = data28;
    }

    public String getDiaSemana28() {
        return diaSemana28;
    }

    public void setDiaSemana28(String diaSemana28) {
        this.diaSemana28 = diaSemana28;
    }

    public String getData29() {
        return data29;
    }

    public void setData29(String data29) {
        this.data29 = data29;
    }

    public String getDiaSemana29() {
        return diaSemana29;
    }

    public void setDiaSemana29(String diaSemana29) {
        this.diaSemana29 = diaSemana29;
    }

    public String getData30() {
        return data30;
    }

    public void setData30(String data30) {
        this.data30 = data30;
    }

    public String getDiaSemana30() {
        return diaSemana30;
    }

    public void setDiaSemana30(String diaSemana30) {
        this.diaSemana30 = diaSemana30;
    }

    public String getData31() {
        return data31;
    }

    public void setData31(String data31) {
        this.data31 = data31;
    }

    public String getDiaSemana31() {
        return diaSemana31;
    }

    public void setDiaSemana31(String diaSemana31) {
        this.diaSemana31 = diaSemana31;
    }

    public Integer getSequencial() {
        return sequencial;
    }

    public void setSequencial(Integer sequencial) {
        this.sequencial = sequencial;
    }

    public Integer getQtdAluno() {
        return getListaAluno().size();
    }
    
    public String getHoraInicial(){
    	return getHorarioTurma().getHoraInicial();
    }
}