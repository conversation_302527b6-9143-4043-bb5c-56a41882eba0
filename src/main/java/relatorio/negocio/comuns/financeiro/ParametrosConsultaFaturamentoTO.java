package relatorio.negocio.comuns.financeiro;

import negocio.comuns.basico.ClienteTitularDependenteVO;
import negocio.comuns.plano.PlanoVO;

import java.util.Date;
import java.util.List;

public class ParametrosConsultaFaturamentoTO {

    private int empresa;
    private Date dataIni;
    private Date dataFim;
    private Date dataReceb;
    private Date dataContratosLancadosAPartir;
    private boolean comissao;
    private String tipo;
    private boolean ehGestaoNotas;
    private Integer operador;
    private Integer consultor;
    private Integer codPessoa;
    private boolean familia;
    private ClienteTitularDependenteVO clienteTitularDependenteVO;
    private Boolean retiraEdicaoPagamento;
    private Boolean retirarRecebiveisComPendencia;
    private List<PlanoVO> planosFiltrar;

    public boolean isFamilia() {
        return familia;
    }

    public void setFamilia(boolean familia) {
        this.familia = familia;
    }

    public int getEmpresa() {
        return empresa;
    }

    public void setEmpresa(int empresa) {
        this.empresa = empresa;
    }

    public Date getDataIni() {
        return dataIni;
    }

    public void setDataIni(Date dataIni) {
        this.dataIni = dataIni;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Date getDataReceb() {
        return dataReceb;
    }

    public void setDataReceb(Date dataReceb) {
        this.dataReceb = dataReceb;
    }

    public Date getDataContratosLancadosAPartir() {
        return dataContratosLancadosAPartir;
    }

    public void setDataContratosLancadosAPartir(Date dataContratosLancadosAPartir) {
        this.dataContratosLancadosAPartir = dataContratosLancadosAPartir;
    }

    public boolean isComissao() {
        return comissao;
    }

    public void setComissao(boolean comissao) {
        this.comissao = comissao;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public boolean isEhGestaoNotas() {
        return ehGestaoNotas;
    }

    public void setEhGestaoNotas(boolean ehGestaoNotas) {
        this.ehGestaoNotas = ehGestaoNotas;
    }

    public Integer getOperador() {
        return operador;
    }

    public void setOperador(Integer operador) {
        this.operador = operador;
    }

    public Integer getConsultor() {
        return consultor;
    }

    public void setConsultor(Integer consultor) {
        this.consultor = consultor;
    }

    public Integer getCodPessoa() {
        return codPessoa;
    }

    public void setCodPessoa(Integer codPessoa) {
        this.codPessoa = codPessoa;
    }

    public ClienteTitularDependenteVO getClienteTitularDependenteVO() {
        return clienteTitularDependenteVO;
    }

    public void setClienteTitularDependenteVO(ClienteTitularDependenteVO clienteTitularDependenteVO) {
        this.clienteTitularDependenteVO = clienteTitularDependenteVO;
    }

    public Boolean getRetiraEdicaoPagamento() {
        return retiraEdicaoPagamento;
    }

    public void setRetiraEdicaoPagamento(Boolean retiraEdicaoPagamento) {
        this.retiraEdicaoPagamento = retiraEdicaoPagamento;
    }

    public Boolean getRetirarRecebiveisComPendencia() {
        return retirarRecebiveisComPendencia;
    }

    public void setRetirarRecebiveisComPendencia(Boolean retirarRecebiveisComPendencia) {
        this.retirarRecebiveisComPendencia = retirarRecebiveisComPendencia;
    }

    public List<PlanoVO> getPlanosFiltrar() {
        return planosFiltrar;
    }

    public void setPlanosFiltrar(List<PlanoVO> planosFiltrar) {
        this.planosFiltrar = planosFiltrar;
    }
}
