package relatorio.negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

/**
 * Created by glauco on 10/02/14.
 */
public class CompetenciaSinteticoRelTO extends SuperTO {

    private Integer codCliente;
    private String nomeCliente;
    private Integer codContrato;
    private Double valorCompetencia;
    private Date mesLancamentoProduto;
    private Integer codPlano;
    private String nomePlano;
    private Integer duracaoPlano;
    private String nomeEmpresa;


    public Integer getCodCliente() {
        return codCliente;
    }

    public void setCodCliente(Integer codCliente) {
        this.codCliente = codCliente;
    }

    public Integer getCodContrato() {
        return codContrato;
    }

    public void setCodContrato(Integer codContrato) {
        this.codContrato = codContrato;
    }

    public Double getValorCompetencia() {
        return valorCompetencia;
    }

    public void setValorCompetencia(Double valorCompetencia) {
        this.valorCompetencia = valorCompetencia;
    }

    public Date getMesLancamentoProduto() {
        return mesLancamentoProduto;
    }

    public void setMesLancamentoProduto(Date mesLancamentoProduto) {
        this.mesLancamentoProduto = mesLancamentoProduto;
    }

    public String getMesLancamentoProdutoApresentar() {
        if (getMesLancamentoProduto() != null) {
            return Uteis.getDataAplicandoFormatacao(getMesLancamentoProduto(), "MM/yyyy");
        } else {
            return "";
        }
    }

    public Integer getCodPlano() {
        return codPlano;
    }

    public void setCodPlano(Integer codPlano) {
        this.codPlano = codPlano;
    }

    public String getNomePlano() {
        return nomePlano;
    }

    public void setNomePlano(String nomePlano) {
        this.nomePlano = nomePlano;
    }

    public Integer getDuracaoPlano() {
        return duracaoPlano;
    }

    public void setDuracaoPlano(Integer duracaoPlano) {
        this.duracaoPlano = duracaoPlano;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public String getNomeEmpresa() {
        if (nomeEmpresa == null) {
            nomeEmpresa = "";
        }
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

}
