package relatorio.negocio.comuns.basico;

import java.util.ArrayList;
import java.util.List;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.TipoPendenciaEnum;

/**
 *
 * <AUTHOR>
 */
public class PendenciaRelVO extends SuperVO {

    private TipoPendenciaEnum tipo = TipoPendenciaEnum.NENHUM;
    private double valor = 0.0;
    private int qtd = 0;
    private List<PendenciaResumoPessoaRelVO> listaPendenciaResumoPessoaRelVOs = new ArrayList<PendenciaResumoPessoaRelVO>();
    private boolean exibirMensagem = false;
    private boolean exibirAutorizacaoCobrancaCliente = false;
    private boolean exibirInformacoesPessoa = true;
    private boolean exibirInformacoesFinanceiras = false;
    private EmpresaVO empresaVO = new EmpresaVO();

    public PendenciaRelVO() {
    }

    public PendenciaRelVO (PendenciaRelVO pendencia) {
        this.tipo = pendencia.tipo;
        this.valor = pendencia.valor;
        this.qtd = pendencia.qtd;
    }
    public int getQtd() {
        return qtd;
    }

    public void setQtd(int qtd) {
        this.qtd = qtd;
    }

    public TipoPendenciaEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoPendenciaEnum tipo) {
        this.tipo = tipo;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public List<PendenciaResumoPessoaRelVO> getListaPendenciaResumoPessoaRelVOs() {
        return listaPendenciaResumoPessoaRelVOs;
    }

    public void setListaPendenciaResumoPessoaRelVOs(List<PendenciaResumoPessoaRelVO> listaPendenciaResumoPessoaRelVOs) {
        this.listaPendenciaResumoPessoaRelVOs = listaPendenciaResumoPessoaRelVOs;
    }

    public boolean isExibirMensagem() {
        return exibirMensagem;
    }

    public void setExibirMensagem(boolean exibirMensagem) {
        this.exibirMensagem = exibirMensagem;
    }

    public String getOnComplete() {
        if (this.valor != 0.0 || this.qtd != 0) {
            if (this.getTipo().equals(TipoPendenciaEnum.ParcelaEmAbertoColaborador) || this.getTipo().equals(TipoPendenciaEnum.AniversariantesColaborador)) {
                return "abrirPopup('./pendenciaResumoColaboradorRes.jsp', 'PendenciaResumoColaboradorRes', 980, 700);";
            } else if (this.getTipo().equals(TipoPendenciaEnum.ClientesProdutosVencidos) || this.getTipo().equals(TipoPendenciaEnum.ClientesSemProdutos)) {
                return "abrirPopup('./relatorio/produtosRel.jsp', 'ProdutosRel', 1050, 620);";
            } else if (this.getTipo().equals(TipoPendenciaEnum.BVPendente) || this.getTipo().equals(TipoPendenciaEnum.CadastroIncompletoCliente)) {
                return "abrirPopup('./pendenciaResumoPessoaRel.jsp', 'PendenciaResumoPessoaRel', 980, 700);";
            } else if (this.getTipo().equals(TipoPendenciaEnum.CreditoCCorrente)) {
                return "abrirPopup('./includes/bi/lista_pendencia_credito.jsp', 'ClientesComCredito', 800, 650);";
            } else {
                return "abrirPopup('./pendenciaResumoPessoaRes.jsp', 'PendenciaResumoPessoaRel', 780, 595);";
            }
        } else {
            return "";
        }
    }
    public Boolean getVisitanteCadastroIncompleto(){
        return TipoPendenciaEnum.CadastroIncompletoVisitante == tipo;
    }
    public boolean isExibirAutorizacaoCobrancaCliente() {
        return exibirAutorizacaoCobrancaCliente;
    }
    public boolean isExibirInformacaoContrato(){
        return !(tipo == TipoPendenciaEnum.CadastroIncompletoVisitante || tipo == TipoPendenciaEnum.CartoesVencidos);
    }
    public void setExibirAutorizacaoCobrancaCliente(boolean exibirAutorizacaoCobrancaCliente) {
        this.exibirAutorizacaoCobrancaCliente = exibirAutorizacaoCobrancaCliente;
    }
    public Boolean getTpClienteMesmoCartao(){
        return  getTipo().equals(TipoPendenciaEnum.ClientesMesmoCartao);
    }
    public boolean isExibirInformacoesPessoa() {
        return exibirInformacoesPessoa;
    }

    public void setExibirInformacoesPessoa(boolean exibirInformacoesPessoa) {
        this.exibirInformacoesPessoa = exibirInformacoesPessoa;
    }
    public Boolean getExibirDebitoCC(){
        return tipo.equals(TipoPendenciaEnum.DebitoCCorrente);
    }
    public boolean isExibirInformacoesFinanceiras() {
        return exibirInformacoesFinanceiras;
    }

    public void setExibirInformacoesFinanceiras(boolean exibirInformacoesFinanceiras) {
        this.exibirInformacoesFinanceiras = exibirInformacoesFinanceiras;
    }

    public void ajustaQuantidade() {
        if (getQtd() == 0 && listaPendenciaResumoPessoaRelVOs != null)
            setQtd(listaPendenciaResumoPessoaRelVOs.size());
    }
    public int getOrdem(){
        return getTipo().getId();
    }

    public boolean isExibirQtdParcelasEmAtraso() {
        return tipo == TipoPendenciaEnum.ParcelaPendAtraso;
    }

    public boolean isExibirProblemaCartao(){
        return tipo == TipoPendenciaEnum.CartoesComProblema;
    }

    public boolean isExibirDtNascAniversariantes() {
        return tipo == TipoPendenciaEnum.Aniversariantes;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }
}
