package relatorio.negocio.comuns.basico;

import com.sun.istack.NotNull;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.plano.TurmaVO;

import java.util.ArrayList;
import java.util.List;

public class SGPModalidadeComTurmaTO extends SuperTO {
    private static final long serialVersionUID = 6202071184914396127L;
    private String dia;
    private int qtdeNaoSocio;
    private List<ClienteVO> listaNaoSocio;
    private int qtdeSemCategoria;
    private List<ClienteVO> listaSemCategoria;
    private int qtdeAlunos;
    private List<ClienteVO> listaAlunos;
    private int qtdeSocios;
    private List<ClienteVO> listaSocios;
    private int qtdeComerciario;
    private List<ClienteVO> listaComerciario;
    private int qtdeDependente;
    private List<ClienteVO> listaDependentes;
    private int qtdeUsuario;
    private List<ClienteVO> listaUsuarios;
    private int qtdeEventos;
    private List<ClienteVO> listaEventos;
    private int totalCategorias;
    private int contratosCanceladosDesistentes;
    private List<ClienteVO> listaCanceladosDesistentes;
    private int frequenciaPeriodo;
    private ModalidadeVO modalidadeVO = new ModalidadeVO();
    private TurmaVO turmaVO = new TurmaVO();
    private int qtdTurmasCriadasPeriodo;
    private int qtdTurmasCriadasAtivasPeriodo;
    private int qtdTurmasCriadasAtivasComAlunoPeriodo;
    private int qtdAulasPeriodo;
    private long qtdHoraAulaAtivaComAlunosPeriodoEmMinutos;
    private int frequenciaPossivel;
    private int totalVagasDisponiveis;

    public SGPModalidadeComTurmaTO() {
    }

    public void adicionaFrequencias(SGPModalidadeComTurmaTO frequencia) {
        this.qtdeNaoSocio += frequencia.getQtdeNaoSocio();
        this.qtdeAlunos += frequencia.getQtdeAlunos();
        this.qtdeSocios += frequencia.getQtdeSocios();
        this.qtdeComerciario += frequencia.getQtdeComerciario();
        this.qtdeDependente += frequencia.getQtdeDependente();
        this.qtdeUsuario += frequencia.getQtdeUsuario();
        this.qtdeEventos += frequencia.getQtdeEventos();
        this.contratosCanceladosDesistentes += frequencia.getContratosCanceladosDesistentes();
        this.frequenciaPeriodo += frequencia.getFrequenciaPeriodo();
        this.qtdAulasPeriodo += frequencia.getQtdAulasPeriodo();
        calculaTotalCategorias();
    }

    public void calculaTotalCategorias() {
        totalCategorias = qtdeNaoSocio + qtdeAlunos + qtdeSocios + qtdeComerciario + qtdeDependente + qtdeUsuario + qtdeEventos;
    }

    public ModalidadeVO getModalidadeVO() {
        return modalidadeVO;
    }

    public String getNomeModalidade() {
        return getModalidadeVO().getNome();
    }
    public String getNomeTurma() {
        return getTurmaVO()!=null ?getTurmaVO().getDescricao():"";
    }

    public void setModalidadeVO(ModalidadeVO modalidadeVO) {
        this.modalidadeVO = modalidadeVO;
    }

    public int getQtdeComerciario() {
        return qtdeComerciario;
    }

    public void setQtdeComerciario(int qtdeComerciario) {
        this.qtdeComerciario = qtdeComerciario;
    }

    public void addQtdeComerciario(int qtdeComerciario) {
        this.qtdeComerciario += qtdeComerciario;
    }

    public int getQtdeDependente() {
        return qtdeDependente;
    }

    public void setQtdeDependente(int qtdeDependente) {
        this.qtdeDependente = qtdeDependente;
    }

    public void addQtdeDependente(int qtdeDependente) {
        this.qtdeDependente += qtdeDependente;
    }

    public int getQtdeUsuario() {
        return qtdeUsuario;
    }

    public void setQtdeUsuario(int qtdeUsuario) {
        this.qtdeUsuario = qtdeUsuario;
    }

    public void addQtdeUsuario(int qtdeUsuario) {
        this.qtdeUsuario += qtdeUsuario;
    }

    public int getTotalCategorias() {
        return totalCategorias;
    }

    public void setTotalCategorias(int totalCategorias) {
        this.totalCategorias = totalCategorias;
    }

    public void addTotalCategorias(int totalCategorias) {
        this.totalCategorias += totalCategorias;
    }

    public int getContratosCanceladosDesistentes() {
        return contratosCanceladosDesistentes;
    }

    public void setContratosCanceladosDesistentes(int contratosCanceladosDesistentes) {
        this.contratosCanceladosDesistentes = contratosCanceladosDesistentes;
    }

    public int getFrequenciaPeriodo() {
        return frequenciaPeriodo;
    }

    public void setFrequenciaPeriodo(int frequenciaPeriodo) {
        this.frequenciaPeriodo = frequenciaPeriodo;
    }

    public int getQtdTurmasCriadasPeriodo() {
        return qtdTurmasCriadasPeriodo;
    }

    public void setQtdTurmasCriadasPeriodo(int qtdTurmasCriadasPeriodo) {
        this.qtdTurmasCriadasPeriodo = qtdTurmasCriadasPeriodo;
    }

    public int getQtdTurmasCriadasAtivasPeriodo() {
        return qtdTurmasCriadasAtivasPeriodo;
    }

    public void setQtdTurmasCriadasAtivasPeriodo(int qtdTurmasCriadasAtivasPeriodo) {
        this.qtdTurmasCriadasAtivasPeriodo = qtdTurmasCriadasAtivasPeriodo;
    }

    public int getQtdTurmasCriadasAtivasComAlunoPeriodo() {
        return qtdTurmasCriadasAtivasComAlunoPeriodo;
    }

    public void setQtdTurmasCriadasAtivasComAlunoPeriodo(int qtdTurmasCriadasAtivasComAlunoPeriodo) {
        this.qtdTurmasCriadasAtivasComAlunoPeriodo = qtdTurmasCriadasAtivasComAlunoPeriodo;
    }

    public long getQtdHoraAulaAtivaComAlunosPeriodoEmMinutos() {
        return qtdHoraAulaAtivaComAlunosPeriodoEmMinutos;
    }

    public void setQtdHoraAulaAtivaComAlunosPeriodoEmMinutos(long qtdHoraAulaAtivaComAlunosPeriodoEmMinutos) {
        this.qtdHoraAulaAtivaComAlunosPeriodoEmMinutos = qtdHoraAulaAtivaComAlunosPeriodoEmMinutos;
    }

    public int getQtdAulasPeriodo() {
        return qtdAulasPeriodo;
    }

    public void setQtdAulasPeriodo(int qtdAulasPeriodo) {
        this.qtdAulasPeriodo = qtdAulasPeriodo;
    }

    @Override
    public boolean equals(Object obj) {
        return obj instanceof SGPModalidadeComTurmaTO &&
                ((SGPModalidadeComTurmaTO) obj).getModalidadeVO().equals(this.modalidadeVO);
    }

    public int compareTo(@NotNull Object obj) {
        if (obj instanceof SGPModalidadeComTurmaTO) {
            return this.modalidadeVO.compareTo(((SGPModalidadeComTurmaTO) obj).getModalidadeVO());
        }
        return -1;
    }

    public List<ClienteVO> getListaComerciario() {
        if (listaComerciario == null) {
            listaComerciario = new ArrayList<>();
        }
        return listaComerciario;
    }

    public void setListaComerciario(List<ClienteVO> listaComerciario) {
        this.listaComerciario = listaComerciario;
    }

    public List<ClienteVO> getListaDependentes() {
        if (listaDependentes == null) {
            listaDependentes = new ArrayList<>();
        }
        return listaDependentes;
    }

    public void setListaDependentes(List<ClienteVO> listaDependentes) {
        this.listaDependentes = listaDependentes;
    }

    public List<ClienteVO> getListaUsuarios() {
        if (listaUsuarios == null) {
            listaUsuarios = new ArrayList<>();
        }
        return listaUsuarios;
    }

    public void setListaUsuarios(List<ClienteVO> listaUsuarios) {
        this.listaUsuarios = listaUsuarios;
    }

    public List<ClienteVO> getListaCanceladosDesistentes() {
        if (listaCanceladosDesistentes == null) {
            listaCanceladosDesistentes = new ArrayList<>();
        }
        return listaCanceladosDesistentes;
    }

    public void setListaCanceladosDesistentes(List<ClienteVO> listaCanceladosDesistentes) {
        this.listaCanceladosDesistentes = listaCanceladosDesistentes;
    }

    public int getFrequenciaPossivel() {
        return frequenciaPossivel;
    }

    public void setFrequenciaPossivel(int frequenciaPossivel) {
        this.frequenciaPossivel = frequenciaPossivel;
    }

    public int getTotalVagasDisponiveis() {
        return totalVagasDisponiveis;
    }

    public void setTotalVagasDisponiveis(int totalVagasDisponiveis) {
        this.totalVagasDisponiveis = totalVagasDisponiveis;
    }

    public int getQtdeNaoSocio() {
        return qtdeNaoSocio;
    }

    public void setQtdeNaoSocio(int qtdeNaoSocio) {
        this.qtdeNaoSocio = qtdeNaoSocio;
    }

    public void addQtdeNaoSocio(int qtdeNaoSocio) {
        this.qtdeNaoSocio += qtdeNaoSocio;
    }

    public List<ClienteVO> getListaNaoSocio() {
        if (listaNaoSocio == null) {
            listaNaoSocio = new ArrayList<>();
        }
        return listaNaoSocio;
    }

    public void setListaNaoSocio(List<ClienteVO> listaNaoSocio) {
        this.listaNaoSocio = listaNaoSocio;
    }

    public int getQtdeSemCategoria() {
        return qtdeSemCategoria;
    }

    public void setQtdeSemCategoria(int qtdeSemCategoria) {
        this.qtdeSemCategoria = qtdeSemCategoria;
    }

    public void addQtdeSemCategoria(int qtdeSemCategoria) {
        this.qtdeSemCategoria += qtdeSemCategoria;
    }

    public List<ClienteVO> getListaSemCategoria() {
        if (listaSemCategoria == null) {
            listaSemCategoria = new ArrayList<>();
        }
        return listaSemCategoria;
    }

    public void setListaSemCategoria(List<ClienteVO> listaSemCategoria) {
        this.listaSemCategoria = listaSemCategoria;
    }

    public int getQtdeAlunos() {
        return qtdeAlunos;
    }

    public void setQtdeAlunos(int qtdeAlunos) {
        this.qtdeAlunos = qtdeAlunos;
    }

    public void addQtdeAlunos(int qtdeAlunos) {
        this.qtdeAlunos += qtdeAlunos;
    }

    public List<ClienteVO> getListaAlunos() {
        if (listaAlunos == null) {
            listaAlunos = new ArrayList<>();
        }
        return listaAlunos;
    }

    public void setListaAlunos(List<ClienteVO> listaAlunos) {
        this.listaAlunos = listaAlunos;
    }

    public int getQtdeSocios() {
        return qtdeSocios;
    }

    public void setQtdeSocios(int qtdeSocios) {
        this.qtdeSocios = qtdeSocios;
    }

    public void addQtdeSocios(int qtdeSocios) {
        this.qtdeSocios += qtdeSocios;
    }

    public List<ClienteVO> getListaSocios() {
        if (listaSocios == null) {
            listaSocios = new ArrayList<>();
        }
        return listaSocios;
    }

    public void setListaSocios(List<ClienteVO> listaSocios) {
        this.listaSocios = listaSocios;
    }

    public int getQtdeEventos() {
        return qtdeEventos;
    }

    public void setQtdeEventos(int qtdeEventos) {
        this.qtdeEventos = qtdeEventos;
    }

    public void addQtdeEventos(int qtdeEventos) {
        this.qtdeEventos += qtdeEventos;
    }

    public List<ClienteVO> getListaEventos() {
        if (listaEventos == null) {
            listaEventos = new ArrayList<>();
        }
        return listaEventos;
    }

    public void setListaEventos(List<ClienteVO> listaEventos) {
        this.listaEventos = listaEventos;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getDiaOrTotal() {
        return this.dia == null ? this.modalidadeVO.getNome() : this.dia;
    }

    public String getCargaHorariaConvertida() {
        if (this.qtdHoraAulaAtivaComAlunosPeriodoEmMinutos == 0) {
            return "0,00";
        } else {
            double horasDecimais = (Uteis.arredondarForcando2CasasDecimais((double) this.qtdHoraAulaAtivaComAlunosPeriodoEmMinutos / 60));
            int horas = (int) horasDecimais;
            int minutos = (int) ((horasDecimais - horas) * 60);
            return String.format("%02d,%02d", horas, minutos);
        }
    }

    public TurmaVO getTurmaVO() {
        return turmaVO;
    }

    public void setTurmaVO(TurmaVO turmaVO) {
        this.turmaVO = turmaVO;
    }
}
