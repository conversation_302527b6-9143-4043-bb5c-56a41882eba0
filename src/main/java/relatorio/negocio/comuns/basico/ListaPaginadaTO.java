package relatorio.negocio.comuns.basico;

import negocio.comuns.utilitarias.UteisValidacao;

import java.io.Serializable;

/**
 * Created by <PERSON> on 23/05/2017.
 */
public class ListaPaginadaTO implements Serializable {
    private Integer offset = 0;
    private Integer limit = 0 ;
    private Integer count = 0;
    private String orderBy;
    private boolean orderByDesc = false;
    private String icon;
    private boolean desabilitarAcoes;

    public ListaPaginadaTO(Integer limit) {
        this.limit = limit;
    }

    public void proximaPagina(){
      if((offset+(limit)) < count) {
        offset += limit;
    }
    }
    public void paginaAnterior(){
        if((offset-limit) >= 0) {
        offset -= limit;
    }
    }
    public void primeiraPagina(){
        offset = 0;
    }
    public void ultimaPagina(){
        offset = ((count / limit) - (count % limit == 0 ? 1 : 0)) * limit;
    }
    public String getPaginaAtualApresentar(){
        if(UteisValidacao.emptyNumber(offset)){
            offset = 0;
        }
        if(UteisValidacao.emptyNumber(limit)){
            limit = 0;
        }
        return ((offset / limit) + 1) + "/"+ getNumerosDePaginasPorTotal();
    }

    public Integer getPaginaMostrandoAteApresentar(){
        if(offset+limit> count)return count;
        return offset+limit;

    }

    private Integer getNumerosDePaginasPorTotal(){
        if(UteisValidacao.emptyNumber(limit)){
            limit = 10;
        }
        if(UteisValidacao.emptyNumber(count)){
            count = 0;
        }
            int resto = (count % limit == 0 ? 0 : 1);
        return (getCount()/getLimit())+resto;
        }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getOrderBy() {
        if (orderBy == null) {
            orderBy = "";
        }
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public boolean isOrderByDesc() {
        return orderByDesc;
    }

    public void setOrderByDesc(boolean orderByDesc) {
        if (orderByDesc) {
            setIcon("fa-icon-angle-right");
        } else {
            setIcon("fa-icon-angle-left");
        }
        this.orderByDesc = orderByDesc;
    }

    public String getIcon() {
        if (icon == null) {
            icon = "fa-icon-exchange";
        }
        return icon;
    }

    public boolean isDesabilitarAcoes() {
        if (getCount()==0)
            desabilitarAcoes=  true;
        else if(getNumerosDePaginasPorTotal()==1)
            desabilitarAcoes= true;
        else
            desabilitarAcoes= false;
        return desabilitarAcoes;
    }

    public void setDesabilitarAcoes(boolean desabilitarAcoes) {
        this.desabilitarAcoes = desabilitarAcoes;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }
}
