/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.RespostaPerguntaVO;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class BVsRelatorioListasTO extends SuperTO {
    private static final long serialVersionUID = 4418501482338413170L;

    private RespostaPerguntaVO respostaVO;
    private Integer totalRespondidoPorResposta;
    private Double percentualRespondidoPorResposta;
    private Integer totalConvertidos;
    private List<String> tableNPS = new ArrayList<>();

    public BVsRelatorioListasTO() {
        tableNPS.add("Detratores");
        tableNPS.add("Neutros");
        tableNPS.add("Promotores");
    }



    /**
     * @return the respostaVOs
     */
    public RespostaPerguntaVO getRespostaVO() {
        return respostaVO;
    }

    /**
     * @param respostaVOs the respostaVOs to set
     */
    public void setRespostaVO(RespostaPerguntaVO respostaVO) {
        this.respostaVO = respostaVO;
    }

    /**
     * @return the totaisRespondidosPorResposta
     */
    public Integer getTotalRespondidoPorResposta() {
        return totalRespondidoPorResposta;
    }

    /**
     * @param totaisRespondidosPorResposta the totaisRespondidosPorResposta to set
     */
    public void setTotalRespondidoPorResposta(Integer totalRespondidoPorResposta) {
        this.totalRespondidoPorResposta = totalRespondidoPorResposta;
    }

    /**
     * @return the percentualRespondidoPorResposta
     */
    public Double getPercentualRespondidoPorResposta() {
        return percentualRespondidoPorResposta;
    }

    /**
     * @param percentualRespondidoPorResposta the percentualRespondidoPorResposta to set
     */
    public void setPercentualRespondidoPorResposta(Double percentualRespondidoPorResposta) {
        this.percentualRespondidoPorResposta = percentualRespondidoPorResposta;
    }

    public Integer getTotalConvertidos() {
        return totalConvertidos;
    }

    public void setTotalConvertidos(Integer totalConvertidos) {
        this.totalConvertidos = totalConvertidos;
    }

    public List<String> getTableNPS() {
        return tableNPS;
    }

    public void setTableNPS(List<String> tableNPS) {
        this.tableNPS = tableNPS;
    }
}
