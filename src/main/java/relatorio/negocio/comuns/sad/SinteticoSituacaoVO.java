/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.sad;

import java.util.Iterator;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR>
 */
public class SinteticoSituacaoVO extends SuperVO {

    protected String nome;
    protected Boolean marcado;

    public SinteticoSituacaoVO() {
        super();
        inicializarDados();
    }

    public void inicializarDados() {
        setNome("");
        setMarcado(new Boolean(false));
    }

    public void adicionarObjClienteSituacaoVOs(List listaSituacao, SinteticoSituacaoVO obj) throws Exception {
        int index = 0;
        Iterator i = listaSituacao.iterator();
        while (i.hasNext()) {
            SinteticoSituacaoVO objExistente = (SinteticoSituacaoVO) i.next();
            if (objExistente.getNome().equals(obj.getNome())) {
                listaSituacao.set(index, obj);
                return;
            }
            index++;
        }
        listaSituacao.add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe <code>ClienteClassificacaoVO</code>
     * no List <code>clienteClassificacaoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ClienteClassificacao</code> - getClassificacao().getCodigo() - como identificador (key) do objeto no List.
     * @param classificacao  Parâmetro para localizar e remover o objeto do List.
     */
    public static void excluirObjClienteSituacaoVOs(List listaSituacao, String situacao) throws Exception {
        int index = 0;
        Iterator i = listaSituacao.iterator();
        while (i.hasNext()) {
            SinteticoSituacaoVO objExistente = (SinteticoSituacaoVO) i.next();
            if (objExistente.getNome().equals(situacao)) {
                listaSituacao.remove(index);
                return;
            }
            index++;
        }
    }

    public Boolean getMarcado() {
        return marcado;
    }

    public void setMarcado(Boolean marcado) {
        this.marcado = marcado;
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getNome_Apresentar() {
        if (nome == null) {
            return "";
        }
        if (nome.equals("VI")) {
            return "Visitantes";
        }
        if (nome.equals("PL")) {
            return "Visitantes - Free Pass";
        }
        if (nome.equals("AA")) {
            return "Visitantes - Aula Avulsa";
        }
        if (nome.equals("DI")) {
            return "Visitantes - Diária";
        }
        if (nome.equals("IN")) {
            return "Inativos";
        }
        if (nome.equals("CA")) {
            return "Inativos - Cancelados";
        }
        if (nome.equals("DE")) {
            return "Inativos - Desistentes";
        }
        if (nome.equals("AT")) {
            return "Ativos";
        }
        if (nome.equals("NO")) {
            return "Ativos - Normais";
        }
        if (nome.equals("TR")) {
            return "Trancados";
        }
        if (nome.equals("TV")) {
            return "Trancados Vencidos";
        }
        if (nome.equals("AV")) {
            return "Ativos - A Vencer";
        }
        if (nome.equals("VE")) {
            return "Inativos - Vencidos";
        }
        if (nome.equals("CR")) {
            return "Ativos - Férias";
        }
        if (nome.equals("AE")) {
            return "Ativos - Atestado";
        }
        return nome;
    }
}
