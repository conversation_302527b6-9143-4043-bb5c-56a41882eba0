
package relatorio.negocio.comuns.sad;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class MetaCrescimentoVO extends SuperVO {
    // empresa da meta
    private EmpresaVO empresa = new EmpresaVO();
    // ano da meta
    private int ano;
    // mes da meta
    private int mes;

    // total no início do mes
    private int totalInicial;
    // meta de crescimento para o mes atual
    private int metaCrescimento;
    // total final alcançado
    private int totalFinal;

    // total previsto a renovar
    private int qtdePrevistosRenovar;
    // média de renovações do ano anterior
    private double IRAnterior;
    // média de renovações informada
    private double IRInformado;
    // total de renovacoes realizadas
    private int totalRenovacoes;
    // total de renovacoes atrasadas do mes anterior
    private int totalRenovacoesAtrasadas;

    // media de vendas do ano anterior
    private double ICVAnterior;
    // media de vendas informada
    private double ICVInformado;
    // total de vendas realizadas
    private int totalVendas;
    // total de visitas realizadas
    private int totalVisitas;

    // total de cancelados do mes
    private int totalCancelados;
    // media de cancelados do ano anterior
    private double ICanceladosAnterior;
    // media de cancelados informado
    private double ICanceladosInformado;

    // media de trancados do mes
    private int totalTrancados;
    // media de trancados do ano anterior
    private double ITrancadosAnterior;
    // media de trancados informado
    private double ITrancadosInformado;

    public static void validarDados(MetaCrescimentoVO meta) throws Exception {
        if(meta == null)
            throw new Exception("Objeto Encontrado é Nulo! Contate o Administrador.");
        if(meta.getEmpresa().getCodigo() == 0)
            throw new Exception("Informe a Empresa da Meta.");
        if(meta.getAno() == 0)
            throw new Exception("Informe o Ano da Meta.");
        if(meta.getMes() == 0)
            throw new Exception("Informe o Mês da Meta.");
    }

    public double getTotalRenovacoesEstimada() {
        // calcula a qtde de renovacoes necessarias para atingir a meta
        return Uteis.arredondar(qtdePrevistosRenovar * IRInformado / 100, 0, 0);
    }

    public int getTotalRenovacoesFaltam() {
        // calcula a qtde de renovacoes que faltam para atingir a meta
        int falta = (int) (getTotalRenovacoesEstimada() - totalRenovacoes);
        return (falta < 0 ? 0 : falta);
    }

    public int getPerdaEstimada() {
        // calcula a qtde de clientes que sao perdidos por mes
        return (int) (getNaoRenovacoesEstimada() + getCancelamentosEstimados() + getTrancadosEstimados());
    }

    public int getTotalPerda() {
        return qtdePrevistosRenovar - totalRenovacoes + getTotalCancelados() + getTotalTrancados();
    }

    public double getNaoRenovacoesEstimada() {
        // calcula a qtde de nao renovacoes
        return Uteis.arredondar(qtdePrevistosRenovar * (1 - IRInformado / 100), 0, 0);
    }

    public double getCancelamentosEstimados() {
        // calcula a qtde de cancelamentos
        return Uteis.arredondar(totalInicial * ICanceladosInformado / 100, 0, 0);
    }

    public double getTrancadosEstimados() {
        // calcula a qtde de cancelamentos
        return Uteis.arredondar(totalInicial * ITrancadosInformado / 100, 0, 0);
    }

    public int getTotalVendasNecessarias() {
        // calcula a qtde de vendas necessárias para atingir a meta
        return (int)(getPerdaEstimada() + metaCrescimento - getTotalRenovacoesAtrasadas());
    }

    public int getTotalVendasFaltam() {
        // calcula a qtde de vendas que faltam para atingir a meta
        int falta = (int)(getTotalVendasNecessarias() - getTotalVendas());
        return (falta < 0 ? 0 : falta);
    }

    public double getTotalVisitasNecessarias() {
        if(ICVInformado == 0.0) return 0.0;
        // calcula a qtde de visitas necessárias para atingir a meta
        return Uteis.arredondar(getTotalVendasNecessarias() * 100 / ICVInformado, 0, 0);
    }

    public int getTotalVisitasFaltam() {
        // calcula a qtde de visitas que faltam para atingir a meta
        int falta = (int)(getTotalVisitasNecessarias() - getTotalVisitas());
        return (falta < 0 ? 0 : falta);
    }

    public double getICVRealizado() {
        if(totalVisitas == 0) return 0.0;
        // calcula a média de vendas realizadas
        return Uteis.arredondar(totalVendas * 100 / totalVisitas, 0, 0);
    }
    
    public double getICanceladosRealizado() {
        if(totalCancelados == 0) return 0.0;
        // calcula a média de vendas realizadas
        return Uteis.arredondar(totalInicial * 100 / totalCancelados, 0, 0);
    }

    public double getITrancadosRealizado() {
        if(totalTrancados == 0) return 0.0;
        // calcula a média de vendas realizadas
        return Uteis.arredondar(totalInicial * 100 / totalTrancados, 0, 0);
    }

    public double getIRRealizado() {
        if(totalRenovacoes == 0 || qtdePrevistosRenovar == 0) return 0.0;
        // calcula a média de vendas realizadas
        return Uteis.arredondar(totalRenovacoes * 100 / qtdePrevistosRenovar, 0, 0);
    }

    public int getTotalFinalDaMeta() {
        // calcula a qtde esperada de cliente ao terminar o mes
        return totalInicial + metaCrescimento;
    }

    public double getIMeta() {
        if(totalInicial == 0) return 0;
        // calcula o percentual que a meta representa no total
        return Uteis.arredondar(metaCrescimento * 100 / totalInicial, 2, 0);
    }

    public int getTotalFinalFaltam() {
        // calcula quantos faltaram para cumprir a meta ao final
        int falta = getTotalFinalDaMeta() - totalFinal;
        return (falta < 0 ? 0 : falta);
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public int getAno() {
        return ano;
    }

    public void setAno(int ano) {
        this.ano = ano;
    }

    public int getMes() {
        return mes;
    }

    public void setMes(int mes) {
        this.mes = mes;
    }

    public int getTotalInicial() {
        return totalInicial;
    }

    public void setTotalInicial(int totalInicial) {
        this.totalInicial = totalInicial;
    }

    public int getMetaCrescimento() {
        return metaCrescimento;
    }

    public void setMetaCrescimento(int metaCrescimento) {
        this.metaCrescimento = metaCrescimento;
    }

    public int getTotalFinal() {
        return totalFinal;
    }

    public void setTotalFinal(int totalFinal) {
        this.totalFinal = totalFinal;
    }

    public int getQtdePrevistosRenovar() {
        return qtdePrevistosRenovar;
    }

    public void setQtdePrevistosRenovar(int qtdePrevistosRenovar) {
        this.qtdePrevistosRenovar = qtdePrevistosRenovar;
    }

    public double getIRAnterior() {
        return IRAnterior;
    }

    public void setIRAnterior(double IRAnterior) {
        this.IRAnterior = IRAnterior;
    }

    public double getIRInformado() {
        if(IRInformado == 0) IRInformado = IRAnterior;
        return IRInformado;
    }

    public void setIRInformado(double IRInformado) {
        this.IRInformado = IRInformado;
    }

    public int getTotalRenovacoes() {
        return totalRenovacoes;
    }

    public void setTotalRenovacoes(int totalRenovacoes) {
        this.totalRenovacoes = totalRenovacoes;
    }

    public int getTotalRenovacoesAtrasadas() {
        return totalRenovacoesAtrasadas;
    }

    public void setTotalRenovacoesAtrasadas(int totalRenovacoesAtrasadas) {
        this.totalRenovacoesAtrasadas = totalRenovacoesAtrasadas;
    }

    public double getICVAnterior() {
        return ICVAnterior;
    }

    public void setICVAnterior(double ICVAnterior) {
        this.ICVAnterior = ICVAnterior;
    }

    public double getICVInformado() {
        if(ICVInformado == 0) ICVInformado = ICVAnterior;
        return ICVInformado;
    }

    public void setICVInformado(double ICVInformado) {
        this.ICVInformado = ICVInformado;
    }

    public int getTotalVendas() {
        return totalVendas;
    }

    public void setTotalVendas(int totalVendas) {
        this.totalVendas = totalVendas;
    }

    public int getTotalVisitas() {
        return totalVisitas;
    }

    public void setTotalVisitas(int totalVisitas) {
        this.totalVisitas = totalVisitas;
    }

    public int getTotalCancelados() {
        return totalCancelados;
    }

    public void setTotalCancelados(int totalCancelados) {
        this.totalCancelados = totalCancelados;
    }

    public double getICanceladosAnterior() {
        return ICanceladosAnterior;
    }

    public void setICanceladosAnterior(double ICanceladosAnterior) {
        this.ICanceladosAnterior = ICanceladosAnterior;
    }

    public double getICanceladosInformado() {
        if(ICanceladosInformado == 0) ICanceladosInformado = ICanceladosAnterior;
        return ICanceladosInformado;
    }

    public void setICanceladosInformado(double ICanceladosInformado) {
        this.ICanceladosInformado = ICanceladosInformado;
    }

    public int getTotalTrancados() {
        return totalTrancados;
    }

    public void setTotalTrancados(int totalTrancados) {
        this.totalTrancados = totalTrancados;
    }

    public double getITrancadosAnterior() {
        return ITrancadosAnterior;
    }

    public void setITrancadosAnterior(double ITrancadosAnterior) {
        this.ITrancadosAnterior = ITrancadosAnterior;
    }

    public double getITrancadosInformado() {
        if(ITrancadosInformado == 0) ITrancadosInformado = ITrancadosAnterior;
        return ITrancadosInformado;
    }

    public void setITrancadosInformado(double ITrancadosInformado) {
        this.ITrancadosInformado = ITrancadosInformado;
    }
}
