/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.controle.sad;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import org.json.JSONArray;
import org.json.JSONObject;
import controle.arquitetura.SuperControle;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import javax.faces.model.SelectItem;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.conviteaulaexperimental.ConviteAulaExperimentalBITO;
import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import relatorio.controle.basico.BIControle;

import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

/**
 *
 * <AUTHOR>
 */
public class BIConviteAulaExperimentalControle extends BIControle {

    private ConviteAulaExperimentalBITO bi;
    private List<ConviteAulaExperimentalBITO> lista;
    private JSONArray dadosGrafico;
    private Date inicio = null;
    private Date fim = Calendario.hoje();
    private String clienteConvidou = "";
    private String usuarioConvidou = "";
    private String convidado = "";
    private Boolean todos = true;
    private Boolean validou = false;
    private Boolean agendou = false;
    private Boolean compareceu = false;
    private List<SelectItem> tipos = null;
    private Integer tipo = null;
    private Date dataBase;

    public BIConviteAulaExperimentalControle() {
        super();
    }

    public void atualizar() {
        try {
            bi = getFacade().getConviteAulaExperimental().gerarBI(getDataBaseFiltroBI(), getEmpresaFiltro().getCodigo());
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void abrirBI() {
        try {
            if (inicio == null) {
                inicio = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
            }
            if(tipos == null){
                tipos = new ArrayList<SelectItem>();
                List<TipoConviteAulaExperimentalVO> todosTipos = getFacade().getTipoConviteAulaExperimental().consultarTodos(getEmpresaFiltro().getCodigo());
                todosTipos = Ordenacao.ordenarLista(todosTipos, "descricao");
                for(TipoConviteAulaExperimentalVO t : todosTipos){
                    tipos.add(new SelectItem(t.getCodigo(), t.getDescricao()));
                }
            }
            consultarLista();
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void consultarLista() {
        try {

            lista = getFacade().getConviteAulaExperimental().consultarRelatorio(inicio, fim, getEmpresaFiltro().getCodigo(),
                    clienteConvidou, usuarioConvidou, convidado,
                    todos, validou, agendou, compareceu, tipo);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public ConviteAulaExperimentalBITO getBi() {
        return bi;
    }

    public void setBi(ConviteAulaExperimentalBITO bi) {
        this.bi = bi;
    }

    public EmpresaVO getEmpresaFiltro() {
        return (EmpresaVO) JSFUtilities.getManagedBean("BIControle.empresaFiltro");
    }

    public JSONArray getDadosGrafico() {
        return dadosGrafico;
    }

    public void setDadosGrafico(JSONArray dadosGrafico) {
        this.dadosGrafico = dadosGrafico;
    }

    public void abrirGrafico() {
        try {
            List<ConviteAulaExperimentalBITO> listaBI = getFacade().getConviteAulaExperimental().obterListaBI(null, getEmpresaFiltro().getCodigo());
            dadosGrafico = new JSONArray();
            listaBI = Ordenacao.ordenarLista(listaBI, "dia");
            for (ConviteAulaExperimentalBITO cae : listaBI) {
                JSONObject json = new JSONObject();
                json.put("convertidos", cae.getCompraramContrato());
                json.put("enviados", cae.getConvitesEnviados());
                json.put("validados", cae.getConvitesValidados());
                json.put("dia", cae.getDia());
                json.put("agendaram", cae.getAgendaramAula());
                json.put("compareceram", cae.getAgendaramCompareceram());
                dadosGrafico.put(json);
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public String getJsonGrafico() {
        return dadosGrafico == null ? new JSONArray().toString() : dadosGrafico.toString();
    }

    public List<ConviteAulaExperimentalBITO> getLista() {
        return lista;
    }

    public void setLista(List<ConviteAulaExperimentalBITO> lista) {
        this.lista = lista;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public String getClienteConvidou() {
        return clienteConvidou;
    }

    public void setClienteConvidou(String clienteConvidou) {
        this.clienteConvidou = clienteConvidou;
    }

    public String getUsuarioConvidou() {
        return usuarioConvidou;
    }

    public void setUsuarioConvidou(String usuarioConvidou) {
        this.usuarioConvidou = usuarioConvidou;
    }

    public String getConvidado() {
        return convidado;
    }

    public void setConvidado(String convidado) {
        this.convidado = convidado;
    }

    public Boolean getTodos() {
        return todos;
    }

    public void setTodos(Boolean todos) {
        this.todos = todos;
    }

    public Boolean getValidou() {
        return validou;
    }

    public void setValidou(Boolean validou) {
        this.validou = validou;
    }

    public Boolean getAgendou() {
        return agendou;
    }

    public void setAgendou(Boolean agendou) {
        this.agendou = agendou;
    }

    public Boolean getCompareceu() {
        return compareceu;
    }

    public void setCompareceu(Boolean compareceu) {
        this.compareceu = compareceu;
    }

    public List<SelectItem> getTipos() {
        return tipos;
    }

    public void setTipos(List<SelectItem> tipos) {
        this.tipos = tipos;
    }

    public Integer getTipo() {
        return tipo;
    }

    public void setTipo(Integer tipo) {
        this.tipo = tipo;
    }

    public Date getDataBase() {
        return dataBase;
    }

    public void setDataBase(Date dataBase) {
        this.dataBase = dataBase;
    }

    public String getDataBase_ApresentarMes(){
        DateFormat dfmt = new SimpleDateFormat(" MMMM 'de' yyyy");
        Calendar cal = Calendario.getInstance();
        cal.setTime(getDataBase());
        return dfmt.format(cal.getTime());
    }
    public String getDataBase_ApresentarMesDia(){
        DateFormat dfmt = new SimpleDateFormat("dd 'de' MMMM 'de' yyyy");
        Calendar cal = Calendario.getInstance();
        cal.setTime(getDataBase());
        return dfmt.format(cal.getTime());
    }
}
