package relatorio.controle.basico;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeManager;

import java.util.Date;

public class RecorrenciaClienteTO extends SuperTO {

    private String matricula = "";
    private Integer codigoCliente = 0;
    private Integer nrParcela = 0;
    private String nomeCliente = "";
    private Integer codigoContrato = 0;
    private Date dataLancamento = null;
    private Date dataVencimento = null;
    private Date dataInicio = null;
    private Date dataTermino = null;
    private Integer duracao = 0;
    private Integer nrParcelasEA = 0;
    private Double valor;
    private Integer codigoParcela = 0;
    private String descricaoParcela = "";
    private String suspeita = "";
    private String cpf;
    private String empresa;
    private String tipo;
    private String situacaoCliente;
    private String descricaoConvenio;
    private String descricaoFormaPagamento;

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getDataLancamento_Apresentar() {
        return Uteis.getData(dataLancamento);
    }

    public Date getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(Date dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public String getDataVencimento_Apresentar() {
        return Uteis.getData(dataVencimento);
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataInicio_Apresentar() {
        return Uteis.getData(dataInicio);
    }

    public Date getDataTermino() {
        return dataTermino;
    }

    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    public String getDataTermino_Apresentar() {
        return Uteis.getData(dataTermino);
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getCodigoParcela() {
        return codigoParcela;
    }

    public void setCodigoParcela(Integer codigoParcela) {
        this.codigoParcela = codigoParcela;
    }

    public String getDescricaoParcela() {
        return descricaoParcela;
    }

    public void setDescricaoParcela(String descricaoParcela) {
        this.descricaoParcela = descricaoParcela;
    }

    public String getSuspeita() {
        return suspeita;
    }

    public void setSuspeita(String suspeita) {
        this.suspeita = suspeita;
    }

    public Integer getNrParcelasEA() {
        return nrParcelasEA;
    }
    public String getValor_Apresentar(){
        return Formatador.formatarValorMonetario(valor);
    }
    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public void setNrParcelasEA(Integer nrParcelasEA) {
        this.nrParcelasEA = nrParcelasEA;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public Integer getNrParcela() {
        if(UteisValidacao.emptyNumber(nrParcela)){
            try{
                // considerar que o número da parcela são as duas últimas letras da descrição da parcela.
                if (this.descricaoParcela != null){
                    String nrParcelastr = descricaoParcela.substring(descricaoParcela.indexOf("PARCELA") + 7,
                            descricaoParcela.length()).trim();
                    return Integer.parseInt(nrParcelastr);
                }
            }catch (Exception e){
                nrParcela = 1;
            }
        }
        return nrParcela;
    }

    public void setNrParcela(Integer nrParcela) {
        this.nrParcela = nrParcela;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getSituacaoCliente() {
        return situacaoCliente;
    }

    public void setSituacaoCliente(String situacaoCliente) {
        this.situacaoCliente = situacaoCliente;
    }

    public String getDescricaoConvenio() {
        return descricaoConvenio;
    }

    public void setDescricaoConvenio(String descricaoConvenio) {
        this.descricaoConvenio = descricaoConvenio;
    }

    public String getDescricaoFormaPagamento() {
        return descricaoFormaPagamento;
    }

    public void setDescricaoFormaPagamento(String descricaoFormaPagamento) {
        this.descricaoFormaPagamento = descricaoFormaPagamento;
    }

    public String getDescricaoPlano() throws Exception {
        String descricaoPlano = FacadeManager.getFacade().getPlano().consultarPorCodigoContrato(this.codigoContrato, false, Uteis.NIVELMONTARDADOS_MINIMOS).getDescricao();
        if(descricaoPlano == null) {
            return "-";
        } else {
            return descricaoPlano;
        }
    }
}
