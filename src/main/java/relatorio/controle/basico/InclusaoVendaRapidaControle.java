package relatorio.controle.basico;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaColaboradorVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.OcorrenciaEnum;
import br.com.pactosolucoes.enumeradores.SituacaoContratoEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import br.com.pactosolucoes.integracao.aragorn.AragornService;
import br.com.pactosolucoes.notificacao.ServicoNotificacaoPush;
import controle.basico.ClienteControle;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.FormaPagamentoVendaRapidaEnum;
import negocio.comuns.basico.enumerador.TipoParcelamentoVendaRapidaEnum;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.EventoVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.PessoaCPFTO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoBoletoEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.plano.PlanoAnuidadeParcelaWS;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoEmpresaVO;
import negocio.comuns.plano.PlanoRecorrenciaParcelaWS;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.PlanoWS;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ContadorTempo;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.oamd.CampanhaCupomDescontoPremioPortador;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.oamd.CampanhaCupomDescontoPremioPortadorVO;
import negocio.oamd.CupomDescontoVO;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryMsService;
import servicos.impl.cieloecommerce.CieloeCommerceService;
import servicos.impl.cieloecommerce.ValidacaoCartao;
import servicos.impl.cliente.VendaRapidaRecorrenteServiceImpl;
import servicos.impl.microsservice.integracoes.IntegracoesMSException;
import servicos.impl.microsservice.integracoes.IntegracoesMSService;
import servicos.impl.microsservice.integracoes.RetornoConsultaCDLTO;
import servicos.impl.oamd.OAMDService;
import servicos.integracao.pjbank.recebimento.BoletosManager;
import servicos.propriedades.PropsService;
import servicos.vendasonline.VendasOnlineService;
import servicos.vendasonline.dto.RetornoVendaTO;
import servicos.vendasonline.dto.VendaDTO;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

public class InclusaoVendaRapidaControle extends ClienteControle {

    private List<SelectItem> planos = new ArrayList<>();
    private List<SelectItem> parcelasAdesao = new ArrayList<>();
    private List<SelectItem> convenios = null;
    private Integer convenioSelecionado = null;
    private PlanoVO planoVOSelecionado;
    private Integer convenioSelecionadoCodigoBanco = null;
    private boolean dcoContaValida = false;
    private boolean dcoCpfValido = false;
    private String erroValidacaoDcoConta = "";
    private Integer planoSelecionado = null;
    private Integer nrVezesParcelarAdesao = 1;
    private PlanoWS plano;
    private Integer consultor = null;
    private TipoAutorizacaoCobrancaEnum tipoAutorizacao = null;
    private AutorizacaoCobrancaClienteVO autorizacao = new AutorizacaoCobrancaClienteVO();
    private AutorizacaoCobrancaColaboradorVO autorizacaoColaborador = new AutorizacaoCobrancaColaboradorVO();
    private TipoPagamentoAluno tipoPagamentoAluno = null;
    private boolean parcelarAdesao = false;
    private boolean temParcelaEmAberto = false;
    private boolean barrarDevedorVendaRapida = false;
    private List<ConvenioCobrancaVO> conveniosCobranca = new ArrayList<>();
    private String diaVencimento;
    private List<SelectItem> listaDiaVencimento;
    private ClienteVO indicadoPor;
    private String nomeConvidado;
    private boolean exibirInputIndicadoPor = false;
    private boolean cadastrandoParaConvite = false;
    private String numeroCupomAplicar;
    private String numeroCupomAplicado;
    private String msgInadimplencia;
    private JSONObject jsonInadimplente;
    private List<TipoColaboradorEnum> tiposColaboradoresPersonal = new ArrayList<>();
    private List<ColaboradorVO> colaboradoresPersonal = new ArrayList<>();
    private ColaboradorVO colaboradorPersonalSelecionado;
    private boolean adicionarEnderecoComercial = false;
    private boolean existeCampanhaCupomDescontoVigente;
    private Integer nrVezesParcelarProduto = 1;
    private boolean exibirValidacaoCpf;
    private Integer codigoEventosSelecionado;
    protected List<SelectItem> listaSelectEventoVigente;
    private List<SelectItem> listaFormaPagamento;
    private int formaPagamentoSelecinada = FormaPagamentoVendaRapidaEnum.CARTAO_CREDITO.getCodigo();
    private List<SelectItem> listaTipoParcelamento;
    private int tipoParcelamentoEscolhido = TipoParcelamentoVendaRapidaEnum.PARCELAMENTO_RECORRENTE.getCodigo();
    private int quantidadeParcelamento=0;
    private List<SelectItem> listaQuantidadeParcelas;
    private boolean permitirMudarTipoParcelamentoVendaRapida = false;
    private Integer codigoContratoBase = 0;
    private String vigenciaAteAjustadaContratoBase;
    private String situacaoContratoBase = "";
    private List<ConvenioCobrancaVO> conveniosCobrancaEmpresa;
    private List<BoletoVO> listaBoletosOnlineGerados;
    private List<SelectItem> selectItemConvenioCobrancaBoleto;
    private Integer convenioCobrancaBoletoSelecionado;
    private boolean telaBoletos = false;
    private boolean responsavelObrigatorio = false;
    private boolean apresentarDependente = false;
    private String msgCarregando;
    private boolean pollAtivo = false;

    private String responsavel = "";
    private ClienteVO clienteDependente;
    private boolean pessoaEstrangeira = false;
    private List<CampanhaCupomDescontoPremioPortadorVO> listaPremioPortadorCupom;
    private boolean lancarConcomitante = false;

    public InclusaoVendaRapidaControle() throws Exception {
        super();
        inicializar();
        setLancarConcomitante(false);
        String convidou = JSFUtilities.getFromRequestParameterMap("convidou");
        String convidado = JSFUtilities.getFromRequestParameterMap("convidado");
        if (!UteisValidacao.emptyString(convidou)) {
            cadastrandoParaConvite = true;
            indicadoPor = getFacade().getCliente().consultarPorMatricula(convidou, true, Uteis.NIVELMONTARDADOS_MINIMOS);
            nomeConvidado = convidado;
        }
        String codigoCliente = JSFUtilities.getFromRequestParameterMap("cliente");
        if (!UteisValidacao.emptyString(codigoCliente)) {
            selecionarClienteVenda(getFacade().getCliente().consultarPorCodigo(Integer.parseInt(codigoCliente),false, Uteis.NIVELMONTARDADOS_TODOS));
        }
        String prSituacaoContratoBase = JSFUtilities.getFromRequestParameterMap("situacaoContratoBase");
        String codigoPlano = JSFUtilities.getFromRequestParameterMap("plano");
        if (!UteisValidacao.emptyString(codigoPlano)) {
            setPlanoSelecionado(Integer.parseInt(codigoPlano));
            montarPlanoSelecionado(prSituacaoContratoBase.equals("RN"));
        }
        String contratoBase = JSFUtilities.getFromRequestParameterMap("contratoBase");
        if (!UteisValidacao.emptyString(contratoBase)) {
            setCodigoContratoBase(Integer.parseInt(contratoBase));
        }
        if (!UteisValidacao.emptyString(prSituacaoContratoBase)) {
            setSituacaoContratoBase(prSituacaoContratoBase);

            if (!UteisValidacao.emptyNumber(getCodigoContratoBase())) {
                ContratoVO contratoVOBase = getFacade().getContrato().consultarPorCodigo(getCodigoContratoBase(), Uteis.NIVELMONTARDADOS_MINIMOS);
                setVigenciaAteAjustadaContratoBase(contratoVOBase.getVigenciaAteAjustada_Apresentar());
            }
        }

        String uuidBoleto = JSFUtilities.getFromRequestParameterMap("tkb");
        if (!UteisValidacao.emptyString(uuidBoleto)) {
            try {
                this.setListaBoletosOnlineGerados(new ArrayList<>());

                BoletoVendaRapidaTO dto = (BoletoVendaRapidaTO) JSFUtilities.getFromSession(uuidBoleto);
                for (Integer boleto : dto.getBoletos()) {
                    try {
                        BoletoVO boletoVO = getFacade().getBoleto().consultarPorChavePrimaria(boleto, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                        this.getListaBoletosOnlineGerados().add(boletoVO);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }

                this.getEmpresa().setCodigo(dto.getEmpresa());
                alterarEmpresa();
                this.setConvenioCobrancaBoletoSelecionado(dto.getConvenio());
                this.setTelaBoletos(true);
                if (!UteisValidacao.emptyNumber(dto.getCliente())) {
                    this.setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(dto.getCliente(), Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA));
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    public void limparForma() {
        tipoAutorizacao = null;
        tipoPagamentoAluno = null;
        convenios = null;
        convenioSelecionado = null;
    }

    public String irParaCaixa() throws Exception {
        return irParaCaixaEmAberto(null, null, "", "");
    }

    private void inicializar() {
        try {
            barrarDevedorVendaRapida = getValorCampoConfiguracaoSistema("barrarDevedorVendaRapida", getKey())
                    .equals("t");
            plano = new PlanoWS();
            inicializarConfiguracaoSistema(true);

            permitirMudarTipoParcelamentoVendaRapida = getConfiguracaoSistema().isPermitirMudarTipoParcelamentoVendaRapida();

            adicionarPessoa();
            montarListaEmpresas();
            setEmpresa(getEmpresaLogado());
            montarListaSelectItemConsultor();
            montarListaSelectItemEventoVigente();
            montarPlanosClienteSelecionado(0);
            montarConveniosCobrancaEmpresa();
            limparEnderecosTelefones();
            montarPlanoSelecionado();
            inicializarTiposColaboraresPersonal();
            if (getUsuarioLogado().getColaboradorVO() != null) {
                for (Object o : getListaSelectConsultor()) {
                    if (getUsuarioLogado().getColaboradorVO().getCodigo().equals(((SelectItem) o).getValue())) {
                        consultor = getUsuarioLogado().getColaboradorVO().getCodigo();
                    }
                }
            }

            processarExibicaoInputIndicadoPor();
            montarListaSelectItemConsultor();
        } catch (Exception e) {
            montarErro(e);
        }
    }

    @Override
    public void montarListaSelectItemConsultor() {
        List resultadoConsulta = consultarColaboradorPorSituacao();
        preencherListaSelectConsultor(resultadoConsulta);
    }

    public void montarListaSelectItemEventoVigente()throws Exception{
        List<EventoVO> lista = getFacade().getEvento().consultarEventosVigente();
        listaSelectEventoVigente = new ArrayList<>();
        for (EventoVO eventoVO: lista){
            listaSelectEventoVigente.add(new SelectItem(eventoVO.getCodigo(),eventoVO.getDescricao()));
        }
        if (!listaSelectEventoVigente.isEmpty()){
            listaSelectEventoVigente.add (0,new SelectItem(0,""));
        }
    }

    public List consultarColaboradorPorSituacao() {
        try {
            if (getEmpresa() == null) {
                if (getUsuarioLogado().getAdministrador()) {
                    return new ArrayList();
                }
                setEmpresa(getEmpresaLogado());
            }

            return getFacade().getColaborador().consultarPorTipoColaboradorAtivo(TipoColaboradorEnum.CONSULTOR, "AT",
                    getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
        } catch (Exception ex) {
            Uteis.logar(ex, InclusaoVendaRapidaControle.class);
            montarErro(ex);
            return new ArrayList<ColaboradorVO>();
        }
    }

    private void inicializarTiposColaboraresPersonal() {
        getTiposColaboradoresPersonal().add(TipoColaboradorEnum.PERSONAL_INTERNO);
        getTiposColaboradoresPersonal().add(TipoColaboradorEnum.PERSONAL_EXTERNO);
        getTiposColaboradoresPersonal().add(TipoColaboradorEnum.PERSONAL_TRAINER);
    }

    private void processarExibicaoInputIndicadoPor() throws Exception {
        if (getPlanoSelecionado() != 0) {
            setExibirInputIndicadoPor(getFacade().getBrinde().existeBrindeParaPlano(getPlanoSelecionado()));
        } else {
            setExibirInputIndicadoPor(false);
        }
    }

    public void selecionarClienteVenda() {
        try {
            setSituacaoContratoBase("");
            setCodigoContratoBase(0);
            setVigenciaAteAjustadaContratoBase("");

            ClienteVO selecionado = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
            selecionado = getFacade().getCliente().consultarPorCodigo(selecionado.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
            selecionarClienteVenda(selecionado);
            validarIdade();

            ContratoVO ultimoContrato = getFacade().getContrato().consultarUltimoContratoNaoRenovadoRematriculadoPorCodigoPessoa(selecionado.getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, true, getClienteVO().getEmpresa().getCodigo());
            if (ultimoContrato != null && ultimoContrato.getCodigo() > 0) {
                SituacaoContratoEnum situacaoContrato = obterSituacaoContrato(ultimoContrato);
                setSituacaoContratoBase(situacaoContrato.getCodigo());
                setCodigoContratoBase(ultimoContrato.getCodigo());
                setVigenciaAteAjustadaContratoBase(ultimoContrato.getVigenciaAteAjustada_Apresentar());
                if (!ultimoContrato.getSituacao().equalsIgnoreCase("CA")) {
                    if (!UteisValidacao.emptyNumber(ultimoContrato.getDiaVencimentoProrata())) {
                        setDiaVencimento(String.valueOf(ultimoContrato.getDiaVencimentoProrata()));
                    } else {
                        ContratoRecorrenciaVO contratoReco = getFacade().getContratoRecorrencia().consultarPorContrato(ultimoContrato.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if (contratoReco != null && !UteisValidacao.emptyNumber(contratoReco.getDiaVencimentoCartao())) {
                            setDiaVencimento(String.valueOf(contratoReco.getDiaVencimentoCartao()));
                        }
                    }
                }
            }
            if (!UteisValidacao.emptyNumber(planoSelecionado)) {
                montarPlanoSelecionado();
            }
            montarPlanosClienteSelecionado(selecionado.getCategoria().getCodigo());
        } catch (Exception ex) {
            Uteis.logar(ex, InclusaoVendaRapidaControle.class);
            montarErro(ex);
        }
    }

    private SituacaoContratoEnum obterSituacaoContrato(ContratoVO ultimoContrato) throws Exception {
        ConfiguracaoSistemaVO configuracaoSistemaVO = (ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA);
        if (configuracaoSistemaVO == null) {
            configuracaoSistemaVO = getFacade().getConfiguracaoSistema().consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        //se tiver operação de cancelamento então habilita a Rematricula
        if (getFacade().getContratoOperacao().existeOperacaoParaEsteContrato(ultimoContrato.getCodigo(), "CA")) {
            return SituacaoContratoEnum.REMATRICULA;
        }

        Integer carenciaRenovacao = configuracaoSistemaVO.getCarenciaRenovacao();
        if (getEmpresa().getCarenciaRenovacao() != 0) {
            carenciaRenovacao = getEmpresa().getCarenciaRenovacao();
        }
        Date dataVigenciaAjustada = Uteis.obterDataFutura2(ultimoContrato.getVigenciaAteAjustada(), 1);
        Date dataVigenciaRenovacao = Uteis.obterDataFutura2(ultimoContrato.getVigenciaAteAjustada(), (carenciaRenovacao + 1));

        if (Calendario.menorOuIgual(Calendario.hoje(), dataVigenciaAjustada) || Calendario.menorOuIgual(Calendario.hoje(), dataVigenciaRenovacao)) {
            return SituacaoContratoEnum.RENOVACAO;
        }
        return SituacaoContratoEnum.REMATRICULA;
    }

    @Override
    public void validarIdade() throws Exception {
        setResponsavelObrigatorio(getPessoaVO().getIdade() < 18);
    }

    void validarCampanhaCupomDesconto() throws Exception {
        OAMDService oamdService = new OAMDService();
        try {
            String nomePlano = null;
            if ((planoVOSelecionado != null) && (planoVOSelecionado.getDescricao() != null)) {
                nomePlano = planoVOSelecionado.getDescricao();
            }
            this.existeCampanhaCupomDescontoVigente = oamdService.existeCampanhaCupomDesconto(getEmpresaLogado().getCodEmpresaFinanceiro(), true,
                    nomePlano);
        } catch (Exception e) {
            this.existeCampanhaCupomDescontoVigente = false;
        }finally {
            oamdService = null;
        }
    }

    public List<ClienteVO> executarAutoCompletePossivelDependente(Object suggest) {
        String pref = (String) suggest;
        List<ClienteVO> result;
        try {
            result = getFacade().getCliente().consultarPorNomePessoaSituacaoDiferente(
                    pref, "AT", getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA, 30);
        } catch (Exception ex) {
            result = new ArrayList<>();
        }
        return result;
    }

    public void selecionarDependenteSuggestionBox() {
        ClienteVO clienteSelecionado = (ClienteVO) request().getAttribute("result");
        if (clienteSelecionado != null && !UteisValidacao.emptyNumber(clienteSelecionado.getCodigo())) {
            setClienteDependente(clienteSelecionado);
        }
    }

    public void selecionarClienteVenda(ClienteVO selecionado) {
        try {

            setPessoaVO(selecionado.getPessoa());
            setClienteVO(selecionado);
            setNomeConvidado(selecionado.getPessoa().getNome());
            atribuirEstados(getPessoaVO());
            atribuirCidades(getPessoaVO());
            atribuirEmail(getPessoaVO());
            atribuirEndereco(getPessoaVO());
            atribuirTelefones(getPessoaVO());
            atribuirConsultor(getClienteVO().getVinculoVOs());

            if (barrarDevedorVendaRapida) {
                temParcelaEmAberto = getFacade().getMovParcela().temParcelasAberto(getClienteVO().getCodigo());
                if (temParcelaEmAberto) {
                    throw new ConsistirException(PropsService.getPropertyValueApp("aluno_tem_parcelas_em_aberto_receber_debito"));
                }
            }

            List<AutorizacaoCobrancaClienteVO> autorizacoes = getFacade().getAutorizacaoCobrancaCliente().consultarPorPessoa(getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (!UteisValidacao.emptyList(autorizacoes)) {
                AutorizacaoCobrancaClienteVO ac = autorizacoes.get(0);
                AragornService aragornService = new AragornService();
                aragornService.povoarAutorizacaoCobrancaVO(ac, true,true);
                aragornService = null;
                tipoAutorizacao = ac.getTipoAutorizacao();
                tipoPagamentoAluno = TipoPagamentoAluno.getFromAutorizacao(ac);
                if (tipoAutorizacao != null && tipoPagamentoAluno != null) {
                    inicializarConvenio();
                    autorizacao = ac;
                    autorizacao.setNumeroCartao(autorizacao.getCartaoMascarado_Apresentar());
                    autorizacao.setObjetoVOAntesAlteracao(autorizacao.getClone(true));
                }
            }
            setMsgAlert("Richfaces.hideModalPanel('modalValidacaoCPF');");
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert("Richfaces.hideModalPanel('modalValidacaoCPF');" + getMensagemNotificar());
        }
    }

    public void adicionarAlunoInadimplente() {
        try {
            if (!permissao("VendaAvulsa")) {
                throw new ConsistirException(PropsService.getPropertyValueApp("prt_venda_rapida_voce_nao_possui_permissao"));
            }
            Integer codigo = getFacade().getVendaService().addAlunoInadimplente(jsonInadimplente,
                    getEmpresa().getCodigo());
            ClienteVO clienteVO = getFacade().getCliente().consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            JSONArray titulospendente = jsonInadimplente.getJSONArray("TITULOSPENDENTE");
            for (int i = 0; i < titulospendente.length(); i++) {
                JSONArray array = titulospendente.getJSONArray(i);
                String idExternoFilial = array.getString(0);
                Integer idExternoTitulo = array.getInt(9);
                String bordero = array.getString(10);

                Double valor = array.getDouble(4);
                Double valorJuros = array.getDouble(5);
                Double valorDesconto = array.getDouble(6);
                Double valorFinal = (valor + valorJuros) - valorDesconto;

                boolean estaBordero = !UteisValidacao.emptyString(bordero.trim());
                getFacade().getVendaService().gerarParcelaInadimplente(clienteVO, valorFinal, getUsuarioLogado(),
                        idExternoTitulo.toString(), estaBordero, idExternoFilial,
                        jsonInadimplente.optString("CPFALUNO"));
            }

            setClienteVO(clienteVO);
            irParaCaixa();
            redirect("/faces/tela8.jsp");
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void validarCpfExiste() throws Exception {
        exibirValidacaoCpf = true;
        setMensagemClienteRestricao("");
        montarPlanosClienteSelecionado(0);
        String cpf = getPessoaVO().getCfp();

        if (getPessoaVO() != null && Uteis.removerMascara(cpf).trim().length() > 10) {
            getColaboradorVO().setCodigo(0);
            getPessoaVO().setCodigo(0);
            getClienteVO().setCodigo(0);

            getPessoaVO().setCfp(Uteis.formatarCpfCnpj(Uteis.removerMascara(cpf),false));
            try {
                jsonInadimplente = null;
                setMsgAlert("");

                if (getConfiguracaoSistema().getCfpOb()) {
                    if (!isPessoaEstrangeira()){
                        validarCPF("cpfid", cpf);
                    } else {
                        if (UteisValidacao.emptyString(getPessoaVO().getRne())){
                            throw new ValidacaoException(new String[] { "cpfid" }, "O campo RNE deve ser informado");
                        }
                    }

                }

                if (getEmpresaLogado().isUtilizaGestaoClientesComRestricoes() && clientePossuiRestricao(cpf)) {
                    return;
                }

                if (plano.isPlanoPersonal() || (cpfPerternceColaborador() && !cpfPertenceCliente())) {
                    consultarColaboradoresPersonal();
                    if (!getColaboradoresPersonal().isEmpty()) {
                        setMsgAlert("Richfaces.showModalPanel('modalColaboradoresPorCpf');");
                    }
                } else {
                    consultarPessoaJaCadastrada(true);
                    setMsgAlert(getFuncaoJsParaCampoCPF());

                    if (!getApresentarModalClienteCpfIgual() && getValidarInadimplencia()) {
                        jsonInadimplente = getFacade().getZWFacade().verificarInadimplencia(cpf);
                        if (jsonInadimplente != null && !jsonInadimplente.optBoolean("LIBERADO")) {

                            double valorFinal = 0.0;
                            int qtdTitulos = 0;
                            JSONArray titulospendente = jsonInadimplente.getJSONArray("TITULOSPENDENTE");
                            for (int i = 0; i < titulospendente.length(); i++) {
                                JSONArray array = titulospendente.getJSONArray(i);

                                Double valor = array.getDouble(4);
                                Double valorJuros = array.getDouble(5);
                                double valorDesconto = array.getDouble(6);
                                valorFinal += (valor + valorJuros) - valorDesconto;

                                qtdTitulos++;
                            }

                            setMsgAlert("Richfaces.showModalPanel('modalpendenciaprotheus');");
                            setMsgInadimplencia(
                                    jsonInadimplente.optString("NOMEALUNO") +
                                            PropsService.getPropertyValueApp("esta_inadimplente") +
                                            ". " + PropsService.getPropertyValueApp("esta_pendente_com") +
                                            qtdTitulos +
                                            PropsService.getPropertyValueApp("titulos_totalizando_um_valor_total_de ")
                                            + getEmpresaLogado().getMoeda() +
                                            "" + Uteis.formatarValorEmReal(valorFinal) +
                                            ". " + PropsService.getPropertyValueApp("o_que_deseja_fazer"));
                            return;
                        }
                    } else if (getEmpresa().isConsultarNovoCadastroSPC() && getEmpresa().isPesquisaAutomaticaSPC()){
                        String cpfMae = responsavelObrigatorio ? Uteis.formatarCpfCnpj(Uteis.removerMascara(getPessoaVO().getCpfMae()), false) : "";

                        if (responsavelObrigatorio && !cpfMae.isEmpty() && cpfMae.trim().length() > 10) {
                            consultarNoSPC(cpfMae);
                        } else {
                            setMensagemDetalhada("", "");
                            clienteVO.setApresentarRichModalCadastroRapido(false);
                            setMsgAlert("");

                            if (!responsavelObrigatorio) {
                                consultarNoSPC(cpf);
                            }
                        }
                    }
                }

            } catch (ValidacaoException e) {
                tratarException(null, 0, e, e.getCampos());
            } catch (IntegracoesMSException iex) {
                montarAviso("Dados inválidos" ,iex.getMessage());
                setMsgAlert(getMensagemNotificar());
            } catch (Exception e) {
                if (!e.getMessage().contains("CPF_CADASTRADO")) {
                    setMensagemDetalhada("msg_erro", e.getMessage());
                    clienteVO.setApresentarRichModalCadastroRapido(true);
                    montarErro(e);
                    setMsgAlert(getMensagemNotificar());
                    return;
                }
            }
        } else {
            setMensagemDetalhada("", "");
            clienteVO.setApresentarRichModalCadastroRapido(false);
            setMsgAlert("");
        }
    }

    public void validarRneExiste() {
        exibirValidacaoCpf = true;
        setMensagemDetalhada("", "");
        clienteVO.setApresentarRichModalCadastroRapido(false);
        setMsgAlert("");

        String rne = getPessoaVO().getRne();
        if (getPessoaVO() != null && !Uteis.removerMascara(rne).trim().isEmpty()) {
            getColaboradorVO().setCodigo(0);
            getPessoaVO().setCodigo(0);
            getClienteVO().setCodigo(0);

            getPessoaVO().setRne(rne);
            try {
                consultarPessoaJaCadastradaPeloRNE();
                setMsgAlert(getFuncaoJsParaCampoCPF());
            } catch (Exception e) {
                if (!e.getMessage().contains("RNE_CADASTRADO")) {
                    setMensagemDetalhada("msg_erro", e.getMessage());
                    clienteVO.setApresentarRichModalCadastroRapido(true);
                    montarErro(e);
                    setMsgAlert(getMensagemNotificar());
                }
            }
        }
    }

    private void consultarNoSPC(String cpf) throws Exception {
        ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave((String) JSFUtilities.getFromSession(JSFUtilities.KEY));

        RetornoConsultaCDLTO retornoConsultaCDLTO = IntegracoesMSService
                .consultar(clientDiscoveryDataDTO.getServiceUrls().getIntegracoesMsUrl(),
                        cpf,
                        getEmpresa().getOperadorSpc(),
                        getEmpresa().getSenhaSpc(),
                        getEmpresa().getCodigoAssociadoSpc(),
                        (String) JSFUtilities.getFromSession(JSFUtilities.KEY));

        if(getPessoaVO().isMenorIdade()){
            getPessoaVO().setNomeMae(retornoConsultaCDLTO.getConsumidor().getConsumidorPessoaFisica().getNome());
            getPessoaVO().setDataNascimentoResponsavel(new Date(retornoConsultaCDLTO.getConsumidor().getConsumidorPessoaFisica().getDataNascimento()));
            getPessoaVO().setConstaRestricaoSPC(retornoConsultaCDLTO.isRestricao());
        }else {
            getPessoaVO().setNome(retornoConsultaCDLTO.getConsumidor().getConsumidorPessoaFisica().getNome());
            getPessoaVO().setDataNasc(new Date(retornoConsultaCDLTO.getConsumidor().getConsumidorPessoaFisica().getDataNascimento()));
            String sexo = retornoConsultaCDLTO.getConsumidor().getConsumidorPessoaFisica().getSexo();
            getPessoaVO().setSexo(sexo != null && sexo.startsWith("M") ? "M" : "F");
            getPessoaVO().setConstaRestricaoSPC(retornoConsultaCDLTO.isRestricao());
        }
    }

    public void validarNomeExiste() {
        exibirValidacaoCpf = false;
        setMsgAlert("");

        if (getPessoaVO() != null && Uteis.removerMascara(getPessoaVO().getCfp()).trim().length() <= 10 && getPessoaVO().getNome().trim().length() >= 3) {
            getColaboradorVO().setCodigo(0);
            getPessoaVO().setCodigo(0);
            getClienteVO().setCodigo(0);

            getPessoaVO().setCfp("");

            try {
                jsonInadimplente = null;
                setMsgAlert("");

                if (getConfiguracaoSistema().getCfpOb()) {
                    if (!isPessoaEstrangeira()) {
                        validarCPF("cpfid", getPessoaVO().getCfp());
                    } else {
                        if (UteisValidacao.emptyString(getPessoaVO().getRne())){
                            throw new ValidacaoException(new String[] { "cpfid" }, "O campo RNE deve ser informado");
                        }
                    }
                }

                if (plano.isPlanoPersonal()) {
                    consultarColaboradoresPersonal();
                    if (!getColaboradoresPersonal().isEmpty()) {
                        setMsgAlert("Richfaces.showModalPanel('modalColaboradoresPorCpf');");
                    }
                } else {
                    consultarPessoaMesmoNome();
                    if(!getClientesComMesmoCpf().isEmpty()) {
                        setMsgAlert("Richfaces.showModalPanel(modalValidacaoCPF);");
                    }
                }

            } catch (ValidacaoException e) {
                tratarException(null, 0, e, e.getCampos());
            } catch (Exception e) {
                if (!e.getMessage().contains("CPF_CADASTRADO")) {
                    setMensagemDetalhada("msg_erro", e.getMessage());
                    clienteVO.setApresentarRichModalCadastroRapido(true);
                    montarErro(e);
                    setMsgAlert(getMensagemNotificar());
                    return;
                }
            }
        } else {
            setMensagemDetalhada("", "");
            clienteVO.setApresentarRichModalCadastroRapido(false);
            setMsgAlert("");
        }
    }
    private boolean cpfPerternceColaborador() throws Exception {
        List<ColaboradorVO> colaboradorVOS = getFacade().getColaborador().consultarPorTipos(getTiposColaboradoresPersonal(),
                "AT", getEmpresa().getCodigo(), " AND (cfp like ('" + getPessoaVO().getCfp() + "%') OR cfp like ('" + Uteis.removerMascara(getPessoaVO().getCfp()).trim() + "%'))", null, 5,
                Uteis.NIVELMONTARDADOS_TODOS);
        return !UteisValidacao.emptyList(colaboradorVOS);
    }

    private boolean cpfPertenceCliente() throws Exception {
        List<ClienteVO> clienteVOS = getFacade().getCliente().consultarPorCPF(getPessoaVO().getCfp(), getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        return !UteisValidacao.emptyList(clienteVOS);
    }

    private void consultarColaboradoresPersonal() throws Exception {

        if (getPessoaVO().getCfp().length() > 0) {
            setColaboradoresPersonal(getFacade().getColaborador().consultarPorTipos(getTiposColaboradoresPersonal(),
                    "AT", getEmpresa().getCodigo(), " AND (cfp like ('" + getPessoaVO().getCfp() + "%') OR cfp like ('" + Uteis.removerMascara(getPessoaVO().getCfp()).trim() + "%'))", null, 5,
                    Uteis.NIVELMONTARDADOS_TODOS));
        } else if(getPessoaVO().getNome().trim().length() >= 3) {
            String nome = getPessoaVO().getNome().trim().replace("'", "");
            String orderBy = "nome ilike ('"+nome+"%') desc";
            setColaboradoresPersonal(getFacade().getColaborador().consultarPorTipos(getTiposColaboradoresPersonal(),
                    "AT", getEmpresa().getCodigo(), " AND length(cfp) >= 1 AND nome ilike ('%" + nome + "%')", orderBy, 5,
                    Uteis.NIVELMONTARDADOS_TODOS));
        } else {
            setColaboradoresPersonal(new ArrayList<>());
        }
    }

    private void consultarPessoaMesmoNome() throws Exception {
        getClienteVO().setClienteDenpentedes(new ArrayList<>());
        if (getPessoaVO().getCfp().length() == 0 && getPessoaVO().getNome().trim().length() >= 3) {
            setClientesComMesmoCpf(getFacade().getCliente().consultarClientesPorNomeComCpf(this.getPessoaVO().getNome(), clienteVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        } else {
            setClientesComMesmoCpf(new ArrayList<>());
        }

        setApresentarModalClienteCpfIgual(true);
        getClienteVO().setApresentarRichModalErro(false);
        setApresentarBotaoIgnorarValidacaoCPF(false);

        if(!getClientesComMesmoCpf().isEmpty()) {
            setFotos(new HashMap<>());
            for (ClienteVO cli : getClientesComMesmoCpf()){
                cli.getPessoa().setFoto(getFacade().getPessoa().obterFoto(getKey(), cli.getPessoa().getCodigo()));
                cli.setClienteDenpentedes(getFacade().getCliente().consultarClientesDepententesCfp(cli.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
                validarDadosClienteInsercaoSeEleExistir(cli);
                getFotos().put(cli.getPessoa().getCodigo(), cli.getPessoa().getFoto());
                for(ClienteVO dep : cli.getClienteDenpentedes()){
                    dep.getPessoa().setFoto(getFacade().getPessoa().obterFoto(getKey(), dep.getPessoa().getCodigo()));
                    validarDadosClienteInsercaoSeEleExistir(dep);
                    getFotos().put(dep.getPessoa().getCodigo(), dep.getPessoa().getFoto());
                }
            }
        }
    }

    public String getLabelBtnSalvar() {
        if (formaPagamentoSelecinada == FormaPagamentoVendaRapidaEnum.OUTRA_FORMA_PAGTO.getCodigo()) {
            return "Prosseguir para pagamento";
        } else if (formaPagamentoSelecinada == FormaPagamentoVendaRapidaEnum.BOLETO_BANCARIO_ONLINE.getCodigo()) {
            return "Concluir e gerar boleto(s)";
        }
        return "Concluir";
    }

    public void validarContratoRenovando() {
        setMsgAlert("");
        if (!UteisValidacao.emptyString(situacaoContratoBase) && "RN".equals(situacaoContratoBase)) {
            setMsgAlert("Richfaces.showModalPanel('mdlRenovacaoContrato')");
            return;
        }

        validar();
    }

    public void validarSemConcomitante() {
        setLancarConcomitante(false);
        validar();
    }

    public void validarConcomitante() {
        setLancarConcomitante(true);
        validar();
    }

    public void validar() {
        setMsgAlert("");
        this.setMsgCarregando("Validando informações");
        ContratoVO contratoGravado = null;
        Integer editar = getClienteVO().getCodigo();
        if (getNomeConvidado() != null && !getNomeConvidado().trim().isEmpty()) {
            getClienteVO().getPessoa().setNome(getNomeConvidado());
        }

        boolean permissaoParaConcluirSemCadastrarAutorizacao = false;
        try {
            validarPermissao("PermiteFinalizarVendaRapidaSemAutorizacaoCobranca", "2.80 - Permitir finalizar venda rápida sem autorização de cobrança", getUsuarioLogado());
            permissaoParaConcluirSemCadastrarAutorizacao = true;
        } catch (Exception ignored) {
        }

        try {

            if (plano.getBloquearRecompra() != null && plano.getBloquearRecompra()) {
                int contratosAtivos = getFacade().getPlano()
                        .contarContratosAtivosPorPlanoCliente(plano.getCodigoPlano(), clienteVO.getCodigo());

                if (contratosAtivos > 0 || !"VI".equals(clienteVO.getSituacao())) {
                     throw new ValidacaoException(
                            new String[]{"planoid"},
                            "Venda não realizada. Devida a configuração do plano venda apenas para visitante."
                    );
                }
            }
            if (getEmpresaLogado().isUtilizaGestaoClientesComRestricoes()
                    && !UteisValidacao.emptyString(getMensagemClienteRestricao())) {
                throw new Exception(getMensagemClienteRestricao());
            }

            if (getPlano().isPlanoPersonal()) {
                if (getColaboradorPersonalSelecionado() == null) {
                    ColaboradorVO colaborador = new ColaboradorVO();
                    colaborador.setPessoa(getPessoaVO());
                    setColaboradorPersonalSelecionado(colaborador);
                }
                VendaRapidaRecorrenteServiceImpl.validarDadosColaboradorPersonal(getColaboradorPersonalSelecionado(),
                        getEnderecoResidencialVO(), getTelefoneCelularVO(), getTelefoneResidencialVO(), getEmailVO(),
                        getConfiguracaoSistema());
            } else {
                if (!UteisValidacao.emptyString(getPessoaVO().getCpfMae())
                        && getPessoaVO().getDataNascimentoResponsavel() == null) {
                    throw new ValidacaoException(new String[]{"dataNascResponsavel"},
                            PropsService.getPropertyValueApp("prt_venda_rapida_informe_nascimento_responsavel"));
                }

                getFacade().getVendaService().gravar(getTelefoneCelularVO(), getTelefoneComercialVO(),
                        getTelefoneResidencialVO(), getEnderecoResidencialVO(), getEnderecoComercialVO(),
                        getClienteVO(), getPessoaVO(), getEmailVO(), getUsuarioLogado(), getEmpresa(),
                        getConfiguracaoSistema(), isPessoaEstrangeira(), true);
            }
            if (UteisValidacao.notEmptyNumber(getClienteVO().getCodigo()) && barrarDevedorVendaRapida) {
                temParcelaEmAberto = getFacade().getMovParcela().temParcelasAberto(getClienteVO().getCodigo());
                if (temParcelaEmAberto) {
                    throw new ConsistirException(PropsService.getPropertyValueApp("aluno_tem_parcelas_em_aberto_receber_debito"));
                }
            }
            if (UteisValidacao.emptyNumber(planoSelecionado)) {
                throw new ValidacaoException(new String[]{"planoid"}, PropsService.getPropertyValueApp("plano_deve_ser_informado"));
            }

            if (!permissaoParaConcluirSemCadastrarAutorizacao
                    && (tipoAutorizacao == null || convenioSelecionado == null)
                    && FormaPagamentoVendaRapidaEnum.OUTRA_FORMA_PAGTO.getCodigo() != this.formaPagamentoSelecinada
                    && FormaPagamentoVendaRapidaEnum.BOLETO_BANCARIO_ONLINE.getCodigo() != this.formaPagamentoSelecinada) {
                throw new ValidacaoException(new String[]{"formaid"}, PropsService.getPropertyValueApp("a_forma_pagamento_tem_que_ser_informada"));
            }

            if (!permissaoParaConcluirSemCadastrarAutorizacao
                    && getPlano().getObrigatorioInformarCartaoCreditoVenda()
                    && (tipoAutorizacao == null || tipoAutorizacao != TipoAutorizacaoCobrancaEnum.CARTAOCREDITO || convenioSelecionado == null)) {
                throw new ValidacaoException(new String[]{"formaid"}, PropsService.getPropertyValueApp("cartao_de_credito_obrigatorio_venda_rapida"));
            }

            if (FormaPagamentoVendaRapidaEnum.BOLETO_BANCARIO_ONLINE.getCodigo() == this.formaPagamentoSelecinada &&
                    UteisValidacao.emptyNumber(this.getConvenioCobrancaBoletoSelecionado())) {
                throw new ValidacaoException(new String[]{"formaid"}, PropsService.getPropertyValueApp("o_convenio_tem_que_ser_informada"));
            }

            if (tipoAutorizacao != null) {
                if (tipoAutorizacao.equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA) == !dcoContaValida) {
                    throw new ValidacaoException(new String[]{"formaid"}, PropsService.getPropertyValueApp("dados_da_conta_sao_invalidos"));
                }
                if (tipoAutorizacao.equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA) && !dcoCpfValido) {
                    throw new ValidacaoException(new String[]{"formaid"}, PropsService.getPropertyValueApp("prt_venda_rapida_cpf_invalido"));
                }
            }
            if (getPlano().isPlanoPersonal()) {
                gravar();
            } else {
                if (getEmpresaLogado().isResponderBVNaVendaRapida()) {
                    setMsgAlert("Richfaces.showModalPanel('modalConcluir')");
                } else {
                    gravar();
                }
            }
        } catch (ValidacaoException e) {
            tratarException(contratoGravado, editar, e, e.getCampos());
        } catch (Exception e) {
            tratarException(contratoGravado, editar, e, null);
        }
    }

    private CieloeCommerceService getCieloService(ConvenioCobrancaVO convenioSelcionadoVO) throws Exception {
        return new CieloeCommerceService(Conexao.getFromSession(), getEmpresaLogado().getCodigo(), convenioSelcionadoVO.getCodigo());
    }

    public void cancelarVenda() throws Exception {
        redirect("/faces/clientes.jsp");
    }

    @Override
    public void gravar() {
        gravarAluno(false);
    }

    public void gravarBV() {
        gravarAluno(true);
    }

    private void logarTempo(final String descricao, long fim, long inicio) {
        Uteis.logarDebug(descricao + ": " + (fim - inicio) + "ms");
    }

    public void gravarAluno(boolean bv) {
        long inicio = System.currentTimeMillis();
        this.setMsgCarregando("");

        List<FamiliarVO> dependentesAdicionados =  new ArrayList<>();

        ContratoVO contratoGravado = null;
        Integer editar = getClienteVO().getCodigo();
        if(getNomeConvidado() != null && !getNomeConvidado().trim().isEmpty()) {
            getClienteVO().getPessoa().setNome(getNomeConvidado());
        }
        boolean permissaoParaConcluirSemCadastrarAutorizacao = false;
        try {
            validarPermissao("PermiteFinalizarVendaRapidaSemAutorizacaoCobranca", "2.80 - Permitir finalizar venda rápida sem autorização de cobrança", getUsuarioLogado());
            permissaoParaConcluirSemCadastrarAutorizacao = true;
        } catch (Exception ignored) {
        }

        long permissaoFinalizarVendaRapidaSemCobranca = System.currentTimeMillis();
        logarTempo("permissaoFinalizarVendaRapidaSemCobranca", permissaoFinalizarVendaRapidaSemCobranca, inicio);

        validarPessoaEstrangeira();

        limparFamiliaresSeApresentarDependente();

        try {
            if (getProcessandoOperacao() != null && getProcessandoOperacao()){
                throw new Exception("Operação em processamento, favor aguardar.");
            }
            setProcessandoOperacao(true);
            if (UteisValidacao.emptyNumber(editar)) {
                getClienteVO().setCodigoMatricula(0);
                getClienteVO().setMatricula("");
            }
            getClienteVO().setVinculoVOs(new ArrayList<>());
            if (getConfiguracaoSistema().isUsarSistemaInternacional()){
                formatarTelefones();
            }
            if (!getPlano().isPlanoPersonal()) {
                getFacade().getVendaService().gravar(getTelefoneCelularVO(), getTelefoneComercialVO(),
                        getTelefoneResidencialVO(), getEnderecoResidencialVO(), getEnderecoComercialVO(),
                        getClienteVO(), getPessoaVO(), getEmailVO(), getUsuarioLogado(), getEmpresa(),
                        getConfiguracaoSistema(), isPessoaEstrangeira(), true);
            }
            long gravarVendaService1 = System.currentTimeMillis();
            logarTempo("gravarVendaService1", gravarVendaService1, permissaoFinalizarVendaRapidaSemCobranca);

            if (UteisValidacao.notEmptyNumber(getClienteVO().getCodigo()) && barrarDevedorVendaRapida) {
                temParcelaEmAberto = getFacade().getMovParcela().temParcelasAberto(getClienteVO().getCodigo());
                if (temParcelaEmAberto) {
                    throw new ConsistirException(PropsService.getPropertyValueApp("aluno_tem_parcelas_em_aberto_receber_debito"));
                }
            }
            long temParcelasAberto = System.currentTimeMillis();
            logarTempo("temParcelasAberto", temParcelasAberto, gravarVendaService1);

            if (!permissaoParaConcluirSemCadastrarAutorizacao
                    && (tipoAutorizacao == null || convenioSelecionado == null)
                    && FormaPagamentoVendaRapidaEnum.OUTRA_FORMA_PAGTO.getCodigo() != this.formaPagamentoSelecinada
                    && FormaPagamentoVendaRapidaEnum.BOLETO_BANCARIO_ONLINE.getCodigo() != this.formaPagamentoSelecinada) {
                throw new ValidacaoException(new String[]{"formaid"}, PropsService.getPropertyValueApp("a_forma_pagamento_tem_que_ser_informada"));
            }

            if (FormaPagamentoVendaRapidaEnum.BOLETO_BANCARIO_ONLINE.getCodigo() == this.formaPagamentoSelecinada &&
                    UteisValidacao.emptyNumber(this.getConvenioCobrancaBoletoSelecionado())) {
                throw new ValidacaoException(new String[]{"formaid"}, PropsService.getPropertyValueApp("o_convenio_tem_que_ser_informada"));
            }

            if (tipoAutorizacao != null) {
                if (tipoAutorizacao.equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA) == !dcoContaValida) {
                    throw new ValidacaoException(new String[]{"formaid"}, PropsService.getPropertyValueApp("dados_da_conta_sao_invalidos"));
                }

                getAutorizacao().setTipoACobrar(TipoObjetosCobrarEnum.TUDO);
                getAutorizacao().setCliente(clienteVO);
                getAutorizacao().setTipoAutorizacao(tipoAutorizacao);

                getAutorizacaoColaborador().setTipoACobrar(TipoObjetosCobrarEnum.TUDO);
                getAutorizacaoColaborador().setColaborador(getColaboradorPersonalSelecionado());
                getAutorizacaoColaborador().setTipoAutorizacao(tipoAutorizacao);

                if (TipoAutorizacaoCobrancaEnum.DEBITOCONTA.equals(tipoAutorizacao)) {
                    ConvenioCobrancaVO convenioCobrancaVO = getFacade().getConvenioCobranca()
                            .consultarPorCodigoSemInfoEmpresa(convenioSelecionado,
                                    Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
                    getAutorizacao().setBanco(convenioCobrancaVO.getBanco());
                    getAutorizacaoColaborador().setBanco(convenioCobrancaVO.getBanco());
                } else {
                    getAutorizacao().setOperadoraCartao(tipoPagamentoAluno.operadoraCartao);
                    getAutorizacaoColaborador().setOperadoraCartao(tipoPagamentoAluno.operadoraCartao);
                }

                ConvenioCobrancaVO convenioSelcionadoVO = getFacade().getConvenioCobranca().consultarPorChavePrimaria(convenioSelecionado, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                getAutorizacao().setConvenio(convenioSelcionadoVO);
                getAutorizacaoColaborador().setConvenio(convenioSelcionadoVO);

                if (plano.isPlanoPersonal()) {
                    AutorizacaoCobrancaColaboradorVO.validarDados(getAutorizacaoColaborador(), false);
                } else {
                    AutorizacaoCobrancaClienteVO.validarDados(getAutorizacao(), false);
                }

                if (convenioSelcionadoVO != null
                        && convenioSelcionadoVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {

                    ValidacaoCartao validacaoCartaoCielo;
                    if (plano.isPlanoPersonal()) {
                        validacaoCartaoCielo = getCieloService(convenioSelcionadoVO)
                                .validarDadosCartao(getAutorizacaoColaborador().getNumeroCartao());
                    } else {
                        validacaoCartaoCielo = getCieloService(convenioSelcionadoVO).validarDadosCartao(getAutorizacao().getNumeroCartao());
                    }

                    // Se null, então não conseguiu comunicar com api para validar.
                    // Deixa seguir para não bloquear a venda
                    if (validacaoCartaoCielo.getValido() != null && !validacaoCartaoCielo.getValido()) {
                        throw new ConsistirException(validacaoCartaoCielo.getMensagem());
                    }

                    if (validacaoCartaoCielo.getValido() != null && !validacaoCartaoCielo.isCredito()) {
                        throw new ConsistirException(PropsService.getPropertyValueApp("tipo_do_cartao_nao_credito"));
                    }
                }

            }
            setMsgAlert("");

            long validacaoTipoAutorizacao = System.currentTimeMillis();
            logarTempo("validacaoTipoAutorizacao", validacaoTipoAutorizacao, temParcelasAberto);

            if (!getClienteVO().getPessoa().isMenorIdade()) {
                PessoaVO pessoa = getClienteVO().getPessoa();
                if (!UteisValidacao.emptyString(pessoa.getNomeMae()) && !UteisValidacao.emptyString(pessoa.getCpfMae())) {
                    pessoa.setEmitirNomeTerceiro(true);
                    pessoa.setNomeTerceiro(pessoa.getNomeMae());
                    pessoa.setCpfCNPJTerceiro(pessoa.getCpfMae());
                    pessoa.setEmailMae(pessoa.getEmailMae());
                }
            }

            if (getClienteDependente() != null && !UteisValidacao.emptyNumber(getClienteDependente().getCodigo())) {
                String nomeParentescoDependente = "DEPENDENTE";
                ParentescoVO parentescoDependente;
                List<ParentescoVO> parentescos = getFacade().getParentesco().consultarPorDescricao(nomeParentescoDependente, false, Uteis.NIVELMONTARDADOS_TODOS);
                if (parentescos.isEmpty()) {
                    parentescoDependente = new ParentescoVO();
                    parentescoDependente.setDescricao(nomeParentescoDependente);
                    parentescoDependente.setIdadeLimiteDependencia(9999);
                    getFacade().getParentesco().incluir(parentescoDependente);
                } else {
                    parentescoDependente = parentescos.get(0);
                }


                FamiliarVO dependenteFamiliar = new FamiliarVO();
                dependenteFamiliar.setCompartilharPlano(true);
                dependenteFamiliar.setIdentificador("");
                dependenteFamiliar.setNome(getClienteDependente().getNome_Apresentar());
                dependenteFamiliar.setCodAcesso(getClienteDependente().getCodAcesso());
                dependenteFamiliar.setFamiliar(getClienteDependente().getCodigo());
                dependenteFamiliar.setParentesco(parentescoDependente);
                dependentesAdicionados.add(dependenteFamiliar);
            }

            if (getPlano().isPlanoPersonal()) {
                if (getColaboradorPersonalSelecionado() == null || getColaboradorPersonalSelecionado().getCodigo() == 0) {
                    getFacade().getVendaService().incluirColaboradorPersonal(getColaboradorPersonalSelecionado(),
                            getTelefoneCelularVO(), getTelefoneComercialVO(), getTelefoneResidencialVO(),
                            getEnderecoResidencialVO(), getEmailVO(), getEmpresa());
                } else {
                    getFacade().getVendaService().alterarColaboradorPersonal(getColaboradorPersonalSelecionado(),
                            getTelefoneCelularVO(), getTelefoneComercialVO(), getTelefoneResidencialVO(),
                            getEnderecoResidencialVO(), getEmailVO(), getEmpresa());

                }

                long inclusaoAlteracaoColaborador = System.currentTimeMillis();
                logarTempo("inclusaoAlteracaoColaborador", inclusaoAlteracaoColaborador, validacaoTipoAutorizacao);
            } else if (UteisValidacao.emptyNumber(getClienteVO().getCodigo())) {
                getClienteVO().setCodigoMatricula(0);
                getClienteVO().setMatricula("");
                getClienteVO().setVinculoVOs(new ArrayList<>());

                this.setMsgCarregando("Incluindo cliente");
                getFacade().getVendaService().gravar(getTelefoneCelularVO(), getTelefoneComercialVO(),
                        getTelefoneResidencialVO(), getEnderecoResidencialVO(), getEnderecoComercialVO(),
                        getClienteVO(), getPessoaVO(), getEmailVO(), getUsuarioLogado(), getEmpresa(),
                        getConfiguracaoSistema(), isPessoaEstrangeira(), false);


                this.setMsgCarregando("Adicionando consultor");
                ColaboradorVO consultorVo = new ColaboradorVO();
                consultorVo.setCodigo(consultor);
                getFacade().getVendaService().adicionarConsultor(getClienteVO(), consultorVo);
                long adicionarConsultor = System.currentTimeMillis();
                logarTempo("adicionarConsultor", adicionarConsultor, validacaoTipoAutorizacao);
            } else {
                List<VinculoVO> vinculos = getFacade().getVinculo().consultarPorCodigoCliente(
                        getClienteVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, false);
                if (UteisValidacao.emptyList(vinculos)) {
                    ColaboradorVO consultorVo = new ColaboradorVO();
                    consultorVo.setCodigo(consultor);
                    getFacade().getVendaService().adicionarConsultor(getClienteVO(), consultorVo);
                    VinculoVO v = new VinculoVO();
                    v.setColaborador(consultorVo);
                    v.setCliente(getClienteVO());
                    v.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());
                    getClienteVO().setVinculoVOs(new ArrayList<>());
                    getClienteVO().getVinculoVOs().add(v);
                } else {
                    boolean existeVinculoConsultor = false;
                    for (VinculoVO vinculoVO : vinculos){
                        if (vinculoVO.getTipoVinculo().equals("CO")){
                            existeVinculoConsultor = true;
                            break;
                        }
                    }
                    if (!existeVinculoConsultor){
                        ColaboradorVO consultorVo = new ColaboradorVO();
                        consultorVo.setCodigo(consultor);
                        getFacade().getVendaService().adicionarConsultor(getClienteVO(), consultorVo);
                        VinculoVO v = new VinculoVO();
                        v.setColaborador(consultorVo);
                        v.setCliente(getClienteVO());
                        v.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());
                        vinculos.add(v);
                    }
                    getClienteVO().setVinculoVOs(vinculos);

                }
                getClienteVO().setNovoObj(false);
                getFacade().getVendaService().alterar(getTelefoneCelularVO(), getTelefoneComercialVO(),
                        getTelefoneResidencialVO(), getEnderecoResidencialVO(), getClienteVO(), getPessoaVO(),
                        getEmailVO(), getUsuarioLogado(), getEmpresa(), getConfiguracaoSistema());

                long alterarCliente = System.currentTimeMillis();
                logarTempo("alterarCliente", alterarCliente, validacaoTipoAutorizacao);
            }
            long fimValidacoes = System.currentTimeMillis();
            logarTempo("fimValidacoes", fimValidacoes, validacaoTipoAutorizacao);

            if (getPlano().isPlanoPersonal()) {
                getFacade().getVendaService().incluirPlanoPersonal(getPlanoVOSelecionado(),
                        Integer.parseInt(diaVencimento), nrVezesParcelarAdesao, getColaboradorPersonalSelecionado(),
                        getEmpresa(), getUsuario());

                long incluirPlanoPersonal = System.currentTimeMillis();
                logarTempo("incluirPlanoPersonal", incluirPlanoPersonal, fimValidacoes);
            } else {
                Integer contratoRematricula = 0;
                Integer codigoContratoRenovar = 0;

                long inicioUltimoContratoCliente = System.currentTimeMillis();
                ContratoVO ultimoContratoCliente = getFacade().getContrato().consultarUltimoContratoNaoRenovadoRematriculadoPorCodigoPessoa(getClienteVO().getPessoa().getCodigo(),
                        false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA,true, getClienteVO().getEmpresa().getCodigo());
                long finalUltimoContratoCliente = System.currentTimeMillis();
                logarTempo("Tempo ultimo contrato cliente", finalUltimoContratoCliente, inicioUltimoContratoCliente);

                int nrParcelasPagamento = 12;
                Boolean parcelamentoOperadora = null;
                if (planoVOSelecionado.getRegimeRecorrencia() && FormaPagamentoVendaRapidaEnum.CARTAO_CREDITO.getCodigo() == formaPagamentoSelecinada) {
                    parcelamentoOperadora = (this.tipoParcelamentoEscolhido == TipoParcelamentoVendaRapidaEnum.PARCELAMENTO_OPERADORA.getCodigo());
                    if (this.tipoParcelamentoEscolhido == TipoParcelamentoVendaRapidaEnum.PARCELAMENTO_OPERADORA.getCodigo()) {
                        if (this.quantidadeParcelamento <= 0) {
                            throw new ConsistirException(PropsService.getPropertyValueApp("qtde_parcela_parcelamento_operadora"));
                        }
                        nrParcelasPagamento = quantidadeParcelamento;
                    }
                }

                if (!isLancarConcomitante()) {
                    if ((this.codigoContratoBase > 0) && (this.situacaoContratoBase.equals("RE"))) {
                        contratoRematricula = this.codigoContratoBase;
                    }
                    if ((this.codigoContratoBase > 0) && (this.situacaoContratoBase.equals("RN"))) {
                        codigoContratoRenovar = codigoContratoBase;
                    }
                    if ((codigoContratoBase == 0) && (ultimoContratoCliente.getCodigo() > 0)) {
                        // Acessou a tela direto, não veio da tela do cliente. Validar se é uma renovação.
                        ultimoContratoCliente.obterSituacaoContratoRenovacaoRematricula();
                        if (ultimoContratoCliente.getSituacaoContrato().equals("RN")) {
                            codigoContratoRenovar = ultimoContratoCliente.getCodigo();
                        } else {
                            contratoRematricula = ultimoContratoCliente.getCodigo();
                        }
                    }
                }

                long inicioGravarContratoSite = System.currentTimeMillis();

                this.setMsgCarregando("Incluindo contrato");

                contratoGravado = getFacade().getContrato().gravarContratoSite(getKey(), planoSelecionado,
                        getClienteVO().getCodigo(), nrVezesParcelarAdesao, nrVezesParcelarProduto, getNumeroCupomAplicado(), true, true, nrParcelasPagamento,
                        false, codigoContratoRenovar, getEmpresa().getCodigo(), true, getUsuarioLogado(),
                        Integer.parseInt(getDiaVencimento()), getIndicadoPor(), true, contratoRematricula, null,(parcelamentoOperadora != null?parcelamentoOperadora:null), this.codigoEventosSelecionado, isLancarConcomitante());

                long gravarContratoSite = System.currentTimeMillis();
                logarTempo("inicioGravarContratoSite", gravarContratoSite, inicioGravarContratoSite);
            }

            if (contratoGravado != null && contratoGravado.getCodigo() > 0) {
                if (!dependentesAdicionados.isEmpty()) {
                    getContratosDependentes().addAll(getFacade().getContratoDependente().findAllByContrato(contratoGravado.getCodigo()));
                    for (FamiliarVO familiarAdicionado : dependentesAdicionados) {
                        familiarAdicionado.setContratoCompartilhado(contratoGravado.getCodigo());
                        processarAlteracaoFamiliar(familiarAdicionado, false);
                    }
                    getClienteVO().getFamiliarVOs().addAll(dependentesAdicionados);
                    getFacade().getFamiliar().alterarFamiliars(getClienteVO().getCodigo(), getClienteVO().getFamiliarVOs(), getClienteVO().getFamiliaresExcluidosVOs());
                } else if (contratoGravado.getPlano().getQuantidadeCompartilhamentos() > 0 &&
                        contratoGravado.isContratoRematricula() || contratoGravado.isContratoRenovacao()) {
                    vincularDependentesContratoRenovadoOuRematriculado(contratoGravado);
                }
            }

            long iniciandoNovasValidacoes = System.currentTimeMillis();

            if (tipoAutorizacao != null) {
                if (plano.isPlanoPersonal()) {
                    if (getAutorizacaoColaborador().getCodigo() == 0) {
                        getFacade().getVendaService().incluirAutorizacaoCobrancaColaborador(getAutorizacaoColaborador());
                    } else {
                        getFacade().getVendaService().alterarAutorizacaoCobrancaColaborador(getAutorizacaoColaborador());
                    }

                    this.setMsgCarregando("Verificando cartão");
                    getFacade().getTransacao().realizaCobrancaVerificarCartao(null, this.getColaboradorPersonalSelecionado(), getAutorizacaoColaborador(), getUsuarioLogado(), this.getIpCliente());

                    long planoPersonal = System.currentTimeMillis();
                    logarTempo("planoPersonal", planoPersonal, iniciandoNovasValidacoes);
                } else {
                    if (UteisValidacao.emptyNumber(getAutorizacao().getCodigo())) {
                        getFacade().getVendaService().gravarAutorizacao(getAutorizacao());
                    } else {
                        getFacade().getAutorizacaoCobrancaCliente().alterar(getAutorizacao());
                        if (!getAutorizacao().isAtiva()) {
                            getFacade().getClienteMensagem().excluirClienteMensagemCartaoVencido(
                                    getAutorizacao().getCartaoMascarado(), getAutorizacao().getCliente().getCodigo());
                        }
                    }

                    this.setMsgCarregando("Verificando cartão");
                    getFacade().getTransacao().realizaCobrancaVerificarCartao(this.getClienteVO(), null, getAutorizacao(), getUsuarioLogado(), this.getIpCliente());
                    long planoNaoPersonal = System.currentTimeMillis();
                    logarTempo("planoNaoPersonal", planoNaoPersonal, iniciandoNovasValidacoes);
                }
            }

            long fimPlanoPersonalNaoPersonal = System.currentTimeMillis();

            notificarRecursoEmpresa(RecursoSistema.VENDA_RAPIDA_SUCESSO, ContadorTempo.encerraContagem());
            if (bv && !getPlano().isPlanoPersonal()) {
                ClienteControle clienteControle = getControlador(ClienteControle.class);
                ClienteVO cliente = getFacade().getCliente().consultarPorChavePrimaria(getClienteVO().getCodigo(),
                        Uteis.NIVELMONTARDADOS_TODOS);
                clienteControle.prepararTelaCliente(cliente, false);
                clienteControle.limparContrato();

                clienteControle.setQuestionarioClienteVO(new QuestionarioClienteVO());
                if (this.codigoEventosSelecionado != null){
                    clienteControle.getQuestionarioClienteVO().getEventoVO().setCodigo(this.codigoEventosSelecionado);
                }
                clienteControle.getClienteVO().setVinculoVOs(getFacade().getVinculo().consultarPorCodigoCliente(
                        clienteVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, false));
                String retornoQuestionario = clienteControle.questionarioSerRespondidoPeloCliente();
                if (!getAutorizacao().isAtiva()) {
                    getFacade().getClienteMensagem().excluirClienteMensagemCartaoVencido(
                            getAutorizacao().getCartaoMascarado(), getAutorizacao().getCliente().getCodigo());
                    notificarRecursoEmpresa(RecursoSistema.VENDA_RAPIDA_SUCESSO_BV, ContadorTempo.encerraContagem());
                }

                long dadosClientes = System.currentTimeMillis();
                logarTempo("dadosClientes", dadosClientes, fimPlanoPersonalNaoPersonal);
                if (!retornoQuestionario.equals("questionario")) {
                    redirectProximaTela("/faces/clienteNav.jsp?page=cliente&matricula=" + getClienteVO().getCodigoMatricula(), contratoGravado);
                    //redirect("/faces/clienteNav.jsp?page=cliente&matricula=" + getClienteVO().getCodigoMatricula());
                } else {
                    clienteControle.setFinalizarVenda(false);
                    clienteControle.setProcessandoOperacao(false);
                    clienteControle.setRetornoBV("");
                    clienteControle.setClienteSessao(true);
                    clienteControle.setIncluirPessoa(false);
                    clienteControle.setQuestionarioSemCliente(true);
                    redirect("/faces/tela4.jsp");
                }

            } else if (!getPlano().isPlanoPersonal()) {

                ClienteControle clienteControle = getControlador(ClienteControle.class);
                ClienteVO cliente = getFacade().getCliente().consultarPorChavePrimaria(getClienteVO().getCodigo(),
                        Uteis.NIVELMONTARDADOS_TODOS);
                clienteControle.prepararTelaCliente(cliente, false);
                clienteControle.limparContrato();

                clienteControle.setQuestionarioClienteVO(new QuestionarioClienteVO());
                if (this.codigoEventosSelecionado != null){
                    clienteControle.getQuestionarioClienteVO().getEventoVO().setCodigo(this.codigoEventosSelecionado);
                }
                clienteControle.getClienteVO().setVinculoVOs(getFacade().getVinculo().consultarPorCodigoCliente(
                        clienteVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, false));
                String retornoQuestionario = clienteControle.questionarioSerRespondidoPeloCliente();

                long validacaoQuestionario = System.currentTimeMillis();
                logarTempo("validacaoQuestionario", validacaoQuestionario, fimPlanoPersonalNaoPersonal);

                if (retornoQuestionario.equals("questionario")) {
                    clienteControle.getQuestionarioClienteVO().getEventoVO().setCodigo(this.codigoEventosSelecionado);
                    clienteControle.setFinalizarVenda(false);
                    clienteControle.setProcessandoOperacao(false);
                    clienteControle.setRetornoBV("");
                    clienteControle.setClienteSessao(true);
                    clienteControle.setIncluirPessoa(false);
                    clienteControle.setQuestionarioSemCliente(true);

                    clienteControle.cadastrarVisitante();
                    clienteControle.cadastrarVisitanteSemResponderBV();

                    long necessitaBV = System.currentTimeMillis();
                    logarTempo("necessitaBV", necessitaBV, validacaoQuestionario);
                }
                //redirect("/faces/clienteNav.jsp?page=cliente&matricula=" + getClienteVO().getCodigoMatricula());
                redirectProximaTela("/faces/clienteNav.jsp?page=cliente&matricula=" + getClienteVO().getCodigoMatricula(),contratoGravado);
                if (!getAutorizacao().isAtiva()) {
                    getFacade().getClienteMensagem().excluirClienteMensagemCartaoVencido(
                            getAutorizacao().getCartaoMascarado(), getAutorizacao().getCliente().getCodigo());
                }

                long fimEtapa = System.currentTimeMillis();
                logarTempo("fimEtapa", fimEtapa, validacaoQuestionario);

            } else if (plano.isPlanoPersonal()) {
                irParaCaixaEmAberto(null, null, getColaboradorPersonalSelecionado().getPessoa().getNome(), "");
                notificarRecursoEmpresa(RecursoSistema.PLANO_PERSONAL_LANCADO_SUCESSO);
                redirect("/faces/tela8.jsp");

                long fimPlanoPersonal = System.currentTimeMillis();
                logarTempo("fimPlanoPersonal", fimPlanoPersonal, fimPlanoPersonalNaoPersonal);
            }

            if (contratoGravado != null &&
                    this.formaPagamentoSelecinada == FormaPagamentoVendaRapidaEnum.BOLETO_BANCARIO_ONLINE.getCodigo()) {
                String uuid = gerarBoletos(contratoGravado);
                if (!bv && !UteisValidacao.emptyString(uuid)) {
                    redirect("/faces/inclusaoAlunoVenda.jsp?tkb=" + uuid);
                }
            }

            boolean cobrancaAutomaticaHabilitada = getFacade().getEmpresa().isHabilitarCobrancaAutomaticaNaVenda(getEmpresa().getCodigo());
            if (contratoGravado != null && cobrancaAutomaticaHabilitada) {
                realizarCobrancaAutomatica(contratoGravado);
            }

            this.setMsgCarregando("");
            enviaNotificacaoPushContrato(contratoGravado);
        } catch (ValidacaoException e) {
            tratarException(contratoGravado, editar, e, e.getCampos());
        } catch (Exception e) {
            tratarException(contratoGravado, editar, e, null);
        } finally {
            this.setMsgCarregando("");
            setProcessandoOperacao(false);
        }

        logarTempo("TUDO", System.currentTimeMillis(), inicio);
    }



    // GC-461: Ao habilitar a config "habilitarCobrancaAutomaticaNaVenda", será realizado a cobrança automática do sistema no ato da negociação do contrato
    private void realizarCobrancaAutomatica(ContratoVO contratoGravado) throws Exception {
        Connection con = Conexao.getFromSession();
        String chave = getKey();


        try {
            boolean possuiVencimentoNaDataLancamento = Uteis.getData(contratoGravado.getDataLancamento(), "DD/MM/YYYY").equals(Uteis.getData(contratoGravado.getDataPrimeiraParcela(), "DD/MM/YYYY"));
            if(possuiVencimentoNaDataLancamento) {
                VendasOnlineService vendasOnlineService = new VendasOnlineService(chave, con);
                VendaDTO venda = new VendaDTO();

                // AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO = getAutorizacao();

                venda.setOrigemCobranca(OrigemCobrancaEnum.ZW_AUTOMATICO.getCodigo());
                venda.setCodigoEvento(contratoGravado.getEventoVO().getCodigo());
                venda.setCodigoColaborador(contratoGravado.getConsultor().getCodigo());
                venda.setUnidade(this.getEmpresa().getCodigo());
                venda.setCpf(this.getPessoaVO().getCfp());
                venda.setCpfMae(this.getPessoaVO().getCpfMae());
                venda.setCpfPai(this.getPessoaVO().getCpfPai());
                venda.setNome(this.getPessoaVO().getNome());
                venda.setDataNascimento(this.getPessoaVO().getDataNasc_Apresentar());
                venda.setEmail(this.getEmailVO().getEmail());
                venda.setTelefone(this.getTelefoneCelularVO().getNumero());
                venda.setSexo(this.getPessoaVO().getSexo());
                venda.setPlano(this.getPlanoSelecionado());
                //dados endereço
                venda.setCep(this.getEnderecoResidencialVO().getCep());
                venda.setEndereco(this.getEnderecoResidencialVO().getEndereco());
                venda.setBairro(this.getEnderecoResidencialVO().getBairro());
                venda.setNumero(this.getEnderecoResidencialVO().getNumero());
                venda.setComplemento(this.getEnderecoResidencialVO().getComplemento());
                venda.setDiaVencimento(Integer.valueOf(this.getDiaVencimento()));
                //dados cartao
                venda.setCvv(this.autorizacao.getCvv());
                venda.setValidade(this.autorizacao.getValidadeCartao());
                venda.setNumeroCartao(this.autorizacao.getNumeroCartao());
                venda.setNomeCartao(this.autorizacao.getNomeTitularCartao());
                venda.setCpftitularcard(this.autorizacao.getCpfTitular());


                List<MovParcelaVO> listaParcelas = getFacade().getMovParcela().consultarPorContrato(contratoGravado.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                List<MovParcelaVO> parcelasComVencimentoNoMesmoDiaLancamentoContratoEA = listaParcelas.stream()
                        .filter(parcela -> Uteis.getData(parcela.getDataVencimento(), "DD/MM/YYYY")
                                .equals(Uteis.getData(contratoGravado.getDataLancamento(), "DD/MM/YYYY"))
                                && "EA".equals(parcela.getSituacao()))
                        .collect(Collectors.toList());


                List<RetornoVendaTO> retornoVendaTOList = new ArrayList<>();
                vendasOnlineService.cobrarParcelas(
                        venda,
                        parcelasComVencimentoNoMesmoDiaLancamentoContratoEA,
                        this.quantidadeParcelamento,
                        this.clienteVO,
                        getUsuarioLogado(),
                        this.convenioSelecionado,
                        this.getEmpresa().getCodigo(),
                        false,
                        null,
                        OrigemCobrancaEnum.ZW_AUTOMATICO,
                        0.0,
                        retornoVendaTOList
                );

                logarTempo("cobrancaAutomaticaNoAtoDaVendaRealizada", System.currentTimeMillis(), 0);
            }

        } catch (Exception e) {
            throw new ConsistirException("Erro ao realizar cobrança automática: " + e.getMessage());
        }
    }


    private void vincularDependentesContratoRenovadoOuRematriculado(ContratoVO contratoTitular) throws Exception {
        ParentescoVO parentescoDependente = getFacade().getParentesco().obterParentescoCriandoSeNaoExiste("DEPENDENTE");
        getContratosDependentes().addAll(getFacade().getContratoDependente().findAllByContrato(contratoTitular.getCodigo()));

        Integer codigoContratoAnterior = !UteisValidacao.emptyNumber(contratoTitular.getContratoBaseadoRenovacao()) ?
                contratoTitular.getContratoBaseadoRenovacao() : contratoTitular.getContratoBaseadoRematricula();
        if (!UteisValidacao.emptyNumber(codigoContratoAnterior)) {
            List<FamiliarVO> dependentesAdicionados = new ArrayList<>();
            List<ContratoDependenteVO> contratosDependentesAntigos = getFacade().getContratoDependente().findAllByContratoOrderByPosicaoDependente(codigoContratoAnterior);
            for (ContratoDependenteVO contratoDependenteAntigo: contratosDependentesAntigos) {
                ClienteVO clienteDependente = getFacade().getCliente().consultarPorCodigo(contratoDependenteAntigo.getCliente().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                FamiliarVO dependenteFamiliar = getFacade().getFamiliar().consultarPorClienteFamiliar(contratoTitular.getCliente().getCodigo(), clienteDependente.getCodigo());
                dependenteFamiliar.setCompartilharPlano(true);
                dependenteFamiliar.setIdentificador("");
                dependenteFamiliar.setNome(clienteDependente.getNome_Apresentar());
                dependenteFamiliar.setCodAcesso(clienteDependente.getCodAcesso());
                dependenteFamiliar.setFamiliar(clienteDependente.getCodigo());
                dependenteFamiliar.setParentesco(parentescoDependente);
                dependentesAdicionados.add(dependenteFamiliar);
                if (dependentesAdicionados.size() >= contratoTitular.getPlano().getQuantidadeCompartilhamentos()) {
                    break;
                }
            }
            for (FamiliarVO familiarAdicionado : dependentesAdicionados) {
                familiarAdicionado.setContratoCompartilhado(contratoTitular.getCodigo());
                processarAlteracaoFamiliar(familiarAdicionado, false);
            }
            getClienteVO().getFamiliarVOs().addAll(dependentesAdicionados);
            getFacade().getFamiliar().alterarFamiliars(getClienteVO().getCodigo(), getClienteVO().getFamiliarVOs(), getClienteVO().getFamiliaresExcluidosVOs());
        }
    }

    private static void enviaNotificacaoPushContrato(ContratoVO contratoVO) throws Exception {
        try {
            if(contratoVO != null && contratoVO.getCodigo() != null && contratoVO.getCodigo() != 0) {
                String chave = JSFUtilities.getFromSession("key").toString();
                ServicoNotificacaoPush.enviaNotificacaoContrato(chave, contratoVO.getEmpresa().getCodigo(), false, contratoVO.getOrigemSistema().getDescricao(),
                        contratoVO.getValorFinal(), contratoVO.getPlano().getDescricao(), contratoVO.getContratoDuracao().getNumeroMeses(), contratoVO.getEmpresa().getNome(), contratoVO.getCliente().getCodigo(), getFacade().getMovParcela().getCon(), "");
            }
        } catch (Exception ignore) {
        }
    }

    private void limparFamiliaresSeApresentarDependente() {
        if (isApresentarDependente()) {
            getClienteVO().setFamiliarVOs(new ArrayList<>());
        }
    }

    private void validarPessoaEstrangeira() {
        if (isPessoaEstrangeira()) {
            getClienteVO().getPessoa().setCfp(null);
        } else {
            getClienteVO().getPessoa().setRne(null);
        }
    }

    private void redirectProximaTela(String tela, ContratoVO contratoVO) throws Exception {
        if (contratoVO == null) {
            redirect(tela);
        }
        if (this.formaPagamentoSelecinada == FormaPagamentoVendaRapidaEnum.OUTRA_FORMA_PAGTO.getCodigo()) {
            irParaCaixaEmAberto(null, null, "", getClienteVO().getCodigoMatricula().toString());
            redirect("/faces/tela8.jsp");
        } else {
            redirect(tela);
        }
    }

    private String gerarBoletos(ContratoVO contratoVO) {
        Date inicio = Calendario.hoje();
        try {
            this.setMsgCarregando("Gerando boletos");

            this.setListaBoletosOnlineGerados(new ArrayList<>());

            List<MovParcelaVO> parcelas = getFacade().getMovParcela().consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, "EA");
            if (UteisValidacao.emptyList(parcelas)) {
                throw new ConsistirException("Nenhuma parcela em aberto encontrada, para gerar boleto");
            }

            ConvenioCobrancaVO convenioCobrancaVO = null;
            for (ConvenioCobrancaVO obj : this.getConveniosCobrancaEmpresa()) {
                if (obj.getCodigo().equals(this.getConvenioCobrancaBoletoSelecionado())) {
                    convenioCobrancaVO = obj;
                    break;
                }
            }
            if (convenioCobrancaVO == null) {
                throw new ConsistirException("Convênio de cobrança não encontrado");
            }

            //adicionar autorização de cobrança de boleto caso o cliente não tenha
            adicionarAutorizacaoCobrancaBoleto(contratoVO, convenioCobrancaVO);

            PessoaCPFTO pessoaCPFTO = getFacade().getBoleto().obterDadosPessoaPagador(contratoVO.getEmpresa().getCodigo(), contratoVO.getPessoa(), true, true);

            Date inicioG = Calendario.hoje();

            // Customização SESC para Boleto Caixa Online registar na geração ou na impressão
            boolean registrarBoletoAgora = true;
            if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE)) {
                registrarBoletoAgora = !convenioCobrancaVO.isRegistrarBoletoOnlineSomenteNaImpressao();
            }

            List<BoletoVO> boletosGerados = getFacade().getBoleto().gerarBoletoPorParcela(new PessoaVO(pessoaCPFTO.getCodigo()),
                    convenioCobrancaVO, parcelas, getUsuarioLogado(), OrigemCobrancaEnum.ZW_MANUAL_VENDA_RAPIDA, false, registrarBoletoAgora);
            Date fimG = Calendario.hoje();
            Uteis.logarDebug("InclusaoVendaRapidaControle | gerarBoletosPjBank | Tempo gasto: " + ((fimG.getTime() - inicioG.getTime()) / 1000) + " segundos");

            this.setListaBoletosOnlineGerados(Ordenacao.ordenarLista(boletosGerados, "dataVencimento"));

            BoletoVendaRapidaTO dto = new BoletoVendaRapidaTO();
            dto.setEmpresa(getEmpresa().getCodigo());
            dto.setContrato(contratoVO.getCodigo());
            dto.setCliente(getClienteVO().getCodigo());
            dto.setConvenio(this.getConvenioCobrancaBoletoSelecionado());
            dto.setBoletos(new ArrayList<>());
            for (BoletoVO boletoVO : this.getListaBoletosOnlineGerados()) {
                dto.getBoletos().add(boletoVO.getCodigo());
            }
            String uuid = UUID.randomUUID().toString();
            JSFUtilities.storeOnSession(uuid, dto);
            return uuid;
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        } finally {
            Date fim = Calendario.hoje();
            Uteis.logarDebug("InclusaoVendaRapidaControle | gerarBoletos | Tempo gasto: " + ((fim.getTime() - inicio.getTime()) / 1000) + " segundos");
            this.setMsgCarregando("");
        }
    }

    private void adicionarAutorizacaoCobrancaBoleto(ContratoVO contratoVO, ConvenioCobrancaVO convenioCobrancaVO) {
        try {
            boolean existe = getFacade().getAutorizacaoCobrancaCliente().existeAutorizacaoCobranca(null, contratoVO.getPessoa().getCodigo(),
                    TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO);
            if (existe) {
                return;
            }
            AutorizacaoCobrancaClienteVO auto = new AutorizacaoCobrancaClienteVO();
            auto.setCliente(getFacade().getCliente().consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), contratoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_WS));
            auto.setConvenio(convenioCobrancaVO);
            auto.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO);
            auto.setTipoACobrar(TipoObjetosCobrarEnum.TUDO);
            auto.setValidarAutorizacaoCobrancaSemelhante(false);
            getFacade().getAutorizacaoCobrancaCliente().incluir(auto);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void tratarException(ContratoVO contratoGravado, Integer editar, Exception e, String[] campos) {
        e.printStackTrace();
        Uteis.logar(e, InclusaoVendaRapidaControle.class);
        try {
            if (contratoGravado != null) {
                getFacade().getContrato().excluir(contratoGravado);
                getContratosDependentes().clear();
                getFacade().getZWFacade().atualizarSintetico(contratoGravado.getCliente(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, false);
            }
        } catch (Exception ignored) {
        }
        montarErro(e);
        setMsgAlert(getMensagemNotificar());

        if (campos != null && campos.length > 0) {
            String function = "try{camposRequeridos('";
            for (String c : campos) {
                function += "," + c + "";
            }
            function = function.replaceFirst(",", "") + "')}catch(e){console.log(e)}; ";
            setMsgAlert(function + getMsgAlert());
        }
    }

    public void enviarEventoSelecionado(){
        //
    }

    public void montarPlanoSelecionado() throws Exception {
        montarPlanoSelecionado(situacaoContratoBase.equals("RN"));
    }

    public void montarPlanoSelecionado(boolean isRenovacao) throws Exception {
        setListaPremioPortadorCupom(null);
        setNumeroCupomAplicar("");
        setNumeroCupomAplicado("");
        planoVOSelecionado = getFacade().getPlano().consultarPorChavePrimaria(planoSelecionado, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);

        if (planoVOSelecionado == null) {
            plano = new PlanoWS();
            return;
        }

        if (planoVOSelecionado.isPlanoPersonal() && getClienteVO() != null && !UteisValidacao.emptyNumber(getClienteVO().getCodigo())) {
            limparDadosCliente();
        }

        nrVezesParcelarAdesao = 1;
        nrVezesParcelarProduto = 1;

        parcelarAdesao = planoVOSelecionado.getCobrarAdesaoSeparada();
        parcelasAdesao = new ArrayList<>();
        parcelasAdesao.add(new SelectItem(1, PropsService.getPropertyValueApp("a_vista")));

        PlanoEmpresaVO planoEmpresaVO = planoVOSelecionado.obterPlanoEmpresa(getEmpresa().getCodigo());

        // Acrescentado parâmetro para evitar que caso o plano recorrência possua parcelas com valores diferentes, o sistema mostre estas parcelas em contratos renovados
        plano = planoVOSelecionado.toWS(planoEmpresaVO, Conexao.getFromSession(), isRenovacao, false,false);
        for (int i = 2; i <= planoVOSelecionado.getNrVezesParcelarAdesao(); i++) {
            parcelasAdesao.add(new SelectItem(i, i + "x"));
        }
        montarListaDiasVencimento(planoVOSelecionado.getListaPreenchidaVencimentos());

        processarExibicaoInputIndicadoPor();

        validarCampanhaCupomDesconto();

        if (!UteisValidacao.emptyList(planoVOSelecionado.getPlanoDuracaoVOs())) {
            PlanoDuracaoVO planoDuracaoVO = planoVOSelecionado.getPlanoDuracaoVOs().get(0);
            this.quantidadeParcelamento = planoDuracaoVO.getNumeroMeses();
        }
        if (planoVOSelecionado.getParcelamentoOperadora()) {
            this.tipoParcelamentoEscolhido = TipoParcelamentoVendaRapidaEnum.PARCELAMENTO_OPERADORA.getCodigo();
            if (!planoVOSelecionado.isParcelamentoOperadoraDuracao()) {
                this.quantidadeParcelamento = planoVOSelecionado.getMaximoVezesParcelar();
            }
        } else {
            this.tipoParcelamentoEscolhido = TipoParcelamentoVendaRapidaEnum.PARCELAMENTO_RECORRENTE.getCodigo();
        }
        montarListaQuantidadeParcelas(quantidadeParcelamento);
        setApresentarDependente(planoVOSelecionado.getQuantidadeCompartilhamentos() > 0
                && (situacaoContratoBase == null || !situacaoContratoBase.equals("RN")));
        if (!apresentarDependente) {
            clienteDependente = new ClienteVO();
        }
    }

    private void limparDadosCliente() {
        setClienteVO(new ClienteVO());
        setPessoaVO(new PessoaVO());
        setEnderecoResidencialVO(new EnderecoVO());
        setEnderecoComercialVO(new EnderecoVO());
        setCidadeVO(new CidadeVO());
        setEmailVO(new EmailVO());
        setConsultor(0);
        setCodigoContratoBase(0);
        setSituacaoContratoBase("");
        setTelefoneCelularVO(new TelefoneVO());
        setTelefoneResidencialVO(new TelefoneVO());
        setTelefoneVO(new TelefoneVO());
        setTelefoneComercialVO(new TelefoneVO());
    }

    private void montarListaQuantidadeParcelas(int quantidadeParcelamento) {
        setListaQuantidadeParcelas(new ArrayList<>());
        for (int i = 1; i <= quantidadeParcelamento; i++) {
            getListaQuantidadeParcelas().add(new SelectItem(i, String.valueOf(i)));
        }
    }

    public void selecionar(ActionEvent evt) {
        String t = (String) evt.getComponent().getAttributes().get("t");
        tipoPagamentoAluno = TipoPagamentoAluno.valueOf(t);
        tipoAutorizacao = tipoPagamentoAluno.t;

        try {
            inicializarConvenio();
        } catch (Exception e) {
            convenios = null;
            convenioSelecionado = null;
        }
    }

    private void inicializarConvenio() throws Exception {
        conveniosCobranca = getFacade().getVendaService().convenios(getEmpresa().getCodigo(), tipoAutorizacao,
                tipoPagamentoAluno.tc);
        convenioSelecionado = null;
        if (UteisValidacao.emptyList(conveniosCobranca)) {
            convenios = null;
        } else if (conveniosCobranca.size() == 1) {
            convenios = null;
            convenioSelecionado = conveniosCobranca.get(0).getCodigo();
            convenioSelecionadoCodigoBanco = conveniosCobranca.get(0).getBanco().getCodigoBanco();
        } else {
            convenios = new ArrayList<>();
            convenioSelecionado = conveniosCobranca.get(0).getCodigo();
            for (ConvenioCobrancaVO cc : conveniosCobranca) {
                convenios.add(new SelectItem(cc.getCodigo(), cc.getDescricao()));
            }
        }
    }

    private void montarConveniosCobrancaEmpresa() {
        try {
            this.setConveniosCobrancaEmpresa(new ArrayList<>());
            if(UteisValidacao.emptyNumber(getEmpresa().getCodigo())) {
                return;
            }
            this.setConveniosCobrancaEmpresa(getFacade().getConvenioCobranca().consultarPorEmpresa(getEmpresa().getCodigo(),
                    SituacaoConvenioCobranca.ATIVO, false, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            montarSelectItemConveniosCobrancaBoletoOnline();
        } catch (Exception ex) {
            ex.printStackTrace();
            this.setConveniosCobrancaEmpresa(new ArrayList<>());
        }
    }

    public void gravarConvidado() {
        try {
            setMsgAlert("");
            if (UteisValidacao.emptyNumber(getClienteVO().getCodigo())) {
                getClienteVO().setCodigoMatricula(0);
                getClienteVO().setMatricula("");
                getClienteVO().setVinculoVOs(new ArrayList<>());

                //validar
                getFacade().getVendaService().gravar(getTelefoneCelularVO(), getTelefoneComercialVO(),
                        getTelefoneResidencialVO(), getEnderecoResidencialVO(), getEnderecoComercialVO(),
                        getClienteVO(), getPessoaVO(), getEmailVO(), getUsuarioLogado(), getEmpresa(),
                        getConfiguracaoSistema(), isPessoaEstrangeira(), true);

                //gravar
                getFacade().getVendaService().gravar(getTelefoneCelularVO(), getTelefoneComercialVO(),
                        getTelefoneResidencialVO(), getEnderecoResidencialVO(), getEnderecoComercialVO(),
                        getClienteVO(), getPessoaVO(), getEmailVO(), getUsuarioLogado(), getEmpresa(),
                        getConfiguracaoSistema(), isPessoaEstrangeira(), false);

                ColaboradorVO consultorVo = new ColaboradorVO();
                consultorVo.setCodigo(consultor);
                getFacade().getVendaService().adicionarConsultor(getClienteVO(), consultorVo);

                //envia email do "mailing após cadastro do visitante", caso exista criado
                try{
                    boolean enviarMalaDiretaVisitante = !UteisValidacao.emptyList(getFacade().getMalaDireta().consultarCodigosPorOcorrencia(
                            OcorrenciaEnum.INCLUSAO_VISITANTE, clienteVO.getEmpresa().getCodigo()));
                    if (enviarMalaDiretaVisitante) {
                        enviarMalaDiretaVisitante(getClienteVO().getCodigo());
                    }
                }catch (Exception ex){
                    Logger.getLogger(ClienteControle.class.getName()).log(Level.SEVERE, null, ex);
                }

            }
            getFacade().getConvite().lancarConvite(getUsuarioLogado(), getClienteVO(), indicadoPor);
            redirect("/faces/clienteNav.jsp?page=cliente&matricula=" + getClienteVO().getCodigoMatricula());
        } catch (ValidacaoException ex1) {
            montarErro(ex1);
            setMsgAlert(getMensagemNotificar());
        } catch (Exception ex2) {
            try {
                getFacade().getCliente().excluir(getClienteVO());
            } catch (Exception e1) {
            }
            montarErro(ex2);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void alterarEmpresa() throws Exception {
        if(UteisValidacao.emptyNumber(getEmpresa().getCodigo())) {
            return;
        }
        setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        montarPlanos();
        montarListaSelectItemConsultor();
        montarConveniosCobrancaEmpresa();
    }

    public void montarPlanos() throws Exception {
        nrVezesParcelarAdesao = 1;
        nrVezesParcelarProduto = 1;
        planos = new ArrayList<>();
        if (UteisValidacao.emptyNumber(getEmpresa().getCodigo())) {
            return;
        }
        Map<Integer, String> planosMap = getFacade().getPlano().consultarPlanosSimplesVendaRapida(getEmpresa().getCodigo());
        planos.add(new SelectItem(0, ""));
        for (Map.Entry<Integer, String> entryPlano : planosMap.entrySet()) {
            planos.add(new SelectItem(entryPlano.getKey(), entryPlano.getValue()));
        }
        if (UteisValidacao.emptyList(planos)) {
            planoSelecionado = null;
            return;
        }
        planos = Ordenacao.ordenarLista(planos, "label");
        planoSelecionado = 0;
    }

    public void montarPlanosClienteSelecionado(Integer codigoCategoriaCliente) throws Exception {
        if (UteisValidacao.emptyNumber(codigoCategoriaCliente)) {
            String codigoCliente = "";
            try {
                codigoCliente = JSFUtilities.getFromRequestParameterMap("cliente");
            } catch (Exception ignore) {
            }
            if (!UteisValidacao.emptyString(codigoCliente) && !UteisValidacao.emptyNumber(Integer.parseInt(codigoCliente))) {
                ClienteVO cli = getFacade().getCliente().consultarPorCodigo(Integer.parseInt(codigoCliente), false, Uteis.NIVELMONTARDADOS_TODOS);
                if (cli != null && !UteisValidacao.emptyNumber(cli.getCodigo()) && cli.getCategoria() != null && !UteisValidacao.emptyNumber(cli.getCategoria().getCodigo())) {
                    codigoCategoriaCliente = cli.getCategoria().getCodigo();
                }
            }
        }

        planos = new ArrayList<>();
        if (UteisValidacao.emptyNumber(getEmpresa().getCodigo())) {
            return;
        }
        Map<Integer, String> planosMap = getFacade().getPlano().consultarPlanosSimplesVendaRapidaClienteSelecionado(getEmpresa().getCodigo(), codigoCategoriaCliente);
        planos.add(new SelectItem(0, ""));
        for (Map.Entry<Integer, String> entryPlano : planosMap.entrySet()) {
            planos.add(new SelectItem(entryPlano.getKey(), entryPlano.getValue()));
        }
        if (UteisValidacao.emptyList(planos)) {
            planoSelecionado = null;
            return;
        }
        planos = Ordenacao.ordenarLista(planos, "label");
        boolean planoSelecionadoEncontrado = false;
        if(!UteisValidacao.emptyNumber(planoSelecionado)) {
            for (SelectItem item : planos) {
                if(item.getValue().equals(planoSelecionado)){
                    planoSelecionadoEncontrado = true;
                    break;
                }
            }
        }
        if(!planoSelecionadoEncontrado) {
            planoSelecionado = 0;
        }
    }

    private Map<String, Integer> getMapParcelasAgrupadas(List<PlanoRecorrenciaParcelaWS> parcelas) {
        Map<String, Integer> mapParcelas = new LinkedHashMap<>();

        for(PlanoRecorrenciaParcelaWS parcela : parcelas) {
            int quantidade = 0;
            double valor = Uteis.converterMoedaParaDouble(parcela.getValor());
            if (getListaPremioPortadorCupom() != null) {
                for (CampanhaCupomDescontoPremioPortadorVO premio : getListaPremioPortadorCupom()) {
                    if (premio.getTipoPremio().equals(CampanhaCupomDescontoPremioPortadorVO.TIPO_PREMIO_MENSALIDADE)) {
                        if (premio.getDescricaoPremio().equals("PARCELA " + parcela.getNumero())) {
                            valor = valor - Uteis.arredondarForcando2CasasDecimais(premio.calcularDescontoPremio(valor));
                            break;
                        }
                    }
                }
            }
            String valorFinal = Uteis.getDoubleFormatado(valor);
            if(mapParcelas.containsKey(valorFinal)) {
                quantidade = mapParcelas.get(valorFinal);
            }
            quantidade++;
            mapParcelas.put(valorFinal, quantidade);
        }

        return mapParcelas;
    }

    private double getValorDescontoTotalDosCupons(List<PlanoRecorrenciaParcelaWS> parcelas) {
        double valorDescontos = 0.0;

        for(PlanoRecorrenciaParcelaWS parcela : parcelas) {
            int quantidade = 0;
            double valor = Uteis.converterMoedaParaDouble(parcela.getValor());
            if (getListaPremioPortadorCupom() != null) {
                for (CampanhaCupomDescontoPremioPortadorVO premio : getListaPremioPortadorCupom()) {
                    if (premio.getTipoPremio().equals(CampanhaCupomDescontoPremioPortadorVO.TIPO_PREMIO_MENSALIDADE)) {
                        if (premio.getDescricaoPremio().equals("PARCELA " + parcela.getNumero())) {
                            valorDescontos += Uteis.arredondarForcando2CasasDecimais(premio.calcularDescontoPremio(valor));
                            break;
                        }
                    }
                }
            }
        }
        return valorDescontos;
    }

    private Map<String, Integer> getMapParcelasAnuidadeAgrupadas(List<PlanoAnuidadeParcelaWS> parcelas) {
        Map<String, Integer> mapParcelas = new LinkedHashMap<>();

        for(PlanoAnuidadeParcelaWS parcela : parcelas) {
            int quantidade = 0;
            String valor = Formatador.formatarValorMonetarioSemMoeda(parcela.getValor());
            if(mapParcelas.containsKey(valor)) {
                quantidade = mapParcelas.get(valor);
            }
            quantidade++;
            mapParcelas.put(valor, quantidade);
        }

        return mapParcelas;
    }

    private String getApresentarValoresParcelados(Map<String, Integer> mapParcelas) {
        String parcelasApresentar = "";

        for(String key : mapParcelas.keySet()) {
            if(!parcelasApresentar.equals("")) {
                parcelasApresentar += " ";
            }
            parcelasApresentar += mapParcelas.get(key) + "XR$ " + key.replace(" R$ ", "").replace("R$", "");
        }
        return parcelasApresentar;
    }

    public String getMensagemRenovacaoRematricula() {
        switch (getSituacaoContratoBase()) {
            case "RN":
                return "Renovação do contrato de código " + getCodigoContratoBase();
            case "RE":
                return "Rematrícula do contrato de código " + getCodigoContratoBase();
            default:
                return "";
        }
    }

    public TipoAutorizacaoCobrancaEnum getTipoAutorizacao() {
        return tipoAutorizacao;
    }

    public void setTipoAutorizacao(TipoAutorizacaoCobrancaEnum tipoAutorizacao) {
        this.tipoAutorizacao = tipoAutorizacao;
    }

    public List<SelectItem> getConvenios() {
        return convenios;
    }

    public void setConvenios(List<SelectItem> convenios) {
        this.convenios = convenios;
    }

    public List<SelectItem> getPlanos() {
        return planos;
    }

    public void setPlanos(List<SelectItem> planos) {
        this.planos = planos;
    }

    public Integer getPlanoSelecionado() {
        return planoSelecionado;
    }

    public void setPlanoSelecionado(Integer planoSelecionado) {
        this.planoSelecionado = planoSelecionado;
    }

    public String getValorMatriculaApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(plano.getTaxaAdesao());
    }

    public String getValorManutencaoApresentar() {
        if(UteisValidacao.emptyList(plano.getParcelasAnuidade())) {
            return Formatador.formatarValorMonetarioSemMoeda(plano.getValorAnuidade());
        }

        Map<String, Integer> mapParcelas = getMapParcelasAnuidadeAgrupadas(plano.getParcelasAnuidade());
        return getApresentarValoresParcelados(mapParcelas);
    }

    public String getValorMensalApresentar() {
        if(UteisValidacao.emptyList(plano.getParcelas())) {
            return Formatador.formatarValorMonetarioSemMoeda(plano.getValorMensal());
        }
        Map<String, Integer> mapParcelas = getMapParcelasAgrupadas(plano.getParcelas());
        if (planoVOSelecionado.getRegimeRecorrencia()){
            if (formaPagamentoSelecinada == FormaPagamentoVendaRapidaEnum.CARTAO_CREDITO.getCodigo()
                    && tipoParcelamentoEscolhido == TipoParcelamentoVendaRapidaEnum.PARCELAMENTO_OPERADORA.getCodigo()){
                double valorTotalMensalidade = plano.getParcelas().size() * plano.getValorMensal() - getValorDescontoTotalDosCupons(plano.getParcelas());
                return this.quantidadeParcelamento  + " X R$ "  + Formatador.formatarValorMonetarioSemMoeda(Uteis.arredondarForcando2CasasDecimais(valorTotalMensalidade/quantidadeParcelamento));
            }
        }
        return getApresentarValoresParcelados(mapParcelas);
    }

    public Integer getNrVezesParcelarAdesao() {
        return nrVezesParcelarAdesao;
    }

    public void setNrVezesParcelarAdesao(Integer nrVezesParcelarAdesao) {
        this.nrVezesParcelarAdesao = nrVezesParcelarAdesao;
    }

    public PlanoWS getPlano() {
        if (plano == null) {
            plano = new PlanoWS();
        }
        return plano;
    }

    public void setPlano(PlanoWS plano) {
        this.plano = plano;
    }

    public List<SelectItem> getParcelasAdesao() {
        return parcelasAdesao;
    }

    public void setParcelasAdesao(List<SelectItem> parcelasAdesao) {
        this.parcelasAdesao = parcelasAdesao;
    }

    public AutorizacaoCobrancaClienteVO getAutorizacao() {
        return autorizacao;
    }

    public void setAutorizacao(AutorizacaoCobrancaClienteVO autorizacao) {
        this.autorizacao = autorizacao;
    }

    public Integer getConvenioSelecionadoCodigoBanco() {
        return convenioSelecionadoCodigoBanco;
    }

    public void setConvenioSelecionadoCodigoBanco(Integer convenioSelecionadoCodigoBanco) {
        this.convenioSelecionadoCodigoBanco = convenioSelecionadoCodigoBanco;
    }

    public boolean isDcoContaValida() {
        return dcoContaValida;
    }

    public void setDcoContaValida(boolean dcoContaValida) {
        this.dcoContaValida = dcoContaValida;
    }

    public boolean isDcoCpfValido() {
        return dcoCpfValido;
    }

    public void setDcoCpfValido(boolean dcoCpfValido) {
        this.dcoCpfValido = dcoCpfValido;
    }

    public String getErroValidacaoDcoConta() {
        return erroValidacaoDcoConta;
    }

    public void setErroValidacaoDcoConta(String erroValidacaoDcoConta) {
        this.erroValidacaoDcoConta = erroValidacaoDcoConta;
    }

    public String getDiaVencimento() {
        if (diaVencimento == null) {
            diaVencimento = "";
        }
        return diaVencimento;
    }

    public void setDiaVencimento(String diaVencimento) {
        this.diaVencimento = diaVencimento;
    }

    public List<SelectItem> getListaDiaVencimento() {
        if (listaDiaVencimento == null) {
            listaDiaVencimento = new ArrayList<>();
        }
        return listaDiaVencimento;
    }

    public void setListaDiaVencimento(List<SelectItem> listaDiaVencimento) {
        this.listaDiaVencimento = listaDiaVencimento;
    }

    public List<ClienteVO> consultarClientes(Object suggest) {
        String nomeOuMatricula = (String) suggest;
        List<ClienteVO> clientes = new ArrayList<>();
        Integer matricula = 0;

        try {
            matricula = Integer.parseInt(nomeOuMatricula);
        } catch (Exception e) {
        }

        try {
            if (matricula > 0) {
                clientes = getFacade().getCliente().consultarPorMatricula(matricula.toString(), 0, false,
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                clientes = getFacade().getCliente().consultarPorNomePessoa(nomeOuMatricula, 0,
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS, 50);
            }
        } catch (Exception e) {
            Uteis.logar(e, InclusaoVendaRapidaControle.class);
        }

        return clientes;
    }

    public void selecionarClienteIndicacao() {
        ClienteVO cliente = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
        setIndicadoPor(cliente);
    }

    public ClienteVO getIndicadoPor() {
        return indicadoPor;
    }

    public void setIndicadoPor(ClienteVO indicadoPor) {
        this.indicadoPor = indicadoPor;
    }

    public boolean isExibirInputIndicadoPor() {
        return exibirInputIndicadoPor;
    }

    public void setExibirInputIndicadoPor(boolean exibirInputIndicadoPor) {
        this.exibirInputIndicadoPor = exibirInputIndicadoPor;
    }

    public String getNumeroCupomAplicar() {
        if (numeroCupomAplicar == null) {
            numeroCupomAplicar = "";
        }
        return numeroCupomAplicar;
    }

    public void setNumeroCupomAplicar(String numeroCupomAplicar) {
        this.numeroCupomAplicar = numeroCupomAplicar;
    }

    public String getNumeroCupomAplicado() {
        if (numeroCupomAplicado == null) {
            numeroCupomAplicado = "";
        }
        return numeroCupomAplicado;
    }

    public void setNumeroCupomAplicado(String numeroCupomAplicado) {
        this.numeroCupomAplicado = numeroCupomAplicado;
    }

    public List<TipoColaboradorEnum> getTiposColaboradoresPersonal() {
        return tiposColaboradoresPersonal;
    }

    public void setTiposColaboradoresPersonal(List<TipoColaboradorEnum> tiposColaboradoresPersonal) {
        this.tiposColaboradoresPersonal = tiposColaboradoresPersonal;
    }

    public List<ColaboradorVO> getColaboradoresPersonal() {
        return colaboradoresPersonal;
    }

    public void setColaboradoresPersonal(List<ColaboradorVO> colaboradoresPersonal) {
        this.colaboradoresPersonal = colaboradoresPersonal;
    }

    public void selecionarColaboradorPersonal() {
        try {
            ColaboradorVO colaborador = (ColaboradorVO) context().getExternalContext().getRequestMap()
                    .get("colaborador");
            setPessoaVO(colaborador.getPessoa());
            setColaboradorPersonalSelecionado(colaborador);

            setNomeConvidado(colaborador.getPessoa().getNome());
            atribuirEstados(getPessoaVO());
            atribuirCidades(getPessoaVO());
            atribuirEmail(getPessoaVO());
            atribuirEndereco(getPessoaVO());
            atribuirTelefones(getPessoaVO());

            List<AutorizacaoCobrancaColaboradorVO> autorizacoes = getFacade().getAutorizacaoCobrancaColaborador().consultarPorPessoa(getPessoaVO().getCodigo());
            if (!UteisValidacao.emptyList(autorizacoes)) {
                AutorizacaoCobrancaColaboradorVO autorizacaoCobrancaColaborador = autorizacoes.get(0);
                AragornService aragornService = new AragornService();
                aragornService.povoarAutorizacaoCobrancaVO(autorizacaoCobrancaColaborador, true,true);
                aragornService = null;
                tipoAutorizacao = autorizacaoCobrancaColaborador.getTipoAutorizacao();
                tipoPagamentoAluno = TipoPagamentoAluno.getFromAutorizacao(autorizacaoCobrancaColaborador);
                if (tipoAutorizacao != null && tipoPagamentoAluno != null) {
                    inicializarConvenio();
                    setAutorizacaoColaborador(autorizacaoCobrancaColaborador);
                    getAutorizacaoColaborador().setNumeroCartao(getAutorizacaoColaborador().getCartaoMascarado_Apresentar());
                    getAutorizacaoColaborador().setObjetoVOAntesAlteracao(autorizacaoCobrancaColaborador.getClone(true));
                    if (tipoAutorizacao.equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA)) {
                        setDcoContaValida(true);
                    }
                }
            }

        } catch (Exception e) {
            montarErroComLog(e);
        }
    }

    private void atribuirEstados(PessoaVO pessoaVO) throws Exception {
        if (pessoaVO.getPais() != null && UteisValidacao.notEmptyNumber(pessoaVO.getPais().getCodigo())) {
            montarListaSelectItemEstado();
        }
    }

    private void atribuirCidades(PessoaVO pessoaVO) throws Exception {
        if (pessoaVO.getEstadoVO() != null && UteisValidacao.notEmptyNumber(pessoaVO.getEstadoVO().getCodigo())) {
            montarListaSelectItemCidade();
        }
    }

    private void atribuirEmail(PessoaVO pessoaVO) {
        List<EmailVO> emails = pessoaVO.getEmailVOs();
        if (!UteisValidacao.emptyList(emails)) {
            setEmailVO(emails.get(0));
        }
    }

    private void atribuirEndereco(PessoaVO pessoaVO) {
        List<EnderecoVO> enderecos = pessoaVO.getEnderecoVOs();
        for (EnderecoVO e : enderecos) {
            if (UteisValidacao.emptyString(e.getTipoEndereco()) || e.getTipoEndereco().equals(TipoEnderecoEnum.RESIDENCIAL.getCodigo())) {
                setEnderecoResidencialVO(e);
            }
            if (e.getTipoEndereco().equals(TipoEnderecoEnum.COMERCIAL.getCodigo())) {
                setAdicionarEnderecoComercial(true);
                setEnderecoComercialVO(e);
            }
        }
    }

    private void atribuirTelefones(PessoaVO pessoaVO) {
        List<TelefoneVO> telefones = pessoaVO.getTelefoneVOs();
        for (TelefoneVO t : telefones) {
            if (t.getTipoTelefone().equals("RE")) {
                setTelefoneResidencialVO(t);
            } else if (t.getTipoTelefone().equals("CE")) {
                setTelefoneCelularVO(t);
            }
        }
    }

    private void atribuirConsultor(List<VinculoVO> listaVinculos) {
        listaVinculos.stream()
                .filter(it -> it.getTipoVinculo().equals("CO"))
                .findFirst()
                .ifPresent(vConsultor -> setConsultor(vConsultor.getColaborador().getCodigo()));
    }

    @Override
    public void informarNovoCpf() throws Exception {
        informarNovoCadastro();
        getPessoaVO().setCfp("");
    }

    public void informarNovoCadastro() {
        try {
            setMsgAlert("");
            limparMsg();
            getColaboradorVO().setCodigo(0);
            getPessoaVO().setCodigo(0);
            getClienteVO().setCodigo(0);
            getClienteVO().setMsgErroExisteCliente("");
            setApresentarModalClienteCpfIgual(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public ColaboradorVO getColaboradorPersonalSelecionado() {
        return colaboradorPersonalSelecionado;
    }

    public void setColaboradorPersonalSelecionado(ColaboradorVO colaboradorPersonalSelecionado) {
        this.colaboradorPersonalSelecionado = colaboradorPersonalSelecionado;
    }

    public PlanoVO getPlanoVOSelecionado() {
        return planoVOSelecionado;
    }

    public void setPlanoVOSelecionado(PlanoVO planoVOSelecionado) {
        this.planoVOSelecionado = planoVOSelecionado;
    }

    public AutorizacaoCobrancaColaboradorVO getAutorizacaoColaborador() {
        return autorizacaoColaborador;
    }

    public void setAutorizacaoColaborador(AutorizacaoCobrancaColaboradorVO autorizacao) {
        this.autorizacaoColaborador = autorizacao;
    }

    public boolean isAdicionarEnderecoComercial() {
        return adicionarEnderecoComercial;
    }

    public void setAdicionarEnderecoComercial(boolean adicionarEnderecoComercial) {
        this.adicionarEnderecoComercial = adicionarEnderecoComercial;
    }

    public boolean isExisteCampanhaCupomDescontoVigente() {
        return existeCampanhaCupomDescontoVigente;
    }

    public void setExisteCampanhaCupomDescontoVigente(boolean existeCampanhaCupomDescontoVigente) {
        this.existeCampanhaCupomDescontoVigente = existeCampanhaCupomDescontoVigente;
    }

    public void irParaTelaCliente() {
        try {
            redirect("/faces/clienteNav.jsp?page=cliente&matricula=" + getClienteVO().getCodigoMatricula());
        } catch (Exception e) {
            Uteis.logar(e, InclusaoVendaRapidaControle.class);
        }
    }

    public void irParaTelaVendaRapida() {
        try {
            redirect("/faces/inclusaoAlunoVenda.jsp");
        } catch (Exception e) {
            Uteis.logar(e, InclusaoVendaRapidaControle.class);
        }
    }

    public boolean isTelaBoletos() {
        return telaBoletos;
    }

    public void setTelaBoletos(boolean telaBoletos) {
        this.telaBoletos = telaBoletos;
    }

    enum TipoPagamentoAluno {
        VISA(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, null, OperadorasExternasAprovaFacilEnum.VISA),
        MASTERCARD(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, null, OperadorasExternasAprovaFacilEnum.MASTERCARD),
        AMEX(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, null, OperadorasExternasAprovaFacilEnum.AMEX),
        DINERS(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, null, OperadorasExternasAprovaFacilEnum.DINERS),
        ELO(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, null, OperadorasExternasAprovaFacilEnum.ELO),
        HIPERCARD(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, null, OperadorasExternasAprovaFacilEnum.HIPERCARD),
        ITAU(TipoAutorizacaoCobrancaEnum.DEBITOCONTA, TipoConvenioCobrancaEnum.DCO_ITAU, null),
        BRADESCO(TipoAutorizacaoCobrancaEnum.DEBITOCONTA, TipoConvenioCobrancaEnum.DCO_BRADESCO, null),
        SANTANDER(TipoAutorizacaoCobrancaEnum.DEBITOCONTA, TipoConvenioCobrancaEnum.DCO_SANTANDER, null),
        BANCOBRASIL(TipoAutorizacaoCobrancaEnum.DEBITOCONTA, TipoConvenioCobrancaEnum.DCO_BB, null),
        CAIXA(TipoAutorizacaoCobrancaEnum.DEBITOCONTA, TipoConvenioCobrancaEnum.DCO_CAIXA_SICOV, null),;

        TipoAutorizacaoCobrancaEnum t;
        TipoConvenioCobrancaEnum tc;
        OperadorasExternasAprovaFacilEnum operadoraCartao;

        TipoPagamentoAluno(TipoAutorizacaoCobrancaEnum t, TipoConvenioCobrancaEnum tc,
                           OperadorasExternasAprovaFacilEnum operadoraCartao) {
            this.t = t;
            this.operadoraCartao = operadoraCartao;
            this.tc = tc;
        }

        public static TipoPagamentoAluno getFromAutorizacao(AutorizacaoCobrancaColaboradorVO autorizacao) {
            return getFromAutorizacao(autorizacao.getTipoAutorizacao(), autorizacao.getOperadoraCartao(),
                    autorizacao.getConvenio().getTipo());
        }

        public static TipoPagamentoAluno getFromAutorizacao(AutorizacaoCobrancaClienteVO autorizacao) {
            return getFromAutorizacao(autorizacao.getTipoAutorizacao(), autorizacao.getOperadoraCartao(),
                    autorizacao.getConvenio().getTipo());
        }

        public static TipoPagamentoAluno getFromAutorizacao(TipoAutorizacaoCobrancaEnum tipoAutorizacao,
                                                            OperadorasExternasAprovaFacilEnum operadoraCartao, TipoConvenioCobrancaEnum tipoConvenio) {
            if (tipoAutorizacao == null) {
                return null;
            }

            switch (tipoAutorizacao) {
                case CARTAOCREDITO:
                    if (operadoraCartao == null) {
                        return null;
                    }
                    switch (operadoraCartao) {
                        case AMEX:
                            return TipoPagamentoAluno.AMEX;
                        case MASTERCARD:
                            return TipoPagamentoAluno.MASTERCARD;
                        case VISA:
                            return TipoPagamentoAluno.VISA;
                        case ELO:
                            return TipoPagamentoAluno.ELO;
                        case DINERS:
                            return TipoPagamentoAluno.DINERS;
                        case HIPERCARD:
                            return TipoPagamentoAluno.HIPERCARD;
                        default:
                            return null;
                    }
                case DEBITOCONTA:
                    if (tipoConvenio == null) {
                        return null;
                    }
                    switch (tipoConvenio) {
                        case DCO_ITAU:
                            return TipoPagamentoAluno.ITAU;
                        case DCO_BRADESCO:
                        case DCO_BRADESCO_240:
                            return TipoPagamentoAluno.BRADESCO;
                        case DCO_SANTANDER_150:
                        case DCO_SANTANDER:
                            return TipoPagamentoAluno.SANTANDER;
                        case DCO_BB:
                            return TipoPagamentoAluno.BANCOBRASIL;
                        case DCO_CAIXA:
                            return TipoPagamentoAluno.CAIXA;
                        default:
                            return null;
                    }
                default:
                    return null;
            }
        }

        public String getImagem() {
            return (t.equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO) ? "./imagens/bandeiras/" : "./imagens/bancos/")
                    + name().toLowerCase() + ".png";
        }
    }

    public TipoPagamentoAluno getTipoPagamentoAluno() {
        return tipoPagamentoAluno;
    }

    public void setTipoPagamentoAluno(TipoPagamentoAluno tipoPagamentoAluno) {
        this.tipoPagamentoAluno = tipoPagamentoAluno;
    }

    public String getImagemForma() {
        return getTipoPagamentoAluno().getImagem();
    }

    public Integer getConsultor() {
        return consultor;
    }

    public void setConsultor(Integer consultor) {
        this.consultor = consultor;
    }

    public Integer getConvenioSelecionado() {
        return convenioSelecionado;
    }

    public void setConvenioSelecionado(Integer convenioSelecionado) {
        this.convenioSelecionado = convenioSelecionado;
        for (ConvenioCobrancaVO convenio : conveniosCobranca) {
            if (convenio.getCodigo() == convenioSelecionado) {
                this.convenioSelecionadoCodigoBanco = convenio.getBanco().getCodigoBanco();
            }
        }
    }

    public boolean isParcelarAdesao() {
        return parcelarAdesao;
    }

    public void setParcelarAdesao(boolean parcelarAdesao) {
        this.parcelarAdesao = parcelarAdesao;
    }

    public boolean isTemParcelaEmAberto() {
        return temParcelaEmAberto;
    }

    public void setTemParcelaEmAberto(boolean temParcelaEmAberto) {
        this.temParcelaEmAberto = temParcelaEmAberto;
    }

    public boolean isBarrarDevedorVendaRapida() {
        return barrarDevedorVendaRapida;
    }

    public void setBarrarDevedorVendaRapida(boolean barrarDevedorVendaRapida) {
        this.barrarDevedorVendaRapida = barrarDevedorVendaRapida;
    }

    private void montarListaDiasVencimento(List<String> prm) {
        List<SelectItem> objs = new ArrayList<>();
        for (String obj : prm) {
            objs.add(new SelectItem(obj, obj));
        }
        Integer diaAtual = Uteis.getDiaMesData(Calendario.hoje());
        SelectItem itemHoje = new SelectItem(diaAtual, "HOJE");
        objs.remove(0);
        objs.add(0, itemHoje);
        setListaDiaVencimento(objs);
    }

    public boolean isCadastrandoParaConvite() {
        return cadastrandoParaConvite;
    }

    public void setCadastrandoParaConvite(boolean cadastrandoParaConvite) {
        this.cadastrandoParaConvite = cadastrandoParaConvite;
    }

    public void aplicarCupomDesconto() {
        try {
            limparMsg();
            if (UteisValidacao.emptyString(getNumeroCupomAplicar())) {
                throw new ConsistirException(PropsService.getPropertyValueApp("prt_venda_rapida_informe_o_numero_cupom"));
            }
            OAMDService oamdService = new OAMDService();
            CupomDescontoVO cupomDescontoVO = oamdService.validarCupomPortadorCupom(getEmpresaLogado().getCodEmpresaFinanceiro(), getNumeroCupomAplicar(),
                    getPlano().getDescricao());
            if (!cupomDescontoVO.getMsgValidacao().equals("")) {
                throw new ConsistirException(cupomDescontoVO.getMsgValidacao());
            }
            CampanhaCupomDescontoPremioPortador campanhaCupomDescontoPremioPortadorDao = new CampanhaCupomDescontoPremioPortador();
            setListaPremioPortadorCupom(campanhaCupomDescontoPremioPortadorDao.consultarOAMD(cupomDescontoVO.getCampanhaCupomDescontoVO().getId(), getPlano().getDescricao()));
            setNumeroCupomAplicado(getNumeroCupomAplicar());
            setMsgAlert("Richfaces.hideModalPanel('modalCupomDesconto');");
            montarSucessoGrowl(PropsService.getPropertyValueApp("prt_venda_rapida_aplicado_removido_com_sucesso"));
            notificarRecursoEmpresa(RecursoSistema.CUPOM_DESCONTO_NEGOCIACAO_PLANO);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void retirarCupomDesconto() {
        try {
            setListaPremioPortadorCupom(null);
            setNumeroCupomAplicar("");
            setNumeroCupomAplicado("");
            montarSucessoGrowl(PropsService.getPropertyValueApp("prt_venda_rapida_cupom_removido_com_sucesso"));
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void abrirModalCupomDesconto() {
        limparMsg();
        setListaPremioPortadorCupom(null);
        setNumeroCupomAplicar("");
        setNumeroCupomAplicado("");
    }

    public String getMsgInadimplencia() {
        return msgInadimplencia;
    }

    public void setMsgInadimplencia(String msgInadimplencia) {
        this.msgInadimplencia = msgInadimplencia;
    }

    public void acaoAdicionarEnderecoComercial() {
        setAdicionarEnderecoComercial(!isAdicionarEnderecoComercial());
    }

    public void validarCPF(String campo, String cpf) throws ValidacaoException {
        if (UteisValidacao.emptyString(cpf) || !SuperVO.verificaCPF(cpf.trim())) {
            throw new ValidacaoException(new String[] { campo }, PropsService.getPropertyValueApp("prt_venda_rapida_cpf_invalido"));
        }
    }

    public void validarCPFMaeVendaRapida() {
        try {
            setMsgAlert("");
            if (!UteisValidacao.emptyString(getPessoaVO().getCpfMae())
                    && !SuperVO.verificaCPF(getPessoaVO().getCpfMae())) {
                throw new ValidacaoException(new String[]{"cpfrespon"},
                        PropsService.getPropertyValueApp("prt_venda_rapida_cpf_responsavel_invalido"));
            }
            setMsgAlert("document.getElementById('form:dataNasc').focus();");
        } catch (ValidacaoException e) {
            tratarException(null, 0, e, e.getCampos());
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public boolean getDebitoCaixa() {
        return tipoPagamentoAluno != null && tipoPagamentoAluno.equals(TipoPagamentoAluno.CAIXA);

    }

    public String getNomeConvidado() {
        if(nomeConvidado == null){
            nomeConvidado = "";
        }
        return nomeConvidado;
    }

    public void setNomeConvidado(String nomeConvidado) {
        this.nomeConvidado = nomeConvidado;
    }

    public Integer getNrVezesParcelarProduto() {
        return nrVezesParcelarProduto;
    }

    public void setNrVezesParcelarProduto(Integer nrVezesParcelarProduto) {
        this.nrVezesParcelarProduto = nrVezesParcelarProduto;
    }

    public boolean isExibirValidacaoCpf() {
        return exibirValidacaoCpf;
    }

    public Integer getCodigoEventosSelecionado() {
        return codigoEventosSelecionado;
    }

    public void setCodigoEventosSelecionado(Integer codigoEventosSelecionado) {
        this.codigoEventosSelecionado = codigoEventosSelecionado;
    }

    public List<SelectItem> getListaSelectEventoVigente() {
        return listaSelectEventoVigente;
    }

    public void setListaSelectEventoVigente(List listaSelectEventoVigente) {
        this.listaSelectEventoVigente = listaSelectEventoVigente;
    }

    public List<SelectItem> getListaFormaPagamento() {
        this.listaFormaPagamento = new ArrayList<>();
        for (FormaPagamentoVendaRapidaEnum formaPagamentoEnum : FormaPagamentoVendaRapidaEnum.values()) {
            if (FormaPagamentoVendaRapidaEnum.DEBITO_CONTA_CORRENTE.equals(formaPagamentoEnum) && !this.isEmpresaTemConvenioDebitoConta()) {
                continue;
            }
            if (FormaPagamentoVendaRapidaEnum.BOLETO_BANCARIO_ONLINE.equals(formaPagamentoEnum) && !this.isEmpresaTemConvenioBoletoOnline()) {
                continue;
            }
            this.listaFormaPagamento.add(new SelectItem(formaPagamentoEnum.getCodigo(), formaPagamentoEnum.getDescricao()));
        }
        Ordenacao.ordenarLista(this.listaFormaPagamento, "label");
        return listaFormaPagamento;
    }


    public int getFormaPagamentoSelecinada() {
        return formaPagamentoSelecinada;
    }

    public void setFormaPagamentoSelecinada(int formaPagamentoSelecinada) {
        this.formaPagamentoSelecinada = formaPagamentoSelecinada;
    }

    public FormaPagamentoVendaRapidaEnum getFormaPagamentoEnum(){
        return FormaPagamentoVendaRapidaEnum.get(this.formaPagamentoSelecinada);
    }

    public boolean isMostrarPagamentoDebitoConta(){
        return ((tipoAutorizacao == null) && this.isEmpresaTemConvenioDebitoConta()) &&
                (this.formaPagamentoSelecinada == FormaPagamentoVendaRapidaEnum.DEBITO_CONTA_CORRENTE.getCodigo());
    }

    public boolean isMostrarPagamentoCredito(){
        return ((tipoAutorizacao == null) && (this.formaPagamentoSelecinada == FormaPagamentoVendaRapidaEnum.CARTAO_CREDITO.getCodigo()));
    }

    public List<SelectItem> getListaTipoParcelamento() {
        if (listaTipoParcelamento == null){
            listaTipoParcelamento = new ArrayList<>();
            for (TipoParcelamentoVendaRapidaEnum obj: TipoParcelamentoVendaRapidaEnum.values()){
                this.listaTipoParcelamento.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }

        }
        return listaTipoParcelamento;
    }

    public int getTipoParcelamentoEscolhido() {
        return tipoParcelamentoEscolhido;
    }

    public void setTipoParcelamentoEscolhido(int tipoParcelamentoEscolhido) {
        this.tipoParcelamentoEscolhido = tipoParcelamentoEscolhido;
    }

    public int getQuantidadeParcelamento() {
        return quantidadeParcelamento;
    }

    public void setQuantidadeParcelamento(int quantidadeParcelamento) {
        this.quantidadeParcelamento = quantidadeParcelamento;
    }

    public boolean isTipoParcelamentoOperadora(){
        return isMostrarTipoParcelamentoOperadora()
                && (this.tipoParcelamentoEscolhido == TipoParcelamentoVendaRapidaEnum.PARCELAMENTO_OPERADORA.getCodigo());
    }

    public boolean isMostrarTipoParcelamentoOperadora(){
        return ((planoVOSelecionado != null && planoVOSelecionado.getRegimeRecorrencia())
                && (this.formaPagamentoSelecinada == FormaPagamentoVendaRapidaEnum.CARTAO_CREDITO.getCodigo()));
    }

    public List<SelectItem> getListaQuantidadeParcelas() {
        if (listaQuantidadeParcelas == null) {
            listaQuantidadeParcelas = new ArrayList<>();
        }
        return listaQuantidadeParcelas;
    }

    public void setListaQuantidadeParcelas(List<SelectItem> listaQuantidadeParcelas) {
        this.listaQuantidadeParcelas = listaQuantidadeParcelas;
    }

    public boolean isPermitirMudarTipoParcelamentoVendaRapida() {
        return permitirMudarTipoParcelamentoVendaRapida;
    }

    public void setPermitirMudarTipoParcelamentoVendaRapida(boolean permitirMudarTipoParcelamentoVendaRapida) {
        this.permitirMudarTipoParcelamentoVendaRapida = permitirMudarTipoParcelamentoVendaRapida;
    }

    public Integer getCodigoContratoBase() {
        return codigoContratoBase;
    }

    public void setCodigoContratoBase(Integer codigoContratoBase) {
        this.codigoContratoBase = codigoContratoBase;
    }

    public String getSituacaoContratoBase() {
        if (situacaoContratoBase == null) {
            situacaoContratoBase = "";
        }
        return situacaoContratoBase;
    }

    public void setSituacaoContratoBase(String situacaoContratoBase) {
        this.situacaoContratoBase = situacaoContratoBase;
    }

    public List<ConvenioCobrancaVO> getConveniosCobrancaEmpresa() {
        if (conveniosCobrancaEmpresa == null) {
            conveniosCobrancaEmpresa = new ArrayList<>();
        }
        return conveniosCobrancaEmpresa;
    }

    public void setConveniosCobrancaEmpresa(List<ConvenioCobrancaVO> conveniosCobrancaEmpresa) {
        this.conveniosCobrancaEmpresa = conveniosCobrancaEmpresa;
    }

    public List<BoletoVO> getListaBoletosOnlineGerados() {
        if (listaBoletosOnlineGerados == null) {
            listaBoletosOnlineGerados = new ArrayList<>();
        }
        return listaBoletosOnlineGerados;
    }

    public void setListaBoletosOnlineGerados(List<BoletoVO> listaBoletosOnlineGerados) {
        this.listaBoletosOnlineGerados = listaBoletosOnlineGerados;
    }

    public Integer getConvenioCobrancaBoletoSelecionado() {
        if (convenioCobrancaBoletoSelecionado == null) {
            convenioCobrancaBoletoSelecionado = 0;
        }
        return convenioCobrancaBoletoSelecionado;
    }

    public void setConvenioCobrancaBoletoSelecionado(Integer convenioCobrancaBoletoSelecionado) {
        this.convenioCobrancaBoletoSelecionado = convenioCobrancaBoletoSelecionado;
    }

    public List<SelectItem> getSelectItemConvenioCobrancaBoleto() {
        if (selectItemConvenioCobrancaBoleto == null) {
            selectItemConvenioCobrancaBoleto = new ArrayList<>();
        }
        return selectItemConvenioCobrancaBoleto;
    }

    public void setSelectItemConvenioCobrancaBoleto(List<SelectItem> selectItemConvenioCobrancaBoleto) {
        this.selectItemConvenioCobrancaBoleto = selectItemConvenioCobrancaBoleto;
    }

    public boolean isApresentarSelectItemConvenioCobrancaBoleto() {
        return (this.formaPagamentoSelecinada == FormaPagamentoVendaRapidaEnum.BOLETO_BANCARIO_ONLINE.getCodigo()) &&
                getSelectItemConvenioCobrancaBoleto().size() > 1;
    }

    private void montarSelectItemConveniosCobrancaBoletoOnline() {
        this.setSelectItemConvenioCobrancaBoleto(new ArrayList<>());

        Map<Integer, ConvenioCobrancaVO> mapaConv = new HashMap<>();
        for (ConvenioCobrancaVO obj : this.getConveniosCobrancaEmpresa()) {
            if (obj.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO_ONLINE) &&
                    !mapaConv.containsKey(obj.getCodigo())) {
                mapaConv.put(obj.getCodigo(), obj);
            }
        }

        for (Map.Entry<Integer, ConvenioCobrancaVO> entry : mapaConv.entrySet()) {
            ConvenioCobrancaVO conv = entry.getValue();
            this.getSelectItemConvenioCobrancaBoleto().add(new SelectItem(conv.getCodigo(), conv.getDescricao()));
        }

        Ordenacao.ordenarLista(this.getSelectItemConvenioCobrancaBoleto(), "label");

        if (mapaConv.size() == 1) {
            this.setConvenioCobrancaBoletoSelecionado(mapaConv.entrySet().iterator().next().getValue().getCodigo());
        } else {
            this.getSelectItemConvenioCobrancaBoleto().add(0, new SelectItem(0, ""));
        }
    }

    public boolean isEmpresaTemConvenioBoletoOnline() {
        return existeTipoCobranca(TipoCobrancaEnum.BOLETO_ONLINE);
    }

    public boolean isEmpresaTemConvenioDebitoConta() {
        return existeTipoCobranca(TipoCobrancaEnum.EDI_DCO);
    }

    private boolean existeTipoCobranca(TipoCobrancaEnum tipoCobrancaEnum) {
        for (ConvenioCobrancaVO obj : this.getConveniosCobrancaEmpresa()) {
            if (obj.getTipo().getTipoCobranca().equals(tipoCobrancaEnum)) {
                return true;
            }
        }
        return false;
    }

    public void enviarEmailBoleto(ActionEvent evt) {
        try {
            limparMsg();

            String tipoEnvio = (String) JSFUtilities.getFromActionEvent("tipoEnvioBoleto", evt);
            boolean enviarTodos = tipoEnvio.equalsIgnoreCase("todos");

            BoletoVO boletoVO;
            if (enviarTodos) {
                if (UteisValidacao.emptyList(this.getListaBoletosOnlineGerados())) {
                    throw new ConsistirException("Nenhum boleto encontrado.");
                }

                boletoVO = this.getListaBoletosOnlineGerados().get(0);
            } else {
                boletoVO = (BoletoVO) JSFUtilities.getFromActionEvent("boletoDetalhe", evt);
            }

            List<EmailVO> emailsEnviar = getFacade().getEmail().consultarEmails(boletoVO.getPessoaVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UteisValidacao.emptyList(emailsEnviar)) {
                throw new ConsistirException(boletoVO.getPessoaVO().getNome() + " não tem e-mail cadastrado.");
            }

            String[] emails = new String[emailsEnviar.size()];
            int i = 0;
            for (EmailVO emailVO : emailsEnviar) {
                emails[i] = emailVO.getEmail();
                i++;
            }

            if (!enviarTodos || this.getListaBoletosOnlineGerados().size() == 1) {
                getFacade().getBoleto().enviarEmailBoleto(getKey(), boletoVO, emails, true, false);
            } else {
                String linkBoletos = obterLinkBoletos();
                getFacade().getBoleto().enviarEmailBoletoLink(getKey(),
                        linkBoletos, boletoVO.getEmpresaVO().getCodigo(),
                        boletoVO.getPessoaVO().getCodigo(), emails);
            }

            montarSucessoGrowl("E-mail enviado com sucesso, para " + Arrays.toString(emails).replace("[", "").replace("]", ""));
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void imprimirBoletos() {
        try {
            limparMsg();
            setMsgAlert("");
            String linkBoleto = obterLinkBoletos();
            setMsgAlert("window.open('" + linkBoleto + "', '_blank');");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
            setMsgAlert(getMensagemNotificar());
        }
    }

    private String obterLinkBoletos() throws Exception {
        Date inicioG = Calendario.hoje();
        try {
            // Quando o Venda Rápida foi feito, só tinhamos integrado PjBank para boleto online, esse envio foi pensado apenas para ele.
            // Para ajustar os outros que surgiram depois de 2023, vou demorar muito tempo e parece que tem planos para descontinuar o Vendas Rápida
            // Por isso foi combinado com PM se a ação de impressão não for de PjBank, só retornar erro.
            // Se no futuro mudarem de ideia, precisa ajustar nesse metodo para os outros boletos online.
            if (!UteisValidacao.emptyList(this.getListaBoletosOnlineGerados()) &&
                    !this.getListaBoletosOnlineGerados().get(0).getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK)) {
                throw new ConsistirException("Impressão disponível apenas para Pjbank. Para outros tipos de boletos, utilize a opção a tela do aluno.");
            }

            if (this.getListaBoletosOnlineGerados().size() == 1) {
                return this.getListaBoletosOnlineGerados().get(0).getLinkBoleto();
            }

            List<BoletoVO> boletosSelecionados = new ArrayList<>();
            for (BoletoVO boletoVO : this.getListaBoletosOnlineGerados()) {
                if (boletoVO.getTipo().equals(TipoBoletoEnum.PJ_BANK)) {
                    boletosSelecionados.add(boletoVO);
                }
            }

            if (UteisValidacao.emptyList(boletosSelecionados)) {
                throw new ConsistirException("Nenhum boleto selecionado");
            }

            ConvenioCobrancaVO convenioCobrancaVO = getFacade().getConvenioCobranca().consultarPorChavePrimaria(boletosSelecionados.get(0).getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            LinkedHashSet<String> pedidos = BoletoVO.obterListaImpressaoBoleto(boletosSelecionados);
            BoletosManager boletosManager = new BoletosManager(convenioCobrancaVO.getCredencialPJBank(), convenioCobrancaVO.getChavePJBank(), convenioCobrancaVO);
            return boletosManager.getByIds(pedidos);
        } finally {
            Date fimG = Calendario.hoje();
            Uteis.logarDebug("InclusaoVendaRapidaControle | gerarBoletosPjBank | Tempo gasto: " + ((fimG.getTime() - inicioG.getTime()) / 1000) + " segundos");
        }
    }

    public boolean isResponsavelObrigatorio() {
        return responsavelObrigatorio;
    }

    public void setResponsavelObrigatorio(boolean responsavelObrigatorio) {
        this.responsavelObrigatorio = responsavelObrigatorio;
    }

    public String getMsgCarregando() {
        if (msgCarregando == null) {
            msgCarregando = "";
        }
        return msgCarregando;
    }

    public void setMsgCarregando(String msgCarregando) {
        this.msgCarregando = msgCarregando;
    }

    public void ativarPoll() {
        this.setMsgCarregando("");
        this.setPollAtivo(true);
    }

    public void desativarPoll() {
        this.setMsgCarregando("");
        this.setPollAtivo(false);
    }

    public void atualizarMsg() {
        this.setMsgCarregando("");
    }

    public boolean isPollAtivo() {
        return pollAtivo;
    }

    public void setPollAtivo(boolean pollAtivo) {
        this.pollAtivo = pollAtivo;
    }

    public String getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(String responsavel) {
        this.responsavel = responsavel;
    }

    public boolean isApresentarDependente() {
        return apresentarDependente;
    }

    public void setApresentarDependente(boolean apresentarDependente) {
        this.apresentarDependente = apresentarDependente;
    }

    public ClienteVO getClienteDependente() {
        if (clienteDependente == null) {
            clienteDependente = new ClienteVO();
        }
        return clienteDependente;
    }

    public void setClienteDependente(ClienteVO clienteDependente) {
        this.clienteDependente = clienteDependente;
    }

    public boolean isPessoaEstrangeira() {
        return pessoaEstrangeira;
    }

    public void setPessoaEstrangeira(boolean pessoaEstrangeira) {
        this.pessoaEstrangeira = pessoaEstrangeira;
    }

    public String getVigenciaAteAjustadaContratoBase() {
        return vigenciaAteAjustadaContratoBase;
    }

    public void setVigenciaAteAjustadaContratoBase(String vigenciaAteAjustadaContratoBase) {
        this.vigenciaAteAjustadaContratoBase = vigenciaAteAjustadaContratoBase;
    }

    public List<CampanhaCupomDescontoPremioPortadorVO> getListaPremioPortadorCupom() {
        return listaPremioPortadorCupom;
    }

    public void setListaPremioPortadorCupom(List<CampanhaCupomDescontoPremioPortadorVO> listaPremioPortadorCupom) {
        this.listaPremioPortadorCupom = listaPremioPortadorCupom;
    }

    public void consultarCpfSpcManual() {
        try{
            String cpf = responsavelObrigatorio ? getPessoaVO().getCpfMae() : getPessoaVO().getCfp();
            validarCpfExiste();
            if (!cpf.isEmpty()) {
                consultarNoSPC(cpf);
            }
        } catch (Exception e){
            setMsgAlert(e.getMessage());
        }
    }

    public boolean isLancarConcomitante() {
        return lancarConcomitante;
    }

    public void setLancarConcomitante(boolean lancarConcomitante) {
        this.lancarConcomitante = lancarConcomitante;
    }
}
