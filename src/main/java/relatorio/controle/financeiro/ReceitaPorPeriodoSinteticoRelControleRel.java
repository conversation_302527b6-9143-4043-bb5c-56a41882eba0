package relatorio.controle.financeiro;

import java.util.ArrayList;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Vector;
import javax.faces.model.SelectItem;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ContadorTempo;
import negocio.comuns.utilitarias.Uteis;
import org.jfree.data.general.DefaultPieDataset;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.financeiro.ReceitaPorPeriodoSinteticoRelTO;
import relatorio.negocio.jdbc.financeiro.ReceitaPorPeriodoSinteticoRel;

public class ReceitaPorPeriodoSinteticoRelControleRel extends SuperControleRelatorio {

    private ReceitaPorPeriodoSinteticoRel receitaPorPeriodoSinteticoRel;
    private String dataInicioPrm;
    private String dataTerminoPrm;
    protected String enderecoEmpresa;
    protected String cidadeEmpresa;
    private List listaSelectItemTipoFormaPagamento;
    //jsfChart
    private DefaultPieDataset dataSetPizza = new DefaultPieDataset();
    //flex - CHART
    private Hashtable<String, Double[]> data = new Hashtable<String, Double[]>();
    private Vector<String> colors = new Vector<String>();
    private Vector<String> names = new Vector<String>();
    private String dadosGrafico = "";
    private boolean permissaoConsultaTodasEmpresas = false;
    private Integer filtroEmpresa;

    public Hashtable<String, Double[]> getData() {
        return data;
    }

    public Vector<String> getNames() {
        return names;
    }

    public Vector<String> getColors() {
        return colors;
    }

    public DefaultPieDataset getDataSetPizza() {
        return dataSetPizza;
    }

    public ReceitaPorPeriodoSinteticoRelControleRel() throws Exception {
        inicializarDados();
        montarTipoFormaPagamento();
    }
    public void montarListaSelectItemEmpresa() {
        try {
            permissaoConsultaTodasEmpresas = permissao("ConsultarInfoTodasEmpresas");
            if (permissaoConsultaTodasEmpresas) {
                montarListaEmpresasComItemTodas();
            }
            setFiltroEmpresa(getEmpresaLogado().getCodigo());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarTipoFormaPagamento() {
        List objs = new ArrayList();
        // ATENÇAO! Não altere a ordem destes 3 elementos. tela usa o codigo para renderizar um label
        objs.add(new SelectItem(1, "Só Compensado"));
        objs.add(new SelectItem(2, "Vendido e Compensado"));
        objs.add(new SelectItem(3, "Lançamento Caixa"));
        setListaSelectItemTipoFormaPagamento(objs);
    }

    public void inicializarDados() throws Exception {
        setReceitaPorPeriodoSinteticoRel(new ReceitaPorPeriodoSinteticoRel());
        setDataInicioPrm("");
        setDataTerminoPrm("");
        montarListaSelectItemEmpresa();
    }

    public void imprimirPDF() {
        try {
            ContadorTempo.limparCronometro();
            ContadorTempo.iniciarContagem();
            getReceitaPorPeriodoSinteticoRel().validarDados();
            setTipoRelatorio("PDF");
            setListaRelatorio(new ArrayList());
            setDataInicioPrm(Uteis.getData(getReceitaPorPeriodoSinteticoRel().getDataInicio()));
            setDataTerminoPrm(Uteis.getData(getReceitaPorPeriodoSinteticoRel().getDataTermino()));
            // a data termino setada está com hora zerada, por isso adiciona +1 para ficar zero horas do dia seguinte
            List listaRegistro = receitaPorPeriodoSinteticoRel.montarDadosReceitaPorPeriodoSinteticoRelVO(getFiltroEmpresa());
            if (!listaRegistro.isEmpty()) {
                setListaRelatorio(listaRegistro);
            } else {
                throw new Exception("Não foi encontrado nenhum Recibo no período informado.");
            }
            setRelatorio("sim");
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
            imprimirRelatorio();
            notificarRecursoEmpresa(RecursoSistema.RELATORIO_RECEITA, ContadorTempo.encerraContagem(), Calendario.diferencaEmDias(receitaPorPeriodoSinteticoRel.getDataInicio(), receitaPorPeriodoSinteticoRel.getDataTermino()) + 1);
        } catch (Exception e) {
            setRelatorio("nao");
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void montarChart() {
        try {
            receitaPorPeriodoSinteticoRel.setDataInicio(Uteis.obterPrimeiroDiaMes(receitaPorPeriodoSinteticoRel.getDataInicio()));
            receitaPorPeriodoSinteticoRel.setDataTermino(Uteis.obterUltimoDiaMes(receitaPorPeriodoSinteticoRel.getDataInicio()));
            setDataInicioPrm(Uteis.getData(Uteis.obterPrimeiroDiaMes(Calendario.hoje())));
            setDataTerminoPrm(Uteis.getData(Uteis.obterUltimoDiaMes(Calendario.hoje())));
            Map<String, Double> dados = receitaPorPeriodoSinteticoRel.consultarCompensados(getFiltroEmpresa());
            

            Double vCartaoCredito = dados.get("CA") == null ? 0.0 : dados.get("CA");
            Double vCartaoDebito = dados.get("CD") == null ? 0.0 : dados.get("CD");
            Double vCheque = dados.get("CH") == null ? 0.0 : dados.get("CH");
            Double vDinheiro = dados.get("AV") == null ? 0.0 : dados.get("AV");
            Double vOutros = dados.get("BB") == null ? 0.0 : dados.get("BB");
            vOutros += dados.get("BB") == null ? 0.0 : dados.get("BB");
            vOutros += dados.get("CO") == null ? 0.0 : dados.get("CO");
            vOutros += dados.get("CC") == null ? 0.0 : dados.get("CC");

            dadosGrafico = "var chartData2 = [";
            dadosGrafico += "{\"tipo\": \"C. crédito\", \"valor\": \""+Uteis.arredondarForcando2CasasDecimais(vCartaoCredito)+"\", \"color\": \"#F26F04\"}, ";
            dadosGrafico += "{\"tipo\": \"C. débito\", \"valor\": \""+Uteis.arredondarForcando2CasasDecimais(vCartaoDebito)+"\", \"color\": \"#F2EB04\"}, ";
            dadosGrafico += "{\"tipo\": \"Cheque\", \"valor\": \""+Uteis.arredondarForcando2CasasDecimais(vCheque)+"\", \"color\": \"#1A62CF\"}, ";
            dadosGrafico += "{\"tipo\": \"Dinheiro\", \"valor\": \""+Uteis.arredondarForcando2CasasDecimais(vDinheiro)+"\", \"color\": \"#19FC43\"}, ";
            dadosGrafico += "{\"tipo\": \"Outros\", \"valor\": \""+Uteis.arredondarForcando2CasasDecimais(vOutros)+"\", \"color\": \"#C40D87\"}]; ";


            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setRelatorio("nao");
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void getImprimirHTML() {
        try {
            setTipoRelatorio("HTML");
            setListaRelatorio(new ArrayList());
            setDataInicioPrm(Uteis.getData(getReceitaPorPeriodoSinteticoRel().getDataInicio()));
            setDataTerminoPrm(Uteis.getData(getReceitaPorPeriodoSinteticoRel().getDataTermino()));
            List listaRegistro = receitaPorPeriodoSinteticoRel.montarDadosReceitaPorPeriodoSinteticoRelVO(getFiltroEmpresa());
            if (!listaRegistro.isEmpty()) {
                setListaRelatorio(listaRegistro);
            } else {
                throw new Exception("Não foi encontrado nenhum Recibo no período informado.");
            }
            setRelatorio("sim");
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
            imprimirRelatorio();
        } catch (Exception e) {
            setRelatorio("nao");
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void imprimirRelatorio() {
        try {
            String nomeRelatorio = ReceitaPorPeriodoSinteticoRel.getIdEntidadeLocal();
            String titulo = "Receita Por Período Sintético";
            String design = ReceitaPorPeriodoSinteticoRel.getDesignIReportRelatorio();
            if (receitaPorPeriodoSinteticoRel.getFormaPagamento().intValue() != 0) {
                receitaPorPeriodoSinteticoRel.setDescricaoFiltros("Forma de Pagamento:");
                if (receitaPorPeriodoSinteticoRel.getFormaPagamento().intValue() == 1) {
                    receitaPorPeriodoSinteticoRel.setDescricaoFiltros(receitaPorPeriodoSinteticoRel.getDescricaoFiltros() + "Só compensado; ");
                } else if (receitaPorPeriodoSinteticoRel.getFormaPagamento().intValue() == 2) {
                    receitaPorPeriodoSinteticoRel.setDescricaoFiltros(receitaPorPeriodoSinteticoRel.getDescricaoFiltros() + "Data Venda e Compensado; ");
                } else if (receitaPorPeriodoSinteticoRel.getFormaPagamento().intValue() == 3) {
                    receitaPorPeriodoSinteticoRel.setDescricaoFiltros(receitaPorPeriodoSinteticoRel.getDescricaoFiltros() + "Lançamento Caixa; ");
                }
            }
            receitaPorPeriodoSinteticoRel.setDescricaoFiltros(receitaPorPeriodoSinteticoRel.getDescricaoFiltros() + "Empresa: " + getNomeEmpresaSelecionada(getFiltroEmpresa()) + "; ");
            if (receitaPorPeriodoSinteticoRel.getVisaoDinheiro()
                    || receitaPorPeriodoSinteticoRel.getVisaoChequeVista()
                    || receitaPorPeriodoSinteticoRel.getVisaoChequePrazo()
                    || receitaPorPeriodoSinteticoRel.getVisaoCartaoCredito()
                    || receitaPorPeriodoSinteticoRel.getVisaoCartaoDebito()
                    || receitaPorPeriodoSinteticoRel.getVisaoOutros()
                    || receitaPorPeriodoSinteticoRel.getVisaoBoleto()) {
                receitaPorPeriodoSinteticoRel.setDescricaoFiltros(receitaPorPeriodoSinteticoRel.getDescricaoFiltros() + "Visão:");
            }
            if (receitaPorPeriodoSinteticoRel.getVisaoDinheiro()) {
                receitaPorPeriodoSinteticoRel.setDescricaoFiltros(receitaPorPeriodoSinteticoRel.getDescricaoFiltros() + " Dinheiro");
            }
            if (receitaPorPeriodoSinteticoRel.getVisaoBoleto()) {
                receitaPorPeriodoSinteticoRel.setDescricaoFiltros(receitaPorPeriodoSinteticoRel.getDescricaoFiltros() + " Boleto");
            }
            if (receitaPorPeriodoSinteticoRel.getVisaoChequeVista()) {
                receitaPorPeriodoSinteticoRel.setDescricaoFiltros(receitaPorPeriodoSinteticoRel.getDescricaoFiltros() + " Ch. à Vista");
            }
            if (receitaPorPeriodoSinteticoRel.getVisaoChequePrazo()) {
                receitaPorPeriodoSinteticoRel.setDescricaoFiltros(receitaPorPeriodoSinteticoRel.getDescricaoFiltros() + " Ch. à Prazo");
            }
            if (receitaPorPeriodoSinteticoRel.getVisaoCartaoCredito()) {
                receitaPorPeriodoSinteticoRel.setDescricaoFiltros(receitaPorPeriodoSinteticoRel.getDescricaoFiltros() + " Cartão de Crédito");
            }
            if (receitaPorPeriodoSinteticoRel.getVisaoCartaoDebito()) {
                receitaPorPeriodoSinteticoRel.setDescricaoFiltros(receitaPorPeriodoSinteticoRel.getDescricaoFiltros() + " Cartão de Débito");
            }
            if (receitaPorPeriodoSinteticoRel.getVisaoOutros()) {
                receitaPorPeriodoSinteticoRel.setDescricaoFiltros(receitaPorPeriodoSinteticoRel.getDescricaoFiltros() + " Outros");
            }

            apresentarRelatorioObjetos(nomeRelatorio, titulo, getNomeEmpresaSelecionada(getFiltroEmpresa()), "", "", getTipoRelatorio(),
                    "/" + ReceitaPorPeriodoSinteticoRel.getIdEntidadeLocal() + "/registros", design, getUsuarioLogado().getNome(),
                    receitaPorPeriodoSinteticoRel.getDescricaoFiltros(), getListaRelatorio());


            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public List getListaSelectItemOrdenacao() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("NR", "Número do Recibo"));
        objs.add(new SelectItem("NA", "Nome do Aluno"));
        return objs;
    }

    public String getDataInicioPrm() {
        return dataInicioPrm;
    }

    public void setDataInicioPrm(String dataInicioPrm) {
        this.dataInicioPrm = dataInicioPrm;
    }

    public String getDataTerminoPrm() {
        return dataTerminoPrm;
    }

    public void setDataTerminoPrm(String dataTerminoPrm) {
        this.dataTerminoPrm = dataTerminoPrm;
    }

    public String getCidadeEmpresa() {
        return cidadeEmpresa;
    }

    public void setCidadeEmpresa(String cidadeEmpresa) {
        this.cidadeEmpresa = cidadeEmpresa;
    }

    public String getEnderecoEmpresa() {
        return enderecoEmpresa;
    }

    public void setEnderecoEmpresa(String enderecoEmpresa) {
        this.enderecoEmpresa = enderecoEmpresa;
    }

    public ReceitaPorPeriodoSinteticoRel getReceitaPorPeriodoSinteticoRel() {
        return receitaPorPeriodoSinteticoRel;
    }

    public void setReceitaPorPeriodoSinteticoRel(ReceitaPorPeriodoSinteticoRel receitaPorPeriodoSinteticoRel) {
        this.receitaPorPeriodoSinteticoRel = receitaPorPeriodoSinteticoRel;
    }

    /**
     * @return the listaSelectItemTipoFormaPagamento
     */
    public List getListaSelectItemTipoFormaPagamento() {
        return listaSelectItemTipoFormaPagamento;
    }

    /**
     * @param listaSelectItemTipoFormaPagamento the listaSelectItemTipoFormaPagamento to set
     */
    public void setListaSelectItemTipoFormaPagamento(List listaSelectItemTipoFormaPagamento) {
        this.listaSelectItemTipoFormaPagamento = listaSelectItemTipoFormaPagamento;
    }

    public void novo() throws Exception {
        notificarRecursoEmpresa(RecursoSistema.fromDescricao(FuncionalidadeSistemaEnum.RECEITA_PERIODO.toString()));

        inicializarDados();
        return;
    }

    public void vazio() {
        
    }

    public String getDadosGrafico() {
        return dadosGrafico;
    }

    public void setDadosGrafico(String dadosGrafico) {
        this.dadosGrafico = dadosGrafico;
    }

    public boolean isPermissaoConsultaTodasEmpresas() {
        return permissaoConsultaTodasEmpresas;
    }

    public void setPermissaoConsultaTodasEmpresas(boolean permissaoConsultaTodasEmpresas) {
        this.permissaoConsultaTodasEmpresas = permissaoConsultaTodasEmpresas;
    }

    public Integer getFiltroEmpresa() {
        if (filtroEmpresa == null) {
            filtroEmpresa = 0;
        }
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(Integer filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }
}
