package relatorio.controle.financeiro;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.TotalizadorRemessaTO;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.basico.Empresa;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.jdbc.financeiro.SaldoContaCorrenteRel;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/*
 * <AUTHOR> 27/10/2017
 */
public class ResultadoConvenioCobrancaControleRel extends SuperControleRelatorio {

    private Date dataInicial;
    private Date dataFinal;
    private List<ConvenioCobrancaVO> listaConvenioCobranca;
    private List<SelectItem> listaEmpresas;
    private Integer empresaSelecionada;
    private List<TotalizadorRemessaTO> totalizadorConvenio;

    public Integer[] ARRAY_TIPO_CONVENIOS = new Integer[]{
            TipoConvenioCobrancaEnum.DCC.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_GETNET.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_BIN.getCodigo(),
            //transação online
            TipoConvenioCobrancaEnum.DCC_VINDI.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_MAXIPAGO.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_E_REDE.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_STONE_ONLINE.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_MUNDIPAGG.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_PAGAR_ME.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_PAGBANK.getCodigo(),
            TipoConvenioCobrancaEnum.DCC_STRIPE.getCodigo()};

    public ResultadoConvenioCobrancaControleRel() throws Exception {
        inicializarDados();
    }

    private void inicializarDados() throws Exception {
        setDataInicial(Calendario.hoje());
        setDataFinal(Calendario.hoje());
        montarListaEmpresas();
        montarListaConvenio();
        obterUsuarioLogado();

    }

    public void montarListaEmpresas() throws Exception {
        listaEmpresas = obterListaEmpresas(getFacade().getEmpresa().consultarPorNome("", true, false, Uteis.NIVELMONTARDADOS_MINIMOS));
        setEmpresaSelecionada(getEmpresaLogado().getCodigo());
    }

    public void montarListaConvenio() {
        try {
            setListaConvenioCobranca(new ArrayList<ConvenioCobrancaVO>());
            setListaConvenioCobranca(getFacade().getConvenioCobranca().consultarPorEmpresaESituacao(getEmpresaSelecionada(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, ARRAY_TIPO_CONVENIOS, SituacaoConvenioCobranca.ATIVO));
        } catch (Exception e) {
            setListaConvenioCobranca(new ArrayList<ConvenioCobrancaVO>());
        }
    }

    public Date getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public List<SelectItem> getListaEmpresas() {
        return listaEmpresas;
    }

    public void setListaEmpresas(List<SelectItem> listaEmpresas) {
        this.listaEmpresas = listaEmpresas;
    }

    public Integer getEmpresaSelecionada() {
        if (empresaSelecionada == null) {
            empresaSelecionada = 0;
        }
        return empresaSelecionada;
    }

    public void setEmpresaSelecionada(Integer empresaSelecionada) {
        this.empresaSelecionada = empresaSelecionada;
    }

    public List<ConvenioCobrancaVO> getListaConvenioCobranca() {
        if (listaConvenioCobranca == null) {
            listaConvenioCobranca = new ArrayList<ConvenioCobrancaVO>();
        }
        return listaConvenioCobranca;
    }

    public void setListaConvenioCobranca(List<ConvenioCobrancaVO> listaConvenioCobranca) {
        this.listaConvenioCobranca = listaConvenioCobranca;
    }

    public List<TotalizadorRemessaTO> getTotalizadorConvenio() {
        if (totalizadorConvenio == null) {
            totalizadorConvenio = new ArrayList<TotalizadorRemessaTO>();
        }
        return totalizadorConvenio;
    }

    public void setTotalizadorConvenio(List<TotalizadorRemessaTO> totalizadorConvenio) {
        this.totalizadorConvenio = totalizadorConvenio;
    }

    public String consultarRelatorio() {
        try {
            limparMsg();
            validarDados();
            setTotalizadorConvenio(new ArrayList<TotalizadorRemessaTO>());

            for (ConvenioCobrancaVO obj : listaConveniosSelecionados()) {

//                ConvenioCobrancaVO convenioCobrancaVO = getFacade().getConvenioCobranca().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
//
                TotalizadorRemessaTO total = new TotalizadorRemessaTO();
//                total.setLabel(convenioCobrancaVO.getDescricao());
//
//                if (convenioCobrancaVO.getTipo().isTransacaoOnline()) {
//                    getFacade().getZWFacade().getTransacao().totalizadorPorConvenio(total, getEmpresaSelecionada(), getDataInicial(), getDataFinal(), convenioCobrancaVO.getTipo().getTipoRemessa().getTipoTransacao());
//                } else {
//                    getFacade().getZWFacade().getRemessaItem().totalizadorPorConvenio(total, getEmpresaSelecionada(), getDataInicial(), getDataFinal(), convenioCobrancaVO.getCodigo());
//                }

                getFacade().getMovParcelaTentativaConvenio().totalizadorPorConvenio(total, getEmpresaSelecionada(), getDataInicial(), getDataFinal(), obj.getCodigo().toString());
                getTotalizadorConvenio().add(total);
            }
            return "relatorio";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consulta";
        }
    }

    public String voltar() {
        return "consulta";
    }

    private List<ConvenioCobrancaVO> listaConveniosSelecionados() {
        List<ConvenioCobrancaVO> lista = new ArrayList<ConvenioCobrancaVO>();

        for (ConvenioCobrancaVO obj : getListaConvenioCobranca()) {
            if (obj.isSelecionado()) {
                lista.add(obj);
            }
        }

        if (UteisValidacao.emptyList(lista)) {
            return getListaConvenioCobranca();
        }
        return lista;
    }

    public void validarDados() throws Exception {
        if (getEmpresaSelecionada() == 0) {
            throw new Exception("Nenhum empresa selecionada");
        }

        if (getDataInicial() == null) {
            throw new Exception("Informe a Data Inicial");
        }

        if (getDataFinal() == null) {
            throw new Exception("Informe a Data Final");
        }

        if (UteisValidacao.emptyList(getListaConvenioCobranca())) {
            throw new Exception("Não existe nenhum convênio cadastrado");
        }


//        String nome = "Luiz";
//        Double peso = 77.0;
//        String sexo = "M";
//
//
//        String teste = nome + "|" + peso + "|" + sexo;
//
//        List<String> listaGeral = new ArrayList<String>();
//
//        listaGeral.add(teste);
//
//        for(String obj : listaGeral) {
//
//            String[] dados = obj.split("\\|");
//
//            String aanome = dados[0]; //nome
//            String aapeso = dados[1]; //peso
//            String aasexo = dados[2]; //sexo
//
//
//        }






















    }


}
