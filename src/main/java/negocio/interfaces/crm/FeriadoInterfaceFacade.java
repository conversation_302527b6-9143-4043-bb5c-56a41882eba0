package negocio.interfaces.crm;

import java.util.Date;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.FeriadoVO;
import java.util.List;
import java.util.Set;

import negocio.comuns.crm.AberturaMetaVO;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface FeriadoInterfaceFacade extends SuperInterface {

    public FeriadoVO novo() throws Exception;

    public void incluir(FeriadoVO obj) throws Exception;

    public void alterar(FeriadoVO obj) throws Exception;

    public void excluir(FeriadoVO obj) throws Exception;

    public FeriadoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDia(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNomeCidade(String valorConsulta, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public List consultarPorNomeEstado(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorNomePais(String valorConsulta, int nivelMontarDados) throws Exception;

    public List<Date> consultarPorPeriodoEmpresa(Date inicio, Date fim, EmpresaVO empresaVO) throws Exception;

    public Set<Date> consultarPorPeriodoEmpresaAulaCheia(Date inicio, Date fim, EmpresaVO empresaVO) throws Exception;

    public Boolean validarFeriadoPorEmpresaParaCalculoAberturaMeta(EmpresaVO empresa, Date prmIni) throws Exception;

    public Boolean consultarPorDiaAndFeriado(Date prmIni, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<Date> consultarDiasFeriados(Date inicio, Date fim, EmpresaVO empresa) throws Exception;

    public String consultarJSON() throws Exception;
}
