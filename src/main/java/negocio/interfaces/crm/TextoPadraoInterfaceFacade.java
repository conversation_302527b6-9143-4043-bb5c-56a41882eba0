package negocio.interfaces.crm;

import negocio.comuns.crm.TextoPadraoVO;
import negocio.interfaces.basico.SuperInterface;

import javax.faces.model.SelectItem;
import java.util.List;

/**
 * Created by glauco on 15/01/14.
 */
public interface TextoPadraoInterfaceFacade extends SuperInterface {

    public String consultarJSON(Integer empresa) throws Exception;

    public void incluir(TextoPadraoVO textoPadraoVO) throws Exception;

    public void alterar(TextoPadraoVO textoPadraoVO) throws Exception;

    public void excluir(TextoPadraoVO textoPadraoVO) throws Exception;

    public List<TextoPadraoVO> consultarTodos(int nivelMontarDados) throws Exception;

    public TextoPadraoVO consultarPorChavePrimaria(Integer codigoConsulta, int nivelMontarDados) throws Exception;

    public TextoPadraoVO consultarPorFase(Integer faseCRM, Integer codEmpresa, int nivelMontarDados) throws Exception;

    public List<TextoPadraoVO> consultarPorFaseETipoContato(Integer faseCRM, String tipoContato, Integer codEmpresa, int nivelMontarDados) throws Exception;

    public List<SelectItem> consultarPorFaseVariosParaMontarCombo(Integer faseCRM, Integer codEmpresa, int nivelMontarDados) throws Exception;

    public List<SelectItem> consultarPorFase(Integer faseCRM, Integer codEmpresa) throws Exception;

}
