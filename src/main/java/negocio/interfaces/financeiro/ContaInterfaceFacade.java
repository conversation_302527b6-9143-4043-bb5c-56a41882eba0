package negocio.interfaces.financeiro;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.financeiro.ComportamentoConta;
import negocio.comuns.financeiro.ContaVO;
import negocio.interfaces.basico.SuperInterface;
import servicos.pactobank.dto.ExtratoMovimentoZWDTO;

public interface ContaInterfaceFacade extends SuperInterface {

    public void alterar(ContaVO obj) throws Exception;

    public void excluir(ContaVO obj) throws Exception;

    public void incluir(ContaVO obj) throws Exception;

    public String consultarJSON() throws Exception;

    public List consultar(ContaVO filtro, boolean somenteAtivas, int nivelMontarDados, String chaveZW) throws Exception;

    public List<ContaVO> consultarTiposContas(String tipos, int nivelMontarDados) throws Exception;

    public ContaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception;

    public ContaVO consultarPorDescricao(String descricao, int nivelMontarDados) throws Exception;

    public List<ContaVO> consultarContasNaoVinculadasCaixaEmAberto(Integer empresa, int nivelMontarDados) throws Exception;

    public Double saldoAte(int codigoConta, Date ate) throws Exception;

    public Double saldoAte(Integer codigoConta, Date ate, boolean previsto, Integer empresa, String contas) throws Exception;

    public ContaVO consultarPorCheque(int codigoCheque) throws Exception;

    public ContaVO consultarPorLote(int codigoLote) throws Exception;

    public ComportamentoConta getComportamento(int codigoConta) throws Exception;

    public List<ContaVO> consultarContasParaCaixa(Integer empresa, int nivelMontarDados) throws Exception;

    public List<ContaVO> consultarContasMovimentadas(Date inicio, Date fim, Integer empresa, String contas) throws Exception;

    public ContaVO consultarOuCriarContaBancoPadrao(EmpresaVO empresa) throws Exception;

    public List<GenericoTO> consultarGenerico(Date data, Integer empresa) throws Exception;

    public List<ContaVO> consultarContasBI(Integer empresa) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException;

    public List<ContaVO> consultarContasPorMovContas(String movContas) throws Exception;

    public List<ContaVO> consultarContasCaixaAberto(int empresa,int caixaAberto, boolean somenteAtivos) throws Exception;

    public List<ContaVO> consultarContasSimples(Integer empresa, Boolean somenteAtivas) throws Exception;

    public boolean existeConta(ComportamentoConta comportamento) throws Exception;

    public List<ExtratoMovimentoZWDTO> consultarExtratoContaStoneOpenBank(String chaveZW, Integer empresaZW, Date inicio, Date fim, int limit, int pageAnterior, int pagePosterior);

    public void alterarSituacaoPorCodigoContrato(Integer codigoContrato) throws Exception;

}
