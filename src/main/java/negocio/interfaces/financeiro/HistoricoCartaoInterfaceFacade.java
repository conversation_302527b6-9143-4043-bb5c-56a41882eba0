package negocio.interfaces.financeiro;

/**
 * Created by <PERSON> on 25/05/2015.
 */

import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import negocio.comuns.financeiro.CartaoCreditoTO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.HistoricoCartaoVO;
import negocio.comuns.financeiro.LoteVO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.ResultSet;
import java.util.List;

public interface HistoricoCartaoInterfaceFacade extends SuperInterface {

    void incluir(HistoricoCartaoVO historico) throws Exception;

    void incluir(HistoricoCartaoVO historico, boolean controlarTransacao) throws Exception;

    List<HistoricoCartaoVO> consultarPorCartaoComposicao(String codigos) throws Exception;

    public void inicializarHistorico(CartaoCreditoVO cartao, int movConta, LoteVO lote) throws Exception;

    public void getContaLoteCartao(CartaoCreditoTO cartao, boolean datafimisnull) throws Exception;

    public void getCodigoLoteCartao(CartaoCreditoVO cartao, boolean datafimisnull) throws Exception;

    public boolean cartaoJaSaiuLote(String codigosComposicao, Integer codigoLote) throws Exception;

    public List<HistoricoCartaoVO> montarDadosConsulta(ResultSet dados, int nivelMontarDados) throws Exception;

}
