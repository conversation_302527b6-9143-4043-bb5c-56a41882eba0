/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.financeiro;

import java.util.Date;
import java.util.List;
import negocio.comuns.financeiro.DadosGerencialPmgVO;
import negocio.comuns.financeiro.enumerador.IndicadoresDadosGerencialEnum;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface DadosGerencialPmgInterfaceFacade extends SuperInterface{
    
    void incluirSemCommit(DadosGerencialPmgVO obj) throws Exception;

    void alterar(DadosGerencialPmgVO obj) throws Exception;

    void excluir(Integer codigo) throws Exception;

    List<DadosGerencialPmgVO> consultarDados(Date inicioPeriodo, Date finalPeriodo, String identificador, String tipo, Integer empresa) throws Exception;
    
    void gerarDadosPMG(Integer empresa, final String chave, final Date data, final List<IndicadoresDadosGerencialEnum> indicadores) throws Exception;

    void gerarDadosDiaPMG(Integer empresa, final String chave, final Date dia, final List<IndicadoresDadosGerencialEnum> indicadores) throws Exception;

    String obterValorVendasDia(Integer empresa, final String chave, final Date dataConsultar, final String tipoDado) throws Exception;
}
