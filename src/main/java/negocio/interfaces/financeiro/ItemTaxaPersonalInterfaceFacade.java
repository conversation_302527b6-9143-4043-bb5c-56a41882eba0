
package negocio.interfaces.financeiro;

import java.util.List;
import negocio.comuns.financeiro.ItemTaxaPersonalVO;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface ItemTaxaPersonalInterfaceFacade extends SuperInterface {

    public void incluir(ItemTaxaPersonalVO obj, int controle) throws Exception;
    public void incluirSemCommit(ItemTaxaPersonalVO obj, int controle) throws Exception;

    public void excluir(int codigo) throws Exception;
    public void excluirSemCommit(int codigo) throws Exception;

    public ItemTaxaPersonalVO consultarPorChavePrimaria(int codigoPrm, int nivelMontarDados) throws Exception;
    public List<ItemTaxaPersonalVO> consultarPorControle(int controle, int nivelMontarDados) throws Exception;

    public void alterarItemTaxaPersonal(int codigoMovProduto, int codigoItemTaxaPersonal) throws Exception;
}
