/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.basico;

import org.json.JSONArray;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import negocio.comuns.basico.BrindeVO;
import negocio.comuns.basico.EmpresaVO;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;

/**
 *
 * <AUTHOR>
 */
public interface BrindeInterfaceFacade extends SuperInterface{
    
    public BrindeVO novo() throws Exception;
    
    public void incluir(BrindeVO obj) throws Exception;
    
    public void alterar(BrindeVO obj) throws Exception;
    
    public void excluir(BrindeVO obj) throws Exception;
    
    public List<BrindeVO> consultarTodosBrindes(Integer empresa) throws Exception;
    
    public List<BrindeVO> consultarTodosBrindes(Integer empresa,int nivelMontarDados) throws Exception;
    
    public void alterarSemCommit(BrindeVO obj) throws Exception;
    
    public String consultarJSON(Integer empresa) throws Exception;
    
    public BrindeVO consultarPorChavePrimaria(Integer codigo)throws Exception;
    
    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int empresa) throws SQLException;
     
    public List<BrindeVO> consultarBrindeDisponiveisPorPonto(Double ponto,Integer empresa)throws Exception;
    
    public String consultarBrindeDisponiveisPorPontoJson(Integer ponto,Integer empresa,String mostrarTodos) throws Exception;
    
    public List<BrindeVO> consultarBrindesAtivos(Integer empresa,int nivelMontarDados) throws Exception;
    
    public JSONArray consultarListaBrindeAtivoTreino(Integer empresa) throws Exception;

    boolean existeBrindeParaPlano(Integer codigoPlano) throws Exception;

    List<BrindeVO> consultarTotalizadorBiBrindes(Date dataInicial, Date dataFinal, EmpresaVO empresa, ListaPaginadaTO paginadorListaBrindesBi) throws Exception;
}
