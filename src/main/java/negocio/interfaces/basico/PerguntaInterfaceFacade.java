package negocio.interfaces.basico;

import negocio.comuns.basico.PerguntaVO;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface PerguntaInterfaceFacade extends SuperInterface {


    public PerguntaVO novo() throws Exception;

    public void incluir(PerguntaVO obj) throws Exception;

    public void alterar(PerguntaVO obj) throws Exception;

    public void excluir(PerguntaVO obj) throws Exception;

    public PerguntaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public PerguntaVO consultarPorCodigoExato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorTipoPergunta(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void incluir(PerguntaVO obj, boolean centralEventos) throws Exception;

    public void incluirSemCommit(PerguntaVO obj) throws Exception;

    public void alterar(PerguntaVO obj, boolean centralEventos) throws Exception;

    public void alterarSemCommit(PerguntaVO obj) throws Exception ;

    public void excluir(PerguntaVO obj, boolean centralEventos) throws Exception;

    public int consultarPorDescricao(String valorConsulta) throws Exception;

    public String consultarJSON() throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws Exception;

}