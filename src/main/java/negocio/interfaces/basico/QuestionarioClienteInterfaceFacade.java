package negocio.interfaces.basico;

import negocio.comuns.basico.QuestionarioClienteVO;
import negocio.comuns.basico.enumerador.TipoBVEnum;
import relatorio.negocio.comuns.basico.ICVTO;

import java.sql.ResultSet;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface QuestionarioClienteInterfaceFacade extends SuperInterface {

    public QuestionarioClienteVO novo() throws Exception;

    public void incluir(QuestionarioClienteVO obj, Boolean validarQuestionario) throws Exception;

    public void alterar(QuestionarioClienteVO obj, Boolean validarQuestionario) throws Exception;

    public void excluir(QuestionarioClienteVO obj) throws Exception;

    public QuestionarioClienteVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public QuestionarioClienteVO consultarPorCodigoQuestionario(QuestionarioClienteVO obj, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoQuestionario(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorMatriculaCliente(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoCliente(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoClienteTipoQuestionario(Integer valorConsulta, String tipoQuestionario, int nivelMontarDados) throws Exception;

    public ResultSet consultarPorCodigoClienteResultset(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorData(Date prmIni, Date prmFim, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public Integer consultaQuantidadeQuestionarioPorDataEmpresa(Date prmIni, Date prmFim, Integer empresa, boolean controlarAcesso) throws Exception;

    public Integer consultaQuantidadeQuestionarioPorDataEmpresaPorColaborador(Date prmIni, Date prmFim, String codigoColaboradores, Integer empresa, List<TipoBVEnum> listaTiposBV, boolean controlarAcesso) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void incluirSemComit(QuestionarioClienteVO obj, Boolean validarQuestionario, boolean processo) throws Exception;

    public void incluirSemComit(QuestionarioClienteVO obj) throws Exception ;

    public void alterarSemCommit(QuestionarioClienteVO obj, Boolean validarQuestionario) throws Exception;

    public void alterarSemCommit(QuestionarioClienteVO obj) throws Exception ;

    public QuestionarioClienteVO consultarUltimoBVCliente(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List<QuestionarioClienteVO> consultaQuestionarioPorDataEmpresaPorColaborador(Date prmIni, Date prmFim, String codigoColaboradores, List<TipoBVEnum> listaTiposBVs,
                                                                                        Integer empresa, boolean controlarAcesso, int nivelMontarDados, boolean bolsa,
                                                                                        boolean desconsiderarGympass, String listaOrigemSistema, String listaEvento) throws Exception;

    public Integer contarQtdQuestionarioVisitantesPorDataEmpresa(Date dataInicial, Date dataFinal, String horarioInicial, String horarioFinal, Integer empresa) throws Exception;
    
    
    public Map<Integer, ICVTO> gerarICVResumido(Integer empresa, Date data) throws Exception;
    
    public Map<Integer,Integer> consultaMapaQuestionarioPorDataEmpresaPorColaborador(Date prmIni,
                                                                                     Date prmFim,
                                                                                     String codigoColaboradores,
                                                                                     Integer empresa,
                                                                                     List<TipoBVEnum> listaTiposBV,
                                                                                     boolean controlarAcesso) throws Exception;

    public Map<Integer,Integer> consultaMapaQuestionarioPorDataEmpresaPorColaborador(Date prmIni,
                                                                                     Date prmFim,
                                                                                     String codigoColaboradores,
                                                                                     Integer empresa,
                                                                                     List<TipoBVEnum> listaTiposBV,
                                                                                     boolean controlarAcesso,
                                                                                     boolean incluirBolsistas, boolean desconsiderarGympass) throws Exception;

    List consultarPorClienteQuestionario(Integer cliente, Integer questionario, int nivelMontarDados) throws Exception;
}
