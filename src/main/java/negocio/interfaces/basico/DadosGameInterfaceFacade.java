/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.basico;

import br.com.pactosolucoes.comuns.json.DadosGameJSON;
import br.com.pactosolucoes.comuns.json.SimplesJSON;
import org.json.JSONArray;
import java.util.List;
import negocio.comuns.basico.DadosGameVO;

/**
 *
 * <AUTHOR>
 */
public interface DadosGameInterfaceFacade extends SuperInterface{
    
    public List<DadosGameJSON> montarDadosGame(String mesAno, Integer nrMesesAnteriores, Integer empresa) throws Exception;
    public JSONArray obterConsultores(String mesAno, Integer nrMesesAnteriores, Integer empresa) throws Exception;
    public JSONArray analiseVendasPorDuracao(String mesAno, Integer empresa) throws Exception;
    public JSONArray obterVencimentos(String mesAno, Integer empresa) throws Exception;
    public void incluir(DadosGameVO dados) throws Exception;
    public void salvarConfigs(Integer empresa, String configs) throws Exception;
    public String obterConfigs(Integer empresa) throws Exception;
    public JSONArray contratosPorDuracao(Integer empresa) throws Exception;
    public JSONArray obterFaturamentoDuracao(String mesAno, Integer nrMesesAnteriores, Integer empresa) throws Exception;
    public JSONArray obterMetasCRM(String mesAno, Integer nrMesesAnteriores, Integer empresa) throws Exception;
    public List<SimplesJSON> operacoesExcecoes(Integer empresa) throws Exception;
}
