package negocio.interfaces.contrato;

import negocio.comuns.contrato.ConvenioDescontoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface ConvenioDescontoInterfaceFacade extends SuperInterface {


    public ConvenioDescontoVO novo() throws Exception;

    public void incluir(ConvenioDescontoVO obj) throws Exception;

    public void alterar(ConvenioDescontoVO obj) throws Exception;

    public void excluir(ConvenioDescontoVO obj) throws Exception;

    public ConvenioDescontoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List<ConvenioDescontoVO> consultarPorCodigo(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDataAssinatura(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDataInicioVigencia(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorConvenioVigente(Date data, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception ;

    public List consultarPorDataFinalVigencia(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescontoParcela(Double valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorResponsavelAutorizacao(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDataAutorizacao(Date prmIni, Date prmFim, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void incluirSemCommit(ConvenioDescontoVO obj) throws Exception;

    public String consultarJSON(Integer empresa) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws Exception;
}