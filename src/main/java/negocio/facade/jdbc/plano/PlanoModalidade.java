package negocio.facade.jdbc.plano;

import negocio.comuns.plano.ModalidadeVO;
import java.util.Iterator;
import negocio.comuns.plano.PlanoModalidadeVO;
import negocio.facade.jdbc.arquitetura.*;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>PlanoModalidadeVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>PlanoModalidadeVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see PlanoModalidadeVO
 * @see SuperEntidade
 * @see Plano
 */
public class PlanoModalidade extends SuperEntidade {    

    public PlanoModalidade() throws Exception {
        super();
        setIdEntidade("Plano");
    }

    public PlanoModalidade(Connection con) throws Exception {
    	super(con);
        setIdEntidade("Plano");
	}

	/**
     * Operação responsável por retornar um novo objeto da classe <code>PlanoModalidadeVO</code>.
     */
    public PlanoModalidadeVO novo() throws Exception {
        incluir(getIdEntidade());
        return new PlanoModalidadeVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>PlanoModalidadeVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>PlanoModalidadeVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(PlanoModalidadeVO obj) throws Exception {
        PlanoModalidadeVO.validarDados(obj);
        // PlanoModalidade.incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO PlanoModalidade( plano, modalidade, listaVezesSemana) VALUES ( ?, ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getPlano() != 0) {
            sqlInserir.setInt(1, obj.getPlano());
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getModalidade().getCodigo() != 0) {
            sqlInserir.setInt(2, obj.getModalidade().getCodigo());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.setString(3, obj.getListaVezesSemana());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        PlanoModalidadeVezesSemana planoModalidadeVezesSemanaDAO = new PlanoModalidadeVezesSemana(con);
        planoModalidadeVezesSemanaDAO.incluirPlanoModalidadeVezesSemana(obj.getCodigo(), obj.getPlanoModalidadeVezesSemanaVOs());
        planoModalidadeVezesSemanaDAO = null;
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>PlanoModalidadeVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>PlanoModalidadeVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(PlanoModalidadeVO obj) throws Exception {
        PlanoModalidadeVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE PlanoModalidade set plano=?, modalidade=?, listaVezesSemana=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getPlano() != 0) {
            sqlAlterar.setInt(1, obj.getPlano());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        if (obj.getModalidade().getCodigo() != 0) {
            sqlAlterar.setInt(2, obj.getModalidade().getCodigo());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.setString(3, obj.getListaVezesSemana());
        sqlAlterar.setInt(4, obj.getCodigo());
        sqlAlterar.execute();
        PlanoModalidadeVezesSemana planoModalidadeVezesSemanaDAO = new PlanoModalidadeVezesSemana(con);
        planoModalidadeVezesSemanaDAO.alterarPlanoModalidadeVezesSemana(obj.getCodigo(), obj.getPlanoModalidadeVezesSemanaVOs());
        planoModalidadeVezesSemanaDAO = null;
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>PlanoModalidadeVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>PlanoModalidadeVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(PlanoModalidadeVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM PlanoModalidade WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
        getFacade().getPlanoModalidadeVezesSemana().excluirPlanoModalidadeVezesSemana(obj.getCodigo());
    }

    /**
     * Responsável por realizar uma consulta de <code>PlanoModalidade</code> através do valor do atributo 
     * <code>nome</code> da classe <code>Modalidade</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>PlanoModalidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomeModalidade(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT PlanoModalidade.* FROM PlanoModalidade, Modalidade WHERE PlanoModalidade.modalidade = Modalidade.codigo and upper( Modalidade.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Modalidade.nome";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    /**
     * Responsável por realizar uma consulta de <code>PlanoModalidade</code> através do valor do atributo 
     * <code>descricao</code> da classe <code>Plano</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>PlanoModalidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoPlano(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT PlanoModalidade.* FROM PlanoModalidade, Plano WHERE PlanoModalidade.plano = Plano.codigo and upper( Plano.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Plano.descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    /**
     * Responsável por realizar uma consulta de <code>PlanoModalidade</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PlanoModalidadeVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM PlanoModalidade WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>PlanoModalidadeVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            PlanoModalidadeVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>PlanoModalidadeVO</code>.
     * @return  O objeto da classe <code>PlanoModalidadeVO</code> com os dados devidamente montados.
     */
    public static PlanoModalidadeVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        PlanoModalidadeVO obj = new PlanoModalidadeVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setListaVezesSemana(dadosSQL.getString("listaVezesSemana"));
        obj.setPlano(dadosSQL.getInt("plano"));
        obj.getModalidade().setCodigo(new Integer(dadosSQL.getInt("modalidade")));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            montarDadosModalidade(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            montarDadosModalidade(obj, Uteis.NIVELMONTARDADOS_TODOS, con);
            return obj;
        }
        PlanoModalidadeVezesSemana planoModalidadeVezesSemana = new PlanoModalidadeVezesSemana(con);
        obj.setPlanoModalidadeVezesSemanaVOs(planoModalidadeVezesSemana.consultarPlanoVezesSemanaVOs(obj.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS));
        planoModalidadeVezesSemana = null;
        montarDadosModalidade(obj, Uteis.NIVELMONTARDADOS_TODOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>ModalidadeVO</code> relacionado ao objeto <code>PlanoModalidadeVO</code>.
     * Faz uso da chave primária da classe <code>ModalidadeVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosModalidade(PlanoModalidadeVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getModalidade().getCodigo() == 0) {
            obj.setModalidade(new ModalidadeVO());
            return;
        }
        Modalidade modalidade = new Modalidade(con);
        obj.setModalidade(modalidade.consultarPorChavePrimaria(obj.getModalidade().getCodigo(), nivelMontarDados));
        modalidade = null;
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>PlanoModalidadeVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>PlanoModalidade</code>.
     * @param plano campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirPlanoModalidades(Integer plano) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM PlanoModalidade WHERE (plano = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, plano);
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>PlanoModalidadeVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirPlanoModalidades</code> e <code>incluirPlanoModalidades</code> disponíveis na classe <code>PlanoModalidade</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarPlanoModalidades(Integer plano, List objetos) throws Exception {
        String str = "DELETE FROM  PlanoModalidade WHERE plano = " + plano;
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            PlanoModalidadeVO objeto = (PlanoModalidadeVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo();
        }
        PreparedStatement sqlExcluir = con.prepareStatement(str);
        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            PlanoModalidadeVO obj = (PlanoModalidadeVO) e.next();
            if (obj.getCodigo().equals(new Integer(0))) {
                obj.setPlano(plano);
                incluir(obj);
            } else {
                alterar(obj);
            }
        }
        //excluirPlanoModalidades(plano);
        //incluirPlanoModalidades(plano, objetos);
    }

    /**
     * Operação responsável por incluir objetos da <code>PlanoModalidadeVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>plano.Plano</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirPlanoModalidades(Integer planoPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            PlanoModalidadeVO obj = (PlanoModalidadeVO) e.next();
            obj.setPlano(planoPrm);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>PlanoModalidadeVO</code> relacionados a um objeto da classe <code>plano.Plano</code>.
     * @param plano  Atributo de <code>plano.Plano</code> a ser utilizado para localizar os objetos da classe <code>PlanoModalidadeVO</code>.
     * @return List  Contendo todos os objetos da classe <code>PlanoModalidadeVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarPlanoModalidades(Integer plano, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List objetos = new ArrayList();
        String sql = "SELECT * FROM PlanoModalidade WHERE plano = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, plano);
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            PlanoModalidadeVO novoObj = new PlanoModalidadeVO();
            novoObj = PlanoModalidade.montarDados(resultado, nivelMontarDados, this.con);
            objetos.add(novoObj);
        }
        Ordenacao.ordenarLista(objetos, "nomeModalidade_Apresentar");
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>PlanoModalidadeVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public PlanoModalidadeVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM PlanoModalidade WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( PlanoModalidade ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }
    public boolean consultarPorCodigo(Integer codigo) throws Exception{
        consultar(getIdEntidade(),false);
        String sql = "SELECT * FROM PlanoModalidade WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        return tabelaResultado.next();
    }

    public PlanoModalidadeVO consultar(Integer codigoPlano, Integer codigoModalidade, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM PlanoModalidade WHERE plano = ? and modalidade = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPlano);
        sqlConsultar.setInt(2, codigoModalidade);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return null;
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));

    }

    public PlanoModalidadeVO consultarPorPlanoModalidade(int plano, int modalidade, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM PlanoModalidade WHERE plano = ? and modalidade = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, plano);
        sqlConsultar.setInt(2, modalidade);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( PlanoModalidade ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public List<Integer> modalidadesPlano(Integer plano) throws Exception{
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select modalidade from planomodalidade p where plano = " + plano, con);
        List<Integer> modalidades = new ArrayList<>();
        while(rs.next()){
            modalidades.add(rs.getInt("modalidade"));
        }
        return modalidades;
    }
}

