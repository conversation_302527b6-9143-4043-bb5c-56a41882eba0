/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.acesso;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import acesso.webservice.Validador;
import acesso.webservice.retorno.RetornoRequisicaoValidacaoAcesso;
import acesso.webservice.retorno.RetornoRequisicaoWS;
import negocio.comuns.acesso.DadosAcessoOfflineVO;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.acesso.DadosAcessoOfflineInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DadosAcessoOffline extends SuperEntidade implements DadosAcessoOfflineInterfaceFacade {

    public DadosAcessoOffline() throws Exception {
        super();
    }

    public DadosAcessoOffline(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public void incluir(DadosAcessoOfflineVO obj) throws Exception {
        String sql = "INSERT INTO dadosacessooffline(codigopk, pessoa, coletor, tipopessoa, "
                + "horariosTurmas, horariosPlanos, retornovalidacao, codigousuario, senha, dia, validarSaldoCreditoTreino, saldoCreditoTreino, minutosAposUltimoAcessoDiminuirCredito, idexterno, idexternointegracao)"
                + " VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = getCon().prepareStatement(sql)) {
            int i = 0;
            sqlInserir.setInt(++i, obj.getCodigoPK());
            sqlInserir.setInt(++i, obj.getPessoa().getCodigo());
            sqlInserir.setInt(++i, obj.getColetor().getCodigo());
            sqlInserir.setString(++i, obj.getTipoPessoa());
            sqlInserir.setString(++i, obj.getHorariosTurmas());
            sqlInserir.setString(++i, obj.getHorariosPlanos());
            sqlInserir.setString(++i, obj.getRetornoValidacao());
            sqlInserir.setInt(++i, obj.getUsuario().getCodigo());
            sqlInserir.setString(++i, obj.getUsuario().getSenha());
            sqlInserir.setDate(++i, Uteis.getDataJDBC(obj.getDia()));
            sqlInserir.setBoolean(++i, obj.isValidarSaldoCreditoTreino());
            sqlInserir.setInt(++i, obj.getSaldoCreditoTreino());
            sqlInserir.setInt(++i, obj.getMinutosAposUltimoAcessoDiminuirCredito());
            sqlInserir.setInt(++i, obj.getIdexterno());
            sqlInserir.setString(++i, obj.getIdexternointegracao());
            sqlInserir.execute();
        }
    }

    @Override
    public void excluirTudo(Integer codigoPessoa) throws Exception {
        StringBuilder  sql = new StringBuilder("DELETE FROM dadosacessooffline ");
        if (codigoPessoa != null) {
            sql.append(" where pessoa = ").append(codigoPessoa);
        }
        try (PreparedStatement sqlExcluir = getCon().prepareStatement(sql.toString())) {
            sqlExcluir.execute();
        }
    }

    private static String extrairTextoDoRetornoValidacaoAcesso(RetornoRequisicaoValidacaoAcesso obj) {
        StringBuilder ret = new StringBuilder();
        List<String> lista = UtilReflection.getListAttributes(obj.getClass());
        for (String att : lista) {
            if (att.toLowerCase().equals("fotocliente")) {
                continue;
            }
            ret.append(att);
            ret.append("=");
            Object valor = UtilReflection.getValor(obj, att);
            if (valor != null) {
                ret.append((String) UtilReflection.getValor(obj, att));
            }
            ret.append("\n");
        }
        return ret.toString();
    }

    public void preencherDadosSemCommit(String key, Integer codigoPessoa) throws Exception {
        AcessoControle validacao = DaoAuxiliar.retornarAcessoControle(key);
        try {
            validacao.setValidacaoAcessoOffline(true);
            excluirTudo(codigoPessoa);
            StringBuilder sqlCliente = new StringBuilder();
            sqlCliente.append(" select distinct 'CL' tipopessoa,  c.codigo codigopk, c.pessoa, c.codacesso, 0 as idexterno, p.idexternointegracao, 0 AS  codigousuario, NULL AS  senha, sc.validarSaldoCreditoTreino,sc.saldoCreditoTreino, \n");
            sqlCliente.append("  emp.minutosAposUltimoAcessoDiminuirCredito,la.empresa as empresaacesso, c.empresa as empresapessoa  \n");
            sqlCliente.append("  from cliente  c LEFT JOIN  situacaoclientesinteticodw sc ON sc.codigocliente = c.codigo \n");
            sqlCliente.append("  left join acessocliente ac on ac.cliente = c.codigo left join localacesso la on la.codigo = ac.localacesso  \n");
            sqlCliente.append("   LEFT JOIN empresa emp on emp.codigo = c.empresa INNER JOIN pessoa p ON c.pessoa = p.codigo \n");
            sqlCliente.append(" where (c.situacao = 'AT' OR (sc.situacaocontrato = 'VE')) and (ac.codigo is null or la.utilizarModoOffline)  \n");
            StringBuilder sqlColaborador = new StringBuilder();
            sqlColaborador.append("  select distinct 'CO' tipopessoa,  c.codigo codigopk, c.pessoa, c.codacesso, 0 as idexterno, p.idexternointegracao, usu.codigo AS codigousuario, usu.senha  AS senha, false as validarSaldoCreditoTreino,  \n");
            sqlColaborador.append("   0 as saldoCreditoTreino, 0 as minutosAposUltimoAcessoDiminuirCredito ,la.empresa as empresaacesso, c.empresa as empresapessoa  \n");
            sqlColaborador.append("   from colaborador c LEFT JOIN usuario usu ON usu.colaborador = c.codigo \n");
            sqlColaborador.append("  left join acessocolaborador ac on ac.colaborador = c.codigo left join localacesso la on la.codigo = ac.localacesso \n");
            sqlColaborador.append("   LEFT JOIN empresa emp on emp.codigo = c.empresa INNER JOIN pessoa p ON c.pessoa = p.codigo  \n");
            sqlColaborador.append("  where c.situacao = 'AT' \n");
            StringBuilder sql = new StringBuilder();
            sql.append(" select tipopessoa,codigopk, p.pessoa,codacesso, idexterno, idexternointegracao, codigousuario,senha, validarSaldoCreditoTreino, saldoCreditoTreino, minutosAposUltimoAcessoDiminuirCredito,  \n");
            sql.append(" clt.codigo coletor, clt.numeroterminal, clt.localacesso, la.empresa, current_date as dia  from ( \n");
            if (codigoPessoa != null) {
                sql.append(sqlCliente.toString());
                sql.append(" and c.pessoa = ").append(codigoPessoa);
                sql.append("  UNION ALL \n");
                sql.append(sqlColaborador.toString());
                sql.append(" and c.pessoa = ").append(codigoPessoa);
            } else {
                sql.append(sqlCliente.toString());
                sql.append("        UNION ALL \n");
                sql.append(sqlColaborador.toString());
            }
            sql.append(") as p inner join localacesso la on la.empresa = p.empresaacesso or la.empresa = p.empresapessoa \n");
            sql.append("  inner join coletor clt on clt.localacesso = la.codigo   \n");
            sql.append(" WHERE clt.desativado IS FALSE AND la.utilizarModoOffline IS TRUE \n");
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    DadosAcessoOfflineVO vo = new DadosAcessoOfflineVO();
                    while (rs.next()) {
                        Uteis.logar("$$$ validacao acesso pessoa:"+rs.getInt("pessoa"));
                        long t1 = System.currentTimeMillis();
                        final String codAcesso = rs.getString("codacesso");
                        RetornoRequisicaoValidacaoAcesso retValidacao = Validador.validarAcesso(
                                codAcesso,
                                rs.getInt("empresa"),
                                rs.getInt("localacesso"),
                                rs.getString("numeroterminal"),
                                DirecaoAcessoEnum.DA_ENTRADA,
                                false,
                                MeioIdentificacaoEnum.MATRICULATECLADOCOMPUTADOR,
                                key);
                        vo.setCodigoPK(rs.getInt("codigopk"));
                        vo.getColetor().setCodigo(rs.getInt("coletor"));
                        vo.getColetor().setNumeroTerminal(rs.getInt("numeroterminal"));
                        vo.getPessoa().setCodigo(rs.getInt("pessoa"));
                        vo.setTipoPessoa(rs.getString("tipopessoa"));
                        vo.getUsuario().setCodigo(rs.getInt("codigousuario"));
                        vo.getUsuario().setSenha(rs.getString("senha"));
                        vo.setHorariosTurmas(validacao.getHorariosTurmasPermitidosParaOffline());
                        vo.setHorariosPlanos(validacao.getHorariosPlanosPermitidosParaOffline());
                        vo.setRetornoValidacao(extrairTextoDoRetornoValidacaoAcesso(retValidacao));
                        vo.setDia(rs.getDate("dia"));
                        vo.setValidarSaldoCreditoTreino(rs.getBoolean("validarSaldoCreditoTreino"));
                        vo.setSaldoCreditoTreino(rs.getInt("saldoCreditoTreino"));
                        vo.setMinutosAposUltimoAcessoDiminuirCredito(rs.getInt("minutosAposUltimoAcessoDiminuirCredito"));
                        vo.setIdexterno(rs.getInt("idexterno"));
                        vo.setIdexternointegracao(rs.getString("idexternointegracao"));
                        incluir(vo);
                        long t2 = System.currentTimeMillis();
                        Uteis.logar(null, String.format("%s %s %s ms", codAcesso, retValidacao.getSituacaoAcesso(), t2 - t1));
                    }
                }
            }
        } finally {
            validacao.setValidacaoAcessoOffline(false);
        }
    }

    @Override
    public void preencherDados(String key, Integer codigoPessoa) throws Exception {
        try {
            con.setAutoCommit(false);
            preencherDadosSemCommit(key, codigoPessoa);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        }finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public List<DadosAcessoOfflineVO> consultarDados(Integer localAcesso) throws Exception {
        List<DadosAcessoOfflineVO> ret = new ArrayList<DadosAcessoOfflineVO>();
        String sql = "  select d.*,"
                + "            c.numeroterminal,"
                + "            coalesce(cl.codacesso, co.codacesso) codacesso,"
                + "            coalesce(cl.codacessoalternativo, co.codacessoalternativo) codacessoalternativo,"
                + "            cl.codigomatricula,"
                + "            coalesce(pcl.senhaacesso, pco.senhaacesso) senhaacesso,"
                + " (select max(ct.vigenciaateajustada) from contrato ct where ct.pessoa = d.pessoa) as ultimoVencimento, "
                + "        coalesce ((select cd.numeromeses from contrato ct inner join contratoduracao cd on cd.contrato  = ct.codigo where ct.pessoa = d.pessoa order by ct.vigenciaateajustada desc limit 1), 0) as duracao "
                + "       from dadosacessooffline d "
                + " inner join coletor c on d.coletor = c.codigo"
                + "  left join cliente cl on d.pessoa = cl.pessoa and cl.situacao = 'AT'"
                + "  left join pessoa pcl on cl.pessoa = pcl.codigo"
                + "  left join colaborador co on d.pessoa = co.pessoa and co.situacao = 'AT'"
                + "  left join pessoa pco on co.pessoa = pco.codigo"
                + "      where c.localacesso = " + localAcesso.toString();
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql)) {
                while (rs.next()) {
                    ret.add(DadosAcessoOfflineVO.montarDados(rs));
                }
            }
        }
        return ret;
    }

    @Override
    public List<DadosAcessoOfflineVO> consultarDadosPessoa(Integer localAcesso, Integer codigoPessoa) throws Exception{
        List<DadosAcessoOfflineVO> ret = new ArrayList<DadosAcessoOfflineVO>();
        StringBuilder sql = new StringBuilder();
        sql.append("select d.*,  \n");
        sql.append("       c.numeroterminal, \n");
        sql.append("       coalesce(cl.codacesso, co.codacesso) codacesso, \n");
        sql.append("       coalesce(cl.codacessoalternativo, co.codacessoalternativo) codacessoalternativo, \n");
        sql.append("       cl.codigomatricula, \n");
        sql.append("       coalesce(pcl.senhaacesso, pco.senhaacesso) senhaacesso, \n");
         sql.append("     (select max(ct.vigenciaateajustada) from contrato ct where ct.pessoa = d.pessoa) as ultimoVencimento, \n");
         sql.append("     coalesce ((select cd.numeromeses from contrato ct inner join contratoduracao cd on cd.contrato  = ct.codigo where ct.pessoa = d.pessoa order by ct.vigenciaateajustada desc limit 1), 0) as duracao \n");
        sql.append("from dadosacessooffline d  \n");
        sql.append(" inner join coletor c on d.coletor = c.codigo \n");
        sql.append(" left join cliente cl on d.pessoa = cl.pessoa and cl.situacao = 'AT' \n");
        sql.append(" left join pessoa pcl on cl.pessoa = pcl.codigo \n");
        sql.append(" left join colaborador co on d.pessoa = co.pessoa and co.situacao = 'AT' \n");
        sql.append(" left join pessoa pco on co.pessoa = pco.codigo \n");
        sql.append(" where c.localacesso = ").append(localAcesso.toString()).append("\n");
        sql.append(" and d.pessoa = ").append(codigoPessoa);
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                while (rs.next()) {
                    ret.add(DadosAcessoOfflineVO.montarDados(rs));
                }
            }
        }
        return ret;
    }

    @Override
    public void excluirDadosLocalAcesso(Integer localAcesso) throws Exception {
        String sql = "DELETE FROM dadosacessooffline where coletor in(select codigo from coletor  where localacesso  = " + localAcesso +");";
        try (PreparedStatement sqlExcluir = getCon().prepareStatement(sql)) {
            sqlExcluir.execute();
        }
    }

    @Override
    public void preencherDadosLocalAcesso(Integer localAcesso, Integer empresa) throws Exception {
        con.setAutoCommit(false);
        try {
            excluirDadosLocalAcesso(localAcesso);

            String sql = "SELECT p.*, c.codigo coletor, c.numeroterminal, c.localacesso, la.empresa, current_date as dia"
                    + " FROM ( SELECT 'CL' tipopessoa, c.codigo codigopk, p.codigo pessoa, c.codacesso, "
                    + "               NULL AS  codigousuario, NULL AS  senha, 0 as idexterno, p.idexternointegracao as idexternointegracao"
                    + "          FROM pessoa p"
                    + "    INNER JOIN cliente c on p.codigo = c.pessoa"
                    + "    LEFT JOIN situacaoclientesinteticodw sc ON sc.codigocliente = c.codigo"
                    + "         WHERE (c.situacao = 'AT' OR (sc.situacaocontrato = 'VE')) and c.empresa = " + empresa
                    + "         UNION"
                    + "        SELECT 'CO', c.codigo, p.codigo, c.codacesso, "
                    + "               usu.codigo AS codigousuario, usu.senha  AS senha, 0 as idexterno, p.idexternointegracao as idexternointegracao"
                    + "          FROM pessoa p"
                    + "    INNER JOIN colaborador c on p.codigo = c.pessoa"
                    + "    LEFT JOIN usuario usu ON usu.colaborador = c.codigo"
                    + "         WHERE c.situacao = 'AT') p,"
                    + "  coletor c INNER JOIN localacesso la on c.localacesso = la.codigo"
                    + " WHERE c.desativado IS FALSE "
                    + "   AND la.utilizarModoOffline IS TRUE and la.codigo = "+ localAcesso;
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql)) {
                    DadosAcessoOfflineVO vo = new DadosAcessoOfflineVO();
                    AcessoControle validacao = new AcessoControle(getCon());
                    validacao.setValidacaoAcessoOffline(true);
                    while (rs.next()) {
                        SituacaoAcessoEnum situacaoAcesso = validacao.tentarAcesso(
                                rs.getString("codacesso"),
                                DirecaoAcessoEnum.DA_ENTRADA,
                                rs.getInt("empresa"),
                                rs.getInt("localacesso"),
                                rs.getString("numeroterminal"),
                                false, false);

                        RetornoRequisicaoValidacaoAcesso retValidacao = RetornoRequisicaoWS.montarRetornoValidacaoAcesso(situacaoAcesso, validacao, false);
                        retValidacao.setTerminal(rs.getString("numeroterminal"));
                        vo.setCodigoPK(rs.getInt("codigopk"));
                        vo.getColetor().setCodigo(rs.getInt("coletor"));
                        vo.getColetor().setNumeroTerminal(rs.getInt("numeroterminal"));
                        vo.getPessoa().setCodigo(rs.getInt("pessoa"));
                        vo.setTipoPessoa(rs.getString("tipopessoa"));
                        vo.getUsuario().setCodigo(rs.getInt("codigousuario"));
                        vo.getUsuario().setSenha(rs.getString("senha"));
                        vo.setIdexterno(rs.getInt("idexterno"));
                        vo.setIdexternointegracao(rs.getString("idexternointegracao"));
                        vo.setHorariosTurmas(validacao.getHorariosTurmasPermitidosParaOffline());
                        vo.setHorariosPlanos(validacao.getHorariosPlanosPermitidosParaOffline());
                        vo.setRetornoValidacao(extrairTextoDoRetornoValidacaoAcesso(retValidacao));
                        vo.setDia(rs.getDate("dia"));
                        incluir(vo);
                    }
                }
            }

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        }
    }
}
