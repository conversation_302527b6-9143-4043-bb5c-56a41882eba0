package negocio.facade.jdbc.contrato;

import br.com.pactosolucoes.enumeradores.TipoHorarioCreditoTreinoEnum;
import br.com.pactosolucoes.enumeradores.TipoOperacaoContratoEnum;
import br.com.pactosolucoes.enumeradores.TipoOperacaoCreditoTreinoEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoDuracaoCreditoTreinoVO;
import negocio.comuns.contrato.ContratoDuracaoVO;
import negocio.comuns.contrato.ContratoModalidadeCreditoVO;
import negocio.comuns.contrato.ContratoModalidadeHorarioTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.ControleCreditoTreinoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.interfaces.contrato.ContratoDuracaoCreditoTreinoInterfaceFacade;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 13/11/2015.
 */
public class ContratoDuracaoCreditoTreino extends SuperEntidade implements ContratoDuracaoCreditoTreinoInterfaceFacade {

    public ContratoDuracaoCreditoTreino() throws Exception {
        super();
    }

    public ContratoDuracaoCreditoTreino(Connection conexao) throws Exception {
        super(conexao);
    }

    public void incluir(ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO)throws Exception{
        String sql = "insert into ContratoDuracaoCreditoTreino(contratoDuracao, tipoHorario, numeroVezesSemana, quantidadeCreditoCompra, valorUnitario, creditoTreinoNaoCumulativo, quantidadeCreditoMensal, dataUltimoCreditoMensal) values (?,?,?,?,?,?,?,?)";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, contratoDuracaoCreditoTreinoVO.getContratoDuracaoVO().getCodigo());
        pst.setInt(2, contratoDuracaoCreditoTreinoVO.getTipoHorarioCreditoTreinoEnum().getCodigo());
        pst.setInt(3, contratoDuracaoCreditoTreinoVO.getNumeroVezesSemana());
        pst.setInt(4, contratoDuracaoCreditoTreinoVO.getQuantidadeCreditoCompra());
        pst.setDouble(5, contratoDuracaoCreditoTreinoVO.getValorUnitario());
        pst.setBoolean(6, contratoDuracaoCreditoTreinoVO.isCreditoTreinoNaoCumulativo());
        pst.setInt(7, contratoDuracaoCreditoTreinoVO.getQuantidadeCreditoMensal());
        pst.setTimestamp(8, Uteis.getDataJDBCTimestamp(contratoDuracaoCreditoTreinoVO.getDataUltimoCreditoMensal()));
        pst.execute();

        // incluir saldo do contrato anterior que foi renovado.
        ContratoVO contratoVO = contratoDuracaoCreditoTreinoVO.getContratoDuracaoVO().getContratoVO();
        Date datalancamento = Calendario.hoje();
        if(contratoVO.getDataAlteracaoManual() != null){
            datalancamento = contratoVO.getDataAlteracaoManual();
        }
        ControleCreditoTreino ccTreinoDAO = new ControleCreditoTreino(con);
        SituacaoClienteSinteticoDW situacaoClienteSinteticoDWDAO = new SituacaoClienteSinteticoDW(con);

        Calendar dataTransf = Calendario.getInstance(datalancamento);
        if (UtilReflection.objetoMaiorQueZero(contratoVO, "getContratoOrigemRenovacao().getCodigo()") && !contratoDuracaoCreditoTreinoVO.isCreditoTreinoNaoCumulativo()){
            if ((contratoVO.getDiasRestanteContratoOrigemRenovacao() > 0) &&
                    (contratoVO.getContratoOrigemRenovacao().getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getQuantidadeCreditoDisponivel() > 0)) {

                // Retirar o saldo de credito do contrato origem
                ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
                controleCreditoTreinoVO.setContratoVO(contratoVO.getContratoOrigemRenovacao());
                controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.TRANSFERENCIA_SALDO);
                controleCreditoTreinoVO.setQuantidade(contratoVO.getContratoOrigemRenovacao().getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getQuantidadeCreditoDisponivel() * -1);
                controleCreditoTreinoVO.setUsuarioVO(contratoVO.getUsuarioVO());
                controleCreditoTreinoVO.setDatalancamento(datalancamento);
                controleCreditoTreinoVO.setDataOperacao(datalancamento);
                controleCreditoTreinoVO.setObservacao("TRANSFERENCIA DE SALDO PARA O CONTRATO: " + contratoVO.getCodigo());
                ccTreinoDAO.incluirSemCommit(controleCreditoTreinoVO,null, situacaoClienteSinteticoDWDAO,datalancamento);

                // Incluir o saldo de credito do contrato origem para o novo contrato
                controleCreditoTreinoVO = new ControleCreditoTreinoVO();
                controleCreditoTreinoVO.setContratoVO(contratoVO);
                controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.TRANSFERENCIA_SALDO);
                controleCreditoTreinoVO.setContratoOrigem(contratoVO.getContratoOrigemRenovacao());
                controleCreditoTreinoVO.setQuantidade(contratoVO.getContratoOrigemRenovacao().getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getQuantidadeCreditoDisponivel());
                controleCreditoTreinoVO.setUsuarioVO(contratoVO.getUsuarioVO());
                dataTransf.add(Calendar.SECOND, 1);
                controleCreditoTreinoVO.setDatalancamento(dataTransf.getTime());
                controleCreditoTreinoVO.setDataOperacao(dataTransf.getTime());
                controleCreditoTreinoVO.setObservacao("TRANSFERENCIA DE SALDO DO CONTRATO: " + contratoVO.getContratoOrigemRenovacao().getCodigo());
                ccTreinoDAO.incluirSemCommit(controleCreditoTreinoVO,null, situacaoClienteSinteticoDWDAO, controleCreditoTreinoVO.getDatalancamento());
            }
        }
        // incluir operação de compra.
        if (!contratoDuracaoCreditoTreinoVO.isVindoDeTransferencia()){
            ContratoModalidadeCredito contratoModalidadeCreditoDAO = new ContratoModalidadeCredito(con);
            boolean contratoSessao = contratoModalidadeCreditoDAO.contratoVendaCreditoSessao(contratoVO.getCodigo());
            if (contratoSessao) {
                List<ContratoModalidadeCreditoVO> lista = contratoModalidadeCreditoDAO.consultarPorCodigoContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                for (ContratoModalidadeCreditoVO creditoVO : lista) {
                    ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
                    controleCreditoTreinoVO.setContratoVO(contratoVO);
                    controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.COMPRA);
                    controleCreditoTreinoVO.setQuantidade(creditoVO.getQtdCreditoCompra());
                    controleCreditoTreinoVO.setObservacao(creditoVO.getContratoModalidadeVO().getModalidade().getNome());
                    controleCreditoTreinoVO.setModalidadeVO(creditoVO.getContratoModalidadeVO().getModalidade());
                    dataTransf.add(Calendar.SECOND, 1);
                    controleCreditoTreinoVO.setDatalancamento(dataTransf.getTime());
                    controleCreditoTreinoVO.setDataOperacao(dataTransf.getTime());
                    controleCreditoTreinoVO.setUsuarioVO(contratoVO.getUsuarioVO());
                    ccTreinoDAO.incluirSemCommit(controleCreditoTreinoVO,null, situacaoClienteSinteticoDWDAO,datalancamento);
                }

            } else {

                ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
                controleCreditoTreinoVO.setContratoVO(contratoVO);
                controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.COMPRA);
                if (contratoDuracaoCreditoTreinoVO.isCreditoTreinoNaoCumulativo()) {
                    controleCreditoTreinoVO.setQuantidade(contratoDuracaoCreditoTreinoVO.getQuantidadeCreditoMensal());
                } else {
                    controleCreditoTreinoVO.setQuantidade(contratoDuracaoCreditoTreinoVO.getQuantidadeCreditoCompra());
                }
                dataTransf.add(Calendar.SECOND, 1);
                controleCreditoTreinoVO.setDatalancamento(dataTransf.getTime());
                controleCreditoTreinoVO.setDataOperacao(dataTransf.getTime());
                controleCreditoTreinoVO.setUsuarioVO(contratoVO.getUsuarioVO());

                ccTreinoDAO.incluirSemCommit(controleCreditoTreinoVO,null, situacaoClienteSinteticoDWDAO,datalancamento);

                if (contratoVO.getDataAlteracaoManual() != null) {
                    boolean atualizarTreino = false;
                    for (ContratoModalidadeVO cmVO : contratoVO.getContratoModalidadeVOs()) {
                        for (ContratoModalidadeHorarioTurmaVO cmhtVO : cmVO.getListaContratoModalidadesHorarioTurmaVOs()) {
                            Date dataInicioAvaliar = contratoVO.getVigenciaDe();
                            for (int i = 0; Calendario.menor(dataInicioAvaliar, contratoVO.getDataAlteracaoManual()); i++) {
                                dataInicioAvaliar = Calendario.somarDias(contratoVO.getVigenciaDe(), i);
                                int diaSemana = Calendario.getDiaSemana(dataInicioAvaliar);
                                if (diaSemana == cmhtVO.getHorarioTurma().getDiaSemanaNumero()) {
                                    ccTreinoDAO.diminuirCreditoTreinoNaoComparecimentoSemCommit(contratoVO, dataInicioAvaliar,
                                            "CONTRATO LANÇADO COM DATA BASE ALTERADA", null,
                                            cmhtVO.getHorarioTurma(), null);
                                    atualizarTreino = true;
                                }
                            }
                        }
                    }
                    if (atualizarTreino) {
                        situacaoClienteSinteticoDWDAO.atualizarInformacoesCreditoTreino(contratoVO.getPessoa().getCodigo(), contratoVO.getDataAlteracaoManual());
                    }
                    situacaoClienteSinteticoDWDAO = null;
                }
            }
        }
        ccTreinoDAO = null;
    }

    public void excluir(Integer codigoContrato)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("delete from contratoDuracaoCreditoTreino \n");
        sql.append("where contratoDuracao in (select codigo from contratoDuracao where contrato = ?)");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setInt(1, codigoContrato);
        pst.execute();
    }

    public void alterarVezesSemana(ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO, Integer numeroVezesSemana)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("update ContratoDuracaoCreditoTreino set numeroVezesSemana = ").append(numeroVezesSemana).append(" where codigo = ").append(contratoDuracaoCreditoTreinoVO.getCodigo());
        executarConsulta(sql.toString(), con);
    }

    public ContratoDuracaoCreditoTreinoVO consultarPorContratoDuracao(Integer codigoContratoDuracao, int nivelMontarDados)throws Exception{
        ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO = null;
        String sql = "select * from ContratoDuracaoCreditoTreino where contratoDuracao = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1,codigoContratoDuracao);
        ResultSet rs = pst.executeQuery();
        if (rs.next()){
            contratoDuracaoCreditoTreinoVO = montarDados(rs, nivelMontarDados);
        }
        return contratoDuracaoCreditoTreinoVO;
    }
    public ContratoDuracaoCreditoTreinoVO consultarPorContrato(Integer codigoContrato, int nivelMontarDados)throws Exception{
        ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO = null;
        StringBuilder sql = new StringBuilder();
        sql.append("select cdc.* \n");
        sql.append("from contratoDuracaoCreditoTreino cdc \n");
        sql.append("inner join contratoDuracao cd on cd.codigo = cdc.contratoDuracao \n");
        sql.append("where cd.contrato = ? ");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setInt(1, codigoContrato);
        ResultSet rs = pst.executeQuery();
        if (rs.next()){
            contratoDuracaoCreditoTreinoVO = montarDados(rs, nivelMontarDados);
        }
        return contratoDuracaoCreditoTreinoVO;
    }

    public ContratoDuracaoCreditoTreinoVO consultarDadosParaCancelamentoContrato(ContratoVO contratoVO, Integer codigoPlano, boolean retrocederValorMensalPlanoCancelamento)throws Exception{
        ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO = new ContratoDuracaoCreditoTreinoVO();
        contratoVO.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().setValorUnitario(Uteis.arredondarForcando2CasasDecimais(contratoVO.getValorBaseCalculo() / contratoVO.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getQuantidadeCreditoCompra()));
        contratoDuracaoCreditoTreinoVO.setQuantidadeCreditoCompra(contratoVO.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getQuantidadeCreditoCompra());
        contratoDuracaoCreditoTreinoVO.setValorUnitario(contratoVO.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getValorUnitario());
        contratoDuracaoCreditoTreinoVO.setValorUnitarioCompraOriginal(contratoVO.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getValorUnitario());
        contratoDuracaoCreditoTreinoVO.setContratoDuracaoVO(contratoVO.getContratoDuracao());
        contratoDuracaoCreditoTreinoVO.setTipoHorarioCreditoTreinoEnum(contratoVO.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getTipoHorarioCreditoTreinoEnum());

        // consultar transferencia de saldo.
        ControleCreditoTreinoVO controleCreditoTreinoVO = getFacade().getControleCreditoTreino().consultarOperacaoTransferenciaSaldo(contratoVO.getContratoDuracao().getContrato(),false,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (controleCreditoTreinoVO != null 
                && UteisValidacao.notEmptyNumber(controleCreditoTreinoVO.getContratoOrigem().getCodigo())){//apenas para contratos que receberam créditos, que não sejam de estornos de contratos futuros
            contratoDuracaoCreditoTreinoVO.setQuantidadeTransferenciaSaldo(controleCreditoTreinoVO.getQuantidade());
            ContratoDuracaoCreditoTreinoVO creditoContratoOrigem = getFacade().getContratoDuracaoCreditoTreino().consultarPorContrato(controleCreditoTreinoVO.getContratoOrigem().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            contratoDuracaoCreditoTreinoVO.setTotalTransferenciaSaldo(contratoDuracaoCreditoTreinoVO.getQuantidadeTransferenciaSaldo() * creditoContratoOrigem.getValorUnitario());
            contratoDuracaoCreditoTreinoVO.setContratoTransferenciaSaldo(controleCreditoTreinoVO.getContratoOrigem());
        }
        // consultar o saldo de créditos.
        contratoDuracaoCreditoTreinoVO.setQuantidadeCreditoDisponivel(ControleCreditoTreino.consultarSaldoCredito(con,contratoVO.getContratoDuracao().getContrato()));

        if (contratoVO.getPlano().isCreditoTreinoNaoCumulativo()){
            contratoDuracaoCreditoTreinoVO.setQuantidadeCreditoUtilizado(getFacade().getControleCreditoTreino().consultarUtilizacaoCreditoPorContrato(contratoVO.getCodigo()));
        }

        // consultar o valor unitário base para cancelamento.
        if (retrocederValorMensalPlanoCancelamento)
          getFacade().getPlanoDuracaoCreditoTreino().consultarContratoDuracaoCreditoTreinoBaseCalculo(contratoDuracaoCreditoTreinoVO,codigoPlano);
        else
           contratoDuracaoCreditoTreinoVO.setContratoDuracaoCreditoTreinoBaseCalculo(contratoVO.getContratoDuracao().getContratoDuracaoCreditoTreinoVO());

        return contratoDuracaoCreditoTreinoVO;

    }


    public ContratoDuracaoCreditoTreinoVO consultarPorCodigo(Integer codigo, int nivelMontarDados)throws Exception{
        ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO = null;
        String sql = "select * from ContratoDuracaoCreditoTreino where codigo = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1,codigo);
        ResultSet rs = pst.executeQuery();
        if (rs.next()){
            contratoDuracaoCreditoTreinoVO = montarDados(rs, nivelMontarDados);
        }
        return contratoDuracaoCreditoTreinoVO;
    }

    private ContratoDuracaoCreditoTreinoVO montarDados(ResultSet rs, int nivelMontarDados)throws  Exception{
        ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO = montarDadosBasico(rs);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA){
            return contratoDuracaoCreditoTreinoVO;

        }
        ContratoDuracao contratoDuracao = new ContratoDuracao(con);
        contratoDuracaoCreditoTreinoVO.setContratoDuracaoVO(contratoDuracao.consultarPorChavePrimaria(contratoDuracaoCreditoTreinoVO.getContratoDuracaoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        contratoDuracao = null;

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS){
            return contratoDuracaoCreditoTreinoVO;
        }

        Contrato contrato = new Contrato(con);
        contratoDuracaoCreditoTreinoVO.getContratoDuracaoVO().setContratoVO(contrato.consultarPorChavePrimaria(contratoDuracaoCreditoTreinoVO.getContratoDuracaoVO().getContrato(), Uteis.NIVELMONTARDADOS_MINIMOS));
        contrato = null;

        return contratoDuracaoCreditoTreinoVO;
    }

    private ContratoDuracaoCreditoTreinoVO montarDadosBasico(ResultSet rs)throws  Exception{
        ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO = new ContratoDuracaoCreditoTreinoVO();
        contratoDuracaoCreditoTreinoVO.setCodigo(rs.getInt("codigo"));
        contratoDuracaoCreditoTreinoVO.setContratoDuracaoVO(new ContratoDuracaoVO());
        contratoDuracaoCreditoTreinoVO.getContratoDuracaoVO().setCodigo(rs.getInt("contratoDuracao"));
        contratoDuracaoCreditoTreinoVO.setTipoHorarioCreditoTreinoEnum(TipoHorarioCreditoTreinoEnum.getTipo(rs.getInt("tipoHorario")));
        contratoDuracaoCreditoTreinoVO.setNumeroVezesSemana(rs.getInt("numeroVezesSemana"));
        contratoDuracaoCreditoTreinoVO.setQuantidadeCreditoCompra(rs.getInt("quantidadeCreditoCompra"));
        contratoDuracaoCreditoTreinoVO.setQuantidadeCreditoDisponivel(rs.getInt("quantidadeCreditoDisponivel"));
        contratoDuracaoCreditoTreinoVO.setValorUnitario(rs.getDouble("valorUnitario"));
        contratoDuracaoCreditoTreinoVO.setCreditoTreinoNaoCumulativo(rs.getBoolean("creditoTreinoNaoCumulativo"));
        contratoDuracaoCreditoTreinoVO.setQuantidadeCreditoMensal(rs.getInt("quantidadeCreditoMensal"));
        contratoDuracaoCreditoTreinoVO.setDataUltimoCreditoMensal(rs.getTimestamp("dataUltimoCreditoMensal"));
        return  contratoDuracaoCreditoTreinoVO;
    }

    
    public List<ContratoVO> consultarCodigosContratosEncerrar(Date data) throws Exception{
        StringBuilder sql = new StringBuilder("SELECT con.* FROM contrato con \n");
        sql.append(" INNER JOIN situacaoclientesinteticodw sw ON sw.codigocontrato = con.codigo AND con.vendaCreditoTreino \n");
        sql.append(" inner join contratoDuracao cd on cd.contrato = con.codigo  \n");
        sql.append(" inner join contratoDuracaoCreditoTreino cdc on cdc.contratoDuracao = cd.codigo \n");
        sql.append(" WHERE cdc.quantidadeCreditoDisponivel < 1 AND sw.situacao = 'AT' \n");
        sql.append(" AND cdc.creditoTreinoNaoCumulativo = false \n");
        sql.append(" AND NOT EXISTS (SELECT codigo FROM contratooperacao ");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sql.append(" WHERE contrato = con.codigo AND datafimefetivacaooperacao >= '").append(sdf.format(data.getTime()));
        sql.append("' AND tipooperacao IN ('");
        sql.append(TipoOperacaoContratoEnum.CANCELAMENTO.getSigla()).append("','");
        sql.append(TipoOperacaoContratoEnum.CARENCIA.getSigla()).append("','");
        sql.append(TipoOperacaoContratoEnum.TRANCAMENTO.getSigla()).append("','");
        sql.append(TipoOperacaoContratoEnum.TRANCAMENTO_VENCIDO.getSigla()).append("','");
        sql.append(TipoOperacaoContratoEnum.ATESTADO.getSigla()).append("') )\n");
        sql.append(" AND NOT EXISTS (SELECT codigo FROM controlecreditotreino  WHERE contrato = con.codigo AND quantidade < 0");
        sql.append(" AND tipooperacaocreditotreino = 4 AND diaaula >= '").append(sdf.format(data.getTime())).append("') ");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        return Contrato.montarDadosConsulta(pst.executeQuery(), Uteis.NIVELMONTARDADOS_TODOS, con);
    }

    public void processarCreditoDisponivelMensal(Date diaProcessamentoRobo)throws Exception{
        Uteis.logar(null, "PROCESSO LIBERAR CRÉDITOS MENSAIS - CRÉDITO TREINO - NÃO CUMULATIVO - em: " + Uteis.getDataComHora(Calendario.hoje()));

        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO usuarioReco = usuarioDAO.getUsuarioRecorrencia();

        String sql = "select \n" +
                "cdc.* \n" +
                "from contratoduracaocreditotreino  cdc\n" +
                "inner join contratoduracao cd on cd.codigo = cdc.contratoduracao\n" +
                "inner join contrato c on c.codigo = cd.contrato\n" +
                "where 1 = 1\n" +
                "and c.situacao = 'AT'\n" +
                "and cdc.creditoTreinoNaoCumulativo = true\n" +
                "and (select cast(cdc.dataultimocreditomensal as date) + interval '1 month' as date)::date <= '"+Uteis.getData(diaProcessamentoRobo)+"'";
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql);

        while (rs.next()) {
            ContratoDuracaoCreditoTreinoVO obj = montarDados(rs, Uteis.NIVELMONTARDADOS_TODOS);
            Uteis.logar(null, "LIBERAR CRÉDITOS MENSAIS - CRÉDITO TREINO - NÃO CUMULATIVO CONTRATO - " + obj.getContratoDuracaoVO().getContrato() + " em: " + Uteis.getDataComHora(Calendario.hoje()));

            if (obj.getQuantidadeCreditoDisponivel() > 0) {
                //incluir operacao zerar
                ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
                controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.AJUSTE_MENSAL);
                controleCreditoTreinoVO.setObservacao("Zerar créditos do mês. Contrato " + obj.getContratoDuracaoVO().getContrato());
                controleCreditoTreinoVO.setQuantidade(-1 * obj.getQuantidadeCreditoDisponivel());
                controleCreditoTreinoVO.setContratoVO(new ContratoVO());
                controleCreditoTreinoVO.getContratoVO().setCodigo(obj.getContratoDuracaoVO().getContrato());
                controleCreditoTreinoVO.setDataOperacao(Calendario.hoje());
                controleCreditoTreinoVO.setUsuarioVO(usuarioReco);
                getFacade().getControleCreditoTreino().incluirSemCommit(controleCreditoTreinoVO, null, getFacade().getSituacaoClienteSinteticoDW(), Calendario.hoje());
            }

            // incluir operação disponível mensal
            ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
            controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.AJUSTE_MENSAL);
            controleCreditoTreinoVO.setObservacao("Créditos disponíveis para o mês. Contrato " + obj.getContratoDuracaoVO().getContrato());
            if (Calendario.diferencaEmDias( obj.getDataUltimoCreditoMensal(), Calendario.hoje()) >= 35){
                controleCreditoTreinoVO.setObservacao(controleCreditoTreinoVO.getObservacao() + "\nAjustado a data do ultimo crédito para o dia: " + Calendario.getDia(obj.getContratoDuracaoVO().getContratoVO().getVigenciaDe()));
            }
            controleCreditoTreinoVO.setQuantidade(obj.getQuantidadeCreditoMensal());
            controleCreditoTreinoVO.setContratoVO(new ContratoVO());
            controleCreditoTreinoVO.getContratoVO().setCodigo(obj.getContratoDuracaoVO().getContrato());
            controleCreditoTreinoVO.setDatalancamento(Calendario.hoje());
            controleCreditoTreinoVO.setDataOperacao(Calendario.hoje());
            controleCreditoTreinoVO.setUsuarioVO(usuarioReco);
            getFacade().getControleCreditoTreino().incluirSemCommit(controleCreditoTreinoVO, null, getFacade().getSituacaoClienteSinteticoDW(), Calendario.hoje());

            if (Calendario.diferencaEmDias( obj.getDataUltimoCreditoMensal(), Calendario.hoje()) >= 35){
                Calendar calendar = Calendario.getInstance();
                calendar.setTime(Calendario.hoje());
                calendar.set(Calendar.DAY_OF_MONTH, Calendario.getDia(obj.getContratoDuracaoVO().getContratoVO().getVigenciaDe()));;

                Date novaData = calendar.getTime();
                alterarDataUltimoCreditoMensal(obj, novaData);
            } else {
                Date dataInicioVigenciaContrato = Uteis.retirarHoraDaData(obj.getContratoDuracaoVO().getContratoVO().getVigenciaDe());
                if (isContratoInicioVigenciaUltimoDiaDoMes(dataInicioVigenciaContrato)) {
                    Calendar calendar = Calendario.getInstance();
                    calendar.setTime(Calendario.hoje());

                    int ultimoDiaDoMes = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
                    int diaVigencia = Calendario.getDia(obj.getContratoDuracaoVO().getContratoVO().getVigenciaDe());

                    if (diaVigencia > ultimoDiaDoMes) {
                        calendar.set(Calendar.DAY_OF_MONTH, ultimoDiaDoMes);
                    } else {
                        calendar.set(Calendar.DAY_OF_MONTH, diaVigencia);
                    }

                        Date novaData = calendar.getTime();
                    alterarDataUltimoCreditoMensal(obj, novaData);
                } else {
                    alterarDataUltimoCreditoMensal(obj);
                }
            }
        }
    }

    public static boolean isContratoInicioVigenciaUltimoDiaDoMes(Date data) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(data);

        int dia = cal.get(Calendar.DAY_OF_MONTH);
        int ultimoDia = cal.getActualMaximum(Calendar.DAY_OF_MONTH);

        return dia == ultimoDia;
    }

    public void processarCreditoDisponivelMensalZerarCreditoContratoInativo(Date diaProcessamentoRobo) throws Exception {
        Uteis.logar(null, "PROCESSO ZERAR CRÉDITOS DISPONÍVEIS CONTRATOS INATIVOS - CRÉDITO TREINO - NÃO CUMULATIVO - em: " + Uteis.getDataComHora(Calendario.hoje()));

        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO usuarioReco = usuarioDAO.getUsuarioRecorrencia();

        String sql = "select \n" +
                "  cdc.*,\n" +
                "  (select sum(quantidade) from controlecreditotreino   where contrato   = c.codigo) as saldoAtual\n" +
                "from contratoduracaocreditotreino cdc \n" +
                "inner join contratoduracao cd on cd.codigo = cdc.contratoduracao \n" +
                "inner join contrato c on c.codigo = cd.contrato \n" +
                "where 1 = 1\n" +
                "and cdc.creditoTreinoNaoCumulativo = true \n" +
                "and (select sum(quantidade) from controlecreditotreino   where contrato   = c.codigo) > 0 \n" +
                "and c.vigenciaateajustada::date < '"+Uteis.getDataFormatoBD(diaProcessamentoRobo)+"'::date \n" +
                "AND c.situacao <> 'TR' ";
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql);

        while (rs.next()) {
            ContratoDuracaoCreditoTreinoVO obj = montarDados(rs, Uteis.NIVELMONTARDADOS_TODOS);
            Uteis.logar(null, "PROCESSO ZERAR CRÉDITOS DISPONÍVEIS CONTRATOS INATIVOS - CRÉDITO TREINO - NÃO CUMULATIVO CONTRATO - " + obj.getContratoDuracaoVO().getContrato() + " em: " + Uteis.getDataComHora(Calendario.hoje()));
            //incluir operacao zerar
            ControleCreditoTreinoVO controleCreditoTreinoVO = new ControleCreditoTreinoVO();
            controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.AJUSTE_MENSAL);
            controleCreditoTreinoVO.setObservacao("Contrato inativo, zerar créditos disponíveis. Contrato " + obj.getContratoDuracaoVO().getContrato());
            controleCreditoTreinoVO.setQuantidade(-1 * obj.getQuantidadeCreditoDisponivel());
            controleCreditoTreinoVO.setContratoVO(new ContratoVO());
            controleCreditoTreinoVO.getContratoVO().setCodigo(obj.getContratoDuracaoVO().getContrato());
            controleCreditoTreinoVO.setDataOperacao(Calendario.hoje());
            controleCreditoTreinoVO.setUsuarioVO(usuarioReco);
            getFacade().getControleCreditoTreino().incluirSemCommit(controleCreditoTreinoVO, null, getFacade().getSituacaoClienteSinteticoDW(), Calendario.hoje());

        }
    }

    private void alterarDataUltimoCreditoMensal(ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("update ContratoDuracaoCreditoTreino set dataUltimoCreditoMensal = '").append(Uteis.getDataJDBCTimestamp(Calendario.hoje())).append("' where codigo = ").append(contratoDuracaoCreditoTreinoVO.getCodigo());
        executarConsulta(sql.toString(), con);
    }

    public void alterarDataUltimoCreditoMensal(ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO, Date data)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("update ContratoDuracaoCreditoTreino set dataUltimoCreditoMensal = '").append(Uteis.getDataJDBCTimestamp(data)).append("' where codigo = ").append(contratoDuracaoCreditoTreinoVO.getCodigo());
        executarConsulta(sql.toString(), con);
    }

}
