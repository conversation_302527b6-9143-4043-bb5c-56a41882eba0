package negocio.facade.jdbc.contrato;

import negocio.comuns.contrato.ReajusteContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.contrato.ReajusteContratoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Types;

/**
 * Created by ulisses on 23/11/2016.
 */
public class ReajusteContrato extends SuperEntidade implements ReajusteContratoInterfaceFacade {

    public ReajusteContrato() throws Exception {
        super();
    }

    public ReajusteContrato(Connection conexao) throws Exception {
        super(conexao);
    }

    public void incluir(ReajusteContratoVO reajusteContratoVO)throws Exception{
        reajusteContratoVO.validarDados(reajusteContratoVO);
        StringBuilder sql = new StringBuilder();
        sql.append("insert into reajusteContrato (contrato, indiceFinanceiroReajustePreco, percentualIndice, valorMensalAnterior, valorMensalNovo, usuario, datalancamento) \n");
        sql.append("values(?,?,?,?,?,?,?)");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setInt(1, reajusteContratoVO.getContratoVO().getCodigo());
        if (UtilReflection.objetoMaiorQueZero(reajusteContratoVO, "getIndiceFinanceiroReajustePrecoVO().getCodigo()")){
            pst.setInt(2, reajusteContratoVO.getIndiceFinanceiroReajustePrecoVO().getCodigo());
        }else {
            pst.setNull(2, Types.NULL);
        }
        pst.setDouble(3, reajusteContratoVO.getPercentualIndice());
        pst.setDouble(4, reajusteContratoVO.getValorMensalAnterior());
        pst.setDouble(5, reajusteContratoVO.getValorMensalNovo());
        pst.setInt(6, reajusteContratoVO.getUsuarioVO().getCodigo());
        pst.setTimestamp(7, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        pst.execute();
        reajusteContratoVO.setCodigo(obterValorChavePrimariaCodigo());
        reajusteContratoVO.setNovoObj(false);
    }

    public void excluir(Integer codigoContrato)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("delete from reajusteContrato where contrato =?");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setInt(1, codigoContrato);
        pst.execute();
    }

}
