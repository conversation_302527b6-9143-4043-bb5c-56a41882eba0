package negocio.facade.jdbc.vendas;

import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.interfaces.vendas.VendasOnlineConvenioTentativaInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 13/08/2020
 */
public class VendasOnlineConvenioTentativa extends SuperEntidade implements VendasOnlineConvenioTentativaInterfaceFacade {

    public VendasOnlineConvenioTentativa() throws Exception {
        super();
    }

    public VendasOnlineConvenioTentativa(Connection con) throws Exception {
        super(con);
    }

    public void gravar(VendasConfigVO vendasConfigVO, UsuarioVO usuarioVO, List<VendasOnlineConvenioTentativaVO> listaAtual) throws Exception {
        ConvenioCobranca convenioCobrancaDAO;
        try {
            con.setAutoCommit(false);
            convenioCobrancaDAO = new ConvenioCobranca(con);

            List<VendasOnlineConvenioTentativaVO> listaAnterior = consultarPorEmpresa(vendasConfigVO.getEmpresa(), false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);

            boolean teveAlteracao = false;

            for (VendasOnlineConvenioTentativaVO objAtu : listaAtual) {
                //item novo
                if (UteisValidacao.emptyNumber(objAtu.getCodigo())) {
                    teveAlteracao = true;
                    break;
                }

                //itens que tiveram alteração
                boolean teveAlteracaoItem = true;
                for (VendasOnlineConvenioTentativaVO objAnt : listaAnterior) {
                    if (objAnt.getCodigo().equals(objAtu.getCodigo()) &&
                            objAnt.getOrdem().equals(objAtu.getOrdem()) &&
                            objAnt.getTipoParcelamentoStone().equalsIgnoreCase(objAtu.getTipoParcelamentoStone()) &&
                            objAnt.getEmpresaVO().getCodigo().equals(objAtu.getEmpresaVO().getCodigo()) &&
                            objAnt.getConvenioCobrancaVO().getCodigo().equals(objAtu.getConvenioCobrancaVO().getCodigo())) {
                        teveAlteracaoItem = false;
                    }
                }
                if (teveAlteracaoItem) {
                    teveAlteracao = true;
                    break;
                }
            }

            if (!teveAlteracao) {
                for (VendasOnlineConvenioTentativaVO objAnt : listaAnterior) {
                    boolean foiExcluido = true;
                    for (VendasOnlineConvenioTentativaVO objAtu : listaAtual) {
                        if (objAnt.getCodigo().equals(objAtu.getCodigo())) {
                            foiExcluido = false;
                            break;
                        }
                    }
                    if (foiExcluido) {
                        teveAlteracao = true;
                    }
                }
            }

            excluirPorEmpresa(vendasConfigVO.getEmpresa());

            Map<Integer, ConvenioCobrancaVO> mapaConvenio = convenioCobrancaDAO.obterMapaConvenios(null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            for (VendasOnlineConvenioTentativaVO obj : listaAtual) {
                obj.setConvenioCobrancaVO(mapaConvenio.get(obj.getConvenioCobrancaVO().getCodigo()));
                incluir(obj);
            }

            if (teveAlteracao) {
                gravarLog(listaAnterior, listaAtual, usuarioVO, vendasConfigVO);
            }

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            convenioCobrancaDAO = null;
            con.setAutoCommit(true);
        }
    }

    private void gravarLog(List<VendasOnlineConvenioTentativaVO> listaAnterior,
                           List<VendasOnlineConvenioTentativaVO> listaAtual, UsuarioVO usuarioVO, VendasConfigVO vendasConfigVO) throws Exception {
        try {

            //quando a lista anterior é maior que a atual
            for (VendasOnlineConvenioTentativaVO objAnterior : listaAnterior) {
                boolean encontrou = false;
                for (VendasOnlineConvenioTentativaVO objAtual : listaAtual) {
                    if (objAnterior.getOrdem().equals(objAtual.getOrdem())) {
                        encontrou = true;
                    } else {
                        continue;
                    }

                    if (!objAnterior.getConvenioCobrancaVO().getCodigo().equals(objAtual.getConvenioCobrancaVO().getCodigo()) ||
                            !objAnterior.getTipoParcelamentoStone().equals(objAtual.getTipoParcelamentoStone())) {
                        incluirLog(vendasConfigVO, objAtual, objAnterior, usuarioVO, "ALTERAÇÃO");
                        break;
                    }
                }

                if (!encontrou) {
                    incluirLog(vendasConfigVO, new VendasOnlineConvenioTentativaVO(), objAnterior, usuarioVO, "EXCLUSÃO");
                }
            }

            //quando a lista atual é maior que a anterior
            for (VendasOnlineConvenioTentativaVO objAtual : listaAtual) {
                boolean encontrou = false;
                for (VendasOnlineConvenioTentativaVO objAnterior : listaAnterior) {
                    if (objAnterior.getOrdem().equals(objAtual.getOrdem())) {
                        encontrou = true;
                        break;
                    }
                }

                if (!encontrou) {
                    incluirLog(vendasConfigVO, objAtual, new VendasOnlineConvenioTentativaVO(), usuarioVO, "INCLUSÃO");
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private void incluirLog(VendasConfigVO vendasConfigVO, VendasOnlineConvenioTentativaVO objAtual,
                            VendasOnlineConvenioTentativaVO objAnterior, UsuarioVO usuarioVO, String operacao) throws Exception {
        Log logDAO;
        try {
            logDAO = new Log(con);

            if (!objAtual.getOrdem().equals(objAnterior.getOrdem())) {
                LogVO log = new LogVO();
                log.setNomeEntidade("VENDASCONFIG_VENDASONLINECONVENIOTENTATIVA");
                log.setNomeEntidadeDescricao("VENDASCONFIG_VENDASONLINECONVENIOTENTATIVA");
                log.setDescricao("");
                log.setChavePrimaria(vendasConfigVO.getCodigo().toString());
                log.setDataAlteracao(Calendario.hoje());
                log.setUsuarioVO(usuarioVO);
                log.setResponsavelAlteracao(usuarioVO.getNomeAbreviado());
                log.setOperacao(operacao);
                log.setUserOAMD(usuarioVO.getUserOamd());
                log.setNomeCampo("ORDEM NA LISTA");
                log.setValorCampoAnterior(objAnterior.getOrdem().toString());
                log.setValorCampoAlterado(objAtual.getOrdem().toString());
                logDAO.incluirSemCommit(log);
            }

            if (!objAtual.getConvenioCobrancaVO().getCodigo().equals(objAnterior.getConvenioCobrancaVO().getCodigo())) {
                LogVO log = new LogVO();
                log.setNomeEntidade("VENDASCONFIG_VENDASONLINECONVENIOTENTATIVA");
                log.setNomeEntidadeDescricao("VENDASCONFIG_VENDASONLINECONVENIOTENTATIVA");
                log.setDescricao("");
                log.setChavePrimaria(vendasConfigVO.getCodigo().toString());
                log.setDataAlteracao(Calendario.hoje());
                log.setUsuarioVO(usuarioVO);
                log.setResponsavelAlteracao(usuarioVO.getNomeAbreviado());
                log.setOperacao(operacao);
                log.setUserOAMD(usuarioVO.getUserOamd());
                log.setNomeCampo("CONVENIO");
                log.setValorCampoAnterior(objAnterior.getConvenioCobrancaVO().getDescricao());
                log.setValorCampoAlterado(objAtual.getConvenioCobrancaVO().getDescricao());
                logDAO.incluirSemCommit(log);
            }

            if (objAtual.getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE) || objAnterior.getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
                LogVO log = new LogVO();
                log.setNomeEntidade("VENDASCONFIG_VENDASONLINECONVENIOTENTATIVA");
                log.setNomeEntidadeDescricao("VENDASCONFIG_VENDASONLINECONVENIOTENTATIVA");
                log.setDescricao("");
                log.setChavePrimaria(vendasConfigVO.getCodigo().toString());
                log.setDataAlteracao(Calendario.hoje());
                log.setUsuarioVO(usuarioVO);
                log.setResponsavelAlteracao(usuarioVO.getNomeAbreviado());
                log.setOperacao(operacao);
                log.setUserOAMD(usuarioVO.getUserOamd());
                //tipo parcelamento
                log.setNomeCampo("TIPO_PARCELAMENTO");
                log.setValorCampoAnterior(objAnterior.getTipoParcelamentoStone().equals("") ? "Nenhum" : (objAnterior.getTipoParcelamentoStone().equals("MCHT") ? "Lojista" : "Emissor"));
                log.setValorCampoAlterado(objAtual.getTipoParcelamentoStone().equals("") ? "Nenhum" : (objAtual.getTipoParcelamentoStone().equals("MCHT") ? "Lojista" : "Emissor"));

                logDAO.incluirSemCommit(log);
            }
        } finally {
            logDAO = null;
        }
    }

    private JSONArray obterArrayLog(List<VendasOnlineConvenioTentativaVO> lista) throws Exception {
        Empresa empresaDAO;
        ConvenioCobranca convenioCobrancaDAO;
        try {
            empresaDAO = new Empresa(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);

            JSONArray array = new JSONArray();
            Map<Integer, EmpresaVO> mapaEmpresa = empresaDAO.obterMapaEmpresas(Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            Map<Integer, ConvenioCobrancaVO> mapaConvenio = convenioCobrancaDAO.obterMapaConvenios(null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            for (VendasOnlineConvenioTentativaVO rateioVO : lista) {
                EmpresaVO empresaVO = mapaEmpresa.get(rateioVO.getEmpresaVO().getCodigo());
                ConvenioCobrancaVO convenioCobrancaVO = mapaConvenio.get(rateioVO.getConvenioCobrancaVO().getCodigo());

                JSONObject rateio = new JSONObject();
                rateio.put("ordem", rateioVO.getOrdem());
                rateio.put("convenio", convenioCobrancaVO.getCodigo());
                rateio.put("convenioDescricao", convenioCobrancaVO.getDescricao());
                rateio.put("tipoParcelamentoStone", rateioVO.getTipoParcelamentoStoneApresentar());
                rateio.put("empresa", empresaVO.getCodigo());
                rateio.put("empresaNome", empresaVO.getNome());
                array.put(rateio);
            }
            return array;
        } finally {
            empresaDAO = null;
            convenioCobrancaDAO = null;
        }
    }

    private void incluir(VendasOnlineConvenioTentativaVO obj) throws Exception {
        VendasOnlineConvenioTentativaVO.validarDados(obj);
        String insert = "INSERT INTO VendasOnlineConvenioTentativa(conveniocobranca, empresa, ordem, tipoParcelamentoStone) VALUES (?, ?, ?, ?);";
        try (PreparedStatement ps = con.prepareStatement(insert)) {
            int i = 0;
            resolveFKNull(ps, ++i, obj.getConvenioCobrancaVO().getCodigo());
            resolveFKNull(ps, ++i, obj.getEmpresaVO().getCodigo());
            resolveFKNull(ps, ++i, obj.getOrdem());
            ps.setString(++i, obj.getTipoParcelamentoStone());
            ps.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    private void excluirPorEmpresa(Integer empresa) throws Exception {
        String sql = "DELETE FROM VendasOnlineConvenioTentativa WHERE empresa = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, empresa);
            ps.execute();
        }
    }

    public List<VendasOnlineConvenioTentativaVO> consultarPorEmpresa(Integer empresa, boolean somenteConvenioAtivo, int nivelMontarDados) throws Exception {
        if (UteisValidacao.emptyNumber(empresa)) {
            return new ArrayList<>();
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("cp.* \n");
        sql.append("from VendasOnlineConvenioTentativa cp \n");
        sql.append("inner join conveniocobranca cc on cc.codigo = cp.conveniocobranca \n");
        sql.append("where cp.empresa = ").append(empresa).append(" \n");
        if (somenteConvenioAtivo) {
            sql.append("and cc.situacao = ").append(SituacaoConvenioCobranca.ATIVO.getCodigo()).append(" \n");
        }
        sql.append("order by cp.ordem \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                return montarDadosConulta(rs, nivelMontarDados, this.con);
            }
        }
    }

    private List<VendasOnlineConvenioTentativaVO> montarDadosConulta(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        List<VendasOnlineConvenioTentativaVO> lista = new ArrayList<>();
        while (rs.next()) {
            lista.add(montarDados(rs, nivelMontarDados, con));
        }
        return lista;
    }

    private VendasOnlineConvenioTentativaVO montarDados(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        VendasOnlineConvenioTentativaVO obj = new VendasOnlineConvenioTentativaVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.getConvenioCobrancaVO().setCodigo(rs.getInt("convenioCobranca"));
        obj.getEmpresaVO().setCodigo(rs.getInt("empresa"));
        obj.setOrdem(rs.getInt("ordem"));
        obj.setTipoParcelamentoStone(rs.getString("tipoParcelamentoStone"));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            montarDadosConvenioCobranca(obj, con);
            montarDadosEmpresa(obj, con);
        }
        return obj;
    }

    private void montarDadosConvenioCobranca(VendasOnlineConvenioTentativaVO obj, Connection con) throws Exception {
        if (!UteisValidacao.emptyNumber(obj.getConvenioCobrancaVO().getCodigo())) {
            ConvenioCobranca dao = new ConvenioCobranca(con);
            obj.setConvenioCobrancaVO(dao.consultarPorChavePrimaria(obj.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            dao = null;
        }
    }

    private void montarDadosEmpresa(VendasOnlineConvenioTentativaVO obj, Connection con) throws Exception {
        if (!UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())) {
            Empresa dao = new Empresa(con);
            obj.setEmpresaVO(dao.consultarPorChavePrimaria(obj.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA));
            dao = null;
        }
    }

}
