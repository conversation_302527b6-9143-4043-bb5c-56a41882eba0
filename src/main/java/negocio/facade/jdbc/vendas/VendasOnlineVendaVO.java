package negocio.facade.jdbc.vendas;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.financeiro.TransacaoVO;
import servicos.vendasonline.dto.RetornoVendaTO;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 30/09/2022
 */
public class VendasOnlineVendaVO extends SuperVO {

    private Integer codigo;
    private Date dataRegistro;
    private PessoaVO pessoaVO;
    private String dados;
    private TransacaoVO transacaoVO;
    private BoletoVO boletoVO;
    private PixVO pixVO;

    public VendasOnlineVendaVO() {

    }

    public VendasOnlineVendaVO(RetornoVendaTO retornoVendaTO) {
        this.setPessoaVO(retornoVendaTO.getClienteVO().getPessoa());
        this.setDados(retornoVendaTO.toJSONVendaVendasOnline().toString());
        this.setTransacaoVO(retornoVendaTO.getTransacaoVO());
        this.setBoletoVO(retornoVendaTO.getBoletoVO());
        this.setPixVO(retornoVendaTO.getPixVO());
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public String getDados() {
        if (dados == null) {
            dados = "";
        }
        return dados;
    }

    public void setDados(String dados) {
        this.dados = dados;
    }

    public TransacaoVO getTransacaoVO() {
        if (transacaoVO == null) {
            transacaoVO = new TransacaoVO();
        }
        return transacaoVO;
    }

    public void setTransacaoVO(TransacaoVO transacaoVO) {
        this.transacaoVO = transacaoVO;
    }

    public BoletoVO getBoletoVO() {
        if (boletoVO == null) {
            boletoVO = new BoletoVO();
        }
        return boletoVO;
    }

    public void setBoletoVO(BoletoVO boletoVO) {
        this.boletoVO = boletoVO;
    }

    public PixVO getPixVO() {
        if (pixVO == null) {
            pixVO = new PixVO();
        }
        return pixVO;
    }

    public void setPixVO(PixVO pixVO) {
        this.pixVO = pixVO;
    }
}
