package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.RelatorioOrcamentarioConfigVO;
import negocio.comuns.financeiro.RelatorioOrcamentarioValoresPrevisaoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Empresa;
import negocio.interfaces.financeiro.RelatorioOrcamentarioConfigInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class RelatorioOrcamentarioConfig extends SuperEntidade implements RelatorioOrcamentarioConfigInterfaceFacade {

    public RelatorioOrcamentarioConfig() throws Exception {
        super();
    }

    public RelatorioOrcamentarioConfig(Connection conexao) throws Exception {
        super(conexao);
    }

    private Boolean existePrevisaoMesAnoEmpresa(RelatorioOrcamentarioConfigVO obj) throws SQLException {
        String sql = " SELECT * FROM relatorioorcamentario WHERE empresa = ? AND mes = ? AND ano = ? " + (UteisValidacao.emptyNumber(obj.getCodigo()) ? "" : " AND codigo != "+obj.getCodigo());
        PreparedStatement stm = con.prepareStatement(sql);
        int i = 1;
        stm.setInt(i++, obj.getEmpresa().getCodigo());
        stm.setInt(i++, obj.getMes().getCodigo());
        stm.setInt(i++, obj.getAno());

        ResultSet resultSet = stm.executeQuery();
        return resultSet.next();
    }

    public Integer obterIdRelatorioOrcamentarioPorMesAnoEmpresa(Integer idEmpresa, String mesAno) throws SQLException {
        String sql = " SELECT codigo FROM relatorioorcamentario WHERE empresa = ? AND mes = ? AND ano = ? ;";
        PreparedStatement stm = con.prepareStatement(sql);
        int i = 1;
        stm.setInt(i++, idEmpresa);
        stm.setInt(i++, Integer.parseInt(mesAno.substring(0, 2)));
        stm.setInt(i++, Integer.parseInt(mesAno.substring(3)));

        ResultSet resultSet = stm.executeQuery();
        if(resultSet.next()) {
            return resultSet.getInt("codigo");
        }
        return 0;
    }

    @Override
    public void incluir(RelatorioOrcamentarioConfigVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
            obj.setCodigo(obterUltimoIdCadastrado());
            con.setAutoCommit(false);
            incluirPrevisao(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            if (obj.getCodigo() != 0) {
                excluir(obj);
            }
            obj.setCodigo(0);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public Integer obterUltimoIdCadastrado() throws Exception {
        String str = "SELECT codigo FROM relatorioorcamentario ORDER BY codigo DESC LIMIT 1;";
        PreparedStatement stm = con.prepareStatement(str);
        ResultSet tabelaResultado = stm.executeQuery();
        if (tabelaResultado.next()) {
            return tabelaResultado.getInt("codigo");
        } else {
            return null;
        }
    }

    @Override
    public void incluirSemCommit(RelatorioOrcamentarioConfigVO obj) throws Exception {
        obj.validarDados();
        if (existePrevisaoMesAnoEmpresa(obj)) {
            throw new ConsistirException("Já existe uma meta para esta empresa no mês informado.");
        }
        obj.realizarUpperCaseDados();
        PreparedStatement sql = con.prepareStatement("INSERT INTO relatorioorcamentario "
                + "(descricao, empresa, mes, ano) VALUES (?, ?, ?, ?)");
        sql.setString(1, obj.getDescricao());
        sql.setInt(2, obj.getEmpresa().getCodigo());
        sql.setInt(3, obj.getMes().getCodigo());
        sql.setInt(4, obj.getAno());
        sql.execute();
        obj.setNovoObj(false);
    }

    private void incluirPrevisao(RelatorioOrcamentarioConfigVO obj) throws Exception {
        for (RelatorioOrcamentarioValoresPrevisaoVO mf : obj.getValores()) {
            mf.setRelatorioOrcamentarioConfigVO(obj.getCodigo());
            getFacade().getRelatorioOrcamentarioConfigPrevisao().incluirSemCommit(mf);
        }
    }

    @Override
    public void alterar(RelatorioOrcamentarioConfigVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void alterarSemCommit(RelatorioOrcamentarioConfigVO obj) throws Exception {
        obj.validarDados();
        if (existePrevisaoMesAnoEmpresa(obj)) {
            throw new ConsistirException("Já existe uma meta para esta empresa no mês informado. ");
        }
        obj.realizarUpperCaseDados();
        PreparedStatement sql = con.prepareStatement("UPDATE relatorioorcamentario SET "
                + "descricao=?, empresa=?,mes=?, ano=? WHERE codigo = ?");
        sql.setString(1, obj.getDescricao());
        sql.setInt(2, obj.getEmpresa().getCodigo());
        sql.setInt(3, obj.getMes().getCodigo());
        sql.setInt(4, obj.getAno());
        sql.setInt(5, obj.getCodigo());
        
        sql.execute();
        alterarPrevisao(obj);
    }

    private void alterarPrevisao(RelatorioOrcamentarioConfigVO obj) throws Exception {
        getFacade().getRelatorioOrcamentarioConfigPrevisao().excluirSemCommit(obj.getCodigo());
        incluirPrevisao(obj);
    }

    @Override
    public void excluir(RelatorioOrcamentarioConfigVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluirSemCommit(RelatorioOrcamentarioConfigVO obj) throws Exception {
        PreparedStatement sqlExcluir = con.prepareStatement("DELETE FROM relatorioorcamentario WHERE codigo = ?");
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    @Override
    public RelatorioOrcamentarioConfigVO consultarPorChavePrimaria(Integer codigo) throws Exception {
        StringBuilder str = new StringBuilder();
        str.append("SELECT * FROM relatorioorcamentario WHERE codigo = ").append(codigo);
        PreparedStatement stm = con.prepareStatement(str.toString());
        ResultSet tabelaResultado = stm.executeQuery();
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, this.con);
        } else {
            return new RelatorioOrcamentarioConfigVO();
        }
    }

    public List<RelatorioOrcamentarioConfigVO> montarDadosConsulta(ResultSet tabelaResultado, Connection con) throws Exception {
        List<RelatorioOrcamentarioConfigVO> vetResultado = new ArrayList<RelatorioOrcamentarioConfigVO>();
        while (tabelaResultado.next()) {
            vetResultado.add(montarDados(tabelaResultado, con));
        }
        return vetResultado;
    }

    public RelatorioOrcamentarioConfigVO montarDados(ResultSet dadosSQL, Connection con) throws Exception {
        RelatorioOrcamentarioConfigVO obj = montarDadosBasico(dadosSQL);
        montarEmpresa(obj, con);
        montarListaPrevisao(obj, con);
        return obj;
    }

    private RelatorioOrcamentarioConfigVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        RelatorioOrcamentarioConfigVO obj = new RelatorioOrcamentarioConfigVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setEmpresa(new EmpresaVO());
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.setMes(Mes.getMesPeloCodigo(dadosSQL.getInt("mes")));
        obj.setAno(dadosSQL.getInt("ano"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        return obj;
    }

    public void montarEmpresa(RelatorioOrcamentarioConfigVO obj, Connection con) throws Exception {
        Empresa empresa = new Empresa(con);
        obj.setEmpresa(empresa.consultarPorChavePrimaria(
                obj.getEmpresa().getCodigo(),
                Uteis.NIVELMONTARDADOS_MINIMOS));
    }

    public void montarListaPrevisao(RelatorioOrcamentarioConfigVO obj, Connection con) throws Exception {
        RelatorioOrcamentarioConfigPrevisao previsaoDao = new RelatorioOrcamentarioConfigPrevisao(con);
        List<RelatorioOrcamentarioValoresPrevisaoVO> listaPrevisao = new ArrayList<>();
        List<PlanoContaTO> planoDeContas = getFacade().getPlanoConta().consultarTodos();
        for (PlanoContaTO planoContaTO: planoDeContas){
            listaPrevisao.add(previsaoDao.consultarIdrelatorioOrcamentarioConfigIdPlanoConta(obj.getCodigo(), planoContaTO));
        }
        obj.setValores(listaPrevisao);
    }

    public String consultarJSON(Integer empresa) throws Exception {
        JSONObject aaData = new JSONObject();
        ResultSet rs = getRS(empresa);
        JSONArray lista = new JSONArray();
        while (rs.next()) {
            JSONArray itemLista = new JSONArray();
            itemLista.put(rs.getString("codigo"));
            itemLista.put(rs.getString("nomeEmpresa"));
            itemLista.put(
                    rs.getString("ano") + "/" +
                          (rs.getString("mes").length() < 2 ? "0" : "") +
                          rs.getString("mes")
            );
            itemLista.put(rs.getString("descricao"));
            lista.put(itemLista);
        }
        aaData.put("aaData", lista);
        return aaData.toString();
    }

    private ResultSet getRS(Integer empresa) throws SQLException {
        StringBuilder sql = new StringBuilder(
                "SELECT ro.codigo, ro.descricao, em.nome as nomeEmpresa, ro.empresa, ro.ano, ro.mes  \n"
                + " FROM relatorioorcamentario ro \n"
                + " LEFT JOIN empresa em ON em.codigo = ro.empresa \n"
        );
        if (empresa != 0) {
            sql.append(" WHERE ro.empresa = ").append(empresa).append(";");
        }
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        return sqlConsultar.executeQuery();
    }

}
