/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.arquitetura;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;

import br.com.pactosolucoes.bi.dto.DadosGrupoRiscoDTO;
import br.com.pactosolucoes.bi.dto.FiltroDTO;
import br.com.pactosolucoes.bi.dto.GrupoRiscoClientesDTO;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.ZonaChurnEnum;
import negocio.comuns.arquitetura.RiscoChurnDTO;
import negocio.comuns.arquitetura.RiscoVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.*;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 *
 * <AUTHOR>
 */
public class Risco extends SuperEntidade {

    public Risco() throws Exception {
        super();
    }

    public Risco(Connection conexao) throws Exception {
        super(conexao);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>RiscoVO</code>.
     */
    public RiscoVO novo() throws Exception {
        incluir(getIdEntidade());
        RiscoVO obj = new RiscoVO();
        return obj;
    }

    public void incluir(RiscoVO obj) throws Exception {
        incluir(obj, true);
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>RiscoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>RiscoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(RiscoVO obj, boolean controleAcesso) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj, controleAcesso);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>RiscoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>RiscoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluirSemCommit(RiscoVO obj, boolean controleAcesso) throws Exception {
        try {
            if (controleAcesso) {
                incluir(getIdEntidade());
            }
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO Risco( cliente, colaborador, empresa, nomeCliente, matriculaCliente, peso, foneCliente, dia ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            if (obj.getClienteVO().getCodigo().intValue() != 0) {
                sqlInserir.setInt(1, obj.getClienteVO().getCodigo().intValue());
            } else {
                sqlInserir.setNull(1, 0);
            }
            if (obj.getColaboradorVO().getCodigo().intValue() != 0) {
                sqlInserir.setInt(2, obj.getColaboradorVO().getCodigo().intValue());
            } else {
                sqlInserir.setNull(2, 0);
            }
            if (obj.getEmpresaVO().getCodigo().intValue() != 0) {
                sqlInserir.setInt(3, obj.getEmpresaVO().getCodigo().intValue());
            } else {
                sqlInserir.setNull(3, 0);
            }
            sqlInserir.setString(4, obj.getNomeCliente());
            sqlInserir.setString(5, obj.getMatriculaCliente());
            sqlInserir.setInt(6, obj.getPeso().intValue());
            sqlInserir.setString(7, obj.getFoneCliente());
            sqlInserir.setTimestamp(8, Uteis.getDataJDBCTimestamp(obj.getDia()));
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>CidadeVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>CidadeVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(RiscoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE Risco set cliente=?, colaborador=?, empresa=?, nomeCliente=?, matriculaCliente=?, peso=?, foneCliente=?, dia = ? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            if (obj.getClienteVO().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(1, obj.getClienteVO().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(1, 0);
            }
            if (obj.getColaboradorVO().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(2, obj.getColaboradorVO().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            if (obj.getEmpresaVO().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(3, obj.getEmpresaVO().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(3, 0);
            }
            sqlAlterar.setString(4, obj.getNomeCliente());
            sqlAlterar.setString(5, obj.getMatriculaCliente());
            sqlAlterar.setInt(6, obj.getPeso().intValue());
            sqlAlterar.setString(7, obj.getFoneCliente());
            sqlAlterar.setTimestamp(8, Uteis.getDataJDBCTimestamp(obj.getDia()));
            sqlAlterar.setInt(9, obj.getCodigo().intValue());
            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>CidadeVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>CidadeVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(RiscoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluir(getIdEntidade());
            String sql = "DELETE FROM Risco WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>CidadeVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluirTodoRegistro() throws Exception {
        try {
            String sql = "DELETE FROM Risco";
            Statement sqlExcluir = con.createStatement();
            sqlExcluir.execute(sql);
        } catch (Exception e) {
            throw e;
        } finally {
            Uteis.reinicializarSequencePostgreSQL("risco");
        }
    }

    public void excluirPorListaCliente(List<ClienteVO> listaClientes) throws Exception {
        try {
            for (ClienteVO clienteVO : listaClientes) {
                String sql = "DELETE FROM Risco where cliente = " + clienteVO.getCodigo();
                Statement sqlExcluir = con.createStatement();
                sqlExcluir.execute(sql);
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>PeriodoAcessoCliente</code> através do valor do atributo
     * <code>Integer cliente</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PeriodoAcessoClienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoCliente(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Risco WHERE cliente = " + valorConsulta.intValue() + " ORDER BY cliente";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>PeriodoAcessoCliente</code> através do valor do atributo
     * <code>Integer cliente</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PeriodoAcessoClienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoColaborador(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Risco WHERE colaborador = " + valorConsulta.intValue() + " ORDER BY colaborador";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>PeriodoAcessoCliente</code> através do valor do atributo
     * <code>Integer cliente</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PeriodoAcessoClienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarClientePorVinculoColaborador(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT DISTINCT(risco.cliente), risco.colaborador, risco.codigo, empresa, nomeCliente, matriculaCliente, peso, foneCliente, dia FROM risco "
                + "INNER JOIN vinculo ON vinculo.cliente = risco.cliente"
                + "WHERE vinculo.colaborador =" + valorConsulta + " and risco.empresa = " + empresa + " ORDER BY peso DESC, nomeCliente";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List<RiscoVO> consultarClientePorVinculoColaborador(String key, List<ColaboradorVO> lista, Integer empresa,
                                                               List<Integer> listaPesos, boolean ativoSemAtestadoSemCarencia, boolean controlarAcesso) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        FiltroDTO filtroDTO = getFacade().getBiMSService().obterBI(key, BIEnum.GRUPO_RISCO,
                getFiltroDTO(false, lista, empresa, ativoSemAtestadoSemCarencia, null, null, false, listaPesos), true);
        DadosGrupoRiscoDTO dto = JSONMapper.getObject(new JSONObject(filtroDTO.getJsonDados()), DadosGrupoRiscoDTO.class);
        return montarDadosConsultaMsBI(dto);
    }

    public HashMap<String, Integer> contarClientePorVinculoColaboradorClienteEmRiscoSemContato(String key, List<ColaboradorVO> lista, Integer empresa, Date dataInicial,
                                                                                               Date dataFinal, boolean ativosSemAtestadoSemCarencia,boolean atualizar) throws SQLException, Exception {
        FiltroDTO filtroDTO = getFacade().getBiMSService().obterBI(key, BIEnum.GRUPO_RISCO,
                getFiltroDTO(true, lista, empresa, ativosSemAtestadoSemCarencia, dataInicial, dataFinal, false, null), atualizar);
        return JSONMapper.getObject(new JSONObject(filtroDTO.getJsonDados()), DadosGrupoRiscoDTO.class).getQtdClientes();
    }

    private FiltroDTO getFiltroDTO(boolean contar, List<ColaboradorVO> lista, Integer empresa, boolean ativosSemAtestadoSemCarencia, Date dataInicial, Date dataFinal, boolean clientesSemContato, List<Integer> listaPesosRisco) {
        FiltroDTO filtroDTO = new FiltroDTO();
        filtroDTO.setNome(BIEnum.GRUPO_RISCO.name());
        JSONObject filtros = new JSONObject();
        filtros.put("empresa", empresa);
        JSONArray colaboradores = new JSONArray();
        if (lista != null) {
            for (ColaboradorVO co : lista) {
                colaboradores.put(co.getCodigo() + ";" + co.getColaboradorEscolhidoPendencia());
            }
        }
        filtros.put("colaboradores", colaboradores);
        JSONArray listaPesosRiscos = new JSONArray();
        if (listaPesosRisco != null) {
            for (Integer pesoRisco : listaPesosRisco) {
                listaPesosRiscos.put(pesoRisco);
            }
        }
        filtros.put("listaPesosRisco", listaPesosRiscos);
        filtros.put("ativoSemAtestadoSemCarencia", ativosSemAtestadoSemCarencia);
        filtros.put("contar", contar);
        filtros.put("clientesSemContato", clientesSemContato);
        filtros.put("dataInicial", dataInicial != null ? dataInicial.getTime() : null);
        filtros.put("dataFinal", dataFinal != null ? dataFinal.getTime() : null);
        filtroDTO.setFiltros(filtros.toString());
        return filtroDTO;
    }

    public String adicionarFiltros(List<ColaboradorVO> listaColaboradorVOs, int codigoEmpresa, Date dataInicial, Date dataFinal, boolean ativoSemAtestadoSemCarencia) throws Exception {
        int qtde = 0;
        StringBuilder sqlStr = new StringBuilder();
        if (ativoSemAtestadoSemCarencia) {
            sqlStr.append("INNER JOIN cliente ON cliente.codigo = risco.cliente\n");
            sqlStr.append("LEFT JOIN Contrato con ON con.pessoa = cliente.pessoa\n");
            sqlStr.append("LEFT JOIN contratooperacao co ON co.contrato = con.codigo AND (co.tipooperacao = 'AT' OR co.tipooperacao = 'CR') AND ");
            sqlStr.append(" ('").append(Uteis.getDataHoraJDBC(Calendario.hoje(), "00:00:00")).append("'  between co.datainicioefetivacaooperacao and co.datafimefetivacaooperacao)\n");
        }
        sqlStr.append("INNER JOIN vinculo ON risco.cliente = vinculo.cliente ");
        StringBuilder colaboradores = new StringBuilder();
        for (ColaboradorVO co : listaColaboradorVOs) {
            if (co.getColaboradorEscolhidoPendencia()) {
                if (qtde == 0) {
                    qtde++;
                    colaboradores.append(co.getCodigo());
                } else {
                    colaboradores.append(",").append(co.getCodigo());
                }
            }
        }
        if (!UteisValidacao.emptyString(colaboradores.toString())) {
            sqlStr.append(" AND vinculo.colaborador in (").append(colaboradores).append(")\n");
        }
        sqlStr.append(" LEFT JOIN(select distinct(risco.cliente) ");
        sqlStr.append(" from risco left join historicocontato on historicocontato.cliente = risco.cliente ");
        sqlStr.append(" INNER JOIN vinculo ON risco.cliente = vinculo.cliente ");

        if(codigoEmpresa != 0){
            sqlStr.append(" WHERE risco.empresa = " + codigoEmpresa);

        }else {
            sqlStr.append(" WHERE peso in (6,7,8) ");
        }

        sqlStr.append(" AND (historicocontato.dia >'" + Uteis.getDataHoraJDBC(dataInicial, "00:00:00") + "'  ");
        sqlStr.append("and historicocontato.dia <='" + Uteis.getDataHoraJDBC(dataFinal, "23:59:59") + "') and historicocontato.fase = 'RI') as riscoCliente ON riscoCliente.cliente = risco.cliente ");

        if(codigoEmpresa != 0 ){
            sqlStr.append(" WHERE risco.empresa = " + codigoEmpresa + " and peso in (6,7,8) ");
        }else{
            sqlStr.append(" WHERE peso in (6,7,8) ");
        }

        // filtra a consulta pelos colaboradores
        if (!UteisValidacao.emptyString(colaboradores.toString())) {
            sqlStr.append(" AND vinculo.colaborador in (").append(colaboradores).append(") ");
        }

        //todos os clientes com contatos que não estejam dentro do intervalo dos 15 dias com fase Grupo Risco
        //ou então todos os clientes que possuem contatos durante o intervalo de 15 dias mas que não seja da fase Grupo Risco
        sqlStr.append(" and (historicocontato.dia <'" + Uteis.getDataHoraJDBC(dataInicial, "00:00:00") + "' ");
        sqlStr.append(" or historicocontato.dia >'" + Uteis.getDataHoraJDBC(dataFinal, "23:59:59") + "' or historicocontato.dia is null ");
        sqlStr.append("or (historicocontato.dia >'" + Uteis.getDataHoraJDBC(dataInicial, "00:00:00") + "'  ");
        sqlStr.append("and historicocontato.dia<='" + Uteis.getDataHoraJDBC(dataFinal, "23:59:59") + "') and historicocontato.fase != 'RI')");
        //não incluir os clientes com contato dos ultimos 15 dias do tipo Grupo Risco
        sqlStr.append(" and riscoCliente is null\n");
        qtde = 0;
        for (ColaboradorVO co : listaColaboradorVOs) {
            if (co.getColaboradorEscolhidoPendencia()) {
                if (qtde == 0) {
                    qtde++;
                    sqlStr.append(" AND (");
                } else {
                    sqlStr.append(" OR ");
                }
                sqlStr.append("vinculo.colaborador = ").append(co.getCodigo().intValue());
            }
        }
        sqlStr.append(qtde > 0 ? ")\n" : "");
        if (ativoSemAtestadoSemCarencia) {
            sqlStr.append("and co.codigo is null");
        }
        return sqlStr.toString();
    }

    public List<RiscoVO> consultarClienteEmRiscoSemContato(List<ColaboradorVO> listaColaboradorVOs, int codigoEmpresa, Date dataInicial, Date dataFinal, boolean ativoSemAtestadoSemCarencia) throws Exception {
        StringBuilder sqlStr = new StringBuilder("SELECT distinct(risco.cliente), risco.*  from risco");
        sqlStr.append(" LEFT JOIN historicocontato on historicocontato.cliente = risco.cliente ");
        sqlStr.append(" AND (historicocontato.dia >'").append(Uteis.getDataHoraJDBC(dataInicial, "00:00:00")).append("'  ");
        sqlStr.append(" AND historicocontato.dia <='").append(Uteis.getDataHoraJDBC(dataFinal, "23:59:59")).append("') and historicocontato.fase = 'RI' ");
        sqlStr.append(adicionarFiltros(listaColaboradorVOs, codigoEmpresa, dataInicial, dataFinal, ativoSemAtestadoSemCarencia));
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>PeriodoAcessoCliente</code> através do valor do atributo
     * <code>Integer cliente</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PeriodoAcessoClienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoEmpresa(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Risco WHERE empresa = " + valorConsulta.intValue() + " ORDER BY empresa";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /*
     * Consulta o grupo de risco do cliente pela matrícula.
     */
    public List<RiscoVO> consultarPorMatriculaCliente(String matriculaConsulta, String empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT r.* FROM Risco r ");
        sqlStr.append("inner join cliente cli on cli.codigo = r.cliente ");
        sqlStr.append("WHERE cli.codigomatricula = ");
        sqlStr.append(matriculaConsulta);
        sqlStr.append(" AND r.empresa = ");
        sqlStr.append(empresa);
        sqlStr.append(" ORDER BY matriculaCliente");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString());
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por realizar uma consulta de <code>PeriodoAcessoCliente</code> através do valor do atributo
     * <code>Integer cliente</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PeriodoAcessoClienteVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoColaboradorEmpresa(Integer colaborador, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Risco WHERE colaborador = " + colaborador.intValue() + " and empresa = " + empresa.intValue() + " ORDER BY peso desc";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>RiscoVO</code> resultantes da consulta.
     */
    public static List<RiscoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<RiscoVO> vetResultado = new ArrayList<>();
        while (tabelaResultado.next()) {
            RiscoVO obj = montarDados(null, tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public List<RiscoVO> montarDadosConsultaMsBI(DadosGrupoRiscoDTO dados) {
        List<RiscoVO> vetResultado = new ArrayList<>();
        for (GrupoRiscoClientesDTO dado : dados.getListaClientes()) {
            RiscoVO obj = new RiscoVO();
            obj.setNovoObj(false);
            obj.setCodigo(dado.getCodigo());
            obj.getClienteVO().setCodigo(dado.getCliente());
            obj.getColaboradorVO().setCodigo(dado.getColaborador());
            obj.getEmpresaVO().setCodigo(dado.getEmpresa());
            obj.setNomeCliente(dado.getNomeCliente());
            obj.setMatriculaCliente(dado.getMatriculaCliente());
            obj.setFoneCliente(dado.getFoneCliente());
            obj.setPeso(dado.getPeso());
            obj.setDia(Calendario.getInstance(dado.getDia()).getTime());
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static RiscoVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        RiscoVO obj = new RiscoVO();
        obj.setNovoObj(false);
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.getClienteVO().setCodigo(dadosSQL.getInt("cliente"));
        obj.getColaboradorVO().setCodigo(dadosSQL.getInt("colaborador"));
        obj.getEmpresaVO().setCodigo(dadosSQL.getInt("empresa"));
        obj.setNomeCliente(dadosSQL.getString("nomeCliente"));
        obj.setMatriculaCliente(dadosSQL.getString("matriculacliente"));
        obj.setFoneCliente(dadosSQL.getString("foneCliente"));
        obj.setPeso(new Integer(dadosSQL.getInt("peso")));
        obj.setDia((dadosSQL.getTimestamp("dia")));
        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>RiscoVO</code>.
     * @return  O objeto da classe <code>RiscoVO</code> com os dados devidamente montados.
     */
    public static RiscoVO montarDados(RiscoVO obj, ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        if(obj == null){
            obj = montarDadosBasico(dadosSQL);
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        montarDadosCliente(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        montarDadosColaborador(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return obj;
    }

    public static void montarDadosCliente(RiscoVO obj, int nivelMontarDados) throws Exception {
        if (obj.getClienteVO().getCodigo().intValue() == 0) {
            obj.setClienteVO(new ClienteVO());
            return;
        }
        obj.setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(obj.getClienteVO().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS));
    }

    public static void montarDadosColaborador(RiscoVO obj, int nivelMontarDados) throws Exception {
        if (obj.getColaboradorVO().getCodigo().intValue() == 0) {
            obj.setColaboradorVO(new ColaboradorVO());
            return;
        }
        obj.setColaboradorVO(getFacade().getColaborador().consultarPorChavePrimaria(obj.getColaboradorVO().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS));
    }

    public static void montarDadosEmpresa(RiscoVO obj, int nivelMontarDados) throws Exception {
        if (obj.getEmpresaVO().getCodigo().intValue() == 0) {
            obj.setEmpresaVO(new EmpresaVO());
            return;
        }
        obj.setEmpresaVO(getFacade().getEmpresa().consultarPorChavePrimaria(obj.getEmpresaVO().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS));
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>RiscoVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public RiscoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Risco WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Risco ).");
        }
        return (montarDados(null, tabelaResultado, nivelMontarDados));
    }

    /**
     * Operação responsável por obter o último valor gerado para uma chave primária.
     * É utilizada para obter o valor gerado pela SGBD para uma chave primária,
     * a apresentação do mesmo e a implementação de possíveis relacionamentos.
     */
    public Integer obterValorChavePrimariaCodigo() throws Exception {
        inicializar();
        String sqlStr = "SELECT MAX(codigo) FROM Risco";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        tabelaResultado.next();
        return (new Integer(tabelaResultado.getInt(1)));
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>RiscoVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public RiscoVO consultarPorCliente(Integer codigoCliente, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Risco WHERE cliente = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoCliente.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( Risco ).");
        }
        return (montarDados(null, tabelaResultado, nivelMontarDados));
    }

    public int consultarPesoPorCliente(Integer codigoCliente) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT peso FROM Risco WHERE cliente = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoCliente.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return 0;
        }
        return tabelaResultado.getInt("peso");

    }
    
    public int consultarNumeroRiscoAlto(Integer empresa) throws Exception {
        return contar("select count(codigo) from risco where peso > 5 and empresa = "+empresa, con);
    }


    public List<RiscoChurnDTO> gerarBiRiscoChurn(List<ColaboradorVO> listaColaboradorVOs, Integer empresa){
        List<RiscoChurnDTO> lista = new ArrayList<>();
        try {
            String colaboradores = "";
            for (ColaboradorVO co : listaColaboradorVOs) {
                if (co.getColaboradorEscolhidoPendencia()) {
                        colaboradores += "," + co.getCodigo();
                }
            }

            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT CASE \n");
            sql.append(" WHEN s.riscochurn BETWEEN 0 AND 59 THEN 'SEGURANCA' \n");
            sql.append(" WHEN s.riscochurn BETWEEN 60 AND 69 THEN 'ATENCAO' \n");
            sql.append(" WHEN s.riscochurn BETWEEN 70 AND 84 THEN 'RISCO' \n");
            sql.append(" WHEN s.riscochurn BETWEEN 85 AND 94 THEN 'CRITICA' \n");
            sql.append(" WHEN s.riscochurn BETWEEN 95 AND 100 THEN 'DESPEDIDA' \n");
            sql.append(" END AS zona, COUNT(distinct(s.codigocliente)) AS clientes \n");
            sql.append(" FROM situacaoclientesinteticodw s\n");
            if(!UteisValidacao.emptyString(colaboradores)){
                sql.append(" inner join vinculo v on v.cliente = s.codigocliente \n");
            }
            sql.append(" where s.situacao = 'AT' \n");
            if(!UteisValidacao.emptyNumber(empresa)){
                sql.append(" and empresacliente = ").append(empresa);
            }
            if(!UteisValidacao.emptyString(colaboradores)){
                sql.append("and v.colaborador in (").append(colaboradores.replaceFirst(",", ""));
                sql.append(") ");
            }
            sql.append(" GROUP BY zona");
            Statement stm = con.createStatement();
            ResultSet tabelaResultado = stm.executeQuery(sql.toString());
            Integer totalClientes = 0;
            Set<ZonaChurnEnum> zonasAdd = new HashSet<>(Arrays.asList(ZonaChurnEnum.values()));
            while (tabelaResultado.next()) {
                String zona = tabelaResultado.getString("zona");
                if(!UteisValidacao.emptyString(zona)){
                    RiscoChurnDTO obj = new RiscoChurnDTO();
                    obj.setClientes(tabelaResultado.getInt("clientes"));
                    obj.setZona(ZonaChurnEnum.valueOf(zona));
                    zonasAdd.remove(obj.getZona());
                    totalClientes += obj.getClientes();
                    lista.add(obj);
                }
            }

            for(RiscoChurnDTO obj : lista){
                obj.setPercentual(Uteis.arredondarForcando2CasasDecimais(
                        obj.getClientes().doubleValue() * 100.0) / totalClientes.doubleValue());
            }

            for(ZonaChurnEnum z : zonasAdd){
                RiscoChurnDTO obj = new RiscoChurnDTO();
                obj.setClientes(0);
                obj.setZona(z);
                obj.setPercentual(0.0);
                lista.add(obj);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return Ordenacao.ordenarLista(lista, "ordinalZona");
    }

    public List<RiscoVO> consultarListaChurn(List<ColaboradorVO> listaColaboradorVOs,
                                                           int codigoEmpresa, ZonaChurnEnum zona) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append(" select codigocliente, empresacliente,nomecliente, matricula, ");
        sqlStr.append(" telefonescliente, riscochurnlancamento, riscochurn from situacaoclientesinteticodw s ");
        sqlStr.append(" where s.situacao = 'AT' and empresacliente = ").append(codigoEmpresa);
        sqlStr.append(" and riscochurn between ").append(zona.getMin());
        sqlStr.append(" and ").append(zona.getMax());

        List<RiscoVO> vetResultado = new ArrayList<>();
        try (Statement stm = con.createStatement()) {
            try (ResultSet dadosSQL = stm.executeQuery(sqlStr.toString())) {
                while (dadosSQL.next()) {
                    RiscoVO obj = new RiscoVO();
                    obj.setNovoObj(false);
                    obj.setCodigo(0);
                    obj.getClienteVO().setCodigo(dadosSQL.getInt("codigocliente"));
                    obj.getEmpresaVO().setCodigo(dadosSQL.getInt("empresacliente"));
                    obj.setNomeCliente(dadosSQL.getString("nomeCliente"));
                    obj.setMatriculaCliente(dadosSQL.getString("matricula"));
                    obj.setFoneCliente(dadosSQL.getString("telefonescliente"));
                    obj.setPeso(Double.valueOf(dadosSQL.getDouble("riscochurn")).intValue());
                    obj.setDia((dadosSQL.getTimestamp("riscochurnlancamento")));
                    vetResultado.add(obj);
                }
            }
        }
        return vetResultado;
    }

}
