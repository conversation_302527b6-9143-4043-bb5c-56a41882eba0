package negocio.facade.jdbc.arquitetura;

import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.arquitetura.LogTotalPassVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.interfaces.arquitetura.LogTotalPassInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class LogTotalPass extends SuperEntidade implements LogTotalPassInterfaceFacade {
    protected static String idEntidade;

    public LogTotalPass() throws Exception {
        super();
    }

    public LogTotalPass(Connection con) throws Exception {
        super(con);
    }

    private Integer codigo;
    private Integer pessoa;
    private Date dataregistro;
    private String json;
    private String resposta;
    private BigDecimal tempo_resposta;
    private String tipo;
    private Integer empresa;
    private String ip;
    private Usuario usuario;
    private String origem;

    @Override
    public LogTotalPassVO incluir(LogTotalPassVO obj) throws Exception {
        String sql = "INSERT INTO logtotalpass(pessoa, dataregistro, uri, apikey, json, resposta, tempo_resposta, tipo, empresa, ip, origem, respostaapi) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            int i = 0;
            sqlInserir.setInt(++i, obj.getPessoa());
            sqlInserir.setTimestamp(++i, obj.getDataregistro());
            sqlInserir.setString(++i, obj.getUri());
            sqlInserir.setString(++i, obj.getApikey());
            sqlInserir.setString(++i, obj.getJson());
            sqlInserir.setString(++i, obj.getResposta());
            sqlInserir.setLong(++i, obj.getTempoResposta());
            sqlInserir.setString(++i, obj.getTipo());
            sqlInserir.setInt(++i, obj.getEmpresa());
            sqlInserir.setString(++i, obj.getIp());
            sqlInserir.setString(++i, obj.getOrigem());
            sqlInserir.setString(++i, obj.getRespostaApi());
            sqlInserir.execute();

            ResultSet generatedKeys = sqlInserir.getGeneratedKeys();
            if (generatedKeys.next()) {
                int codigoInserido = generatedKeys.getInt(1);

                LogTotalPassVO logTotalPass = new LogTotalPassVO();
                logTotalPass.setCodigo(codigoInserido);
                logTotalPass.setPessoa(obj.getPessoa());
                logTotalPass.setDataregistro(obj.getDataregistro());
                logTotalPass.setUri(obj.getUri());
                logTotalPass.setApikey(obj.getApikey());
                logTotalPass.setJson(obj.getJson());
                logTotalPass.setResposta(obj.getResposta());
                logTotalPass.setTempoResposta(obj.getTempoResposta());
                logTotalPass.setTipo(obj.getTipo());
                logTotalPass.setEmpresa(obj.getEmpresa());
                logTotalPass.setIp(obj.getIp());
                logTotalPass.setIp(obj.getOrigem());
                logTotalPass.setRespostaApi(obj.getRespostaApi());

                return logTotalPass;
            }
        }
        throw new Exception("Falha ao inserir o registro no banco de dados");
    }

    public List<LogTotalPassVO> consultarPorPessoaLogTotalPass(Integer pessoa) throws Exception {
        String sqlStr = "SELECT *, case when resposta = '204' then 'Sucesso' else 'Negado' end as status FROM LogTotalPass WHERE  pessoa = " + pessoa + " order by codigo desc";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        }
    }

    @Override
    public String consultarLogTotalPassJSON(Integer cliente, Integer nrPaginaUltimosAcessos) throws Exception {
        consultar(idEntidade);

        JSONObject aaData = new JSONObject();
        JSONArray valores = new JSONArray();

        StringBuilder sql = new StringBuilder("SELECT *, case when resposta = '204' then 'Sucesso' else 'Negado' end as status FROM LogTotalPass \n");
        sql.append(" WHERE pessoa = ? \n");
        sql.append(" ORDER BY codigo desc \n");
        if (nrPaginaUltimosAcessos != null) {
            sql.append(" offset 0 limit ").append(nrPaginaUltimosAcessos);
        }
        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setInt(1, cliente);
        ResultSet rs = stm.executeQuery();
        while (rs.next()) {
            JSONArray itemArray = new JSONArray();
            itemArray.put(Uteis.getDataAplicandoFormatacao(rs.getTimestamp("dataregistro"), "dd/MM/yy HH:mm"));
            itemArray.put(rs.getString("origem"));
            itemArray.put(rs.getString("tempo_resposta").concat("ms"));
            itemArray.put(rs.getString("resposta"));
            itemArray.put(rs.getString("status"));
            itemArray.put(rs.getString("uri"));
            itemArray.put(rs.getString("apikey"));
            itemArray.put(rs.getString("json"));
            itemArray.put(rs.getString("tipo"));
            itemArray.put(rs.getString("ip"));
            itemArray.put(rs.getString("respostaapi"));
            valores.put(itemArray);
        }
        aaData.put("aaData", valores);
        return aaData.toString();
    }

    public static List<LogTotalPassVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<LogTotalPassVO> vetResultado = new ArrayList<LogTotalPassVO>();
        while (tabelaResultado.next()) {
            LogTotalPassVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static LogTotalPassVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        LogTotalPassVO obj = montarDadosBasico(dadosSQL);
        return obj;
    }

    public static LogTotalPassVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        LogTotalPassVO obj = new LogTotalPassVO();
        obj.setNovoObj(new Boolean(false));
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setPessoa(new Integer(dadosSQL.getInt("pessoa")));
        obj.setUri(dadosSQL.getString("uri"));
        obj.setApikey(dadosSQL.getString("apikey"));
        obj.setDataregistro(dadosSQL.getTimestamp("dataRegistro"));
        obj.setJson(dadosSQL.getString("json"));
        obj.setTempoResposta(dadosSQL.getLong("tempo_resposta"));
        obj.setResposta(dadosSQL.getString("resposta"));
        obj.setEmpresa(dadosSQL.getInt("empresa"));
        obj.setIp(dadosSQL.getString("ip"));
        obj.setUsuario(dadosSQL.getInt("usuario"));
        obj.setTipo(dadosSQL.getString("tipo"));
        obj.setOrigem(dadosSQL.getString("origem"));
        obj.setStatus(dadosSQL.getString("status"));

        return obj;
    }


}
