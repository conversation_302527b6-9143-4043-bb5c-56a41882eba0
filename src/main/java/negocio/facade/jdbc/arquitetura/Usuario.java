package negocio.facade.jdbc.arquitetura;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoInfoMigracaoEnum;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.*;
import negocio.comuns.basico.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Criptografia;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.acesso.DadosAcessoOffline;
import negocio.facade.jdbc.basico.*;
import negocio.interfaces.arquitetura.UsuarioInterfaceFacade;
import negocio.interfaces.basico.ColaboradorInterfaceFacade;
import negocio.interfaces.basico.PessoaInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.File;
import java.net.URL;
import java.security.MessageDigest;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.text.Normalizer;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static controle.arquitetura.SuperControle.getRedirectLoginServidorLocal;
import static controle.arquitetura.SuperControle.getUrlLoginExterno;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos
 * dados da classe
 * <code>UsuarioVO</code>. Responsável por implementar operações como incluir,
 * alterar, excluir e consultar pertinentes a classe
 * <code>UsuarioVO</code>. Encapsula toda a interação com o banco de dados.
 *
 * @see UsuarioVO
 * @see SuperEntidade
 */
public class Usuario extends SuperEntidade implements UsuarioInterfaceFacade {
    
    public Usuario() throws Exception {
        super();
    }

    public Usuario(Connection conexao) throws Exception {
        super(conexao);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>UsuarioVO</code>.
     */
    @Override
    public UsuarioVO novo() throws Exception {
        incluir(getIdEntidade());
        return new UsuarioVO();
    }


    private void incluirNovoColaborador(UsuarioVO obj)throws Exception {

        ColaboradorInterfaceFacade colaborador = new Colaborador(this.con);
        if (obj.getTipoUsuario().equals("NC")) {
            PessoaInterfaceFacade pessoa = new Pessoa(this.con);
            pessoa.incluirConexaoInicializada(obj.getColaboradorVO().getPessoa(), con);
            pessoa = null;
            colaborador.incluirApartirDaTelaUsuarioSistemaSemCommit(obj.getColaboradorVO());
        }
        for (Object object:  obj.getUsuarioPerfilAcessoVOs()){
            UsuarioPerfilAcessoVO usuarioPerfilAcessoVO  = (UsuarioPerfilAcessoVO)object;
            if (usuarioPerfilAcessoVO.getEmpresa().getCodigo().intValue() != obj.getColaboradorVO().getEmpresa().getCodigo().intValue()){
                ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorCodigoPessoa(obj.getColaboradorVO().getPessoa().getCodigo(), usuarioPerfilAcessoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if ((colaboradorVO == null) || (colaboradorVO.getCodigo() == null) || (colaboradorVO.getCodigo() <= 0)){
                    obj.getColaboradorVO().setNovoObj(false);
                    obj.getColaboradorVO().setCodigo(null);
                    obj.getColaboradorVO().setEmpresa(usuarioPerfilAcessoVO.getEmpresa());
                    colaborador.incluirApartirDaTelaUsuarioSistemaSemCommit(obj.getColaboradorVO());
                }
            }
        }
        colaborador = null;

    }
    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>UsuarioVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>UsuarioVO</code> que será gravado no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    @Override
    public void incluir(UsuarioVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj, centralEventos);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void incluirSemCommit(UsuarioVO obj) throws Exception {
        incluirSemCommit(obj, false);
    }

    @Override
    public void incluirSemCommit(UsuarioVO obj, boolean centralEventos) throws Exception {
        if (centralEventos) {
//        		 incluirObj(getIdEntidade());
        } else {
            incluir(getIdEntidade());
        }

        UsuarioVO.validarDados(obj);
        validarOutroUsuarioComMesmoNome(obj);

        obj.realizarUpperCaseDados(true, con);

        incluirNovoColaborador(obj);

        String sql = "INSERT INTO Usuario( nome, username, senha, tipoUsuario, cliente, colaborador, "
                + "administrador, dataalteracaosenha, permiteAlterarPropriaSenha, serviceUsuario, serviceSenha, "
                + "pedirSenhaFuncionalidade, permissaoAlterarRPS, permiteExecutarProcessos, pin, permiteexclusaocliente, "
                + "tipoTw, perfilTw, statusTw, permiteAcionarEmergencia )"
                + " VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setString(1, obj.getNome());
            String userNameNormalized = Normalizer.normalize(obj.getUsername(), Normalizer.Form.NFD);
            userNameNormalized = userNameNormalized.replaceAll("[\\p{InCombiningDiacriticalMarks}]", "");
            sqlInserir.setString(2, userNameNormalized);
            obj.setSenha(Uteis.encriptar(obj.getSenha()));
            obj.setSenhaConfirmar(obj.getSenha());
            sqlInserir.setString(3, obj.getSenha());
            sqlInserir.setString(4, obj.getTipoUsuario());
            //converte chaves estrangeiras com valor zerado para NULL
            if (obj.getClienteVO().getCodigo() == 0) {
                sqlInserir.setNull(5, Types.NUMERIC);
            } else {
                sqlInserir.setInt(5, obj.getClienteVO().getCodigo());
            }
            if (obj.getColaboradorVO().getCodigo() == 0) {
                sqlInserir.setNull(6, Types.NUMERIC);
            } else {
                sqlInserir.setInt(6, obj.getColaboradorVO().getCodigo());
            }
            sqlInserir.setBoolean(7, obj.getAdministrador());
            sqlInserir.setTimestamp(8, Uteis.getDataJDBCTimestamp(obj.getDataUltimaAlteracaoSenha()));
            sqlInserir.setBoolean(9, obj.getPermiteAlterarPropriaSenha());

            sqlInserir.setString(10, obj.getServiceUsuario());
            if (!obj.getServiceSenha().isEmpty() && !obj.getServiceSenha().contains("==")) {
                obj.setServiceSenha(Criptografia.encrypt(obj.getServiceSenha(),
                        SuperControle.Crypt_KEY, SuperControle.Crypt_ALGORITM));
            }
            sqlInserir.setString(11, obj.getServiceSenha());
            sqlInserir.setBoolean(12, obj.isPedirSenhaFuncionalidade());
            sqlInserir.setBoolean(13, obj.isPermissaoAlterarRPS());
            sqlInserir.setBoolean(14, obj.isPermiteExecutarProcessos());

            if (obj.isAlterouPIN()) {
                sqlInserir.setString(15, Uteis.encriptar(obj.getPin()));
            } else {
                sqlInserir.setString(15, obj.getPin());
            }
            sqlInserir.setBoolean(16, obj.isPermiteExclusaoCliente());
            if (UteisValidacao.emptyNumber(obj.getTipoTw())) {
                sqlInserir.setNull(17, Types.NUMERIC);
            } else {
                sqlInserir.setInt(17, obj.getTipoTw());
            }
            if (UteisValidacao.emptyNumber(obj.getPerfilTw())) {
                sqlInserir.setNull(18, Types.NUMERIC);
            } else {
                sqlInserir.setInt(18, obj.getPerfilTw());
            }
            if (obj.getStatusTw() == null) {
                sqlInserir.setNull(19, Types.NUMERIC);
            } else {
                sqlInserir.setInt(19, obj.getStatusTw());
            }
            sqlInserir.setBoolean(20, obj.getPermiteAcionarBotaoPanico());
            sqlInserir.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
        UsuarioPerfilAcesso usuarioPerfilAcesso = new UsuarioPerfilAcesso(con);
        if (obj.getAdministrador()) {
            usuarioPerfilAcesso.incluirUsuarioPerfilAcessoAdministrador(obj.getCodigo(), obj.getUsuarioPerfilAcessoVOs());
        } else {
            usuarioPerfilAcesso.incluirUsuarioPerfilAcesso(obj.getCodigo(), obj.getUsuarioPerfilAcessoVOs());
        }
        usuarioPerfilAcesso = null;
        HorarioAcessoSistema has = new HorarioAcessoSistema(con);
        has.incluirHorarioAcessoSistemaUsuario(obj.getCodigo(), obj.getUsuarioHorarioAcessoSistemaVOs());
        has = null;
        processarRecursoPadraoNovoUsuario(false, false, obj.getUsuarioVO());
    }
    /* (non-Javadoc)
     * @see negocio.interfaces.arquitetura.PerfilAcessoInterfaceFacade#incluir(negocio.comuns.arquitetura.PerfilAcessoVO)
     */

    public void processarRecursoPadraoNovoUsuario(boolean controlarTransacao, boolean exception, UsuarioVO usuarioVO) throws Exception {
        Empresa empresaDAO;
        Usuario usuarioDAO;
        try {
            if (controlarTransacao) {
                this.con.setAutoCommit(false);
            }

            empresaDAO = new Empresa(this.con);
            usuarioDAO = new Usuario(this.con);

            if (usuarioVO == null ||
                    UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                usuarioVO = usuarioDAO.getUsuarioRecorrencia();
            }

            //adicionar o recurso empresa para todos os usuários
            StringBuilder sqlEmpresa = new StringBuilder();
            sqlEmpresa.append("select \n");
            sqlEmpresa.append("codigo, \n");
            sqlEmpresa.append("tiposInfoMigracaoPadrao \n");
            sqlEmpresa.append("from empresa \n");
            sqlEmpresa.append("where ativa \n");
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlEmpresa.toString(), con);
            while (rs.next()) {
                Integer codEmpresa = rs.getInt("codigo");
                String tiposInfoMigracaoPadrao = rs.getString("tiposInfoMigracaoPadrao");
                if (tiposInfoMigracaoPadrao == null) {
                    tiposInfoMigracaoPadrao = "";
                }

                List<TipoInfoMigracaoEnum> tiposAtivos = new ArrayList<>();
                for (String tipoStr : tiposInfoMigracaoPadrao.split(",")) {
                    if (!UteisValidacao.emptyString(tipoStr)) {
                        tiposAtivos.add(TipoInfoMigracaoEnum.valueOf(tipoStr));
                    }
                }

                List<TipoInfoMigracaoEnum> tiposDesativados = new ArrayList<>();
                for (TipoInfoMigracaoEnum tipoEnum : TipoInfoMigracaoEnum.values()) {
                    if (!tipoEnum.isRecursoPadrao()) {
                        continue;
                    }
                    boolean removido = true;
                    for (TipoInfoMigracaoEnum tipoHabi : tiposAtivos) {
                        if (tipoEnum.equals(tipoHabi)) {
                            removido = false;
                            break;
                        }
                    }
                    if (removido) {
                        tiposDesativados.add(tipoEnum);
                    }
                }

                for (TipoInfoMigracaoEnum tipoAdd : tiposAtivos) {
                    empresaDAO.alterarRecursoSistemaUsuarios(tipoAdd, true, codEmpresa, usuarioVO, true, this.con);
                }
                for (TipoInfoMigracaoEnum tipoRemover : tiposDesativados) {
                    empresaDAO.alterarRecursoSistemaUsuarios(tipoRemover, false, codEmpresa, usuarioVO, false, this.con);
                }
            }

            if (controlarTransacao) {
                this.con.commit();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            if (controlarTransacao) {
                this.con.rollback();
            }
            if (exception) {
                throw ex;
            }
        } finally {
            empresaDAO = null;
            if (controlarTransacao) {
                this.con.setAutoCommit(true);
            }
        }
    }

    public static void main(String[] args) {
        try {
            System.out.println(Uteis.encriptar("123456"));
        } catch (Exception ex) {

        }
    }

    @Override
    public void incluir(UsuarioVO obj) throws Exception {
        this.incluir(obj, false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>UsuarioVO</code>. Sempre utiliza a chave primária da classe como
     * atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>UsuarioVO</code> que será alterada no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void alterar(UsuarioVO obj, boolean centralEventos, boolean somenteAlterarSuaPropriaSenha,boolean alterarSemPermissao) throws Exception {
        try {
            con.setAutoCommit(false);
            if (centralEventos) {
//            	super.alterarObj(getIdEntidade());
            } else {
                //se o usuário possui somente a permissão de alterar sua própria senha (ver cadastro de usuário)
                //a senha de cadastro de usuário será desconsiderada.
                if (!somenteAlterarSuaPropriaSenha && !alterarSemPermissao) {
                    alterar(getIdEntidade());
                }
            }
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);

        }
    }

    public void alterarSemCommit(UsuarioVO obj) throws Exception {
        UsuarioVO.validarDados(obj);
        validarOutroUsuarioComMesmoNome(obj);
            obj.realizarUpperCaseDados(false, con);
            String sql = "UPDATE Usuario set nome=?, username=?, senha=?, tipoUsuario=?, cliente=?, "
                    + "colaborador=?, administrador=? , dataAlteracaoSenha =? ,permiteAlterarPropriaSenha=?, "
                + "serviceUsuario=?, serviceSenha=?, pedirSenhaFuncionalidade = ?, permissaoAlterarRPS = ?, permiteExecutarProcessos = ?, linguagem = ?, pin = ?, permiteExclusaoCliente = ?," +
                    " tipoTw = ?, perfilTw = ?, statusTw = ?, acessoPactoApp = ?, permiteAcionarEmergencia = ? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getNome());
            sqlAlterar.setString(2, obj.getUsername());

            if (!MessageDigest.isEqual(consultarSenhaPorCodigoUsuario(obj.getCodigo()).getBytes(), obj.getSenha().getBytes())) {
                obj.setSenha(Uteis.encriptar(obj.getSenha()));
            }
            obj.setSenhaConfirmar(obj.getSenha());
            sqlAlterar.setString(3, obj.getSenha());
            sqlAlterar.setString(4, obj.getTipoUsuario());
            //converte chaves estrangeiras com valor zerado para NULL
            if (obj.getClienteVO().getCodigo() == 0) {
                    sqlAlterar.setNull(5, Types.NUMERIC);
                } else {
                sqlAlterar.setInt(5, obj.getClienteVO().getCodigo());
                }
            if (obj.getColaboradorVO().getCodigo() == 0) {
                    sqlAlterar.setNull(6, Types.NUMERIC);
                } else {
                sqlAlterar.setInt(6, obj.getColaboradorVO().getCodigo());
                }
            sqlAlterar.setBoolean(7, obj.getAdministrador());
            sqlAlterar.setTimestamp(8, Uteis.getDataJDBCTimestamp(obj.getDataUltimaAlteracaoSenha()));
            sqlAlterar.setBoolean(9, obj.getPermiteAlterarPropriaSenha());

            sqlAlterar.setString(10, obj.getServiceUsuario());
            if (obj.getServiceSenha() != null && !obj.getServiceSenha().isEmpty() && !obj.getServiceSenha().contains("==")) {
                obj.setServiceSenha(Criptografia.encrypt(obj.getServiceSenha(),
                        SuperControle.Crypt_KEY, SuperControle.Crypt_ALGORITM));
            }
            sqlAlterar.setString(11, obj.getServiceSenha());
            sqlAlterar.setBoolean(12, obj.isPedirSenhaFuncionalidade());
            sqlAlterar.setBoolean(13, obj.isPermissaoAlterarRPS());
            sqlAlterar.setBoolean(14, obj.isPermiteExecutarProcessos());
            sqlAlterar.setString(15, obj.getLinguagem());
            if (obj.isAlterouPIN()) {
                sqlAlterar.setString(16, obj.getUsuarioPactoSolucoes() ? "" : Uteis.encriptar(obj.getPin()));
            } else {
                sqlAlterar.setString(16, obj.getUsuarioPactoSolucoes() ? "" : obj.getPin());
            }
            sqlAlterar.setBoolean(17, obj.isPermiteExclusaoCliente());
            if (UteisValidacao.emptyNumber(obj.getTipoTw())) {
                sqlAlterar.setNull(18, Types.NUMERIC);
            } else {
                sqlAlterar.setInt(18, obj.getTipoTw());
            }
            if (UteisValidacao.emptyNumber(obj.getPerfilTw())) {
                sqlAlterar.setNull(19, Types.NUMERIC);
            } else {
                sqlAlterar.setInt(19, obj.getPerfilTw());
            }
            if (obj.getStatusTw() == null) {
                sqlAlterar.setNull(20, Types.NUMERIC);
            } else {
                sqlAlterar.setInt(20, obj.getStatusTw());
            }
            sqlAlterar.setBoolean(21, obj.getAcessoPactoApp());
            sqlAlterar.setBoolean(22, obj.getPermiteAcionarBotaoPanico());
            sqlAlterar.setInt(23, obj.getCodigo());

            sqlAlterar.execute();
        }
        incluirColaboradorParaEmpresaPerfilAcesso(obj);
        getFacade().getUsuarioPerfilAcesso().alterarUsuarioPerfilAcesso(obj.getCodigo(), obj.getUsuarioPerfilAcessoVOs());
        getFacade().getHorarioAcessoSistema().alterarHorarioAcessoSistemaUsuario(obj.getCodigo(), obj.getUsuarioHorarioAcessoSistemaVOs());

        if ((obj.getColaboradorVO() != null) && (obj.getColaboradorVO().getCodigo() != null) &&
                (obj.getColaboradorVO().getCodigo() > 0) && (obj.isAlterouSenha()) &&
                (obj.getColaboradorVO().getSituacao().equals("AT"))) {
            DadosAcessoOffline dadosAcessoOffline = new DadosAcessoOffline(con);
            dadosAcessoOffline.preencherDadosSemCommit((String) JSFUtilities.getFromSession(JSFUtilities.KEY), obj.getColaboradorVO().getPessoa().getCodigo());
            dadosAcessoOffline = null;
        }
    }

    private void incluirColaboradorParaEmpresaPerfilAcesso(UsuarioVO usuarioVO)throws  Exception{
        if ((usuarioVO.getColaboradorVO() == null) || (usuarioVO.getColaboradorVO().getCodigo() == null) || (usuarioVO.getColaboradorVO().getCodigo() <= 0)) {
          return;
        }
        List<UsuarioPerfilAcessoVO> listaPerfilAcessoBanco = getFacade().getUsuarioPerfilAcesso().consultarUsuarioPerfilAcesso(usuarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        List<UsuarioPerfilAcessoVO> listaExcluir = new ArrayList<UsuarioPerfilAcessoVO>();
        List<UsuarioPerfilAcessoVO> listaIncluir = new ArrayList<UsuarioPerfilAcessoVO>();
        // pegar os perfils que que foram excluidos.
        for(UsuarioPerfilAcessoVO usuPerfil: listaPerfilAcessoBanco){
            UsuarioPerfilAcessoVO perfilMemoria = null;
            boolean achou = false;
            for (Object object : usuarioVO.getUsuarioPerfilAcessoVOs()){
                perfilMemoria = (UsuarioPerfilAcessoVO)object;
                if ((perfilMemoria.getEmpresa().getCodigo().equals(usuPerfil.getEmpresa().getCodigo())) &&
                   (perfilMemoria.getPerfilAcesso().getCodigo().equals(usuPerfil.getPerfilAcesso().getCodigo()))){
                    achou = true;
                    break;
                }
            }
            if (!achou){
                listaExcluir.add(usuPerfil);
            }
        }
        // pegar os perfils que foram incluidos.
        for (Object object:  usuarioVO.getUsuarioPerfilAcessoVOs()){
            UsuarioPerfilAcessoVO usuarioPerfilAcessoVO  = (UsuarioPerfilAcessoVO)object;
            if ((usuarioPerfilAcessoVO.getCodigo() == null) || (usuarioPerfilAcessoVO.getCodigo() <= 0)){
                listaIncluir.add(usuarioPerfilAcessoVO);
            }
        }
        // inativar os colaborores que não tem permissão de acesso na empresa correspondente.
        for(UsuarioPerfilAcessoVO usuarioPerfilAcessoVO: listaExcluir){
            ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorCodigoPessoa(usuarioVO.getColaboradorVO().getPessoa().getCodigo(), usuarioPerfilAcessoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            colaboradorVO.setSituacao("NA");
            getFacade().getColaborador().alterarSituacao("NA", colaboradorVO.getCodigo());
        }

        for(UsuarioPerfilAcessoVO usuarioPerfilAcessoVO: listaIncluir){
            // Incluir um novo colaborador para o novo perfil de acesso incluído.
            ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorCodigoPessoa(usuarioVO.getColaboradorVO().getPessoa().getCodigo(), usuarioPerfilAcessoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if ((colaboradorVO == null) || (colaboradorVO.getCodigo() == null) || (colaboradorVO.getCodigo() <= 0)){
                usuarioVO.getColaboradorVO().setNovoObj(false);
                usuarioVO.getColaboradorVO().setCodigo(null);
                usuarioVO.getColaboradorVO().setEmpresa(usuarioPerfilAcessoVO.getEmpresa());
                getFacade().getColaborador().incluirApartirDaTelaUsuarioSistemaSemCommit(usuarioVO.getColaboradorVO());
            }else{
                colaboradorVO.setSituacao("AT");
                getFacade().getColaborador().alterarSituacao("AT", colaboradorVO.getCodigo());
            }
        }


    }

    /* (non-Javadoc)
     * @see negocio.interfaces.arquitetura.PerfilAcessoInterfaceFacade#incluir(negocio.comuns.arquitetura.PerfilAcessoVO)
     */

    @Override
    public void alterar(UsuarioVO obj) throws Exception {
        this.alterar(obj, false, false,false);
    }
    @Override
    public void alterarSemPermissao(UsuarioVO obj) throws Exception {
        this.alterar(obj, false, false,true);
    }
    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>UsuarioVO</code>. Sempre utiliza a chave primária da classe como
     * atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>UsuarioVO</code> que será alterada no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void alterarNomeUsuario(UsuarioVO obj) throws Exception {
        try {
            String sql = "UPDATE Usuario set nome=?  WHERE ((codigo = ?))";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setString(1, obj.getNome());
                sqlAlterar.setInt(2, obj.getCodigo());
                sqlAlterar.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void alterarShowModalInativar(UsuarioVO obj) throws Exception {
        try {
            String sql = "UPDATE Usuario set showmodalinativar=?, dataexibirmodalinativarusuers=?   WHERE ((codigo = ?))";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setBoolean(1, obj.isModalInativar());
                sqlAlterar.setDate(2, Uteis.getDataJDBC(obj.getExibirModalInativarUsersHoje()));
                sqlAlterar.setInt(3, obj.getCodigo());
                sqlAlterar.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void alterarShowModalPlanos(UsuarioVO obj) throws Exception {
        String sql = "UPDATE Usuario SET showmodalplanos=?, dataexibirmodalplanos=? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setBoolean(1, obj.isShowModalPlanos());
            sqlAlterar.setDate(2, Uteis.getDataJDBC(obj.getDataExibirModalPlanos()));
            sqlAlterar.setInt(3, obj.getCodigo());
            sqlAlterar.execute();
        }
    }



    public void registrarUltimoLoginAcessoAgora(UsuarioVO obj) throws Exception {
        try {
            String sql = "UPDATE Usuario set ultimoacesso = ?  WHERE ((codigo = ?))";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setDate(1, Uteis.getDataJDBC(Calendario.hoje()));
                sqlAlterar.setInt(2, obj.getCodigo());
                sqlAlterar.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void alterarUsernameSenha(UsuarioVO obj) throws Exception {
        try {
            obj.realizarUpperCaseDados(false, con);
            String sql = "UPDATE Usuario set username=?, senha=?  WHERE ((codigo = ?))";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setString(1, obj.getUsername());
                sqlAlterar.setString(2, Uteis.encriptar(obj.getSenha()));
                sqlAlterar.setInt(3, obj.getCodigo());
                sqlAlterar.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }
    public void alterarSenhaUsuario(UsuarioVO obj, boolean somenteAlterarSuaPropriaSenha) throws Exception {
        alterarSenhaUsuario(obj, somenteAlterarSuaPropriaSenha, null);
    }
    public void alterarSenhaUsuario(UsuarioVO obj, boolean somenteAlterarSuaPropriaSenha, String senhaCriptografada) throws Exception {
        //se o usuário possui somente a permissão de alterar sua própria senha (ver cadastro de usuário)
        //a senha de cadastro de usuário será desconsiderada.
        if (!somenteAlterarSuaPropriaSenha) {
            alterar(getIdEntidade());
        }
        try {
            obj.realizarUpperCaseDados(false, con);
            String sql = "UPDATE Usuario set senha=? , dataAlteracaoSenha =?  WHERE ((codigo = ?))";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                if (UteisValidacao.emptyString(senhaCriptografada)) {
                    sqlAlterar.setString(1, Uteis.encriptar(obj.getSenha()));
                } else {
                    sqlAlterar.setString(1, senhaCriptografada);
                }
                sqlAlterar.setTimestamp(2, Uteis.getDataJDBCTimestamp(obj.getDataUltimaAlteracaoSenha()));
                sqlAlterar.setInt(3, obj.getCodigo());
                sqlAlterar.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>UsuarioVO</code>. Sempre localiza o registro a ser excluído através
     * da chave primária da entidade. Primeiramente verifica a conexão com o
     * banco de dados e a permissão do usuário para realizar esta operacão na
     * entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>UsuarioVO</code> que será removido no
     * banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluir(UsuarioVO obj, boolean centralEventos) throws Exception {
        try {
            con.setAutoCommit(false);
            if (centralEventos) {
//            	super.excluirObj(getIdEntidade());
            } else {
                excluir(getIdEntidade());
            }

            UsuarioEmailVO usuarioEmailVO = new UsuarioEmailVO();
            usuarioEmailVO.setUsuario(obj.getCodigo());
            getFacade().getUsuarioEmail().excluirSemCommit(usuarioEmailVO);


            UsuarioTelefoneVO usuarioTelefoneVO = new UsuarioTelefoneVO();
            usuarioTelefoneVO.setUsuario(obj.getCodigo());
            getFacade().getUsuarioTelefone().excluirSemCommit(usuarioTelefoneVO);

            String sql = "DELETE FROM Usuario WHERE codigo = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }

            getFacade().getUsuarioPerfilAcesso().excluirUsuarioPerfilAcesso(obj.getCodigo());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /* (non-Javadoc)
     * @see negocio.interfaces.arquitetura.PerfilAcessoInterfaceFacade#incluir(negocio.comuns.arquitetura.PerfilAcessoVO)
     */
    public void excluir(UsuarioVO obj) throws Exception {
        this.excluir(obj, false);
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Usuario</code> através do valor do atributo
     * <code>nome</code> da classe
     * <code>PerfilAcesso</code> Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @return List Contendo vários objetos da classe <code>UsuarioVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorNomePerfilAcesso(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        //String sqlStr = "SELECT Usuario.* FROM Usuario, PerfilAcesso WHERE Usuario.PerfilAcesso = PerfilAcesso.codigo and upper( PerfilAcesso.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY PerfilAcesso.nome";
        String sqlStr = "SELECT * FROM Usuario u "
                + "inner join usuarioperfilacesso upa on u.codigo = upa.usuario "
                + "inner join perfilacesso p on upa.perfilacesso = p.codigo "
                + "WHERE (upper(p.nome)) like('%" + valorConsulta.toUpperCase() + "%') ORDER BY p.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Usuario</code> através do valor do atributo
     * <code>String username</code>. Retorna os objetos, com início do valor do
     * atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>UsuarioVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorUsername(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Usuario WHERE upper( username ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY username";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Usuario</code> através do valor do atributo
     * <code>String nome</code>. Retorna os objetos, com início do valor do
     * atributo idêntico ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>UsuarioVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorNome(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM Usuario WHERE upper(nome) like upper(?)";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setString(1, valorConsulta+"%");
            ResultSet tabelaResultado = sqlConsultar.executeQuery();
            return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
        }
    }

    public List consultarPorNomeIdentico(String valorConsulta, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM Usuario WHERE upper(nome) = upper(?)";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setString(1, valorConsulta);
            ResultSet tabelaResultado = sqlConsultar.executeQuery();
            return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
        }
    }

    public List consultarPorNomeAtivo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Usuario" +
                " inner join colaborador on colaborador.codigo = usuario.colaborador" +
                " WHERE upper( nome ) like('" + valorConsulta.toUpperCase() + "%')  " +
                "AND colaborador.situacao = 'AT' ORDER BY nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<UsuarioVO> consultarUsuariosSemAcessoPorUmMes() throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT u.codigo, c.situacao, u.nome, u.username, u.ultimoacesso, c.pessoa\n");
        sqlStr.append("from colaborador c\n");
        sqlStr.append("inner join usuario u on u.colaborador = c.codigo\n");
        sqlStr.append("where c.situacao = 'AT' and upper(u.username) <> 'PACTOBR'  \n");
        sqlStr.append("and not exists (select codigo from tipocolaborador where descricao in ('PE', 'PI') and colaborador = c.codigo) \n");
        sqlStr.append("and u.ultimoacesso <= '").append(Uteis.getDataJDBC(Calendario.somarDias(Calendario.hoje(), -35))).append("'\n");

        try (Statement stm = con.createStatement()) {
            ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString());

            List<UsuarioVO> vetResultado = new ArrayList<UsuarioVO>();
                while (tabelaResultado.next()) {
                    UsuarioVO obj = new UsuarioVO();
                    obj.setCodigo(tabelaResultado.getInt("codigo"));
                    obj.setUltimoAcesso(tabelaResultado.getDate("ultimoacesso"));
                    obj.setUsername(tabelaResultado.getString("username"));
                    obj.setNome(tabelaResultado.getString("nome"));
                    obj.getColaboradorVO().setSituacao(tabelaResultado.getString("situacao"));
                    obj.getColaboradorVO().getPessoa().setCodigo(tabelaResultado.getInt("pessoa"));
                    vetResultado.add(obj);
                }
                return vetResultado;
        }
    }


    public List consultarPorSituacaoColaborador(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Usuario WHERE colaborador in " + "(select codigo from colaborador where situacao = '" + valorConsulta.toUpperCase() + "') ORDER BY nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<UsuarioVO> consultarUsuarioAberturaMeta(Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        /*String sqlStr = "SELECT\n"
                + "  *\n"
                + "FROM usuario usu\n"
                + "  INNER JOIN colaborador col\n"
                + "    ON usu.colaborador = col.codigo\n"
                + "WHERE 1 = 1\n"
                + "      AND situacao = 'AT'\n"
                + "      AND empresa = " + empresa + "\n"
                + "      AND (SELECT\n"
                + "             count(*)\n"
                + "           FROM tipocolaborador tc\n"
                + "           WHERE 1 = 1\n"
                + "                 AND tc.colaborador = col.codigo\n"
                + "                 AND tc.descricao IN (\n"
                + "             SELECT\n"
                + "               DISTINCT (tipocolaborador)\n"
                + "             FROM tiposvinculosfase\n"
                + "           )) > 0;";*/
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT usu.codigo, usu.administrador, usu.cliente,usu.tipoUsuario, usu.senha,  \n");
        sql.append("       usu.username, usu.nome, usu.dataAlteracaoSenha, usu.permiteAlterarPropriaSenha,  \n");
        sql.append("       usu.serviceUsuario, usu.serviceSenha, sqlColEmp.codigo as colaborador \n");
        sql.append("FROM usuario usu \n");
        sql.append("inner join( \n");
        sql.append("	select us.codigo as codigoUsuario, colEmp.codigo, colEmp.pessoa, colEmp.situacao, colEmp.empresa \n");
        sql.append("	from colaborador col \n");
        sql.append("	inner join usuario us on us.colaborador = col.codigo \n");
        sql.append("	inner join usuarioperfilacesso upa on upa.usuario = us.codigo \n");
        sql.append("	inner join colaborador colEmp on colEmp.pessoa = col.pessoa AND colEmp.empresa = upa.empresa\n");
        sql.append("	where upa.empresa = ").append(empresa).append(" and colEmp.situacao = 'AT' \n");
        sql.append("	) as sqlColEmp on sqlColEmp.codigoUsuario = usu.codigo  \n");
        sql.append("WHERE (SELECT \n");
        sql.append("        count(*) \n");
        sql.append("FROM vinculo v \n");
        sql.append("WHERE v.colaborador = sqlColEmp.codigo) > 0 ");
//        sql.append("WHERE (SELECT \n");
//        sql.append("             count(*) \n");
//        sql.append("           FROM tipocolaborador tc \n");
//        sql.append("           WHERE tc.colaborador = sqlColEmp.codigo \n");
//        sql.append("                 AND tc.descricao IN ( \n");
//        sql.append("             SELECT \n");
//        sql.append("               DISTINCT (tipocolaborador) \n");
//        sql.append("             FROM tiposvinculosfase \n");
//        sql.append("           )) > 0 \n");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public UsuarioVO consultarPorNomeUsuario(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Usuario WHERE upper( nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    UsuarioVO obj = new UsuarioVO();
                    return obj;
                }
                return montarDados(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public boolean consultarPorNomeUsuarioEDiferenteDoUsuario(String nome, int codigoUsuarioDiferente) throws Exception {
        String sql = "select exists(SELECT codigo FROM Usuario WHERE upper( nome ) like upper(?) and codigo <> ?) as existe";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setString(1, nome); //dessa forma evita erros com nomes que tenha aspas simples
            sqlConsultar.setInt(2, codigoUsuarioDiferente);
            ResultSet rs = sqlConsultar.executeQuery();
            if (rs.next()) {
                return rs.getBoolean("existe");
            }
            return false;
        }
    }

    public boolean consultarPorUsernameEDiferenteDoUsuario(String username, int codigoUsuarioDiferente) throws Exception {
        String sqlStr = "select exists(SELECT codigo FROM Usuario WHERE username ilike('" + username + "') and codigo <> " + codigoUsuarioDiferente + ") as existe";
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlStr)) {
                if (rs.next()) {
                    return rs.getBoolean("existe");
                }
                return false;
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Usuario</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>UsuarioVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Usuario WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>UsuarioVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public UsuarioVO consultarPorCodigo(Integer codigoPrm, int nivelMontarDados) throws Exception {
        UsuarioVO usuario = null;
        String sql = "SELECT * FROM Usuario WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet resultDados = sqlConsultar.executeQuery()) {
                if (resultDados.next()) {
                    usuario = montarDados(resultDados, nivelMontarDados, this.con);
                }
            }
        }

        return usuario;
    }

    public boolean validarSenhaUsuarioLogado(Integer codigo, String senha) throws Exception {
        return validarSenhaUsuarioLogado(codigo, senha, "");
    }

    public boolean validarSenhaUsuarioLogado(Integer codigo, String senha, String pin) throws Exception {
        String sql = "SELECT * FROM Usuario WHERE codigo = ? and (senha = ? or pin = ?)";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigo);
            sqlConsultar.setString(2, senha);
            sqlConsultar.setString(3, pin);
            return sqlConsultar.executeQuery().next();
        }
    }

    public void atualizarPin(Integer codigo, String pin) throws Exception {
        String sql = "update Usuario set pin = ? WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setString(1, Uteis.encriptar(pin));
            sqlConsultar.setInt(2, codigo);
            sqlConsultar.executeUpdate();
        }
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>UsuarioVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public UsuarioVO consultarPorCodigoColaborador(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT u.* ");
        sql.append("FROM Usuario u inner join ");
        sql.append("   colaborador uc on u.colaborador = uc.codigo inner join ");
        sql.append("   pessoa p on uc.pessoa = p.codigo inner join ");
        sql.append("   colaborador pc on p.codigo = pc.pessoa ");
        sql.append("where pc.codigo = ? ");
        sql.append("order by u.codigo desc ");

        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new UsuarioVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public UsuarioVO consultarPorCodigoUsuarioSemCliente(Integer valorConsulta, Boolean controleAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controleAcesso);
        String sql = "SELECT * FROM Usuario WHERE codigo >= ? and cliente isnull ORDER BY codigo";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, valorConsulta);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new UsuarioVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarPorNomeUsuarioSomenteColaborador(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Usuario WHERE upper( nome ) like('" + valorConsulta.toUpperCase() + "%') and colaborador <> 0 ORDER BY nome limit 20";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de
     * <code>Usuario</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou
     * superiores ao parâmetro fornecido. Faz uso da operação
     * <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o
     * List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o
     * usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>UsuarioVO</code>
     * resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public List consultarPorCodigoUsuarioSomenteColaborador(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Usuario WHERE codigo >= " + valorConsulta + " and colaborador <> 0 ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    // public List consultarPorNomeUsuarioSemCliente(String valorConsulta,
    // boolean controlarAcesso, int nivelMontarDados) throws Exception {
    // consultar(getIdEntidade(), controlarAcesso);
    // String sqlStr = "SELECT * FROM Usuario WHERE upper( nome ) like('" +
    // valorConsulta.toUpperCase() +
    // "%') and cliente isnull ORDER BY nome limit 20";
    // Statement stm = con.createStatement();
    // ResultSet tabelaResultado = stm.executeQuery(sqlStr);
    // return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    // }
    public List<UsuarioVO> consultarPorNomeUsuarioComLimite(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM usuario usu\n";
        if (empresa != null && empresa != 0) {
            sqlStr += "  INNER JOIN colaborador col ON usu.colaborador = col.codigo\n" +
                    "  INNER JOIN usuarioperfilacesso upa ON usu.codigo = upa.usuario\n"
                    + "where upa.empresa = " + empresa;
        }
        sqlStr += sqlStr.contains("where") ? " and " : " where ";
        sqlStr += "  upper(usu.nome) like('" + valorConsulta.toUpperCase() + "%') "
                + "ORDER BY usu.nome limit 50";

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<UsuarioVO> consultarTodosUsuarioComLimite(boolean controlarAcesso, Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM usuario usu\n";
        if (empresa != null && empresa != 0) {
            sqlStr += "  INNER JOIN colaborador col ON usu.colaborador = col.codigo\n" +
                    "  INNER JOIN usuarioperfilacesso upa ON usu.codigo = upa.usuario\n"
                    + "where upa.empresa = " + empresa;
        }
        sqlStr += " ORDER BY usu.nome limit 50";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List consultarTodosSemAdministrador(int nivelMontarDados) throws Exception {
        String sqlStr = "";
        sqlStr = "SELECT Usuario.* FROM Usuario where administrador = false and userName <> ''";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<UsuarioVO> consultarAtivosSemAdministradorComTelefone() throws Exception {
        String sql = "SELECT DISTINCT sqlUsuario.codigo, * FROM ( " +
                "    SELECT u.*, " +
                "           col.pessoa, " +
                "           (SELECT email FROM usuarioemail WHERE usuario = u.codigo LIMIT  1) email " +
                "    FROM Usuario u " +
                "             INNER join colaborador col on col.codigo = u.colaborador " +
                "    WHERE col.situacao = 'AT' " +
                "      and u.administrador = false " +
                "      and u.userName <> '' " +
                "    ) as sqlUsuario " +
                "INNER JOIN telefone ON telefone.pessoa = sqlUsuario.pessoa";

        List<UsuarioVO> usuarios = new ArrayList<>();
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql)) {
                while (rs.next()) {
                    UsuarioVO usuario = montarDadosBasico(rs);
                    usuario.setEmail(rs.getString("email"));
                    TelefoneVO telefone = Telefone.montarDados(rs, Uteis.NIVELMONTARDADOS_MINIMOS);
                    usuario.setTelefone(telefone);
                    usuarios.add(usuario);
                }

                return usuarios;
            }
        }
    }

    public List consultarTodosAtivosSemAdministrador(int nivelMontarDados) throws Exception {
        StringBuilder sqlStr = new StringBuilder("SELECT u.* FROM Usuario u \n");
        sqlStr.append("inner join colaborador col on col.codigo = u.colaborador \n");
        sqlStr.append("where col.situacao = 'AT' and u.administrador = false and u.userName <> ''");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public UsuarioVO consultarPorCodigoUsuario(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "";
        sqlStr = "Select Usuario.* FROM Usuario WHERE usuario.codigo = " + valorConsulta.intValue() + " ORDER BY codigo";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new UsuarioVO();
                }
                return montarDados(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public String consultarSenhaPorCodigoUsuario(Integer valorConsulta) throws Exception {
        String sqlStr = "Select senha FROM Usuario WHERE codigo = " + valorConsulta;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return "";
                }
                return tabelaResultado.getString("senha");
            }
        }
    }

    public UsuarioVO consultarPorCodigoPessoa(Integer codigoPessoa, int nivelMontarDados) throws Exception {
        String sqlStr = "select * from usuario where colaborador in \n" +
            "( select codigo from colaborador  where pessoa = "+codigoPessoa+")";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlStr)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new UsuarioVO();
                }
                return montarDados(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>UsuarioVO</code>
     * resultantes da consulta.
     */
    public static List<UsuarioVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<UsuarioVO> vetResultado = new ArrayList<UsuarioVO>();
        while (tabelaResultado.next()) {
            UsuarioVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private static UsuarioVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        UsuarioVO obj = new UsuarioVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setNome(dadosSQL.getString("nome"));
        obj.getColaboradorVO().setCodigo(dadosSQL.getInt("colaborador"));
        obj.setUsername(dadosSQL.getString("username"));
        obj.setSenha(dadosSQL.getString("senha"));
        obj.setSenhaConfirmar(obj.getSenha());
        obj.setTipoUsuario(dadosSQL.getString("tipoUsuario"));
        obj.setAdministrador(dadosSQL.getBoolean("administrador"));
        obj.getClienteVO().setCodigo(dadosSQL.getInt("cliente"));
        try {//estes campos podem ainda não existir no momento do login do usuário, por isso mascara-se as exceções de 'coluna não encontrada neste ResultSet'
            obj.setDataUltimaAlteracaoSenha(dadosSQL.getTimestamp("dataAlteracaoSenha"));
            obj.setPermiteAlterarPropriaSenha(dadosSQL.getBoolean("permiteAlterarPropriaSenha"));
            obj.setServiceUsuario(dadosSQL.getString("serviceUsuario"));
            obj.setServiceSenha(dadosSQL.getString("serviceSenha"));
            obj.setPedirSenhaFuncionalidade(dadosSQL.getBoolean("pedirSenhaFuncionalidade"));
            obj.setPermissaoAlterarRPS(dadosSQL.getBoolean("permissaoAlterarRPS"));
            obj.setPermiteExecutarProcessos(dadosSQL.getBoolean("permiteExecutarProcessos"));
            obj.setShowModalInativar(dadosSQL.getBoolean("showmodalinativar"));
            obj.setExibirModalInativarUsersHoje(dadosSQL.getDate("dataexibirmodalinativarusuers"));
            obj.setPin(dadosSQL.getString("pin"));
            obj.setPermiteExclusaoCliente(dadosSQL.getBoolean("permiteexclusaocliente"));
            obj.setAcessoPactoApp(dadosSQL.getBoolean("acessoPactoApp"));
            obj.setShowModalPlanos(dadosSQL.getBoolean("showModalPlanos"));
            obj.setDataExibirModalPlanos(dadosSQL.getDate("dataexibirmodalplanos"));
        } catch (Exception e) {
            //dataalteracaosenha nao existira na primeira vez rodada
        }

        try{
            obj.setLinguagem(dadosSQL.getString("linguagem"));
        }catch (Exception ignored){
        }

        try{
            obj.setUsuarioGeral(dadosSQL.getString("usuarioGeral"));
        }catch (Exception ignored){
        }

        try{
            obj.setPerfilTw(dadosSQL.getInt("perfilTw"));
        }catch (Exception ignored){
        }

        try{
            obj.setPermiteAcionarBotaoPanico(dadosSQL.getBoolean("permiteAcionarEmergencia"));
        }catch (Exception ignored){
        }

        return obj;

    }

    public static String obterFotoKey(Integer codigoColaborador, Connection con){
        try {
            try (ResultSet rs = con.prepareStatement("SELECT fotokey FROM pessoa p \n"
                    + "INNER JOIN colaborador c ON p.codigo = c.pessoa AND c.codigo = " + codigoColaborador).executeQuery()) {
                if (rs.next()) {
                    return rs.getString("fotokey");
                }
            }
        } catch (Exception e) {
            //nada a fazer
        }
        return null;
    }
    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>UsuarioVO</code>.
     *
     * @return O objeto da classe <code>UsuarioVO</code> com os dados
     * devidamente montados.
     */
    public static UsuarioVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_REGISTRAR_JUSTIFICATIVA) {
            UsuarioVO obj = new UsuarioVO();
            obj.setNome(dadosSQL.getString("nome"));
            return obj;
        }

        UsuarioVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            montarDadosColaborador(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            obj.getColaboradorVO().getPessoa().setFotoKey(obterFotoKey(obj.getColaboradorVO().getCodigo(), con));
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_VALIDACAOACESSO) {
            montarDadosColaborador(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            obj.getColaboradorVO().getPessoa().setFotoKey(obterFotoKey(obj.getColaboradorVO().getCodigo(), con));

            UsuarioPerfilAcesso usuarioPerfilAcesso = new UsuarioPerfilAcesso(con);
            if (obj.getAdministrador()) {
                obj.setUsuarioPerfilAcessoVOs(usuarioPerfilAcesso.consultarUsuarioPerfilAcesso(obj.getCodigo(), Uteis.NIVELMONTARDADOS_LOGIN));
            } else {
                obj.setUsuarioPerfilAcessoVOs(usuarioPerfilAcesso.consultarUsuarioPerfilAcessoEmpresaAtiva(obj.getCodigo(), Uteis.NIVELMONTARDADOS_LOGIN));
            }

            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            if (obj.getColaboradorVO().getCodigo() > 0) {
                montarDadosColaborador(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            }
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR) {
            if (obj.getColaboradorVO().getCodigo() > 0) {
                montarDadosColaborador(obj, Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR, con);
            }
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            return obj;
        }
        if (obj.getClienteVO().getCodigo() > 0) {
            montarDadosCliente(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
        }
        if (obj.getColaboradorVO().getCodigo() > 0) {
            montarDadosColaborador(obj, Uteis.NIVELMONTARDADOS_ORGANIZADORCARTEIRA, con);
        }

        UsuarioPerfilAcesso usuarioPerfilAcesso = new UsuarioPerfilAcesso(con);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_LOGIN) {
            if (obj.getAdministrador()) {
                obj.setUsuarioPerfilAcessoVOs(usuarioPerfilAcesso.consultarUsuarioPerfilAcesso(obj.getCodigo(), Uteis.NIVELMONTARDADOS_LOGIN));
            } else {
                obj.setUsuarioPerfilAcessoVOs(usuarioPerfilAcesso.consultarUsuarioPerfilAcessoEmpresaAtiva(obj.getCodigo(), Uteis.NIVELMONTARDADOS_LOGIN));
            }
        } else {
            obj.setUsuarioPerfilAcessoVOs(usuarioPerfilAcesso.consultarUsuarioPerfilAcessoEmpresaAtiva(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }

        if (!obj.getAdministrador()) {
            HorarioAcessoSistema horarioAcessoSistema = new HorarioAcessoSistema(con);
            obj.setUsuarioHorarioAcessoSistemaVOs(horarioAcessoSistema.consultar(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            return obj;
        }
        obj.setDicasEsconder(montarDicasEsconder(con, obj.getCodigo()));
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>CategoriaVO</code> relacionado ao objeto
     * <code>ClienteVO</code>. Faz uso da chave primária da classe
     * <code>CategoriaVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosCliente(UsuarioVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getClienteVO().getCodigo() == 0) {
            obj.setClienteVO(new ClienteVO());
            return;
        }
        Cliente cliente = new Cliente(con);
        obj.setClienteVO(cliente.consultarPorChavePrimaria(obj.getClienteVO().getCodigo(), nivelMontarDados));
        cliente = null;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>CategoriaVO</code> relacionado ao objeto
     * <code>ClienteVO</code>. Faz uso da chave primária da classe
     * <code>CategoriaVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosColaborador(UsuarioVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getColaboradorVO().getCodigo() == 0) {
            obj.setColaboradorVO(new ColaboradorVO());
            return;
        }
        Colaborador colaborador = new Colaborador(con);
        obj.setColaboradorVO(colaborador.consultarPorChavePrimaria(obj.getColaboradorVO().getCodigo(), nivelMontarDados));
        colaborador = null;
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>UsuarioVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public UsuarioVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        UsuarioVO o = (UsuarioVO) obterFromCache(codigoPrm);
        if (o != null){
            return o;
        }
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Usuario WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Usuário ).");
                }
                o = montarDados(tabelaResultado, nivelMontarDados, this.con);
            }
        }
        putToCache(o);
        return o;
    }

    /**
     * Operação responsável por obter o último valor gerado para uma chave
     * primária. É utilizada para obter o valor gerado pela SGBD para uma chave
     * primária, a apresentação do mesmo e a implementação de possíveis
     * relacionamentos.
     */
    public Integer consultarPorCodigoPessoaRetornandoCodigoUsuario(Integer codigoPessoa) throws Exception {
        inicializar();
        String sqlStr = "select usuario.codigo from usuario where colaborador in ( " + "SELECT consultor FROM QuestionarioCliente " + "where QuestionarioCliente.codigo in (SELECT MAX(QuestionarioCliente.codigo) FROM QuestionarioCliente " + "inner join cliente on cliente.codigo = QuestionarioCliente.cliente " + "inner join pessoa on pessoa.codigo = cliente.pessoa and pessoa.codigo = " + codigoPessoa + "))";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                if (!tabelaResultado.next()) {
                    return 0;
                }
                return (tabelaResultado.getInt(1));
            }
        }
    }

    public UsuarioVO criarOuConsultarSeExistePorNome(UsuarioVO obj) throws Exception {
        String sql = "SELECT * FROM Usuario WHERE nome = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setString(1, obj.getNome());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    incluir(obj);
                    return obj;
                } else {
                    return (Usuario.montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con));
                }
            }
        }
    }

    /*author   : carla
     * Data    : 07/11/11
     * Objetivo: Consultar usuarios administradores.
     */
    public List<UsuarioVO> consultarPorAdministrador(int nivelMontarDados) throws Exception {
        String sqlStr = "";
        sqlStr = "SELECT * FROM Usuario WHERE administrador = true ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<UsuarioVO> consultarPorAcessoEmpresa(Integer empresa, int nivelMontarDados) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT u.* FROM usuario u, usuarioperfilacesso upa ");
        sql.append(" WHERE u.codigo = upa.usuario ");
        sql.append(" AND upa.empresa = " + empresa);
        sql.append(" ORDER BY nome");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }
    public List<UsuarioVO> consultarTodosUsuariosColaboradorPorEmpresa(int empresa,int nivelMontaDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" Select * FROM Usuario usuario ");
        sql.append(" INNER JOIN Colaborador col ON  col.codigo = usuario.colaborador ");
        sql.append(" INNER JOIN usuarioperfilacesso upa on usuario.codigo = upa.usuario ");
        sql.append(" WHERE upa.empresa = ").append(empresa);
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontaDados, this.con);
            }
        }
    }

    public List<UsuarioVO> consultarTodosUsuariosColaboradorAtivosPorEmpresaEPassivelAbrirMetaAgendamento(Integer empresa, TipoColaboradorEnum tipoColaborador, Integer nivelMontaDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT DISTINCT usuario.* FROM Usuario usuario ");
        sql.append(" INNER JOIN Colaborador col ON  col.codigo = usuario.colaborador ");
        sql.append(" INNER JOIN usuarioperfilacesso upa on usuario.codigo = upa.usuario ");
        sql.append(" WHERE col.situacao = 'AT' AND upa.empresa = ?");
        sql.append(" AND EXISTS(");
        sql.append("        SELECT 1");
        sql.append("        FROM permissao p");
        sql.append("        WHERE p.codperfilacesso = upa.perfilacesso");
        sql.append("        AND p.tituloapresentacao = '7.24 - Meta Agendamento'");
        sql.append(" )");
        if(tipoColaborador != null) {
            sql.append(" AND EXISTS (");
            sql.append("    SELECT 1 ");
            sql.append("    FROM tipocolaborador tipo");
            sql.append("    WHERE tipo.colaborador = col.codigo");
            sql.append("    AND tipo.escricao = ?");
            sql.append(" )");
        }
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            ps.setInt(1, empresa);
            if (tipoColaborador != null)
                ps.setString(2, tipoColaborador.getSigla());
            try (ResultSet tabelaResultado = ps.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontaDados, this.con);
            }
        }
    }

    public UsuarioVO consultarPorColaborador(Integer colaborador, int nivelMontarDados) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT u.* FROM usuario u ");
        sql.append(" WHERE u.colaborador = " + colaborador);
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        if (tabelaResultado.next()) {
           return (montarDados(tabelaResultado, nivelMontarDados, this.con));
        }else {
            throw new Exception("Nenhum usuário encontrado para este colaborador nesta empresa!");
        }
    }

    public void validarOutroUsuarioComMesmoNome(UsuarioVO obj) throws Exception {
        boolean outroUsuarioComMesmoNome = consultarPorNomeUsuarioEDiferenteDoUsuario(obj.getNome(), obj.getCodigo());
        if (outroUsuarioComMesmoNome) {
            if (!obj.getColaboradorVO().getPessoa().getNome().isEmpty()) {
                throw new ConsistirException("Já existe um usuário com este Nome de Colaborador");
            } else if (!obj.getClienteVO().getPessoa().getNome().isEmpty()) {
                throw new ConsistirException("Já existe um usuário com este Nome de Cliente");
            }
        }
        boolean outroUsuarioComMesmoNomeUsuario = consultarPorUsernameEDiferenteDoUsuario(obj.getUsername(), obj.getCodigo());
        if (outroUsuarioComMesmoNomeUsuario) {
            throw new ConsistirException("Já existe um usuário com este Nome do Usuário (E-mail / Username)");
        }
    }

    public List<UsuarioVO> consultarPorNomeEDiferenteDoUsuarioSomenteColaborador(String nome, int codigoUsuarioDiferente, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Usuario WHERE upper( nome ) like('" + nome.toUpperCase() + "%') and codigo <> " + codigoUsuarioDiferente + " and colaborador <> 0 ORDER BY username";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<UsuarioVO> consultarNomeComFoto(String nome, List<UsuarioVO> usuariosNaoPesquisar) throws Exception {
        String usuarioNaoPesquisar = Uteis.retornarCodigos(usuariosNaoPesquisar);
        String sqlStr = "SELECT distinct u.nome,p.nome as nomepessoa,  p.foto, p.codigo as pessoa, u.codigo as user, p.fotokey FROM Usuario u "
                + "INNER JOIN colaborador c ON u.colaborador = c.codigo "
                + "inner join colaborador colpe on c.pessoa = colpe.pessoa  AND colpe.situacao LIKE 'AT' "
                + "INNER JOIN pessoa p ON p.codigo = c.pessoa "
                + "WHERE upper( u.nome ) like('" + nome.toUpperCase() + "%') and u.codigo not in (" + usuarioNaoPesquisar + ")  ORDER BY u.nome";
        List<UsuarioVO> lista;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                lista = new ArrayList<UsuarioVO>();
                while (tabelaResultado.next()) {
                    UsuarioVO user = new UsuarioVO();
                    user.setNome(tabelaResultado.getString("nome"));
                    try {
                        user.getColaboradorVO().getPessoa().setFoto(tabelaResultado.getBytes("foto"));
                    } catch (Exception e) {
                    }
                    user.getColaboradorVO().getPessoa().setFotoKey(tabelaResultado.getString("fotokey"));
                    user.getColaboradorVO().getPessoa().setCodigo(tabelaResultado.getInt("pessoa"));
                    user.getColaboradorVO().getPessoa().setNome(tabelaResultado.getString("nomepessoa"));
                    user.setCodigo(tabelaResultado.getInt("user"));
                    lista.add(user);

                }
            }
        }
        return lista;
    }

    public String consultarJSON(Integer empresa, String situacao) throws Exception {
        JSONObject aaData;
        JSONArray valores;
        try (ResultSet rs = getPS(empresa, situacao).executeQuery()) {
            ColaboradorVO colaborador = new ColaboradorVO();
            aaData = new JSONObject();
            valores = new JSONArray();

            while (rs.next()) {
                String nomeEmpresa = (rs.getString("nomeEmpresa") != null) ? rs.getString("nomeEmpresa") : "";
                colaborador.setCodigo(rs.getInt("colaborador"));
                colaborador.setSituacao(rs.getString("situacao"));

                JSONArray itemArray = new JSONArray();

                itemArray.put(rs.getString("codigo"));
                itemArray.put(rs.getString("nome"));
                itemArray.put(rs.getString("username"));
                itemArray.put(rs.getString("email"));
                itemArray.put(colaborador.getSituacao_Apresentar());
                itemArray.put(rs.getString("perfilacesso"));
                itemArray.put(nomeEmpresa.trim());
                itemArray.put(colaborador.getCodigo());

                valores.put(itemArray);
            }
        }

        aaData.put("aaData", valores);

        return aaData.toString();
    }

    private PreparedStatement getPS(Integer empresa, String situacao) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT distinct(usr.codigo), coalesce(pc.nome, usr.nome) as nome, usr.username,col.situacao, pa.nome AS perfilacesso, \n");

        sql.append(" (SELECT string_agg(empresa.nome, ', ')  \n");
        sql.append(" FROM usuarioperfilacesso upa2  \n");
        sql.append(" INNER JOIN empresa ON empresa.codigo = upa2.empresa  \n");
        sql.append(" WHERE usr.codigo = upa2.usuario  \n");
        sql.append(") AS nomeEmpresa, \n");
        sql.append(" usr.colaborador as colaborador, \n");
        sql.append("(select email from usuarioemail where usuario = usr.codigo order by codigo desc limit 1) as email \n");
        sql.append("FROM usuario usr \n");
        sql.append("LEFT JOIN colaborador col ON usr.colaborador = col.codigo \n");
        sql.append("LEFT JOIN pessoa pc on pc.codigo = col.pessoa \n");
        sql.append("LEFT JOIN usuarioperfilacesso upa ON usr.codigo = upa.usuario \n");
        sql.append("LEFT JOIN perfilacesso pa ON upa.perfilacesso = pa.codigo \n");
        sql.append("LEFT JOIN empresa e on e.codigo = upa.empresa \n");
        sql.append(" WHERE 1 = 1 \n");
        if (empresa != 0) {
            sql.append("AND upa.empresa = ").append(empresa).append(" \n");
        }
        if (!situacao.equals("TD")) {
                sql.append("AND col.situacao = '").append(situacao).append("' \n");
        }
        sql.append("  ORDER BY coalesce(pc.nome, usr.nome) ");
        return con.prepareStatement(sql.toString());
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa, String situacao) throws SQLException {
        List lista;
        try (ResultSet rs = getPS(empresa, situacao).executeQuery()) {
            lista = new ArrayList();

            while (rs.next()) {
                UsuarioVO usu = new UsuarioVO();
                String geral = rs.getString("codigo") + rs.getString("nome") + rs.getString("username") + rs.getString("situacao") + rs.getString("perfilacesso");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    usu.setCodigo(rs.getInt("codigo"));
                    usu.setNome(rs.getString("nome"));
                    usu.setUsername(rs.getString("username"));
                    usu.getColaboradorVO().setSituacao(rs.getString("situacao"));
                    usu.setPerfisUsuario(rs.getString("perfilacesso"));
                    lista.add(usu);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Nome")) {
            Ordenacao.ordenarLista(lista, "nome");
        } else if (campoOrdenacao.equals("Usuário")) {
            Ordenacao.ordenarLista(lista, "username");
        } else if (campoOrdenacao.equals("Situação")) {
            Ordenacao.ordenarLista(lista, "situacao_Usuario");
        } else if (campoOrdenacao.equals("Perfil de Acesso")) {
            Ordenacao.ordenarLista(lista, "perfisUsuario");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    private static List<String> montarDicasEsconder(Connection con, int codigo) throws Exception {
        try {
            List<String> dicas;
            try (ResultSet consultar = criarConsulta(" SELECT dica FROM usuariodicasesconder WHERE usuario = " + codigo, con)) {
                dicas = new ArrayList<String>();
                while (consultar.next()) {
                    dicas.add(consultar.getString("dica"));
                }
            }
            return dicas;
        } catch (Exception e) {
            return new ArrayList<String>();
        }

    }

    @Override
    public Integer obterCodigoPessoaUsuario(int colaborador) throws Exception {
        try (ResultSet rs = criarConsulta("SELECT pessoa FROM colaborador WHERE codigo = " + colaborador, con)) {
            if (rs.next()) {
                return rs.getInt("pessoa");
            } else {
                return 0;
            }
        }
    }

    @Override
    public void marcarNaoAparecerMais(int usuario, String dica, boolean naoMostrarMais) throws Exception {
        if (naoMostrarMais) {
            executarConsulta("INSERT INTO usuariodicasesconder(usuario, dica) VALUES (" + usuario + ", '" + dica + "')", con);
        } else {
            executarConsulta("DELETE FROM usuariodicasesconder WHERE usuario = " + usuario + " AND dica LIKE '" + dica + "'", con);
        }

    }

    public Boolean consultarUsuarioAtivo(Integer usuarioZw) throws Exception {
        try (ResultSet rs = criarConsulta(" select c.situacao from usuario u "
                + " inner join colaborador c on u.colaborador = c.codigo AND u.codigo = " + usuarioZw, con)) {
            if (rs.next()) {
                return rs.getString("situacao").equals("AT");
            }
        }
        return true;


    }

    public void verificarHorariosAcessosPermitidos(UsuarioTO usuario) throws Exception {
        String dataAtual = Uteis.getHoraAtual();
        String diaSemana = Calendario.getDiaDaSemanaAbreviado(Calendario.hoje());
        HorarioAcessoSistema horarioAcessoDao = new HorarioAcessoSistema(con);
        List<HorarioAcessoSistemaVO> listaHorarioAcessoSistemaVOs = horarioAcessoDao.consultarHorarioAtualDentroIntervalosPermitidos(
                usuario.getCodigo().intValue(), dataAtual, diaSemana, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaHorarioAcessoSistemaVOs.isEmpty()) {
            usuario.setForaDoHorario(true);
            usuario.setMensagem("Acesso não permitido para esse horário.");
        }
    }
    public UsuarioVO consultarColaboradorResponsavelPeloCliente(Integer codigoCliente, int nivelMontarDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select usu.* \n");
        sql.append("from usuario usu \n");
        sql.append("inner join colaborador col on col.codigo = usu.colaborador \n");
        sql.append("where col.pessoa =  \n");
        sql.append("                  (select col.pessoa \n");
        sql.append("                   from cliente cli \n");
        sql.append("                   inner join vinculo vinc on vinc.cliente = cli.codigo \n");
        sql.append("                   inner join colaborador col on col.codigo = vinc.colaborador \n");
        sql.append("                   where vinc.tipoVinculo = 'CO' and cli.codigo = ? limit 1) \n");
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            pst.setInt(1, codigoCliente);
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next())
                    return montarDados(rs, nivelMontarDados, con);
            }
        }
        return null;
    }

    public UsuarioVO getUsuarioRecorrencia() throws Exception {
        UsuarioVO userRecorrencia = new UsuarioVO();
        userRecorrencia.setNome("RECORRENCIA");
        userRecorrencia.setUsername("RECOR");
        userRecorrencia.setSenha(PropsService.getPropertyValue(PropsService.RECORRENCIA_USER_PASSWORD));
        userRecorrencia.setAdministrador(Boolean.TRUE);
        userRecorrencia.setValidarDados(false);
        userRecorrencia = criarOuConsultarSeExistePorNome(userRecorrencia);
        return userRecorrencia;
    }


    public List<UsuarioVO> consultarUsuariosComMeta(Integer empresa, Date diaMeta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("u.* \n");
        sql.append("from usuario u \n");
        sql.append("inner join aberturameta am on am.colaboradorresponsavel = u.codigo \n");
        sql.append("where am.empresa = ").append(empresa).append("\n");
        sql.append("and am.dia = '").append(Uteis.getData(diaMeta)).append("';");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public List<UsuarioVO> consultarUsuarioDataNascimentoCPF(String dataNascimento, String cpf) throws Exception {
        List<UsuarioVO> listaRetornar = new ArrayList<UsuarioVO>();
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("u.codigo, \n");
        sql.append("u.administrador, \n");
        sql.append("u.username, \n");
        sql.append("u.dataalteracaosenha, \n");
        sql.append("p.nome, \n");
        sql.append("c.codigo as colaborador, \n");
        sql.append("c.empresa as empresaColaborador, \n");
        sql.append("p.codigo as pessoa, \n");
        sql.append("p.datanasc, \n");
        sql.append("p.cfp \n");
        sql.append("from usuario u \n");
        sql.append("inner join colaborador c on c.codigo = u.colaborador \n");
        sql.append("inner join pessoa p on p.codigo = c.pessoa \n");
        sql.append("where p.datanasc = '").append(dataNascimento).append("' \n");
        sql.append("and p.cfp = '").append(cpf).append("' \n");

        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet dadosSQL = pst.executeQuery()) {
                while (dadosSQL.next()) {
                    UsuarioVO usuarioVO = new UsuarioVO();
                    usuarioVO.setCodigo(dadosSQL.getInt("codigo"));
                    usuarioVO.setAdministrador(dadosSQL.getBoolean("administrador"));
                    usuarioVO.setUsername(dadosSQL.getString("username"));
                    usuarioVO.setNome(dadosSQL.getString("nome"));
                    usuarioVO.getColaboradorVO().setCodigo(dadosSQL.getInt("colaborador"));
                    usuarioVO.getColaboradorVO().getEmpresa().setCodigo(dadosSQL.getInt("empresaColaborador"));
                    usuarioVO.getColaboradorVO().getPessoa().setCodigo(dadosSQL.getInt("pessoa"));
                    usuarioVO.getColaboradorVO().getPessoa().setDataNasc(Uteis.getDataJDBC(dadosSQL.getDate("datanasc")));
                    usuarioVO.setDataUltimaAlteracaoSenha(Uteis.getDataJDBC(dadosSQL.getDate("dataalteracaosenha")));
                    usuarioVO.getColaboradorVO().getPessoa().setCfp(dadosSQL.getString("cfp"));
                    listaRetornar.add(usuarioVO);
                }
            }
        }
        return listaRetornar;
    }

    public StringBuilder gerarCorpoEmail(UsuarioVO usuarioVO, String mensagemSuperior, String mensagemInferior, String labelBotao, String urlBotao, boolean mostrarSenha) throws Exception {

        String imagemSuperior = "http://wiki.pactosolucoes.com.br/midias/email_app/zw_emailP4CTO.png";
        if (mostrarSenha) {
            imagemSuperior = "http://wiki.pactosolucoes.com.br/midias/email_app/bemvindo_emailP4CTO.png";
        }

        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailUsuarioLoginZillyonWeb.txt").toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());
        String aux = texto.toString()
                .replaceAll("#NOME_USUARIO", usuarioVO.getNomeAbreviado())
                .replaceAll("#USERNAME", usuarioVO.getUsername())
                .replaceAll("#URL_IMAGEMSUPERIOR", imagemSuperior)
                .replaceAll("#MENSAGEM_SUPERIOR", mensagemSuperior)
                .replaceAll("#URL_BOTAO", urlBotao)
                .replaceAll("#LABELBOTAO", labelBotao)
                .replaceAll("#MENSAGEM_INFERIOR", mensagemInferior);

        if (mostrarSenha) {
            aux = aux.replaceAll("#MOSTRAR_SENHA", "Senha: ").replaceAll("#SENHA", usuarioVO.getSenhaNaoCriptografada());
        } else {
            aux = aux.replaceAll("#MOSTRAR_SENHA", "").replaceAll("#SENHA", "");
        }
        return new StringBuilder(aux);
    }
    
    public UsuarioVO consultarPorColaboradorEmpresa(Integer colaborador,Integer empresa, int nivelMontarDados) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append(" select usu.* from usuario usu ");
        sql.append(" inner join usuarioperfilacesso upa on upa.usuario = usu.codigo ");
        sql.append("where upa.empresa =").append(empresa);
        sql.append(" and usu.colaborador in (select codigo from colaborador where pessoa in (select pessoa from colaborador where codigo = ").append(colaborador).append("))");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                if (!tabelaResultado.next()) {
                    return new UsuarioVO();
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    /* Consulta específica criada a pedido do lider tecnico */
    public UsuarioVO consultarNomePorUsername(String username) throws Exception {
        String sql = "select nome from usuario where upper(username) = '"+ username.toUpperCase() +"'";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_REGISTRAR_JUSTIFICATIVA, this.con);
                }
            }
        }
        return new UsuarioVO();
    }

    public UsuarioVO consultarPorUsername(String username, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "select * from usuario where username ilike '"+ username +"'";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return (montarDados(tabelaResultado, nivelMontarDados, this.con));
                }
            }
        }
        return new UsuarioVO();
    }
    public List<UsuarioVO> consultarListaUsuariosCRM(Integer empresa, boolean somenteAtivo, boolean somenteCodigos) throws Exception {
        consultar(getIdEntidade(), false);
        List<UsuarioVO> listaRetornar = new ArrayList<UsuarioVO>();

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("usu.codigo \n");
        if(!somenteCodigos) {
            sql.append(" ,        usu.nome, \n");
            sql.append("        sqlColEmp.codigo as colaborador, \n");
            sql.append("sqlColEmp.empresa, \n");
            sql.append("        tc.descricao \n");
        }
        sql.append("FROM usuario usu \n");
        sql.append("inner join ( select us.codigo as codigoUsuario, colEmp.codigo, colEmp.pessoa, colEmp.situacao, colEmp.empresa \n");
        sql.append("from colaborador col \n");
        sql.append("inner join usuario us on us.colaborador = col.codigo \n");
        sql.append("inner join usuarioperfilacesso upa on upa.usuario = us.codigo \n");
        sql.append("inner join colaborador colEmp on colEmp.pessoa = col.pessoa AND colEmp.empresa = upa.empresa \n");
        sql.append("where 1 = 1 \n");
        if (somenteAtivo) {
            sql.append("and colEmp.situacao = 'AT' \n");
}
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("and upa.empresa =  ").append(empresa).append(" \n");
        }
        sql.append(") as sqlColEmp on sqlColEmp.codigoUsuario = usu.codigo \n");
        sql.append("left join tipocolaborador tc on tc.colaborador = sqlColEmp.codigo \n");
        sql.append("where 1 = 1 \n");
        sql.append("and usu.codigo in (select colaboradorresponsavel from aberturameta  where colaboradorresponsavel  is not null) \n");
        if(!somenteCodigos) {
            sql.append("order by 2 \n");
        }

        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet dadosSQL = pst.executeQuery()) {

                while (dadosSQL.next()) {
                    UsuarioVO usuarioVO = new UsuarioVO();
                    usuarioVO.setCodigo(dadosSQL.getInt("codigo"));
                    if(!somenteCodigos) {
                        usuarioVO.setNome(dadosSQL.getString("nome"));

                        ColaboradorVO colaboradorVO = new ColaboradorVO();
                        colaboradorVO.setCodigo(dadosSQL.getInt("colaborador"));
                        colaboradorVO.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
                        usuarioVO.setColaboradorVO(colaboradorVO);

                        usuarioVO.setTipoColaboradorEnum(TipoColaboradorEnum.getTipo(dadosSQL.getString("descricao")));
                    }

                    listaRetornar.add(usuarioVO);
                }
            }
        }
        return listaRetornar;
    }

    @Override
    public List<UsuarioVO> listarPorEmail(String emailDestinatario) throws Exception {
        List<UsuarioVO> listaUsuarioVO;
        try (PreparedStatement ps = con.prepareStatement(new StringBuilder()
                .append(" SELECT usuario.codigo as \"usuario.codigo\",                              ")
                .append("          usuario.nome as \"usuario.nome\",                                ")
                .append("         pessoa.codigo as \"pessoa.codigo\"                                ")

                .append("   FROM usuario                                                            ")
                .append("        INNER JOIN colaborador on colaborador.codigo = usuario.colaborador ")
                .append("        INNER JOIN      pessoa on      pessoa.codigo = colaborador.pessoa  ")
                .append("        INNER JOIN       email on       email.pessoa = pessoa.codigo       ")

                .append("WHERE email.email = ?                                                      ")
                .toString())) {

            ps.setString(1, emailDestinatario);

            try (ResultSet resultSet = ps.executeQuery()) {

                listaUsuarioVO = new ArrayList<UsuarioVO>();
                UsuarioVO usuarioVO;
                ColaboradorVO colaboradorVO;
                PessoaVO pessoaVO;
                while (resultSet.next()) {
                    usuarioVO = new UsuarioVO();
                    usuarioVO.setCodigo(resultSet.getInt("usuario.codigo"));
                    usuarioVO.setNome(resultSet.getString("usuario.nome"));

                    pessoaVO = new PessoaVO();
                    pessoaVO.setCodigo(resultSet.getInt("pessoa.codigo"));

                    colaboradorVO = new ColaboradorVO();
                    colaboradorVO.setPessoa(pessoaVO);

                    usuarioVO.setColaboradorVO(colaboradorVO);
                    listaUsuarioVO.add(usuarioVO);
                }
            }
        }

        return listaUsuarioVO;
    }

    public UsuarioVO consultarUsuarioEmailLeadMeta(Integer empresa,String email, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT usu.codigo, usu.administrador, usu.cliente,usu.tipoUsuario, usu.senha,  \n");
        sql.append("       usu.username, usu.nome, usu.dataAlteracaoSenha, usu.permiteAlterarPropriaSenha,  \n");
        sql.append("       usu.serviceUsuario, usu.serviceSenha, sqlColEmp.codigo as colaborador \n");
        sql.append("FROM usuario usu \n");
        sql.append("inner join( \n");
        sql.append("	select us.codigo as codigoUsuario, colEmp.codigo, colEmp.pessoa, colEmp.situacao, colEmp.empresa \n");
        sql.append("	from colaborador col \n");
        sql.append("	inner join usuario us on us.colaborador = col.codigo \n");
        sql.append("	inner join email e on e.pessoa = col.pessoa \n");
        sql.append("	inner join usuarioperfilacesso upa on upa.usuario = us.codigo \n");
        sql.append("	inner join colaborador colEmp on colEmp.pessoa = col.pessoa AND colEmp.empresa = upa.empresa\n");
        sql.append("	where upa.empresa = ").append(empresa).append(" and colEmp.situacao = 'AT' and e.email ilike '").append(email.toLowerCase()).append("' ");
        sql.append("	) as sqlColEmp on sqlColEmp.codigoUsuario = usu.codigo  \n");
        sql.append("WHERE (SELECT \n");
        sql.append("        count(*) \n");
        sql.append("FROM vinculo v \n");
        sql.append("WHERE v.colaborador = sqlColEmp.codigo) > 0 ");

        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
            }
        }
        return null;
    }

    public boolean consultarSituacao(Integer codigoUsuario, Integer codigoEmpresa) throws SQLException {
        String sql = "SELECT col.situacao FROM usuario usu\n" +
                "INNER JOIN usuarioperfilacesso upa ON usu.codigo = upa.usuario\n" +
                "INNER JOIN colaborador col ON usu.colaborador = col.codigo\n" +
                "WHERE usu.codigo = "+codigoUsuario+"\n" +
                "AND upa.empresa = "+codigoEmpresa+";";

        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql)) {
                if (rs.next()) {
                    return "AT".equals(rs.getString("situacao"));
                }
            }
        }
        return false;
    }


    public String enviarEmailSenha(String key, UsuarioVO usuarioVO) {
        try {
            if (usuarioVO != null && !UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                if (UteisValidacao.validaEmail(usuarioVO.getEmailVO().getEmail())) {

                    StringBuffer url = request().getRequestURL();
                    URL u = new URL(url.toString());
                    String urlAcessar = getUrlLoginExterno() + "/" + key;
                    if (getRedirectLoginServidorLocal()) {
                        urlAcessar = u.getProtocol() + "://" + u.getHost() + ":" + u.getPort() + "/login/" + key;
                    }

                    String descEmpresa = "";
                    String naAcademia = "";
                    if (usuarioVO.getColaboradorVO() != null && usuarioVO.getColaboradorVO().getEmpresa() != null) {
                        naAcademia = "na academia ";
                        String nomeAcademia = usuarioVO.getColaboradorVO().getEmpresa().getNome().trim().toUpperCase();
                        if (nomeAcademia.contains("ACAD.") || nomeAcademia.contains("ACADEMIA")) {
                            naAcademia = "na ";
                        }
                        descEmpresa = naAcademia + "<b>" + nomeAcademia + "</b>";
                    }
                    String mensagemSuperior = "Seu cadastro no sistema Pacto foi criado com sucesso " + descEmpresa + ".<br>Confira seu usuário e senha abaixo:";
                    String mensagemInferior = "A partir de agora você começa uma nova jornada na Gestão de Alta Performance e estamos felizes com essa parceria! <br><br>Seja muito bem vindo e bom trabalho!<br>";

                    StringBuilder corpoEmail = gerarCorpoEmail(usuarioVO, mensagemSuperior, mensagemInferior, "Acessar o Sistema", urlAcessar, true);
                    StringBuffer texto = new StringBuffer(corpoEmail);
                    String assunto = "Informações de acesso ao ZillyonWeb";
                    UteisValidacao.enviarEmail(texto, usuarioVO.getEmailVO().getEmail(), usuarioVO.getNome(), assunto);
                    return "Email enviado com sucesso para: " + usuarioVO.getEmailVO().getEmail();
                }
            }
            return "";
        } catch (Exception ex) {
            return "Erro enviar email: " + ex.getMessage();
        }
    }

    public List<UsuarioVO> obterUsuariosPacto(int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM Usuario WHERE upper(username) in ('ADMIN', 'PACTOBR', 'RECOR')";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                return montarDadosConsulta(rs, nivelMontarDados, this.con);
            }
        }
    }

    public void gravarHistoricoAcessoBI(Integer usuario, Integer empresa, BIEnum biEnum) throws SQLException {
        if (UteisValidacao.emptyNumber(usuario)) {
            return;
        }

        String sql = "insert into historicoacessobi(dataregistro,usuario,empresa,bi) values (?,?,?,?)";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            int i = 0;
            pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            pst.setInt(++i, usuario);
            if (UteisValidacao.emptyNumber(empresa)) {
                pst.setNull(++i, Types.NUMERIC);
            } else {
                pst.setInt(++i, empresa);
            }
            if (biEnum == null) {
                pst.setNull(++i, Types.NUMERIC);
            } else {
                pst.setInt(++i, biEnum.getCodigo());
            }
            pst.execute();
        }
    }

    public String consultarEmailUsuarioEmail(Integer codigoUsuario) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select email from UsuarioEmail where usuario = " + codigoUsuario);
        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();
        while (rs.next()) {
            if (rs.getString("email") != null) {
                return rs.getString("email");
            }
        }
        return "";
    }

    public void incluirHistoricoSincronizacao(Integer usuario, boolean sucesso, String resposta, String operacao) throws SQLException {
        String sql = "insert into usuariosincronizacao(dataregistro, usuario, sucesso, resposta, operacao) values (?,?,?,?,?)";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            int i = 0;
            pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            pst.setInt(++i, usuario);
            pst.setBoolean(++i, sucesso);
            pst.setString(++i, resposta);
            pst.setString(++i, operacao);
            pst.execute();
        }
    }

    public List<UsuarioVO> consultarUsuariosSincronizarNovoLogin(boolean sincronizarTodos) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT \n");
        sqlStr.append("u.* \n");
        sqlStr.append("FROM Usuario u \n");
        sqlStr.append("inner join colaborador col on col.codigo = u.colaborador \n");
        sqlStr.append("where col.situacao = 'AT' \n");
        sqlStr.append("and u.administrador = false \n");
        sqlStr.append("and u.userName <> '' \n");
        sqlStr.append("and upper(u.userName) not in ('PACTOBR','ADMIN','RECOR') \n");
        sqlStr.append("and exists(select codigo from usuarioemail where usuario = u.codigo) \n");
        if (!sincronizarTodos) {
            sqlStr.append("and not exists(select codigo from usuariosincronizacao where sucesso and operacao = 'SAVE_USUARIO' and usuario = u.codigo) \n");
        }
        sqlStr.append("order by u.codigo \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_TODOS, this.con);
            }
        }
    }

    public void adicionarUsuarioServicoDescobrir(String ctx, UsuarioVO usuarioVO) {
        try {
            if(!UteisValidacao.emptyString(usuarioVO.getEmail()) &&
                    UteisValidacao.validaEmail(usuarioVO.getEmail())){

                String url = String.format("%s/prest/empresa/%s/v3/inserirUsuario", new Object[]{
                        PropsService.getPropertyValue(PropsService.urlOamd),
                        ctx
                });
                Map<String, String> params = new HashMap<String, String>();
                params.put("email", usuarioVO.getEmail());
                params.put("cpf", usuarioVO.getColaboradorVO().getPessoa().getCfp());
                params.put("telefone", usuarioVO.getColaboradorVO().getPessoa().getTelefones());
                params.put("dataNascimento", Uteis.getDataAplicandoFormatacao(usuarioVO.getColaboradorVO().getPessoa().getDataNasc(), "dd/MM/yyyy"));
                params.put("senha", usuarioVO.getSenha());
                ExecuteRequestHttpService.executeRequest(url, params);
            }
        } catch (Exception e) {
            Uteis.logar(e, Usuario.class);
        }
    }

    public List<UsuarioSincronizacaoVO> consultarLogSincronizacao(Integer usuario, Integer limit) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from usuariosincronizacao where usuario = ").append(usuario).append(" order by codigo desc ");
        if (!UteisValidacao.emptyNumber(limit)) {
            sql.append(" limit ").append(limit);
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                List<UsuarioSincronizacaoVO> lista = new ArrayList<>();
                while (rs.next()) {
                    UsuarioSincronizacaoVO obj = new UsuarioSincronizacaoVO();
                    obj.setCodigo(rs.getInt("codigo"));
                    obj.setDataRegistro(rs.getTimestamp("dataRegistro"));
                    obj.setUsuario(rs.getInt("usuario"));
                    obj.setOperacao(rs.getString("operacao"));
                    obj.setSucesso(rs.getBoolean("sucesso"));
                    obj.setResposta(rs.getString("resposta"));
                    lista.add(obj);
                }
                return lista;
            }
        }
    }

    public void alterarUsuarioGeral(UsuarioVO obj) throws SQLException {
        String sql = "UPDATE Usuario set usuarioGeral = ? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getUsuarioGeral());
            sqlAlterar.setInt(2, obj.getCodigo());
            sqlAlterar.execute();
        }
    }

    public String consultarUsuarioGeral(UsuarioVO obj) throws SQLException {
        if (obj == null || UteisValidacao.emptyNumber(obj.getCodigo())) {
            return null;
        }
        String sql = "SELECT usuarioGeral FROM Usuario WHERE codigo = " + obj.getCodigo();
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql)) {
                if (rs.next()) {
                    return rs.getString("usuarioGeral");
                }
                return null;
            }
        }
    }

    @Override
    public Boolean recursoHabilitado(TipoInfoMigracaoEnum recurso, Integer usuario, Integer empresa) throws Exception {
        if (!UteisValidacao.emptyNumber(empresa) &&
                !recurso.equals(TipoInfoMigracaoEnum.CONFIGURACOES)) {
            boolean padraoEmpresa = recursoPadraoEmpresa(recurso, empresa);
            if (padraoEmpresa) {
                return padraoEmpresa;
            }
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("i.info \n");
        sql.append("from infomigracao i \n");
        sql.append("inner join usuario u on u.codigo = i.usuario \n");
        sql.append("where i.tipoinfo = ").append(recurso.getId()).append(" \n");
        sql.append("and i.usuario = ").append(usuario).append(" \n");
        //recurso liberado somente para pactobr ou admin
        if (recurso.equals(TipoInfoMigracaoEnum.CONFIGURACOES)) {
            sql.append("AND UPPER(u.username) in ('PACTOBR','ADMIN') \n");
        }
        sql.append("order by i.codigo desc limit 1");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return rs.getString("info").equals("true");
                }
                return false;
            }
        }
    }

    public Boolean recursoPadraoEmpresa(TipoInfoMigracaoEnum recurso, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select exists( \n");
        sql.append("select \n");
        sql.append("e.codigo \n");
        sql.append("from empresa e \n");
        sql.append("where e.tiposInfoMigracaoPadrao ilike '%").append(recurso.name()).append("%' \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("and e.codigo = ").append(empresa).append(" \n");
        }
        sql.append(") as existe ");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return rs.getBoolean("existe");
                }
                return false;
            }
        }
    }

    public String origemRecursoHabilitado(TipoInfoMigracaoEnum recurso, Integer usuario) throws Exception {
        String sql = "SELECT origem from infomigracao where tipoinfo = " + recurso.getId()
                + " and usuario = " + usuario;
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql)) {
                if (rs.next()) {
                    return rs.getString("origem");
                }
                return "";
            }
        }
    }

    public void gravarRecurso(TipoInfoMigracaoEnum recurso, Integer usuario, String valor, UsuarioVO usuarioVO) throws Exception {
        gravarRecurso(recurso, usuario, valor, "ZW", usuarioVO);
    }

    public void gravarRecurso(TipoInfoMigracaoEnum recurso, Integer usuario, String valor,
                              String origem, UsuarioVO usuarioVO) throws Exception {
        String sql = "SELECT codigo,info from infomigracao where tipoinfo = " + recurso.getId()
                + " and usuario = " + usuario + " order by codigo desc limit 1";
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql)) {
                boolean gravar = true;
                while (rs.next()) {
                    gravar = false;
                    Integer codigo = rs.getInt("codigo");
                    String infoAnterior = rs.getString("info");
                    String sqlUp = ("update infomigracao set info = '" + valor + "' where codigo = " + codigo);
                    if (!UteisValidacao.emptyString(origem)) {
                        sqlUp = ("update infomigracao set origem = '" + origem + "', info = '" + valor + "' where codigo = " + codigo);
                    }
                    executarUpdate(sqlUp, con);
                    gerarLogRecursoInfoMigracao(recurso, usuario, infoAnterior, valor, usuarioVO, false);
                }

                if(gravar){
                    String sqlIns = ("insert into infomigracao(tipoinfo, info, usuario) values (" + recurso.getId() + ", '" + valor + "', " + usuario + ") ");
                    if (!UteisValidacao.emptyString(origem)) {
                        sqlIns = ("insert into infomigracao(tipoinfo, info, usuario, origem) values (" + recurso.getId() + ", '" + valor + "', " + usuario + ",'" + origem + "') ");
                    }
                    executarUpdate(sqlIns, con);
                    gerarLogRecursoInfoMigracao(recurso, usuario, "", valor, usuarioVO, true);
                }
            }
        }
    }

    public void incluirInfoMigracaoParaUsuarioSem(String valor, Integer codEmpresa,
                                                  TipoInfoMigracaoEnum tipoInfoMigracaoEnum, UsuarioVO usuarioVO) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("insert into infomigracao(tipoinfo, usuario, info, origem) ");
        sql.append("select ").append(tipoInfoMigracaoEnum.getId()).append(",u.codigo,'").append(valor).append("','' from usuario u ");
        sql.append("where u.codigo not in (select i.usuario from infomigracao i where i.tipoinfo = ").append(tipoInfoMigracaoEnum.getId()).append(") ");
        if (!UteisValidacao.emptyNumber(codEmpresa)) {
            sql.append("and u.codigo in (select p.usuario from usuarioperfilacesso p where p.usuario = u.codigo and p.empresa = ").append(codEmpresa).append(") ");
        }
        executarUpdate(sql.toString(), con);
        gerarLogRecursoInfoMigracao(tipoInfoMigracaoEnum, codEmpresa, "", valor, usuarioVO, true);
    }

    private void gerarLogRecursoInfoMigracao(TipoInfoMigracaoEnum tipoInfoMigracaoEnum, Integer usuario,
                                             String valorAnterior, String valorNovo, UsuarioVO usuarioVO,
                                             boolean novo) {
        Log logDAO;
        try {
            logDAO = new Log(con);

            LogVO obj = new LogVO();
            obj.setChavePrimaria(usuario.toString());
            obj.setNomeEntidade("INFOMIGRACAO");
            obj.setNomeEntidadeDescricao("INFOMIGRACAO");
            obj.setOperacao((novo ? "INCLUIR" : "ALTERAR") + " INFOMIGRACAO USUARIO - Usuario: " + usuario + " | TipoInfoMigracao: " + (tipoInfoMigracaoEnum != null ? (tipoInfoMigracaoEnum.getId() + " - " + tipoInfoMigracaoEnum.name()) : ""));
            obj.setPessoa(0);
            obj.setDataAlteracao(Calendario.hoje());
            try {
                if (usuarioVO != null) {
                    obj.setUsuarioVO(usuarioVO);
                    obj.setResponsavelAlteracao(usuarioVO.getNome());
                    obj.setUserOAMD(usuarioVO.getUserOamd());
                }
            } catch (Exception ex) {
                obj.setResponsavelAlteracao("");
            }
            obj.setNomeCampo("INFOMIGRACAO_INFO");
            obj.setValorCampoAnterior(valorAnterior);
            obj.setValorCampoAlterado(valorNovo);
            logDAO.incluirSemCommit(obj);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            logDAO = null;
        }
    }

    public List<UsuarioVO> consultarUsuariosPermissaoModoEmergencia() throws Exception {
        String sql = "SELECT * from usuario where permiteAcionarEmergencia = true";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql)) {
                return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_TODOS, this.con);
            }
        }

    }
}
