package negocio.facade.jdbc.basico;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorDocumentoRhVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ColaboradorDocumentoRhInterface;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 14/11/2022.
 */
public class ColaboradorDocumentoRh extends SuperEntidade implements ColaboradorDocumentoRhInterface {

    public ColaboradorDocumentoRh() throws Exception {
        super();
    }

    public ColaboradorDocumentoRh(Connection conexao) throws Exception {
        super(conexao);
    }


    public void incluir(ColaboradorDocumentoRhVO colaboradorDocumentoRhVO)throws Exception{
        colaboradorDocumentoRhVO.setDataLancamento(Calendario.hoje());
        PreparedStatement pst = con.prepareStatement("insert into colaboradorDocumentoRH (colaborador, descricaoDocumento, anexo, nomeAnexo, dataLancamento, usuario,identificadorAnexo) values(?,?,?,?,?,?,?) ");
        pst.setInt(1, colaboradorDocumentoRhVO.getColaboradorVO().getCodigo());
        pst.setString(2, colaboradorDocumentoRhVO.getDescricaoDocumento());
        pst.setString(3, colaboradorDocumentoRhVO.getAnexo());
        pst.setString(4, colaboradorDocumentoRhVO.getNomeAnexo());
        pst.setTimestamp(5, Uteis.getDataJDBCTimestamp(colaboradorDocumentoRhVO.getDataLancamento()));
        pst.setInt(6,colaboradorDocumentoRhVO.getUsuarioVO().getCodigo());
        pst.setInt(7,colaboradorDocumentoRhVO.getIdentificadorAnexo());
        pst.execute();
        colaboradorDocumentoRhVO.setCodigo(obterValorChavePrimariaCodigo());
        colaboradorDocumentoRhVO.setNovoObj(false);
    }

    public List<ColaboradorDocumentoRhVO> consultar(ColaboradorVO colaboradorVO)throws Exception{
        List<ColaboradorDocumentoRhVO> lista = new ArrayList<>();
        ResultSet rs = con.createStatement().executeQuery("select u.nome as nomeUsuario, cd.* from colaboradorDocumentoRH cd inner join usuario u on u.codigo = cd.usuario  where cd.colaborador =" + colaboradorVO.getCodigo());
        while (rs.next()){
            lista.add(montarDados(rs, colaboradorVO));
        }

        return lista;
    }

    public void excluir(Integer codigo)throws Exception{
        con.createStatement().execute("delete from colaboradorDocumentoRH where codigo = "+ codigo);
    }


    private ColaboradorDocumentoRhVO montarDados(ResultSet rs, ColaboradorVO colaboradorVO)throws Exception{
        ColaboradorDocumentoRhVO obj = new ColaboradorDocumentoRhVO();
        obj.setColaboradorVO(colaboradorVO);
        obj.setCodigo(rs.getInt("codigo"));
        obj.setUsuarioVO(new UsuarioVO());
        obj.getUsuarioVO().setCodigo(rs.getInt("usuario"));
        obj.getUsuarioVO().setNome(rs.getString("nomeUsuario"));
        obj.setDescricaoDocumento(rs.getString("descricaoDocumento"));
        obj.setAnexo(rs.getString("anexo"));
        obj.setNomeAnexo(rs.getString("nomeAnexo"));
        obj.setDataLancamento(rs.getTimestamp("dataLancamento"));
        obj.setIdentificadorAnexo(rs.getInt("identificadorAnexo"));
        return obj;
    }

    public Integer pesquisarIdentificarAnexo()throws Exception{
        ResultSet rs = con.createStatement().executeQuery("SELECT nextval('identificadorAnexoColaboradorRh_seq');");
        if (rs.next()){
            return rs.getBigDecimal("nextval").intValue();
        }
        return null;
    }



}
