/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import negocio.comuns.arquitetura.HorarioAcessoSistemaVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.HorarioAcessoSistemaInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
public class HorarioAcessoSistema extends SuperEntidade implements HorarioAcessoSistemaInterfaceFacade {

    public HorarioAcessoSistema() throws Exception {
        super();
        setIdEntidade("Usuario");
    }
    public HorarioAcessoSistema(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Usuario");
    }
    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>HorarioAcessoSistemaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>HorarioAcessoSistemaVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(HorarioAcessoSistemaVO obj) throws Exception {
        HorarioAcessoSistemaVO.validarDados(obj);
        //incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        int i = 1;
        String sql = "INSERT INTO HorarioAcessoSistema(diaSemana, horaInicial, horaFinal, usuario) VALUES ( ?,?,?,? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(i++, obj.getDiaSemana().getCodigo());
        sqlInserir.setString(i++, obj.getHoraInicial());
        sqlInserir.setString(i++, obj.getHoraFinal());
        sqlInserir.setInt(i++, obj.getUsuarioVO().getCodigo());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void alterar(HorarioAcessoSistemaVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            HorarioAcessoSistemaVO.validarDados(obj);
            //alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            int i = 1;
            String sql = "UPDATE horarioacessosistema set diaSemana=?, horaInicial=?, horaFinal=? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setString(i++, obj.getDiaSemana().getCodigo());
            sqlAlterar.setString(i++, obj.getHoraInicial());
            sqlAlterar.setString(i++, obj.getHoraFinal());
            sqlAlterar.setInt(i++, obj.getCodigo());
            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluir(Integer codigo) throws Exception {
        try {
            con.setAutoCommit(false);
            //excluir(getIdEntidade());
            String sql = "DELETE FROM horarioAcessoSistema WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, codigo.intValue());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirUsuarioHorarioAcessoSistema(Integer usuario) throws Exception {
        String sql = "DELETE FROM horarioacessosistema WHERE (usuario = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, usuario.intValue());
        sqlExcluir.execute();
    }

    /**
     * Método que consulta horário de acesso do sistema
     * que possui o dia da semana, horário inicial e final
     * @param horarioTurmaConcatenadoVO
     * @param diaSemana
     * @param controlarAcesso
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    public List<HorarioAcessoSistemaVO> consultarPorDiaSemana(String diaSemana,
            Integer usuario, int nivelMontarDados) throws Exception {
        // consultar(getIdEntidade(), true);
        String sqlStr = "select * from horarioAcessoSistema where diaSemana = "
                + "'" + diaSemana + "' and usuario = " + usuario.intValue() + ""
                + " order by horaInicial, horaFinal";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

     /**
     * Método que consulta horário de acesso do sistema
     * que possui o dia da semana, horário inicial e final
     * @param horarioTurmaConcatenadoVO
     * @param diaSemana
     * @param controlarAcesso
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    public List<HorarioAcessoSistemaVO> consultar(Integer usuario, int nivelMontarDados) throws Exception {
        // consultar(getIdEntidade(), true);
        String sqlStr = "select * from horarioAcessoSistema where usuario = "+ usuario.intValue();
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    @Override
    public HorarioAcessoSistemaVO consultarVerificarConflitoHorarios(HorarioAcessoSistemaVO horarioAcessoSistemaVO, int nivelMontarDados) throws Exception {
        String sqlStr = "select *  "
                + " from horarioAcessoSistema"
                + " where diasemana = '" + horarioAcessoSistemaVO.getDiaSemana().getCodigo() + "' "
                + " and codigo != " + horarioAcessoSistemaVO.getCodigo()
                + " and usuario = " + horarioAcessoSistemaVO.getUsuarioVO().getCodigo()
                + " and ((horainicial>='" + horarioAcessoSistemaVO.getHoraInicial() + "' and horafinal<= '" + horarioAcessoSistemaVO.getHoraFinal() + "' ) "
                + " or (horainicial <= '" + horarioAcessoSistemaVO.getHoraInicial() + "' and horafinal>='" + horarioAcessoSistemaVO.getHoraFinal() + "')"
                + " or (horainicial>='" + horarioAcessoSistemaVO.getHoraInicial() + "' and horainicial<= '" + horarioAcessoSistemaVO.getHoraFinal() + "' )  "
                + " or (horafinal >= '" + horarioAcessoSistemaVO.getHoraInicial() + "' and horafinal<='" + horarioAcessoSistemaVO.getHoraFinal() + "') "
                + "or (horaInicial <='" + horarioAcessoSistemaVO.getHoraInicial() + "' and horaFinal <='" + horarioAcessoSistemaVO.getHoraFinal() + "' "
                + "and horaInicial >='" + horarioAcessoSistemaVO.getHoraFinal() + "')) and codigo != " + horarioAcessoSistemaVO.getCodigo();
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        if (!tabelaResultado.next()) {
            return null;
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    public List<HorarioAcessoSistemaVO> consultarHorarioAtualDentroIntervalosPermitidos(Integer usuario, String horaAtual, String diaSemana, int nivelMontarDados) throws Exception {
        String sqlStr = "select * from horarioacessosistema where diasemana = '" + diaSemana + "' "
                + "and (horaInicial<= '" + horaAtual + "' and horaFinal>='" + horaAtual + "') and usuario = " + usuario.intValue();
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public boolean consultarSeExisteHorarioAcessoSistema() throws Exception {
        String sql = "select exists (select codigo from horarioacessosistema limit 1 ) as existe";
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        tabelaResultado.next();
        return tabelaResultado.getBoolean("existe");
    }

    public Integer consultarUltimoCodigo() throws Exception {
        String sql = "select max(codigo) as ultimoCodigo from horarioacessosistema";
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        tabelaResultado.next();
        return tabelaResultado.getInt("ultimoCodigo");
    }

    public void incluirHorarioAcessoSistemaUsuario(Integer usuarioprm, List objetos) throws Exception {
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Entrou no método incluirHorarioAcessoSistemaUsuario ");
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### PARÂMETROS");
        if (UteisValidacao.emptyList(objetos)) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### horários não preenchidos");
        }
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            HorarioAcessoSistemaVO obj = (HorarioAcessoSistemaVO) e.next();
            obj.setUsuarioVO(new UsuarioVO());
            obj.getUsuarioVO().setCodigo(usuarioprm);
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### " + obj.toString());
            incluir(obj);
        }
    }

    public void alterarHorarioAcessoSistemaUsuario(Integer usuario, List objetos) throws Exception {
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Entrou no método alterarHorarioAcessoSistemaUsuario ");
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### PARÂMETROS");
        if (usuario != null) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### usuario: " + usuario);

        }
        excluirUsuarioHorarioAcessoSistema(usuario);
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Excluiu os horários de acesso ");
        incluirHorarioAcessoSistemaUsuario(usuario, objetos);
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>HorarioTurmaVO</code> resultantes da consulta.
     */
    public List<HorarioAcessoSistemaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            HorarioAcessoSistemaVO obj = new HorarioAcessoSistemaVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public HorarioAcessoSistemaVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        HorarioAcessoSistemaVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            return obj;
        }
        return obj;
    }

    private HorarioAcessoSistemaVO montarDadosBasico(ResultSet dadosSQL) throws SQLException {
        HorarioAcessoSistemaVO obj = new HorarioAcessoSistemaVO();
        obj.setNovoObj(false);
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setHoraInicial(dadosSQL.getString("horaInicial"));
        obj.setHoraFinal(dadosSQL.getString("horaFinal"));
        obj.setDiaSemana(DiaSemana.getDiaSemana(dadosSQL.getString("diaSemana")));
        obj.setUsuarioVO(new UsuarioVO());
        obj.getUsuarioVO().setCodigo(dadosSQL.getInt("usuario"));
        return obj;
    }
}
