package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ClienteTitularDependenteVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.enumerador.TipoCategoriaClubeEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ClienteTitularDependenteInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * date : 28/01/2015 12:07:02
 * autor: Ulisses
 */
public class ClienteTitularDependente extends SuperEntidade implements ClienteTitularDependenteInterfaceFacade {
	
    public ClienteTitularDependente() throws Exception {
        super();
    }

    public ClienteTitularDependente(Connection conexao) throws Exception {
        super(conexao);
    }
    
    public ClienteTitularDependenteVO consultarPorCodigo(Integer codigo,  int nivelMontarDados) throws Exception{
    	StringBuilder sql = new StringBuilder();
    	sql.append(" select * from clienteTitularDependente where codigo = ").append(codigo);
    	Statement st = getCon().createStatement();
    	ResultSet rs = st.executeQuery(sql.toString());
    	if (rs.next())
    		return montarDados(rs, nivelMontarDados);
    	return null;
    }
    
    public void excluir(ClienteVO clienteVO) throws Exception{
    	String sql = "delete from clienteTitularDependente where cliente = ?";
    	PreparedStatement pst = con.prepareStatement(sql);
    	pst.setInt(1, clienteVO.getCodigo());
    	pst.execute();
    }

	public void excluirFamilia(ClienteVO clienteTitular) throws Exception{
		StringBuilder sql = new StringBuilder();
		sql.append("delete from clienteTitularDependente \n");
		sql.append("where codigo in( \n");
		sql.append("	select fam.codigo \n");
		sql.append("	from clienteTitularDependente tit \n");
		sql.append("	inner join cliente cliTit on cliTit.codigo = tit.cliente \n");
		sql.append("	inner join clienteTitularDependente fam on tit.clienteTitular = fam.clienteTitular \n");
		sql.append("	where cliTit.codigo = ").append(clienteTitular.getCodigo()).append(")");
		PreparedStatement pst = con.prepareStatement(sql.toString());
		pst.execute();
	}

    public ClienteVO consultarClienteTitular(ClienteVO clienteDependenteAssemelhado, int nivelMontarDados) throws Exception{
    	StringBuilder sql = new StringBuilder();
    	sql.append("select ctdTitular.cliente \n");
    	sql.append(" from clienteTitularDependente ctd \n");
    	sql.append(" inner join clienteTitularDependente ctdTitular on ctdTitular.codigo = ctd.clienteTitular \n");
    	sql.append(" where ctd.cliente = ").append(clienteDependenteAssemelhado.getCodigo()).append("\n");
    	sql.append(" and ctd.clienteTitular = ").append("(select clienteTitular from clienteTitularDependente where cliente = ").append(clienteDependenteAssemelhado.getCodigo()).append(" ")
        .append(" and dataLancamento = (select max(dataLancamento) from clienteTitularDependente where cliente = ").append(clienteDependenteAssemelhado.getCodigo()).append(")) \n");
    	Statement st = getCon().createStatement();
    	ResultSet rs = st.executeQuery(sql.toString());
    	if (rs.next()){
			return getFacade().getCliente().consultarPorCodigo(rs.getInt("cliente"), false, nivelMontarDados);
    	}
    	return null;
    	
    }
    
    public void alterarPagaSeparadoDependenteAssemelhado(ClienteTitularDependenteVO clienteTitularDependenteVO) throws Exception{
    	String sql = "update clienteTitularDependente set pagaSeparado = ? where codigo = ?";
    	PreparedStatement pst = con.prepareStatement(sql);
    	pst.setBoolean(1, clienteTitularDependenteVO.getPagaSeparado());
    	pst.setInt(2, clienteTitularDependenteVO.getCodigo());
    	pst.execute();
    }
    
    public ClienteTitularDependenteVO consultarPorCPF(Integer clienteTitular, String cpf, TipoCategoriaClubeEnum tipoCategoriaClube,  int nivelMontarDados) throws Exception{
    	StringBuilder sql = new StringBuilder();
    	sql.append("select * \n");
    	sql.append("from clienteTitularDependente ctd \n");
    	sql.append("inner join cliente cli on cli.codigo = ctd.cliente \n");
    	sql.append("inner join categoria cat on cat.codigo = cli.categoria \n");
    	sql.append("inner join pessoa p on p.codigo = cli.pessoa \n");
    	sql.append("where p.cfp = '").append(cpf).append("' \n");
    	if (clienteTitular != null){
    	  sql.append("      and  ctd.clienteTitular = ").append(clienteTitular);
    	}
    	sql.append("      and  cat.tipoCategoriaClube = ").append(tipoCategoriaClube.getCodigo());
    	Statement st = getCon().createStatement();
    	ResultSet rs = st.executeQuery(sql.toString());
    	if (rs.next())
    		return montarDados(rs, nivelMontarDados);
    	return null;
    }
    
    public void incluir(ClienteTitularDependenteVO clienteTitularDependenteVO) throws Exception{
    	clienteTitularDependenteVO.validarDados();
        StringBuilder sql = new StringBuilder();
        sql.append("insert into clienteTitularDependente (cliente, clientetitular, parentesco, dataLancamento) values(?,?,?,?) ");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setInt(1, clienteTitularDependenteVO.getClienteVO().getCodigo());
        if (clienteTitularDependenteVO.getClienteTitular() != null){
            pst.setInt(2, clienteTitularDependenteVO.getClienteTitular().getCodigo());        	
        }else{
           pst.setNull(2, java.sql.Types.NULL);
        }
        if ((clienteTitularDependenteVO.getParentescoVO() != null) && (clienteTitularDependenteVO.getParentescoVO().getCodigo() != null) &&
           (clienteTitularDependenteVO.getParentescoVO().getCodigo()  >0)){
            pst.setInt(3, clienteTitularDependenteVO.getParentescoVO().getCodigo());        	
        }else{
        	pst.setNull(3, java.sql.Types.NULL);
        }
        pst.setTimestamp(4, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        pst.execute();
        clienteTitularDependenteVO.setCodigo(obterValorChavePrimariaCodigo());
        clienteTitularDependenteVO.setNovoObj(false);
    	if ((clienteTitularDependenteVO.getClienteVO().getCategoria().getTipoCategoriaClube().equals(TipoCategoriaClubeEnum.PROPRIETARIO.getCodigo())) ||
        	    (clienteTitularDependenteVO.getClienteVO().getCategoria().getTipoCategoriaClube().equals(TipoCategoriaClubeEnum.CONTRIBUINTE_GERAL.getCodigo()))){
            sql.delete(0, sql.length());
            sql.append("update clienteTitularDependente set clientetitular = ").append(clienteTitularDependenteVO.getCodigo());
            sql.append(" where codigo = ").append(clienteTitularDependenteVO.getCodigo());
            PreparedStatement pstAlterar = con.prepareStatement(sql.toString());
            pstAlterar.execute();
            ClienteTitularDependenteVO titular = new ClienteTitularDependenteVO(); 
            titular.setCodigo(clienteTitularDependenteVO.getCodigo());
            clienteTitularDependenteVO.setClienteTitular(titular);
    	}
    	
    }

    public boolean clienteEhTitular(Integer codigoCliente) throws Exception{
    	String sql = "select codigo, clienteTitular from clienteTitularDependente where cliente = ?";
    	PreparedStatement pst = con.prepareStatement(sql);
    	pst.setInt(1, codigoCliente);
    	ResultSet rs = pst.executeQuery();
    	if (rs.next()){
    	   return (rs.getInt("codigo") == rs.getInt("clienteTitular"));	
    	}
    	return false;
    }

	public boolean pessoaEhTitular(Integer codigoPessoa) throws Exception {
		String sql = "select ctd.codigo, clienteTitular from clienteTitularDependente ctd INNER JOIN cliente cli ON ctd.cliente = cli.codigo where pessoa = ?";
		PreparedStatement pst = con.prepareStatement(sql);
		pst.setInt(1, codigoPessoa);
		ResultSet rs = pst.executeQuery();
		return rs.next() && (rs.getInt("codigo") == rs.getInt("clienteTitular"));
	}
    
    
    public List<ClienteTitularDependenteVO> consultarFamiliaPorCodigoCliente(Integer codigoCliente,  int nivelMontarDados) throws Exception{
    	StringBuilder sql = new StringBuilder();
    	sql.append(" select ct.*, extract(year from age(coalesce(datanasc, current_timestamp))) as idade, \n ");
		sql.append(" (ct.codigo = clientetitular)                                  AS titular\n");
    	sql.append(" from clienteTitularDependente ct \n");
    	sql.append(" inner join cliente cli on cli.codigo = ct.cliente \n");
    	sql.append(" left join categoria cat on cat.codigo = cli.categoria \n");    	
    	sql.append(" inner join pessoa p on p.codigo = cli.pessoa \n");
    	sql.append(" where clienteTitular = ").append("(select clienteTitular from clienteTitularDependente where cliente = ").append(codigoCliente).append(") \n ");
    	sql.append(" order by titular desc, cli.situacaoClube, situacao asc, cat.tipoCategoriaClube asc, idade  desc ");
    	Statement st = getCon().createStatement();
    	return montarDadosConsulta(st.executeQuery(sql.toString()), nivelMontarDados);
    }
    
    public ClienteTitularDependenteVO consultarPorCodigoCliente(Integer codigoCliente,  int nivelMontarDados) throws Exception{
    	StringBuilder sql = new StringBuilder();
    	sql.append(" select * from clienteTitularDependente ");
    	sql.append(" where cliente = ").append(codigoCliente);
    	Statement st = getCon().createStatement();
    	ResultSet dados = st.executeQuery(sql.toString());
    	if (dados.next())
    	  return montarDados(dados, nivelMontarDados);
    	return null;
    	
    }

	public ClienteTitularDependenteVO consultarPorCodigoPessoa(Integer codigoPessoa, int nivelMontarDados) throws Exception{
		StringBuilder sql = new StringBuilder();
		sql.append(" select ct.*  \n");
		sql.append("from clienteTitularDependente ct \n");
		sql.append("inner join cliente cli on cli.codigo = ct.cliente \n");
		sql.append("inner join pessoa p on p.codigo = cli.pessoa \n");
		sql.append(" where p.codigo = ").append(codigoPessoa);
		Statement st = getCon().createStatement();
		ResultSet dados = st.executeQuery(sql.toString());
		if (dados.next())
			return montarDados(dados, nivelMontarDados);
		return null;
	}
    
    private List<ClienteTitularDependenteVO> montarDadosConsulta(ResultSet dados, int nivelMontarDados) throws Exception {
        List<ClienteTitularDependenteVO> lista = new ArrayList<ClienteTitularDependenteVO>();
        while (dados.next()) {
        	ClienteTitularDependenteVO obj = montarDados(dados, nivelMontarDados);
            lista.add(obj);
        }
        return lista;
    }

    private ClienteTitularDependenteVO montarDadosBasico(ResultSet dadosSQL)throws Exception {
		ClienteTitularDependenteVO clienteTitularDependenteVO = new ClienteTitularDependenteVO();
		clienteTitularDependenteVO.setCodigo(dadosSQL.getInt("codigo"));
		clienteTitularDependenteVO.setClienteVO(new ClienteVO());
		clienteTitularDependenteVO.getClienteVO().setCodigo(dadosSQL.getInt("cliente"));
		clienteTitularDependenteVO.setClienteTitular(new ClienteTitularDependenteVO(dadosSQL.getInt("clienteTitular")));
		clienteTitularDependenteVO.setDataLancamento(dadosSQL.getTimestamp("dataLancamento"));
		clienteTitularDependenteVO.setPagaSeparado(dadosSQL.getBoolean("pagaSeparado"));
		clienteTitularDependenteVO.setNovoObj(false);
		
    	return clienteTitularDependenteVO;
    }

	private ClienteTitularDependenteVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
		ClienteTitularDependenteVO clienteTitularDependenteVO = montarDadosBasico(dadosSQL);
		
		if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
			return clienteTitularDependenteVO;
		}
		
		clienteTitularDependenteVO.setClienteVO(getFacade().getCliente().consultarPorCodigo(dadosSQL.getInt("cliente"), false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));

		Integer codigoParentesco = dadosSQL.getInt("parentesco");
		if ((codigoParentesco != null) && (codigoParentesco > 0)) {
            clienteTitularDependenteVO.setParentescoVO(getFacade().getParentesco().consultarPorCodigoExato(dadosSQL.getInt("parentesco"), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
			clienteTitularDependenteVO.setClienteTitular(consultarPorCodigo(dadosSQL.getInt("clienteTitular"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
		}

		return clienteTitularDependenteVO;
	}

}
