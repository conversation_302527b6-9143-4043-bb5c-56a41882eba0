package negocio.facade.jdbc.basico.webservice;

import br.com.pactosolucoes.comuns.json.SuperJSON;

import java.util.List;

public class EstornoResumidoContratoJSON extends SuperJSON {
    private int codigoContrato;
    private String dataEstorno;
    private String unidade;
    private Double valorTotalContrato;
    private Double valorPagoEstornado;
    private String consultor;
    private String plano;
    private String responsavelContrato;
    private int codigoCliente;
    private String matricula;
    private String nomeCliente;
    private List<ModalidadeEstornoJSON> modalidadeEstorno;
    private List<PagamentoEstornoJSON> pagamentoEstorno;

    public int getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(int codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public String getDataEstorno() {
        return dataEstorno;
    }

    public void setDataEstorno(String dataEstorno) {
        this.dataEstorno = dataEstorno;
    }

    public String getUnidade() {
        return unidade;
    }

    public void setUnidade(String unidade) {
        this.unidade = unidade;
    }

    public Double getValorTotalContrato() {
        return valorTotalContrato;
    }

    public void setValorTotalContrato(Double valorTotalContrato) {
        this.valorTotalContrato = valorTotalContrato;
    }

    public Double getValorPagoEstornado() {
        return valorPagoEstornado;
    }

    public void setValorPagoEstornado(Double valorPagoEstornado) {
        this.valorPagoEstornado = valorPagoEstornado;
    }

    public String getConsultor() {
        return consultor;
    }

    public void setConsultor(String consultor) {
        this.consultor = consultor;
    }

    public String getPlano() {
        return plano;
    }

    public void setPlano(String plano) {
        this.plano = plano;
    }

    public String getResponsavelContrato() {
        return responsavelContrato;
    }

    public void setResponsavelContrato(String responsavelContrato) {
        this.responsavelContrato = responsavelContrato;
    }

    public int getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(int codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public List<ModalidadeEstornoJSON> getModalidadeEstorno() {
        return modalidadeEstorno;
    }

    public void setModalidadeEstorno(List<ModalidadeEstornoJSON> modalidadeEstorno) {
        this.modalidadeEstorno = modalidadeEstorno;
    }

    public List<PagamentoEstornoJSON> getPagamentoEstorno() {
        return pagamentoEstorno;
    }

    public void setPagamentoEstorno(List<PagamentoEstornoJSON> pagamentoEstorno) {
        this.pagamentoEstorno = pagamentoEstorno;
    }
}