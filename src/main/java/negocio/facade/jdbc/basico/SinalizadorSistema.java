/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.enumeradores.SinalizadorEnum;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.SinalizadorSistemaInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
public class SinalizadorSistema extends SuperEntidade implements SinalizadorSistemaInterfaceFacade {
    public SinalizadorSistema() throws Exception{
        super();
    }
    
    public SinalizadorSistema(Connection con) throws Exception{
        super(con);
    }

    @Override
    public boolean consultarPorSinalizador(SinalizadorEnum sin) throws Exception {
        try{
            PreparedStatement stm = con.prepareStatement("SELECT * FROM sinalizadorsistema WHERE sinalizador = ?;");
            stm.setInt(1, sin.getCodigo());
            ResultSet rs = stm.executeQuery();
            if(rs.next()){
                return rs.getBoolean("sinal");
            }
        }catch(Exception e){
        }
        return false;
    }

    @Override
    public void deletarPorBI(SinalizadorEnum sin) throws Exception {
          executarConsulta("DELETE FROM sinalizadorsistema WHERE sinalizador = "+sin.getCodigo(), con);
    }

    @Override
    public void incluir(SinalizadorEnum sin, boolean value) throws Exception {
        try {
            con.prepareStatement("delete from sinalizadorsistema where sinalizador = " + sin.getCodigo()).execute();
            PreparedStatement stmIncluir = con.prepareStatement("INSERT INTO sinalizadorsistema (sinalizador, sinal) VALUES (?,?)");
            stmIncluir.setInt(1, sin.getCodigo());
            stmIncluir.setBoolean(2, value);
           
            stmIncluir.execute();
        }catch(Exception e){
            try{
                PreparedStatement stmAlterar = con.prepareStatement("update  sinalizadorsistema set sinal =? where sinalizador =?");
                stmAlterar.setBoolean(1, value);
                stmAlterar.setInt(2, sin.getCodigo());
                stmAlterar.execute();
            }catch(Exception ex){
            }
        }
            
    }
    
    
}
