package negocio.facade.jdbc.basico;

import negocio.comuns.basico.PactoPayCobrancaAntecipadaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.PactoPayCobrancaAntecipadaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class PactoPayCobrancaAntecipada extends SuperEntidade implements PactoPayCobrancaAntecipadaInterfaceFacade {

    public PactoPayCobrancaAntecipada() throws Exception {
        super();
    }

    public PactoPayCobrancaAntecipada(Connection conexao) throws Exception {
        super(conexao);
    }

    private PactoPayCobrancaAntecipadaVO montarDadosBasico(ResultSet rs) throws Exception {
        PactoPayCobrancaAntecipadaVO obj = new PactoPayCobrancaAntecipadaVO();
        obj.setNovoObj(false);
        obj.setCodigo(rs.getInt("codigo"));
        obj.getPessoaVO().setCodigo(rs.getInt("pessoa"));
        obj.setDataRegistro(rs.getTimestamp("dataRegistro"));
        obj.setDados(rs.getString("dados"));
        obj.setMeioEnvio(MeioEnvio.getMeioEnvioPorCodigo(rs.getInt("meioEnvio")));
        obj.getPactoPayComunicacao().setCodigo(rs.getInt("pactopaycomunicacao"));
        return obj;
    }

    private PactoPayCobrancaAntecipadaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        PactoPayCobrancaAntecipadaVO obj = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            montarDadosPessoa(obj, Uteis.NIVELMONTARDADOS_GESTAOREMESSA, con);
            return obj;
        }
        return obj;
    }

    private void montarDadosPessoa(PactoPayCobrancaAntecipadaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (UteisValidacao.emptyNumber(obj.getPessoaVO().getCodigo())) {
            obj.setPessoaVO(new PessoaVO());
            return;
        }

        Pessoa pessoaDAO;
        try {
            pessoaDAO = new Pessoa(con);
            obj.setPessoaVO(pessoaDAO.consultarPorChavePrimaria(obj.getPessoaVO().getCodigo(), nivelMontarDados));
        } finally {
            pessoaDAO = null;
        }
    }

    public void incluir(PactoPayCobrancaAntecipadaVO obj) throws Exception {
        try {
            this.con.setAutoCommit(false);
            incluirSemCommit(obj);
            this.con.commit();
        } catch (Exception ex) {
            this.con.rollback();
            throw ex;
        } finally {
            this.con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(PactoPayCobrancaAntecipadaVO obj) throws Exception {
        PactoPayCobrancaAntecipadaVO.validarDados(obj);
        String sql = "INSERT INTO PactoPayCobrancaAntecipada(dataRegistro, pessoa, dados, meioEnvio, pactopaycomunicacao) VALUES (?,?,?,?,?)";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
            resolveIntegerNull(ps, ++i, obj.getPessoaVO().getCodigo());
            ps.setString(++i, obj.getDados());
            ps.setInt(++i, obj.getMeioEnvio() == null ? 0 : obj.getMeioEnvio().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getPactoPayComunicacao().getCodigo());
            ps.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    private void alterar(PactoPayCobrancaAntecipadaVO obj) throws Exception {
        PactoPayCobrancaAntecipadaVO.validarDados(obj);
        String sql = "UPDATE PactoPayCobrancaAntecipada SET pessoa = ?, dados = ?, meioEnvio = ?, pactopaycomunicacao = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            resolveIntegerNull(ps, ++i, obj.getPessoaVO().getCodigo());
            ps.setString(++i, obj.getDados());
            ps.setInt(++i, obj.getMeioEnvio() == null ? 0 : obj.getMeioEnvio().getCodigo());
            resolveIntegerNull(ps, ++i, obj.getPactoPayComunicacao().getCodigo());
            ps.setInt(++i, obj.getCodigo());
            ps.execute();
        }
    }

    public PactoPayCobrancaAntecipadaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM PactoPayCobrancaAntecipada WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigo);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, this.con);
                } else {
                    return null;
                }
            }
        }
    }
}
