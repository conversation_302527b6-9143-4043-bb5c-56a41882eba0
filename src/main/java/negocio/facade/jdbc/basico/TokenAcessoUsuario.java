/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.basico;

import java.sql.Connection;
import java.sql.PreparedStatement;
import negocio.comuns.basico.TokenAcessoUsuarioVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.TokenAcessoUsuarioInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
public class TokenAcessoUsuario extends SuperEntidade implements TokenAcessoUsuarioInterfaceFacade{

        
    public TokenAcessoUsuario() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public TokenAcessoUsuario(Connection con) throws Exception {
        super(con);
        setIdEntidade("Contrato");
    }
    
    @Override
    public void incluir(TokenAcessoUsuarioVO obj) throws Exception {
//        String sql = "INSERT INTO ContratoAssinaturaDigital(contrato, usuarioresponsavel, contratotextoresponsavel,"
//                + "documentos, assinatura, atestado) VALUES (?,?,?,?,?,?)";
//        int i = 1;
//        PreparedStatement sqlInserir = con.prepareStatement(sql);
//        sqlInserir.setInt(i++, obj.getContrato().getCodigo());
//        sqlInserir.setInt(i++, obj.getUsuarioResponsavel().getCodigo());
//        sqlInserir.setInt(i++, obj.getContratoTextoPadrao().getCodigo());
//        sqlInserir.setString(i++, obj.getDocumentos());
//        sqlInserir.setString(i++, obj.getAssinatura());
//        sqlInserir.setString(i++, obj.getAtestado());
//        sqlInserir.execute();
//        obj.setCodigo(obterValorChavePrimariaCodigo());
//        obj.setNovoObj(false);
    }

    @Override
    public void excluir(TokenAcessoUsuarioVO obj) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public void validarToken(String token) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }
    
}
