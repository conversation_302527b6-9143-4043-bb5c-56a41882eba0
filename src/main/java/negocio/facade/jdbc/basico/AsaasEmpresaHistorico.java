package negocio.facade.jdbc.basico;

import negocio.comuns.basico.AsaasEmpresaHistoricoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.Connection;
import java.sql.PreparedStatement;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 26/05/2023
 */

public class AsaasEmpresaHistorico extends SuperEntidade {

    public AsaasEmpresaHistorico() throws Exception {
        super();
    }

    public AsaasEmpresaHistorico(Connection conexao) throws Exception {
        super(conexao);
    }

    public void incluir(AsaasEmpresaHistoricoVO obj) throws Exception {
        String sql = "INSERT INTO public.AsaasEmpresaHistorico(empresa, dataCriacao, paramsEnvio, paramsResposta)\n"
                + " VALUES (?, ?, ?, ?)";

        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 1;
            sqlInserir.setInt(i++, obj.getEmpresa());
            sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getDataCriacao()));
            sqlInserir.setString(i++, obj.getParamsEnvio());
            sqlInserir.setString(i++, obj.getParamsResposta());

            sqlInserir.execute();
        }
    }

}
