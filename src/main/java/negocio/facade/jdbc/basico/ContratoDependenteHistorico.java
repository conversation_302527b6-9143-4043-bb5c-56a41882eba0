package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ContratoDependenteHistoricoVO;
import negocio.comuns.basico.ContratoDependenteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ContratoDependenteHistoricoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Optional;

public class ContratoDependenteHistorico extends SuperEntidade implements ContratoDependenteHistoricoInterfaceFacade {

    public static final String SQL_HISTORICO_DEPENDENTES = "select %s from contratodependentehistorico cdh\n" +
            "inner join cliente cli on cdh.dependente = cli.codigo\n" +
            "inner join contratodependente cd ON cdh.contratodependente = cd.codigo\n" +
            "inner join contrato c ON cd.contrato = c.codigo\n" +
            "inner join pessoa p ON cli.pessoa = p.codigo\n" +
            "where 1 = 1\n" +
            "%s";

    public static final String SQL_DEPENDENTES_VINCULADOS_LISTAR = String.format(SQL_HISTORICO_DEPENDENTES,
            "c.*, p.nome as nomedependente, cli.matricula as matriculadependente, cli.codigo as codigodependente",
            " AND cdh.iniciodependencia  >= '%s' AND cdh.iniciodependencia  <= '%s' %s ");

    public static final String SQL_DEPENDENTES_DESVINCULADOS_LISTAR = String.format(SQL_HISTORICO_DEPENDENTES,
            "c.*, p.nome as nomedependente, cli.matricula as matriculadependente, cli.codigo as  codigodependente",
            " AND (cdh.finaldependencia + interval '1 day')  >= '%s'" +
            " AND (cdh.finaldependencia + interval '1 day') <= '%s' %s ");


    public ContratoDependenteHistorico() throws Exception {
    }

    public ContratoDependenteHistorico(Connection conexao) throws Exception {
        super(conexao);
    }


    public void incluirDependente(ContratoDependenteVO obj) throws Exception {
        if (consultarHistoricoEmAberto(obj).isPresent()) {
            throw new ConsistirException("Esta dependência está vigente ainda");
        }

        String sql = "INSERT INTO contratodependentehistorico(contratodependente, dependente, iniciodependencia)"
                + " VALUES (?, ?, ?)";

        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            int i = 0;
            sqlInserir.setInt(++i, obj.getCodigo());
            sqlInserir.setInt(++i, obj.getCliente().getCodigo());
            sqlInserir.setDate(++i, Uteis.getDataJDBC(Calendario.hoje()));
            sqlInserir.execute();
        }
    }

    public void removerDependente(ContratoDependenteVO obj) throws Exception {
        ContratoDependenteHistoricoVO historico = consultarHistoricoEmAberto(obj).orElse(null);
        if (historico == null) {
            return;
        }

        boolean mesmoDia = Calendario.igual(historico.getDataInicio(), Calendario.hoje());
        if (mesmoDia) {
            excluir(historico);
        } else {
            String sql = "UPDATE contratodependentehistorico SET finaldependencia = ? WHERE codigo = ?";
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                int i = 0;
                ps.setDate(++i, Uteis.getDataJDBC(Calendario.somarDias(Calendario.hoje(), -1)));
                ps.setInt(++i, historico.getCodigo());
                ps.execute();
            }
        }
    }

    public void excluir(ContratoDependenteHistoricoVO historico) throws SQLException {
        String sql = "DELETE FROM contratodependentehistorico WHERE codigo = " + historico.getCodigo();
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            stm.execute();
        }
    }

    public Optional<ContratoDependenteHistoricoVO> consultarHistoricoEmAberto(ContratoDependenteVO contratoDependenteVO) throws Exception {
        String sql = "SELECT * FROM contratodependentehistorico\n" +
                "WHERE contratodependente = ?\n" +
                "AND finaldependencia is null;";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            int i = 0;
            ps.setInt(++i, contratoDependenteVO.getCodigo());
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    ContratoDependenteHistoricoVO historico = new ContratoDependenteHistoricoVO();
                    historico.setCodigo(rs.getInt("codigo"));
                    historico.getContratoDependenteVO().setCodigo(rs.getInt("contratodependente"));
                    historico.getDependente().setCodigo(rs.getInt("dependente"));
                    historico.setDataInicio(rs.getDate("iniciodependencia"));
                    historico.setDataFinal(rs.getDate("finaldependencia"));
                    historico.setNovoObj(false);
                    return Optional.of(historico);
                }
                return Optional.empty();
            }
        }
    }
}
