package negocio.facade.jdbc.crm;

import negocio.comuns.crm.ConfiguracaoDiasPosVendaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ConfiguracaoDiasPosVendaVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ConfiguracaoDiasPosVendaVO</code>.
 * Encapsula toda a interação com o banco de dados.
 *
 * @see ConfiguracaoDiasPosVendaVO
 * @see SuperEntidade
 * @see ConfiguracaoSistemaCRM
 */
public class ConfiguracaoDiasPosVenda extends SuperEntidade {

    public ConfiguracaoDiasPosVenda() throws Exception {
        super();
        setIdEntidade("ConfiguracaoSistemaCRM");
    }

    public ConfiguracaoDiasPosVenda(Connection con) throws Exception {
        super(con);
        setIdEntidade("ConfiguracaoSistemaCRM");
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe <code>ConfiguracaoDiasPosVendaVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ConfiguracaoDiasPosVendaVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ConfiguracaoDiasPosVendaVO</code>.
     *
     * @return O objeto da classe <code>ConfiguracaoDiasPosVendaVO</code> com os dados devidamente montados.
     */
    public static ConfiguracaoDiasPosVendaVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        ConfiguracaoDiasPosVendaVO obj = new ConfiguracaoDiasPosVendaVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.getConfiguracaoSistemaCRM().setCodigo(new Integer(dadosSQL.getInt("configuracaoSistemaCRM")));
        obj.setNrDia(new Integer(dadosSQL.getInt("nrDia")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setAtivo(dadosSQL.getBoolean("ativo"));
        obj.setSiglaResponsavelPeloContato(dadosSQL.getString("siglaresponsavelpelocontato"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>ConfiguracaoDiasPosVendaVO</code>.
     */
    public ConfiguracaoDiasPosVendaVO novo() throws Exception {
        incluirCRM(getIdEntidade());
        return new ConfiguracaoDiasPosVendaVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ConfiguracaoDiasPosVendaVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ConfiguracaoDiasPosVendaVO</code> que será gravado no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(ConfiguracaoDiasPosVendaVO obj) throws Exception {
        ConfiguracaoDiasPosVendaVO.validarDados(obj);
        incluirCRM(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ConfiguracaoDiasPosVenda( configuracaoSistemaCRM, nrDia, descricao, ativo, siglaresponsavelpelocontato ) VALUES ( ?, ?, ?, ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getConfiguracaoSistemaCRM().getCodigo() != 0) {
            sqlInserir.setInt(1, obj.getConfiguracaoSistemaCRM().getCodigo());
        } else {
            sqlInserir.setNull(1, 0);
        }
        sqlInserir.setInt(2, obj.getNrDia());
        sqlInserir.setString(3, obj.getDescricao());
        sqlInserir.setBoolean(4, obj.isAtivo());
        sqlInserir.setString(5, obj.getSiglaResponsavelPeloContato());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ConfiguracaoDiasPosVendaVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ConfiguracaoDiasPosVendaVO</code> que será alterada no banco de dados.
     * @throws Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ConfiguracaoDiasPosVendaVO obj) throws Exception {
        ConfiguracaoDiasPosVendaVO.validarDados(obj);
        alterarCRM(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE ConfiguracaoDiasPosVenda SET configuracaoSistemaCRM=?, nrDia=?, descricao=?, ativo=?, siglaresponsavelpelocontato=? WHERE codigo = ? RETURNING codigo";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getConfiguracaoSistemaCRM().getCodigo() != 0) {
            sqlAlterar.setInt(1, obj.getConfiguracaoSistemaCRM().getCodigo());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        sqlAlterar.setInt(2, obj.getNrDia());
        sqlAlterar.setString(3, obj.getDescricao());
        sqlAlterar.setBoolean(4, obj.isAtivo());
        sqlAlterar.setString(5, obj.getSiglaResponsavelPeloContato());
        sqlAlterar.setInt(6, obj.getCodigo());
        if (!sqlAlterar.executeQuery().next()) {
            incluir(obj);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ConfiguracaoDiasPosVendaVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ConfiguracaoDiasPosVendaVO</code> que será removido no banco de dados.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ConfiguracaoDiasPosVendaVO obj) throws Exception {
        excluirCRM(getIdEntidade());
        String sql = "DELETE FROM ConfiguracaoDiasPosVenda WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    /**
     * Responsável por realizar uma consulta de <code>ConfiguracaoDiasPosVenda</code> através do valor do atributo
     * <code>codigo</code> da classe <code>ConfiguracaoSistemaCRM</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @return List Contendo vários objetos da classe <code>ConfiguracaoDiasPosVendaVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoConfiguracaoSistemaCRM(Integer valorConsulta, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), true);
        String sqlStr = "SELECT ConfiguracaoDiasPosVenda.* FROM ConfiguracaoDiasPosVenda, ConfiguracaoSistemaCRM WHERE ConfiguracaoDiasPosVenda.configuracaoSistemaCRM = ConfiguracaoSistemaCRM.codigo and ConfiguracaoSistemaCRM.codigo >= " + valorConsulta + " ORDER BY ConfiguracaoSistemaCRM.codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    /**
     * Responsável por realizar uma consulta de <code>ConfiguracaoDiasPosVenda</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     *
     * @param controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return List Contendo vários objetos da classe <code>ConfiguracaoDiasPosVendaVO</code> resultantes da consulta.
     * @throws Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ConfiguracaoDiasPosVenda WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    /**
     * Operação responsável por excluir todos os objetos da <code>ConfiguracaoDiasPosVendaVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>ConfiguracaoDiasPosVenda</code>.
     *
     * @param configuracaoSistemaCRM campo chave para exclusão dos objetos no BD.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirConfiguracaoDiasPosVendas(Integer configuracaoSistemaCRM) throws Exception {
        excluirCRM(getIdEntidade());
        String sql = "DELETE FROM ConfiguracaoDiasPosVenda WHERE (configuracaoSistemaCRM = ?)";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, configuracaoSistemaCRM);
        sqlExcluir.execute();
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>ConfiguracaoDiasPosVendaVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirConfiguracaoDiasPosVendas</code> e <code>incluirConfiguracaoDiasPosVendas</code> disponíveis na classe <code>ConfiguracaoDiasPosVenda</code>.
     *
     * @param objetos List com os objetos a serem alterados ou incluídos no BD.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarConfiguracaoDiasPosVendas(Integer configuracaoSistemaCRM, List objetos) throws Exception {
        String str = "DELETE FROM ConfiguracaoDiasPosVenda WHERE configuracaoSistemaCRM = " + configuracaoSistemaCRM;
        Iterator i = objetos.iterator();
        while (i.hasNext()) {
            ConfiguracaoDiasPosVendaVO objeto = (ConfiguracaoDiasPosVendaVO) i.next();
            str += " AND codigo <> " + objeto.getCodigo();
        }
        PreparedStatement sqlExcluir = con.prepareStatement(str);
        sqlExcluir.execute();
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ConfiguracaoDiasPosVendaVO objeto = (ConfiguracaoDiasPosVendaVO) e.next();
            if (objeto.getCodigo().equals(0)) {
                incluir(objeto);
            } else {
                alterar(objeto);
            }
        }
    }

    /**
     * Operação responsável por incluir objetos da <code>ConfiguracaoDiasPosVendaVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>CRM.ConfiguracaoSistemaCRM</code> através do atributo de vínculo.
     *
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirConfiguracaoDiasPosVendas(Integer configuracaoSistemaCRMPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ConfiguracaoDiasPosVendaVO obj = (ConfiguracaoDiasPosVendaVO) e.next();
            obj.getConfiguracaoSistemaCRM().setCodigo(configuracaoSistemaCRMPrm);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>ConfiguracaoDiasPosVendaVO</code> relacionados a um objeto da classe <code>CRM.ConfiguracaoSistemaCRM</code>.
     *
     * @param configuracaoSistemaCRM Atributo de <code>CRM.ConfiguracaoSistemaCRM</code> a ser utilizado para localizar os objetos da classe <code>ConfiguracaoDiasPosVendaVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ConfiguracaoDiasPosVendaVO</code> resultantes da consulta.
     * @throws Exception Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarConfiguracaoDiasPosVendas(Integer configuracaoSistemaCRM, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        List objetos = new ArrayList();
        String sql = "SELECT * FROM ConfiguracaoDiasPosVenda WHERE configuracaoSistemaCRM = ? order by nrdia";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, configuracaoSistemaCRM);
        ResultSet resultado = sqlConsulta.executeQuery();
        while (resultado.next()) {
            ConfiguracaoDiasPosVendaVO novoObj = new ConfiguracaoDiasPosVendaVO();
            novoObj = ConfiguracaoDiasPosVenda.montarDados(resultado, nivelMontarDados);
            objetos.add(novoObj);
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ConfiguracaoDiasPosVendaVO</code>
     * através de sua chave primária.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ConfiguracaoDiasPosVendaVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM ConfiguracaoDiasPosVenda WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ConfiguracaoDiasPosVenda ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }
}
