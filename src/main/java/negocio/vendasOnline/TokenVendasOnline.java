package negocio.vendasOnline;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.TokenVendasOnlineInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 26/08/2022
 */
public class TokenVendasOnline extends SuperEntidade implements TokenVendasOnlineInterfaceFacade {

    public TokenVendasOnline() throws Exception {
    }

    public TokenVendasOnline(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void incluir(TokenVendasOnlineVO obj) throws Exception {
        TokenVendasOnlineVO.validarDados(obj);

        String sql = "INSERT INTO tokenVendasOnline(token, dataRegistro, dados)"
                + "VALUES (?,?,?)";

        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {

            sqlInserir.setString(1, obj.getToken());
            sqlInserir.setTimestamp(2, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
            sqlInserir.setString(3, obj.getDados());

            sqlInserir.execute();
        }
    }

    @Override
    public void alterar(TokenVendasOnlineVO obj) throws Exception {
        try {
            TokenVendasOnlineVO.validarDados(obj);
            String sql = "UPDATE tokenVendasOnline set token = ?, "
                    + "dataRegistro = ?, dados = ? "
                    + " WHERE codigo = ?";
            int i = 1;
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                sqlAlterar.setString(i++, obj.getToken());
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
                sqlAlterar.setString(i++, obj.getDados());
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataUtilizado()));
                sqlAlterar.setInt(i, obj.getCodigo());
                sqlAlterar.execute();
            }

        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public void excluir(TokenVendasOnlineVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "DELETE FROM tokenVendasOnline WHERE codigo = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo().intValue());
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public TokenVendasOnlineVO consultarPorToken(String token) throws Exception {
        String sql = "SELECT * FROM tokenVendasOnline WHERE token = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setString(1, token);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException(String.format("Dados Não Encontrados ( TokenVendasOnline %s)",
                            new Object[]{
                                    token
                            }));
                }
                return montarDados(tabelaResultado);
            }
        }
    }

    @Override
    public TokenVendasOnlineVO consultarPorTokenShort(String token) throws Exception {
        String sql = "SELECT * FROM tokenVendasOnline WHERE token = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setString(1, token);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return  null;
                }
                return montarDados(tabelaResultado);
            }
        }
    }
    @Override
    public void inutilizarToken(String token) throws Exception {
        try {
            String sql = "UPDATE tokenVendasOnline SET dataUtilizado = ? WHERE token = ?";
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                ps.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                ps.setString(2, token);
                ps.execute();
            }
        } catch (Exception ignored) {
        }
    }

    private TokenVendasOnlineVO montarDados(ResultSet rs) throws Exception {
        TokenVendasOnlineVO obj = new TokenVendasOnlineVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setToken(rs.getString("token"));
        obj.setDataRegistro(rs.getTimestamp("dataRegistro"));
        obj.setDados(rs.getString("dados"));
        obj.setDataUtilizado(rs.getDate("dataUtilizado"));
        return obj;
    }

    public boolean cobrancaEmProcessamento(String token) throws Exception {
        String sql = "SELECT emProcessamento FROM tokenVendasOnline WHERE token = ?";

        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setString(1, token);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return tabelaResultado.getBoolean("emProcessamento");
                }
                return false;
            }
        }
    }

    public void mudarCobrancaEmProcessamento(String token, boolean emProcessamento) throws Exception {
        try {
            String sql = "UPDATE tokenVendasOnline SET emProcessamento = ? WHERE token = ?";
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                ps.setBoolean(1, emProcessamento);
                ps.setString(2, token);
                ps.execute();
            }
        } catch (Exception ignored) {
        }
    }

}
