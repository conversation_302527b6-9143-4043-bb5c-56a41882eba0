package negocio.comuns.utilitarias;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.webservice.client.Exception;
import servicos.integracao.IntegracaoCadastrosWSConsumer;

import java.util.List;

public class TesteTokenApis {

    public static void main(String[] args) throws Exception {
        consumirIntegracaoWS();
    }

    private static void consumirIntegracaoWS() throws Exception {

        List<EmpresaVO> elements = IntegracaoCadastrosWSConsumer.getListaEmpresasRemotas("http://localhost:8082/zw", "elements");
        for(EmpresaVO empresaVO : elements){
            System.out.println(empresaVO.getNome());
        }
    }

    private static void consumirIntegracaoTurmas() throws Exception {

        List<EmpresaVO> elements = IntegracaoCadastrosWSConsumer.getListaEmpresasRemotas("http://localhost:8082/zw", "elements");
        for(EmpresaVO empresaVO : elements){
            System.out.println(empresaVO.getNome());
        }
    }

}
