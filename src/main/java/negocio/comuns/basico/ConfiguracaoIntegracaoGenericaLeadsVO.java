package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.enumerador.AcaoObjcaoLeadEnum;

public class ConfiguracaoIntegracaoGenericaLeadsVO extends SuperVO{

    private int empresa = 0;
    private boolean habilitada = false;
    private String horaLimite = "23:59";
    private UsuarioVO responsavelPadrao = new UsuarioVO();
    private int acaoObjecao = AcaoObjcaoLeadEnum.NENHUMA.getCodigo();

    public int getEmpresa() {
        return empresa;
    }

    public void setEmpresa(int empresa) {
        this.empresa = empresa;
    }

    public boolean isHabilitada() {
        return this.habilitada;
    }

    public void setHabilitada(final boolean habilitada) {
        this.habilitada = habilitada;
    }

    public String getHoraLimite() {
        return this.horaLimite;
    }

    public void setHoraLimite(final String horaLimite) {
        this.horaLimite = horaLimite;
    }

    public UsuarioVO getResponsavelPadrao() {
        return this.responsavelPadrao;
    }

    public void setResponsavelPadrao(final UsuarioVO responsavelPadrao) {
        this.responsavelPadrao = responsavelPadrao;
    }

    public int getAcaoObjecao() {
        return this.acaoObjecao;
    }

    public void setAcaoObjecao(final int acaoObjecao) {
        this.acaoObjecao = acaoObjecao;
    }

    public boolean isAcaoObjecaoLeadQualquer() {
        return getAcaoObjecao() == AcaoObjcaoLeadEnum.ATUALIZAR_QUALQUER.getCodigo();
    }

    public boolean isAcaoObjecaoLeadApenasDefinitiva() {
        return getAcaoObjecao() == AcaoObjcaoLeadEnum.APENAS_DEFINTIVA.getCodigo();
    }
}
