package negocio.comuns.basico;

import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

public class FamiliarTO {

    private Integer codigo;
    private Integer codigoPessoa;
    private Integer codigoCliente;
    private Integer codigoMatricula;
    private String nome;
    private String parentesco;
    private String situacao;
    private String descricaoPlano;
    private String situacaoContrato;
    private String fotoKey;
    private Date vencimento;
    private Boolean selecionado = Boolean.FALSE;

    public FamiliarTO() {
    }



    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoMatricula() {
        return codigoMatricula;
    }

    public void setCodigoMatricula(Integer codigoMatricula) {
        this.codigoMatricula = codigoMatricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getParentesco() {
        return parentesco;
    }

    public void setParentesco(String parentesco) {
        this.parentesco = parentesco;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public Date getVencimento() {
        return vencimento;
    }

    public String getVencimentoApresentar() {
        return vencimento == null ? "" : Uteis.getData(vencimento);
    }

    public void setVencimento(Date vencimento) {
        this.vencimento = vencimento;
    }

    public String getPrimeiroNome(){
        return Uteis.getPrimeiroNome(nome).toLowerCase();
    }

    public String getUrlFoto() {
        try {
            return Uteis.getPaintFotoDaNuvem(getFotoKey());
        } catch (Exception e) {
            return "";
        }
    }

    public Boolean getAtivo() {
        return situacao.equals("AT");
    }

    public Boolean getAtivoNormal() {
        return situacao.equals("AT") && situacaoContrato.equals("NO");
    }

    public Boolean getAtivoAvencer() {
        return situacao.equals("AT") && situacaoContrato.equals("AV");
    }

    public Boolean getAtivoAtestado() {
        return situacao.equals("AT") && situacaoContrato.equals("AE");
    }

    public Boolean getAtivoCarencia() {
        return situacao.equals("AT") && situacaoContrato.equals("CR");
    }

    public Boolean getTrancado() {
        return situacao.equals("TR");
    }

    public Boolean getTrancadoVencido() {
        return situacao.equals("TR") && situacaoContrato.equals("TV");
    }

    public Boolean getInativo() {
        return situacao.equals("IN");
    }

    public Boolean getInativoCancelamento() {
        return situacao.equals("IN") && situacaoContrato.equals("CA");
    }

    public Boolean getInativoDesistente() {
        return situacao.equals("IN") && situacaoContrato.equals("DE");
    }

    public Boolean getInativoVencido() {
        return situacao.equals("IN") && situacaoContrato.equals("VE");
    }

    public Boolean getVisitante() {
        return situacao.equals("VI");
    }

    public Boolean getVisitanteFreePass() {
        return situacao.equals("VI") && situacaoContrato.equals("PL");
    }

    public Boolean getVisitanteDiaria() {
        return situacao.equals("VI") && situacaoContrato.equals("DI");
    }

    public Boolean getVisitanteAulaAvulsa() {
        return situacao.equals("VI") && situacaoContrato.equals("AA");
    }

    public Boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(Boolean selecionado) {
        this.selecionado = selecionado;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public String getDescricaoPlano() {
        return descricaoPlano;
    }

    public void setDescricaoPlano(String descricaoPlano) {
        this.descricaoPlano = descricaoPlano;
    }
}
