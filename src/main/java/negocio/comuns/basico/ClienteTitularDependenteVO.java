package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.Lista;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.TipoCategoriaClubeEnum;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * date : 28/01/2015 11:58:21
 * autor: Ulisses
 */
public class ClienteTitularDependenteVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    @ChaveEstrangeira
    private ClienteVO clienteVO;
    @ChaveEstrangeira
    private ClienteTitularDependenteVO clienteTitular;
    @ChaveEstrangeira
    private ParentescoVO parentescoVO;
    private Date dataLancamento;
    private Boolean pagaSeparado;

    public ClienteTitularDependenteVO() {

    }

    public ClienteTitularDependenteVO(Integer codigo) {
        this.codigo = codigo;
    }


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteTitularDependenteVO getClienteTitular() {
        return clienteTitular;
    }

    public void setClienteTitular(ClienteTitularDependenteVO clienteTitular) {
        this.clienteTitular = clienteTitular;
    }

    public ParentescoVO getParentescoVO() {
        return parentescoVO;
    }

    public void setParentescoVO(ParentescoVO parentescoVO) {
        this.parentescoVO = parentescoVO;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public void validarDados() throws Exception {
        if (getClienteVO() == null) {
            throw new Exception("O campo CLIENTE (ClienteTitularDependente) deve ser informado.");
        }
        if ((getParentescoVO() == null) || (getParentescoVO().getCodigo() == null) || (getParentescoVO().getCodigo() <= 0)) {
            throw new Exception("O campo PARENTESCO (ClienteTitularDependente) deve ser informado.");
        }
        if ((this.clienteVO.getCategoria().getTipoCategoriaClube().equals(TipoCategoriaClubeEnum.DEPENDENTE.getCodigo())) ||
                (this.clienteVO.getCategoria().getTipoCategoriaClube().equals(TipoCategoriaClubeEnum.ASSEMELHADO.getCodigo()))) {
            if ((getClienteTitular() == null) || (getClienteTitular().getCodigo() == null) || (getClienteTitular().getCodigo() <= 0)) {
                throw new Exception("O campo CLIENTETITULAR (ClienteTitularDependente) deve ser informado.");
            }
        }
    }

	public Boolean getPagaSeparado() {
        if (pagaSeparado == null) {
            pagaSeparado = false;
        }
		return pagaSeparado;
	}

	public void setPagaSeparado(Boolean pagaSeparado) {
		this.pagaSeparado = pagaSeparado;
	}
	
	public String getNomeOperacaoPagaSeparado(){
		return ((this.pagaSeparado != null) && (this.pagaSeparado)) ? "SIM" : "NÃO";
	}
    
	public String getMsgConfirmacaoPagaSeparado(){
		return ((this.pagaSeparado != null) && (this.pagaSeparado)) ? "Deseja alterar para que o titular pague os débitos do " + 
	                                                                  this.clienteVO.getCategoria().getTipoCategoriaClubeEnum().getDescricao() + " " +    
	                                                                  this.clienteVO.getPessoa().getNome() + " ? " :
                                                                	  "Deseja alterar para que o " + this.clienteVO.getCategoria().getTipoCategoriaClubeEnum().getDescricao() +  " " +  
	                                                                  this.clienteVO.getPessoa().getNome() + " seja responsável por pagar seus próprios débitos ? ";	                                                                	  
	                                             
	}
    
}
