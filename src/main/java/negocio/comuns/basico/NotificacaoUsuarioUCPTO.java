package negocio.comuns.basico;


import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.enumerador.TipoNotificacaoUsuarioEnum;
import negocio.comuns.utilitarias.NotificacaoUsuarioVO;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 24/01/2017.
 */
public class NotificacaoUsuarioUCPTO extends SuperTO {

    private TipoNotificacaoUsuarioEnum tipoNotificacaoUCPEnum;
    private List<NotificacaoUsuarioVO> listaNotificacoes;

    public TipoNotificacaoUsuarioEnum getTipoNotificacaoUCPEnum() {
        return tipoNotificacaoUCPEnum;
    }

    public void setTipoNotificacaoUCPEnum(TipoNotificacaoUsuarioEnum tipoNotificacaoUCPEnum) {
        this.tipoNotificacaoUCPEnum = tipoNotificacaoUCPEnum;
    }

    public List<NotificacaoUsuarioVO> getListaNotificacoes() {
        if (listaNotificacoes == null) {
            listaNotificacoes = new ArrayList<NotificacaoUsuarioVO>();
        }
        return listaNotificacoes;
    }

    public void setListaNotificacoes(List<NotificacaoUsuarioVO> listaNotificacoes) {
        this.listaNotificacoes = listaNotificacoes;
    }

    public Integer getOrdenacao() {
        return tipoNotificacaoUCPEnum.getOrdem();
    }

    public Integer getTotalNotificacao() {
        return getListaNotificacoes().size();
    }

    public String getLinkAgrupado() {
        try {
            String request = (String) JSFUtilities.getManagedBean("SuperControle.contextPath");

            if (getTotalNotificacao() > 3) {
                return request + "/redir?up&visualizacaoUCP=" + tipoNotificacaoUCPEnum.getLinkAgrupado();
            } else {
                return request + "/redir?up";
            }
        } catch (Exception ex) {
            return "";
        }
    }
}
