package negocio.comuns.basico.fabricas;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.basico.enumerador.TipoBIDCC;
import negocio.facade.jdbc.arquitetura.FacadeFactory;

import java.sql.ResultSet;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by Rafael on 22/06/2016.
 */

public abstract class BiDCCRelAbstractFactory {
    protected FacadeFactory facadeFactory;
    protected int codigoEmpresa;
    protected ConfPaginacao confPaginacao;
    protected List<Integer> colaboradores;
    protected List<Integer> convenios;
    protected Date dataBase;
    protected static Map<TipoBIDCC, Class<? extends BiDCCRelAbstractFactory>> instancias = new ConcurrentHashMap<TipoBIDCC, Class<? extends BiDCCRelAbstractFactory>>();

    static {
        instancias.put(TipoBIDCC.ContratosAtivosRecorrencia, ContratosAtivosRecorrencia.class);
        instancias.put(TipoBIDCC.ContratosCanceladosRecorrencia, ContratosCanceladosRecorrencia.class);
        instancias.put(TipoBIDCC.ContratosNaoRenovadosRecorrencia, ContratosNaoRenovadosRecorrencia.class);
        instancias.put(TipoBIDCC.ContratosSemAutorizacaoCobranca, ContratosSemAutorizacaoCobranca.class);

        instancias.put(TipoBIDCC.CartoesCreditoVencidos, CartoesCreditoVencidos.class);
        instancias.put(TipoBIDCC.CartoesCreditoAVencer, CartoesCreditoAVencer.class);
        instancias.put(TipoBIDCC.ClientesMesmoCartao, ClientesMesmoCartao.class);

        instancias.put(TipoBIDCC.ParcelasCanceladas, ParcelasCanceladas.class);
        instancias.put(TipoBIDCC.ParcelasEmAberto, ParcelasEmAberto.class);
        instancias.put(TipoBIDCC.ParcelasVencidasEmAberto, ParcelasVencidasEmAberto.class);
        instancias.put(TipoBIDCC.OperacoesSuspeitas, OperacoesSuspeitas.class);
        instancias.put(TipoBIDCC.AlunosAdimplentes, AlunosAdimplentes.class);
        instancias.put(TipoBIDCC.CartoesComProblema, CartoesComProblema.class);
    }

    private static Map<TipoBIDCC, BiDCCRelAbstractFactory> instanciar() throws  Exception{
        Map<TipoBIDCC, BiDCCRelAbstractFactory> inst = new HashMap<TipoBIDCC, BiDCCRelAbstractFactory>(instancias.size());
        for(TipoBIDCC key : instancias.keySet()){
            inst.put(key, instancias.get(key).newInstance());
        }
        return inst;
    }

    /**
     * Realiza uma nova intancia da classe correspodente ao <code>tipo</code> passado como parâmetro.
     * @param tipo
     * @return
     * @throws Exception
     */
    private static BiDCCRelAbstractFactory getInstancia(TipoBIDCC tipo) throws  Exception{
        return instancias.containsKey(tipo) ? instancias.get(tipo).newInstance() : null;
    }

    /**
     * Retorna a instancia do {@link PendenciaRelAbstractFactory} que esta no contexto do usuario.
     * @param tipo
     * @return
     */
    private static BiDCCRelAbstractFactory getInstanciaContexto(TipoBIDCC tipo) throws  Exception{
        Map<TipoBIDCC, BiDCCRelAbstractFactory> map = (Map<TipoBIDCC, BiDCCRelAbstractFactory>) JSFUtilities.getFromSession(BiDCCRelAbstractFactory.class.getSimpleName() + "INSTANCIAS_BIDCCRELFACTORY");
        if(map == null){
            map = instanciar();
            JSFUtilities.storeOnSession(BiDCCRelAbstractFactory.class.getSimpleName() + "INSTANCIAS_BIDCCRELFACTORY", map);
        }
        return map.get(tipo);
    }

    public static BiDCCRelAbstractFactory getFactory(TipoBIDCC tipo) throws Exception {
        BiDCCRelAbstractFactory instancia = null;
        if(JSFUtilities.isJSFContext()){
            instancia = getInstanciaContexto(tipo);
        }else{
            instancia = getInstancia(tipo);
        }
        if(instancia == null){
            throw new Exception("Para criar um utilitário de BiDCCRel é necessário fornecer um tipo não nulo.");
        }
        return instancia;
    }

    public static BiDCCRelAbstractFactory getFactory(TipoBIDCC tipo,
                                                         FacadeFactory facadeFactory,
                                                         int codigoEmpresaFiltro,
                                                         ConfPaginacao confPaginacao, List<Integer> colaboradores,Date dataBase, List<Integer> convenios) throws Exception {
        BiDCCRelAbstractFactory fabrica = getFactory(tipo);
        fabrica.facadeFactory = facadeFactory;
        fabrica.codigoEmpresa = codigoEmpresaFiltro;
        fabrica.confPaginacao = confPaginacao;
        fabrica.colaboradores = colaboradores;
        fabrica.dataBase = dataBase;
        fabrica.convenios = convenios;
        return fabrica;
    }

    public String trataMensagem(String mensagem) {
        return mensagem;
    }

    public abstract Double getSum() throws Exception;
    public abstract List getList() throws Exception;

    public abstract Integer getCount() throws Exception;

    public abstract ResultSet getResult() throws Exception;

    protected Date getDataBaseFiltroBI() {
        return dataBase;
    }
    protected List<Integer> getConvenios(){
        if(this.convenios == null){
            this.convenios = new ArrayList<Integer>();
        }
        return this.convenios;
    }

    protected Date getDataBaseFiltroBIDCC() {
        return (Date) JSFUtilities.getManagedBean("RelContratosRecorrenciaControle.dataBaseFiltro");
    }

    boolean isSomenteParcelasForaMes() {
        return (Boolean) JSFUtilities.getManagedBean("RelContratosRecorrenciaControle.somenteParcelasForaMes");
    }

    boolean isSomenteParcelasMes() {
        return (Boolean) JSFUtilities.getManagedBean("RelContratosRecorrenciaControle.somenteParcelasMes");
    }

    protected Date getDataBaseInicialFiltroBI() {
        return (Date) JSFUtilities.getManagedBean("PendenciaControleRel.dataBaseInicialFiltro");
    }
}
