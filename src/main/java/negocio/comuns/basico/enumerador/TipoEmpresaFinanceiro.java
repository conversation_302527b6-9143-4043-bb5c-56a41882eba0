package negocio.comuns.basico.enumerador;

public enum TipoEmpresaFinanceiro {

    NAO_TIPIFICADO("Não tipificado", "NÃO AUTORIZADO"),
    PRODUCAO("Produção", ""),
    USO_INTERNO("Uso Interno/Demais favorecidos", "USO AUTORIZADO"),
    TESTE("Testes", "TESTES"),
    IMPLANTACAO("Em Implantação", "IMPLANTAÇÃO"),
    INATIVO("Inativo", "NÃO AUTORIZADO");

    private final String descricao;
    private final String mensagem;

    TipoEmpresaFinanceiro(String descricao, String mensagem) {
        this.descricao = descricao;
        this.mensagem = mensagem;
    }

    public String getDescricao() {
        return descricao;
    }

    public String getMensagem() {
        return mensagem;
    }
}
