/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico.enumerador;

import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import servicos.integracao.interfaces.parceirofidelidade.ParceiroFidelidadeAPI;

/**
 * <AUTHOR>
 */
public enum TipoParceiroEnum {

    NENHUM(0, "NENHUM", "", null, ""),
    DOTZ(1, "DOTZ", "DZ", ParceiroFidelidadeAPI.class, "logo-dotz.png");

    private Integer id;
    private String nome;
    private String simbolo;
    private Class classAPI;
    private String logomarca;

    private TipoParceiroEnum(final Integer id, final String nome, final String simbolo, Class classAPI, String logomarca) {
        this.id = id;
        this.nome = nome;
        this.simbolo = simbolo;
        this.classAPI = classAPI;
        this.logomarca = logomarca;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Class getClassAPI() {
        return classAPI;
    }

    public void setClassAPI(Class classAPI) {
        this.classAPI = classAPI;
    }

    public static TipoParceiroEnum valueOf(Integer id) {
        for (TipoParceiroEnum tipo : TipoParceiroEnum.values()) {
            if (tipo.getId().equals(id)) {
                return tipo;
            }
        }
        return NENHUM;
    }

    public String getSimbolo() {
        return simbolo;
    }

    public void setSimbolo(String simbolo) {
        this.simbolo = simbolo;
    }

    public static Integer getTipoPagamento(TipoParceiroEnum tipoParceiroEnum, String formaPagamento) {

        if (tipoParceiroEnum.equals(TipoParceiroEnum.DOTZ)) {

            if (formaPagamento.equals(TipoFormaPagto.AVISTA.getSigla())) {
                return 1;
            } else if (formaPagamento.equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
                return 2;
            } else if (formaPagamento.equals(TipoFormaPagto.CARTAODEBITO.getSigla())) {
                return 3;
            } else if (formaPagamento.equals(TipoFormaPagto.CHEQUE.getSigla())) {
                return 4;
            } else {
                return 6;
            }

        }
        return 0;
    }

    public String getLogomarca() {
        return logomarca;
    }

    public void setLogomarca(String logomarca) {
        this.logomarca = logomarca;
    }
}
