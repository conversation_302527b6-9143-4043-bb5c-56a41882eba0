package negocio.comuns.basico.enumerador;

/**
 * Created by <PERSON> on 06/05/2016.
 */
public enum BIZillyonWebEnum {
    PENDECIA,
    GRUPO_RISCO,
    INDICE_RENOVACAO,
    CONVERSAO_VENDAS,
    CONVERSAO_VENDAS_SS,
    ROTATIVIDADE_CONTRATO,
    METAS_FINANCEIRAS,
    DCC,
    TICKET_MEDIO;

    public static BIZillyonWebEnum getFromOrdinal(int o){
        for(BIZillyonWebEnum c : values()){
            if(c.ordinal() == o){
                return c;
            }
        }
        return null;
    }
}
