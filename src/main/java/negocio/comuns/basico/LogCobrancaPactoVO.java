package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.comuns.json.ItemCobrancaPactoJSON;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.comuns.util.Formatador;
import org.json.JSONArray;
import org.json.JSONException;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.FormaPagamento;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class LogCobrancaPactoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Date dataCobranca;
    private Integer quantidade;
    private Double valorTotal;
    private Integer qtdParcelas;
    private boolean gerarNota = false;
    private String nomeUsuarioOAMD;
    private Integer empresa;
    private String justificativa;
    private boolean gerarCobrancaFinanceiro;
    private String observacao;
    private String itensCobrancaPacto;
    private String jsonCobranca;
    private Integer tipoCobrancaPacto;
    private String tabelaCreditoPacto;
    private Double valorMedioUnitario;

    @Override
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataCobranca() {
        return dataCobranca;
    }

    public void setDataCobranca(Date dataCobranca) {
        this.dataCobranca = dataCobranca;
    }

    public Integer getQuantidade() {
        if (quantidade == null) {
            quantidade = 0;
        }
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Double getValorTotal() {
        if (valorTotal == null) {
            valorTotal = 0.0;
        }
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public Integer getQtdParcelas() {
        if (qtdParcelas == null) {
            qtdParcelas = 0;
        }
        return qtdParcelas;
    }

    public void setQtdParcelas(Integer qtdParcelas) {
        this.qtdParcelas = qtdParcelas;
    }

    public boolean isGerarNota() {
        return gerarNota;
    }

    public void setGerarNota(boolean gerarNota) {
        this.gerarNota = gerarNota;
    }

    public String getNomeUsuarioOAMD() {
        if (nomeUsuarioOAMD == null) {
            nomeUsuarioOAMD = "";
        }
        return nomeUsuarioOAMD;
    }

    public void setNomeUsuarioOAMD(String nomeUsuarioOAMD) {
        this.nomeUsuarioOAMD = nomeUsuarioOAMD;
    }

    public Integer getEmpresa() {
        if (empresa == null) {
            empresa = 0;
        }
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getJustificativa() {
        if (justificativa == null) {
            justificativa = "";
        }
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public boolean isGerarCobrancaFinanceiro() {
        return gerarCobrancaFinanceiro;
    }

    public void setGerarCobrancaFinanceiro(boolean gerarCobrancaFinanceiro) {
        this.gerarCobrancaFinanceiro = gerarCobrancaFinanceiro;
    }

    public String getObservacao_Apresentar() {
        return getObservacao().replace(" \n", "<br/>");
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getItensCobrancaPacto() {
        if (itensCobrancaPacto == null) {
            itensCobrancaPacto = "";
        }
        return itensCobrancaPacto;
    }

    public void setItensCobrancaPacto(String itensCobrancaPacto) {
        this.itensCobrancaPacto = itensCobrancaPacto;
    }

    public List<ItemCobrancaPactoJSON> getListaItens() {
        try {
            if (UteisValidacao.emptyString(getItensCobrancaPacto())) {
                return new ArrayList<ItemCobrancaPactoJSON>();
            } else {
                return JSONMapper.getList(new JSONArray(getItensCobrancaPacto()), ItemCobrancaPactoJSON.class);
            }
        } catch (Exception ex) {
            return new ArrayList<ItemCobrancaPactoJSON>();
        }
    }

    public String getDataCobranca_Apresentar() {
        return Uteis.getDataComHHMM(getDataCobranca());
    }

    public String getValorTotal_Apresentar() {
        return Formatador.formatarValorMonetario(getValorTotal());
    }

    public String getJsonCobranca() {
        if (jsonCobranca == null) {
            jsonCobranca = "";
        }
        return jsonCobranca;
    }

    public void setJsonCobranca(String jsonCobranca) {
        this.jsonCobranca = jsonCobranca;
    }

    public Integer getTipoCobrancaPacto() {
        return tipoCobrancaPacto;
    }

    public void setTipoCobrancaPacto(Integer tipoCobrancaPacto) {
        this.tipoCobrancaPacto = tipoCobrancaPacto;
    }

    public String getTabelaCreditoPacto() {
        if (tabelaCreditoPacto == null) {
            tabelaCreditoPacto = "";
        }
        return tabelaCreditoPacto;
    }

    public void setTabelaCreditoPacto(String tabelaCreditoPacto) {
        this.tabelaCreditoPacto = tabelaCreditoPacto;
    }

    public Double getValorMedioUnitario() {
        if (valorMedioUnitario == null) {
            valorMedioUnitario = 0.0;
        }
        return valorMedioUnitario;
    }

    public void setValorMedioUnitario(Double valorMedioUnitario) {
        this.valorMedioUnitario = valorMedioUnitario;
    }
}
