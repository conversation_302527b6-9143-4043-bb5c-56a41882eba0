package negocio.comuns.basico;

import java.util.Date;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import negocio.comuns.arquitetura.SuperVO;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

public class FornecedorVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String cnpj;
    protected String inscricaoEstadual;
    protected String inscricaoMunicipal;
    protected String contato;
    protected String descricao;
    protected String telefone;
    protected String endereco;
    protected String email;
    protected PessoaVO pessoa = new PessoaVO();
    protected boolean checado;
    private boolean isEmpresaInternacional;
    @Lista
    @NaoControlarLogAlteracao
    private List<EnderecoVO> enderecoVOs;
    @NaoControlarLogAlteracao
    private List<TelefoneVO> telefoneVOs;
    @NaoControlarLogAlteracao
    private List<EmailVO> emailVOs;
    private EmpresaVO empresaVO;
    protected String cfdf;
    private String pais;
    private String estado;
    private String cidade;
    private String observacao;
    private Integer planoConta; // atributo transient
    private Integer centroCusto;  // atributo transient
    private String nomeFantasia;
    private String razaoSocial;
    private Date dataCadastro;
    private Date dataValidade;
    private String cnae;
    private String descricaoCnae;
    private String codigoFpas;
    private Integer grauRiscoNr4;
    private Integer grauRiscoInss;
    private String sindicato;
    private Integer nrTotalFuncionarios;
    private Integer porteEmpresa;
    private String documento;
    private String documentoExtensao;

    /**
     * Método usado para validar dados de fornecedor em cadastro de pessoa simplificado
     * pela tela de lançamentos financeiros
     * @param obj
     * @throws ConsistirException
     */
    public static void validarDadosSimplificado(FornecedorVO obj) throws ConsistirException {
        if (obj.getEmpresaVO().getCodigo() == null) {
            throw new ConsistirException("O campo EMPRESA deve ser informado.");
        }
    }

    /**
     * Valida CNPJ do fornecedor 
     * @param obj
     * @param empresa
     * @throws ConsistirException
     * @throws Exception
     */
    public static void validarCNPJ(FornecedorVO obj, String empresa) throws ConsistirException, Exception {
        FornecedorVO validaCNPJ = new FornecedorVO();
        try {
            validaCNPJ = getFacade().getFornecedor().consultarPorCNPJExcetoCodigo(obj);
            if ((validaCNPJ != null) && (!obj.getCnpj().trim().equals(""))) {
                throw new ConsistirException("O CNPJ digitado " + validaCNPJ.getCnpj() + " já está cadastrado no sistema para o Fornecedor de Nome: " + validaCNPJ.getPessoa().getNome() + "");
            } else if (!SuperVO.validaCNPJ(obj.getCnpj())) {
                throw new ConsistirException("O CNPJ não é VÁLIDO.");
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public List<EnderecoVO> getEnderecoVOs() {
        if (enderecoVOs == null) {
            enderecoVOs = new ArrayList<EnderecoVO>();
        }
        return enderecoVOs;
    }

    public List<TelefoneVO> getTelefoneVOs() {
        if (telefoneVOs == null) {
            telefoneVOs = new ArrayList<TelefoneVO>();
        }
        return telefoneVOs;
    }

    public List<EmailVO> getEmailVOs() {
        if (emailVOs == null) {
            emailVOs = new ArrayList<EmailVO>();
        }
        return emailVOs;
    }
    public String getNome_Apresentar(){
        return getPessoa().getNome();
    }

    public void setEnderecoVOs(List<EnderecoVO> enderecoVOs) {
        this.enderecoVOs = enderecoVOs;
    }

    public void setTelefoneVOs(List<TelefoneVO> telefoneVOs) {
        this.telefoneVOs = telefoneVOs;
    }

    public void setEmailVOs(List<EmailVO> emailVOs) {
        this.emailVOs = emailVOs;
    }

    public void excluirObjEnderecoVOs(EnderecoVO obj) throws Exception {
        int index = 0;
        Iterator<EnderecoVO> i = getEnderecoVOs().iterator();
        while (i.hasNext()) {
            EnderecoVO objExistente = i.next();
            if (objExistente.getEndereco().equals(obj.getEndereco())
                    && (objExistente.getNumero().equals(obj.getNumero()))
                    && objExistente.getBairro().equals(obj.getBairro())
                    && objExistente.getComplemento().equals(
                    obj.getComplemento())) {
                getEnderecoVOs().remove(index);
                return;
            }
            index++;
        }
    }

    public void excluirObjEmailVOs(EmailVO obj) throws Exception {
        int index = 0;
        Iterator<EmailVO> i = getEmailVOs().iterator();
        while (i.hasNext()) {
            EmailVO objExistente = i.next();
            if (objExistente.getEmail().equals(obj.getEmail())) {
                getEmailVOs().remove(index);
                return;
            }
            index++;
        }
    }

    public void excluirObjTelefoneVOs(TelefoneVO obj) throws Exception {
        int index = 0;
        Iterator<TelefoneVO> i = getTelefoneVOs().iterator();
        while (i.hasNext()) {
            TelefoneVO objExistente = i.next();
            if (objExistente.getNumero().equals(obj.getNumero())) {
                getTelefoneVOs().remove(index);
                return;
            }
            index++;
        }
    }

    public void adicionarObjEnderecoVOs(EnderecoVO obj) throws Exception {
        EnderecoVO.validarDados(obj);
        int index = 0;
        Iterator<EnderecoVO> i = getEnderecoVOs().iterator();
        while (i.hasNext()) {
            EnderecoVO objExistente = i.next();
            if (objExistente.getEndereco().equals(obj.getEndereco())
                    && (objExistente.getNumero().equals(obj.getNumero()))
                    && objExistente.getBairro().equals(obj.getBairro())
                    && objExistente.getComplemento().equals(obj.getComplemento())) {
                getEnderecoVOs().set(index, obj);
                return;
            }
            index++;
        }
        getEnderecoVOs().add(obj);
    }

    public void adicionarObjTelefoneVOs(TelefoneVO obj) throws Exception {
        obj.validarDados(obj.isUsarSistemaInternacional());
        int index = 0;
        Iterator<TelefoneVO> i = getTelefoneVOs().iterator();
        while (i.hasNext()) {
            TelefoneVO objExistente = i.next();
            if (objExistente.getNumero().equals(obj.getNumero())) {
                getTelefoneVOs().set(index, obj);
                return;
            }
            index++;
        }
        getTelefoneVOs().add(obj);
    }

    public void adicionarObjEmailVOs(EmailVO obj) throws Exception {
        EmailVO.validarDados(obj);
        int index = 0;
        Iterator<EmailVO> i = getEmailVOs().iterator();
        while (i.hasNext()) {
            EmailVO objExistente = i.next();
            if (objExistente.getEmail().equals(obj.getEmail())) {
                getEmailVOs().set(index, obj);
                return;
            }
            index++;
        }
        getEmailVOs().add(obj);
    }
    public String getNomeApresentar(){
        return getPessoa().getNome();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        if (codigo == null) {
            this.codigo = 0;
        }
        this.codigo = codigo;
    }

    public String getCnpj() {
        if (cnpj == null) {
            cnpj = "";
        }
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getContato() {
        if (contato != null) {
            return contato;
        }
        return "";
    }

    public void setContato(String contato) {
        this.contato = contato;
    }

    public boolean isChecado() {
        return checado;
    }

    public void setChecado(boolean checado) {
        this.checado = checado;
    }

    public PessoaVO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaVO pessoa) {
        this.pessoa = pessoa;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public void validarDados(FornecedorVO obj) throws Exception{
        if(UteisValidacao.emptyString(obj.getDescricao())){
            throw new ConsistirException("O campo Nome deve ser informado!");
        }
        if(getFacade().getFornecedor().existeFornecedorNome(obj)){
            throw new ConsistirException("Já existe um cadastro de fornecedor com este nome.");
        }
        if(getFacade().getFinanceiro().getConfiguracaoFinanceiro().consultar().isCnpjObrigatorioFornecedor() &&
                !UteisValidacao.emptyString(obj.getCnpj()) && !Uteis.isNumeroValido(Uteis.formatarCpfCnpj(obj.getCnpj(), true))){
            throw new ConsistirException("O campo CPF/CNPJ deve conter apenas numeros!");
        }
        if(getFacade().getFinanceiro().getConfiguracaoFinanceiro().consultar().isCnpjObrigatorioFornecedor() &&
                !UteisValidacao.emptyString(obj.getCnpj()) && !(Uteis.formatarCpfCnpj(obj.getCnpj(), true).length() == 11
                || (Uteis.formatarCpfCnpj(obj.getCnpj(), true).length() == 14)))
        {
            throw new ConsistirException("Quantidade de digitos para o campo CPF/CNPJ deve ser 11 ou 14.");
        }
        if(getFacade().getFinanceiro().getConfiguracaoFinanceiro().consultar().isCnpjObrigatorioFornecedor() &&
                UteisValidacao.emptyString(obj.getCnpj())){
            throw new ConsistirException("O campo CNPJ deve ser informado!");
        }
        if(getFacade().getFinanceiro().getConfiguracaoFinanceiro().consultar().isCnpjObrigatorioFornecedor() &&
                getFacade().getFornecedor().existeFornecedorCNPJ(obj)){
            throw new ConsistirException("Já existe um cadastro de fornecedor com este cnpj.");
        }
        if(obj.getEmpresaVO() == null || UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())){
            throw new ConsistirException("O campo Empresa deve ser informado!");
        }
        if(UteisValidacao.emptyString(obj.getContato())){
            throw new ConsistirException("O campo Contato deve ser informado!");
        }
    }

    public String getInscricaoEstadual() {
        if (inscricaoEstadual == null) {
            inscricaoEstadual = "";
        }
        return inscricaoEstadual;
    }

    public void setInscricaoEstadual(String inscricaoEstadual) {
        this.inscricaoEstadual = inscricaoEstadual;
    }

    public String getInscricaoMunicipal() {
        if (inscricaoMunicipal == null) {
            inscricaoMunicipal = "";
        }
        return inscricaoMunicipal;
    }

    public void setInscricaoMunicipal(String inscricaoMunicipal) {
        this.inscricaoMunicipal = inscricaoMunicipal;
    }

    public String getCfdf() {
        if (cfdf == null) {
            cfdf = "";
        }
        return cfdf;
    }

    public void setCfdf(String cfdf) {
        this.cfdf = cfdf;
    }

    public boolean isEmpresaInternacional() {
        return isEmpresaInternacional;
    }

    public void setEmpresaInternacional(boolean empresaInternacional) {
        isEmpresaInternacional = empresaInternacional;
    }

    public String getPais() {
        return pais;
    }

    public void setPais(String pais) {
        this.pais = pais;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Integer getPlanoConta() {
        return planoConta;
    }

    public void setPlanoConta(Integer planoConta) {
        this.planoConta = planoConta;
    }

    public Integer getCentroCusto() {
        return centroCusto;
    }

    public void setCentroCusto(Integer centroCusto) {
        this.centroCusto = centroCusto;
    }

    public String getNomeFantasia() {
        return nomeFantasia;
    }

    public void setNomeFantasia(String nomeFantasia) {
        this.nomeFantasia = nomeFantasia;
    }

    public String getRazaoSocial() {
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Date getDataValidade() {
        return dataValidade;
    }

    public void setDataValidade(Date dataValidade) {
        this.dataValidade = dataValidade;
    }

    public String getCnae() {
        return cnae;
    }

    public void setCnae(String cnae) {
        this.cnae = cnae;
    }

    public String getCodigoFpas() {
        return codigoFpas;
    }

    public void setCodigoFpas(String codigoFpas) {
        this.codigoFpas = codigoFpas;
    }

    public Integer getGrauRiscoNr4() {
        return grauRiscoNr4;
    }

    public void setGrauRiscoNr4(Integer grauRiscoNr4) {
        this.grauRiscoNr4 = grauRiscoNr4;
    }

    public Integer getGrauRiscoInss() {
        return grauRiscoInss;
    }

    public void setGrauRiscoInss(Integer grauRiscoInss) {
        this.grauRiscoInss = grauRiscoInss;
    }

    public String getSindicato() {
        return sindicato;
    }

    public void setSindicato(String sindicato) {
        this.sindicato = sindicato;
    }

    public Integer getNrTotalFuncionarios() {
        return nrTotalFuncionarios;
    }

    public void setNrTotalFuncionarios(Integer nrTotalFuncionarios) {
        this.nrTotalFuncionarios = nrTotalFuncionarios;
    }

    public Integer getPorteEmpresa() {
        return porteEmpresa;
    }

    public void setPorteEmpresa(Integer porteEmpresa) {
        this.porteEmpresa = porteEmpresa;
    }

    public String getDocumento() {
        return documento;
    }

    public void setDocumento(String documento) {
        this.documento = documento;
    }

    public String getDocumentoExtensao() {
        return documentoExtensao;
    }

    public void setDocumentoExtensao(String documentoExtensao) {
        this.documentoExtensao = documentoExtensao;
    }

    public String getDescricaoCnae() {
        return descricaoCnae;
    }

    public void setDescricaoCnae(String descricaoCnae) {
        this.descricaoCnae = descricaoCnae;
    }
}
