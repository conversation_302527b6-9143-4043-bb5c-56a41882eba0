package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.Lista;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade Pais. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class PaisVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo = 0;
    protected String nome = "";
    /**
     * Atributo responsável por manter os objetos da classe <code>Estado</code>.
     */
    @Lista
    @ListJson(clazz = EstadoVO.class)
    private List estadoVOs = new ArrayList();
    private String nacionalidade = "";

    /**
     * Construtor padrão da classe <code>Pais</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public PaisVO() {
        super();
    }
    
    /**
     * Operação responsável por validar os dados de um objeto da classe <code>PaisVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(PaisVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getNome().equals("")) {
            throw new ConsistirException("O campo NOME (País) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        setNome(getNome().toUpperCase());
    }


    /**
     * Operação responsável por adicionar um novo objeto da classe <code>EstadoVO</code>
     * ao List <code>estadoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>Estado</code> - getSigla() - como identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>EstadoVO</code> que será adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjEstadoVOs(EstadoVO obj) throws Exception {
        EstadoVO.validarDados(obj);
        int index = 0;
        Iterator i = getEstadoVOs().iterator();
        while (i.hasNext()) {
            EstadoVO objExistente = (EstadoVO) i.next();
            if (objExistente.getSigla().equals(obj.getSigla())) {
                getEstadoVOs().set(index, obj);
                return;
            }
            index++;
        }
        getEstadoVOs().add(obj);
    }

    /**
     * Operação responsável por excluir um objeto da classe <code>EstadoVO</code>
     * no List <code>estadoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>Estado</code> - getSigla() - como identificador (key) do objeto no List.
     *
     * @param sigla Parâmetro para localizar e remover o objeto do List.
     */
    public void excluirObjEstadoVOs(String sigla) throws Exception {
        int index = 0;
        Iterator i = getEstadoVOs().iterator();
        while (i.hasNext()) {
            EstadoVO objExistente = (EstadoVO) i.next();
            if (objExistente.getSigla().equals(sigla)) {
                getEstadoVOs().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe <code>EstadoVO</code>
     * no List <code>estadoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>Estado</code> - getSigla() - como identificador (key) do objeto no List.
     *
     * @param sigla Parâmetro para localizar o objeto do List.
     */
    public EstadoVO consultarObjEstadoVO(String sigla) throws Exception {
        Iterator i = getEstadoVOs().iterator();
        while (i.hasNext()) {
            EstadoVO objExistente = (EstadoVO) i.next();
            if (objExistente.getSigla().equals(sigla)) {
                return objExistente;
            }
        }
        return null;
    }

    /**
     * Retorna Atributo responsável por manter os objetos da classe <code>Estado</code>.
     */
    public List getEstadoVOs() {
        if (estadoVOs == null) {
            estadoVOs = new ArrayList();
        }
        return (estadoVOs);
    }

    /**
     * Define Atributo responsável por manter os objetos da classe <code>Estado</code>.
     */
    public void setEstadoVOs(List estadoVOs) {
        this.estadoVOs = estadoVOs;
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return (nome);
    }

    public String getSiglaNome() {
        if (nome == null) {
            nome = "BRA";
        }else {
            if (nome.equals("BRASIL")){
                return "BRA";
            }else if (nome.equals("URUGUAI")){
                return "URY";
            }else if (nome.equals("ARGENTINA")){
                return "ARG";
            }else if (nome.equals("PERU")){
                return "PER";
            }else if (nome.equals("VENEZUELA")){
                return "VEN";
            }else if (nome.equals("BOLÍVIA")){
                return "BOL";
            }
        }
        return (nome);
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNacionalidade() {
        return nacionalidade;
    }

    public void setNacionalidade(String nacionalidade) {
        this.nacionalidade = nacionalidade;
    }
}
