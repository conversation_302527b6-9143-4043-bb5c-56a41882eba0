package negocio.comuns.basico;
import annotations.arquitetura.ListJson;
import negocio.comuns.basico.RespostaPerguntaVO;
import java.util.Iterator;
import java.util.List;
import java.util.ArrayList;
import java.util.Objects;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.Lista;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade Pergunta. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
*/
public class PerguntaVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected String descricao;
    protected String tipoPergunta;
    protected Boolean adicionarPergunta;    
    /** Atributo responsável por manter os objetos da classe <code>RespostaPergunta</code>. */
    @NaoControlarLogAlteracao
    @ListJson(clazz = RespostaPerguntaVO.class)
    private List<RespostaPerguntaVO> respostaPerguntaVOs;
    @NaoControlarLogAlteracao
    private List<RespostaPerguntaVO> respostaPerguntaVOsAntesAlteracao;
    private boolean nps = false;
	
    /**
     * Construtor padrão da classe <code>Pergunta</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
    */
    public PerguntaVO() {
        super();
        inicializarDados();
    }
     
	
    /**
     * Operação responsável por validar os dados de um objeto da classe <code>PerguntaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
    */
    public static void validarDados(PerguntaVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
            }
        if (obj.getDescricao().equals("")) { 
            throw new ConsistirException("O campo DESCRIÇÃO (Pergunta) deve ser informado.");
        }
        if (UteisValidacao.emptyString(obj.getTipoPergunta())) { 
            throw new ConsistirException("O campo TIPO PERGUNTA (Pergunta) deve ser informado.");
        }
    }
     
    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
    */
    public void realizarUpperCaseDados() {
        setDescricao( getDescricao().toUpperCase() );
        setTipoPergunta( getTipoPergunta().toUpperCase() );
    }
     
    /**
     * Operação reponsável por inicializar os atributos da classe.
    */
    public void inicializarDados() {
        setCodigo( new Integer(0) );
        setDescricao( "" );
        setTipoPergunta( "" );
        setAdicionarPergunta( new Boolean(false) );      
        setRespostaPerguntaVOs( new ArrayList() );
        setRespostaPerguntaVOsAntesAlteracao(new ArrayList() );
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe <code>RespostaPerguntaVO</code>
     * ao List <code>respostaPerguntaVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>RespostaPergunta</code> - getDescricaoRespota() - como identificador (key) do objeto no List.
     * @param obj    Objeto da classe <code>RespostaPerguntaVO</code> que será adiocionado ao Hashtable correspondente.
    */
    public void adicionarObjRespostaPerguntaVOs(RespostaPerguntaVO obj) throws Exception {
        RespostaPerguntaVO.validarDados(obj);
        int index = 0;
        Iterator i = getRespostaPerguntaVOs().iterator();
        while (i.hasNext()) {
            RespostaPerguntaVO objExistente = (RespostaPerguntaVO)i.next();
            if (objExistente.getDescricaoRespota().equals(obj.getDescricaoRespota())) {
                getRespostaPerguntaVOs().set( index , obj );
                return;
            }
            index++;
        }
        getRespostaPerguntaVOs().add( obj );
    }

    /**
     * Operação responsável por excluir um objeto da classe <code>RespostaPerguntaVO</code>
     * no List <code>respostaPerguntaVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>RespostaPergunta</code> - getDescricaoRespota() - como identificador (key) do objeto no List.
     * @param descricaoRespota  Parâmetro para localizar e remover o objeto do List.
    */
    public List<RespostaPerguntaVO> excluirObjRespostaPerguntaVOs(String descricaoRespota, List<RespostaPerguntaVO> listaObjetosExcluidos) throws Exception {
        int index = 0;
        Iterator i = getRespostaPerguntaVOs().iterator();
        while (i.hasNext()) {
            RespostaPerguntaVO objExistente = (RespostaPerguntaVO)i.next();
            if (objExistente.getDescricaoRespota().equals(descricaoRespota)) {
                listaObjetosExcluidos.add(objExistente);
                getRespostaPerguntaVOs().remove( index );
                return listaObjetosExcluidos;
            }
            index++;
        }
        return listaObjetosExcluidos;
    }

    /**
     * Operação responsável por consultar um objeto da classe <code>RespostaPerguntaVO</code>
     * no List <code>respostaPerguntaVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>RespostaPergunta</code> - getDescricaoRespota() - como identificador (key) do objeto no List.
     * @param descricaoRespota  Parâmetro para localizar o objeto do List.
    */
    public RespostaPerguntaVO consultarObjRespostaPerguntaVO(String descricaoRespota) throws Exception {
        Iterator i = getRespostaPerguntaVOs().iterator();
        while (i.hasNext()) {
            RespostaPerguntaVO objExistente = (RespostaPerguntaVO)i.next();
            if (objExistente.getDescricaoRespota().equals(descricaoRespota)) {
                return objExistente;
            }
        }
        return null;
    }
	

    /** Retorna Atributo responsável por manter os objetos da classe <code>RespostaPergunta</code>. */
    public List<RespostaPerguntaVO> getRespostaPerguntaVOs() {
        return (respostaPerguntaVOs);
    }
     
    /** Define Atributo responsável por manter os objetos da classe <code>RespostaPergunta</code>. */
    public void setRespostaPerguntaVOs( List<RespostaPerguntaVO> respostaPerguntaVOs ) {
        this.respostaPerguntaVOs = respostaPerguntaVOs;
    }

    public String getTipoPergunta() {
        if (tipoPergunta== null) {
            tipoPergunta = "";
        }
        return (tipoPergunta);
    }
     
    /**
     * Operação responsável por retornar o valor de apresentação de um atributo com um domínio específico. 
     * Com base no valor de armazenamento do atributo esta função é capaz de retornar o 
     * de apresentação correspondente. Útil para campos como sexo, escolaridade, etc. 
    */
    public String getTipoPergunta_Apresentar() {
        if (tipoPergunta== null) {
            tipoPergunta = "";
        }
        if (tipoPergunta.equals("ME")) {
            return "Multipla Escolha";
        }
        if (tipoPergunta.equals("SE")) {
            return "Simples  Escolha";
        }
        if (tipoPergunta.equals("TE")) {
            return "Textual";
        }
        if (tipoPergunta.equals("SN")) {
            return "Sim/Não";
        }
        if(tipoPergunta.equals("NS")){
            return "NPS";
        }
        return (tipoPergunta);
    }
     
    public void setTipoPergunta( String tipoPergunta ) {
        this.tipoPergunta = tipoPergunta;
    }

    public String getDescricaoAbreviada() {
        return getDescricao().length() > 70 ? getDescricao().substring(0, 70) : getDescricao();
    }

    public String getDescricao() {
        if (descricao== null) {
            descricao = "";
        }
        return (descricao);
    }
     
    public void setDescricao( String descricao ) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return (codigo);
    }
     
    public void setCodigo( Integer codigo ) {
        this.codigo = codigo;
    }

    public Boolean getAdicionarPergunta() {
        return adicionarPergunta;
    }

    public void setAdicionarPergunta(Boolean adicionarPergunta) {
        this.adicionarPergunta = adicionarPergunta;
    }

    public void registrarRespostaAntesAlteracao() throws Exception{
        respostaPerguntaVOsAntesAlteracao = new ArrayList<RespostaPerguntaVO>();
        for (RespostaPerguntaVO resposta : respostaPerguntaVOs){
            respostaPerguntaVOsAntesAlteracao.add((RespostaPerguntaVO) resposta.getClone(true));
        }
    }

    public List<RespostaPerguntaVO> getRespostaPerguntaVOsAntesAlteracao() {
        return respostaPerguntaVOsAntesAlteracao;
    }

    public void setRespostaPerguntaVOsAntesAlteracao(List<RespostaPerguntaVO> respostaPerguntaVOsAntesAlteracao) {
        this.respostaPerguntaVOsAntesAlteracao = respostaPerguntaVOsAntesAlteracao;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PerguntaVO that = (PerguntaVO) o;
        return Objects.equals(codigo, that.codigo) &&
                Objects.equals(descricao, that.descricao) &&
                Objects.equals(tipoPergunta, that.tipoPergunta) &&
                Objects.equals(adicionarPergunta, that.adicionarPergunta) &&
                Objects.equals(respostaPerguntaVOs, that.respostaPerguntaVOs) &&
                Objects.equals(respostaPerguntaVOsAntesAlteracao, that.respostaPerguntaVOsAntesAlteracao);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codigo, descricao, tipoPergunta, adicionarPergunta, respostaPerguntaVOs, respostaPerguntaVOsAntesAlteracao);
    }


    public boolean isNps() {
        return nps;
    }

    public void setNps(boolean nps) {
        this.nps = nps;
    }
}