package negocio.comuns.contrato;
import java.util.Date;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade Convenio. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
*/
public class ConvenioVO extends SuperVO {
	
    protected Integer codigo;
    protected String descricao;
    protected Date dataAssinatura;
    protected Date dataInicioVigencia;
    protected Date dataFinalVigencia;
    protected Double descontoParcela;
    protected Integer responsavelAutorizacao;
    protected Date dataAutorizacao;
    protected String situacao;
	
    /**
     * Construtor padrão da classe <code>Convenio</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
    */
    public ConvenioVO() {
        super();
        inicializarDados();
    }
     
	
    /**
     * Operação responsável por validar os dados de um objeto da classe <code>ConvenioVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
    */
    public static void validarDados(ConvenioVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
            }
        if (obj.getDescricao().equals("")) { 
            throw new ConsistirException("O campo DESCRIÇÃO (Convênio) deve ser informado.");
        }
        if (obj.getDataAssinatura() == null) { 
            throw new ConsistirException("O campo DATA DE ASSINATURA (Convênio) deve ser informado.");
        }
    }
     
    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
    */
    public void realizarUpperCaseDados() {
        setDescricao( getDescricao().toUpperCase() );
        setSituacao( getSituacao().toUpperCase() );
    }
     
    /**
     * Operação reponsável por inicializar os atributos da classe.
    */
    public void inicializarDados() {
        setCodigo( new Integer(0) );
        setDescricao( "" );
        setDataAssinatura( negocio.comuns.utilitarias.Calendario.hoje() );
        setDataInicioVigencia( negocio.comuns.utilitarias.Calendario.hoje() );
        setDataFinalVigencia( negocio.comuns.utilitarias.Calendario.hoje() );
        setDescontoParcela( new Double(0) );
        setResponsavelAutorizacao( new Integer(0) );
        setDataAutorizacao( negocio.comuns.utilitarias.Calendario.hoje() );
        setSituacao( "" );
    }
	

    public String getSituacao() {
        if (situacao == null ) {
            situacao = "";
        }
        return (situacao);
    }
     
    public void setSituacao( String situacao ) {
        this.situacao = situacao;
    }

    public Date getDataAutorizacao() {
        return (dataAutorizacao);
    }
     
    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa. 
    */
    public String getDataAutorizacao_Apresentar() {
        return (Uteis.getData(dataAutorizacao));
    }
     
    public void setDataAutorizacao( Date dataAutorizacao ) {
        this.dataAutorizacao = dataAutorizacao;
    }

    public Integer getResponsavelAutorizacao() {
        return (responsavelAutorizacao);
    }
     
    public void setResponsavelAutorizacao( Integer responsavelAutorizacao ) {
        this.responsavelAutorizacao = responsavelAutorizacao;
    }

    public Double getDescontoParcela() {
        return (descontoParcela);
    }
     
    public void setDescontoParcela( Double descontoParcela ) {
        this.descontoParcela = descontoParcela;
    }

    public Date getDataFinalVigencia() {
        return (dataFinalVigencia);
    }
     
    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa. 
    */
    public String getDataFinalVigencia_Apresentar() {
        return (Uteis.getData(dataFinalVigencia));
    }
     
    public void setDataFinalVigencia( Date dataFinalVigencia ) {
        this.dataFinalVigencia = dataFinalVigencia;
    }

    public Date getDataInicioVigencia() {
        return (dataInicioVigencia);
    }
     
    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa. 
    */
    public String getDataInicioVigencia_Apresentar() {
        return (Uteis.getData(dataInicioVigencia));
    }
     
    public void setDataInicioVigencia( Date dataInicioVigencia ) {
        this.dataInicioVigencia = dataInicioVigencia;
    }

    public Date getDataAssinatura() {
        return (dataAssinatura);
    }
     
    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa. 
    */
    public String getDataAssinatura_Apresentar() {
        return (Uteis.getData(dataAssinatura));
    }
     
    public void setDataAssinatura( Date dataAssinatura ) {
        this.dataAssinatura = dataAssinatura;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return (descricao);
    }
     
    public void setDescricao( String descricao ) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return (codigo);
    }
     
    public void setCodigo( Integer codigo ) {
        this.codigo = codigo;
    }
}