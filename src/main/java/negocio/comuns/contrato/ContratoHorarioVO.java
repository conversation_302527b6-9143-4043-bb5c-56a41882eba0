package negocio.comuns.contrato;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.plano.HorarioVO;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade PlanoHorario. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 * @see Plano
 */
public class ContratoHorarioVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    @NaoControlarLogAlteracao
    protected Integer contrato;
    /** Atributo responsável por manter o objeto relacionado da classe <code><PERSON><PERSON><PERSON> </code>.*/
    @ChaveEstrangeira
    protected HorarioVO horario;
    protected String tipoValor;
    protected String tipoOperacao;
    protected Double percentualDesconto;
    protected Double valorEspecifico;

    /**
     * Construtor padrão da classe <code>PlanoHorario</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ContratoHorarioVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>PlanoHorarioVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(ContratoHorarioVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if ((obj.getHorario() == null) ||
                (obj.getHorario().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo HORÁRIO (Horários) deve ser informado.");
        }

        if (obj.getTipoValor() == null) {
            obj.setTipoValor("");
        }
        if (obj.getTipoValor().equals("PD")) {
            if (obj.getTipoOperacao() == null || obj.getTipoOperacao().equals("")) {
                throw new ConsistirException("O campo TIPO de OPERAÇÃO (Plano Horário) deve ser informado.");
            }
            if (obj.getPercentualDesconto().doubleValue() == 0) {
                throw new ConsistirException("O campo PERCENTUAL DE DESCONTO (Plano Horário) deve ser informado.");
            }
        }
        if (obj.getTipoValor().equals("VE")) {
            if (obj.getTipoOperacao() == null || obj.getTipoOperacao().equals("")) {
                throw new ConsistirException("O campo TIPO de OPERAÇÃO (Plano Horário) deve ser informado.");
            }
            if (obj.getValorEspecifico().doubleValue() == 0) {
                throw new ConsistirException("O campo VALOR ESPECÍFICO (Plano Horário) deve ser informado.");
            }
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setHorario(new HorarioVO());
        setPercentualDesconto(new Double(0));
        setValorEspecifico(new Double(0));
        setTipoValor("");
        setTipoOperacao("");
    }

    /**
     * Retorna o objeto da classe <code>Horario</code> relacionado com (<code>PlanoHorario</code>).
     */
    public HorarioVO getHorario() {
        if (horario == null) {
            horario = new HorarioVO();
        }
        return (horario);
    }

    /**
     * Define o objeto da classe <code>Horario</code> relacionado com (<code>PlanoHorario</code>).
     */
    public void setHorario(HorarioVO obj) {
        this.horario = obj;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getValorEspecifico() {
        return (valorEspecifico);
    }

    public void setValorEspecifico(Double valorEspecifico) {
        this.valorEspecifico = valorEspecifico;
    }

    public Double getPercentualDesconto() {
        return (percentualDesconto);
    }

    public void setPercentualDesconto(Double percentualDesconto) {
        this.percentualDesconto = percentualDesconto;
    }

    public String getTipoValor() {
        if (tipoValor == null) {
            tipoValor = "";
        }
        return tipoValor;
    }

    public void setTipoValor(String tipoValor) {
        this.tipoValor = tipoValor;
    }

    public String getTipoOperacao() {
        if (tipoOperacao == null) {
            tipoOperacao = "";
        }
        return tipoOperacao;
    }

    public String getTipoOperacao_Apresentar() {
        if (tipoOperacao == null || tipoOperacao.equals("")) {
            return ("");
        }

        if (tipoOperacao.equals("AC")) {
            return ("Acrescimo");
        }
        if (tipoOperacao.equals("RE")) {
            return ("Redução");
        }

        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public boolean getApresentarValorEspecifico() {
        if (getTipoValor() == null || getTipoValor().equals("")) {
            return false;
        }
        if (getTipoValor().equals("VE")) {
            return true;
        }
        return false;
    }

    public boolean getApresentarValorDesconto() {
        if (getTipoValor() == null || getTipoValor().equals("")) {
            return false;
        }
        if (getTipoValor().equals("PD")) {
            return true;
        }
        return false;
    }
}
