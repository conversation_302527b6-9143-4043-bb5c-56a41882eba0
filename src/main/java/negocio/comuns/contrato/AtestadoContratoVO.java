/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.comuns.contrato;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

import annotations.arquitetura.NaoControlarLogAlteracao;
import java.util.Iterator;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class AtestadoContratoVO extends SuperVO {

    @NaoControlarLogAlteracao
    protected ContratoVO contratoVO;
//    protected Integer chavePrimariaDoBonusOperacao;
//    protected Integer chavePrimariaDoBonusHistorico;
    protected Integer empresa;
    protected Date dataInicio;
    protected Date dataTermino;
    protected Date dataInicioRetorno;
    protected Date dataTerminoRetorno;
    protected Date dataRegistro;
    protected Integer tipoJustificativa;
    protected Integer nrDias;
    protected Integer nrDiasAtestado;
    private Integer nrDiasASomar = 0;

//    protected Integer nrDiasBonusHistorico;
//    protected Integer diasRestanteOperacao;
//    protected Integer diasRestanteHistorico;
    protected String observacao;
    protected UsuarioVO responsavelOperacao;
    protected Boolean mensagemErro;
    protected Boolean apresentarPeriodoAtestado;
    protected Boolean qtdDiasAtestadoMaiorQueContrato;
    private boolean contratoVencido;
    private ContratoOperacaoVO contratoOperacaoVO;
    
    private String chaveArquivo;

    public AtestadoContratoVO() {
        super();
        inicializarDados();
    }

    public void inicializarDados() {
        setContratoVO(new ContratoVO());
        setResponsavelOperacao(new UsuarioVO());
        setObservacao("");
        setMensagemErro(false);
        setTipoJustificativa(0);
        setEmpresa(0);
        setNrDiasAtestado(0);
//        setDataInicio(negocio.comuns.utilitarias.Calendario.hoje());
//        setDataTermino(negocio.comuns.utilitarias.Calendario.hoje());
//        setDataInicioRetorno(negocio.comuns.utilitarias.Calendario.hoje());
//        setDataTerminoRetorno(negocio.comuns.utilitarias.Calendario.hoje());
        setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
        setApresentarPeriodoAtestado(false);
//        setNrDiasBonusHistorico(0);
//        setNrDiasBonusOperacao(0);
//        setDiasRestanteOperacao(0);
//        setDiasRestanteHistorico(0);
//        setChavePrimariaDoBonusHistorico(0);
//        setChavePrimariaDoBonusOperacao(0);
        setQtdDiasAtestadoMaiorQueContrato(false);
        setContratoVencido(false);
    }

    public void validarPeriodoAtestado(List<ContratoOperacaoVO> listaOperacao) throws Exception {
        if (getDataInicio() == null || getDataTermino() == null) {
            throw new ConsistirException("O período do atestado deve ser informado");
        }
        if (Uteis.getCompareData(getDataInicio(), getDataTermino()) > 0) {
            throw new ConsistirException("O campo ATÉ não pode ser antes do campo INÍCIO");
        }
        if (getTipoJustificativa().equals(0)) {
            throw new ConsistirException("A JUSTIFICATIVA DO ATESTADO deve ser Informada");
        }
        ValidacaoContratoOperacao.validarPeriodoOperacao("Atestado", getDataInicio(), this.contratoVO, listaOperacao);
        ValidacaoContratoOperacao.validarSeExisteTrancamentoSemRetorno(contratoVO);
    }

//    public Boolean validarVigernciaContrato() throws ParseException, Exception {
//        try {
//            if (Uteis.getCompareData(negocio.comuns.utilitarias.Calendario.hoje(), getContratoVO().getVigenciaAteAjustada()) > 0) {
//                return true;
//            } else {
//                return false;
//            }
//        } catch (Exception e) {
//            throw e;
//        }
//    }
    public void inicializarDadosOperacaoContrato(Boolean retroativo) throws Exception {
        try {
            ContratoOperacaoVO obj = new ContratoOperacaoVO();
            obj.setContrato(this.contratoVO.getCodigo());
            obj.setDataOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            if(isContratoVencido()){
                 obj.setDescricaoCalculo("Atestado Retroativo.\r\n"
                            + "O Contrato estava vencido na data de lançamento! \r\n"
                         + "Então foi  adicionado apenas o período de acesso referente aos dias que o(a) aluno(a) tinha direito.\r\n"
                         + "No caso "+ getNrDias() + " dias.");
            } else {
                if (retroativo) {
                    if (getQtdDiasAtestadoMaiorQueContrato()) {
                        obj.setDescricaoCalculo("Atestado Retroativo.\r\n"
                                + "Lançamento de um atestado retroativo portanto não foi modificado seu historico e nem seu periodo acesso.\r\n"
                                + "Nova data de termino ajustada de seu contrato: "
                                + contratoVO.getDataPrevistaRenovar_Apresentar()
                                + "\r\n---------------------------------------------------------------------------------------\n"
                                + "A quantidade de dias informado para o atestado é " + getNrDiasAtestado() + " dias. Maior que os " + getNrDias() + " dias restante do contrato portanto os dias\r\n"
                                + " de acrescimo serão os que ainda faltam no contrato.");
                    } else {
                        obj.setDescricaoCalculo("Atestado Retroativo.\r\n"
                                + "Lançamento de um atestado retroativo portanto não foi modificado seu historico e nem seu periodo acesso.\r\n"
                                + "Nova data de termino ajustada de seu contrato: "
                                + contratoVO.getDataPrevistaRenovar_Apresentar()
                                + "");
                    }
                } else {
                    if (getQtdDiasAtestadoMaiorQueContrato()) {
                        obj.setDescricaoCalculo("Atestado.\r\n"
                                + "Lançamento de um atestado: \r\n"
                                + "Nova data de termino ajustada de seu contrato : " + contratoVO.getVigenciaAteAjustada_Apresentar()
                                + "---------------------------------------------------------------------------------------\n"
                                + "A quantidade de dias informado para o atestado é  " + getNrDiasAtestado() + " dias. Maior que os " + getNrDias() + " dias restante do contrato portanto os dias\r\n"
                                + " de acrescimo serão os que ainda faltam no contrato.");
                    } else {
                        obj.setDescricaoCalculo("Atestado.\r\n"
                                + "Lançamento de um atestado: \r\n"
                                + "Nova data de termino ajustada de seu contrato : " + contratoVO.getVigenciaAteAjustada_Apresentar());
                    }
                }
            }
            obj.setObservacao(this.observacao);
            obj.setOperacaoPaga(false);
            obj.setResponsavel(this.responsavelOperacao);
            obj.getTipoJustificativa().setCodigo(this.tipoJustificativa);
            obj.setTipoOperacao("AT");
            obj.setDataFimEfetivacaoOperacao(this.dataTermino);
            obj.setDataInicioEfetivacaoOperacao(this.dataInicio);
            obj.setChaveArquivo(getChaveArquivo());
            getFacade().getContratoOperacao().incluirSemCommit(obj, false);
            this.setContratoOperacaoVO(obj);
            setNovoObj(false);
        } catch (Exception e) {
            throw e;
        }
    }

    public Boolean inicializarDadosHistoricoContratoAtestado() throws Exception {
        try {
            Boolean existeAvencer = false;
            HistoricoContratoVO hisAVencer = null;
            HistoricoContratoVO obj = new HistoricoContratoVO();
            obj = getFacade().getHistoricoContrato().obterHistoricoContratoPorCodigoContratoDataInicioDataFim(getContratoVO().getCodigo(), this.dataInicio, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (obj != null && obj.getAvencer()) {
                  hisAVencer = (HistoricoContratoVO) obj.getClone(true);
                //valido que dia começou o historio se foi no mesmo dia do novo registro de historico.  
                if (Uteis.getCompareData(obj.getDataInicioSituacao(), this.dataInicio) == 0) {
                    getFacade().getHistoricoContrato().excluir(obj);
                } else {
                    obj.setDataFinalSituacao(Uteis.obterDataAnterior(this.dataInicio, 1));
                    getFacade().getHistoricoContrato().alterarSemCommit(obj, true);
                }
               
                existeAvencer = true;
            } else if (obj != null) {
                //valido que dia começou o historio se foi no mesmo dia do novo registro de historico.
                if (Uteis.getCompareData(obj.getDataInicioSituacao(), this.dataInicio) == 0) {
                    obj.setDataFinalSituacao(Uteis.obterDataAnterior(this.dataInicio, 0));
                } else {
                    obj.setDataFinalSituacao(Uteis.obterDataAnterior(this.dataInicio, 1));
                }
                getFacade().getHistoricoContrato().alterarSemCommit(obj, true);
            }
            
            obj = new HistoricoContratoVO();
            obj.setContrato(this.contratoVO.getCodigo().intValue());
            obj.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setResponsavelRegistro(this.responsavelOperacao);
            obj.setSituacaoRelativaHistorico("");
            obj.setDescricao("ATESTADO");
            obj.setTipoHistorico("AT");
            obj.setDataFinalSituacao(this.dataTermino);
            obj.setDataInicioSituacao(this.dataInicio);

            ValidacaoHistoricoContrato.validarPeriodoHistoricoContratoOperacao(
                    this.getDataInicio(),
                    this.getDataTermino(),
                    this.getContratoVO().getCodigo(),
                    getFacade().getHistoricoContrato(),
                    obj);

            getFacade().getHistoricoContrato().incluirSemCommit(obj, true);

            obj = new HistoricoContratoVO();
            obj.setContrato(this.contratoVO.getCodigo().intValue());
            obj.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setResponsavelRegistro(this.responsavelOperacao);
            obj.setSituacaoRelativaHistorico("");
            obj.setDescricao("RETORNO-ATESTADO");
            obj.setTipoHistorico("RA");
            obj.setDataInicioSituacao(this.dataInicioRetorno);
            if (existeAvencer) {
                hisAVencer.setCodigo(0);
                hisAVencer.setNovoObj(true);
                hisAVencer.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
                obj.setDataFinalSituacao(this.dataInicioRetorno);
                hisAVencer.setDataInicioSituacao(obj.getDataFinalSituacao());
                hisAVencer.setDataFinalSituacao(this.dataTerminoRetorno);
            } else {
                obj.setDataFinalSituacao(this.dataTerminoRetorno);
            }
            getFacade().getHistoricoContrato().incluirSemCommit(obj, true);
            if (existeAvencer) {
                getFacade().getHistoricoContrato().incluirSemCommit(hisAVencer, true);
            }
            return existeAvencer;
        } catch (Exception e) {
            throw e;
        }
    }

     public void alterarUltimoHistorico() throws Exception {
         HistoricoContratoVO obj = getFacade().getHistoricoContrato().obterUltimoHistoricoContratoPorContrato(this.contratoVO.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
         if(obj.getAvencer()){
            getFacade().getHistoricoContrato().excluir(obj);
            obj = getFacade().getHistoricoContrato().obterUltimoHistoricoContratoPorContrato(this.contratoVO.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
         } 
         obj.setDataFinalSituacao(this.dataTerminoRetorno);
         getFacade().getHistoricoContrato().alterarSemCommit(obj, false);
     }

    public void inicializarDadosHistoricoContratoAtestadoRetroativo() throws Exception {
        try {
            HistoricoContratoVO obj = new HistoricoContratoVO();
            obj.setContrato(this.contratoVO.getCodigo().intValue());
            obj.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setResponsavelRegistro(this.responsavelOperacao);
            obj.setSituacaoRelativaHistorico("");
            obj.setDescricao("RETORNO-ATESTADO");
            obj.setTipoHistorico("RA");
            obj.setDataFinalSituacao(this.dataTerminoRetorno);
            obj.setDataInicioSituacao(this.dataInicioRetorno);
            getFacade().getHistoricoContrato().incluirSemCommit(obj, true);
        } catch (Exception e) {
            throw e;
        }
    }

    public void gravarPeriodoAcesso() throws Exception {
        try {
            PeriodoAcessoClienteVO obj = new PeriodoAcessoClienteVO();
            obj.setPessoa(getContratoVO().getPessoa().getCodigo().intValue());
            obj.setContrato(getContratoVO().getCodigo().intValue());
            obj.setContratoBaseadoRenovacao(getContratoVO().getContratoBaseadoRenovacao());
            obj.setTipoAcesso("AT");
            obj.setDataFinalAcesso(getDataTermino());
            obj.setDataInicioAcesso(getDataInicio());
            getFacade().getPeriodoAcessoCliente().incluirSemCommit(obj);
        } catch (Exception e) {
            throw e;
        }
    }

    public void alterarSituacaoContrato(Date data) throws Exception {
        try {
            getContratoVO().setDataPrevistaRenovar(data);
            getContratoVO().setDataPrevistaRematricula(data);
            getContratoVO().setVigenciaAteAjustada(data);
            getFacade().getContrato().alterarSituacaoContrato(getContratoVO());
        } catch (Exception e) {
            throw e;
        }
    }

    public void alterarSituacaoContratoRetroativo() throws Exception {
        try {
            Date data = negocio.comuns.utilitarias.Calendario.hoje();
            if (Uteis.getCompareData(getDataTermino(), getContratoVO().getVigenciaAteAjustada()) >= 0) {
                data = Uteis.obterDataFutura2(getDataTermino(), getNrDias());
            } else {
                data = Uteis.obterDataFutura2(getContratoVO().getVigenciaAteAjustada(), getNrDias());
            }
            getContratoVO().setDataPrevistaRenovar(data);
            getContratoVO().setDataPrevistaRematricula(data);
            getContratoVO().setVigenciaAteAjustada(data);
            getFacade().getContrato().alterarSituacaoContrato(getContratoVO());
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarDadosPeriodoAcessoCliente() throws Exception {
        try {
            List<PeriodoAcessoClienteVO> listaPeriodos = getFacade().getPeriodoAcessoCliente().consultarPorVigenteOuFuturoContrato(this.dataInicio, this.contratoVO.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS);
            for (PeriodoAcessoClienteVO periodoAcesso : listaPeriodos) {
                if (Uteis.getCompareData(periodoAcesso.getDataInicioAcesso(), this.dataInicio) == 0) {
                    getFacade().getPeriodoAcessoCliente().excluirSemCommit(periodoAcesso);
                } else {
                    periodoAcesso.setDataFinalAcesso(Uteis.obterDataAnterior(this.dataInicio, 1));
                    getFacade().getPeriodoAcessoCliente().alterarSemCommit(periodoAcesso);
                }
            }
            gravarPeriodoAcesso();
            inicializarPeriodoAcessoRetorno();
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarDadosRetornoPeriodoAcessoCliente() throws Exception {
        try {
            PeriodoAcessoClienteVO periodoAcesso = new PeriodoAcessoClienteVO();
            periodoAcesso = getFacade().getPeriodoAcessoCliente().consultarPorDataEspecificaECodigoContrato(this.dataInicio, this.contratoVO.getCodigo().intValue(), true, Uteis.NIVELMONTARDADOS_TODOS);
            if (periodoAcesso != null) {
                periodoAcesso.setDataFinalAcesso(Uteis.obterDataAnterior(this.dataInicio, 1));
                getFacade().getPeriodoAcessoCliente().alterarSemCommit(periodoAcesso);
            }
            inicializarDadosRetornoManualPeriodoAcesso();
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarPeriodoAcessoRetorno() throws Exception {
        try {
            PeriodoAcessoClienteVO novoPeriodoAcesso = new PeriodoAcessoClienteVO();
            novoPeriodoAcesso.setContrato(getContratoVO().getCodigo());
            novoPeriodoAcesso.setContratoBaseadoRenovacao(getContratoVO().getContratoBaseadoRenovacao());

            novoPeriodoAcesso.setDataFinalAcesso(getDataTerminoRetorno());
            novoPeriodoAcesso.setDataInicioAcesso(getDataInicioRetorno());

            novoPeriodoAcesso.setTipoAcesso("RA");
            novoPeriodoAcesso.setPessoa(getContratoVO().getPessoa().getCodigo());
            if (!getContratoVO().getSituacao().equals("IN")){
                List <PeriodoAcessoClienteVO> periodosFuturos = getFacade().getPeriodoAcessoCliente().consultarPorDataInicioContrato(getDataInicio(),getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                if(!periodosFuturos.isEmpty()){
                    Iterator i = periodosFuturos.iterator();
                    while (i.hasNext()){
                        PeriodoAcessoClienteVO periodo = (PeriodoAcessoClienteVO) i.next();
                        getFacade().getPeriodoAcessoCliente().excluirSemCommit(periodo);
                    }
                }
            }
            getFacade().getPeriodoAcessoCliente().incluirSemCommit(novoPeriodoAcesso);
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarPeriodoAcessoRetornoRetroativo() throws Exception {
        try {
            PeriodoAcessoClienteVO novoPeriodoAcesso = new PeriodoAcessoClienteVO();
            novoPeriodoAcesso.setContrato(getContratoVO().getCodigo());
            novoPeriodoAcesso.setContratoBaseadoRenovacao(getContratoVO().getContratoBaseadoRenovacao());
            novoPeriodoAcesso.setDataFinalAcesso(this.dataTerminoRetorno);
            novoPeriodoAcesso.setDataInicioAcesso(this.dataInicioRetorno);
            novoPeriodoAcesso.setTipoAcesso("RA");
            novoPeriodoAcesso.setPessoa(getContratoVO().getPessoa().getCodigo());
            getFacade().getPeriodoAcessoCliente().incluirSemCommit(novoPeriodoAcesso);

        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarDadosContratoRenovacao(ContratoVO contratoRenovacao, ContratoVO obj) throws Exception {
        try {
            Date dataFimAnterior = contratoRenovacao.getVigenciaAte();
            contratoRenovacao.obterDataFinalContratoComContratoDuracao(Uteis.obterDataFutura2(obj.getVigenciaAteAjustada(), 1));
            getFacade().getContrato().alterarDatasVigenciaContrato(contratoRenovacao);
            inicializarDadosMatriculaTurmaContratoRenovacao(contratoRenovacao, dataFimAnterior);
        } catch (Exception e) {
            throw e;
        }

    }

    public void inicializarDadosPeriodoAcessoContratoRenovacao(ContratoVO contrato, Date data) throws Exception {
        try {
            PeriodoAcessoClienteVO periodoAcesso = new PeriodoAcessoClienteVO();
            periodoAcesso = getFacade().getPeriodoAcessoCliente().consultarPorDataEspecificaECodigoContrato(data, contrato.getCodigo().intValue(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (periodoAcesso != null) {
                getFacade().getPeriodoAcessoCliente().excluirSemCommit(periodoAcesso);
            }
            periodoAcesso = new PeriodoAcessoClienteVO();
            periodoAcesso.setContrato(contrato.getCodigo().intValue());
            periodoAcesso.setDataInicioAcesso(contrato.getVigenciaDe());
            periodoAcesso.setDataFinalAcesso(contrato.getVigenciaAteAjustada());
            periodoAcesso.setPessoa(contrato.getPessoa().getCodigo().intValue());
            periodoAcesso.setTipoAcesso("CA");
            getFacade().getPeriodoAcessoCliente().incluirSemCommit(periodoAcesso);
        } catch (Exception e) {
            throw e;
        }

    }

    public void inicializarDadosContratoOperacaoContratoRenovacao(ContratoVO contratoRenovacao, ContratoVO obj) throws Exception {
        try {
            ContratoOperacaoVO objContratoOperacaoVO = new ContratoOperacaoVO();
            objContratoOperacaoVO.setContrato(contratoRenovacao.getCodigo().intValue());
            objContratoOperacaoVO.setDataFimEfetivacaoOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            objContratoOperacaoVO.setDataInicioEfetivacaoOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            objContratoOperacaoVO.setDataOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            objContratoOperacaoVO.setDescricaoCalculo("");
            objContratoOperacaoVO.setObservacao("Modificação: \n\r"
                    + " Data de Início,\n\r"
                    + " Data de Término,\n\r"
                    + " Data de Previsão Renovação,\n\r"
                    + " Data de Previsão Rematricula,\n\r"
                    + " Devido uma OPERAÇÃO de Atesado no contrato de Numero: " + obj.getCodigo().intValue() + ".");
            objContratoOperacaoVO.setOperacaoPaga(false);
            objContratoOperacaoVO.setResponsavel(getResponsavelOperacao());
            objContratoOperacaoVO.setTipoOperacao("AC");
            objContratoOperacaoVO.getTipoJustificativa().setCodigo(this.tipoJustificativa);
            getFacade().getContratoOperacao().incluirSemCommit(objContratoOperacaoVO, false);
            this.setContratoOperacaoVO(objContratoOperacaoVO);
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarDadosHistoricoContratoContratoRenovacao(ContratoVO contratoRenovacao, 
            ContratoVO obj, Date dataVigencia) throws Exception {
        try {            
            HistoricoContratoVO objHistoricoContratoVO = new HistoricoContratoVO();
            objHistoricoContratoVO = getFacade().getHistoricoContrato().
                    obterHistoricoContratoPorDataEspecifica(
                    contratoRenovacao.getCodigo().intValue(), dataVigencia,
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (objHistoricoContratoVO != null) {
                objHistoricoContratoVO.setDataInicioSituacao(contratoRenovacao.getVigenciaDe());
                objHistoricoContratoVO.setDataFinalSituacao(contratoRenovacao.getVigenciaAteAjustada());
                getFacade().getHistoricoContrato().alterarSemCommit(objHistoricoContratoVO, true);
            } else {
                throw new Exception(String.format("Não foi possível encontrar um "
                        + "histórico de contrato na data %s para o contrato sucessor %s",
                        new Object[]{
                            Uteis.getData(dataVigencia),
                            contratoRenovacao.getCodigo()
                        }));
            }

        } catch (Exception e) {
            throw e;
        }
    }
    
    public void inicializarDadosMatriculaTurmaContratoRenovacao(ContratoVO contratoSucessor, Date dataFimAnterior) throws Exception {
    	 List<MatriculaAlunoHorarioTurmaVO> lista = getFacade().getMatriculaAlunoHorarioTurma().consultarMatriculaAtiva(contratoSucessor.getCodigo(), dataFimAnterior);
    	 for (MatriculaAlunoHorarioTurmaVO matricula : lista) {
    		 matricula.setDataInicio(contratoSucessor.getVigenciaDe());
         	if (contratoSucessor.getContratoResponsavelRenovacaoMatricula() == null || contratoSucessor.getContratoResponsavelRenovacaoMatricula() == 0  ){
                    matricula.setDataFim(Uteis.somarDias(contratoSucessor.getVigenciaAte(), contratoSucessor.getEmpresa().getToleranciaOcupacaoTurma()));
         	} else {
         			matricula.setDataFim(contratoSucessor.getVigenciaAte());
         	}
             getFacade().getMatriculaAlunoHorarioTurma().alterarInicioFimMatriculaSemCommit(matricula);
         }
		
	}

    public void alterarOperacaoAtestado() throws Exception {
        try {
            ContratoOperacaoVO obj = new ContratoOperacaoVO();
            obj = getFacade().getContratoOperacao().consultarOperacaoContratoPorDataInicioCodigoContratoTipoOperacao(negocio.comuns.utilitarias.Calendario.hoje(), getContratoVO().getCodigo().intValue(),"AT",false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (obj != null) {
                if (Uteis.getCompareData(negocio.comuns.utilitarias.Calendario.hoje(), obj.getDataInicioEfetivacaoOperacao()) == 0) {
                    Long nrDias = Uteis.nrDiasEntreDatas(obj.getDataInicioEfetivacaoOperacao(), obj.getDataFimEfetivacaoOperacao());
                    setNrDias(nrDias.intValue() + 1);
                    obj.setDataFimEfetivacaoOperacao(negocio.comuns.utilitarias.Calendario.hoje());
                } else {
                    Long nrDias = Uteis.nrDiasEntreDatas(negocio.comuns.utilitarias.Calendario.hoje(), obj.getDataFimEfetivacaoOperacao());
                    setNrDias(nrDias.intValue() + 1);
                    obj.setDataFimEfetivacaoOperacao(Uteis.obterDataAnterior(negocio.comuns.utilitarias.Calendario.hoje(), 1));
                }
                getFacade().getContratoOperacao().alterarSemCommit(obj);
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public Date inicializarDadosRetornoAtestadoHistoricoContrato() throws Exception {
        try {
            Date data = negocio.comuns.utilitarias.Calendario.hoje();

            //alterando atestado
            HistoricoContratoVO atestado = getFacade().getHistoricoContrato().
                    obterUltimoHistoricoContratoPorContratoTipoHistorico(
                    getContratoVO().getCodigo().intValue(), "AT", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (atestado != null) {
                if (Uteis.getCompareData(negocio.comuns.utilitarias.Calendario.hoje(), atestado.getDataInicioSituacao()) == 0) {
                    getFacade().getHistoricoContrato().excluir(atestado);
                    HistoricoContratoVO retorno = getFacade().getHistoricoContrato().obterUltimoHistoricoContratoPorContratoTipoHistorico(
                        getContratoVO().getCodigo().intValue(), "RA", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    getFacade().getHistoricoContrato().excluir(retorno);
                    data = Uteis.obterDataAnterior(retorno.getDataFinalSituacao(), getNrDias().intValue());
                    HistoricoContratoVO ultimo = getFacade().getHistoricoContrato().obterUltimoHistoricoContratoPorContrato(getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                     if(ultimo.getAvencer() && Calendario.maior(ultimo.getDataInicioSituacao(),negocio.comuns.utilitarias.Calendario.hoje() )){
                        data = Uteis.obterDataAnterior(ultimo.getDataFinalSituacao(), getNrDias().intValue());
                        getFacade().getHistoricoContrato().excluir(ultimo);
                        ultimo = getFacade().getHistoricoContrato().obterUltimoHistoricoContratoPorContrato(getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    }
                    ultimo.setDataFinalSituacao(data);
                    getFacade().getHistoricoContrato().alterarSemCommit(ultimo, false);
                } else {
                    atestado.setDataFinalSituacao(Uteis.obterDataAnterior(negocio.comuns.utilitarias.Calendario.hoje(), 1));
                    getFacade().getHistoricoContrato().alterarSemCommit(atestado, true);
                    HistoricoContratoVO retorno = getFacade().getHistoricoContrato().obterUltimoHistoricoContratoPorContratoTipoHistorico(
                        getContratoVO().getCodigo().intValue(), "RA", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                     HistoricoContratoVO ultimo = getFacade().getHistoricoContrato().obterUltimoHistoricoContratoPorContrato(getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (retorno != null) {
                        if (retorno.isRetornoManual()) {
                            throw new Exception(String.format("Retorno manual para o histórico \"%s\" já foi realizado.",
                                    new Object[]{
                                        retorno.getDescricao()
                                    }));
                        }
                        if (Uteis.getCompareData(negocio.comuns.utilitarias.Calendario.hoje(), retorno.getDataInicioSituacao()) == 0) {
                            retorno.setDataFinalSituacao(negocio.comuns.utilitarias.Calendario.hoje());
                        } else {
                            //se é o mesmo dia da carência e retorno, então o retorno começa amanhã
                            retorno.setDataInicioSituacao(Uteis.obterDataFutura(atestado.getDataFinalSituacao(), 2));
                            retorno.setDataFinalSituacao(Uteis.obterDataAnterior(retorno.getDataFinalSituacao(), getNrDias().intValue()));
                        }
                        data = retorno.getDataFinalSituacao();
                        //setando que o retorno manual da operação de origem já foi executado
                        retorno.setRetornoManual(true);
                        if(!ultimo.getCodigo().equals(retorno.getCodigo()) && ultimo.getAvencer()){ // trata hisotico a vencer
                            ultimo.setDataInicioSituacao(retorno.getDataInicioSituacao());
                            ultimo.setDataFinalSituacao(Uteis.obterDataAnterior(ultimo.getDataFinalSituacao(), getNrDias()));
                            retorno.setDataFinalSituacao(retorno.getDataInicioSituacao());
                            getFacade().getHistoricoContrato().alterarSemCommit(ultimo, false);
                            data = ultimo.getDataFinalSituacao();
                        }
                        getFacade().getHistoricoContrato().alterarSemCommit(retorno, true);
                    }
                }
                
            }
            //alterando retorno atestado
        
            return data;
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarDadosRetornoManualPeriodoAcesso() throws Exception {
        try {

            //alterando periodo atestado
            PeriodoAcessoClienteVO perioAcessoAtestado = getFacade().getPeriodoAcessoCliente().obterUltimoDiaPeriodoAcessoContratoTipo(getContratoVO().getCodigo(), "AT", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (perioAcessoAtestado != null) {
                if (Calendario.igual(negocio.comuns.utilitarias.Calendario.hoje(), perioAcessoAtestado.getDataInicioAcesso())) {
                    perioAcessoAtestado.setDataFinalAcesso(negocio.comuns.utilitarias.Calendario.hoje());
                } else {
                    perioAcessoAtestado.setDataFinalAcesso(Uteis.obterDataAnterior(negocio.comuns.utilitarias.Calendario.hoje(), 1));
                }
                getFacade().getPeriodoAcessoCliente().alterarSemCommit(perioAcessoAtestado);
            }
            //alterando periodo retorno atestado
            PeriodoAcessoClienteVO perioAcessoRetorno = getFacade().getPeriodoAcessoCliente().obterUltimoDiaPeriodoAcessoContratoTipo(getContratoVO().getCodigo(), "RA", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (perioAcessoRetorno != null) {
                if (Calendario.igual(negocio.comuns.utilitarias.Calendario.hoje(), perioAcessoRetorno.getDataInicioAcesso())) {
                    perioAcessoRetorno.setDataFinalAcesso(negocio.comuns.utilitarias.Calendario.hoje());
                } else {
                    //no mesmo dia do retorno manual o aluno estará de atestado, porém, o seu período de acesso de retorno do atestado
                    //liberará o acesso para o aluno. Isso é uma regra!
                    perioAcessoRetorno.setDataInicioAcesso(Uteis.obterDataFutura(perioAcessoAtestado.getDataFinalAcesso(), 2));
                }
                getFacade().getPeriodoAcessoCliente().alterarSemCommit(perioAcessoRetorno);
                
            }
            PeriodoAcessoClienteVO perioAcessoUltimo = getFacade().getPeriodoAcessoCliente().obterUltimoDiaPeriodoAcessoContrato(getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (perioAcessoUltimo != null) {
            	perioAcessoUltimo.setDataFinalAcesso(Uteis.obterDataAnterior(perioAcessoUltimo.getDataFinalAcesso(), getNrDias().intValue()));
            	 getFacade().getPeriodoAcessoCliente().alterarSemCommit(perioAcessoUltimo);
            }
        } catch (Exception e) {
            throw e;
        }

    }

    public Boolean getApresentarPeriodoAtestado() {
        return apresentarPeriodoAtestado;
    }

    public void setApresentarPeriodoAtestado(Boolean apresentarPeriodoAtestado) {
        this.apresentarPeriodoAtestado = apresentarPeriodoAtestado;
    }

    public ContratoVO getContratoVO() {
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public String getDataInicio_Apresentar() {
        return Uteis.getData(dataInicio);
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Date getDataTermino() {
        return dataTermino;
    }

    public String getDataTermino_Apresentar() {
        return Uteis.getData(dataTermino);
    }

    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Boolean getMensagemErro() {
        return mensagemErro;
    }

    public void setMensagemErro(Boolean mensagemErro) {
        this.mensagemErro = mensagemErro;
    }

    public Integer getNrDias() {
        return nrDias;
    }

    public void setNrDias(Integer nrDias) {
        this.nrDias = nrDias;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public UsuarioVO getResponsavelOperacao() {
        return responsavelOperacao;
    }

    public void setResponsavelOperacao(UsuarioVO responsavelOperacao) {
        this.responsavelOperacao = responsavelOperacao;
    }

    public Integer getTipoJustificativa() {
        return tipoJustificativa;
    }

    public void setTipoJustificativa(Integer tipoJustificativa) {
        this.tipoJustificativa = tipoJustificativa;
    }

    public Date getDataInicioRetorno() {
        return dataInicioRetorno;
    }

    public String getDataInicioRetorno_Apresentar() {
        return Uteis.getData(dataInicioRetorno);
    }

    public void setDataInicioRetorno(Date dataInicioRetorno) {
        this.dataInicioRetorno = dataInicioRetorno;
    }

    public Date getDataTerminoRetorno() {
        return dataTerminoRetorno;
    }

    public String getDataTerminoRetorno_Apresentar() {
        return Uteis.getData(dataTerminoRetorno);
    }

    public void setDataTerminoRetorno(Date dataTerminoRetorno) {
        this.dataTerminoRetorno = dataTerminoRetorno;
    }

    /**
     * @return the nrDiasAtestado
     */
    public Integer getNrDiasAtestado() {
        return nrDiasAtestado;
    }

    /**
     * @param nrDiasAtestado the nrDiasAtestado to set
     */
    public void setNrDiasAtestado(Integer nrDiasAtestado) {
        this.nrDiasAtestado = nrDiasAtestado;
    }

    public Boolean getQtdDiasAtestadoMaiorQueContrato() {
        return qtdDiasAtestadoMaiorQueContrato;
    }

    public void setQtdDiasAtestadoMaiorQueContrato(Boolean qtdDiasAtestadoMaiorQueContrato) {
        this.qtdDiasAtestadoMaiorQueContrato = qtdDiasAtestadoMaiorQueContrato;
    }

    public Integer getNrDiasASomar() {
        if(nrDiasASomar == null){
            nrDiasASomar= 0;
        }
        return nrDiasASomar;
    }

    public void setNrDiasASomar(Integer nrDiasASomar) {
        this.nrDiasASomar = nrDiasASomar;
    }
    
    public void setContratoVencido(boolean contratoVencido) {
            this.contratoVencido = contratoVencido;
    }

    public boolean isContratoVencido() {
            return contratoVencido;
    }

    public ContratoOperacaoVO getContratoOperacaoVO() {
        if (contratoOperacaoVO == null) {
            contratoOperacaoVO = new ContratoOperacaoVO();
        }
        return contratoOperacaoVO;
    }

    public void setContratoOperacaoVO(ContratoOperacaoVO contratoOperacaoVO) {
        this.contratoOperacaoVO = contratoOperacaoVO;
    }

    public String getChaveArquivo() {
        if (chaveArquivo == null) {
            chaveArquivo = "";
        }
        return chaveArquivo;
    }

    public void setChaveArquivo(String chaveArquivo) {
        this.chaveArquivo = chaveArquivo;
    }
}
