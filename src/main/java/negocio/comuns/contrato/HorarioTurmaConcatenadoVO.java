/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.contrato;

import java.util.Iterator;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.HorarioTurmaVO;

/**
 *
 * <AUTHOR>
 */
public class HorarioTurmaConcatenadoVO extends SuperVO {

    protected Integer domingo;
    protected Integer segunda;
    protected Integer terca;
    protected Integer quarta;
    protected Integer quinta;
    protected Integer sexta;
    protected Integer sabado;
    protected Boolean domingoEscolhido;
    protected Boolean segundaEscolhido;
    protected Boolean tercaEscolhido;
    protected Boolean quartaEscolhido;
    protected Boolean quintaEscolhido;
    protected Boolean sextaEscolhido;
    protected Boolean sabadoEscolhido;
    protected String turma;
    protected ContratoModalidadeHorarioTurmaVO contratoModalidadeHorarioTurma;
    protected HorarioTurmaVO horarioTurma;
    private boolean domingoCheio;
    private boolean segundaCheia;
    private boolean tercaCheia;
    private boolean quartaCheia;
    private boolean quintaCheia;
    private boolean sextaCheia;
    private boolean sabadoCheio;
    
    private boolean escolhido;

    public HorarioTurmaConcatenadoVO() {
        inicializarDados();
    }

    private void inicializarDados() {
        setDomingo(new Integer(0));
        setSegunda(new Integer(0));
        setTerca(new Integer(0));
        setQuarta(new Integer(0));
        setQuinta(new Integer(0));
        setSexta(new Integer(0));
        setSabado(new Integer(0));
        setDomingoEscolhido(false);
        setSegundaEscolhido(false);
        setTercaEscolhido(false);
        setQuartaEscolhido(false);
        setQuintaEscolhido(false);
        setSextaEscolhido(false);
        setSabadoEscolhido(false);
        setTurma("");
        setContratoModalidadeHorarioTurma(new ContratoModalidadeHorarioTurmaVO());
        setHorarioTurma(new HorarioTurmaVO());

    }

    public void verificarModalidadeTurma(ContratoModalidadeTurmaVO contratoTurma, String diaSemana, Boolean horarioEscolhido, Integer vagas) throws Exception {
        Iterator i = contratoTurma.getContratoModalidadeHorarioTurmaVOs().iterator();
        while (i.hasNext()) {
            ContratoModalidadeHorarioTurmaVO contratoHorarioTurma = (ContratoModalidadeHorarioTurmaVO) i.next();
            if (vagas.intValue() != 0) {
                alterarValorContratoModalidadeHorarioTurma(contratoHorarioTurma, diaSemana, horarioEscolhido);
            } else if (!contratoTurma.getTurma().getBloquearMatriculasAcimaLimite()) {
                alterarValorContratoModalidadeHorarioTurma(contratoHorarioTurma, diaSemana, horarioEscolhido);
            } else if (horarioEscolhido) {
                throw new Exception("O horário escolhido está cheio, Por Favor escolha outro horário.");
            } else {
                throw new Exception("");
            }
        }
    }

    public void alterarValorContratoModalidadeHorarioTurma(ContratoModalidadeHorarioTurmaVO contratoHorarioTurma, String diaSemana, Boolean horarioEscolhido) {
        if (contratoHorarioTurma.getHorarioTurma().getDiaSemana().equals(diaSemana) &&
                contratoHorarioTurma.getHorarioTurma().getHoraInicial().equals(getContratoModalidadeHorarioTurma().getHorarioTurma().getHoraInicial()) &&
                contratoHorarioTurma.getHorarioTurma().getHoraFinal().equals(getContratoModalidadeHorarioTurma().getHorarioTurma().getHoraFinal()) &&
                contratoHorarioTurma.getHorarioTurma().getAmbiente().getCodigo().equals(getContratoModalidadeHorarioTurma().getHorarioTurma().getAmbiente().getCodigo().intValue()) &&
                contratoHorarioTurma.getHorarioTurma().getNivelTurma().getCodigo().equals(getContratoModalidadeHorarioTurma().getHorarioTurma().getNivelTurma().getCodigo().intValue()) &&
                contratoHorarioTurma.getHorarioTurma().getProfessor().getCodigo().equals(getContratoModalidadeHorarioTurma().getHorarioTurma().getProfessor().getCodigo().intValue())) {

            contratoHorarioTurma.getHorarioTurma().setHorarioTurmaEscolhida(horarioEscolhido);
        }
    }

    public void verificarDuplicidadeEscolhaHorario(HorarioTurmaConcatenadoVO obj, String diaSemana) throws Exception {
        try {
            if (getContratoModalidadeHorarioTurma().getHorarioTurma().getHoraInicial().equals(obj.getContratoModalidadeHorarioTurma().getHorarioTurma().getHoraInicial()) &&
                    getContratoModalidadeHorarioTurma().getHorarioTurma().getHoraFinal().equals(obj.getContratoModalidadeHorarioTurma().getHorarioTurma().getHoraFinal()) &&
                    !getContratoModalidadeHorarioTurma().getHorarioTurma().getProfessor().getCodigo().equals(obj.getContratoModalidadeHorarioTurma().getHorarioTurma().getProfessor().getCodigo().intValue())) {


                throw new Exception("O horário das " + obj.getContratoModalidadeHorarioTurma().getHorarioTurma().getHoraInicial() +
                        " as " + obj.getContratoModalidadeHorarioTurma().getHorarioTurma().getHoraFinal() + " do dia " + diaSemana +
                        " da turma " + obj.getTurma() + " não pode ser MARCADO pois, já existe esse Horário marcado para turma " + getTurma() + ".");
            }
        } catch (Exception e) {
            throw e;
        }

    }

    public void validarHorarioTurmaConcatenadoDiaSemana(ContratoModalidadeHorarioTurmaVO contratoHorarioTurma) {
        if (contratoHorarioTurma.getHorarioTurma().getDiaSemana().equals("DM")) {
            setDomingo(contratoHorarioTurma.getHorarioTurma().getNrMaximoAluno() - contratoHorarioTurma.getHorarioTurma().getNrAlunoMatriculado());
            if (contratoHorarioTurma.getHorarioTurma().getHorarioTurmaEscolhida()) {
                setDomingoEscolhido(true);
            }
        }
        if (contratoHorarioTurma.getHorarioTurma().getDiaSemana().equals("SG")) {
            setSegunda(contratoHorarioTurma.getHorarioTurma().getNrMaximoAluno() - contratoHorarioTurma.getHorarioTurma().getNrAlunoMatriculado());
            if (contratoHorarioTurma.getHorarioTurma().getHorarioTurmaEscolhida()) {
                setSegundaEscolhido(true);
            }
        }
        if (contratoHorarioTurma.getHorarioTurma().getDiaSemana().equals("TR")) {
            setTerca(contratoHorarioTurma.getHorarioTurma().getNrMaximoAluno() - contratoHorarioTurma.getHorarioTurma().getNrAlunoMatriculado());
            if (contratoHorarioTurma.getHorarioTurma().getHorarioTurmaEscolhida()) {
                setTercaEscolhido(true);
            }
        }
        if (contratoHorarioTurma.getHorarioTurma().getDiaSemana().equals("QA")) {
            setQuarta(contratoHorarioTurma.getHorarioTurma().getNrMaximoAluno() - contratoHorarioTurma.getHorarioTurma().getNrAlunoMatriculado());
            if (contratoHorarioTurma.getHorarioTurma().getHorarioTurmaEscolhida()) {
                setQuartaEscolhido(true);
            }
        }
        if (contratoHorarioTurma.getHorarioTurma().getDiaSemana().equals("QI")) {
            setQuinta(contratoHorarioTurma.getHorarioTurma().getNrMaximoAluno() - contratoHorarioTurma.getHorarioTurma().getNrAlunoMatriculado());
            if (contratoHorarioTurma.getHorarioTurma().getHorarioTurmaEscolhida()) {
                setQuintaEscolhido(true);
            }
        }
        if (contratoHorarioTurma.getHorarioTurma().getDiaSemana().equals("SX")) {
            setSexta(contratoHorarioTurma.getHorarioTurma().getNrMaximoAluno() - contratoHorarioTurma.getHorarioTurma().getNrAlunoMatriculado());
            if (contratoHorarioTurma.getHorarioTurma().getHorarioTurmaEscolhida()) {
                setSextaEscolhido(true);
            }
        }
        if (contratoHorarioTurma.getHorarioTurma().getDiaSemana().equals("SB")) {
            setSabado(contratoHorarioTurma.getHorarioTurma().getNrMaximoAluno() - contratoHorarioTurma.getHorarioTurma().getNrAlunoMatriculado());
            if (contratoHorarioTurma.getHorarioTurma().getHorarioTurmaEscolhida()) {
                setSabadoEscolhido(true);
            }
        }
    }

    public int validarHorarioTurmaConcatenado(ContratoModalidadeHorarioTurmaVO contratoHorarioTurma, List listaHorarioTurmaConcatenadoVOs) {
        int passo = 0;
        Iterator i = listaHorarioTurmaConcatenadoVOs.iterator();
        while (i.hasNext()) {
            passo = 0;
            HorarioTurmaConcatenadoVO horarioTurmaConcatenado = (HorarioTurmaConcatenadoVO) i.next();
            if (horarioTurmaConcatenado.getContratoModalidadeHorarioTurma().getHorarioTurma().getTurma().equals(contratoHorarioTurma.getHorarioTurma().getTurma().intValue())) {
                passo++;
            }
            if (horarioTurmaConcatenado.getContratoModalidadeHorarioTurma().getHorarioTurma().getAmbiente().getCodigo().equals(contratoHorarioTurma.getHorarioTurma().getAmbiente().getCodigo().intValue())) {
                passo++;
            }
            if (horarioTurmaConcatenado.getContratoModalidadeHorarioTurma().getHorarioTurma().getNivelTurma().getCodigo().equals(contratoHorarioTurma.getHorarioTurma().getNivelTurma().getCodigo().intValue())) {
                passo++;
            }
            if (horarioTurmaConcatenado.getContratoModalidadeHorarioTurma().getHorarioTurma().getProfessor().getCodigo().equals(contratoHorarioTurma.getHorarioTurma().getProfessor().getCodigo().intValue())) {
                passo++;
            }
            if (horarioTurmaConcatenado.getContratoModalidadeHorarioTurma().getHorarioTurma().getHoraInicial().equals(contratoHorarioTurma.getHorarioTurma().getHoraInicial())) {
                passo++;
            }
            if (horarioTurmaConcatenado.getContratoModalidadeHorarioTurma().getHorarioTurma().getHoraFinal().equals(contratoHorarioTurma.getHorarioTurma().getHoraFinal())) {
                passo++;
            }
            if (passo == 6) {
                validarHorarioTurmaConcatenadoDiaSemana(horarioTurmaConcatenado, contratoHorarioTurma);
                return passo;
            }
        }
        validarHorarioTurmaConcatenadoDiaSemana(contratoHorarioTurma);
        return passo;


    }

    public void validarHorarioTurmaConcatenadoDiaSemana(HorarioTurmaConcatenadoVO horarioTurmaConcatenado, ContratoModalidadeHorarioTurmaVO contratoHorarioTurma) {
        if (contratoHorarioTurma.getHorarioTurma().getDiaSemana().equals("DM")) {
            horarioTurmaConcatenado.setDomingo(contratoHorarioTurma.getHorarioTurma().getNrMaximoAluno() - contratoHorarioTurma.getHorarioTurma().getNrAlunoMatriculado());
            if (contratoHorarioTurma.getHorarioTurma().getHorarioTurmaEscolhida()) {
                horarioTurmaConcatenado.setDomingoEscolhido(true);
            }
        }
        if (contratoHorarioTurma.getHorarioTurma().getDiaSemana().equals("SG")) {
            horarioTurmaConcatenado.setSegunda(contratoHorarioTurma.getHorarioTurma().getNrMaximoAluno() - contratoHorarioTurma.getHorarioTurma().getNrAlunoMatriculado());
            if (contratoHorarioTurma.getHorarioTurma().getHorarioTurmaEscolhida()) {
                horarioTurmaConcatenado.setSegundaEscolhido(true);
            }
        }
        if (contratoHorarioTurma.getHorarioTurma().getDiaSemana().equals("TR")) {
            horarioTurmaConcatenado.setTerca(contratoHorarioTurma.getHorarioTurma().getNrMaximoAluno() - contratoHorarioTurma.getHorarioTurma().getNrAlunoMatriculado());
            if (contratoHorarioTurma.getHorarioTurma().getHorarioTurmaEscolhida()) {
                horarioTurmaConcatenado.setTercaEscolhido(true);
            }
        }
        if (contratoHorarioTurma.getHorarioTurma().getDiaSemana().equals("QA")) {
            horarioTurmaConcatenado.setQuarta(contratoHorarioTurma.getHorarioTurma().getNrMaximoAluno() - contratoHorarioTurma.getHorarioTurma().getNrAlunoMatriculado());
            if (contratoHorarioTurma.getHorarioTurma().getHorarioTurmaEscolhida()) {
                horarioTurmaConcatenado.setQuartaEscolhido(true);
            }
        }
        if (contratoHorarioTurma.getHorarioTurma().getDiaSemana().equals("QI")) {
            horarioTurmaConcatenado.setQuinta(contratoHorarioTurma.getHorarioTurma().getNrMaximoAluno() - contratoHorarioTurma.getHorarioTurma().getNrAlunoMatriculado());
            if (contratoHorarioTurma.getHorarioTurma().getHorarioTurmaEscolhida()) {
                horarioTurmaConcatenado.setQuintaEscolhido(true);
            }
        }
        if (contratoHorarioTurma.getHorarioTurma().getDiaSemana().equals("SX")) {
            horarioTurmaConcatenado.setSexta(contratoHorarioTurma.getHorarioTurma().getNrMaximoAluno() - contratoHorarioTurma.getHorarioTurma().getNrAlunoMatriculado());
            if (contratoHorarioTurma.getHorarioTurma().getHorarioTurmaEscolhida()) {
                horarioTurmaConcatenado.setSextaEscolhido(true);
            }
        }
        if (contratoHorarioTurma.getHorarioTurma().getDiaSemana().equals("SB")) {
            horarioTurmaConcatenado.setSabado(contratoHorarioTurma.getHorarioTurma().getNrMaximoAluno() - contratoHorarioTurma.getHorarioTurma().getNrAlunoMatriculado());
            if (contratoHorarioTurma.getHorarioTurma().getHorarioTurmaEscolhida()) {
                horarioTurmaConcatenado.setSabadoEscolhido(true);
            }
        }
    }

    public int agruparHorarioTurmaConcatenado(HorarioTurmaVO horarioTurma, List listaHorarioTurmaConcatenadoVOs) {
        int passo = 0;
        Iterator i = listaHorarioTurmaConcatenadoVOs.iterator();
        while (i.hasNext()) {
            passo = 0;
            HorarioTurmaConcatenadoVO horarioTurmaConcatenado = (HorarioTurmaConcatenadoVO) i.next();
            if (horarioTurmaConcatenado.getHorarioTurma().getTurma().equals(horarioTurma.getTurma().intValue())) {
                passo++;
            }
            if (horarioTurmaConcatenado.getHorarioTurma().getAmbiente().getCodigo().equals(horarioTurma.getAmbiente().getCodigo().intValue())) {
                passo++;
            }
            if (horarioTurmaConcatenado.getHorarioTurma().getNivelTurma().getCodigo().equals(horarioTurma.getNivelTurma().getCodigo().intValue())) {
                passo++;
            }
            if (horarioTurmaConcatenado.getHorarioTurma().getProfessor().getCodigo().equals(horarioTurma.getProfessor().getCodigo().intValue())) {
                passo++;
            }
            if (horarioTurmaConcatenado.getHorarioTurma().getHoraInicial().equals(horarioTurma.getHoraInicial())) {
                passo++;
            }
            if (horarioTurmaConcatenado.getHorarioTurma().getHoraFinal().equals(horarioTurma.getHoraFinal())) {
                passo++;
            }
            if (passo == 6) {
                agruparHorarioTurmaConcatenadoDiaSemana(horarioTurmaConcatenado, horarioTurma);
                return passo;
            }
        }
        agruparHorarioTurmaConcatenadoDiaSemana(this, horarioTurma);
        return passo;
    }

    public static void agruparHorarioTurmaConcatenadoDiaSemana(HorarioTurmaConcatenadoVO horarioTurmaConcatenado, HorarioTurmaVO horarioTurma) {
        int qtde;
        boolean aux = horarioTurma.getNrMaximoAluno().intValue() > horarioTurma.getNrAlunoMatriculado().intValue();
        if(aux)
            qtde = horarioTurma.getNrMaximoAluno() - horarioTurma.getNrAlunoMatriculado();
        else
            qtde = horarioTurma.getNrAlunoMatriculado();

        if (horarioTurma.getDiaSemana().equals("DM")) {
            horarioTurmaConcatenado.setDomingoCheio(aux);
            horarioTurmaConcatenado.setDomingo(qtde);
        } else

        if (horarioTurma.getDiaSemana().equals("SG")) {
            horarioTurmaConcatenado.setSegundaCheia(aux);
            horarioTurmaConcatenado.setSegunda(qtde);
        } else

        if (horarioTurma.getDiaSemana().equals("TR")) {
            horarioTurmaConcatenado.setTercaCheia(aux);
            horarioTurmaConcatenado.setTerca(qtde);
        } else

        if (horarioTurma.getDiaSemana().equals("QA")) {
            horarioTurmaConcatenado.setQuartaCheia(aux);
            horarioTurmaConcatenado.setQuarta(qtde);
        } else

        if (horarioTurma.getDiaSemana().equals("QI")) {
            horarioTurmaConcatenado.setQuintaCheia(aux);
            horarioTurmaConcatenado.setQuinta(qtde);
        } else

        if (horarioTurma.getDiaSemana().equals("SX")) {
            horarioTurmaConcatenado.setSextaCheia(aux);
            horarioTurmaConcatenado.setSexta(qtde);
        } else

        if (horarioTurma.getDiaSemana().equals("SB")) {
            horarioTurmaConcatenado.setSabadoCheio(aux);
            horarioTurmaConcatenado.setSabado(qtde);
        }
    }

    public void adicionarHorarioTurmaEmLista(HorarioTurmaConcatenadoVO obj, List lista) {
        int index = 0;
        Iterator i = lista.iterator();
        while (i.hasNext()) {
            HorarioTurmaConcatenadoVO objExistente = (HorarioTurmaConcatenadoVO) i.next();
            if (objExistente.getTurma().equals(obj.getTurma()) &&
                    objExistente.getContratoModalidadeHorarioTurma().getHorarioTurma().getDiaSemana().equals(obj.getContratoModalidadeHorarioTurma().getHorarioTurma().getDiaSemana()) &&
                    objExistente.getContratoModalidadeHorarioTurma().getHorarioTurma().getHoraInicial().equals(obj.getContratoModalidadeHorarioTurma().getHorarioTurma().getHoraInicial()) &&
                    objExistente.getContratoModalidadeHorarioTurma().getHorarioTurma().getHoraFinal().equals(obj.getContratoModalidadeHorarioTurma().getHorarioTurma().getHoraFinal()) &&
                    objExistente.getContratoModalidadeHorarioTurma().getHorarioTurma().getNivelTurma().getCodigo().equals(obj.getContratoModalidadeHorarioTurma().getHorarioTurma().getNivelTurma().getCodigo()) &&
                    objExistente.getContratoModalidadeHorarioTurma().getHorarioTurma().getAmbiente().getCodigo().equals(obj.getContratoModalidadeHorarioTurma().getHorarioTurma().getAmbiente().getCodigo()) &&
                    objExistente.getContratoModalidadeHorarioTurma().getHorarioTurma().getProfessor().getCodigo().equals(obj.getContratoModalidadeHorarioTurma().getHorarioTurma().getProfessor().getCodigo())) {
                lista.set(index, obj);
                return;
            }
            index++;
        }
        lista.add(obj);
    }

    public ContratoModalidadeHorarioTurmaVO getContratoModalidadeHorarioTurma() {
        return contratoModalidadeHorarioTurma;
    }

    public void setContratoModalidadeHorarioTurma(ContratoModalidadeHorarioTurmaVO contratoModalidadeHorarioTurma) {
        this.contratoModalidadeHorarioTurma = contratoModalidadeHorarioTurma;
    }

    public Integer getDomingo() {
        return domingo;
    }

    public void setDomingo(Integer domingo) {
        this.domingo = domingo;
    }

    public Integer getQuarta() {
        return quarta;
    }

    public void setQuarta(Integer quarta) {
        this.quarta = quarta;
    }

    public Integer getQuinta() {
        return quinta;
    }

    public void setQuinta(Integer quinta) {
        this.quinta = quinta;
    }

    public Integer getSabado() {
        return sabado;
    }

    public void setSabado(Integer sabado) {
        this.sabado = sabado;
    }

    public Integer getSegunda() {
        return segunda;
    }

    public void setSegunda(Integer segunda) {
        this.segunda = segunda;
    }

    public Integer getSexta() {
        return sexta;
    }

    public void setSexta(Integer sexta) {
        this.sexta = sexta;
    }

    public Integer getTerca() {
        return terca;
    }

    public void setTerca(Integer terca) {
        this.terca = terca;
    }

    public String getTurma() {
        if (turma == null) {
            turma = "";
        }
        return turma;
    }

    public void setTurma(String turma) {
        this.turma = turma;
    }

    public Boolean getDomingoEscolhido() {
        return domingoEscolhido;
    }

    public void setDomingoEscolhido(Boolean domingoEscolhido) {
        this.domingoEscolhido = domingoEscolhido;
    }

    public Boolean getQuartaEscolhido() {
        return quartaEscolhido;
    }

    public void setQuartaEscolhido(Boolean quartaEscolhido) {
        this.quartaEscolhido = quartaEscolhido;
    }

    public Boolean getQuintaEscolhido() {
        return quintaEscolhido;
    }

    public void setQuintaEscolhido(Boolean quintaEscolhido) {
        this.quintaEscolhido = quintaEscolhido;
    }

    public Boolean getSabadoEscolhido() {
        return sabadoEscolhido;
    }

    public void setSabadoEscolhido(Boolean sabadoEscolhido) {
        this.sabadoEscolhido = sabadoEscolhido;
    }

    public Boolean getSegundaEscolhido() {
        return segundaEscolhido;
    }

    public void setSegundaEscolhido(Boolean segundaEscolhido) {
        this.segundaEscolhido = segundaEscolhido;
    }

    public Boolean getSextaEscolhido() {
        return sextaEscolhido;
    }

    public void setSextaEscolhido(Boolean sextaEscolhido) {
        this.sextaEscolhido = sextaEscolhido;
    }

    public Boolean getTercaEscolhido() {
        return tercaEscolhido;
    }

    public void setTercaEscolhido(Boolean tercaEscolhido) {
        this.tercaEscolhido = tercaEscolhido;
    }

    public HorarioTurmaVO getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(HorarioTurmaVO horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public boolean isDomingoCheio() {
        return domingoCheio;
    }

    public void setDomingoCheio(boolean domingoCheio) {
        this.domingoCheio = domingoCheio;
    }

    public boolean isSegundaCheia() {
        return segundaCheia;
    }

    public void setSegundaCheia(boolean segundaCheia) {
        this.segundaCheia = segundaCheia;
    }

    public boolean isTercaCheia() {
        return tercaCheia;
    }

    public void setTercaCheia(boolean tercaCheia) {
        this.tercaCheia = tercaCheia;
    }

    public boolean isQuartaCheia() {
        return quartaCheia;
    }

    public void setQuartaCheia(boolean quartaCheia) {
        this.quartaCheia = quartaCheia;
    }

    public boolean isQuintaCheia() {
        return quintaCheia;
    }

    public void setQuintaCheia(boolean quintaCheia) {
        this.quintaCheia = quintaCheia;
    }

    public boolean isSextaCheia() {
        return sextaCheia;
    }

    public void setSextaCheia(boolean sextaCheia) {
        this.sextaCheia = sextaCheia;
    }

    public boolean isSabadoCheio() {
        return sabadoCheio;
    }

    public void setSabadoCheio(boolean sabadoCheio) {
        this.sabadoCheio = sabadoCheio;
    }

    public boolean isApresentarDomingo() {
        return getDomingo().intValue() > 0;
    }

    public boolean isApresentarSegunda() {
        return getSegunda().intValue() > 0;
    }

    public boolean isApresentarTerca() {
        return getTerca().intValue() > 0;
    }

    public boolean isApresentarQuarta() {
        return getQuarta().intValue() > 0;
    }

    public boolean isApresentarQuinta() {
        return getQuinta().intValue() > 0;
    }

    public boolean isApresentarSexta() {
        return getSexta().intValue() > 0;
    }
    
    public boolean isApresentarSabado() {
        return getSabado().intValue() > 0;
    }

	/**
	 * @return O campo escolhido.
	 */
	public boolean isEscolhido() {
		return this.escolhido;
	}

	/**
	 * @param escolhido O novo valor de escolhido.
	 */
	public void setEscolhido(boolean escolhido) {
		this.escolhido = escolhido;
	}
}
