package negocio.comuns.nfe;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;

/*
 * Created by <PERSON><PERSON> on 29/03/2017.
 */
public class FormaPagamentoNFCeVO {

    @ChavePrimaria
    private Integer id_NFCe_FormaPagamento;
    @ChaveEstrangeira
    private NotaFiscalConsumidorNFCeVO notaFiscalConsumidorNFCeVO;
    private String formaPagamento;
    private Double valor;

    public Integer getId_NFCe_FormaPagamento() {
        if (id_NFCe_FormaPagamento == null) {
            id_NFCe_FormaPagamento = 0;
        }
        return id_NFCe_FormaPagamento;
    }

    public void setId_NFCe_FormaPagamento(Integer id_NFCe_FormaPagamento) {
        this.id_NFCe_FormaPagamento = id_NFCe_FormaPagamento;
    }

    public NotaFiscalConsumidorNFCeVO getNotaFiscalConsumidorNFCeVO() {
        if (notaFiscalConsumidorNFCeVO == null) {
            notaFiscalConsumidorNFCeVO = new NotaFiscalConsumidorNFCeVO();
        }
        return notaFiscalConsumidorNFCeVO;
    }

    public void setNotaFiscalConsumidorNFCeVO(NotaFiscalConsumidorNFCeVO notaFiscalConsumidorNFCeVO) {
        this.notaFiscalConsumidorNFCeVO = notaFiscalConsumidorNFCeVO;
    }

    public String getFormaPagamento() {
        if (formaPagamento == null) {
            formaPagamento = "";
        }
        return formaPagamento;
    }

    public void setFormaPagamento(String formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public Double getValor() {
        if (valor == null) {
            valor = 0.0;
        }
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }
}
