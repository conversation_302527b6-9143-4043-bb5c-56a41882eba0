/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.conviteaulaexperimental;

import br.com.pacto.priv.utils.Uteis;
import java.util.Date;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class ConviteAulaExperimentalBITO extends SuperTO{
    
    public Integer alunosSemConsultor = 0;
    public Integer convitesEnviados = 0;
    public Integer convitesValidados = 0;
    public Integer agendaramAula = 0;
    public Integer agendaramCompareceram = 0;
    public Integer conversaoVendas = 0;
    public Integer compraramContrato = 0;
    public String dia;
    
    public String convite;
    public String convidado;
    public String clienteConvidou;
    public String usuarioConvidou;
    public Date dataLancamento;
    public Date dataValidacao;
    public Date dataPresenca;
    
    public Boolean agendou;
    public Boolean compareceu;
    
    public String getQuemConvidou(){
        return UteisValidacao.emptyString(clienteConvidou) ? 
                usuarioConvidou : clienteConvidou;
    }
    
    public String getStatus(){
        return !agendou ? "Não agendou."
                : (compareceu ? "Agendou e compareceu." : "Agendou.");
    }

    public Integer getAlunosSemConsultor() {
        return alunosSemConsultor;
    }

    public void setAlunosSemConsultor(Integer alunosSemConsultor) {
        this.alunosSemConsultor = alunosSemConsultor;
    }

    public Integer getConvitesEnviados() {
        return convitesEnviados;
    }

    public void setConvitesEnviados(Integer convitesEnviados) {
        this.convitesEnviados = convitesEnviados;
    }

    public Integer getConvitesValidados() {
        return convitesValidados;
    }

    public void setConvitesValidados(Integer convitesValidados) {
        this.convitesValidados = convitesValidados;
    }

    public Integer getAgendaramAula() {
        return agendaramAula;
    }

    public void setAgendaramAula(Integer agendaramAula) {
        this.agendaramAula = agendaramAula;
    }

    public Integer getAgendaramCompareceram() {
        return agendaramCompareceram;
    }

    public void setAgendaramCompareceram(Integer agendaramCompareceram) {
        this.agendaramCompareceram = agendaramCompareceram;
    }

    public Integer getConversaoVendas() {
        return conversaoVendas;
    }

    public void setConversaoVendas(Integer conversaoVendas) {
        this.conversaoVendas = conversaoVendas;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public Integer getCompraramContrato() {
        return compraramContrato;
    }

    public void setCompraramContrato(Integer compraramContrato) {
        this.compraramContrato = compraramContrato;
    }

    public String getConvidado() {
        return convidado;
    }

    public void setConvidado(String convidado) {
        this.convidado = convidado;
    }

    public String getClienteConvidou() {
        return clienteConvidou;
    }

    public void setClienteConvidou(String clienteConvidou) {
        this.clienteConvidou = clienteConvidou;
    }

    public String getUsuarioConvidou() {
        return usuarioConvidou;
    }

    public void setUsuarioConvidou(String usuarioConvidou) {
        this.usuarioConvidou = usuarioConvidou;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public String getDataLancamentoApresentar() {
        return Uteis.getData(dataLancamento);
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataValidacao() {
        return dataValidacao;
    }
    
    public String getDataValidacaoApresentar() {
        return Uteis.getData(dataValidacao);
    }

    public void setDataValidacao(Date dataValidacao) {
        this.dataValidacao = dataValidacao;
    }

    public Date getDataPresenca() {
        return dataPresenca;
    }

    public String getDataPresencaApresentar() {
        return Uteis.getData(dataPresenca);
    }

    public void setDataPresenca(Date dataPresenca) {
        this.dataPresenca = dataPresenca;
    }

    public Boolean getAgendou() {
        return agendou;
    }

    public void setAgendou(Boolean agendou) {
        this.agendou = agendou;
    }

    public Boolean getCompareceu() {
        return compareceu;
    }

    public void setCompareceu(Boolean compareceu) {
        this.compareceu = compareceu;
    }

    public String getConvite() {
        return convite;
    }

    public void setConvite(String convite) {
        this.convite = convite;
    }
    
}
