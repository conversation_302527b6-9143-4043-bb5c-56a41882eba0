package negocio.comuns.plano;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.FKJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.spivi.EventJSON;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.AulaDesmarcadaVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.plano.Turma;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Reponsável por manter os dados da entidade HorarioTurma. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 * @see Turma
 */
public class HorarioTurmaVO extends SuperVO implements Cloneable {

    @ChavePrimaria
    protected Integer codigo;
    protected Integer turma;
    protected Integer toleranciaEntradaMinutos = 0;
    protected Integer toleranciaEntradaAposMinutos = 0;
    protected String horaInicial;
    protected String horaFinal;
    protected String situacao;
    protected String diaSemana;
    protected String identificadorTurma;
    protected Integer diaSemanaNumero;
    protected Integer nrMaximoAluno;
    protected Integer limiteVagasAgregados;
    protected Integer nrAlunoMatriculado;
    protected Integer nrAlunoReposicao = 0;
    @NaoControlarLogAlteracao
    protected Integer nrAlunoEntraramPorReposicao = 0;//-x para número de vagas de reposição
    @NaoControlarLogAlteracao
    protected Integer nrAlunoSairamPorReposicao = 0;//+x para número de vagas de reposição
    protected Boolean horarioTurmaEscolhida;
    @ChaveEstrangeira
    @FKJson
    protected ColaboradorVO professor;
    @ChaveEstrangeira
    @FKJson
    protected AmbienteVO ambiente;
    @ChaveEstrangeira
    @FKJson
    protected NivelTurmaVO nivelTurma;
    @NaoControlarLogAlteracao
    private boolean botaoEditar;
    private double txOcupacao;
    private double freqMedia;
    @NaoControlarLogAlteracao
    private int professorAnterior; // guarda o codigo do professor quando for edicao
    @NaoControlarLogAlteracao
    private String ordenaDiaSemana;
    @NaoControlarLogAlteracao
    private String ordenaHorario;
    @NaoControlarLogAlteracao
    private String ordenaAmbiente;
    @NaoControlarLogAlteracao
    private String ordenaProfessor;
    @NaoControlarLogAlteracao
    @FKJson
    private AulaDesmarcadaVO aulaDesmarcadaVO;
    @NaoControlarLogAlteracao
    private Integer codigoAulaCheia;
    private boolean advertenciaAcimaLimite = false;      //caso a turma esteja acima do limite, mas isso seja permitido
    private boolean obstrucaoConclusaoManutencao = false; //caso a turma não permita matricula acima do limite, e ela esteja acima(retorno trancamento)
    private Integer nrMatriculasFuturas = 0;
    private String msgMatriculasFuturas = "";
    @NaoControlarLogAlteracao
    private Date dataAula; // atributo transient.
    @NaoControlarLogAlteracao
    private Integer minutosAntecedenciaDesmarcarAula = 0;// atributo transient.
    private Integer spiviEventID;
    private EventJSON spiviEvent;

    private ContratoVO contratoVO; // atributo transiente

    private boolean liberadoMarcacaoApp = true;

    @NaoControlarLogAlteracao
    private boolean horarioSelecionado = false;

    private boolean ativo =true;
    private Date dataEntrouTurma;
    private Date dataSaiuTurma;

    protected Integer nrAlunoMatriculadosFuturo = 0;
    private String publicIdTurmaMgb;

    @NaoControlarLogAlteracao
    private String tipoCategoriaCliente;
    @NaoControlarLogAlteracao
    private boolean capacidadePorCategoria;

    public HorarioTurmaVO() {
        super();
        inicializarDados();
    }

    public static void validarDados(HorarioTurmaVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getDiaSemana() == null || obj.getDiaSemana().equals("")) {
            throw new ConsistirException("O campo Dia Semana (Horário Turma) deve ser informado.");
        }
        if (obj.getDiaSemana() != null || !obj.getDiaSemana().equals("")) {
            montarDadosDiaSemanaNumero(obj);
        }
        if (obj.getToleranciaEntradaMinutos() == null) {
            obj.setToleranciaEntradaMinutos(0);
        }
        if (obj.getToleranciaEntradaAposMinutos() == null) {
            obj.setToleranciaEntradaAposMinutos(0);
        }
        if (obj.getNrMaximoAluno() == null) {
            obj.setNrMaximoAluno(0);
        }
        if (obj.getLimiteVagasAgregados() == null) {
            obj.setLimiteVagasAgregados(0);
        }
        if (obj.getHoraInicial().equals("")) {
            throw new ConsistirException("O campo HORA INICIAL (Horário Turma) deve ser informado.");
        }
        if (obj.getHoraFinal().equals("")) {
            throw new ConsistirException("O campo HORA FINAL (Horário Turma) deve ser informado.");
        }
        if ((obj.getProfessor() == null)
                || (obj.getProfessor().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo PROFESSOR (Horário Turma) deve ser informado.");
        }
        if ((obj.getAmbiente() == null)
                || (obj.getAmbiente().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo AMBIENTE (Horário Turma) deve ser informado.");
        }
        if ((obj.getNivelTurma() == null)
                || (obj.getNivelTurma().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo NÍVEL DA TURMA (Horário Turma) deve ser informado.");
        }
        if (obj.getSituacao() == null || obj.getSituacao().equals("")) {
            throw new ConsistirException("O campo SITUAÇÃO (Horário Turma) deve ser informado.");
        }
    }

    public void realizarUpperCaseDados() {
    }

    public void inicializarDados() {
        setCodigo(new Integer(0));
        setHoraInicial("");
        setHoraFinal("");
        setSituacao("AT");
        setDiaSemana("");
        setDiaSemanaNumero(new Integer(0));
        setNrMaximoAluno(new Integer(0));
        setLimiteVagasAgregados(new Integer(0));
        setNrAlunoMatriculado(new Integer(0));
        setProfessor(new ColaboradorVO());
        setAmbiente(new AmbienteVO());
        setNivelTurma(new NivelTurmaVO());
        setHorarioTurmaEscolhida(false);
        setBotaoEditar(false);
        setProfessorAnterior(0);
        setAulaDesmarcadaVO(new AulaDesmarcadaVO());
        setAtivo(true);
    }

    public NivelTurmaVO getNivelTurma() {
        if (nivelTurma == null) {
            nivelTurma = new NivelTurmaVO();
        }
        return (nivelTurma);
    }

    public void setNivelTurma(NivelTurmaVO obj) {
        this.nivelTurma = obj;
    }

    public AmbienteVO getAmbiente() {
        if (ambiente == null) {
            ambiente = new AmbienteVO();
        }
        return (ambiente);
    }

    public void setAmbiente(AmbienteVO obj) {
        this.ambiente = obj;
    }

    public ColaboradorVO getProfessor() {
        if (professor == null) {
            professor = new ColaboradorVO();
        }
        return (professor);
    }

    public void setProfessor(ColaboradorVO obj) {
        this.professor = obj;
        if (professorAnterior == 0) {
            professorAnterior = professor.getCodigo();
        }
    }

    public Integer getNrMaximoAluno() {
        return (nrMaximoAluno);
    }

    public void setNrMaximoAluno(Integer nrMaximoAluno) {
        this.nrMaximoAluno = nrMaximoAluno;
    }

    public Integer getLimiteVagasAgregados() {
        return limiteVagasAgregados;
    }

    public void setLimiteVagasAgregados(Integer limiteVagasAgregados) {
        this.limiteVagasAgregados = limiteVagasAgregados;
    }

    public String getSituacao() {
        if (situacao == null) {
            situacao = "";
        }
        return (situacao);
    }

    public String getSituacao_Apresentar() {
        if (situacao == null) {
            situacao = "";
        }
        if (situacao.equals("IN")) {
            return "Inativa";
        }
        if (situacao.equals("AT")) {
            return "Ativa";
        }
        return (situacao);
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getHoraFinal() {
        if (horaFinal == null) {
            horaFinal = "";
        }
        return (horaFinal);
    }

    public void setHoraFinal(String horaFinal) {
        this.horaFinal = horaFinal;
    }

    public String getHoraInicial() {
        if (horaInicial == null) {
            horaInicial = "";
        }
        return (horaInicial);
    }

    public String getHoraInicialComTolerancia() {
        Integer tolerancia = this.getToleranciaEntradaMinutos() == null ? 0
                : this.getToleranciaEntradaMinutos();
        String hrInicial = horaInicial;
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        if (tolerancia > 0) {
            try {
                Date d1 = sdf.parse(horaInicial);
                Calendar calI = Calendario.getInstance();
                calI.setTime(d1);

                Calendar cal = Calendario.getInstance();
                cal.setTime(d1);
                cal.add(Calendar.MINUTE, tolerancia * (-1));

                if (cal.get(Calendar.DAY_OF_YEAR) != calI.get(Calendar.DAY_OF_YEAR)) {
                    return "00:00";
                }
                hrInicial = sdf.format(cal.getTime());
            } catch (ParseException ex) {
                Logger.getLogger(HorarioTurmaVO.class.getName()).log(Level.SEVERE,
                        ex.getMessage(), ex);
            }
        }
        return hrInicial;
    }

    public String getHoraInicialComToleranciaApos() {
        Integer tolerancia = this.getToleranciaEntradaAposMinutos() == null ? 0
                : this.getToleranciaEntradaAposMinutos();
        String hrInicialApos = horaFinal;
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        if (tolerancia > 0) {
            try {
                Date d1 = sdf.parse(horaInicial);
                Calendar calI = Calendario.getInstance();
                calI.setTime(d1);

                Calendar cal = Calendario.getInstance();
                cal.setTime(d1);
                cal.add(Calendar.MINUTE, tolerancia);

                if (cal.get(Calendar.DAY_OF_YEAR) != calI.get(Calendar.DAY_OF_YEAR)) {
                    return "00:00";
                }
                hrInicialApos = sdf.format(cal.getTime());
            } catch (ParseException ex) {
                Logger.getLogger(HorarioTurmaVO.class.getName()).log(Level.SEVERE,
                        ex.getMessage(), ex);
            }
        }
        return hrInicialApos;
    }

    public void setHoraInicial(String horaInicial) {
        this.horaInicial = horaInicial;
    }

    public Integer getTurma() {
        if (turma == null) {
            turma = 0;
        }
        return (turma);
    }

    public void setTurma(Integer turma) {
        this.turma = turma;
    }

    @Override
    public Integer getCodigo() {
        return (codigo);
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getHorarioTurmaEscolhida() {
        return horarioTurmaEscolhida;
    }

    public void setHorarioTurmaEscolhida(Boolean horarioTurmaEscolhida) {
        this.horarioTurmaEscolhida = horarioTurmaEscolhida;
    }

    public String getDiaSemana() {
        if (diaSemana == null) {
            diaSemana = "";
        }
        return diaSemana;
    }


    public Integer diaSemanaNumero(){
        //Não foi feito com Date porque existem muitos locais no sistema que utilizam essa abreviação
        HashMap<String, Integer> numeros = new HashMap<>();
        numeros.put("DM", 1);
        numeros.put("SG", 2);
        numeros.put("TR", 3);
        numeros.put("QA", 4);
        numeros.put("QI", 5);
        numeros.put("SX", 6);
        numeros.put("SB", 7);
        return numeros.get(this.diaSemana);
    }

    public String getDiaSemanaInvert_Apresentar() {
        if (diaSemana == null) {
            diaSemana = "";
        }
        if (diaSemana.equals("Domingo")) {
            return "DM";
        }
        if (diaSemana.equals("Segunda")) {
            return "SG";
        }
        if (diaSemana.equals("Terça") || diaSemana.contains("Ter")) {
            return "TR";
        }
        if (diaSemana.equals("Quarta")) {
            return "QA";
        }
        if (diaSemana.equals("Quinta")) {
            return "QI";
        }
        if (diaSemana.equals("Sexta")) {
            return "SX";
        }
        if (diaSemana.equals("Sábado") || diaSemana.contains("bado")) {
            return "SB";
        }
        return (diaSemana);
    }

    public String getDiaSemana_Apresentar() {
        if (diaSemana == null) {
            diaSemana = "";
        }
        if (diaSemana.equals("DM")) {
            return "Domingo";
        }
        if (diaSemana.equals("SG")) {
            return "Segunda";
        }
        if (diaSemana.equals("TR")) {
            return "Terça";
        }
        if (diaSemana.equals("QA")) {
            return "Quarta";
        }
        if (diaSemana.equals("QI")) {
            return "Quinta";
        }
        if (diaSemana.equals("SX")) {
            return "Sexta";
        }
        if (diaSemana.equals("SB")) {
            return "Sábado";
        }
        return (diaSemana);
    }
    public String getDiaSemanaAbreviado_Apresentar() {
        if (diaSemana == null) {
            diaSemana = "";
        }
        if (diaSemana.equals("DM")) {
            return "Dom";
        }
        if (diaSemana.equals("SG")) {
            return "Seg";
        }
        if (diaSemana.equals("TR")) {
            return "Ter";
        }
        if (diaSemana.equals("QA")) {
            return "Qua";
        }
        if (diaSemana.equals("QI")) {
            return "Qui";
        }
        if (diaSemana.equals("SX")) {
            return "Sex";
        }
        if (diaSemana.equals("SB")) {
            return "Sáb";
        }
        return (diaSemana);
    }
    public static void montarDadosDiaSemanaNumero(HorarioTurmaVO obj) {
        if (obj.getDiaSemana().equals("DM")) {
            obj.setDiaSemanaNumero(1);
        }
        if (obj.getDiaSemana().equals("SG")) {
            obj.setDiaSemanaNumero(2);
        }
        if (obj.getDiaSemana().equals("TR")) {
            obj.setDiaSemanaNumero(3);
        }
        if (obj.getDiaSemana().equals("QA")) {
            obj.setDiaSemanaNumero(4);
        }
        if (obj.getDiaSemana().equals("QI")) {
            obj.setDiaSemanaNumero(5);
        }
        if (obj.getDiaSemana().equals("SX")) {
            obj.setDiaSemanaNumero(6);
        }
        if (obj.getDiaSemana().equals("SB")) {
            obj.setDiaSemanaNumero(7);
        }
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    @Override
    public HorarioTurmaVO clone() throws CloneNotSupportedException {
        HorarioTurmaVO obj = (HorarioTurmaVO) super.clone();
        obj.setNivelTurma(this.getNivelTurma().clone());
        obj.setAmbiente(this.getAmbiente().clone());
        return obj;
    }

    public Integer getNrAlunoMatriculado() {
        return nrAlunoMatriculado;
    }

    public void setNrAlunoMatriculado(Integer nrAlunoMatriculado) {
        this.nrAlunoMatriculado = nrAlunoMatriculado;
    }

    public Integer getDiaSemanaNumero() {
        if (UteisValidacao.emptyNumber(diaSemanaNumero) && !UteisValidacao.emptyString(getDiaSemana())) {
            montarDadosDiaSemanaNumero(this);
        }
        return diaSemanaNumero;
    }

    public void setDiaSemanaNumero(Integer diaSemanaNumero) {
        this.diaSemanaNumero = diaSemanaNumero;
    }

    public String getIdentificadorTurma() {
        if (identificadorTurma == null) {
            identificadorTurma = "";
        }
        return identificadorTurma;
    }

    public void setIdentificadorTurma(String identificadorTurma) {
        this.identificadorTurma = identificadorTurma;
    }

    public boolean isBotaoEditar() {
        return botaoEditar;
    }

    public void setBotaoEditar(boolean botaoEditar) {
        this.botaoEditar = botaoEditar;
    }

    public void setToleranciaEntradaMinutos(Integer toleranciaEntradaMinutos) {
        this.toleranciaEntradaMinutos = toleranciaEntradaMinutos;
    }

    public Integer getToleranciaEntradaMinutos() {
        return toleranciaEntradaMinutos;
    }

    public void setToleranciaEntradaAposMinutos(Integer toleranciaEntradaAposMinutos) {
        this.toleranciaEntradaAposMinutos = toleranciaEntradaAposMinutos;
    }

    public Integer getToleranciaEntradaAposMinutos() {
        return toleranciaEntradaAposMinutos;
    }

    public int getQuantidadeVagas() {
        return nrMaximoAluno - nrAlunoMatriculado;
    }
    public int getQuantidadeVagasDisponiveis(){
        return (nrMaximoAluno - nrAlunoMatriculado) - (horarioTurmaEscolhida ? 1 : 0);
    }

    public double getTxOcupacao() {
        return txOcupacao;
    }

    public void setTxOcupacao(double txOcupacao) {
        this.txOcupacao = txOcupacao;
    }

    public double getFreqMedia() {
        return freqMedia;
    }

    public void setFreqMedia(double freqMedia) {
        this.freqMedia = freqMedia;
    }

    public int getProfessorAnterior() {
        return professorAnterior;
    }

    public void setProfessorAnterior(int professorAnterior) {
        this.professorAnterior = professorAnterior;
    }

    @Override
    public String toString() {
        return String.valueOf(this.diaSemanaNumero)/* + this.diaSemana + this.horaInicial + "/"+this.horaFinal*/;
    }

    public Integer getNrAlunoReposicao() {
        return nrAlunoReposicao;
    }

    public void setNrAlunoReposicao(Integer nrAlunoReposicao) {
        this.nrAlunoReposicao = nrAlunoReposicao;
    }

    public Integer getNrAlunoEntraramPorReposicao() {
        return nrAlunoEntraramPorReposicao;
    }

    public void setNrAlunoEntraramPorReposicao(Integer nrAlunoEntraramPorReposicao) {
        this.nrAlunoEntraramPorReposicao = nrAlunoEntraramPorReposicao;
    }

    public Integer getNrAlunoSairamPorReposicao() {
        return nrAlunoSairamPorReposicao;
    }

    public void setNrAlunoSairamPorReposicao(Integer nrAlunoSairamPorReposicao) {
        this.nrAlunoSairamPorReposicao = nrAlunoSairamPorReposicao;
    }

    public String getOrdenaDiaSemana() {
        return getDiaSemanaNumero() + getHoraInicial()+getHoraFinal() + getProfessor().getPessoa_Apresentar() +getNivelTurma().getDescricao()+ getAmbiente().getDescricao();
    }
    public String getOrdenaHorario()
    {
        return  getHoraInicial()+getHoraFinal()+ getDiaSemanaNumero()  + getProfessor().getPessoa_Apresentar() +getNivelTurma().getDescricao()+ getAmbiente().getDescricao();
    }
    public String getOrdenaAmbiente(){
        return  getAmbiente().getDescricao() + getHoraInicial()+getHoraFinal()+ getDiaSemanaNumero()  + getProfessor().getPessoa_Apresentar() +getNivelTurma().getDescricao();
    }
   public String getOrdenaProfessor(){
       return  getProfessor().getPessoa_Apresentar()+ getHoraInicial()+getHoraFinal()+ getDiaSemanaNumero()  +getNivelTurma().getDescricao()+getAmbiente().getDescricao() ;
   }

   public AulaDesmarcadaVO getAulaDesmarcadaVO() {
        return aulaDesmarcadaVO;
   }

   public void setAulaDesmarcadaVO(AulaDesmarcadaVO aulaDesmarcadaVO) {
        this.aulaDesmarcadaVO = aulaDesmarcadaVO;
   }


    public ContratoVO getContratoVO() {
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public Integer getCodigoAulaCheia() {
        return codigoAulaCheia;
    }

    public void setCodigoAulaCheia(Integer codigoAulaCheia) {
        this.codigoAulaCheia = codigoAulaCheia;
    }

    public Integer getSpiviEventID() {
        return spiviEventID;
    }

    public void setSpiviEventID(Integer spiviEventID) {
        this.spiviEventID = spiviEventID;
    }

    public boolean estahLotada(boolean desconsiderarAlunoAtual) {
        if(desconsiderarAlunoAtual){
            return getQuantidadeVagas()+1 == 0;
        }
        return getQuantidadeVagas() == 0;
    }

    public boolean isAdvertenciaAcimaLimite() {
        return advertenciaAcimaLimite;
    }

    public void setAdvertenciaAcimaLimite(boolean advertenciaAcimaLimite) {
        this.advertenciaAcimaLimite = advertenciaAcimaLimite;
    }

    public boolean isObstrucaoConclusaoManutencao() {
        return obstrucaoConclusaoManutencao;
    }

    public void setObstrucaoConclusaoManutencao(boolean obstrucaoConclusaoManutencao) {
        this.obstrucaoConclusaoManutencao = obstrucaoConclusaoManutencao;
    }
    public Integer getNrMatriculasFuturas() {
        return nrMatriculasFuturas;
    }

    public void setNrMatriculasFuturas(Integer nrMatriculasFuturas) {
        this.nrMatriculasFuturas = nrMatriculasFuturas;
    }

    public String getMsgMatriculasFuturas() {
        return msgMatriculasFuturas;
    }

    public void setMsgMatriculasFuturas(String msgMatriculasFuturas) {
        this.msgMatriculasFuturas = msgMatriculasFuturas;
    }

    public Date getDataAula() {
        return dataAula;
    }

    public void setDataAula(Date dataAula) {
        this.dataAula = dataAula;
    }

    @Override
    public int hashCode(){
       if (this.turma != null)
         return this.turma.hashCode();
       return 0;
    }

    @Override
    public boolean equals(Object obj){
        if ((obj == null) || (!(obj instanceof HorarioTurmaVO))){
            return false;
        }

        boolean igual = ((HorarioTurmaVO)obj).getCodigo().equals(this.codigo);
        if (igual){
            if (this.getDataAula() != null){
                igual = Calendario.igualComHora(((HorarioTurmaVO)obj).getDataAula(), this.dataAula);
            }
        }
        return igual;

    }

    public static Comparator COMPARATOR_DATA_AULA = new Comparator() {
        public int compare(Object o1, Object o2) {
            HorarioTurmaVO p1 = (HorarioTurmaVO) o1;
            HorarioTurmaVO p2 = (HorarioTurmaVO) o2;
            return p1.getDataAula().compareTo(p2.getDataAula());
        }
    };
    
    public Integer getMinutosAntecedenciaDesmarcarAula() {
        return minutosAntecedenciaDesmarcarAula;
    }

    public void setMinutosAntecedenciaDesmarcarAula(Integer minutosantecedenciadesmarcaraula) {
        this.minutosAntecedenciaDesmarcarAula = minutosantecedenciadesmarcaraula;
    }

    public double getPercOcupacao(){
        return Uteis.arredondarForcando2CasasDecimais(nrMaximoAluno > 0 ? ((double)nrAlunoMatriculado/nrMaximoAluno)*100 : 0);
    }

    public boolean isLiberadoMarcacaoApp() {
        return liberadoMarcacaoApp;
    }

    public void setLiberadoMarcacaoApp(boolean liberadoMarcacaoApp) {
        this.liberadoMarcacaoApp = liberadoMarcacaoApp;
    }

    public boolean isHorarioSelecionado() {
        return horarioSelecionado;
    }

    public void setHorarioSelecionado(boolean horarioSelecionado) {
        this.horarioSelecionado = horarioSelecionado;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public EventJSON getSpiviEvent() {
        return spiviEvent;
    }

    public void setSpiviEvent(EventJSON spiviEvent) {
        this.spiviEvent = spiviEvent;
    }

    public Date getDataEntrouTurma() {
        return dataEntrouTurma;
    }

    public void setDataEntrouTurma(Date dataEntrouTurma) {
        this.dataEntrouTurma = dataEntrouTurma;
    }

    public Date getDataSaiuTurma() {
        return dataSaiuTurma;
    }

    public void setDataSaiuTurma(Date dataSaiuTurma) {
        this.dataSaiuTurma = dataSaiuTurma;
    }

    public Integer getNrAlunoMatriculadosFuturo() {
        return nrAlunoMatriculadosFuturo;
    }

    public void setNrAlunoMatriculadosFuturo(Integer nrAlunoMatriculadosFuturo) {
        this.nrAlunoMatriculadosFuturo = nrAlunoMatriculadosFuturo;
    }

    public String getPublicIdTurmaMgb() {
        return publicIdTurmaMgb;
    }

    public void setPublicIdTurmaMgb(String publicIdTurmaMgb) {
        this.publicIdTurmaMgb = publicIdTurmaMgb;
    }

    public String getTipoCategoriaCliente() {
        if (tipoCategoriaCliente == null) {
            return "";
        }
        return tipoCategoriaCliente;
    }

    public void setTipoCategoriaCliente(String tipoCategoriaCliente) {
        this.tipoCategoriaCliente = tipoCategoriaCliente;
    }

    public boolean isCapacidadePorCategoria() {
        return capacidadePorCategoria;
    }

    public void setCapacidadePorCategoria(boolean capacidadePorCategoria) {
        this.capacidadePorCategoria = capacidadePorCategoria;
    }
}
