package negocio.comuns.plano;

import br.com.pactosolucoes.comuns.util.Formatador;

public class PlanoAnuidadeParcelaWS {

    private Integer numero;
    private Double valor;
    private String valorApresentar;
    private Integer parcela;
    private String parcelaApresentar;

    public PlanoAnuidadeParcelaWS(PlanoAnuidadeParcelaVO obj) {
        this.numero = obj.getNumero();
        this.valor = obj.getValor();
        this.valorApresentar = Formatador.formatarValorMonetario(obj.getValor());
        this.parcela = obj.getParcela();
        this.parcelaApresentar = obj.getParcelaApresentar();
    }

    public Integer getNumero() {
        return numero;
    }

    public void setNumero(Integer numero) {
        this.numero = numero;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getValorApresentar() {
        return valorApresentar;
    }

    public void setValorApresentar(String valorApresentar) {
        this.valorApresentar = valorApresentar;
    }

    public Integer getParcela() {
        return parcela;
    }

    public void setParcela(Integer parcela) {
        this.parcela = parcela;
    }

    public String getParcelaApresentar() {
        return parcelaApresentar;
    }

    public void setParcelaApresentar(String parcelaApresentar) {
        this.parcelaApresentar = parcelaApresentar;
    }
}
