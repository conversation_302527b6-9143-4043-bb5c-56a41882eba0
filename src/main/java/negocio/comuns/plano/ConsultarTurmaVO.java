/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.plano;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 *
 * <AUTHOR>
 */
public class ConsultarTurmaVO extends SuperVO {

    protected Boolean domingo;
    protected Boolean segunda;
    protected Boolean terca;
    protected Boolean quarta;
    protected Boolean quinta;
    protected Boolean sexta;
    protected Boolean sabado;
    protected Boolean h0001as0200;
    protected Boolean h0201as0400;
    protected Boolean h0401as0600;
    protected Boolean h0601as0800;
    protected Boolean h0801as1000;
    protected Boolean h1001as1200;
    protected Boolean h1201as1400;
    protected Boolean h1401as1600;
    protected Boolean h1601as1800;
    protected Boolean h1801as2000;
    protected Boolean h2001as2200;
    protected Boolean h2201as0000;
    protected String horarioTurmaEscolhido;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Modalidade </code>.*/
    protected ModalidadeVO modalidade;
    protected List listaModalidade;
    protected ColaboradorVO professor;
    protected TurmaVO turma;
    protected List listaTurmaVOs;
    protected List listaHorario;
    protected List listaDiaSemana;
    protected EmpresaVO empresaVO;

    public ConsultarTurmaVO() {
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>TurmaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(ConsultarTurmaVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getEmpresaVO() == null || obj.getEmpresaVO().getCodigo().intValue() == 0) {
            throw new ConsistirException("O campo EMPRESA (Consultar Turma) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setModalidade(new ModalidadeVO());
        setTurma(new TurmaVO());
        setEmpresaVO(new EmpresaVO());
        setProfessor(new ColaboradorVO());
        setDomingo(new Boolean(false));
        setSegunda(new Boolean(false));
        setTerca(new Boolean(false));
        setQuarta(new Boolean(false));
        setQuinta(new Boolean(false));
        setSexta(new Boolean(false));
        setSabado(new Boolean(false));
        setH0001as0200(new Boolean(false));
        setH0201as0400(new Boolean(false));
        setH0401as0600(new Boolean(false));
        setH0601as0800(new Boolean(false));
        setH0801as1000(new Boolean(false));
        setH1001as1200(new Boolean(false));
        setH1201as1400(new Boolean(false));
        setH1401as1600(new Boolean(false));
        setH1601as1800(new Boolean(false));
        setH1801as2000(new Boolean(false));
        setH2001as2200(new Boolean(false));
        setH2201as0000(new Boolean(false));
        setHorarioTurmaEscolhido("");
        setListaTurmaVOs(new ArrayList());
        setListaModalidade(new ArrayList());
        setListaDiaSemana(new ArrayList());
        setListaHorario(new ArrayList());

    }

    public void adicionarObjDiaSemana(String diaSemana) {
        getListaDiaSemana().add(diaSemana);
    }

    public void removerObjDiaSemana(String diaSemana) {
        int index = 0;
        Iterator i = getListaDiaSemana().iterator();
        while (i.hasNext()) {
            String objExistente = (String) i.next();
            if (objExistente.equals(diaSemana)) {
                getListaDiaSemana().remove(index);
                return;
            }
            index++;
        }
    }

    public void adicionarObjHorario(String horario) {
        getListaHorario().add(horario);
    }

    public void removerObjHorario(String horario) {
        int index = 0;
        Iterator i = getListaHorario().iterator();
        while (i.hasNext()) {
            String objExistent = (String) i.next();
            if (objExistent.equals(horario)) {
                getListaHorario().remove(index);
                return;
            }
            index++;
        }
    }

    public Boolean getDomingo() {
        return domingo;
    }

    public void setDomingo(Boolean domingo) {
        this.domingo = domingo;
    }

    public Boolean getH0001as0200() {
        return h0001as0200;
    }

    public void setH0001as0200(Boolean h0001as0200) {
        this.h0001as0200 = h0001as0200;
    }

    public Boolean getH0201as0400() {
        return h0201as0400;
    }

    public void setH0201as0400(Boolean h0201as0400) {
        this.h0201as0400 = h0201as0400;
    }

    public Boolean getH0401as0600() {
        return h0401as0600;
    }

    public void setH0401as0600(Boolean h0401as0600) {
        this.h0401as0600 = h0401as0600;
    }

    public Boolean getH0601as0800() {
        return h0601as0800;
    }

    public void setH0601as0800(Boolean h0601as0800) {
        this.h0601as0800 = h0601as0800;
    }

    public Boolean getH0801as1000() {
        return h0801as1000;
    }

    public void setH0801as1000(Boolean h0801as1000) {
        this.h0801as1000 = h0801as1000;
    }

    public Boolean getH1001as1200() {
        return h1001as1200;
    }

    public void setH1001as1200(Boolean h1001as1200) {
        this.h1001as1200 = h1001as1200;
    }

    public Boolean getH1201as1400() {
        return h1201as1400;
    }

    public void setH1201as1400(Boolean h1201as1400) {
        this.h1201as1400 = h1201as1400;
    }

    public Boolean getH1401as1600() {
        return h1401as1600;
    }

    public void setH1401as1600(Boolean h1401as1600) {
        this.h1401as1600 = h1401as1600;
    }

    public Boolean getH1601as1800() {
        return h1601as1800;
    }

    public void setH1601as1800(Boolean h1601as1800) {
        this.h1601as1800 = h1601as1800;
    }

    public Boolean getH1801as2000() {
        return h1801as2000;
    }

    public void setH1801as2000(Boolean h1801as2000) {
        this.h1801as2000 = h1801as2000;
    }

    public Boolean getH2001as2200() {
        return h2001as2200;
    }

    public void setH2001as2200(Boolean h2001as2200) {
        this.h2001as2200 = h2001as2200;
    }

    public Boolean getH2201as0000() {
        return h2201as0000;
    }

    public void setH2201as0000(Boolean h2201as0000) {
        this.h2201as0000 = h2201as0000;
    }

    public String getHorarioTurmaEscolhido() {
        return horarioTurmaEscolhido;
    }

    public void setHorarioTurmaEscolhido(String horarioTurmaEscolhido) {
        this.horarioTurmaEscolhido = horarioTurmaEscolhido;
    }

    public List getListaTurmaVOs() {
        return listaTurmaVOs;
    }

    public void setListaTurmaVOs(List listaTurmaVOs) {
        this.listaTurmaVOs = listaTurmaVOs;
    }

    public ModalidadeVO getModalidade() {
        return modalidade;
    }

    public void setModalidade(ModalidadeVO modalidade) {
        this.modalidade = modalidade;
    }

    public Boolean getQuarta() {
        return quarta;
    }

    public void setQuarta(Boolean quarta) {
        this.quarta = quarta;
    }

    public Boolean getQuinta() {
        return quinta;
    }

    public void setQuinta(Boolean quinta) {
        this.quinta = quinta;
    }

    public Boolean getSabado() {
        return sabado;
    }

    public void setSabado(Boolean sabado) {
        this.sabado = sabado;
    }

    public Boolean getSegunda() {
        return segunda;
    }

    public void setSegunda(Boolean segunda) {
        this.segunda = segunda;
    }

    public Boolean getSexta() {
        return sexta;
    }

    public void setSexta(Boolean sexta) {
        this.sexta = sexta;
    }

    public Boolean getTerca() {
        return terca;
    }

    public void setTerca(Boolean terca) {
        this.terca = terca;
    }

    public ColaboradorVO getProfessor() {
        return professor;
    }

    public void setProfessor(ColaboradorVO professor) {
        this.professor = professor;
    }

    public TurmaVO getTurma() {        
        return turma;
    }

    public void setTurma(TurmaVO turma) {
        this.turma = turma;
    }

    public List getListaModalidade() {
        return listaModalidade;
    }

    public void setListaModalidade(List listaModalidade) {
        this.listaModalidade = listaModalidade;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public List getListaDiaSemana() {
        return listaDiaSemana;
    }

    public void setListaDiaSemana(List listaDiaSemana) {
        this.listaDiaSemana = listaDiaSemana;
    }

    public List getListaHorario() {
        return listaHorario;
    }

    public void setListaHorario(List listaHorario) {
        this.listaHorario = listaHorario;
    }
}
