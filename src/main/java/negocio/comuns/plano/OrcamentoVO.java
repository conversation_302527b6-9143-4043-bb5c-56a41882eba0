package negocio.comuns.plano;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.plano.ModeloOrcamento;

import java.util.Date;

public class OrcamentoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private int cliente = 0;
    private String nomeProspecto = "";
    private int consultor = 0;
    private int paraQuem = 0;
    private int modeloOrcamento = 0;
    private int situacao = 0;
    private String anotacao = "";
    private int idade = 0;
    private Date data = Calendario.hoje();
    private int periodo = 0;
    private int tipoTurma = 0;

    public String getNomeModeloOrcamento() throws Exception {
        return getFacade().getModeloOrcamento().consultarPorChavePrimaria(getModeloOrcamento(), Uteis.NIVELMONTARDADOS_TODOS).getDescricao();
    }

    public String getNomeColaborador() throws Exception {
        return getFacade().getColaborador().consultarPorChavePrimaria(getConsultor(), Uteis.NIVELMONTARDADOS_TODOS).getPessoa().getNomeAbreviado();
    }

    public String getSituacaoString() throws Exception {
        String nome = "";
        if(situacao == 1){
            nome = "Andamento";
        }else if (situacao == 2){
            nome = "Cancelado";
        }else if (situacao == 3){
            nome = "Finalizado";
        }
        return nome;
    }

    public String getDataOriginalApresentar(){
        return Uteis.getData(data);
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeProspecto() {
        return nomeProspecto;
    }

    public void setNomeProspecto(String nomeProspecto) {
        this.nomeProspecto = nomeProspecto;
    }

    public int getConsultor() {
        return consultor;
    }

    public void setConsultor(int consultor) {
        this.consultor = consultor;
    }

    public int getParaQuem() {
        return paraQuem;
    }

    public void setParaQuem(int paraQuem) {
        this.paraQuem = paraQuem;
    }

    public String getAnotacao() {
        return anotacao;
    }

    public void setAnotacao(String anotacao) {
        this.anotacao = anotacao;
    }

    public int getIdade() {
        return idade;
    }

    public void setIdade(int idade) {
        this.idade = idade;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public int getCliente() {
        return cliente;
    }

    public void setCliente(int cliente) {
        this.cliente = cliente;
    }

    public int getModeloOrcamento() {
        return modeloOrcamento;
    }

    public void setModeloOrcamento(int modeloOrcamento) {
        this.modeloOrcamento = modeloOrcamento;
    }

    public int getSituacao() {
        return situacao;
    }

    public void setSituacao(int situacao) {
        this.situacao = situacao;
    }

    public int getPeriodo() {
        return periodo;
    }

    public void setPeriodo(int periodo) {
        this.periodo = periodo;
    }

    public int getTipoTurma() {
        return tipoTurma;
    }

    public void setTipoTurma(int tipoTurma) {
        this.tipoTurma = tipoTurma;
    }
}
