package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: glauco
 * Date: 06/02/13
 * Time: 15:03
 */
public class RecCliConsultorTO extends SuperTO {

    private Integer codRecibo;
    private Date dataRecibo;
    private Integer responsavelLancamentoPagamento;
    private List<RecCliConsultorMovPgTO> movPagamentos;

    public List<ReciboClienteConsultorVO> toReciboClienteConsultor() {
        List<ReciboClienteConsultorVO> recibosAdicionar = new ArrayList<ReciboClienteConsultorVO>();

        for (RecCliConsultorMovPgTO movPagamento : movPagamentos) {
            for (RecCliConsultorMovProdTO produto : movPagamento.getMovProdutos()) {
                UsuarioVO usuarioResponsavelPagamento = new UsuarioVO();
                usuarioResponsavelPagamento.setCodigo(responsavelLancamentoPagamento);

                ReciboPagamentoVO reciboPagamentoVO = new ReciboPagamentoVO();
                reciboPagamentoVO.setCodigo(codRecibo);
                reciboPagamentoVO.setData(dataRecibo);
                reciboPagamentoVO.setUsuarioVO(usuarioResponsavelPagamento);

                FormaPagamentoVO formaPagamentoVO = new FormaPagamentoVO();
                formaPagamentoVO.setCodigo(movPagamento.getFormaPagamento());

                ClienteVO clienteVO = new ClienteVO();
                clienteVO.setCodigo(produto.getCodCliente());

                ColaboradorVO colaboradorVO = new ColaboradorVO();
                colaboradorVO.setCodigo(produto.getCodConsultorEpoca());

                ReciboClienteConsultorVO reciboClienteConsultorVO = new ReciboClienteConsultorVO();
                reciboClienteConsultorVO.setRecibo(reciboPagamentoVO);
                reciboClienteConsultorVO.setConsultor(colaboradorVO);
                reciboClienteConsultorVO.setCliente(clienteVO);
                reciboClienteConsultorVO.setValor(produto.getValor());
                reciboClienteConsultorVO.setFormaPagamento(formaPagamentoVO);
                reciboClienteConsultorVO.setResponsavelPagamento(usuarioResponsavelPagamento);
                recibosAdicionar.add(reciboClienteConsultorVO);
            }
        }


        return recibosAdicionar;
    }

    public Integer getCodRecibo() {
        return codRecibo;
    }

    public void setCodRecibo(Integer codRecibo) {
        this.codRecibo = codRecibo;
    }

    public Date getDataRecibo() {
        return dataRecibo;
    }

    public void setDataRecibo(Date dataRecibo) {
        this.dataRecibo = dataRecibo;
    }

    public List<RecCliConsultorMovPgTO> getMovPagamentos() {
        if (movPagamentos == null) {
            movPagamentos = new ArrayList<RecCliConsultorMovPgTO>();
        }
        return movPagamentos;
    }

    public void setMovPagamentos(List<RecCliConsultorMovPgTO> movPagamentos) {
        this.movPagamentos = movPagamentos;
    }

    public Integer getResponsavelLancamentoPagamento() {
        return responsavelLancamentoPagamento;
    }

    public void setResponsavelLancamentoPagamento(Integer responsavelLancamentoPagamento) {
        this.responsavelLancamentoPagamento = responsavelLancamentoPagamento;
    }
}

