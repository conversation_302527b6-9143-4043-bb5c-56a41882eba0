package negocio.comuns.financeiro.enumerador;

import servicos.impl.cieloecommerce.CieloECommerceRetornoEnum;
import servicos.impl.dcc.bin.DCCBinStatusEnum;
import servicos.impl.dcc.cielo.DCCCieloStatusEnum;
import servicos.impl.dcc.getnet.DCOGetNetStatusEnum;
import servicos.impl.getnet.GetnetOnlineRetornoEnum;
import servicos.impl.pagarMe.PagarMeRetornoEnum;
import servicos.impl.pagbank.PagBankRetornoEnum;
import servicos.impl.redepay.ERedeRetornoEnum;
import servicos.impl.stone.StoneRetornoEnum;
import servicos.impl.stripe.StripeRetornoEnum;

public enum OperacaoRetornoCobrancaEnum {

    NENHUM(          0, "Não identificado"),
    REENVIAR(        1, "Reenviar"),
    CONTATO_ALUNO(   2, "Entrar em contato com aluno"),
    CONTATO_CONVENIO(3, "Entrar em contato com a adquirente"),
    OUTRO(           4, "Outro");

    private Integer codigo;
    private String descricao;

    OperacaoRetornoCobrancaEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static OperacaoRetornoCobrancaEnum obterPorCodigo(Integer codigo) {
        for (OperacaoRetornoCobrancaEnum tipo : OperacaoRetornoCobrancaEnum.values()) {
            if (tipo.getCodigo().equals(codigo)) {
                return tipo;
            }
        }
        return NENHUM;
    }

    public static OperacaoRetornoCobrancaEnum obter(String codigoRetorno, TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum) {
        try {

            if (tipoConvenioCobrancaEnum == null) {
                return NENHUM;
            }

            if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
                return CieloECommerceRetornoEnum.valueOff(codigoRetorno).getOperacaoRetornoCobranca();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
                return ERedeRetornoEnum.valueOff(codigoRetorno).getOperacaoRetornoCobranca();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
                return GetnetOnlineRetornoEnum.valueOff(codigoRetorno).getOperacaoRetornoCobranca();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
                return StoneRetornoEnum.valueOff(codigoRetorno).getOperacaoRetornoCobranca();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)) {
                return PagarMeRetornoEnum.valueOff(codigoRetorno).getOperacaoRetornoCobranca();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGBANK)) {
                return PagBankRetornoEnum.valueOff(codigoRetorno).getOperacaoRetornoCobranca();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_STRIPE)) {
                return StripeRetornoEnum.valueOff(codigoRetorno).getOperacaoRetornoCobranca();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC)) {
                return DCCCieloStatusEnum.valueOff(codigoRetorno).getOperacaoRetornoCobranca();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
                return DCOGetNetStatusEnum.valueOff(codigoRetorno).getOperacaoRetornoCobranca();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
                return DCCBinStatusEnum.valueOff(codigoRetorno).getOperacaoRetornoCobranca();
            }

            //verificar se o codigo de retorno é um codigo pacto
            CodigoRetornoPactoEnum codigoRetornoPactoEnum = CodigoRetornoPactoEnum.consultarPorCodigo(codigoRetorno);
            if (codigoRetornoPactoEnum != null) {
                return codigoRetornoPactoEnum.getOperacaoRetornoCobrancaEnum();
            }
            return NENHUM;
        } catch (Exception ex) {
            ex.printStackTrace();
            return NENHUM;
        }
    }
}
