/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro.enumerador;

import br.com.pactosolucoes.integracao.pactopay.StatusPactoPayEnum;
import negocio.comuns.utilitarias.Ordenacao;

import java.util.ArrayList;
import java.util.List;
import javax.faces.model.SelectItem;

/**
 *
 * <AUTHOR>
 */
public enum SituacaoTransacaoEnum {

    NENHUMA(0, "Nenhuma", "", "", StatusPactoPayEnum.NENHUM),//vazio
    COM_ERRO(1, "Erro", "#FFD42E", "Erro de comunicação com a Administradora ou Dados Inválidos.", StatusPactoPayEnum.ERRO),//amarelo
    APROVADA(2, "Aprovada - Aguardando Confirmação", "#00008B", "Aprovada, porém não capturada (confirmada).", StatusPactoPayEnum.APROVADA_AGUARDANDO_CAPTURA),//azul escuro
    NAO_APROVADA(3, "Não Aprovada", "#DF0000", "Não aprovada por problemas com o Cartão.", StatusPactoPayEnum.NAO_APROVADA),//vermelho
    CONCLUIDA_COM_SUCESSO(4, "Concluída com Sucesso", "#008800", "Aprovada e Confirmada com sucesso.", StatusPactoPayEnum.CONCLUIDA_COM_SUCESSO),//verde
    ERRO_CAPTURA(5, "Erro na Confirmação da Transação", "#000", "Erro durante confirmação da aprovação.", StatusPactoPayEnum.ERRO_RETORNO),// preto
    CANCELADA(6, "Transação Cancelada", "#F3CEDB", "Cancelada no Sistema Pacto e na Adquirente", StatusPactoPayEnum.CANCELADA),//rosa claro
    DESCARTADA(7, "Transação Descartada", "#8CB2FD", "Desconsiderada pelo Sistema Pacto, alguma pendência já foi resolvida por um Usuário.", StatusPactoPayEnum.NENHUM),//azul claro
    ESTORNADA(8, "Transação Estornada", "#961EE6", "Pagamento (recibo) estornado no Sistema Pacto. Porém é necessário o estorno na adquirente manualmente, via portal ou via ligação.", StatusPactoPayEnum.ESTORNADA),//roxo
    PENDENTE(9, "Transação Pendente Aguardando Retorno", "#961EE6", "Pendente", StatusPactoPayEnum.PENDENTE);
    private int id;
    private String descricao;
    private String imagem;
    private String hint;
    private StatusPactoPayEnum statusPactoPayEnum;

    SituacaoTransacaoEnum(int id, String descricao, String imagem, String hint, StatusPactoPayEnum statusPactoPayEnum) {
        this.id = id;
        this.descricao = descricao;
        this.imagem = imagem;
        this.hint = hint;
        this.statusPactoPayEnum = statusPactoPayEnum;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getImagem() {
        return imagem;
    }

    public void setImagem(String imagem) {
        this.imagem = imagem;
    }

    public String getHint() {
        return hint;
    }

    public void setHint(String hint) {
        this.hint = hint;
    }

    public static SituacaoTransacaoEnum getSituacaoTransacaoEnum(final int codigo) {
        for (SituacaoTransacaoEnum situacao : SituacaoTransacaoEnum.values()) {
            if (situacao.getId() == codigo) {
                return situacao;
            }
        }
        return SituacaoTransacaoEnum.NENHUMA;
    }

    public static List getSelectListSituacaoTransacao() {
        List temp = new ArrayList<SituacaoTransacaoEnum>();
        for (SituacaoTransacaoEnum obj : SituacaoTransacaoEnum.values()) {
            if (obj.equals(SituacaoTransacaoEnum.NENHUMA)) {
                continue;
            }
            temp.add(new SelectItem(obj, obj.getDescricao().toUpperCase().replace("TRANSAÇÃO ", "")));
        }
        Ordenacao.ordenarLista(temp, "label");
        temp.add(0, new SelectItem(null, "(Todas)"));
        return temp;
    }

    public static String getSituacoesRepescagem() {
        return String.format("(%s,%s,%s)", new Object[]{
                    NAO_APROVADA.getId(),
                    ERRO_CAPTURA.getId(),
                    DESCARTADA.getId()
                });
    }

    public StatusPactoPayEnum getStatusPactoPayEnum() {
        return statusPactoPayEnum;
    }
}
