/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro.enumerador;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 25/02/2022
 */
public enum OrigemCobrancaEnum {

    //códigos tbm são utilizados no vendas online

    NENHUM(0,"", false),
    ZW_MANUAL_CAIXA_ABERTO(1,"Caixa em Aberto", false),
    ZW_MANUAL_RETENTATIVA(2,"Retentativa - Manual", false),
    ZW_AUTOMATICO(3,"Automático", false),
    VENDAS_ONLINE_VENDA(4,"Vendas Online - Venda", false),
    VENDAS_ONLINE_LINK_PAGAMENTO(5,"Vendas Online - Link Pagamento", false),
    PACTO_STORE(6,"Pacto Store", false),
    WEB_SERVICE(7,"WebService", false),
    VERIFICADOR_CARTAO(8,"Verificar cartão", false),
    PACTO_PAY_RETENTATIVA(9,"Retentativa - PactoPay", false),
    PACTO_PAY_COBRAR(10,"Cobrar - PactoPay", false),
    MAILING(11,"Mailing", false),
    APP_ALUNO(12,"App do Aluno", false),
    REGUA_COBRANCA_COBRANCA_ANTECIPADA(13,"Link Pagamento - Régua de Cobrança - Cobrança Antecipada", true),
    REGUA_COBRANCA_CARTAO_A_VENCER(14,"Link Pagamento - Régua de Cobrança - Cartão a vencer", true),
    REGUA_COBRANCA_COBRANCA_ATRASO(15,"Link Pagamento - Régua de Cobrança - Comunicação de Atraso", true),
    VENDAS_ONLINE_LINK_CADASTRAR(16,"Vendas Online - Link Cadastrar Cartão", false),
    PACTO_PAY(17, "PactoPay", false),
    TOTEM(18, "Totem", false),
    REGUA_COBRANCA_CARTAO_VENCIDO(19,"Link Pagamento - Régua de Cobrança - Cartão vencido", true),
    ZW_HISTORICO_CONTATO(20,"Realizar contato com o aluno - Botão Link para cadastrar cartão online", false),
    REGUA_COBRANCA_COBRANCA_NEGADA(21,"Link Pagamento - Régua de Cobrança - Cobrança Negada", true),
    ZW_AUTOMATICO_RETENTATIVA(22,"Automático - Retentativa", true),
    ZW_MANUAL_AUTORIZACAO_COBRANCA(23,"Manual - Autorização de Cobrança", false),
    ZW_AUTOMATICO_RENOVA_FACIL_CIELO(24,"Renova Fácil Cielo", false),
    ZW_AUTOMATICO_RENOVA_FACIL_FACILITE_PAY(25,"Renova Fácil FacilitePay", false),
    ZW_MANUAL_VENDA_RAPIDA(26,"Adm - Venda Rápida", false),
    REGUA_COBRANCA_COBRANCA_CANCELADA(27,"Régua de Cobrança - Cobrança Cancelada", true),
    REGUA_COBRANCA_COBRANCA_APROVADA(28,"Régua de Cobrança - Cobrança Aprovada", true),
    ZW_MANUAL_GESTAO_BOLETOS_ONLINE(29,"Gestão de Boletos Online", false),
    PACTO_FLOW(30,"App Pacto Flow", false),
    ZW_MANUAL_CAIXA_ABERTO_V2(31,"Caixa em Aberto - V2", false),
    ZW_MANUAL_RETENTATIVA_TELA_ALUNO_V2(32,"Retentativa - Manual - Tela Aluno", false),
    ZW_MANUAL_TELA_ALUNO(33,"Tela do Aluno", false),
    API_SISTEMA_PACTO(34, "API Sistema Pacto", false), //API PACTO UTILIZANDO TOKEN GERADO NAS INTEGRACOES
    ;

    private Integer codigo;
    private String descricao;
    private boolean reguaCobranca; //identificar quais as origem que foram devido a alguma operação da régua de cobranca

    OrigemCobrancaEnum(Integer codigo, String descricao, boolean reguaCobranca) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.reguaCobranca = reguaCobranca;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public boolean isReguaCobranca() {
        return reguaCobranca;
    }

    public static OrigemCobrancaEnum obterPorCodigo(Integer codigo) {
        for (OrigemCobrancaEnum origem : values()) {
            if (origem.getCodigo().equals(codigo)) {
                return origem;
            }
        }
        return OrigemCobrancaEnum.NENHUM;
    }

}
