/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro.enumerador;

/**
 *
 * <AUTHOR>
 */
public enum TipoOperacaoContaCorrenteEnum {

    TOCC_Nenhum("NA", "Nada"),
    TO<PERSON>_Devolver("DE", "Usar o crédito"),
    TOCC_Receber("RE", "Receber o débito"),
    TOCC_Depositar("DT","Depósito de troco"),
    TOCC_Acerto("AC","Acerto de conta");
    
    private String id;
    private String descricao;

    private TipoOperacaoContaCorrenteEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

}
