/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.financeiro.enumerador;

/**
 *
 * <AUTHOR>
 * Define o Tipo de visualização que será apresentado no relatório de Demonstrativo Financeiro.
 */
public enum TipoVisualizacaoRelatorioDF {
    PLANOCONTA(1,"Plano de Conta"),
    CENTROCUSTO(2, "Centro de Custo");

    TipoVisualizacaoRelatorioDF(int codigo, String descricao){
        setCodigo(codigo);
        setDescricao(descricao);
    }

    int codigo;
    String descricao;

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

	public static TipoVisualizacaoRelatorioDF getTipoVisualizacaoDF(final int codigo) {
		TipoVisualizacaoRelatorioDF tipoVisualizacao = null;
		for (TipoVisualizacaoRelatorioDF tipo : TipoVisualizacaoRelatorioDF.values()) {
			if (tipo.getCodigo() == codigo) {
			   tipoVisualizacao = tipo;
                           break;
			}
		}
		return tipoVisualizacao;
	}




}
