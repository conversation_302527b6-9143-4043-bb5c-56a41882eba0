package negocio.comuns.financeiro.enumerador;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public enum TipoExigibilidadeISSEnum {

    EXIGIVEL(1, "Exigível"),
    NAO_INCIDENCIA(2, "Não Incidência"),
    ISENCAO(3, "Isencao"),
    EXPORTACAO(4, "Exportação"),
    IMUNIDADE(5, "Imunidade"),
    SUSPENSA_DECISAO_JUDICIAL(6, "Suspensa Decisão Judicial"),
    SUSPENSA_PROCESSO_ADMINISTRATIVO(7, "Suspensa Processo Administrativo");

    private int id;
    private String descricao;

    private TipoExigibilidadeISSEnum(int id, String descricao) {
        this.id = id;
        this.descricao = descricao;

    }

    public static List<SelectItem> getSelectListExigibilidadeISS() {
        List<SelectItem> temp = new ArrayList<SelectItem>();
        for (int i = 0; i < TipoExigibilidadeISSEnum.values().length; i++) {
            TipoExigibilidadeISSEnum obj = TipoExigibilidadeISSEnum.values()[i];
            temp.add(new SelectItem(obj.getId(), obj.getId() + " - " + obj.getDescricao()));
        }
        return temp;
    }

    public static TipoExigibilidadeISSEnum getTipo(Integer id) {
        for (TipoExigibilidadeISSEnum tipo : TipoExigibilidadeISSEnum.values()) {
            if (tipo.getId() == id) {
                return tipo;
            }
        }
        return EXIGIVEL;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

}