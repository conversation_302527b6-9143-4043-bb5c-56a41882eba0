/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro;

/**
 *
 * <AUTHOR>
 */
public class ComissaoLinearVO {

    private String matricula;
    private String cliente;
    private String situacao;
    private String valorpago;
    private String idpagamento;
    private String modalidade;
    private String fracaopagoporcent;
    private String fracaopagovalor;
    private String contrato;
    private String dia;
    private String horario;
    private String identificadorturma;
    private String professor;
    private String comissao;
    private String iniciomatricula;
    private String fimmatricula;
    private String valorcomissao;

    public String getCliente() {
        return cliente;
    }

    public void setCliente(String cliente) {
        this.cliente = cliente;
    }

    public String getComissao() {
        return comissao;
    }

    public void setComissao(String comissao) {
        this.comissao = comissao;
    }

    public String getContrato() {
        return contrato;
    }

    public void setContrato(String contrato) {
        this.contrato = contrato;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getFimmatricula() {
        return fimmatricula;
    }

    public void setFimmatricula(String fimmatricula) {
        this.fimmatricula = fimmatricula;
    }

    public String getFracaopagoporcent() {
        return fracaopagoporcent;
    }

    public void setFracaopagoporcent(String fracaopagoporcent) {
        this.fracaopagoporcent = fracaopagoporcent;
    }

    public String getFracaopagovalor() {
        return fracaopagovalor;
    }

    public void setFracaopagovalor(String fracaopagovalor) {
        this.fracaopagovalor = fracaopagovalor;
    }

    public String getHorario() {
        return horario;
    }

    public void setHorario(String horario) {
        this.horario = horario;
    }

    public String getIdentificadorturma() {
        return identificadorturma;
    }

    public void setIdentificadorturma(String identificadorturma) {
        this.identificadorturma = identificadorturma;
    }

    public String getIdpagamento() {
        return idpagamento;
    }

    public void setIdpagamento(String idpagamento) {
        this.idpagamento = idpagamento;
    }

    public String getIniciomatricula() {
        return iniciomatricula;
    }

    public void setIniciomatricula(String iniciomatricula) {
        this.iniciomatricula = iniciomatricula;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getValorcomissao() {
        return valorcomissao;
    }

    public void setValorcomissao(String valorcomissao) {
        this.valorcomissao = valorcomissao;
    }

    public String getValorpago() {
        return valorpago;
    }

    public void setValorpago(String valorpago) {
        this.valorpago = valorpago;
    }
}
