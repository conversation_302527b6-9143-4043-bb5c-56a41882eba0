package negocio.comuns.financeiro;
import org.json.JSONObject;

public class BILtvChurnRateEmpresa {
    private Integer codigoEmpresa;
    private String nomeEmpresa;
    private double janeiro;
    private double fevereiro;
    private double marco;
    private double abril;
    private double maio;
    private double junho;
    private double julho;
    private double agosto;
    private double setembro;
    private double outubro;
    private double novembro;
    private double dezembro;

    public BILtvChurnRateEmpresa() {
    }

    public BILtvChurnRateEmpresa(JSONObject dadosLtvEmpresa){
        this.codigoEmpresa = dadosLtvEmpresa.getInt("codigoEmpresa");
        this.nomeEmpresa = dadosLtvEmpresa.getString("nomeEmpresa");
        this.janeiro = dadosLtvEmpresa.getDouble("janeiro");
        this.fevereiro = dadosLtvEmpresa.getDouble("fevereiro");
        this.marco = dadosLtvEmpresa.getDouble("marco");
        this.abril = dadosLtvEmpresa.getDouble("abril");
        this.maio = dadosLtvEmpresa.getDouble("maio");
        this.junho = dadosLtvEmpresa.getDouble("junho");
        this.julho = dadosLtvEmpresa.getDouble("julho");
        this.agosto = dadosLtvEmpresa.getDouble("agosto");
        this.setembro = dadosLtvEmpresa.getDouble("setembro");
        this.outubro = dadosLtvEmpresa.getDouble("outubro");
        this.novembro = dadosLtvEmpresa.getDouble("novembro");
        this.dezembro = dadosLtvEmpresa.getDouble("dezembro");
    }

    private String montaCor(double churn) {
        if (churn <= 3) {
            return "#28A24E";
        } else if (churn > 3 && churn <= 8) {
            return "#F37021";
        } else {
            return "#EF3C34";
        }
    }

    public String getCorJaneiro() {
        return montaCor(getJaneiro());
    }

    public String getCorFevereiro() {
        return montaCor(getFevereiro());
    }

    public String getCorMarco() {
        return montaCor(getMarco());
    }

    public String getCorAbril() {
        return montaCor(getAbril());
    }

    public String getCorMaio() {
        return montaCor(getMaio());
    }

    public String getCorJunho() {
        return montaCor(getJunho());
    }

    public String getCorJulho() {
        return montaCor(getJulho());
    }

    public String getCorAgosto() {
        return montaCor(getAgosto());
    }

    public String getCorSetembro() {
        return montaCor(getSetembro());
    }

    public String getCorOurubro() {
        return montaCor(getOutubro());
    }

    public String getCorNovembro() {
        return montaCor(getNovembro());
    }

    public String getCorDezembro() {
        return montaCor(getDezembro());
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public double getJaneiro() {
        return janeiro;
    }

    public void setJaneiro(double janeiro) {
        this.janeiro = janeiro;
    }

    public double getFevereiro() {
        return fevereiro;
    }

    public void setFevereiro(double fevereiro) {
        this.fevereiro = fevereiro;
    }

    public double getMarco() {
        return marco;
    }

    public void setMarco(double marco) {
        this.marco = marco;
    }

    public double getAbril() {
        return abril;
    }

    public void setAbril(double abril) {
        this.abril = abril;
    }

    public double getMaio() {
        return maio;
    }

    public void setMaio(double maio) {
        this.maio = maio;
    }

    public double getJunho() {
        return junho;
    }

    public void setJunho(double junho) {
        this.junho = junho;
    }

    public double getJulho() {
        return julho;
    }

    public void setJulho(double julho) {
        this.julho = julho;
    }

    public double getAgosto() {
        return agosto;
    }

    public void setAgosto(double agosto) {
        this.agosto = agosto;
    }

    public double getSetembro() {
        return setembro;
    }

    public void setSetembro(double setembro) {
        this.setembro = setembro;
    }

    public double getOutubro() {
        return outubro;
    }

    public void setOutubro(double outubro) {
        this.outubro = outubro;
    }

    public double getNovembro() {
        return novembro;
    }

    public void setNovembro(double novembro) {
        this.novembro = novembro;
    }

    public double getDezembro() {
        return dezembro;
    }

    public void setDezembro(double dezembro) {
        this.dezembro = dezembro;
    }
}
