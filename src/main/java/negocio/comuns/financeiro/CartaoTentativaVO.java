package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.ceopag.CeopagRetornoEnum;
import servicos.impl.cieloecommerce.CieloECommerceRetornoEnum;
import servicos.impl.dcc.bin.DCCBinStatusEnum;
import servicos.impl.dcc.cielo.DCCCieloStatusEnum;
import servicos.impl.dcc.getnet.DCOGetNetStatusEnum;
import servicos.impl.dcccaixaonline.DCCCaixaOnlineRetornoEnum;
import servicos.impl.getnet.GetnetOnlineRetornoEnum;
import servicos.impl.onepayment.OnePaymentRetornoEnum;
import servicos.impl.pagarMe.PagarMeRetornoEnum;
import servicos.impl.pagbank.PagBankRetornoEnum;
import servicos.impl.pagolivre.PagoLivreRetornoEnum;
import servicos.impl.redepay.ERedeRetornoEnum;
import servicos.impl.stone.StoneRetornoEnum;
import servicos.impl.stoneV5.StoneOnlineV5RetornoPSPEnum;
import servicos.impl.stripe.StripeRetornoEnum;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 01/05/2020
 */
public class CartaoTentativaVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Date data;
    private String cartao;
    private ConvenioCobrancaVO convenioCobrancaVO;
    private TipoConvenioCobrancaEnum tipoConvenioCobranca;
    private String codigoRetorno;
    private CodigoRetornoPactoEnum codigoRetornoPacto;
    private boolean transacaoPresencial = false;
    private Integer usuario;
    private Integer transacao;
    private Integer remessaItem;
    private OperacaoRetornoCobrancaEnum operacaoRetornoCobranca;
    @NaoControlarLogAlteracao
    private TransacaoVO transacaoVO;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public String getCartao() {
        if (cartao == null) {
            cartao = "";
        }
        return cartao;
    }

    public void setCartao(String cartao) {
        this.cartao = cartao;
    }

    public ConvenioCobrancaVO getConvenioCobrancaVO() {
        if (convenioCobrancaVO == null) {
            convenioCobrancaVO = new ConvenioCobrancaVO();
        }
        return convenioCobrancaVO;
    }

    public void setConvenioCobrancaVO(ConvenioCobrancaVO convenioCobrancaVO) {
        this.convenioCobrancaVO = convenioCobrancaVO;
    }

    public TipoConvenioCobrancaEnum getTipoConvenioCobranca() {
        if (tipoConvenioCobranca == null) {
            tipoConvenioCobranca = TipoConvenioCobrancaEnum.NENHUM;
        }
        return tipoConvenioCobranca;
    }

    public void setTipoConvenioCobranca(TipoConvenioCobrancaEnum tipoConvenioCobranca) {
        this.tipoConvenioCobranca = tipoConvenioCobranca;
    }

    public String getCodigoRetorno() {
        if (codigoRetorno == null) {
            codigoRetorno = "";
        }
        return codigoRetorno;
    }

    public void setCodigoRetorno(String codigoRetorno) {
        this.codigoRetorno = codigoRetorno;
    }

    public CodigoRetornoPactoEnum getCodigoRetornoPacto() {
        if (codigoRetornoPacto == null) {
            codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
        }
        return codigoRetornoPacto;
    }

    public void setCodigoRetornoPacto(CodigoRetornoPactoEnum codigoRetornoPacto) {
        this.codigoRetornoPacto = codigoRetornoPacto;
    }

    public static CodigoRetornoPactoEnum obterCodigoRetornoPacto(RemessaItemVO remessaItemVO,
                                                                 TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum,
                                                                 String codigoRetorno) {
        return obterCodigoRetornoPactoGeral(null, remessaItemVO, tipoConvenioCobrancaEnum, codigoRetorno);
    }

    public static CodigoRetornoPactoEnum obterCodigoRetornoPacto(TransacaoVO transacaoVO,
                                                                 TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum,
                                                                 String codigoRetorno) {
        return obterCodigoRetornoPactoGeral(transacaoVO, null, tipoConvenioCobrancaEnum, codigoRetorno);
    }

    public static CodigoRetornoPactoEnum obterCodigoRetornoPactoGeral(TransacaoVO transacaoVO, RemessaItemVO remessaItemVO,
                                                                      TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum, String codigoRetorno) {
        try {

            if (tipoConvenioCobrancaEnum == null) {
                throw new Exception("TipoConvenioCobrancaEnum null!");
            }
            if (UteisValidacao.emptyString(codigoRetorno)) {
                throw new Exception("CodigoRetorno vazio!");
            }

            if (transacaoVO != null &&
                    !UteisValidacao.emptyNumber(transacaoVO.getCodigo()) &&
                    transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                return CodigoRetornoPactoEnum.SUCESSO;
            }

            if (remessaItemVO != null && remessaItemVO.getMovPagamento() != null) {
                return CodigoRetornoPactoEnum.SUCESSO;
            }

            //verificar se o codigo de retorno é um codigo pacto
            CodigoRetornoPactoEnum codigoRetornoPactoEnum = CodigoRetornoPactoEnum.consultarPorCodigo(codigoRetorno);
            if (codigoRetornoPactoEnum != null) {
                return codigoRetornoPactoEnum;
            }

            if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
                return CieloECommerceRetornoEnum.valueOff(codigoRetorno).getCodigoRetornoPacto();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
                return ERedeRetornoEnum.valueOff(codigoRetorno).getCodigoRetornoPacto();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
                return GetnetOnlineRetornoEnum.valueOff(codigoRetorno).getCodigoRetornoPacto();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
                return StoneRetornoEnum.valueOff(codigoRetorno).getCodigoRetornoPacto();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
                return StoneOnlineV5RetornoPSPEnum.valueOff(codigoRetorno).getCodigoRetornoPacto();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)) {
                return PagarMeRetornoEnum.valueOff(codigoRetorno).getCodigoRetornoPacto();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGBANK)) {
                return PagBankRetornoEnum.valueOff(codigoRetorno).getCodigoRetornoPacto();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_STRIPE)) {
                return StripeRetornoEnum.valueOff(codigoRetorno).getCodigoRetornoPacto();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE)) {
                return PagoLivreRetornoEnum.valueOff(codigoRetorno).getCodigoRetornoPacto();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) {
                return PagoLivreRetornoEnum.valueOff(codigoRetorno).getCodigoRetornoPacto();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_CEOPAG)) {
                return CeopagRetornoEnum.valueOff(codigoRetorno).getCodigoRetornoPacto();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_ONE_PAYMENT)) {
                return OnePaymentRetornoEnum.valueOff(codigoRetorno).getCodigoRetornoPacto();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
                return DCOGetNetStatusEnum.valueOff(codigoRetorno).getCodigoRetornoPacto();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC)) {
                return DCCCieloStatusEnum.valueOff(codigoRetorno).getCodigoRetornoPacto();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
                return DCCBinStatusEnum.valueOff(codigoRetorno).getCodigoRetornoPacto();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_CAIXA_ONLINE)) {
                return DCCCaixaOnlineRetornoEnum.valueOff(codigoRetorno).getCodigoRetornoPacto();
            }
            return CodigoRetornoPactoEnum.OUTRO;
        } catch (Exception ex) {
            ex.printStackTrace();
            return CodigoRetornoPactoEnum.OUTRO;
        }
    }

    public static String obterDescricaoRetornoAdquirente(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum, String codigoRetorno, TipoCredencialStoneEnum tipoCredencialStoneEnum,
                                                         OperadorasExternasAprovaFacilEnum bandeira) {
        try {

            if (tipoConvenioCobrancaEnum == null) {
                throw new Exception("TipoConvenioCobrancaEnum null!");
            }
            if (UteisValidacao.emptyString(codigoRetorno)) {
                throw new Exception("CodigoRetorno vazio!");
            }

            if (codigoRetorno.startsWith("PAC")) {
                CodigoRetornoPactoEnum codigoRetornoPactoEnum = CodigoRetornoPactoEnum.consultarPorCodigo(codigoRetorno);
                if (codigoRetornoPactoEnum != null) {
                    return codigoRetornoPactoEnum.getDescricao();
                }
            }

            if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
                return CieloECommerceRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
                return ERedeRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
                return GetnetOnlineRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
                return StoneRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)) {
                return PagarMeRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGBANK)) {
                return PagBankRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_STRIPE)) {
                return StripeRetornoEnum.valueOff(codigoRetorno).getDescricao();
            }else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
                return DCOGetNetStatusEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC)) {
                return DCCCieloStatusEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
                return DCCBinStatusEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) ||
                    tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) {
                return PagoLivreRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
                try {
                    if (tipoCredencialStoneEnum != null && bandeira != null) {
                        if (tipoCredencialStoneEnum.equals(TipoCredencialStoneEnum.PSP)) {
                            return StoneOnlineV5RetornoPSPEnum.valueOff(codigoRetorno).getDescricao();
                        } else if (tipoCredencialStoneEnum.equals(TipoCredencialStoneEnum.GATEWAY)) {
                            return RetornoAbecsBandeirasEnum.obterMotivoPorCodigoEBandeira(codigoRetorno, bandeira);
                        }
                        return "Motivo não encontrado";
                    }
                } catch(Exception exc){
                    return "Motivo não encontrado";
                }
            }

            //verificar se o codigo de retorno é um codigo pacto
            CodigoRetornoPactoEnum codigoRetornoPactoEnum = CodigoRetornoPactoEnum.consultarPorCodigo(codigoRetorno);
            if (codigoRetornoPactoEnum != null) {
                return codigoRetornoPactoEnum.getDescricao();
            }
            return "";
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public TransacaoVO getTransacaoVO() {
        if (transacaoVO == null) {
            transacaoVO = new TransacaoVO();
        }
        return transacaoVO;
    }

    public void setTransacaoVO(TransacaoVO transacaoVO) {
        this.transacaoVO = transacaoVO;
    }

    public boolean isTransacaoPresencial() {
        return transacaoPresencial;
    }

    public void setTransacaoPresencial(boolean transacaoPresencial) {
        this.transacaoPresencial = transacaoPresencial;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public Integer getTransacao() {
        return transacao;
    }

    public void setTransacao(Integer transacao) {
        this.transacao = transacao;
    }

    public Integer getRemessaItem() {
        return remessaItem;
    }

    public void setRemessaItem(Integer remessaItem) {
        this.remessaItem = remessaItem;
    }

    public OperacaoRetornoCobrancaEnum getOperacaoRetornoCobranca() {
        if (operacaoRetornoCobranca == null) {
            operacaoRetornoCobranca = OperacaoRetornoCobrancaEnum.NENHUM;
        }
        return operacaoRetornoCobranca;
    }

    public void setOperacaoRetornoCobranca(OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
    }
}
