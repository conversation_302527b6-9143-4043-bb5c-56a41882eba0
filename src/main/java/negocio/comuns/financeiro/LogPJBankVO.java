package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

public class LogPJBankVO extends SuperVO {
    private Integer boletoPJBank;
    private Date dataRegistroLog;
    private String descricao;

    public LogPJBankVO() {
        this.boletoPJBank = boletoPJBank;
        this.dataRegistroLog = dataRegistroLog;
        this.descricao = descricao;
    }

    public Integer getBoletoPJBank() {
        return boletoPJBank;
    }

    public void setBoletoPJBank(Integer boletoPJBank) {
        this.boletoPJBank = boletoPJBank;
    }

    public Date getDataRegistroLog() {
        return dataRegistroLog;
    }

    public void setDataRegistroLog(Date dataRegistroLog) {
        this.dataRegistroLog = dataRegistroLog;
    }

    public String getDescricao() {
        return (descricao == null ? "" : this.descricao);
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
