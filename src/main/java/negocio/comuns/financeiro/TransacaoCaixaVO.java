package negocio.comuns.financeiro;

import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.dcccaixaonline.DCCCaixaBandeirasEnum;
import servicos.impl.dcccaixaonline.DCCCaixaOnlineRetornoEnum;

public class TransacaoCaixaVO extends TransacaoVO {

    public String getNSU() {
        try {
            if (getTipo().equals(TipoTransacaoEnum.DCC_CAIXA_ONLINE)) {
                JSONObject obj = new JSONObject(getParamsResposta());
                JSONObject payment = obj.getJSONObject("payment");
                String nsu = payment.getString("host_usn");
                return nsu.trim();
            }
            return "";
        } catch (Exception ex) {
            return "";
        }
    }

    public String getTID() {
        try {
            if (getTipo().equals(TipoTransacaoEnum.DCC_CAIXA_ONLINE)) {
                JSONObject obj = new JSONObject(getParamsResposta());
                JSONObject payment = obj.getJSONObject("payment");
                return payment.getString("nit");
            }
            return "";
        } catch (Exception ex) {
            return "";
        }
    }

    public String getPayment() throws Exception {
        try {
            JSONObject json = new JSONObject(this.getParamsResposta());
            return json.getJSONObject("payment").toString();
        } catch (Exception e) {
            return new JSONObject().toString();
        }
    }

    public Integer getNrVezes() {
        try {
            JSONObject obj = new JSONObject(getParamsEnvio());
            JSONObject payment = obj.getJSONObject("payment");
            return payment.getInt("installments");
        } catch (Exception ex) {
            return 0;
        }
    }

    public String getCartaoMascarado() {
        try {
            return getValorCartaoMascarado();
        } catch (Exception e) {
            return "";
        }
    }

    public String getValorCartaoMascarado() throws Exception {
        if (getTipo().equals(TipoTransacaoEnum.DCC_CAIXA_ONLINE)) {
            JSONObject obj = new JSONObject(getParamsEnvio());
            JSONObject creditCard = obj.getJSONObject("card");
            return APF.getCartaoMascarado(creditCard.getString("number"));
        } else {
            return "";
        }
    }

    public String getBandeira() {
        String result = "";
        try {
            if (getTipo().equals(TipoTransacaoEnum.DCC_CAIXA_ONLINE)) {
                JSONObject obj = new JSONObject(getParamsEnvio());
                DCCCaixaBandeirasEnum band = DCCCaixaBandeirasEnum.obterBandeiraFiservCodigo(obj.optString("authorizer_id"));
                result = band.getDescricaoPacto();
            }
        } catch (Exception ex) {
            return "";
        }
        return result.toUpperCase();
    }

    public String getResultadoRequisicao() throws Exception {
        String returnMessage = "";
        try {
            JSONObject json = new JSONObject(getPayment());
            returnMessage = json.optString("authorizer_message");
        } catch (Exception ignored) {
        }

        if (UteisValidacao.emptyString(returnMessage)) {
            returnMessage = DCCCaixaOnlineRetornoEnum.valueOf(getCodigoRetorno()).getDescricao();
        }

        return returnMessage;
    }

    public String getAutorizacao() {
        try {
            String payment = getPayment();
            JSONObject obj = new JSONObject(payment);
            String authorization = obj.optString("authorization_number");
            return authorization.trim();
        } catch (Exception ex) {
            return "";
        }
    }

    public String getCodErroExterno() {
        try {
            String codigoRetorno = "";
            try {
                //Se não achou no retorno o Payment e AuthorizerCode, então teve erro no Processamento.
                //Vai no catch e pega esse código Erro.
                //É assim, pois retorna erro para Transação Negada
                String payment = getPayment();
                JSONObject obj = new JSONObject(payment);
                String authorization = obj.getString("authorizer_code");
                if (!UteisValidacao.emptyString(authorization)) {
                    codigoRetorno = "";
                }
            } catch (Exception ex) {
                JSONObject json = new JSONObject(getParamsResposta());
                codigoRetorno = json.optString("code");
            }

            return codigoRetorno;
        } catch (Exception ex) {
            return "";
        }
    }

}
