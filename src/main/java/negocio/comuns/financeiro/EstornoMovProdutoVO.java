package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.armario.Armario;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.contrato.PeriodoAcessoCliente;
import negocio.facade.jdbc.financeiro.AulaAvulsaDiaria;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.Pix;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.financeiro.RemessaItem;
import negocio.facade.jdbc.financeiro.Transacao;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class EstornoMovProdutoVO extends SuperVO {

    protected MovProdutoVO movProdutoVO;
    protected UsuarioVO responsavelEstorno;
    protected Integer contrato;
    protected Integer vendaavulsa;
    protected Integer aulaAvulsaDiaria;
    protected Integer personal;
    protected MovParcelaVO parcela;
    protected ClienteVO clienteVO;
    protected List<EstornoReciboVO> listaEstornoRecibo;
    protected List<MovParcelaVO> listaMovParcela;
    protected List<MovParcelaVO> listaMovParcelaVinculadas;
    protected List<MovProdutoVO> listaMovProduto;
    protected Boolean exiteOutroContratoPagouMinhaParcela;
    private List<TransacaoVO> listaTransacoes = new ArrayList<TransacaoVO>();
    private CaixaVO caixa = new CaixaVO();
    private List<RemessaItemVO> listaItensRemessa = new ArrayList<RemessaItemVO>();
    private boolean estornarOperadora = true;

    public EstornoMovProdutoVO() {
        super();
        inicializarDados();
    }

    public void inicializarDados() {
        setListaMovProduto(new ArrayList<MovProdutoVO>());
        setListaMovParcela(new ArrayList<MovParcelaVO>());
        setListaEstornoRecibo(new ArrayList<EstornoReciboVO>());
        setResponsavelEstorno(new UsuarioVO());
        setMovProdutoVO(new MovProdutoVO());
        setClienteVO(new ClienteVO());
        setContrato(0);
        setExiteOutroContratoPagouMinhaParcela(false);

    }

    public List estornarMovProduto(String key, boolean estornarOperadora, Connection con) throws Exception {
        Transacao transacaoDAO = null;
        RemessaItem remessaItemDAO = null;
        Armario armarioDAO = null;
        try {
            transacaoDAO = new Transacao(con);
            remessaItemDAO = new RemessaItem(con);
            armarioDAO = new Armario(con);

            List<LogVO> listaLog = new ArrayList<LogVO>();
            LogVO log = new LogVO();
            montarLogDadosMovProduto(log);
            montarLogDadosCliente(log);
            montarLogDadosParcelas(log);
            transacaoDAO.gerarLogEstornarTransacoes(estornarOperadora, this.getMovProdutoVO().getPessoa().getCodigo(), getResponsavelEstorno(), "PRODUTO");
            transacaoDAO.estornarTransacoes(estornarOperadora, log, getListaTransacoes(), getResponsavelEstorno(), false);
            remessaItemDAO.estornarItensRemessa(log, getListaItensRemessa(), true, getResponsavelEstorno());
            List<LogVO> logsContrato = validarSeExisteReciboContrato(log, key, con);
            listaLog.addAll(logsContrato);
            if (!getExiteOutroContratoPagouMinhaParcela()) {
                MovProduto movProduto = new MovProduto();
                for (MovProdutoVO produto : getListaMovProduto()) {
                    armarioDAO.estornarAluguel(produto.getCodigo(), log);
                    movProduto.excluirSemCommit(produto);
                }
            }
            listaLog.add(log);
            return listaLog;
        } finally {
            transacaoDAO = null;
            remessaItemDAO = null;
            armarioDAO = null;
        }
    }

    /**
     * Separar as transações de cartão de crédito associadas às parcelas
     * informadas como argumento, para que o estorno do contrato
     * possa retirar as dependências e manter o histórico das transações.
     * @param listParc
     * @throws Exception
     */
    public void montarListaTransacoes(List<MovParcelaVO> listParc, Connection con) throws Exception {
        Transacao transacaoDAO = null;
        try {
            transacaoDAO = new Transacao(con);
            this.setListaTransacoes(transacaoDAO.obterListaTransacoesEstorno(listParc));
        } finally {
            transacaoDAO = null;
        }
    }

    public void montarLogDadosMovProduto(LogVO obj) throws Exception {

        if (!getExiteOutroContratoPagouMinhaParcela()) {
            obj.setChavePrimaria(getCodigo().toString());
            obj.setNomeEntidade("MOVPRODUTO");
            obj.setNomeEntidadeDescricao("MOVIMENTAÇÃO PRODUTO");
            obj.setOperacao("ESTORNO - MOVIMENTAÇÃO PRODUTO");
            obj.setResponsavelAlteracao(getResponsavelEstorno().getNome());
            obj.setUserOAMD(getResponsavelEstorno().getUserOamd());
            obj.setNomeCampo("TODOS");
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setValorCampoAnterior("");
            obj.setValorCampoAlterado("--------------------------------------\n\r");
            MovProduto movProduto = new MovProduto();
            for (MovProdutoVO produto : getListaMovProduto()) {
                obj.setValorCampoAlterado(obj.getValorCampoAlterado() + " \n\rCódigo do Movimentação Produto = " + produto.getCodigo() + "\n\r" + " Descrição = " + produto.getProduto().getDescricao().toUpperCase() + "\n\r" + "Valor = R$" + Uteis.getDoubleFormatado(produto.getTotalFinal()) + "\n\r");
            }
        } else {
            obj.setChavePrimaria(getCodigo().toString());
            obj.setNomeEntidade("RECIBO");
            obj.setNomeEntidadeDescricao("Recibo");
            obj.setOperacao("ESTORNO - Recibo Produto");
            obj.setResponsavelAlteracao(getResponsavelEstorno().getNome());
            obj.setNomeCampo("TODOS");
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setValorCampoAlterado("--------------------------------------\n\r");
        }

    }

    public void montarLogDadosCliente(LogVO obj) throws Exception {

        obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "--------------------------------------\n\r");
        obj.setValorCampoAlterado(obj.getValorCampoAlterado() + " \n\rCódigo do Cliente = " + getClienteVO().getCodigo() + "\n\r" + "Matricula = " + getClienteVO().getMatricula() + "\n\r" + "Nome do Cliente = " + getClienteVO().getPessoa().getNome() + "\n\r");

    }

    public void montarLogDadosParcelas(LogVO obj) throws Exception {

        if (!getListaEstornoRecibo().isEmpty()) {
            Iterator i = getListaEstornoRecibo().iterator();
            while (i.hasNext()) {
                EstornoReciboVO estornoRecibo = (EstornoReciboVO) i.next();

                Iterator j = estornoRecibo.getListaMovParcela().iterator();
                obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "\n\r --------------------------------------\n\r");
                while (j.hasNext()) {
                    MovParcelaVO parcela = (MovParcelaVO) j.next();
                    String nome = "";
                    if (parcela.getContrato().getPessoa().getCodigo() != 0) {
                        nome = parcela.getContrato().getPessoa().getNome();
                    } else if (parcela.getAulaAvulsaDiariaVO().getCliente().getPessoa().getCodigo() != 0) {
                        nome = parcela.getAulaAvulsaDiariaVO().getCliente().getPessoa().getNome();
                    } else if (parcela.getVendaAvulsaVO().getCliente().getPessoa().getCodigo() != 0) {
                        nome = parcela.getVendaAvulsaVO().getCliente().getPessoa().getNome();
                    }
                    obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "código da parcela = " + parcela.getCodigo() + " \n\rdescrição parcela = " + parcela.getDescricao() + " \n\rnome do aluno = " + nome + " \n\rvalor da parcela = R$ " + Uteis.getDoubleFormatado(parcela.getValorParcela()) + " \n\rnumero contrato = " + parcela.getContrato().getCodigo() + " \n\rsituação = " + parcela.getSituacao_Apresentar() + "\n\r");
                }
            }
        } else {
            Iterator i = getListaMovParcela().iterator();
            obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "\n\r --------------------------------------\n\r");
            while (i.hasNext()) {
                MovParcelaVO parcela = (MovParcelaVO) i.next();
                String nome = "";
                if (parcela.getContrato().getPessoa().getCodigo() != 0) {
                    nome = parcela.getContrato().getPessoa().getNome();
                } else if (parcela.getAulaAvulsaDiariaVO().getCliente().getPessoa().getCodigo() != 0) {
                    nome = parcela.getAulaAvulsaDiariaVO().getCliente().getPessoa().getNome();
                } else if (parcela.getVendaAvulsaVO().getCliente().getPessoa().getCodigo() != 0) {
                    nome = parcela.getVendaAvulsaVO().getCliente().getPessoa().getNome();
                }

                obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "código da parcela = " + parcela.getCodigo() + " \n\rdescrição parcela = " + parcela.getDescricao() + " \n\rnome do aluno = " + nome + " \n\rvalor da parcela = R$ " + Uteis.getDoubleFormatado(parcela.getValorParcela()) + " \n\rnumero contrato = " + parcela.getContrato().getCodigo() + " \n\rsituação = " + parcela.getSituacao_Apresentar() + "\n\r");

            }
        }

    }

    public List<LogVO> validarSeExisteReciboContrato(LogVO obj, String key, Connection con) throws Exception {
        PeriodoAcessoCliente periodoAcessoClienteDAO;
        ReciboPagamento reciboFacade;
        MovParcela parcelaFacade;
        MovPagamento pagamentoFacade;
        MovProdutoParcela movProdutoParcela;
        Contrato contratoFacade;
        try {
            // declaro os facades para altera situacao do recibo
            periodoAcessoClienteDAO = new PeriodoAcessoCliente(con);
            reciboFacade = new ReciboPagamento(con);
            parcelaFacade = new MovParcela(con);
            pagamentoFacade = new MovPagamento(con);
            movProdutoParcela = new MovProdutoParcela(con);
            contratoFacade = new Contrato(con);

            List<LogVO> logs = new ArrayList<LogVO>();
            if (!getListaEstornoRecibo().isEmpty()) {

                Iterator i = getListaEstornoRecibo().iterator();
                while (i.hasNext()) {

                    EstornoReciboVO estornoRecibo = (EstornoReciboVO) i.next();
                    logs.addAll(estornoRecibo.criarLogEstornoRecibo(pagamentoFacade, movProdutoParcela, con));
                    obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "--------------------------------------\n\r");
                    obj.setValorCampoAlterado(obj.getValorCampoAlterado() + " \n\rcódigo do recibo = " + estornoRecibo.getReciboPagamentoVO().getCodigo() + "\n\r" + "data entrada no caixa = " + estornoRecibo.getReciboPagamentoVO().getData_Apresentar() + "\n\r" + "valor Total = R$ " + Uteis.getDoubleFormatado(estornoRecibo.getReciboPagamentoVO().getValorTotal()) + "\n\r" + "nome do Cliente = " + estornoRecibo.getReciboPagamentoVO().getNomePessoaPagador() + "\n\r" + "responsável lançamento = " + estornoRecibo.getReciboPagamentoVO().getResponsavelLancamento().getNome() + "\n\r");
                    estornoRecibo.setResponsavelEstornoRecivo(getResponsavelEstorno());
                    contratoFacade.lancarSaidaEstornoRecibo(estornoRecibo, null, getCaixa());
                    estornoRecibo.validarSeExisteMovimentoContaCorrente(estornoRecibo, con);
                    if (getExiteOutroContratoPagouMinhaParcela()) {
                        estornoRecibo.alterarSituacaoMovParcela(con);
                    } else {
                        validarParcelaASerExcluida(estornoRecibo.getListaMovParcela(), parcelaFacade, periodoAcessoClienteDAO, getResponsavelEstorno(), con);
                    }
                    estornoRecibo.setResponsavelEstornoRecivo(getResponsavelEstorno());
                    estornoRecibo.excluirMovPagamento(con);
                    reciboFacade.tentarEstornoCreditoPersonal(estornoRecibo.getReciboPagamentoVO(), key, this.getMovProdutoVO().getProduto().getTipoProduto().equals(TipoProduto.CREDITO_PERSONAL.getCodigo()));
                    reciboFacade.exluirPix(estornoRecibo);
                    reciboFacade.excluir(estornoRecibo.getReciboPagamentoVO());
                }
            } else {
                validarParcelaASerExcluida(getListaMovParcela(), parcelaFacade, periodoAcessoClienteDAO, getResponsavelEstorno(), con);
            }
            return logs;
        } finally {
            periodoAcessoClienteDAO = null;
            reciboFacade = null;
            parcelaFacade = null;
            pagamentoFacade = null;
            movProdutoParcela = null;
            contratoFacade = null;
        }
    }

    public void validarParcelaASerExcluida(List listaParcela, MovParcela parcelaFacade, PeriodoAcessoCliente periodoAcessoCliente,
                                           UsuarioVO usuarioVO, Connection con) throws Exception {
        AulaAvulsaDiaria aulaAvulsaDiariaDAO;
        Pix pixDAO;
        try {
            aulaAvulsaDiariaDAO = new AulaAvulsaDiaria(con);
            pixDAO = new Pix(con);

            if (!listaParcela.isEmpty()) {
                Iterator e = listaParcela.iterator();
                while (e.hasNext()) {
                    MovParcelaVO parcela = (MovParcelaVO) e.next();
                    pixDAO.excluirPorParcela(parcela.codigo, usuarioVO);
                    if (parcela.getAulaAvulsaDiariaVO() != null && parcela.getAulaAvulsaDiariaVO().getCodigo() != 0) {
                        periodoAcessoCliente.excluirPeriodoAcessoClienteAulaAvulsaDiaria(parcela.getAulaAvulsaDiariaVO().getCodigo().intValue());
                        aulaAvulsaDiariaDAO.excluirSemCommit(parcela.getAulaAvulsaDiariaVO().getCodigo().intValue());
                    }
                    parcelaFacade.excluirSemCommit(parcela);
                }
            }
        } finally {
            aulaAvulsaDiariaDAO = null;
            pixDAO = null;
        }
    }

    public List<MovParcelaVO> getListaMovParcela() {
        return listaMovParcela;
    }

    public void setListaMovParcela(List<MovParcelaVO> listaMovParcela) {
        this.listaMovParcela = listaMovParcela;
    }

    public UsuarioVO getResponsavelEstorno() {
        return responsavelEstorno;
    }

    public void setResponsavelEstorno(UsuarioVO responsavelEstorno) {
        this.responsavelEstorno = responsavelEstorno;
    }

    public boolean getApresentarNomePorContrato(MovParcelaVO obj) {
        return obj.getContrato() != null && obj.getContrato().getCodigo() != 0;
    }

    public boolean getApresentarNomePorVendaAvulsa(MovParcelaVO obj) {
        return obj.getVendaAvulsaVO() != null && obj.getVendaAvulsaVO().getCodigo() != 0;
    }

    public boolean getApresentarNomePorAulaAvulsaDiaria(MovParcelaVO obj) {
        return obj.getAulaAvulsaDiariaVO() != null && obj.getAulaAvulsaDiariaVO().getCodigo() != 0;
    }
    
    public boolean getApresentarNomePorTaxaPersonal(MovParcelaVO obj) {
        return obj.getPersonal() != null && obj.getPersonal().getCodigo() != 0;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public List<EstornoReciboVO> getListaEstornoRecibo() {
        return listaEstornoRecibo;
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public void setListaEstornoRecibo(List<EstornoReciboVO> listaEstornoRecibo) {
        this.listaEstornoRecibo = listaEstornoRecibo;
    }

    public MovProdutoVO getMovProdutoVO() {
        return movProdutoVO;
    }

    public Boolean getExiteOutroContratoPagouMinhaParcela() {
        return exiteOutroContratoPagouMinhaParcela;
    }

    public void setExiteOutroContratoPagouMinhaParcela(Boolean exiteOutroContratoPagouMinhaParcela) {
        this.exiteOutroContratoPagouMinhaParcela = exiteOutroContratoPagouMinhaParcela;
    }

    public void setMovProdutoVO(MovProdutoVO movProdutoVO) {
        this.movProdutoVO = movProdutoVO;
    }

    public List<MovProdutoVO> getListaMovProduto() {
        return listaMovProduto;
    }

    public void setListaMovProduto(List<MovProdutoVO> listaMovProduto) {
        this.listaMovProduto = listaMovProduto;
    }

    public List<TransacaoVO> getListaTransacoes() {
        return listaTransacoes;
    }

    public void setListaTransacoes(List<TransacaoVO> listaTransacoes) {
        this.listaTransacoes = listaTransacoes;
    }

    public CaixaVO getCaixa() {
        return caixa;
    }

    public void setCaixa(CaixaVO caixa) {
        this.caixa = caixa;
    }

    public Integer getVendaAvulsa() {
        return vendaavulsa;
    }

    public void setVendaavulsa(Integer vendaavulsa) {
        this.vendaavulsa = vendaavulsa;
    }

    public Integer getPersonal() {
        return personal;
    }

    public void setPersonal(Integer personal) {
        this.personal = personal;
    }

    public Integer getAulaAvulsaDiaria() {
        return aulaAvulsaDiaria;
    }

    public void setAulaAvulsaDiaria(Integer aulaAvulsaDiaria) {
        this.aulaAvulsaDiaria= aulaAvulsaDiaria;
    }
    
    public MovParcelaVO getParcela() {
        return parcela;
    }

    public void setParcela(MovParcelaVO parcela) {
        this.parcela = parcela;
        this.setContrato(parcela.getContrato().getCodigo());
        this.setAulaAvulsaDiaria(parcela.getAulaAvulsaDiariaVO().getCodigo());
        this.setVendaavulsa(parcela.getVendaAvulsaVO().getCodigo());
        this.setPersonal(parcela.getPersonal().getCodigo());
    }

    public List<RemessaItemVO> getListaItensRemessa() {
        return listaItensRemessa;
    }

    public void setListaItensRemessa(List<RemessaItemVO> listaItensRemessa) {
        this.listaItensRemessa = listaItensRemessa;
    }

    public List<MovParcelaVO> getListaMovParcelaVinculadas() {
        return listaMovParcelaVinculadas;
    }

    public void setListaMovParcelaVinculadas(List<MovParcelaVO> listaMovParcelaVinculadas) {
        this.listaMovParcelaVinculadas = listaMovParcelaVinculadas;
    }

    public boolean isEstornarOperadora() {
        return estornarOperadora;
    }

    public void setEstornarOperadora(boolean estornarOperadora) {
        this.estornarOperadora = estornarOperadora;
    }
}
