/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.financeiro;

import java.util.Date;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;

/**
 *
 * <AUTHOR>
 */
public class CaixaMovContaVO extends SuperTO{

    private int codigo;
    private CaixaVO caixaVo = new CaixaVO();
    private MovContaVO movContaVo = new MovContaVO();
    private String descricao = "" ;
    private double valor = 0;
    private Date dataMovimento;

    public CaixaVO getCaixaVo() {
        return caixaVo;
    }

    public String getValor_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValor());
    }
    
    public void setCaixaVo(CaixaVO caixaVo) {
        this.caixaVo = caixaVo;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public MovContaVO getMovContaVo() {
        return movContaVo;
    }

    public void setMovContaVo(MovContaVO movContaVo) {
        this.movContaVo = movContaVo;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public Date getDataMovimento() {
        return dataMovimento;
    }

    public void setDataMovimento(Date dataMovimento) {
        this.dataMovimento = dataMovimento;
    }

    

}
