package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.enumeradores.TipoContaPagarLoteEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.financeiro.enumerador.RegistrationStatusKobanaEnum;
import negocio.comuns.financeiro.enumerador.StatusPagamentoKobanaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.util.Date;

/**
 * Created by <PERSON> on 09/07/2024.
 */

public class LoteKobanaItemVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected double valor;
    protected LoteKobanaVO loteKobanaVO;
    protected String uid;
    protected StatusPagamentoKobanaEnum status;
    protected RegistrationStatusKobanaEnum registration_status;
    protected String financial_account_uid;
    protected Date created_at;
    protected Date updated_at;
    protected String codigobarras;
    protected String qrcode;
    protected MovContaVO movcontaVO;
    protected ContaBancariaFornecedorVO contaBancariaFornecedorVO;
    protected String rejected_error;
    protected Date rejected_at;

    public LoteKobanaItemVO() {
        super();
    }

    public LoteKobanaItemVO(JSONObject dados) throws Exception {
        this.valor = dados.getDouble("amount");
        this.uid = dados.getString("uid");
        this.status = StatusPagamentoKobanaEnum.obterPorValue(dados.getString("status"));
        this.registration_status = RegistrationStatusKobanaEnum.obterPorValue(dados.getString("registration_status"));
        this.financial_account_uid = dados.getString("financial_account_uid");
        this.created_at = Uteis.getDate(dados.getString("created_at"), "yyyy-MM-dd'T'HH:mm:ss");
        this.updated_at = Uteis.getDate(dados.getString("updated_at"), "yyyy-MM-dd'T'HH:mm:ss");
        //boleto normal
        if (dados.has("bank_billet")) {
            JSONObject bank_billet = dados.getJSONObject("bank_billet");
            this.codigobarras = bank_billet.getString("code");
        }
        //boleto consumo
        if (dados.has("utility")) {
            JSONObject utility = dados.getJSONObject("utility");
            this.codigobarras = utility.getString("code");
        }
        //pix
        if (dados.has("pix")) {
            JSONObject pix = dados.getJSONObject("pix");
            this.qrcode = pix.getString("qrcode");
        }
        //transferência
        if (dados.has("target")) {
            JSONObject bank_account = dados.optJSONObject("target").optJSONObject("bank_account");
            this.contaBancariaFornecedorVO = new ContaBancariaFornecedorVO(bank_account);
        }
    }

    public LoteKobanaItemVO(MovContaVO movContaVO, LoteKobanaVO loteKobanaVO) throws Exception {
        this.valor = movContaVO.getValor();
        this.loteKobanaVO = loteKobanaVO;
        this.financial_account_uid = loteKobanaVO.getFinancial_account_uid();
        this.created_at = loteKobanaVO.getCreated_at();
        this.updated_at = loteKobanaVO.getUpdated_at();
        this.movcontaVO = movContaVO;
        if (!UteisValidacao.emptyString(movContaVO.getCodigoBarras())) {
            this.codigobarras = movContaVO.getCodigoBarras();
        }
        if (!UteisValidacao.emptyString(movContaVO.getPayloadPix())) {
            this.qrcode = movContaVO.getPayloadPix();
        }
        if (movContaVO.getContaBancariaFornecedorVO() != null && loteKobanaVO.getTipoContaPagarLoteEnum().equals(TipoContaPagarLoteEnum.TRANSFERENCIA)) {
            this.contaBancariaFornecedorVO = movContaVO.getContaBancariaFornecedorVO();
        }
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getUid() {
        if (UteisValidacao.emptyString(uid)) {
            return "";
        }
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public StatusPagamentoKobanaEnum getStatus() {
        return status;
    }

    public void setStatus(StatusPagamentoKobanaEnum status) {
        this.status = status;
    }

    public void setCreated_at(Date created_at) {
        this.created_at = created_at;
    }

    public Date getUpdated_at() {
        return updated_at;
    }

    public void setUpdated_at(Date updated_at) {
        this.updated_at = updated_at;
    }

    public String getUpdated_at_Apresentar() {
        if (updated_at == null) {
            return "";
        }
        return Uteis.getDataComHora(updated_at);
    }

    public RegistrationStatusKobanaEnum getRegistration_status() {
        return registration_status;
    }

    public void setRegistration_status(RegistrationStatusKobanaEnum registration_status) {
        this.registration_status = registration_status;
    }

    public Date getCreated_at() {
        return created_at;
    }

    public String getFinancial_account_uid() {
        if (UteisValidacao.emptyString(financial_account_uid)) {
            return "";
        }
        return financial_account_uid;
    }

    public void setFinancial_account_uid(String financial_account_uid) {
        this.financial_account_uid = financial_account_uid;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public String getCodigobarras() {
        if (UteisValidacao.emptyString(codigobarras)) {
            return "";
        }
        return codigobarras;
    }

    public void setCodigobarras(String codigobarras) {
        this.codigobarras = codigobarras;
    }

    public LoteKobanaVO getLoteKobanaVO() {
        return loteKobanaVO;
    }

    public void setLoteKobanaVO(LoteKobanaVO loteKobanaVO) {
        this.loteKobanaVO = loteKobanaVO;
    }

    public String getQrcode() {
        if (UteisValidacao.emptyString(qrcode)) {
            return "";
        }
        return qrcode;
    }

    public void setQrcode(String qrcode) {
        this.qrcode = qrcode;
    }

    public MovContaVO getMovcontaVO() {
        return movcontaVO;
    }

    public void setMovcontaVO(MovContaVO movcontaVO) {
        this.movcontaVO = movcontaVO;
    }

    public String getStatus_Apresentar() {
        if (status == null) {
            return "";
        }
        return status.getNomeApresentar();
    }

    public String getRegistration_Status_Apresentar() {
        if (registration_status == null) {
            return "";
        }
        return registration_status.getDescricao();
    }

    public boolean isExibirColunaQRCode() {
        if (getLoteKobanaVO().getTipoContaPagarLoteEnum() != null && getLoteKobanaVO().getTipoContaPagarLoteEnum().equals(TipoContaPagarLoteEnum.PAYLOAD_PIX)) {
            return true;
        }
        return false;
    }

    public boolean isExibirColunaCodBarras() {
        if (getLoteKobanaVO().getTipoContaPagarLoteEnum() != null && getLoteKobanaVO().getTipoContaPagarLoteEnum().equals(TipoContaPagarLoteEnum.BOLETO)) {
            return true;
        }
        return false;
    }

    public ContaBancariaFornecedorVO getContaBancariaFornecedorVO() {
        if (contaBancariaFornecedorVO == null) {
            return new ContaBancariaFornecedorVO();
        }
        return contaBancariaFornecedorVO;
    }

    public void setContaBancariaFornecedorVO(ContaBancariaFornecedorVO contaBancariaFornecedorVO) {
        this.contaBancariaFornecedorVO = contaBancariaFornecedorVO;
    }

    public String getTitleUltAtualizacaoPagamentoExplicacao() {
        return "Exibe a data da última atualização do <b>pagamento</b>." +
                "</br> Qualquer atualização é enviada automaticamente para a Pacto pelo próprio Banco.";
    }

    public String getStyleCssStatus() {
        if (status == null) {
            return "";
        } else {
            return "background-color:" + status.getColor() + "!important;";
        }
    }

    public String getUidPagamentoCopiar() {
        return "<b>Uid do pagamento:</b> " + getUid() + " <br><br> <b>Clique para Copiar</b>";
    }

    public String getRejected_error() {
        if (UteisValidacao.emptyString(rejected_error)) {
            return "";
        }
        return rejected_error;
    }

    public void setRejected_error(String rejected_error) {
        this.rejected_error = rejected_error;
    }

    public Date getRejected_at() {
        return rejected_at;
    }

    public void setRejected_at(Date rejected_at) {
        this.rejected_at = rejected_at;
    }

    public String getMotivoRejeicao_Apresentar() {
        StringBuilder sb = new StringBuilder();
        sb.append(getStatus().getDescricao());
        if (!UteisValidacao.emptyString(rejected_error)) {
            sb.append(" - ").append(rejected_error);
        } else {
            if (getStatus().equals(StatusPagamentoKobanaEnum.REJECTED)) {
                sb.append(" - ").append("O Banco não nos informou o motivo. Verifique todas as informações da conta antes de gerar o lote.");
            } else if (getStatus().equals(StatusPagamentoKobanaEnum.CANCELED)) {
                sb.append(" - ").append("O Banco cancelou o pagamento e não nos informou o motivo");
            }
        }
        return sb.toString();
    }
}
