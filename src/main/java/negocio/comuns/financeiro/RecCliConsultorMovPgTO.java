package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.plano.enumerador.TipoProduto;

import java.util.ArrayList;
import java.util.List;

public class RecCliConsultorMovPgTO extends SuperTO {

    private Integer formaPagamento;
    private String produtosPagos;
    private List<RecCliConsultorMovProdTO> movProdutos;

    public Integer getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(Integer formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public String getProdutosPagos() {
        return produtosPagos;
    }

    public void setProdutosPagos(String produtosPagos) {
        this.produtosPagos = produtosPagos;
    }

    public List<RecCliConsultorMovProdTO> getMovProdutos() {
        return movProdutos;
    }

    public void setMovProdutos(List<RecCliConsultorMovProdTO> movProdutos) {
        this.movProdutos = movProdutos;
    }

    public List<ProdutoPago> getListaProdutosPagos() {
        List<ProdutoPago> pagos = new ArrayList<ProdutoPago>();
        if (null != getProdutosPagos()) {
            String[] pps = getProdutosPagos().split("\\|");
            for (String pp : pps) {
                if (!pp.isEmpty()) {
                    String[] it = pp.split(",");

                    ProdutoPago p = new ProdutoPago();
                    p.setProduto(Integer.parseInt(it[0]));
                    p.setTipoProduto(TipoProduto.getTipoProdutoCodigo(it[1]));
                    p.setContrato(Integer.parseInt(it[2]));
                    p.setValor(Double.parseDouble(it[3]));

                    pagos.add(p);
                }
            }
        }

        return pagos;
    }
}
