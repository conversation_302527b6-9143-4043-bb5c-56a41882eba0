package negocio.comuns.crm;

import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade AberturaMeta. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */

public class AberturaMetaVO extends SuperVO {

    public String nomeGrupoColaboradorVenda;
    public String nomeGrupoColaboradorRetencao;
    public String nomeGrupoColaboradorEstudio;
    protected UsuarioVO colaboradorResponsavel;
    protected UsuarioVO responsavelCadastro;
    protected Date dia;
    private Date diaFechamento;
    private Boolean metaEmAberto = true;
    private Boolean aberturaRetroativa;
    private List<FecharMetaVO> fecharMetaVosVenda;
    private List<FecharMetaVO> fecharMetaVosRetencao;
    private List<FecharMetaVO> fecharMetaVosLead;
    private List<FecharMetaVO> fecharMetaVosEstudio = new ArrayList<FecharMetaVO>();
    private UsuarioVO responsavelLiberacaoTrocaColaboradorResponsavel;
    //metodos q não seram persistidos no banco apenas para controle
    private List<GrupoColaboradorVO> grupoColaboradorListaVenda;
    private List<GrupoColaboradorVO> grupoColaboradorListaRetencao;
    private List<GrupoColaboradorVO> grupoColaboradorListaEstudio;
    private ConfiguracaoSistemaCRMVO configuracaoSistemaVO;
    private Boolean existeMetaParaParticipante;
    private String nomeParticipanteSelecionado;
    private String nomeParticipanteSelecionadoRetencao;
    // lista criada para receber as 2 duas listas(Indicador Vendas e Indicador Retenção)
    private List<FecharMetaVO> listaFechametoDia;
    private Double totalMetaDia;
    //campo usado para fazer consulta de feriado
    private EmpresaVO empresaVO;
    private String justificativa = "";
    private List<FecharMetaVO> fecharMetaVosVendaApresentar;
    ///atributos transientes
    private String codigoClientesObjecaoDefinitiva;
    private String codigoPassivosObjecaoDefinitiva;
    private String codigoIndicadosObjecaoDefinitiva;
    private String codigoPessoaObjecaoDefinitiva;
    private Boolean metasAgrupadasColaboradorIndisponivel;
    private FasesCRMEnum faseEspecifica;

    /**
     * Construtor padrão da classe <code>AberturaMeta</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public AberturaMetaVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar a unicidade dos dados de um objeto da classe <code>AberturaMetaVO</code>.
     */
    public static void validarUnicidade(List<AberturaMetaVO> lista, AberturaMetaVO obj) throws ConsistirException {
        for (AberturaMetaVO repetido : lista) {
        }
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>AberturaMetaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                            o atributo e o erro ocorrido.
     */
    public static void validarDados(AberturaMetaVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getColaboradorResponsavel().getNome().equals("")) {
            throw new ConsistirException("O campo Colaborador Responsável não pode ser vazio !");
        }
    }
     /**
     * Método responsavel por adquirir o caminho onde se encontra o relatorio jasper
     *
     * @param idEntidade idEntidade
     * <AUTHOR>
     */
    public static String getIsDesignIReportRelatorio(String idEntidade) {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "crm" + File.separator + idEntidade + ".jrxml");
    }

    /**
     * Método responsavel por adquirir o caminho onde se encontra o relatorio jasper
     *
     * @param nomeRel nome do relatorio
     * <AUTHOR>
     */
    public static String getIsDesignIReportRelatorioExcel(String nomeRel) {
        return ("designRelatorio" + File.separator + "crm" + File.separator + nomeRel);
    }

    public void addMeta(AberturaMetaVO meta) {
        if (this.getFecharMetaVosVenda().isEmpty()) {
            this.getFecharMetaVosVenda().addAll(meta.getFecharMetaVosVenda());
        } else
            for (FecharMetaVO cmeta : meta.getFecharMetaVosVenda()) {
                for (FecharMetaVO fmeta : this.getFecharMetaVosVenda()) {
                    if (cmeta.getApresentarMetaAgendamento() && fmeta.getApresentarMetaAgendamento()) {
                        copiarMetas(cmeta, fmeta);
                    } else if (cmeta.getApresentarMetaVinteQuatroHoras() && fmeta.getApresentarMetaVinteQuatroHoras()) {
                        copiarMetas(cmeta, fmeta);
                    } else if (cmeta.getApresentarMetaRenovacao() && fmeta.getApresentarMetaRenovacao()) {
                        copiarMetas(cmeta, fmeta);
                    } else if (cmeta.getApresentarMetaPassivo() && fmeta.getApresentarMetaPassivo()) {
                        copiarMetas(cmeta, fmeta);
                    } else if (cmeta.getApresentarMetaIndicados() && fmeta.getApresentarMetaIndicados()) {
                        copiarMetas(cmeta, fmeta);
                    } else if (cmeta.getApresentarMetaQtdeVendas() && fmeta.getApresentarMetaQtdeVendas()) {
                        copiarMetas(cmeta, fmeta);
                    } else if (cmeta.getApresentarMetaFaturamento() && fmeta.getApresentarMetaFaturamento()) {
                        copiarMetas(cmeta, fmeta);
                    } else if (cmeta.getApresentarMetaPosVenda() && fmeta.getApresentarMetaPosVenda()) {
                        copiarMetas(cmeta, fmeta);
                    } else if (cmeta.getApresentarMetaGrupoRisco() && fmeta.getApresentarMetaGrupoRisco()) {
                        copiarMetas(cmeta, fmeta);
                    } else if (cmeta.getApresentarMetaFaltosos() && fmeta.getApresentarMetaFaltosos()) {
                        copiarMetas(cmeta, fmeta);
                    } else if (cmeta.getApresentarMetaPerda() && fmeta.getApresentarMetaPerda()) {
                        copiarMetas(cmeta, fmeta);
                    } else if (cmeta.getApresentarMetaAniversariante() && fmeta.getApresentarMetaAniversariante()) {
                        copiarMetas(cmeta, fmeta);
                    }
                }
            }
        this.getColaboradorResponsavel().setNome(this.getColaboradorResponsavel().getNome() + " - " + meta.getColaboradorResponsavel().getNome());
        this.getResponsavelCadastro().setNome(this.getResponsavelCadastro().getNome() + " - " + meta.getResponsavelCadastro().getNome());
    }

    /**
     * Responsável por
     *
     * <AUTHOR>
     * 19/05/2011
     */
    private void copiarMetas(FecharMetaVO cmeta, FecharMetaVO fmeta) {
        cmeta.setMeta(cmeta.getMeta() + fmeta.getMeta());
        cmeta.setMetaAtingida(cmeta.getMetaAtingida() + fmeta.getMetaAtingida());
        cmeta.setPorcentagem(cmeta.getPorcentagem() + fmeta.getPorcentagem());
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        if (!Uteis.realizarUpperCaseDadosAntesPersistencia) {
            return;
        }
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(null);
        setColaboradorResponsavel(new UsuarioVO());
        setResponsavelCadastro(new UsuarioVO());
        setDia(negocio.comuns.utilitarias.Calendario.hoje());
        setFecharMetaVosVenda(new ArrayList<FecharMetaVO>());
        setFecharMetaVosRetencao(new ArrayList<FecharMetaVO>());
        setMetaEmAberto(true);
        setResponsavelLiberacaoTrocaColaboradorResponsavel(new UsuarioVO());
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>FecharMetaDetalhadoVO</code> ao List
     * <code>fecharMetaDetalhadoVOs</code>. Utiliza o atributo padrão de
     * consulta da classe <code>FecharMetaDetalhado</code> -
     * getCliente().getCodigo() - como identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>FecharMetaDetalhadoVO</code> que será
     *            adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjFecharMetaVenda(FecharMetaVO obj) throws Exception {
        FecharMetaVO.validarDados(obj);
        obj.setAberturaMetaVO(this);
        int index = 0;
        Iterator i = getFecharMetaVosVenda().iterator();
        while (i.hasNext()) {
            FecharMetaVO objExistente = (FecharMetaVO) i.next();
            if (objExistente.getIdentificadorMeta().equals(obj.getIdentificadorMeta())) {
                getFecharMetaVosVenda().set(index, obj);
                return;
            }
            index++;
        }
        getFecharMetaVosVenda().add(obj);
    }

    public void adicionarObjFecharMetaEstudio(FecharMetaVO obj) throws Exception {
        FecharMetaVO.validarDados(obj);
        obj.setAberturaMetaVO(this);
        int index = 0;
        Iterator i = getFecharMetaVosEstudio().iterator();
        while (i.hasNext()) {
            FecharMetaVO objExistente = (FecharMetaVO) i.next();
            if (objExistente.getIdentificadorMeta().equals(obj.getIdentificadorMeta())) {
                getFecharMetaVosEstudio().set(index, obj);
                return;
            }
            index++;
        }
        getFecharMetaVosEstudio().add(obj);
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe
     * <code>FecharMetaDetalhadoVO</code> ao List
     * <code>fecharMetaDetalhadoVOs</code>. Utiliza o atributo padrão de
     * consulta da classe <code>FecharMetaDetalhado</code> -
     * getCliente().getCodigo() - como identificador (key) do objeto no List.
     *
     * @param obj Objeto da classe <code>FecharMetaDetalhadoVO</code> que será
     *            adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjFecharMetaRetencao(FecharMetaVO obj) throws Exception {
        FecharMetaVO.validarDados(obj);
        obj.setAberturaMetaVO(this);
        int index = 0;
        Iterator i = getFecharMetaVosRetencao().iterator();
        while (i.hasNext()) {
            FecharMetaVO objExistente = (FecharMetaVO) i.next();
            if (objExistente.getIdentificadorMeta().equals(obj.getIdentificadorMeta())) {
                getFecharMetaVosRetencao().set(index, obj);
                return;
            }
            index++;
        }
        getFecharMetaVosRetencao().add(obj);
    }

    public Date getDia() {
        if (dia == null) {
            dia = negocio.comuns.utilitarias.Calendario.hoje();
        }
        return (dia);
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public String getDia_Apresentar() {
        return (Uteis.getData(dia));
    }

    public UsuarioVO getResponsavelCadastro() {
        if (responsavelCadastro == null) {
            responsavelCadastro = new UsuarioVO();
        }
        return (responsavelCadastro);
    }

    public void setResponsavelCadastro(UsuarioVO responsavelCadastro) {
        this.responsavelCadastro = responsavelCadastro;
    }

    public UsuarioVO getColaboradorResponsavel() {
        if (colaboradorResponsavel == null) {
            colaboradorResponsavel = new UsuarioVO();
        }
        return (colaboradorResponsavel);
    }

    public void setColaboradorResponsavel(UsuarioVO colaboradorResponsavel) {
        this.colaboradorResponsavel = colaboradorResponsavel;
    }

    public String getMetaEmAberto_Apresentar() {
        if (getMetaEmAberto() == null) {
            return "";
        } else if (getMetaEmAberto()) {
            return "SIM";
        }
        return "NÃO";
    }

    public Boolean getMetaEmAberto() {
        return metaEmAberto;
    }

    public void setMetaEmAberto(Boolean metaEmAberto) {
        this.metaEmAberto = metaEmAberto;
    }

    public List<GrupoColaboradorVO> getGrupoColaboradorListaVenda() {
        if (grupoColaboradorListaVenda == null) {
            grupoColaboradorListaVenda = new ArrayList<GrupoColaboradorVO>();
        }
        return grupoColaboradorListaVenda;
    }

    public void setGrupoColaboradorListaVenda(List<GrupoColaboradorVO> grupoColaboradorListaVenda) {
        this.grupoColaboradorListaVenda = grupoColaboradorListaVenda;
    }

    public List<GrupoColaboradorVO> getGrupoColaboradorListaRetencao() {
        if (grupoColaboradorListaRetencao == null) {
            grupoColaboradorListaRetencao = new ArrayList<GrupoColaboradorVO>();
        }
        return grupoColaboradorListaRetencao;
    }

    public void setGrupoColaboradorListaRetencao(List<GrupoColaboradorVO> grupoColaboradorListaRetencao) {
        this.grupoColaboradorListaRetencao = grupoColaboradorListaRetencao;
    }

    public List<GrupoColaboradorVO> getGrupoColaboradorListaEstudio() {
        if (grupoColaboradorListaEstudio == null) {
            grupoColaboradorListaEstudio = new ArrayList<GrupoColaboradorVO>();
        }
        return grupoColaboradorListaEstudio;
    }

    public void setGrupoColaboradorListaEstudio(List<GrupoColaboradorVO> grupoColaboradorListaEstudio) {
        this.grupoColaboradorListaEstudio = grupoColaboradorListaEstudio;
    }
    
    

    public UsuarioVO getResponsavelLiberacaoTrocaColaboradorResponsavel() {
        return responsavelLiberacaoTrocaColaboradorResponsavel;
    }

    public void setResponsavelLiberacaoTrocaColaboradorResponsavel(
            UsuarioVO responsavelLiberacaoTrocaColaboradorResponsavel) {
        this.responsavelLiberacaoTrocaColaboradorResponsavel = responsavelLiberacaoTrocaColaboradorResponsavel;
    }

    public ConfiguracaoSistemaCRMVO getConfiguracaoSistemaVO() {
        if (configuracaoSistemaVO == null) {
            configuracaoSistemaVO = new ConfiguracaoSistemaCRMVO();
        }
        return configuracaoSistemaVO;
    }

    public void setConfiguracaoSistemaVO(
            ConfiguracaoSistemaCRMVO configuracaoSistemaVO) {
        this.configuracaoSistemaVO = configuracaoSistemaVO;
    }

    public List<FecharMetaVO> getFecharMetaVosVenda() {
        if (fecharMetaVosVenda == null) {
            fecharMetaVosVenda = new ArrayList<FecharMetaVO>();
        }

        return fecharMetaVosVenda;
    }

    public void setFecharMetaVosVenda(List<FecharMetaVO> fecharMetaVosVenda) {
        this.fecharMetaVosVenda = fecharMetaVosVenda;
    }

    public List<FecharMetaVO> getFecharMetaVosRetencao() {
        if (fecharMetaVosRetencao == null) {
            fecharMetaVosRetencao = new ArrayList<FecharMetaVO>();
        }
        return fecharMetaVosRetencao;
    }

    public void setFecharMetaVosRetencao(List<FecharMetaVO> fecharMetaVosRetencao) {
        this.fecharMetaVosRetencao = fecharMetaVosRetencao;
    }

    public Boolean getExisteMetaParaParticipante() {
        if (existeMetaParaParticipante == null) {
            existeMetaParaParticipante = false;
        }
        return existeMetaParaParticipante;
    }

    public void setExisteMetaParaParticipante(Boolean existeMetaParaParticipante) {
        this.existeMetaParaParticipante = existeMetaParaParticipante;
    }

    public String getNomeParticipanteSelecionado() {
        if (nomeParticipanteSelecionado == null) {
            nomeParticipanteSelecionado = "";
        }
        return nomeParticipanteSelecionado;
    }

    public void setNomeParticipanteSelecionado(String nomeParticipanteSelecionado) {
        this.nomeParticipanteSelecionado = nomeParticipanteSelecionado;
    }

    public String getNomeGrupoColaboradorVenda() {
        if (nomeGrupoColaboradorVenda == null) {
            nomeGrupoColaboradorVenda = "";
        }
        return nomeGrupoColaboradorVenda;
    }

    public void setNomeGrupoColaboradorVenda(String nomeGrupoColaboradorVenda) {
        this.nomeGrupoColaboradorVenda = nomeGrupoColaboradorVenda;
    }

    public String getNomeGrupoColaboradorRetencao() {
        if (nomeGrupoColaboradorRetencao == null) {
            nomeGrupoColaboradorRetencao = "";
        }
        return nomeGrupoColaboradorRetencao;
    }

    public void setNomeGrupoColaboradorRetencao(String nomeGrupoColaboradorRetencao) {
        this.nomeGrupoColaboradorRetencao = nomeGrupoColaboradorRetencao;
    }
    
    public String getNomeGrupoColaboradorEstudio() {
        if (nomeGrupoColaboradorEstudio == null) {
            nomeGrupoColaboradorEstudio = "";
        }
        return nomeGrupoColaboradorEstudio;
    }

    public void setNomeGrupoColaboradorEstudio(String nomeGrupoColaboradorEstudio) {
        this.nomeGrupoColaboradorEstudio = nomeGrupoColaboradorEstudio;
    }

    public String getNomeParticipanteSelecionadoRetencao() {
        if (nomeParticipanteSelecionadoRetencao == null) {
            nomeParticipanteSelecionadoRetencao = "";
        }
        return nomeParticipanteSelecionadoRetencao;
    }

    public void setNomeParticipanteSelecionadoRetencao(String nomeParticipanteSelecionadoRetencao) {
        this.nomeParticipanteSelecionadoRetencao = nomeParticipanteSelecionadoRetencao;
    }

    public List<FecharMetaVO> getListaFechametoDia() {
        if (listaFechametoDia == null) {
            listaFechametoDia = new ArrayList<FecharMetaVO>();
        }
   //     Ordenacao.ordenarLista(listaFechametoDia,"ordemTotalizadorMeta");
        return listaFechametoDia;
    }

    public void setListaFechametoDia(List<FecharMetaVO> listaFechametoDia) {
        this.listaFechametoDia = listaFechametoDia;
    }

    public Double getTotalMetaDia() {
        //Soma todas Metas e Metas Atigindas e faz  (somaMetaAtingida * 100 )/somalMeta
        totalMetaDia = 0d;
        //double cont = 0;
        double somalMeta=0;
        double somaMetaAtingida=0;
        for (FecharMetaVO fecharMeta : this.getListaFechametoDia()) {
            if (fecharMeta.getMeta() > 0.0 && !fecharMeta.getFase().getSigla().equals(FasesCRMEnum.INDICACOES.getSigla())) {
                somaMetaAtingida +=fecharMeta.getMetaAtingida();
                somalMeta+=fecharMeta.getMeta();
                //totalMetaDia += fecharMeta.getPorcentagem();
          //      cont++;
            }
        }

   //     if (cont == 0) {
    //       return 0d;
    //    }
   //     totalMetaDia = totalMetaDia / cont;
     totalMetaDia = somalMeta == 0.0 ? 100.0 : (somaMetaAtingida * 100 )/somalMeta;
        return totalMetaDia;
    }

    public void setTotalMetaDia(Double totalMetaDia) {
        this.totalMetaDia = totalMetaDia;
    }

    public Date getDiaFechamento() {
        return diaFechamento;
    }

    public void setDiaFechamento(Date diaFechamento) {
        this.diaFechamento = diaFechamento;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    @Override
    public int hashCode() {
        return codigo != null ? codigo.hashCode() : 0;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        AberturaMetaVO other = (AberturaMetaVO) obj;
        //comparar data
        return Calendario.getDataComHoraZerada(this.getDia()).equals(Calendario.getDataComHoraZerada(other.getDia()))
                && this.getColaboradorResponsavel().getCodigo().equals(other.getColaboradorResponsavel().getCodigo());
    }

    public AberturaMetaVO getClone() {
        AberturaMetaVO meta = new AberturaMetaVO();
        meta.setCodigo(this.getCodigo());
        meta.getColaboradorResponsavel().setCodigo(this.getColaboradorResponsavel().getCodigo());
        meta.getColaboradorResponsavel().setNome(this.getColaboradorResponsavel().getNome());
        meta.getResponsavelCadastro().setNome(this.getResponsavelCadastro().getNome());
        meta.setMetaEmAberto(this.getMetaEmAberto());
        meta.setDia(this.getDia());
        meta.setDiaFechamento(this.getDiaFechamento());
        return meta;

    }

    public Boolean getAberturaRetroativa() {
        if (aberturaRetroativa == null) {
            aberturaRetroativa = Boolean.FALSE;
        }
        return aberturaRetroativa;
    }

    public void setAberturaRetroativa(Boolean aberturaretroativa) {
        this.aberturaRetroativa = aberturaretroativa;
    }

    public List<FecharMetaVO> getFecharMetaVosEstudio() {
        return fecharMetaVosEstudio;
    }

    public void setFecharMetaVosEstudio(List<FecharMetaVO> fecharMetaVosEstudio) {
        this.fecharMetaVosEstudio = fecharMetaVosEstudio;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public List<FecharMetaVO> getFecharMetaVosVendaApresentar() {
        fecharMetaVosVendaApresentar = new ArrayList<FecharMetaVO>();
        for (FecharMetaVO fecharMetaVO : fecharMetaVosVenda) {
            if (!fecharMetaVO.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla())
                    && !fecharMetaVO.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_EX_ALUNOS.getSigla())
                    && !fecharMetaVO.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS.getSigla())
                    && !fecharMetaVO.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_INDICADOS.getSigla())
                    && !fecharMetaVO.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_DESISTENTES.getSigla())) {
                fecharMetaVosVendaApresentar.add(fecharMetaVO);
            }
        }
        try {
            Ordenacao.ordenarLista(fecharMetaVosVendaApresentar, "ordemTotalizadorMeta");
        }catch (Exception e){}
        return fecharMetaVosVendaApresentar;
    }

    public void setFecharMetaVosVendaApresentar(List<FecharMetaVO> fecharMetaVosVendaApresentar) {
        this.fecharMetaVosVendaApresentar = fecharMetaVosVendaApresentar;
    }

    public String getCodigoClientesObjecaoDefinitiva() {
        if (codigoClientesObjecaoDefinitiva == null){
            codigoClientesObjecaoDefinitiva = "9999999";
        }
        return codigoClientesObjecaoDefinitiva;
    }

    public void setCodigoClientesObjecaoDefinitiva(String codigoClientesObjecaoDefinitiva) {
        this.codigoClientesObjecaoDefinitiva = codigoClientesObjecaoDefinitiva;
    }

    public String getCodigoPassivosObjecaoDefinitiva() {
        if (codigoPassivosObjecaoDefinitiva == null){
            codigoPassivosObjecaoDefinitiva = "9999999";
        }
        return codigoPassivosObjecaoDefinitiva;
    }

    public void setCodigoPassivosObjecaoDefinitiva(String codigoPassivosObjecaoDefinitiva) {
        this.codigoPassivosObjecaoDefinitiva = codigoPassivosObjecaoDefinitiva;
    }

    public String getCodigoIndicadosObjecaoDefinitiva() {
        if (codigoIndicadosObjecaoDefinitiva == null){
            codigoIndicadosObjecaoDefinitiva = "9999999";
        }
        return codigoIndicadosObjecaoDefinitiva;
    }

    public void setCodigoIndicadosObjecaoDefinitiva(String codigoIndicadosObjecaoDefinitiva) {
        this.codigoIndicadosObjecaoDefinitiva = codigoIndicadosObjecaoDefinitiva;
    }

    public String getCodigoPessoaObjecaoDefinitiva() {
        if (codigoPessoaObjecaoDefinitiva == null){
            codigoPessoaObjecaoDefinitiva = "9999999";
        }
        return codigoPessoaObjecaoDefinitiva;
    }

    public void setCodigoPessoaObjecaoDefinitiva(String codigoPessoaObjecaoDefinitiva) {
        this.codigoPessoaObjecaoDefinitiva = codigoPessoaObjecaoDefinitiva;
    }


    public Boolean getMetasAgrupadasColaboradorIndisponivel() {
        if (metasAgrupadasColaboradorIndisponivel == null){
            metasAgrupadasColaboradorIndisponivel = false;
        }
        return metasAgrupadasColaboradorIndisponivel;
    }

    public void setMetasAgrupadasColaboradorIndisponivel(Boolean metasAgrupadasColaboradorIndisponivel) {
        this.metasAgrupadasColaboradorIndisponivel = metasAgrupadasColaboradorIndisponivel;
    }

    public List<FecharMetaVO> getFecharMetaVosLead() {
        return fecharMetaVosLead;
    }

    public void setFecharMetaVosLead(List<FecharMetaVO> fecharMetaVosLead) {
        this.fecharMetaVosLead = fecharMetaVosLead;
    }

    public FasesCRMEnum getFaseEspecifica() {
        return faseEspecifica;
    }

    public void setFaseEspecifica(FasesCRMEnum faseEspecifica) {
        this.faseEspecifica = faseEspecifica;
    }
}
