package negocio.comuns.crm;
import java.util.Date;
import java.util.List;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade Evento. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
*/

public class EventoVO extends SuperVO {
	
    protected Integer codigo;
    protected String descricao;
    protected String status;
    private Date dataLancamento;
    private Date vigenciaInicial;
    private Date vigenciaFinal;
    private String observacao;
    private boolean marcado=false;
	
    /**
     * Construtor padrão da classe <code>Evento</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
    */
    public EventoVO() {
        super();
        inicializarDados();
    }
     
	
    /**
     * Operação responsável por validar a unicidade dos dados de um objeto da classe <code>EventoVO</code>.
    */
    public static void validarUnicidade(List<EventoVO> lista, EventoVO obj) throws ConsistirException {
        for (EventoVO repetido : lista) {
        }
    }
    /**
     * Operação responsável por validar os dados de um objeto da classe <code>EventoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
    */
    public static void validarDados(EventoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if(obj.getDescricao().equals("")){
            throw new ConsistirException("O campo DESCRIÇÃO deve ser informado !");
        }
        /*if(obj.getStatus().equals("")){
            throw  new ConsistirException("Escolha um STATUS !");
        }*/
        if (obj.getVigenciaInicial() == null ){
            throw  new ConsistirException("O campo 'Vigência inicial' deve ser informado");
        }

        if (obj.getVigenciaFinal() == null ){
            throw  new ConsistirException("O campo 'Vigência final' deve ser informado");
        }
        if ((obj.getVigenciaInicial() != null) && (obj.getVigenciaFinal() != null)){
            if (Calendario.menor(obj.getVigenciaFinal(),obj.getVigenciaInicial())){
                throw  new ConsistirException("O campo 'Vigência inicial' deve ser menor que o campo 'Vigência final'");
            }
        }

    }
     
    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
    */
    public void realizarUpperCaseDados() {
        setDescricao( getDescricao().toUpperCase() );
    }
     
    /**
     * Operação reponsável por inicializar os atributos da classe.
    */
    public void inicializarDados() {
    }

        public String getTipoStatus_Apresentar() {
        if (status.equals(null)) {
            return "";
        }
        return Dominios.getTipoStatus().get(status);
    }

	

    public String getStatus() {
    	if(status == null){
    		status = "";
    	}
        return (status);
    }
    public String status() {
        if (status == null) {
            status = "";
        }
        return (status);
    }
     
    public void setStatus( String status ) {
        this.status = status;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return (descricao);
    }
     
    public void setDescricao( String descricao ) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = new Integer(0);
        }
        return (codigo);
    }
     
    public void setCodigo( Integer codigo ) {
        this.codigo = codigo;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getVigenciaInicial() {
        return vigenciaInicial;
    }

    public void setVigenciaInicial(Date vigenciaInicial) {
        this.vigenciaInicial = vigenciaInicial;
    }

    public Date getVigenciaFinal() {
        return vigenciaFinal;
    }

    public void setVigenciaFinal(Date vigenciaFinal) {
        this.vigenciaFinal = vigenciaFinal;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getDataLancamento_Apresentar(){
        if (this.dataLancamento != null){
            return Uteis.getData(this.dataLancamento);
        }
        return "";
    }

    public boolean isMarcado() {
        return marcado;
    }

    public void setMarcado(boolean marcado) {
        this.marcado = marcado;
    }
}