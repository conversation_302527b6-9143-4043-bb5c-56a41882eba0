package negocio.comuns.crm;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

public class IndicadoVO extends SuperVO {

    private Integer codigo;
    private String nomeIndicado;
    private String telefoneIndicado;
    private String telefone;
    private String email;
    private int bounced;
    private IndicacaoVO indicacaoVO;
    private ClienteVO clienteVO;
    private EmpresaVO empresaVO;
    private Boolean contatoRealizado = false;
    private OrigemSistemaEnum origemSistemaEnum = OrigemSistemaEnum.ZW;
    private Date dataLancamento;
    private ObjecaoVO objecao;
    private boolean lead = false;
    private String cpf;

    public IndicadoVO() {
        super();
        inicializarDados();
    }

    public void inicializarDados() {
    }

    public void realizarUpperCaseDados() {
        setNomeIndicado(getNomeIndicado().toUpperCase());
        //setTelefoneIndicado(getTelefoneIndicado().toUpperCase());
    }

    public static void validarDados(IndicadoVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getNomeIndicado().equals("")) {
            throw new ConsistirException("O campo NOME INDICADO não pode ser vazio !");
        }
        if (obj.getTelefone().equals("") && (obj.getTelefoneIndicado().equals(""))) {
            throw new ConsistirException("Informe ao menos um número de telefone !");
        }

    }

    /**
     * @return the nomeIndicado
     */
    public String getNomeIndicado() {
        if (nomeIndicado == null) {
            nomeIndicado = "";
        }
        return (nomeIndicado);
    }

    public void setNomeIndicado(String nomeIndicado) {
        this.nomeIndicado = nomeIndicado;
    }
    public String getTelefoneIndicado() {
        if (telefoneIndicado == null) {
            telefoneIndicado = "";
        }
        return (telefoneIndicado);
    }

    public void setTelefoneIndicado(String telefoneIndicado) {
        this.telefoneIndicado = telefoneIndicado;
    }

    /**
     * @return the telefone
     */
    public String getTelefone() {
        if (telefone == null) {
            telefone = "";
        }
        return telefone;
    }

    /**
     * @param telefone the telefone to set
     */
    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    /**
     * @return the email
     */
    public String getEmail() {
        if (email == null) {
            email = "";
        }
        return email;
    }

    /**
     * @param email the email to set
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * @return the codigo
     */
    public Integer getCodigo() {
        if (codigo == null) {
            codigo = new Integer(0);
        }
        return codigo;
    }

    /**
     * @param codigo the codigo to set
     */
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the indicacaoVO
     */
    public IndicacaoVO getIndicacaoVO() {
        if (indicacaoVO == null) {
            indicacaoVO = new IndicacaoVO();
        }
        return indicacaoVO;
    }

    /**
     * @param indicacaoVO the indicacaoVO to set
     */
    public void setIndicacaoVO(IndicacaoVO indicacaoVO) {
        this.indicacaoVO = indicacaoVO;
    }

    public ClienteVO getClienteVO() {
        if (clienteVO == null) {
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    /**
     * @param empresaVO the empresaVO to set
     */
    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    /**
     * @return the empresaVO
     */
    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public Boolean getContatoRealizado() {
        return contatoRealizado;
    }
    public String getContatoRealizado_Apresentar(){
        if(contatoRealizado)
           return "Contato Realizado!";
        else
            return "Contato não Realizado!";
    }
    public String getStiloIconeContato(){
        if(contatoRealizado)
            return "font-size:22px;color:#24771A";
        else
            return "font-size:22px;color:#BB1F1F";
    }
    public void setContatoRealizado(Boolean contatoRealizado) {
        this.contatoRealizado = contatoRealizado;
    }

    public String getTelefones() {
        StringBuilder telefones = new StringBuilder();
        if (!telefone.isEmpty()) {
            if (Uteis.validarTelefoneCelular(telefone)) {
                telefones.append(telefone);
            }
        }

        if (!telefoneIndicado.isEmpty()) {
            if (Uteis.validarTelefoneCelular(telefoneIndicado)) {
                if (!telefones.toString().isEmpty()) {
                    telefones.append(";" + telefoneIndicado);
                } else {
                    telefones.append(telefoneIndicado);
                }
            }
            return telefones.toString();
        }
        return "";
    }

    public OrigemSistemaEnum getOrigemSistemaEnum() {
        return origemSistemaEnum;
    }

    public void setOrigemSistemaEnum(OrigemSistemaEnum origemSistemaEnum) {
        this.origemSistemaEnum = origemSistemaEnum;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getNomeClienteIndicou() {
        return getIndicacaoVO().getClienteQueIndicou().getPessoa().getNome();
    }

    public String getNomeColaboradorIndicou() {
        return getIndicacaoVO().getColaboradorQueIndicou().getPessoa().getNome();
    }

    public String getEventoIndicacao() {
        return getIndicacaoVO().getEvento().getDescricao();
    }

    public String getResponsavelCadastro() {
        return getIndicacaoVO().getResponsavelCadastro().getNome();
    }

    public String getDataLancamento_Apresentar() {
        return Uteis.getDataComHHMM(getDataLancamento());
    }

    public ObjecaoVO getObjecao() {
        if (objecao == null) {
            objecao = new ObjecaoVO();
        }
        return objecao;
    }

    public void setObjecao(ObjecaoVO objecao) {
        this.objecao = objecao;
    }

    public boolean isLead() {
        return lead;
    }

    public void setLead(boolean lead) {
        this.lead = lead;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public int getBounced() {
        return bounced;
    }

    public void setBounced(int bounced) {
        this.bounced = bounced;
    }
}
