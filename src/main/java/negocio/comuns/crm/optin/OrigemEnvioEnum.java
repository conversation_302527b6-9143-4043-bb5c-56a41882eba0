package negocio.comuns.crm.optin;

/**
 *
 * <AUTHOR>
 */
public enum OrigemEnvioEnum {

    WAGI(0,"Origem via Wagi"),
    SMTP(1,"Origem via SMTP");

    private Integer codigo;
    private String descricao;

    OrigemEnvioEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
