
package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for direcaoAcessoEnum.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="direcaoAcessoEnum">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="DA_ENTRADA"/>
 *     &lt;enumeration value="DA_SAIDA"/>
 *     &lt;enumeration value="DA_INDEFINIDA"/>
 *     &lt;enumeration value="DA_OESPERADO"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "direcaoAcessoEnum")
@XmlEnum
public enum DirecaoAcessoEnum {

    DA_ENTRADA,
    DA_SAIDA,
    DA_INDEFINIDA,
    DA_OESPERADO;

    public String value() {
        return name();
    }

    public static DirecaoAcessoEnum fromValue(String v) {
        return valueOf(v);
    }

}
