
package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the negocio.comuns.acesso.webservice.client package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _MontarDadosOfflinePessoa_QNAME = new QName("http://webservice.acesso/", "montarDadosOfflinePessoa");
    private final static QName _RegistrarVersaoAcesso_QNAME = new QName("http://webservice.acesso/", "registrarVersaoAcesso");
    private final static QName _MontarListaAcessosPessoasComFotoResponse_QNAME = new QName("http://webservice.acesso/", "montarListaAcessosPessoasComFotoResponse");
    private final static QName _ValidarAcessoClientePelaMatriculaResponse_QNAME = new QName("http://webservice.acesso/", "validarAcessoClientePelaMatriculaResponse");
    private final static QName _RegistrarCodigoNaoPossuiMaisDeumaDigital_QNAME = new QName("http://webservice.acesso/", "registrarCodigoNaoPossuiMaisDeumaDigital");
    private final static QName _BuscarConfigLocalAcesso_QNAME = new QName("http://webservice.acesso/", "buscarConfigLocalAcesso");
    private final static QName _BuscarCodigoAcesso_QNAME = new QName("http://webservice.acesso/", "buscarCodigoAcesso");
    private final static QName _UltimoDadosOfflineGerado_QNAME = new QName("http://webservice.acesso/", "ultimoDadosOfflineGerado");
    private final static QName _RegistrarCodigoPossuiMaisDeumaDigital_QNAME = new QName("http://webservice.acesso/", "registrarCodigoPossuiMaisDeumaDigital");
    private final static QName _RegistrarAcesso_QNAME = new QName("http://webservice.acesso/", "registrarAcesso");
    private final static QName _ValidarAcessoPeloCodigoAcessoAvaliandoIntegracao_QNAME = new QName("http://webservice.acesso/", "validarAcessoPeloCodigoAcessoAvaliandoIntegracao");
    private final static QName _BuscarConfigIntegracaoAcesso_QNAME = new QName("http://webservice.acesso/", "buscarConfigIntegracaoAcesso");
    private final static QName _ConsultarClientesPeloNome_QNAME = new QName("http://webservice.acesso/", "consultarClientesPeloNome");
    private final static QName _PegarFotoPessoa_QNAME = new QName("http://webservice.acesso/", "pegarFotoPessoa");
    private final static QName _RegistrarCodigoPossuiMaisDeumaDigitalResponse_QNAME = new QName("http://webservice.acesso/", "registrarCodigoPossuiMaisDeumaDigitalResponse");
    private final static QName _BuscarCodigoAcessoResponse_QNAME = new QName("http://webservice.acesso/", "buscarCodigoAcessoResponse");
    private final static QName _TemDadosOfflineGerados_QNAME = new QName("http://webservice.acesso/", "temDadosOfflineGerados");
    private final static QName _ValidarAcessoPeloCodigoAcessoAvaliandoIntegracaoResponse_QNAME = new QName("http://webservice.acesso/", "validarAcessoPeloCodigoAcessoAvaliandoIntegracaoResponse");
    private final static QName _MontarDadosOffline_QNAME = new QName("http://webservice.acesso/", "montarDadosOffline");
    private final static QName _MontarDadosOfflinePessoaResponse_QNAME = new QName("http://webservice.acesso/", "montarDadosOfflinePessoaResponse");
    private final static QName _RegistrarVersaoAcessoResponse_QNAME = new QName("http://webservice.acesso/", "registrarVersaoAcessoResponse");
    private final static QName _GetVersaoSistemaResponse_QNAME = new QName("http://webservice.acesso/", "getVersaoSistemaResponse");
    private final static QName _GravarArquivoLocal_QNAME = new QName("http://webservice.acesso/", "gravarArquivoLocal");
    private final static QName _GravarSenhaIntegracaoEncriptadaResponse_QNAME = new QName("http://webservice.acesso/", "gravarSenhaIntegracaoEncriptadaResponse");
    private final static QName _ValidarAcessoColaboradorPeloCodigoResponse_QNAME = new QName("http://webservice.acesso/", "validarAcessoColaboradorPeloCodigoResponse");
    private final static QName _BuscarConfigLocalAcessoResponse_QNAME = new QName("http://webservice.acesso/", "buscarConfigLocalAcessoResponse");
    private final static QName _ValidarAcessoPessoaPorSenhaResponse_QNAME = new QName("http://webservice.acesso/", "validarAcessoPessoaPorSenhaResponse");
    private final static QName _RegistrarLiberacaoAcessoResponse_QNAME = new QName("http://webservice.acesso/", "registrarLiberacaoAcessoResponse");
    private final static QName _BuscarConfigIntegracaoAcessoResponse_QNAME = new QName("http://webservice.acesso/", "buscarConfigIntegracaoAcessoResponse");
    private final static QName _ConsultarOperacoesPendentes_QNAME = new QName("http://webservice.acesso/", "consultarOperacoesPendentes");
    private final static QName _ExcluirPessoaFotoLocalAcesso_QNAME = new QName("http://webservice.acesso/", "excluirPessoaFotoLocalAcesso");
    private final static QName _UltimoDadosOfflineGeradoResponse_QNAME = new QName("http://webservice.acesso/", "ultimoDadosOfflineGeradoResponse");
    private final static QName _GravarSenhaIntegracaoResponse_QNAME = new QName("http://webservice.acesso/", "gravarSenhaIntegracaoResponse");
    private final static QName _SolicitarUtilitario_QNAME = new QName("http://webservice.acesso/", "solicitarUtilitario");
    private final static QName _ResetMapaControladoresResponse_QNAME = new QName("http://webservice.acesso/", "resetMapaControladoresResponse");
    private final static QName _ValidarAcessoPeloCodigoAcessoResponse_QNAME = new QName("http://webservice.acesso/", "validarAcessoPeloCodigoAcessoResponse");
    private final static QName _GravarSenhaIntegracaoEncriptada_QNAME = new QName("http://webservice.acesso/", "gravarSenhaIntegracaoEncriptada");
    private final static QName _ResetMapaControladores_QNAME = new QName("http://webservice.acesso/", "resetMapaControladores");
    private final static QName _ConsultarClientesPeloNomeResponse_QNAME = new QName("http://webservice.acesso/", "consultarClientesPeloNomeResponse");
    private final static QName _ExcluirPessoaFotoLocalAcessoResponse_QNAME = new QName("http://webservice.acesso/", "excluirPessoaFotoLocalAcessoResponse");
    private final static QName _ValidarAcessoPessoaPorSenha_QNAME = new QName("http://webservice.acesso/", "validarAcessoPessoaPorSenha");
    private final static QName _RegistrarCodigoNaoPossuiMaisDeumaDigitalResponse_QNAME = new QName("http://webservice.acesso/", "registrarCodigoNaoPossuiMaisDeumaDigitalResponse");
    private final static QName _MontarListaAcessosPessoasComFoto_QNAME = new QName("http://webservice.acesso/", "montarListaAcessosPessoasComFoto");
    private final static QName _RegistrarDownloadBaseOfflineResponse_QNAME = new QName("http://webservice.acesso/", "registrarDownloadBaseOfflineResponse");
    private final static QName _GravarArquivoLocalResponse_QNAME = new QName("http://webservice.acesso/", "gravarArquivoLocalResponse");
    private final static QName _GetVersaoSistema_QNAME = new QName("http://webservice.acesso/", "getVersaoSistema");
    private final static QName _PegarFotoPessoaResponse_QNAME = new QName("http://webservice.acesso/", "pegarFotoPessoaResponse");
    private final static QName _ValidarPemissaoUsuarioResponse_QNAME = new QName("http://webservice.acesso/", "validarPemissaoUsuarioResponse");
    private final static QName _ValidarAcessoColaboradorPeloCodigo_QNAME = new QName("http://webservice.acesso/", "validarAcessoColaboradorPeloCodigo");
    private final static QName _ValidarSePodeExcluirDigital_QNAME = new QName("http://webservice.acesso/", "validarSePodeExcluirDigital");
    private final static QName _TemDadosOfflineGeradosResponse_QNAME = new QName("http://webservice.acesso/", "temDadosOfflineGeradosResponse");
    private final static QName _ConsultarOperacoesPendentesResponse_QNAME = new QName("http://webservice.acesso/", "consultarOperacoesPendentesResponse");
    private final static QName _SolicitarUtilitarioResponse_QNAME = new QName("http://webservice.acesso/", "solicitarUtilitarioResponse");
    private final static QName _Exception_QNAME = new QName("http://webservice.acesso/", "Exception");
    private final static QName _ValidarAcessoClientePelaMatricula_QNAME = new QName("http://webservice.acesso/", "validarAcessoClientePelaMatricula");
    private final static QName _BuscarPorCodigoAcesso_QNAME = new QName("http://webservice.acesso/", "buscarPorCodigoAcesso");
    private final static QName _RegistrarLiberacaoAcesso_QNAME = new QName("http://webservice.acesso/", "registrarLiberacaoAcesso");
    private final static QName _BuscarPorCodigoAcessoResponse_QNAME = new QName("http://webservice.acesso/", "buscarPorCodigoAcessoResponse");
    private final static QName _RegistrarDownloadBaseOffline_QNAME = new QName("http://webservice.acesso/", "registrarDownloadBaseOffline");
    private final static QName _ValidarPemissaoUsuario_QNAME = new QName("http://webservice.acesso/", "validarPemissaoUsuario");
    private final static QName _ValidarSePodeExcluirDigitalResponse_QNAME = new QName("http://webservice.acesso/", "validarSePodeExcluirDigitalResponse");
    private final static QName _MontarDadosOfflineResponse_QNAME = new QName("http://webservice.acesso/", "montarDadosOfflineResponse");
    private final static QName _RegistrarAcessoResponse_QNAME = new QName("http://webservice.acesso/", "registrarAcessoResponse");
    private final static QName _ValidarAcessoPeloCodigoAcesso_QNAME = new QName("http://webservice.acesso/", "validarAcessoPeloCodigoAcesso");
    private final static QName _GravarSenhaIntegracao_QNAME = new QName("http://webservice.acesso/", "gravarSenhaIntegracao");
    private final static QName _PegarFotoPessoaResponseReturn_QNAME = new QName("", "return");
    private final static QName _GravarArquivoLocalConteudo_QNAME = new QName("", "conteudo");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: negocio.comuns.acesso.webservice.client
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link ValidarAcessoColaboradorPeloCodigoResponse }
     * 
     */
    public ValidarAcessoColaboradorPeloCodigoResponse createValidarAcessoColaboradorPeloCodigoResponse() {
        return new ValidarAcessoColaboradorPeloCodigoResponse();
    }

    /**
     * Create an instance of {@link BuscarConfigLocalAcessoResponse }
     * 
     */
    public BuscarConfigLocalAcessoResponse createBuscarConfigLocalAcessoResponse() {
        return new BuscarConfigLocalAcessoResponse();
    }

    /**
     * Create an instance of {@link ValidarAcessoPessoaPorSenhaResponse }
     * 
     */
    public ValidarAcessoPessoaPorSenhaResponse createValidarAcessoPessoaPorSenhaResponse() {
        return new ValidarAcessoPessoaPorSenhaResponse();
    }

    /**
     * Create an instance of {@link GetVersaoSistemaResponse }
     * 
     */
    public GetVersaoSistemaResponse createGetVersaoSistemaResponse() {
        return new GetVersaoSistemaResponse();
    }

    /**
     * Create an instance of {@link GravarSenhaIntegracaoEncriptadaResponse }
     * 
     */
    public GravarSenhaIntegracaoEncriptadaResponse createGravarSenhaIntegracaoEncriptadaResponse() {
        return new GravarSenhaIntegracaoEncriptadaResponse();
    }

    /**
     * Create an instance of {@link GravarArquivoLocal }
     * 
     */
    public GravarArquivoLocal createGravarArquivoLocal() {
        return new GravarArquivoLocal();
    }

    /**
     * Create an instance of {@link ExcluirPessoaFotoLocalAcesso }
     * 
     */
    public ExcluirPessoaFotoLocalAcesso createExcluirPessoaFotoLocalAcesso() {
        return new ExcluirPessoaFotoLocalAcesso();
    }

    /**
     * Create an instance of {@link GravarSenhaIntegracaoResponse }
     * 
     */
    public GravarSenhaIntegracaoResponse createGravarSenhaIntegracaoResponse() {
        return new GravarSenhaIntegracaoResponse();
    }

    /**
     * Create an instance of {@link UltimoDadosOfflineGeradoResponse }
     * 
     */
    public UltimoDadosOfflineGeradoResponse createUltimoDadosOfflineGeradoResponse() {
        return new UltimoDadosOfflineGeradoResponse();
    }

    /**
     * Create an instance of {@link ResetMapaControladoresResponse }
     * 
     */
    public ResetMapaControladoresResponse createResetMapaControladoresResponse() {
        return new ResetMapaControladoresResponse();
    }

    /**
     * Create an instance of {@link SolicitarUtilitario }
     * 
     */
    public SolicitarUtilitario createSolicitarUtilitario() {
        return new SolicitarUtilitario();
    }

    /**
     * Create an instance of {@link RegistrarLiberacaoAcessoResponse }
     * 
     */
    public RegistrarLiberacaoAcessoResponse createRegistrarLiberacaoAcessoResponse() {
        return new RegistrarLiberacaoAcessoResponse();
    }

    /**
     * Create an instance of {@link BuscarConfigIntegracaoAcessoResponse }
     * 
     */
    public BuscarConfigIntegracaoAcessoResponse createBuscarConfigIntegracaoAcessoResponse() {
        return new BuscarConfigIntegracaoAcessoResponse();
    }

    /**
     * Create an instance of {@link ConsultarOperacoesPendentes }
     * 
     */
    public ConsultarOperacoesPendentes createConsultarOperacoesPendentes() {
        return new ConsultarOperacoesPendentes();
    }

    /**
     * Create an instance of {@link BuscarCodigoAcesso }
     * 
     */
    public BuscarCodigoAcesso createBuscarCodigoAcesso() {
        return new BuscarCodigoAcesso();
    }

    /**
     * Create an instance of {@link BuscarConfigLocalAcesso }
     * 
     */
    public BuscarConfigLocalAcesso createBuscarConfigLocalAcesso() {
        return new BuscarConfigLocalAcesso();
    }

    /**
     * Create an instance of {@link RegistrarCodigoNaoPossuiMaisDeumaDigital }
     * 
     */
    public RegistrarCodigoNaoPossuiMaisDeumaDigital createRegistrarCodigoNaoPossuiMaisDeumaDigital() {
        return new RegistrarCodigoNaoPossuiMaisDeumaDigital();
    }

    /**
     * Create an instance of {@link UltimoDadosOfflineGerado }
     * 
     */
    public UltimoDadosOfflineGerado createUltimoDadosOfflineGerado() {
        return new UltimoDadosOfflineGerado();
    }

    /**
     * Create an instance of {@link RegistrarAcesso }
     * 
     */
    public RegistrarAcesso createRegistrarAcesso() {
        return new RegistrarAcesso();
    }

    /**
     * Create an instance of {@link RegistrarCodigoPossuiMaisDeumaDigital }
     * 
     */
    public RegistrarCodigoPossuiMaisDeumaDigital createRegistrarCodigoPossuiMaisDeumaDigital() {
        return new RegistrarCodigoPossuiMaisDeumaDigital();
    }

    /**
     * Create an instance of {@link MontarDadosOfflinePessoa }
     * 
     */
    public MontarDadosOfflinePessoa createMontarDadosOfflinePessoa() {
        return new MontarDadosOfflinePessoa();
    }

    /**
     * Create an instance of {@link MontarListaAcessosPessoasComFotoResponse }
     * 
     */
    public MontarListaAcessosPessoasComFotoResponse createMontarListaAcessosPessoasComFotoResponse() {
        return new MontarListaAcessosPessoasComFotoResponse();
    }

    /**
     * Create an instance of {@link RegistrarVersaoAcesso }
     * 
     */
    public RegistrarVersaoAcesso createRegistrarVersaoAcesso() {
        return new RegistrarVersaoAcesso();
    }

    /**
     * Create an instance of {@link ValidarAcessoClientePelaMatriculaResponse }
     * 
     */
    public ValidarAcessoClientePelaMatriculaResponse createValidarAcessoClientePelaMatriculaResponse() {
        return new ValidarAcessoClientePelaMatriculaResponse();
    }

    /**
     * Create an instance of {@link ValidarAcessoPeloCodigoAcessoAvaliandoIntegracaoResponse }
     * 
     */
    public ValidarAcessoPeloCodigoAcessoAvaliandoIntegracaoResponse createValidarAcessoPeloCodigoAcessoAvaliandoIntegracaoResponse() {
        return new ValidarAcessoPeloCodigoAcessoAvaliandoIntegracaoResponse();
    }

    /**
     * Create an instance of {@link MontarDadosOffline }
     * 
     */
    public MontarDadosOffline createMontarDadosOffline() {
        return new MontarDadosOffline();
    }

    /**
     * Create an instance of {@link MontarDadosOfflinePessoaResponse }
     * 
     */
    public MontarDadosOfflinePessoaResponse createMontarDadosOfflinePessoaResponse() {
        return new MontarDadosOfflinePessoaResponse();
    }

    /**
     * Create an instance of {@link RegistrarVersaoAcessoResponse }
     * 
     */
    public RegistrarVersaoAcessoResponse createRegistrarVersaoAcessoResponse() {
        return new RegistrarVersaoAcessoResponse();
    }

    /**
     * Create an instance of {@link ValidarAcessoPeloCodigoAcessoAvaliandoIntegracao }
     * 
     */
    public ValidarAcessoPeloCodigoAcessoAvaliandoIntegracao createValidarAcessoPeloCodigoAcessoAvaliandoIntegracao() {
        return new ValidarAcessoPeloCodigoAcessoAvaliandoIntegracao();
    }

    /**
     * Create an instance of {@link BuscarConfigIntegracaoAcesso }
     * 
     */
    public BuscarConfigIntegracaoAcesso createBuscarConfigIntegracaoAcesso() {
        return new BuscarConfigIntegracaoAcesso();
    }

    /**
     * Create an instance of {@link PegarFotoPessoa }
     * 
     */
    public PegarFotoPessoa createPegarFotoPessoa() {
        return new PegarFotoPessoa();
    }

    /**
     * Create an instance of {@link ConsultarClientesPeloNome }
     * 
     */
    public ConsultarClientesPeloNome createConsultarClientesPeloNome() {
        return new ConsultarClientesPeloNome();
    }

    /**
     * Create an instance of {@link BuscarCodigoAcessoResponse }
     * 
     */
    public BuscarCodigoAcessoResponse createBuscarCodigoAcessoResponse() {
        return new BuscarCodigoAcessoResponse();
    }

    /**
     * Create an instance of {@link TemDadosOfflineGerados }
     * 
     */
    public TemDadosOfflineGerados createTemDadosOfflineGerados() {
        return new TemDadosOfflineGerados();
    }

    /**
     * Create an instance of {@link RegistrarCodigoPossuiMaisDeumaDigitalResponse }
     * 
     */
    public RegistrarCodigoPossuiMaisDeumaDigitalResponse createRegistrarCodigoPossuiMaisDeumaDigitalResponse() {
        return new RegistrarCodigoPossuiMaisDeumaDigitalResponse();
    }

    /**
     * Create an instance of {@link Exception }
     * 
     */
    public Exception createException() {
        return new Exception();
    }

    /**
     * Create an instance of {@link ValidarAcessoClientePelaMatricula }
     * 
     */
    public ValidarAcessoClientePelaMatricula createValidarAcessoClientePelaMatricula() {
        return new ValidarAcessoClientePelaMatricula();
    }

    /**
     * Create an instance of {@link BuscarPorCodigoAcesso }
     * 
     */
    public BuscarPorCodigoAcesso createBuscarPorCodigoAcesso() {
        return new BuscarPorCodigoAcesso();
    }

    /**
     * Create an instance of {@link RegistrarLiberacaoAcesso }
     * 
     */
    public RegistrarLiberacaoAcesso createRegistrarLiberacaoAcesso() {
        return new RegistrarLiberacaoAcesso();
    }

    /**
     * Create an instance of {@link ConsultarOperacoesPendentesResponse }
     * 
     */
    public ConsultarOperacoesPendentesResponse createConsultarOperacoesPendentesResponse() {
        return new ConsultarOperacoesPendentesResponse();
    }

    /**
     * Create an instance of {@link ValidarSePodeExcluirDigital }
     * 
     */
    public ValidarSePodeExcluirDigital createValidarSePodeExcluirDigital() {
        return new ValidarSePodeExcluirDigital();
    }

    /**
     * Create an instance of {@link TemDadosOfflineGeradosResponse }
     * 
     */
    public TemDadosOfflineGeradosResponse createTemDadosOfflineGeradosResponse() {
        return new TemDadosOfflineGeradosResponse();
    }

    /**
     * Create an instance of {@link SolicitarUtilitarioResponse }
     * 
     */
    public SolicitarUtilitarioResponse createSolicitarUtilitarioResponse() {
        return new SolicitarUtilitarioResponse();
    }

    /**
     * Create an instance of {@link ValidarAcessoPeloCodigoAcesso }
     * 
     */
    public ValidarAcessoPeloCodigoAcesso createValidarAcessoPeloCodigoAcesso() {
        return new ValidarAcessoPeloCodigoAcesso();
    }

    /**
     * Create an instance of {@link GravarSenhaIntegracao }
     * 
     */
    public GravarSenhaIntegracao createGravarSenhaIntegracao() {
        return new GravarSenhaIntegracao();
    }

    /**
     * Create an instance of {@link RegistrarDownloadBaseOffline }
     * 
     */
    public RegistrarDownloadBaseOffline createRegistrarDownloadBaseOffline() {
        return new RegistrarDownloadBaseOffline();
    }

    /**
     * Create an instance of {@link BuscarPorCodigoAcessoResponse }
     * 
     */
    public BuscarPorCodigoAcessoResponse createBuscarPorCodigoAcessoResponse() {
        return new BuscarPorCodigoAcessoResponse();
    }

    /**
     * Create an instance of {@link MontarDadosOfflineResponse }
     * 
     */
    public MontarDadosOfflineResponse createMontarDadosOfflineResponse() {
        return new MontarDadosOfflineResponse();
    }

    /**
     * Create an instance of {@link ValidarSePodeExcluirDigitalResponse }
     * 
     */
    public ValidarSePodeExcluirDigitalResponse createValidarSePodeExcluirDigitalResponse() {
        return new ValidarSePodeExcluirDigitalResponse();
    }

    /**
     * Create an instance of {@link ValidarPemissaoUsuario }
     * 
     */
    public ValidarPemissaoUsuario createValidarPemissaoUsuario() {
        return new ValidarPemissaoUsuario();
    }

    /**
     * Create an instance of {@link RegistrarAcessoResponse }
     * 
     */
    public RegistrarAcessoResponse createRegistrarAcessoResponse() {
        return new RegistrarAcessoResponse();
    }

    /**
     * Create an instance of {@link ResetMapaControladores }
     * 
     */
    public ResetMapaControladores createResetMapaControladores() {
        return new ResetMapaControladores();
    }

    /**
     * Create an instance of {@link ExcluirPessoaFotoLocalAcessoResponse }
     * 
     */
    public ExcluirPessoaFotoLocalAcessoResponse createExcluirPessoaFotoLocalAcessoResponse() {
        return new ExcluirPessoaFotoLocalAcessoResponse();
    }

    /**
     * Create an instance of {@link ConsultarClientesPeloNomeResponse }
     * 
     */
    public ConsultarClientesPeloNomeResponse createConsultarClientesPeloNomeResponse() {
        return new ConsultarClientesPeloNomeResponse();
    }

    /**
     * Create an instance of {@link MontarListaAcessosPessoasComFoto }
     * 
     */
    public MontarListaAcessosPessoasComFoto createMontarListaAcessosPessoasComFoto() {
        return new MontarListaAcessosPessoasComFoto();
    }

    /**
     * Create an instance of {@link RegistrarCodigoNaoPossuiMaisDeumaDigitalResponse }
     * 
     */
    public RegistrarCodigoNaoPossuiMaisDeumaDigitalResponse createRegistrarCodigoNaoPossuiMaisDeumaDigitalResponse() {
        return new RegistrarCodigoNaoPossuiMaisDeumaDigitalResponse();
    }

    /**
     * Create an instance of {@link ValidarAcessoPessoaPorSenha }
     * 
     */
    public ValidarAcessoPessoaPorSenha createValidarAcessoPessoaPorSenha() {
        return new ValidarAcessoPessoaPorSenha();
    }

    /**
     * Create an instance of {@link ValidarAcessoPeloCodigoAcessoResponse }
     * 
     */
    public ValidarAcessoPeloCodigoAcessoResponse createValidarAcessoPeloCodigoAcessoResponse() {
        return new ValidarAcessoPeloCodigoAcessoResponse();
    }

    /**
     * Create an instance of {@link GravarSenhaIntegracaoEncriptada }
     * 
     */
    public GravarSenhaIntegracaoEncriptada createGravarSenhaIntegracaoEncriptada() {
        return new GravarSenhaIntegracaoEncriptada();
    }

    /**
     * Create an instance of {@link GetVersaoSistema }
     * 
     */
    public GetVersaoSistema createGetVersaoSistema() {
        return new GetVersaoSistema();
    }

    /**
     * Create an instance of {@link ValidarPemissaoUsuarioResponse }
     * 
     */
    public ValidarPemissaoUsuarioResponse createValidarPemissaoUsuarioResponse() {
        return new ValidarPemissaoUsuarioResponse();
    }

    /**
     * Create an instance of {@link PegarFotoPessoaResponse }
     * 
     */
    public PegarFotoPessoaResponse createPegarFotoPessoaResponse() {
        return new PegarFotoPessoaResponse();
    }

    /**
     * Create an instance of {@link ValidarAcessoColaboradorPeloCodigo }
     * 
     */
    public ValidarAcessoColaboradorPeloCodigo createValidarAcessoColaboradorPeloCodigo() {
        return new ValidarAcessoColaboradorPeloCodigo();
    }

    /**
     * Create an instance of {@link RegistrarDownloadBaseOfflineResponse }
     * 
     */
    public RegistrarDownloadBaseOfflineResponse createRegistrarDownloadBaseOfflineResponse() {
        return new RegistrarDownloadBaseOfflineResponse();
    }

    /**
     * Create an instance of {@link GravarArquivoLocalResponse }
     * 
     */
    public GravarArquivoLocalResponse createGravarArquivoLocalResponse() {
        return new GravarArquivoLocalResponse();
    }

    /**
     * Create an instance of {@link RetornoRequisicaoValidarPermissaoUsuario }
     * 
     */
    public RetornoRequisicaoValidarPermissaoUsuario createRetornoRequisicaoValidarPermissaoUsuario() {
        return new RetornoRequisicaoValidarPermissaoUsuario();
    }

    /**
     * Create an instance of {@link RetornoRequisicaoBuscarLocais }
     * 
     */
    public RetornoRequisicaoBuscarLocais createRetornoRequisicaoBuscarLocais() {
        return new RetornoRequisicaoBuscarLocais();
    }

    /**
     * Create an instance of {@link RetornoRequisicaoValidacaoAcesso }
     * 
     */
    public RetornoRequisicaoValidacaoAcesso createRetornoRequisicaoValidacaoAcesso() {
        return new RetornoRequisicaoValidacaoAcesso();
    }

    /**
     * Create an instance of {@link ColetorWS }
     * 
     */
    public ColetorWS createColetorWS() {
        return new ColetorWS();
    }

    /**
     * Create an instance of {@link RetornoRequisicaoBuscarCodigoAcesso }
     * 
     */
    public RetornoRequisicaoBuscarCodigoAcesso createRetornoRequisicaoBuscarCodigoAcesso() {
        return new RetornoRequisicaoBuscarCodigoAcesso();
    }

    /**
     * Create an instance of {@link IntegracaoAcessoWS }
     * 
     */
    public IntegracaoAcessoWS createIntegracaoAcessoWS() {
        return new IntegracaoAcessoWS();
    }

    /**
     * Create an instance of {@link RetornoRequisicaoRegistrarAcesso }
     * 
     */
    public RetornoRequisicaoRegistrarAcesso createRetornoRequisicaoRegistrarAcesso() {
        return new RetornoRequisicaoRegistrarAcesso();
    }

    /**
     * Create an instance of {@link RetornoRequisicaoDadosOffline }
     * 
     */
    public RetornoRequisicaoDadosOffline createRetornoRequisicaoDadosOffline() {
        return new RetornoRequisicaoDadosOffline();
    }

    /**
     * Create an instance of {@link RetornoRequisicaoLiberacaoAcesso }
     * 
     */
    public RetornoRequisicaoLiberacaoAcesso createRetornoRequisicaoLiberacaoAcesso() {
        return new RetornoRequisicaoLiberacaoAcesso();
    }

    /**
     * Create an instance of {@link SuperJSON }
     * 
     */
    public SuperJSON createSuperJSON() {
        return new SuperJSON();
    }

    /**
     * Create an instance of {@link RetornoRequisicaoWS }
     * 
     */
    public RetornoRequisicaoWS createRetornoRequisicaoWS() {
        return new RetornoRequisicaoWS();
    }

    /**
     * Create an instance of {@link RetornoRequisicaoBuscarIntegracoes }
     * 
     */
    public RetornoRequisicaoBuscarIntegracoes createRetornoRequisicaoBuscarIntegracoes() {
        return new RetornoRequisicaoBuscarIntegracoes();
    }

    /**
     * Create an instance of {@link RetornoRequisicaoConsultarClientes }
     * 
     */
    public RetornoRequisicaoConsultarClientes createRetornoRequisicaoConsultarClientes() {
        return new RetornoRequisicaoConsultarClientes();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MontarDadosOfflinePessoa }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "montarDadosOfflinePessoa")
    public JAXBElement<MontarDadosOfflinePessoa> createMontarDadosOfflinePessoa(MontarDadosOfflinePessoa value) {
        return new JAXBElement<MontarDadosOfflinePessoa>(_MontarDadosOfflinePessoa_QNAME, MontarDadosOfflinePessoa.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RegistrarVersaoAcesso }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "registrarVersaoAcesso")
    public JAXBElement<RegistrarVersaoAcesso> createRegistrarVersaoAcesso(RegistrarVersaoAcesso value) {
        return new JAXBElement<RegistrarVersaoAcesso>(_RegistrarVersaoAcesso_QNAME, RegistrarVersaoAcesso.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MontarListaAcessosPessoasComFotoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "montarListaAcessosPessoasComFotoResponse")
    public JAXBElement<MontarListaAcessosPessoasComFotoResponse> createMontarListaAcessosPessoasComFotoResponse(MontarListaAcessosPessoasComFotoResponse value) {
        return new JAXBElement<MontarListaAcessosPessoasComFotoResponse>(_MontarListaAcessosPessoasComFotoResponse_QNAME, MontarListaAcessosPessoasComFotoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarAcessoClientePelaMatriculaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "validarAcessoClientePelaMatriculaResponse")
    public JAXBElement<ValidarAcessoClientePelaMatriculaResponse> createValidarAcessoClientePelaMatriculaResponse(ValidarAcessoClientePelaMatriculaResponse value) {
        return new JAXBElement<ValidarAcessoClientePelaMatriculaResponse>(_ValidarAcessoClientePelaMatriculaResponse_QNAME, ValidarAcessoClientePelaMatriculaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RegistrarCodigoNaoPossuiMaisDeumaDigital }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "registrarCodigoNaoPossuiMaisDeumaDigital")
    public JAXBElement<RegistrarCodigoNaoPossuiMaisDeumaDigital> createRegistrarCodigoNaoPossuiMaisDeumaDigital(RegistrarCodigoNaoPossuiMaisDeumaDigital value) {
        return new JAXBElement<RegistrarCodigoNaoPossuiMaisDeumaDigital>(_RegistrarCodigoNaoPossuiMaisDeumaDigital_QNAME, RegistrarCodigoNaoPossuiMaisDeumaDigital.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BuscarConfigLocalAcesso }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "buscarConfigLocalAcesso")
    public JAXBElement<BuscarConfigLocalAcesso> createBuscarConfigLocalAcesso(BuscarConfigLocalAcesso value) {
        return new JAXBElement<BuscarConfigLocalAcesso>(_BuscarConfigLocalAcesso_QNAME, BuscarConfigLocalAcesso.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BuscarCodigoAcesso }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "buscarCodigoAcesso")
    public JAXBElement<BuscarCodigoAcesso> createBuscarCodigoAcesso(BuscarCodigoAcesso value) {
        return new JAXBElement<BuscarCodigoAcesso>(_BuscarCodigoAcesso_QNAME, BuscarCodigoAcesso.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link UltimoDadosOfflineGerado }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "ultimoDadosOfflineGerado")
    public JAXBElement<UltimoDadosOfflineGerado> createUltimoDadosOfflineGerado(UltimoDadosOfflineGerado value) {
        return new JAXBElement<UltimoDadosOfflineGerado>(_UltimoDadosOfflineGerado_QNAME, UltimoDadosOfflineGerado.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RegistrarCodigoPossuiMaisDeumaDigital }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "registrarCodigoPossuiMaisDeumaDigital")
    public JAXBElement<RegistrarCodigoPossuiMaisDeumaDigital> createRegistrarCodigoPossuiMaisDeumaDigital(RegistrarCodigoPossuiMaisDeumaDigital value) {
        return new JAXBElement<RegistrarCodigoPossuiMaisDeumaDigital>(_RegistrarCodigoPossuiMaisDeumaDigital_QNAME, RegistrarCodigoPossuiMaisDeumaDigital.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RegistrarAcesso }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "registrarAcesso")
    public JAXBElement<RegistrarAcesso> createRegistrarAcesso(RegistrarAcesso value) {
        return new JAXBElement<RegistrarAcesso>(_RegistrarAcesso_QNAME, RegistrarAcesso.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarAcessoPeloCodigoAcessoAvaliandoIntegracao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "validarAcessoPeloCodigoAcessoAvaliandoIntegracao")
    public JAXBElement<ValidarAcessoPeloCodigoAcessoAvaliandoIntegracao> createValidarAcessoPeloCodigoAcessoAvaliandoIntegracao(ValidarAcessoPeloCodigoAcessoAvaliandoIntegracao value) {
        return new JAXBElement<ValidarAcessoPeloCodigoAcessoAvaliandoIntegracao>(_ValidarAcessoPeloCodigoAcessoAvaliandoIntegracao_QNAME, ValidarAcessoPeloCodigoAcessoAvaliandoIntegracao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BuscarConfigIntegracaoAcesso }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "buscarConfigIntegracaoAcesso")
    public JAXBElement<BuscarConfigIntegracaoAcesso> createBuscarConfigIntegracaoAcesso(BuscarConfigIntegracaoAcesso value) {
        return new JAXBElement<BuscarConfigIntegracaoAcesso>(_BuscarConfigIntegracaoAcesso_QNAME, BuscarConfigIntegracaoAcesso.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesPeloNome }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "consultarClientesPeloNome")
    public JAXBElement<ConsultarClientesPeloNome> createConsultarClientesPeloNome(ConsultarClientesPeloNome value) {
        return new JAXBElement<ConsultarClientesPeloNome>(_ConsultarClientesPeloNome_QNAME, ConsultarClientesPeloNome.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PegarFotoPessoa }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "pegarFotoPessoa")
    public JAXBElement<PegarFotoPessoa> createPegarFotoPessoa(PegarFotoPessoa value) {
        return new JAXBElement<PegarFotoPessoa>(_PegarFotoPessoa_QNAME, PegarFotoPessoa.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RegistrarCodigoPossuiMaisDeumaDigitalResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "registrarCodigoPossuiMaisDeumaDigitalResponse")
    public JAXBElement<RegistrarCodigoPossuiMaisDeumaDigitalResponse> createRegistrarCodigoPossuiMaisDeumaDigitalResponse(RegistrarCodigoPossuiMaisDeumaDigitalResponse value) {
        return new JAXBElement<RegistrarCodigoPossuiMaisDeumaDigitalResponse>(_RegistrarCodigoPossuiMaisDeumaDigitalResponse_QNAME, RegistrarCodigoPossuiMaisDeumaDigitalResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BuscarCodigoAcessoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "buscarCodigoAcessoResponse")
    public JAXBElement<BuscarCodigoAcessoResponse> createBuscarCodigoAcessoResponse(BuscarCodigoAcessoResponse value) {
        return new JAXBElement<BuscarCodigoAcessoResponse>(_BuscarCodigoAcessoResponse_QNAME, BuscarCodigoAcessoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link TemDadosOfflineGerados }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "temDadosOfflineGerados")
    public JAXBElement<TemDadosOfflineGerados> createTemDadosOfflineGerados(TemDadosOfflineGerados value) {
        return new JAXBElement<TemDadosOfflineGerados>(_TemDadosOfflineGerados_QNAME, TemDadosOfflineGerados.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarAcessoPeloCodigoAcessoAvaliandoIntegracaoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "validarAcessoPeloCodigoAcessoAvaliandoIntegracaoResponse")
    public JAXBElement<ValidarAcessoPeloCodigoAcessoAvaliandoIntegracaoResponse> createValidarAcessoPeloCodigoAcessoAvaliandoIntegracaoResponse(ValidarAcessoPeloCodigoAcessoAvaliandoIntegracaoResponse value) {
        return new JAXBElement<ValidarAcessoPeloCodigoAcessoAvaliandoIntegracaoResponse>(_ValidarAcessoPeloCodigoAcessoAvaliandoIntegracaoResponse_QNAME, ValidarAcessoPeloCodigoAcessoAvaliandoIntegracaoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MontarDadosOffline }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "montarDadosOffline")
    public JAXBElement<MontarDadosOffline> createMontarDadosOffline(MontarDadosOffline value) {
        return new JAXBElement<MontarDadosOffline>(_MontarDadosOffline_QNAME, MontarDadosOffline.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MontarDadosOfflinePessoaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "montarDadosOfflinePessoaResponse")
    public JAXBElement<MontarDadosOfflinePessoaResponse> createMontarDadosOfflinePessoaResponse(MontarDadosOfflinePessoaResponse value) {
        return new JAXBElement<MontarDadosOfflinePessoaResponse>(_MontarDadosOfflinePessoaResponse_QNAME, MontarDadosOfflinePessoaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RegistrarVersaoAcessoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "registrarVersaoAcessoResponse")
    public JAXBElement<RegistrarVersaoAcessoResponse> createRegistrarVersaoAcessoResponse(RegistrarVersaoAcessoResponse value) {
        return new JAXBElement<RegistrarVersaoAcessoResponse>(_RegistrarVersaoAcessoResponse_QNAME, RegistrarVersaoAcessoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetVersaoSistemaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "getVersaoSistemaResponse")
    public JAXBElement<GetVersaoSistemaResponse> createGetVersaoSistemaResponse(GetVersaoSistemaResponse value) {
        return new JAXBElement<GetVersaoSistemaResponse>(_GetVersaoSistemaResponse_QNAME, GetVersaoSistemaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarArquivoLocal }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "gravarArquivoLocal")
    public JAXBElement<GravarArquivoLocal> createGravarArquivoLocal(GravarArquivoLocal value) {
        return new JAXBElement<GravarArquivoLocal>(_GravarArquivoLocal_QNAME, GravarArquivoLocal.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarSenhaIntegracaoEncriptadaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "gravarSenhaIntegracaoEncriptadaResponse")
    public JAXBElement<GravarSenhaIntegracaoEncriptadaResponse> createGravarSenhaIntegracaoEncriptadaResponse(GravarSenhaIntegracaoEncriptadaResponse value) {
        return new JAXBElement<GravarSenhaIntegracaoEncriptadaResponse>(_GravarSenhaIntegracaoEncriptadaResponse_QNAME, GravarSenhaIntegracaoEncriptadaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarAcessoColaboradorPeloCodigoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "validarAcessoColaboradorPeloCodigoResponse")
    public JAXBElement<ValidarAcessoColaboradorPeloCodigoResponse> createValidarAcessoColaboradorPeloCodigoResponse(ValidarAcessoColaboradorPeloCodigoResponse value) {
        return new JAXBElement<ValidarAcessoColaboradorPeloCodigoResponse>(_ValidarAcessoColaboradorPeloCodigoResponse_QNAME, ValidarAcessoColaboradorPeloCodigoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BuscarConfigLocalAcessoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "buscarConfigLocalAcessoResponse")
    public JAXBElement<BuscarConfigLocalAcessoResponse> createBuscarConfigLocalAcessoResponse(BuscarConfigLocalAcessoResponse value) {
        return new JAXBElement<BuscarConfigLocalAcessoResponse>(_BuscarConfigLocalAcessoResponse_QNAME, BuscarConfigLocalAcessoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarAcessoPessoaPorSenhaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "validarAcessoPessoaPorSenhaResponse")
    public JAXBElement<ValidarAcessoPessoaPorSenhaResponse> createValidarAcessoPessoaPorSenhaResponse(ValidarAcessoPessoaPorSenhaResponse value) {
        return new JAXBElement<ValidarAcessoPessoaPorSenhaResponse>(_ValidarAcessoPessoaPorSenhaResponse_QNAME, ValidarAcessoPessoaPorSenhaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RegistrarLiberacaoAcessoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "registrarLiberacaoAcessoResponse")
    public JAXBElement<RegistrarLiberacaoAcessoResponse> createRegistrarLiberacaoAcessoResponse(RegistrarLiberacaoAcessoResponse value) {
        return new JAXBElement<RegistrarLiberacaoAcessoResponse>(_RegistrarLiberacaoAcessoResponse_QNAME, RegistrarLiberacaoAcessoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BuscarConfigIntegracaoAcessoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "buscarConfigIntegracaoAcessoResponse")
    public JAXBElement<BuscarConfigIntegracaoAcessoResponse> createBuscarConfigIntegracaoAcessoResponse(BuscarConfigIntegracaoAcessoResponse value) {
        return new JAXBElement<BuscarConfigIntegracaoAcessoResponse>(_BuscarConfigIntegracaoAcessoResponse_QNAME, BuscarConfigIntegracaoAcessoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarOperacoesPendentes }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "consultarOperacoesPendentes")
    public JAXBElement<ConsultarOperacoesPendentes> createConsultarOperacoesPendentes(ConsultarOperacoesPendentes value) {
        return new JAXBElement<ConsultarOperacoesPendentes>(_ConsultarOperacoesPendentes_QNAME, ConsultarOperacoesPendentes.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ExcluirPessoaFotoLocalAcesso }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "excluirPessoaFotoLocalAcesso")
    public JAXBElement<ExcluirPessoaFotoLocalAcesso> createExcluirPessoaFotoLocalAcesso(ExcluirPessoaFotoLocalAcesso value) {
        return new JAXBElement<ExcluirPessoaFotoLocalAcesso>(_ExcluirPessoaFotoLocalAcesso_QNAME, ExcluirPessoaFotoLocalAcesso.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link UltimoDadosOfflineGeradoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "ultimoDadosOfflineGeradoResponse")
    public JAXBElement<UltimoDadosOfflineGeradoResponse> createUltimoDadosOfflineGeradoResponse(UltimoDadosOfflineGeradoResponse value) {
        return new JAXBElement<UltimoDadosOfflineGeradoResponse>(_UltimoDadosOfflineGeradoResponse_QNAME, UltimoDadosOfflineGeradoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarSenhaIntegracaoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "gravarSenhaIntegracaoResponse")
    public JAXBElement<GravarSenhaIntegracaoResponse> createGravarSenhaIntegracaoResponse(GravarSenhaIntegracaoResponse value) {
        return new JAXBElement<GravarSenhaIntegracaoResponse>(_GravarSenhaIntegracaoResponse_QNAME, GravarSenhaIntegracaoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SolicitarUtilitario }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "solicitarUtilitario")
    public JAXBElement<SolicitarUtilitario> createSolicitarUtilitario(SolicitarUtilitario value) {
        return new JAXBElement<SolicitarUtilitario>(_SolicitarUtilitario_QNAME, SolicitarUtilitario.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ResetMapaControladoresResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "resetMapaControladoresResponse")
    public JAXBElement<ResetMapaControladoresResponse> createResetMapaControladoresResponse(ResetMapaControladoresResponse value) {
        return new JAXBElement<ResetMapaControladoresResponse>(_ResetMapaControladoresResponse_QNAME, ResetMapaControladoresResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarAcessoPeloCodigoAcessoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "validarAcessoPeloCodigoAcessoResponse")
    public JAXBElement<ValidarAcessoPeloCodigoAcessoResponse> createValidarAcessoPeloCodigoAcessoResponse(ValidarAcessoPeloCodigoAcessoResponse value) {
        return new JAXBElement<ValidarAcessoPeloCodigoAcessoResponse>(_ValidarAcessoPeloCodigoAcessoResponse_QNAME, ValidarAcessoPeloCodigoAcessoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarSenhaIntegracaoEncriptada }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "gravarSenhaIntegracaoEncriptada")
    public JAXBElement<GravarSenhaIntegracaoEncriptada> createGravarSenhaIntegracaoEncriptada(GravarSenhaIntegracaoEncriptada value) {
        return new JAXBElement<GravarSenhaIntegracaoEncriptada>(_GravarSenhaIntegracaoEncriptada_QNAME, GravarSenhaIntegracaoEncriptada.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ResetMapaControladores }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "resetMapaControladores")
    public JAXBElement<ResetMapaControladores> createResetMapaControladores(ResetMapaControladores value) {
        return new JAXBElement<ResetMapaControladores>(_ResetMapaControladores_QNAME, ResetMapaControladores.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesPeloNomeResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "consultarClientesPeloNomeResponse")
    public JAXBElement<ConsultarClientesPeloNomeResponse> createConsultarClientesPeloNomeResponse(ConsultarClientesPeloNomeResponse value) {
        return new JAXBElement<ConsultarClientesPeloNomeResponse>(_ConsultarClientesPeloNomeResponse_QNAME, ConsultarClientesPeloNomeResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ExcluirPessoaFotoLocalAcessoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "excluirPessoaFotoLocalAcessoResponse")
    public JAXBElement<ExcluirPessoaFotoLocalAcessoResponse> createExcluirPessoaFotoLocalAcessoResponse(ExcluirPessoaFotoLocalAcessoResponse value) {
        return new JAXBElement<ExcluirPessoaFotoLocalAcessoResponse>(_ExcluirPessoaFotoLocalAcessoResponse_QNAME, ExcluirPessoaFotoLocalAcessoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarAcessoPessoaPorSenha }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "validarAcessoPessoaPorSenha")
    public JAXBElement<ValidarAcessoPessoaPorSenha> createValidarAcessoPessoaPorSenha(ValidarAcessoPessoaPorSenha value) {
        return new JAXBElement<ValidarAcessoPessoaPorSenha>(_ValidarAcessoPessoaPorSenha_QNAME, ValidarAcessoPessoaPorSenha.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RegistrarCodigoNaoPossuiMaisDeumaDigitalResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "registrarCodigoNaoPossuiMaisDeumaDigitalResponse")
    public JAXBElement<RegistrarCodigoNaoPossuiMaisDeumaDigitalResponse> createRegistrarCodigoNaoPossuiMaisDeumaDigitalResponse(RegistrarCodigoNaoPossuiMaisDeumaDigitalResponse value) {
        return new JAXBElement<RegistrarCodigoNaoPossuiMaisDeumaDigitalResponse>(_RegistrarCodigoNaoPossuiMaisDeumaDigitalResponse_QNAME, RegistrarCodigoNaoPossuiMaisDeumaDigitalResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MontarListaAcessosPessoasComFoto }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "montarListaAcessosPessoasComFoto")
    public JAXBElement<MontarListaAcessosPessoasComFoto> createMontarListaAcessosPessoasComFoto(MontarListaAcessosPessoasComFoto value) {
        return new JAXBElement<MontarListaAcessosPessoasComFoto>(_MontarListaAcessosPessoasComFoto_QNAME, MontarListaAcessosPessoasComFoto.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RegistrarDownloadBaseOfflineResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "registrarDownloadBaseOfflineResponse")
    public JAXBElement<RegistrarDownloadBaseOfflineResponse> createRegistrarDownloadBaseOfflineResponse(RegistrarDownloadBaseOfflineResponse value) {
        return new JAXBElement<RegistrarDownloadBaseOfflineResponse>(_RegistrarDownloadBaseOfflineResponse_QNAME, RegistrarDownloadBaseOfflineResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarArquivoLocalResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "gravarArquivoLocalResponse")
    public JAXBElement<GravarArquivoLocalResponse> createGravarArquivoLocalResponse(GravarArquivoLocalResponse value) {
        return new JAXBElement<GravarArquivoLocalResponse>(_GravarArquivoLocalResponse_QNAME, GravarArquivoLocalResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GetVersaoSistema }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "getVersaoSistema")
    public JAXBElement<GetVersaoSistema> createGetVersaoSistema(GetVersaoSistema value) {
        return new JAXBElement<GetVersaoSistema>(_GetVersaoSistema_QNAME, GetVersaoSistema.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link PegarFotoPessoaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "pegarFotoPessoaResponse")
    public JAXBElement<PegarFotoPessoaResponse> createPegarFotoPessoaResponse(PegarFotoPessoaResponse value) {
        return new JAXBElement<PegarFotoPessoaResponse>(_PegarFotoPessoaResponse_QNAME, PegarFotoPessoaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarPemissaoUsuarioResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "validarPemissaoUsuarioResponse")
    public JAXBElement<ValidarPemissaoUsuarioResponse> createValidarPemissaoUsuarioResponse(ValidarPemissaoUsuarioResponse value) {
        return new JAXBElement<ValidarPemissaoUsuarioResponse>(_ValidarPemissaoUsuarioResponse_QNAME, ValidarPemissaoUsuarioResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarAcessoColaboradorPeloCodigo }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "validarAcessoColaboradorPeloCodigo")
    public JAXBElement<ValidarAcessoColaboradorPeloCodigo> createValidarAcessoColaboradorPeloCodigo(ValidarAcessoColaboradorPeloCodigo value) {
        return new JAXBElement<ValidarAcessoColaboradorPeloCodigo>(_ValidarAcessoColaboradorPeloCodigo_QNAME, ValidarAcessoColaboradorPeloCodigo.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarSePodeExcluirDigital }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "validarSePodeExcluirDigital")
    public JAXBElement<ValidarSePodeExcluirDigital> createValidarSePodeExcluirDigital(ValidarSePodeExcluirDigital value) {
        return new JAXBElement<ValidarSePodeExcluirDigital>(_ValidarSePodeExcluirDigital_QNAME, ValidarSePodeExcluirDigital.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link TemDadosOfflineGeradosResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "temDadosOfflineGeradosResponse")
    public JAXBElement<TemDadosOfflineGeradosResponse> createTemDadosOfflineGeradosResponse(TemDadosOfflineGeradosResponse value) {
        return new JAXBElement<TemDadosOfflineGeradosResponse>(_TemDadosOfflineGeradosResponse_QNAME, TemDadosOfflineGeradosResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarOperacoesPendentesResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "consultarOperacoesPendentesResponse")
    public JAXBElement<ConsultarOperacoesPendentesResponse> createConsultarOperacoesPendentesResponse(ConsultarOperacoesPendentesResponse value) {
        return new JAXBElement<ConsultarOperacoesPendentesResponse>(_ConsultarOperacoesPendentesResponse_QNAME, ConsultarOperacoesPendentesResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SolicitarUtilitarioResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "solicitarUtilitarioResponse")
    public JAXBElement<SolicitarUtilitarioResponse> createSolicitarUtilitarioResponse(SolicitarUtilitarioResponse value) {
        return new JAXBElement<SolicitarUtilitarioResponse>(_SolicitarUtilitarioResponse_QNAME, SolicitarUtilitarioResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Exception }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "Exception")
    public JAXBElement<Exception> createException(Exception value) {
        return new JAXBElement<Exception>(_Exception_QNAME, Exception.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarAcessoClientePelaMatricula }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "validarAcessoClientePelaMatricula")
    public JAXBElement<ValidarAcessoClientePelaMatricula> createValidarAcessoClientePelaMatricula(ValidarAcessoClientePelaMatricula value) {
        return new JAXBElement<ValidarAcessoClientePelaMatricula>(_ValidarAcessoClientePelaMatricula_QNAME, ValidarAcessoClientePelaMatricula.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BuscarPorCodigoAcesso }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "buscarPorCodigoAcesso")
    public JAXBElement<BuscarPorCodigoAcesso> createBuscarPorCodigoAcesso(BuscarPorCodigoAcesso value) {
        return new JAXBElement<BuscarPorCodigoAcesso>(_BuscarPorCodigoAcesso_QNAME, BuscarPorCodigoAcesso.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RegistrarLiberacaoAcesso }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "registrarLiberacaoAcesso")
    public JAXBElement<RegistrarLiberacaoAcesso> createRegistrarLiberacaoAcesso(RegistrarLiberacaoAcesso value) {
        return new JAXBElement<RegistrarLiberacaoAcesso>(_RegistrarLiberacaoAcesso_QNAME, RegistrarLiberacaoAcesso.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BuscarPorCodigoAcessoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "buscarPorCodigoAcessoResponse")
    public JAXBElement<BuscarPorCodigoAcessoResponse> createBuscarPorCodigoAcessoResponse(BuscarPorCodigoAcessoResponse value) {
        return new JAXBElement<BuscarPorCodigoAcessoResponse>(_BuscarPorCodigoAcessoResponse_QNAME, BuscarPorCodigoAcessoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RegistrarDownloadBaseOffline }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "registrarDownloadBaseOffline")
    public JAXBElement<RegistrarDownloadBaseOffline> createRegistrarDownloadBaseOffline(RegistrarDownloadBaseOffline value) {
        return new JAXBElement<RegistrarDownloadBaseOffline>(_RegistrarDownloadBaseOffline_QNAME, RegistrarDownloadBaseOffline.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarPemissaoUsuario }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "validarPemissaoUsuario")
    public JAXBElement<ValidarPemissaoUsuario> createValidarPemissaoUsuario(ValidarPemissaoUsuario value) {
        return new JAXBElement<ValidarPemissaoUsuario>(_ValidarPemissaoUsuario_QNAME, ValidarPemissaoUsuario.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarSePodeExcluirDigitalResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "validarSePodeExcluirDigitalResponse")
    public JAXBElement<ValidarSePodeExcluirDigitalResponse> createValidarSePodeExcluirDigitalResponse(ValidarSePodeExcluirDigitalResponse value) {
        return new JAXBElement<ValidarSePodeExcluirDigitalResponse>(_ValidarSePodeExcluirDigitalResponse_QNAME, ValidarSePodeExcluirDigitalResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MontarDadosOfflineResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "montarDadosOfflineResponse")
    public JAXBElement<MontarDadosOfflineResponse> createMontarDadosOfflineResponse(MontarDadosOfflineResponse value) {
        return new JAXBElement<MontarDadosOfflineResponse>(_MontarDadosOfflineResponse_QNAME, MontarDadosOfflineResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RegistrarAcessoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "registrarAcessoResponse")
    public JAXBElement<RegistrarAcessoResponse> createRegistrarAcessoResponse(RegistrarAcessoResponse value) {
        return new JAXBElement<RegistrarAcessoResponse>(_RegistrarAcessoResponse_QNAME, RegistrarAcessoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarAcessoPeloCodigoAcesso }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "validarAcessoPeloCodigoAcesso")
    public JAXBElement<ValidarAcessoPeloCodigoAcesso> createValidarAcessoPeloCodigoAcesso(ValidarAcessoPeloCodigoAcesso value) {
        return new JAXBElement<ValidarAcessoPeloCodigoAcesso>(_ValidarAcessoPeloCodigoAcesso_QNAME, ValidarAcessoPeloCodigoAcesso.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarSenhaIntegracao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.acesso/", name = "gravarSenhaIntegracao")
    public JAXBElement<GravarSenhaIntegracao> createGravarSenhaIntegracao(GravarSenhaIntegracao value) {
        return new JAXBElement<GravarSenhaIntegracao>(_GravarSenhaIntegracao_QNAME, GravarSenhaIntegracao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link byte[]}{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "", name = "return", scope = PegarFotoPessoaResponse.class)
    public JAXBElement<byte[]> createPegarFotoPessoaResponseReturn(byte[] value) {
        return new JAXBElement<byte[]>(_PegarFotoPessoaResponseReturn_QNAME, byte[].class, PegarFotoPessoaResponse.class, ((byte[]) value));
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link byte[]}{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "", name = "conteudo", scope = GravarArquivoLocal.class)
    public JAXBElement<byte[]> createGravarArquivoLocalConteudo(byte[] value) {
        return new JAXBElement<byte[]>(_GravarArquivoLocalConteudo_QNAME, byte[].class, GravarArquivoLocal.class, ((byte[]) value));
    }

}
