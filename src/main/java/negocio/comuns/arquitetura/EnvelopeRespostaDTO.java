package negocio.comuns.arquitetura;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class EnvelopeRespostaDTO {

    private Meta meta;
    private Object content;
    private Long totalElements;
    private Long totalPages;
    private Boolean first;
    private Boolean last;
    private Long numberOfElements;
    private Long size;
    private Long number;

    /**
     * @param meta Metadados da resposta HTTP
     * @return Uma instância de {@link EnvelopeRespostaDTO} com os metadados inseridos.
     */
    public static EnvelopeRespostaDTO of(Meta meta) {
        return of(meta, (Object) null);
    }

    /**
     * @return Uma instancia de {@link EnvelopeRespostaDTO} sem qualquer dado preenchido
     */
    public static EnvelopeRespostaDTO emBranco() {
        return new EnvelopeRespostaDTO();
    }

    /**
     * @param erro         Marcador do erro
     * @param errorMessage Mensagem de erro mais detalhada
     * @return Uma instância de {@link EnvelopeRespostaDTO} com os metadados inseridos.
     */
    public static EnvelopeRespostaDTO erro(String erro, String errorMessage) {
        return of(new Meta(erro, errorMessage));
    }

    /**
     * @param messageID    Marcador da mensagem
     * @param messageValue Valor de variavel a ser devolvido concatenado junto a mensagem.
     * @return Uma instância de {@link EnvelopeRespostaDTO} com os metadados inseridos.
     */
    public static EnvelopeRespostaDTO mensagemFront(String messageID, String messageValue) {
        return of(new Meta(null, null, messageID, messageValue));
    }

    /**
     * @param content Conteúdo da resposta HTTP
     * @return Uma instância de {@link EnvelopeRespostaDTO} com o conteúdo inserido.
     */
    public static EnvelopeRespostaDTO of(Object content) {
        return of(null, content);
    }

    /**
     * @param meta    Metadados da resposta HTTP
     * @param content Conteúdo da resposta HTTP
     * @return Uma instância de {@link EnvelopeRespostaDTO} com os metadados e o conteúdo inseridos.
     */
    public static EnvelopeRespostaDTO of(Meta meta, Object content) {
        return of(meta, content, null);
    }

    /**
     * @param content      Conteúdo da resposta HTTP
     * @param paginadorDTO Objeto de paginação da aplicação
     * @return Uma instância de {@link EnvelopeRespostaDTO} com os metadados e o conteúdo inseridos.
     */
    public static EnvelopeRespostaDTO of(Object content, PaginadorDTO paginadorDTO) {
        return of(null, content, paginadorDTO);
    }

    /**
     * @param meta         Metadados da resposta HTTP
     * @param content      Conteúdo da resposta HTTP
     * @param paginadorDTO Objeto de paginação
     * @return Uma instância de {@link EnvelopeRespostaDTO} com os metadados e o conteúdo inseridos.
     */
    public static EnvelopeRespostaDTO of(Meta meta, Object content, PaginadorDTO paginadorDTO) {
        EnvelopeRespostaDTO envelopeRespostaDTO = new EnvelopeRespostaDTO();
        envelopeRespostaDTO.setMeta(meta);
        envelopeRespostaDTO.setContent(content);

        if (paginadorDTO != null && null != paginadorDTO.getSize() && content instanceof List) {
            List contentList = (List) content;
            Boolean primeiraPagina = paginadorDTO.getPage() == null || paginadorDTO.getPage() == 0;
            Boolean ultimaPagina = contentList.size() < paginadorDTO.getSize() ||
                    (paginadorDTO.getQuantidadeTotalElementos() - paginadorDTO.getSize() <= paginadorDTO.getPage() * paginadorDTO.getSize());

            envelopeRespostaDTO.setTotalElements(paginadorDTO.getQuantidadeTotalElementos());
            envelopeRespostaDTO.setTotalPages(paginadorDTO.getQuantidadeTotalElementos() / paginadorDTO.getSize());
            envelopeRespostaDTO.setFirst(primeiraPagina);
            envelopeRespostaDTO.setLast(ultimaPagina);
            envelopeRespostaDTO.setTotalElements(paginadorDTO.getQuantidadeTotalElementos());
            envelopeRespostaDTO.setSize(paginadorDTO.getSize());
            envelopeRespostaDTO.setNumber(paginadorDTO.getPage());
        }

        return envelopeRespostaDTO;
    }

    public Meta getMeta() {
        return meta;
    }

    public void setMeta(Meta meta) {
        this.meta = meta;
    }

    public Object getContent() {
        return content;
    }

    public void setContent(Object content) {
        this.content = content;
    }

    public Long getTotalElements() {
        return totalElements;
    }

    public void setTotalElements(Long totalElements) {
        this.totalElements = totalElements;
    }

    public Long getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(Long totalPages) {
        this.totalPages = totalPages;
    }

    public Boolean getFirst() {
        return first;
    }

    public void setFirst(Boolean first) {
        this.first = first;
    }

    public Boolean getLast() {
        return last;
    }

    public void setLast(Boolean last) {
        this.last = last;
    }

    public Long getNumberOfElements() {
        return numberOfElements;
    }

    public void setNumberOfElements(Long numberOfElements) {
        this.numberOfElements = numberOfElements;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public Long getNumber() {
        return number;
    }

    public void setNumber(Long number) {
        this.number = number;
    }

    public static class Meta {
        private String error;
        private String message;
        private String messageID;
        private String messageValue;

        public Meta(String error, String message) {
            this.error = error;
            this.message = message;
        }

        public Meta(String error, String message, String messageID, String messageValue) {
            this.error = error;
            this.message = message;
            this.messageID = messageID;
            this.messageValue = messageValue;
        }

        public String getError() {
            return error;
        }

        public void setError(String error) {
            this.error = error;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getMessageID() {
            return messageID;
        }

        public void setMessageID(String messageID) {
            this.messageID = messageID;
        }

        public String getMessageValue() {
            return messageValue;
        }

        public void setMessageValue(String messageValue) {
            this.messageValue = messageValue;
        }
    }
}
