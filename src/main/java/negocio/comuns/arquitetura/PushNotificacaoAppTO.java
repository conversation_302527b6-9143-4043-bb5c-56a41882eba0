package negocio.comuns.arquitetura;

import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;

import java.util.Date;

public class PushNotificacaoAppTO extends SuperTO {

    private String horario;
    private String ctx;
    private String titulo;
    private String message;
    private String usuario;
    private String path;

    public PushNotificacaoAppTO(String horario, String ctx, String titulo, String message, String usuario, String path) {
        this.horario = horario;
        this.ctx = ctx;
        this.titulo = titulo;
        this.message = message;
        this.usuario = usuario;
        this.path = path;
    }

    public PushNotificacaoAppTO() {
    }

    public String getHorario() {
        return horario;
    }

    public void setHorario(String horario) {
        this.horario = horario;
    }

    public String getCtx() {
        return ctx;
    }

    public void setCtx(String ctx) {
        this.ctx = ctx;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
}
