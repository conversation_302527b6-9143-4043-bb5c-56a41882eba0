package negocio.comuns.arquitetura;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 09/10/2024
 */

public class DetalhesRequestEnviadaVO extends SuperVO {

    private Integer codigo;
    private Date dataRegistro;
    private String url;
    private int statusResponse;
    private long tempoRequisicaoMs;
    private boolean sucesso = false;
    private String nomeTabelaForeignKey;
    private Integer codigoTabelaForeignKey;
    private String response;
    private String origem;
    private String body;

    public DetalhesRequestEnviadaVO() {
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getStatusResponse() {
        return statusResponse;
    }

    public void setStatusResponse(Integer statusResponse) {
        this.statusResponse = statusResponse;
    }

    public long getTempoRequisicaoMs() {
        return tempoRequisicaoMs;
    }

    public void setTempoRequisicaoMs(long tempoRequisicaoMs) {
        this.tempoRequisicaoMs = tempoRequisicaoMs;
    }

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public void setStatusResponse(int statusResponse) {
        this.statusResponse = statusResponse;
    }

    public String getNomeTabelaForeignKey() {
        return nomeTabelaForeignKey;
    }

    public void setNomeTabelaForeignKey(String nomeTabelaForeignKey) {
        this.nomeTabelaForeignKey = nomeTabelaForeignKey;
    }

    public Integer getCodigoTabelaForeignKey() {
        return codigoTabelaForeignKey;
    }

    public void setCodigoTabelaForeignKey(Integer codigoTabelaForeignKey) {
        this.codigoTabelaForeignKey = codigoTabelaForeignKey;
    }

    public String getResponse() {
        if (response == null) {
            return "";
        }
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public String getOrigem() {
        if (origem == null) {
            return "";
        }
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getBody() {
        if (body == null) {
            return "";
        }
        return body;
    }


}
