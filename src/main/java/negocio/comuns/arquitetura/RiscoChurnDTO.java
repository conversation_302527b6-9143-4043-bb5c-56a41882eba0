package negocio.comuns.arquitetura;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.ZonaChurnEnum;

public class RiscoChurnDTO {

    private Integer clientes;
    private Double percentual;
    private ZonaChurnEnum zona;

    public Integer getClientes() {
        return clientes;
    }

    public void setClientes(Integer clientes) {
        this.clientes = clientes;
    }

    public Double getPercentual() {
        return percentual;
    }

    public void setPercentual(Double percentual) {
        this.percentual = percentual;
    }

    public ZonaChurnEnum getZona() {
        return zona;
    }

    public void setZona(ZonaChurnEnum zona) {
        this.zona = zona;
    }

    public Integer getOrdinalZona() {
        return zona.ordinal();
    }

    public String getPercentualFormatado(){
        return Formatador.formatarValorMonetarioSemMoeda(percentual);
    }
}
