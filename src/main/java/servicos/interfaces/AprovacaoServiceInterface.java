/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.interfaces;

import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
public interface AprovacaoServiceInterface {

    TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception;

    TransacaoInterfaceFacade getTransacaoFacade();

    TransacaoVO confirmarTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception;

    TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception;

    TransacaoVO retransmitirTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception;

    TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal, Boolean estornarRecibo) throws Exception;

    TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception;

    void consultarSituacaoCobrancaTransacao(TransacaoVO transacaoVO) throws Exception;
}
