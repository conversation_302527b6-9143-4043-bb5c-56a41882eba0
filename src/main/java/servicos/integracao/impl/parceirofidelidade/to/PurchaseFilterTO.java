package servicos.integracao.impl.parceirofidelidade.to;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import negocio.comuns.arquitetura.SuperTO;

/**
 *
 * <AUTHOR>
 */
public class PurchaseFilterTO extends SuperTO {

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ssZ")
    private Date purchasedDateFrom;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ssZ")
    private Date purchasedDateTo;
    private String locator;
    private String deviceCode;
    private Integer _page;
    private Integer _pageSize = 100;

    public Date getPurchasedDateFrom() {
        return purchasedDateFrom;
    }

    public void setPurchasedDateFrom(Date purchasedDateFrom) {
        this.purchasedDateFrom = purchasedDateFrom;
    }

    public Date getPurchasedDateTo() {
        return purchasedDateTo;
    }

    public void setPurchasedDateTo(Date purchasedDateTo) {
        this.purchasedDateTo = purchasedDateTo;
    }

    public String getLocator() {
        return locator;
    }

    public void setLocator(String locator) {
        this.locator = locator;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public Integer getPage() {
        return _page;
    }

    public void setPage(Integer _page) {
        this._page = _page;
    }

    public Integer getPageSize() {
        return _pageSize;
    }

    public void setPageSize(Integer _pageSize) {
        this._pageSize = _pageSize;
    }
}
