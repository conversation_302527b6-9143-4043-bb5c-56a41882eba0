package servicos.integracao.impl.conciliadora;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.integracao.conciliadora.*;
import br.com.pactosolucoes.integracao.conciliadora.MensagemRetorno;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.thoughtworks.xstream.XStream;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.financeiro.enumerador.TipoRemessaEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.financeiro.CartaoCredito;
import negocio.facade.jdbc.financeiro.ConciliadoraEstorno;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.financeiro.MovPagamentoInterfaceFacade;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class ConciliadoraServiceImpl implements Serializable {

    private MovPagamentoInterfaceFacade movPagamentoDAO;
    private ReciboPagamento reciboPagamentoDAO;
    private ZillyonWebFacade zwFacade;
    private CartaoCredito cartaoCreditoDAO;
    private ConciliadoraEstorno conciliadoraEstornoDAO;

    public ConciliadoraServiceImpl(final Connection con) throws Exception {
        zwFacade = new ZillyonWebFacade(con);
        movPagamentoDAO = new MovPagamento(con);
        reciboPagamentoDAO = new ReciboPagamento(con);
        cartaoCreditoDAO = new CartaoCredito(con);
        conciliadoraEstornoDAO = new ConciliadoraEstorno(con);
    }

    private String getSQL(Integer empresa, Integer recibo, Date dataInicial, Date dataFinal, boolean reprocessar, boolean count) {

        StringBuilder sql = new StringBuilder();
        if (count) {
            sql.append("select count(*) from ( \n");
        }

        sql.append("select \n");
        sql.append("m.codigo \n");
        sql.append("from movpagamento m  \n");
        sql.append("inner join formapagamento fp on m.formapagamento = fp.codigo  \n");
        sql.append("inner join empresa e on m.empresa = e.codigo \n");
        sql.append("where e.usarconciliadora = true \n");
        sql.append("and m.credito = false \n");
        sql.append("and fp.tipoformapagamento in ('CA','CD') \n");

        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("and m.empresa = ").append(empresa).append(" \n");
        }

        if (!UteisValidacao.emptyNumber(recibo)) {
            reprocessar = true;
            sql.append("and m.recibopagamento = ").append(recibo).append(" \n");
        }

        if (!reprocessar) {
            sql.append("and m.enviadoConciliadora = false \n");
        }

        if (dataInicial != null) {
            sql.append("and m.datalancamento::date >= '").append(Uteis.getDataFormatoBD(dataInicial)).append("' \n");
        }

        if (dataFinal != null) {
            sql.append("and m.datalancamento::date <= '").append(Uteis.getDataFormatoBD(dataFinal)).append("' \n");
        }

        sql.append("and e.usarconciliadora = true \n");
        sql.append("order by m.codigo \n");

        if (count) {
            sql.append(" )as sql ");
        }

        return sql.toString();
    }

    public void estornarReciboConciliadora(Integer recibo) throws Exception {
        ConciliadoraEstorno conciliadoraEstornoDAO;
        try {
            conciliadoraEstornoDAO = new ConciliadoraEstorno(this.movPagamentoDAO.getCon());
            conciliadoraEstornoDAO.estornarConciliadora(recibo);
            processarEstornos(recibo);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            conciliadoraEstornoDAO = null;
        }
    }

    public void processarAutomatico(Date diaRobo) throws Exception {
        try {
            Uteis.logar(null, "Integração Conciliadora | Inicio...");
            processarConciliadora(0, 0, Uteis.somarDias(diaRobo, -30), diaRobo, false, false);
        } finally {
            Uteis.logar(null, "Integração Conciliadora | Fim...");
        }
    }

    public void processarConciliadora(Integer empresa, Integer reciboPagamento, Date dataInicial, Date dataFinal, boolean reprocessar, boolean manual) throws Exception {
        processarEstornos(0);

        Set<Integer> codMovPagamentoProcessados = new HashSet<>();
        Integer total = SuperFacadeJDBC.contar(getSQL(empresa, reciboPagamento, dataInicial, dataFinal, reprocessar, true), reciboPagamentoDAO.getCon());
        Uteis.logarDebug("Integração Conciliadora | Tamanho Lista: " + total);

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(getSQL(empresa, reciboPagamento, dataInicial, dataFinal, reprocessar, false), reciboPagamentoDAO.getCon())) {
            int i = 0;
            while (rs.next()) {
                Integer codMovPagamento = 0;
                try {
                    codMovPagamento = rs.getInt("codigo");
                    if (!codMovPagamentoProcessados.contains(codMovPagamento)) {
                        Uteis.logar(null, "Inicia Conciliadora - " + ++i + "/" + total);

                        if (!reprocessar) {
                            String retorno = verificarMovPagamentoJaProcessado(codMovPagamento);
                            if (!UteisValidacao.emptyString(retorno)) {
                                Uteis.logar(null, retorno);
                                continue;
                            }
                        }

                        processarMovPagamento(codMovPagamento, codMovPagamentoProcessados, manual);
                    } else {
                        Uteis.logar(null, "Ops.. Este MovPagamento já foi processado anteriormente - " + ++i + "/" + total);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    Uteis.logar(null, "##### Erro ao enviar Conciliadora | Movpagamento: " + codMovPagamento + " | Erro: " + ex.getMessage());
                }
            }
        }
    }

    private String verificarMovPagamentoJaProcessado(Integer movPagamento) throws Exception {
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select enviadoConciliadora,credito from movpagamento where codigo = " + movPagamento, reciboPagamentoDAO.getCon())) {
            while (rs.next()) {
                boolean enviadoConciliadora = rs.getBoolean("enviadoConciliadora");
                boolean credito = rs.getBoolean("credito");
                if (credito) {
                    return "Conciliadora | MovPagamento de crédito sejá ignorado | " + movPagamento;
                }
                if (enviadoConciliadora) {
                    return "Conciliadora | MovPagamento já processado Conciliadora | " + movPagamento;
                }
            }
        }
        return "";
    }

    private void processarMovPagamento(Integer codMovPagamento, Set<Integer> codMovPagamentoProcessados, boolean manual) throws Exception {
        Uteis.logar(null, "Movpagamento: " + codMovPagamento);

        codMovPagamentoProcessados.add(codMovPagamento);

        Set<Integer> codMovPagamentosProcessadosAgora = new HashSet<>();
        Integer codReciboPagamento = 0;
        String operadoraDescricao = "";
        boolean transacao = false;
        boolean remessa = false;
        Integer tipoTransacao = 0;
        Integer tipoRemessa = 0;
        Integer nrParcelaCartaoCredito = 0;

        String nsu = "";
        String numerounicotransacao = "";

        String autorizacaoCartao = "";

        Integer adquirenteCodigo = 0;
        String adquirenteDescricao = "";
        Integer formaPagamentoCodigo = 0;
        String formaPagamentoDescricao = "";
        String formaPagamentoTipo = "";
        Date dataLancamento = null;
        Date dataPagamento = null;

        Double valorMovPagamento = 0.0;
        Double valorTotalMovPagamento = 0.0;

        String idEmpresa = "";
        String senha = "";
        Integer empresaCodigo = 0;
        String empresaNome = "";
        String empresaRazaoSocial = "";
        Integer pessoaCodigo = 0;

        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("m.codigo as movpagamento, \n");
        sql.append("e.empresaconciliadora, \n");
        sql.append("e.senhaconciliadora, \n");
        sql.append("e.codigo as empresaCodigo, \n");
        sql.append("e.nome as empresaNome, \n");
        sql.append("e.razaosocial as empresaRazaoSocial, \n");
        sql.append("exists(select codigo from transacao where movpagamento = m.codigo) as transacao,  \n");
        sql.append("(select tipo from transacao where movpagamento = m.codigo order by codigo desc limit 1) as tipotransacao,  \n");
        sql.append("(select tipo from remessaitem where movpagamento = m.codigo order by codigo desc limit 1) as tiporemessa,  \n");
        sql.append("exists(select codigo from remessaitem where movpagamento = m.codigo) as remessa, \n");
        sql.append("ad.codigo as adquirenteCodigo, \n");
        sql.append("ad.nome as adquirenteDescricao, \n");
        sql.append("oc.codigo as operadoraCodigo, \n");
        sql.append("oc.descricao as operadoraDescricao, \n");
        sql.append("fp.codigo as formaPagamentoCodigo, \n");
        sql.append("fp.descricao as formaPagamentoDescricao, \n");
        sql.append("fp.tipoformapagamento as formaPagamentoTipo, \n");
        sql.append("m.nrparcelacartaocredito, \n");
        sql.append("m.datalancamento, \n");
        sql.append("m.datapagamento, \n");
        sql.append("m.valor as valorMovPagamento, \n");
        sql.append("m.valortotal as valorTotalMovPagamento, \n");
        sql.append("m.autorizacaocartao, \n");
        sql.append("m.nsu, \n");
        sql.append("m.numerounicotransacao, \n");
        sql.append("m.recibopagamento, \n");
        sql.append("m.pessoa \n");
        sql.append("from movpagamento m  \n");
        sql.append("left join adquirente ad on ad.codigo = m.adquirente \n");
        sql.append("left join operadoracartao oc on oc.codigo = m.operadoracartao \n");
        sql.append("inner join formapagamento fp on fp.codigo = m.formapagamento \n");
        sql.append("inner join empresa e on e.codigo = m.empresa \n");
        sql.append("where m.codigo = ").append(codMovPagamento).append(" \n");
        sql.append("and (m.valor > 0 or m.valortotal > 0) ");

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), reciboPagamentoDAO.getCon())) {
            while (rs.next()) {
                transacao = rs.getBoolean("transacao");
                remessa = rs.getBoolean("remessa");
                tipoTransacao = rs.getInt("tipoTransacao");
                tipoRemessa = rs.getInt("tipoRemessa");
                nrParcelaCartaoCredito = rs.getInt("nrparcelacartaocredito");
                adquirenteCodigo = rs.getInt("adquirenteCodigo");
                adquirenteDescricao = rs.getString("adquirenteDescricao");
                operadoraDescricao = rs.getString("operadoraDescricao");
                formaPagamentoCodigo = rs.getInt("formaPagamentoCodigo");
                formaPagamentoDescricao = rs.getString("formaPagamentoDescricao");
                formaPagamentoTipo = rs.getString("formaPagamentoTipo");
                nsu = rs.getString("nsu");
                autorizacaoCartao = rs.getString("autorizacaoCartao");
                dataLancamento = rs.getTimestamp("dataLancamento");
                dataPagamento = rs.getTimestamp("dataPagamento");
                idEmpresa = rs.getString("empresaconciliadora");
                senha = rs.getString("senhaconciliadora");
                empresaCodigo = rs.getInt("empresaCodigo");
                empresaNome = rs.getString("empresaNome");
                empresaRazaoSocial = rs.getString("empresaRazaoSocial");
                valorMovPagamento = rs.getDouble("valorMovPagamento");
                valorTotalMovPagamento = rs.getDouble("valorTotalMovPagamento");
                codReciboPagamento = rs.getInt("recibopagamento");
                numerounicotransacao = rs.getString("numerounicotransacao");
                pessoaCodigo = rs.getInt("pessoa");
            }
        }


        String dataVenda = Uteis.getDataAplicandoFormatacao(dataLancamento, "dd/MM/yyyy");
        String codigoAutorizacao = StringUtilities.formatarCampoForcandoZerosAEsquerda(autorizacaoCartao, 6);
        String valorTotal = String.valueOf(Uteis.arredondarForcando2CasasDecimais(valorTotalMovPagamento));
        String terminal = formaPagamentoCodigo + " - " + formaPagamentoDescricao;

        if (terminal.length() > 30) {
            String descErro = "Terminal: '" + terminal + "', ultrapassa o limite de 30 caracteres.";
            registrarLogConciliadora(descErro, false, empresaCodigo, codMovPagamento, manual);
            throw new Exception(descErro);
        }

        boolean parcelada = nrParcelaCartaoCredito > 1;
        boolean cartaoCredito = formaPagamentoTipo.equals(TipoFormaPagto.CARTAOCREDITO.getSigla());
        boolean cartaoDebito = formaPagamentoTipo.equals(TipoFormaPagto.CARTAODEBITO.getSigla());

        nsu = (UteisValidacao.emptyString(numerounicotransacao) ? nsu : numerounicotransacao);
        operadoraDescricao = Uteis.retirarAcentuacao(operadoraDescricao).toUpperCase();

        ProdutoEnumConciliadora produto;
        if (cartaoDebito) { //DÉBITO

            if (operadoraDescricao.contains("VISA") || operadoraDescricao.contains("ELECTRON")) {
                produto = ProdutoEnumConciliadora.COD_2;
            } else if (operadoraDescricao.contains("MASTER") || operadoraDescricao.contains("MAESTRO")) {
                produto = ProdutoEnumConciliadora.COD_5;
            } else if (operadoraDescricao.contains("AMEX") || operadoraDescricao.contains("AMERICAN")) {
                produto = ProdutoEnumConciliadora.COD_99;
            } else if (operadoraDescricao.contains("HIPERCARD") || operadoraDescricao.contains("HIPER CARD") || operadoraDescricao.contains("HIPER")) {
                produto = ProdutoEnumConciliadora.COD_67;
            } else if (operadoraDescricao.contains("JCB")) {
                produto = ProdutoEnumConciliadora.COD_34;
            } else if (operadoraDescricao.contains("SOROCRED") || operadoraDescricao.contains("SORO CRED")) {
                produto = ProdutoEnumConciliadora.COD_81;
            } else if (operadoraDescricao.contains("AURA")) {
                produto = ProdutoEnumConciliadora.COD_99;
            } else if (operadoraDescricao.contains("ELO")) {
                produto = ProdutoEnumConciliadora.COD_8;
            } else {
                produto = ProdutoEnumConciliadora.COD_99;
            }

        } else if (!parcelada && cartaoCredito) { //CREDITO A VISTA

            if (operadoraDescricao.contains("VISA") || operadoraDescricao.contains("ELECTRON")) {
                produto = ProdutoEnumConciliadora.COD_1;
            } else if (operadoraDescricao.contains("MASTER") || operadoraDescricao.contains("MAESTRO")) {
                produto = ProdutoEnumConciliadora.COD_4;
            } else if (operadoraDescricao.contains("AMEX") || operadoraDescricao.contains("AMERICAN")) {
                produto = ProdutoEnumConciliadora.COD_197;
            } else if (operadoraDescricao.contains("HIPERCARD") || operadoraDescricao.contains("HIPER CARD") || operadoraDescricao.contains("HIPER")) {
                produto = ProdutoEnumConciliadora.COD_66;
            } else if (operadoraDescricao.contains("JCB")) {
                produto = ProdutoEnumConciliadora.COD_34;
            } else if (operadoraDescricao.contains("SOROCRED") || operadoraDescricao.contains("SORO CRED")) {
                produto = ProdutoEnumConciliadora.COD_43;
            } else if (operadoraDescricao.contains("AURA")) {
                produto = ProdutoEnumConciliadora.COD_99;
            } else if (operadoraDescricao.contains("ELO")) {
                produto = ProdutoEnumConciliadora.COD_7;
            } else {
                produto = ProdutoEnumConciliadora.COD_99;
            }

        } else if (parcelada && cartaoCredito) { //CREDITO PARCELADO

            if (operadoraDescricao.contains("VISA") || operadoraDescricao.contains("ELECTRON")) {
                produto = ProdutoEnumConciliadora.COD_3;
            } else if (operadoraDescricao.contains("MASTER") || operadoraDescricao.contains("MAESTRO")) {
                produto = ProdutoEnumConciliadora.COD_6;
            } else if (operadoraDescricao.contains("AMEX") || operadoraDescricao.contains("AMERICAN")) {
                produto = ProdutoEnumConciliadora.COD_198;
            } else if (operadoraDescricao.contains("HIPERCARD") || operadoraDescricao.contains("HIPER CARD") || operadoraDescricao.contains("HIPER")) {
                produto = ProdutoEnumConciliadora.COD_68;
            } else if (operadoraDescricao.contains("JCB")) {
                produto = ProdutoEnumConciliadora.COD_34;
            } else if (operadoraDescricao.contains("SOROCRED") || operadoraDescricao.contains("SORO CRED")) {
                produto = ProdutoEnumConciliadora.COD_44;
            } else if (operadoraDescricao.contains("AURA")) {
                produto = ProdutoEnumConciliadora.COD_99;
            } else if (operadoraDescricao.contains("ELO")) {
                produto = ProdutoEnumConciliadora.COD_9;
            } else {
                produto = ProdutoEnumConciliadora.COD_99;
            }
        } else {
            produto = ProdutoEnumConciliadora.COD_99;
        }

        MeioCapturaEnumConciliadora meioCaptura = MeioCapturaEnumConciliadora.MANUAL;
        if (cartaoDebito) {
            meioCaptura = MeioCapturaEnumConciliadora.PDV;
        } else if (remessa) {
            meioCaptura = MeioCapturaEnumConciliadora.EDI;
        } else if (transacao) {
            meioCaptura = MeioCapturaEnumConciliadora.E_COMMERCE;
        }


        OperadoraEnumConciliadora operadora = null;

        if (!UteisValidacao.emptyNumber(adquirenteCodigo)) {

            if (adquirenteDescricao.toUpperCase().contains("CIELO")) {
                operadora = OperadoraEnumConciliadora.CIELO;
            } else if (adquirenteDescricao.toUpperCase().contains("BIN")) {
                operadora = OperadoraEnumConciliadora.BIN;
            } else if (adquirenteDescricao.toUpperCase().contains("GETNET") || adquirenteDescricao.toUpperCase().contains("GET NET")) {
                operadora = OperadoraEnumConciliadora.GETNET;
            } else if (adquirenteDescricao.toUpperCase().contains("STONE")) {
                operadora = OperadoraEnumConciliadora.STONE;
            } else if (adquirenteDescricao.toUpperCase().contains("EREDE") || adquirenteDescricao.toUpperCase().contains("REDE") ||
                    adquirenteDescricao.toUpperCase().contains("E-REDE") || adquirenteDescricao.toUpperCase().contains("NEXXERA")) {
                operadora = OperadoraEnumConciliadora.REDE;
            }

        } else if (!UteisValidacao.emptyNumber(tipoTransacao)) {

            TipoTransacaoEnum tipoTransacaoEnum = TipoTransacaoEnum.getTipoTransacaoEnum(tipoTransacao);

            if (tipoTransacaoEnum.equals(TipoTransacaoEnum.CIELO_ONLINE) || tipoTransacaoEnum.equals(TipoTransacaoEnum.CIELO_DEBITO_ONLINE)) {
                operadora = OperadoraEnumConciliadora.CIELO;
            } else if (tipoTransacaoEnum.equals(TipoTransacaoEnum.E_REDE) || tipoTransacaoEnum.equals(TipoTransacaoEnum.E_REDE_DEBITO)) {
                operadora = OperadoraEnumConciliadora.REDE;
            } else if (tipoTransacaoEnum.equals(TipoTransacaoEnum.STONE_ONLINE)) {
                operadora = OperadoraEnumConciliadora.STONE;
            } else if (tipoTransacaoEnum.equals(TipoTransacaoEnum.GETNET_ONLINE)) {
                operadora = OperadoraEnumConciliadora.GETNET;
            }

        } else if (!UteisValidacao.emptyNumber(tipoRemessa)) {

            TipoRemessaEnum tipoRemessaEnum = TipoRemessaEnum.getTipoRemessaEnum(tipoRemessa);

            if (tipoRemessaEnum.equals(TipoRemessaEnum.DCC_BIN)) {
                operadora = OperadoraEnumConciliadora.BIN;
            } else if (tipoRemessaEnum.equals(TipoRemessaEnum.EDI_CIELO)) {
                operadora = OperadoraEnumConciliadora.CIELO;
            } else if (tipoRemessaEnum.equals(TipoRemessaEnum.DCC_STONE)) {
                operadora = OperadoraEnumConciliadora.STONE;
            } else if (tipoRemessaEnum.equals(TipoRemessaEnum.GET_NET) || tipoRemessaEnum.equals(TipoRemessaEnum.DCC_GETNET_ONLINE)) {
                operadora = OperadoraEnumConciliadora.GETNET;
            }

        }

        if (operadora == null) {
            operadora = OperadoraEnumConciliadora.CIELO;
        }

        ModalidadeEnumConciliadora modalidade = ModalidadeEnumConciliadora.DEBITO;
        if (cartaoDebito) {
            modalidade = ModalidadeEnumConciliadora.DEBITO;
        } else if (!parcelada && cartaoCredito) {
            modalidade = ModalidadeEnumConciliadora.CREDITO_A_VISTA;
        } else if (parcelada && cartaoCredito) {
            modalidade = ModalidadeEnumConciliadora.CREDITO_PARCELADO;
        }


        ListaConciliadoraTO listaConciliadoraTO = new ListaConciliadoraTO();
        listaConciliadoraTO.setRegistro(new ArrayList<RegistroConciliadoraTO>());
        CabecalhoConciliadoraTO cabecalhoConciliadoraTO = new CabecalhoConciliadoraTO();

        cabecalhoConciliadoraTO.setVersao("3"); // FIXO
        cabecalhoConciliadoraTO.setEmpresa(idEmpresa);
        cabecalhoConciliadoraTO.setNomeSistema("ZillyonWeb - " + empresaRazaoSocial);
        cabecalhoConciliadoraTO.setDataInicial(dataVenda);
        cabecalhoConciliadoraTO.setDataFinal(dataVenda);
        cabecalhoConciliadoraTO.setLote(codMovPagamento);
        listaConciliadoraTO.setCabecalho(cabecalhoConciliadoraTO);

        if (cartaoCredito) {

            List<CartaoCreditoVO> listaCartoes = obterCartoes(codMovPagamentosProcessadosAgora, codMovPagamentoProcessados, dataLancamento, autorizacaoCartao, codMovPagamento, pessoaCodigo);

            for (CartaoCreditoVO cartaoCreditoVO : listaCartoes) {
                if (!cartaoCreditoVO.getSituacao().equalsIgnoreCase("EA")) {
                    continue;
                }

                RegistroConciliadoraTO registroConciliadoraTO = new RegistroConciliadoraTO();

                String identificador = "Rec. " + codReciboPagamento + " - Pag. " + codMovPagamento + " - Credito " + cartaoCreditoVO.getCodigo();
                String valorParcela = String.valueOf(Uteis.arredondarForcando2CasasDecimais(cartaoCreditoVO.getValor()));
                String dataCompensacao = Uteis.getDataAplicandoFormatacao(cartaoCreditoVO.getDataCompensacao(), "dd/MM/yyyy");

                registroConciliadoraTO.setProduto(produto.getCodigo());
                registroConciliadoraTO.setCodigoAutorizacao(codigoAutorizacao);
                registroConciliadoraTO.setIdentificadorPagamento(identificador);
                registroConciliadoraTO.setDataVenda(dataVenda);
                registroConciliadoraTO.setDataVencimento(dataCompensacao);
                registroConciliadoraTO.setValorVendaParcela(valorParcela);
                registroConciliadoraTO.setTotalVenda(valorTotal);
                registroConciliadoraTO.setParcela(StringUtilities.formatarCampo(new BigDecimal(cartaoCreditoVO.getNrParcela()), 2));
                registroConciliadoraTO.setTotalDeParcelas(StringUtilities.formatarCampo(new BigDecimal(nrParcelaCartaoCredito), 2));
                registroConciliadoraTO.setNsu(nsu);
                registroConciliadoraTO.setTerminal(terminal);
                registroConciliadoraTO.setMeioCaptura(meioCaptura.getCodigo().toString());
                registroConciliadoraTO.setOperadora(operadora.getCodigo().toString());
                registroConciliadoraTO.setModalidade(modalidade.getCodigo().toString());

                listaConciliadoraTO.getRegistro().add(registroConciliadoraTO);
            }

        } else if (cartaoDebito) {

            RegistroConciliadoraTO registroConciliadoraTO = new RegistroConciliadoraTO();

            String identificador = "Rec. " + codReciboPagamento + " - Pag. " + codMovPagamento + " - Debito " + codMovPagamento;
            String valorParcela = String.valueOf(Uteis.arredondarForcando2CasasDecimais(valorMovPagamento));
            String dataCompensacao = Uteis.getDataAplicandoFormatacao(dataPagamento, "dd/MM/yyyy");

            registroConciliadoraTO.setProduto(produto.getCodigo());
            registroConciliadoraTO.setCodigoAutorizacao(codigoAutorizacao);
            registroConciliadoraTO.setIdentificadorPagamento(identificador);
            registroConciliadoraTO.setDataVenda(dataVenda);
            registroConciliadoraTO.setDataVencimento(dataCompensacao);
            registroConciliadoraTO.setValorVendaParcela(valorParcela);
            registroConciliadoraTO.setTotalVenda(valorTotal);
            registroConciliadoraTO.setParcela("00");
            registroConciliadoraTO.setTotalDeParcelas("00");
            registroConciliadoraTO.setNsu(nsu);
            registroConciliadoraTO.setTerminal(terminal);
            registroConciliadoraTO.setMeioCaptura(meioCaptura.getCodigo().toString());
            registroConciliadoraTO.setOperadora(operadora.getCodigo().toString());
            registroConciliadoraTO.setModalidade(modalidade.getCodigo().toString());

            listaConciliadoraTO.getRegistro().add(registroConciliadoraTO);
            codMovPagamentosProcessadosAgora.add(codMovPagamento);
        }

        if (listaConciliadoraTO.getRegistro() != null && listaConciliadoraTO.getRegistro().size() > 0) {

            String msgRetorno = enviarConciliadora(listaConciliadoraTO, idEmpresa, senha, codMovPagamento);
            Uteis.logar(null, (msgRetorno.equalsIgnoreCase("ok") ? "Enviado com sucesso" : "Falha ao enviar")+
                    " para Conciliadora | Movpagamento: " + codMovPagamento + " | Autorização: "+codigoAutorizacao+ " | Data Venda: "+dataVenda);
            boolean sucesso = false;
            if (msgRetorno.equalsIgnoreCase("ok")) {
                for (Integer codigoMovPagamento : codMovPagamentosProcessadosAgora) {
                    movPagamentoDAO.marcarEnviadoConciliadora(true, codigoMovPagamento);
                }
                sucesso = true;
            }

            for (Integer codigoMovPagamento : codMovPagamentosProcessadosAgora) {
                registrarLogConciliadora(msgRetorno, sucesso, empresaCodigo, codigoMovPagamento, manual);
            }
            Uteis.logar(null, msgRetorno);
            Uteis.logar(null, "Fim Conciliadora | Movpagamento: " + codMovPagamento);
        }
    }

    private List<CartaoCreditoVO> obterCartoes(Set<Integer> codMovPagamentosProcessadosAgora,
                                               Set<Integer> codMovPagamentoProcessados,
                                               Date dataLancamento, String autorizacao,
                                               Integer codMovPagamento, Integer pessoa) throws Exception {

        if (dataLancamento == null || UteisValidacao.emptyNumber(pessoa) || UteisValidacao.emptyString(autorizacao)) {
            return cartaoCreditoDAO.consultarCartaoCreditos(codMovPagamento, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        StringBuilder consulta = new StringBuilder();
        consulta.append("select \n");
        consulta.append("codigo \n");
        consulta.append("from movpagamento \n");
        consulta.append("where datalancamento::date = '").append(Uteis.getDataFormatoBD(dataLancamento)).append("' \n");
        consulta.append("and pessoa = ").append(pessoa).append(" \n");
        consulta.append("and autorizacaocartao = '").append(autorizacao).append("' \n");
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(consulta.toString(), reciboPagamentoDAO.getCon())) {
            while (rs.next()) {
                codMovPagamentosProcessadosAgora.add(rs.getInt("codigo"));
               codMovPagamentoProcessados.add(rs.getInt("codigo"));
            }
        }

        if (codMovPagamentoProcessados.size() <= 1) {
            return cartaoCreditoDAO.consultarCartaoCreditos(codMovPagamento, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("datacompesancao::date, \n");
        sql.append("nrparcela, \n");
        sql.append("situacao, \n");
        sql.append("sum(valor) as valor, \n");
        sql.append("max(codigo) as codigo \n");
        sql.append("from cartaocredito  \n");
        sql.append("where situacao = 'EA'  \n");
        sql.append("and movpagamento in (select codigo from movpagamento \n");
        sql.append("where datalancamento::date = '").append(Uteis.getDataFormatoBD(dataLancamento)).append("' \n");
        sql.append("and pessoa = ").append(pessoa).append(" \n");
        sql.append("and autorizacaocartao = '").append(autorizacao).append("') \n");
        sql.append("group by 1,2,3 \n");
        sql.append("order by nrparcela \n");

        List<CartaoCreditoVO> lista = new ArrayList<>();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), reciboPagamentoDAO.getCon())) {
            while (rs.next()) {
                CartaoCreditoVO cartaoCreditoVO = new CartaoCreditoVO() ;
                cartaoCreditoVO.setDataCompensacao(rs.getDate("datacompesancao"));
                cartaoCreditoVO.setNrParcela(rs.getInt("nrparcela"));
                cartaoCreditoVO.setSituacao(rs.getString("situacao"));
                cartaoCreditoVO.setValor(rs.getDouble("valor"));
                cartaoCreditoVO.setCodigo(rs.getInt("codigo"));
                lista.add(cartaoCreditoVO);
            }
        }
        return lista;
    }

    private String enviarConciliadora(ListaConciliadoraTO listaConciliadoraTO,
                                      String idEmpresa, String senha,
                                      Integer codMovPagamento) throws Exception {

        XStream xstream = new XStream();
        xstream.alias("registros", ListaConciliadoraTO.class);
        xstream.processAnnotations(CabecalhoConciliadoraTO.class);
        xstream.processAnnotations(RegistroConciliadoraTO.class);
        xstream.addImplicitCollection(ListaConciliadoraTO.class, "registro");
        String xmlRegistros = xstream.toXML(listaConciliadoraTO);

        WSCACliente wscaCliente = new WSCACliente();
        IWSCACliente iwscaCliente = wscaCliente.getBasicHttpsBindingIWSCACliente();
        String response = iwscaCliente.xmlVendaCliente(Integer.parseInt(idEmpresa.replaceAll(" ", "")), senha, xmlRegistros);

        JAXBContext jaxbContext = JAXBContext.newInstance(br.com.pactosolucoes.integracao.conciliadora.MensagemRetorno.class);
        Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
        InputStream targetStream = new ByteArrayInputStream(response.getBytes());

        br.com.pactosolucoes.integracao.conciliadora.MensagemRetorno employee = null;
        try {
            employee = (br.com.pactosolucoes.integracao.conciliadora.MensagemRetorno) jaxbUnmarshaller.unmarshal(targetStream);
        } catch (Exception ex) {
            Uteis.logarDebug(ex.getMessage());
        }

        if (employee != null) {
            Uteis.logarDebug("MovPagamento: " + codMovPagamento);
            Uteis.logarDebug("RETORNO Conciliadora: " + employee.getMensagem());

            StringBuilder msgRetorno = new StringBuilder();
            msgRetorno.append(employee.getMensagem());

            if (employee.getMensagem().toUpperCase().contains("SUCESSO")) {
                return "ok";
            } else {
                if (employee.getXMLErros() != null && employee.getXMLErros().getErroArquivo() != null && employee.getXMLErros().getErroArquivo().size() > 0) {
                    msgRetorno.append("\nMovPagamento: ").append(codMovPagamento).append("\n");
                    for (MensagemRetorno.XMLErros.ErroArquivo erro : employee.getXMLErros().getErroArquivo()) {
                        msgRetorno.append(erro.getErro()).append("\n");
                        Uteis.logar(null, erro.getErro());
                    }
                }
            }
            return msgRetorno.toString();
        } else {
            return "Erro ao enviar informações para conciliadora";
        }
    }

    private void registrarLogConciliadora(String mensagem, boolean sucesso, Integer empresa, Integer movPagamento, boolean manual) {
        try {
            //REGISTRAR LogConciliadora
            String sqlLog = "insert into logconciliadora(data, empresa, movPagamento, sucesso, resultado, manual) values (?, ?, ?, ?, ?, ?); ";
            try (PreparedStatement pst = zwFacade.getCon().prepareStatement(sqlLog)) {
                pst.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                pst.setInt(2, empresa);
                pst.setInt(3, movPagamento);
                pst.setBoolean(4, sucesso);
                pst.setString(5, mensagem);
                pst.setBoolean(6, manual);
                pst.execute();
            }
        } catch (Exception ex) {
            Uteis.logar(null, "ERRO AO REGISTRAR LOG CONCILIADORA: " + ex.getMessage());
        }
    }

    public List<LogConciliadoraVO> obterLogConciliadora(Integer empresa, Integer movPagamento) {
        try {
            List<LogConciliadoraVO> retorno = new ArrayList<LogConciliadoraVO>();
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("* \n");
            sql.append("from logconciliadora \n");
            sql.append("where 1 = 1 \n");
            if (!UteisValidacao.emptyNumber(empresa)) {
                sql.append("and empresa = ").append(empresa).append(" \n");
            }
            if (!UteisValidacao.emptyNumber(movPagamento)) {
                sql.append("and movpagamento = ").append(movPagamento).append(" \n");
            }
            sql.append("order by data desc ");

            try (PreparedStatement sqlConsultar = zwFacade.getCon().prepareStatement(sql.toString())) {
                try (ResultSet rs = sqlConsultar.executeQuery()) {
                    while (rs.next()) {
                        LogConciliadoraVO log = new LogConciliadoraVO();
                        log.setCodigo(rs.getInt("codigo"));
                        log.setData(rs.getTimestamp("data"));
                        log.getEmpresaVO().setCodigo(rs.getInt("empresa"));
                        log.getMovPagamentoVO().setCodigo(rs.getInt("movpagamento"));
                        log.setSucesso(rs.getBoolean("sucesso"));
                        log.setResultado(rs.getString("resultado"));
                        retorno.add(log);
                    }
                }
            }
            return retorno;
        } catch (Exception ex) {
            return new ArrayList<LogConciliadoraVO>();
        }
    }

    private void processarEstornos(Integer reciboPagamento) {
        Uteis.logar(null, "Integração Conciliadora | Inicio... Processar Estornos...");
        try {
            List<EstornoConciliadoraTO> lista = conciliadoraEstornoDAO.consultarEstornosConciliacao(reciboPagamento);

            Uteis.logar(null, "Integração Conciliadora | Processar Estornos: " + lista.size() + " itens");

            for (EstornoConciliadoraTO obj : lista) {

                ListaConciliadoraTO listaConciliadoraTO = new ListaConciliadoraTO();
                listaConciliadoraTO.setRegistro(new ArrayList<>());
                CabecalhoConciliadoraTO cabecalhoConciliadoraTO = new CabecalhoConciliadoraTO();

                String dataVenda = Uteis.getDataAplicandoFormatacao(obj.getDataLancamento(), "dd/MM/yyyy");

                cabecalhoConciliadoraTO.setVersao("3"); // FIXO
                cabecalhoConciliadoraTO.setEmpresa(obj.getEmpresaConciliadora());
                cabecalhoConciliadoraTO.setNomeSistema("ZillyonWeb - Estorno");
                cabecalhoConciliadoraTO.setDataInicial(dataVenda);
                cabecalhoConciliadoraTO.setDataFinal(dataVenda);
                cabecalhoConciliadoraTO.setLote(obj.getMovPagamento());
                listaConciliadoraTO.setCabecalho(cabecalhoConciliadoraTO);

                String msgRetorno = enviarConciliadora(listaConciliadoraTO, obj.getEmpresaConciliadora(), obj.getSenhaConciliadora(), obj.getMovPagamento());
                if (msgRetorno.equalsIgnoreCase("ok")) {
                    conciliadoraEstornoDAO.marcarEstornarConciliadoraProcessado(true, obj.getCodigo());
                }

            }
        } catch (Exception e) {
            System.out.println("Erro ao estornar conciliadora");
        }
        Uteis.logar(null, "Integração Conciliadora | Fim... Processar Estornos...");
    }

    public static void main(String[] args) {
        try {
            Uteis.debug = true;
//            Connection c = DriverManager.getConnection("*********************************************", "postgres", "pactodb");

            String chave = args.length > 0 ? args[0] : "teste";
            Connection c = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(c);
            ConciliadoraServiceImpl conciliadoraService = new ConciliadoraServiceImpl(c);

            if (args.length == 1) {
                conciliadoraService.processarAutomatico(Calendario.hoje());
            } else if (args.length > 1) {
                Date dataInicial = Calendario.getDate("ddMMyyyy", args[1]);
                Date dataFinal = Calendario.getDate("ddMMyyyy", args[2]);
                conciliadoraService.processarConciliadora(0, 0, dataInicial, dataFinal, true, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logar(null, e.getMessage());
        }
    }
}
