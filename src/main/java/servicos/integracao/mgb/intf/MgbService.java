package servicos.integracao.mgb.intf;

import negocio.interfaces.basico.SuperInterface;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.integracao.mgb.impl.StudentDTO;

import java.util.Date;

public interface MgbService extends SuperInterface {

    Boolean integradoMgb(Integer empresa);

    JSONObject  consultarNiveis(Integer empresa) throws Exception;

    JSONObject  consultarPiscinas(Integer empresa) throws Exception;

    void postStudentMgb(Integer empresa, Integer matricula, Boolean sincronizarComTurma, boolean alunoJaConsultadoPorMatriculaMgb) throws Exception;

    String verificarPublicidmgb (Integer matricula) throws Exception;

    StudentDTO findByMatricula(Integer matricula, String publicId) throws Exception;

    void  alterarpublicidmgb(Integer matricula, String codigoMgb, String body) throws Exception;

    String tokenIntegracao(Integer empresa) throws Exception;

    String nivelAluno(Integer empresa, String matricula) throws Exception;

    void insertToken(String chave, Integer empresa, String token) throws Exception;

    JSONArray tokens() throws Exception;

    void updateToken(String chave, Integer empresa, String token) throws Exception;

    String sincronizarAlunosAtivos(Integer empresa) throws Exception;

    String syncTodasTurmasComMgb(Integer empresa, String ctx, Boolean vaiValidarVagas, Integer margemMinutoInicioFimHorario) throws Exception;

    String syncHorarioTurmaMgb(String ctx, Integer empresa, Integer codHorarioTurma, Boolean vaiValidarVagas, Integer margemMinutoInicioFimHorario) throws Exception;

    String syncTurmaMgb(String ctx, Integer empresa, Integer codTurma, Boolean vaiValidarVagas, Integer margemMinutoInicioFimHorario) throws Exception;

    String inativarTurmaMgb(Integer empresa, Integer codigoHorarioTurma) throws Exception;

    String deletarTodasTurmasNoMgb(Integer empresa, Boolean apenasValidar) throws Exception;

    String deletarTurmaMgb(Integer empresa, Integer codigoHorarioTurma) throws Exception;

    String syncAlunoMgb(Integer empresa, Integer codigoCliente, Integer codigoPessoa) throws Exception;

    JSONObject consultarDadosMgbAluno(Integer empresa, Integer codigoCliente) throws Exception;

    void presencaAlunoTurmaMgb(Integer empresa, Integer matricula, Integer codigoHorarioTurma, Date dia, Boolean compareceu) throws Exception;

    void sincronizarProfessorMgb(Integer empresa, Integer codColaborador) throws Exception;

    String deletarPublicIdProfessor(Integer empresa, Integer codColaborador) throws Exception;

    String removerUtilizacaoPiscina(String publicIdPiscina) throws Exception;

}

