
package servicos.integracao.treino;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the servicos.integracao.treino package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _EstornarCreditosResponse_QNAME = new QName("http://webservice.pacto.com.br/", "estornarCreditosResponse");
    private final static QName _DeleteAlunoPesquisaTWResponse_QNAME = new QName("http://webservice.pacto.com.br/", "deleteAlunoPesquisaTWResponse");
    private final static QName _EnviarNotificacoesResponse_QNAME = new QName("http://webservice.pacto.com.br/", "enviarNotificacoesResponse");
    private final static QName _AtualizarFrequenciaSemanal_QNAME = new QName("http://webservice.pacto.com.br/", "atualizarFrequenciaSemanal");
    private final static QName _AdicionarPersonal_QNAME = new QName("http://webservice.pacto.com.br/", "adicionarPersonal");
    private final static QName _DeleteAlunoPesquisaTW_QNAME = new QName("http://webservice.pacto.com.br/", "deleteAlunoPesquisaTW");
    private final static QName _SincronizarRiscoClienteResponse_QNAME = new QName("http://webservice.pacto.com.br/", "sincronizarRiscoClienteResponse");
    private final static QName _SincronizarRiscoCliente_QNAME = new QName("http://webservice.pacto.com.br/", "sincronizarRiscoCliente");
    private final static QName _EstornarFechamentoCreditosResponse_QNAME = new QName("http://webservice.pacto.com.br/", "estornarFechamentoCreditosResponse");
    private final static QName _InserirCreditosResponse_QNAME = new QName("http://webservice.pacto.com.br/", "inserirCreditosResponse");
    private final static QName _ObterTodosProgramasAlunosResponse_QNAME = new QName("http://webservice.pacto.com.br/", "obterTodosProgramasAlunosResponse");
    private final static QName _AdicionarPersonalResponse_QNAME = new QName("http://webservice.pacto.com.br/", "adicionarPersonalResponse");
    private final static QName _ObterTodosAlunos_QNAME = new QName("http://webservice.pacto.com.br/", "obterTodosAlunos");
    private final static QName _AtualizarFrequenciaSemanalResponse_QNAME = new QName("http://webservice.pacto.com.br/", "atualizarFrequenciaSemanalResponse");
    private final static QName _ObterConfiguracaoSistema_QNAME = new QName("http://webservice.pacto.com.br/", "obterConfiguracaoSistema");
    private final static QName _SincronizarParqResponse_QNAME = new QName("http://webservice.pacto.com.br/", "sincronizarParqResponse");
    private final static QName _ObterTodosProgramasAlunos_QNAME = new QName("http://webservice.pacto.com.br/", "obterTodosProgramasAlunos");
    private final static QName _ObterDataUltimaAtualizacaoResponse_QNAME = new QName("http://webservice.pacto.com.br/", "obterDataUltimaAtualizacaoResponse");
    private final static QName _RealizouTreinoNovoResponse_QNAME = new QName("http://webservice.pacto.com.br/", "realizouTreinoNovoResponse");
    private final static QName _SincronizarProfessor_QNAME = new QName("http://webservice.pacto.com.br/", "sincronizarProfessor");
    private final static QName _AtualizarStatusAluno_QNAME = new QName("http://webservice.pacto.com.br/", "atualizarStatusAluno");
    private final static QName _InserirCreditos_QNAME = new QName("http://webservice.pacto.com.br/", "inserirCreditos");
    private final static QName _AddAlunoPesquisaTWResponse_QNAME = new QName("http://webservice.pacto.com.br/", "addAlunoPesquisaTWResponse");
    private final static QName _AtualizarCrossfitResponse_QNAME = new QName("http://webservice.pacto.com.br/", "atualizarCrossfitResponse");
    private final static QName _ObterTodosAlunosResponse_QNAME = new QName("http://webservice.pacto.com.br/", "obterTodosAlunosResponse");
    private final static QName _ExcluirAlunoResponse_QNAME = new QName("http://webservice.pacto.com.br/", "excluirAlunoResponse");
    private final static QName _IncrementarVersaoCliente_QNAME = new QName("http://webservice.pacto.com.br/", "incrementarVersaoCliente");
    private final static QName _EstornarCreditos_QNAME = new QName("http://webservice.pacto.com.br/", "estornarCreditos");
    private final static QName _SincronizarParq_QNAME = new QName("http://webservice.pacto.com.br/", "sincronizarParq");
    private final static QName _ExcluirAlunoMatriculaResponse_QNAME = new QName("http://webservice.pacto.com.br/", "excluirAlunoMatriculaResponse");
    private final static QName _SincronizarCreditosAlunoResponse_QNAME = new QName("http://webservice.pacto.com.br/", "sincronizarCreditosAlunoResponse");
    private final static QName _ExcluirAluno_QNAME = new QName("http://webservice.pacto.com.br/", "excluirAluno");
    private final static QName _AtualizarCrossfit_QNAME = new QName("http://webservice.pacto.com.br/", "atualizarCrossfit");
    private final static QName _EstornarFechamentoCreditos_QNAME = new QName("http://webservice.pacto.com.br/", "estornarFechamentoCreditos");
    private final static QName _ExcluirAlunoMatricula_QNAME = new QName("http://webservice.pacto.com.br/", "excluirAlunoMatricula");
    private final static QName _EnviarNotificacoes_QNAME = new QName("http://webservice.pacto.com.br/", "enviarNotificacoes");
    private final static QName _AtualizarStatusAlunoResponse_QNAME = new QName("http://webservice.pacto.com.br/", "atualizarStatusAlunoResponse");
    private final static QName _ObterDataUltimaAtualizacao_QNAME = new QName("http://webservice.pacto.com.br/", "obterDataUltimaAtualizacao");
    private final static QName _SincronizarCreditosAluno_QNAME = new QName("http://webservice.pacto.com.br/", "sincronizarCreditosAluno");
    private final static QName _SincronizarUsuarioResponse_QNAME = new QName("http://webservice.pacto.com.br/", "sincronizarUsuarioResponse");
    private final static QName _RealizouTreinoNovo_QNAME = new QName("http://webservice.pacto.com.br/", "realizouTreinoNovo");
    private final static QName _IncrementarVersaoClienteResponse_QNAME = new QName("http://webservice.pacto.com.br/", "incrementarVersaoClienteResponse");
    private final static QName _SincronizarProfessorResponse_QNAME = new QName("http://webservice.pacto.com.br/", "sincronizarProfessorResponse");
    private final static QName _ObterConfiguracaoSistemaResponse_QNAME = new QName("http://webservice.pacto.com.br/", "obterConfiguracaoSistemaResponse");
    private final static QName _SincronizarUsuario_QNAME = new QName("http://webservice.pacto.com.br/", "sincronizarUsuario");
    private final static QName _AddAlunoPesquisaTW_QNAME = new QName("http://webservice.pacto.com.br/", "addAlunoPesquisaTW");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: servicos.integracao.treino
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link AddAlunoPesquisaTW }
     * 
     */
    public AddAlunoPesquisaTW createAddAlunoPesquisaTW() {
        return new AddAlunoPesquisaTW();
    }

    /**
     * Create an instance of {@link SincronizarUsuario }
     * 
     */
    public SincronizarUsuario createSincronizarUsuario() {
        return new SincronizarUsuario();
    }

    /**
     * Create an instance of {@link ObterConfiguracaoSistemaResponse }
     * 
     */
    public ObterConfiguracaoSistemaResponse createObterConfiguracaoSistemaResponse() {
        return new ObterConfiguracaoSistemaResponse();
    }

    /**
     * Create an instance of {@link SincronizarProfessorResponse }
     * 
     */
    public SincronizarProfessorResponse createSincronizarProfessorResponse() {
        return new SincronizarProfessorResponse();
    }

    /**
     * Create an instance of {@link IncrementarVersaoClienteResponse }
     * 
     */
    public IncrementarVersaoClienteResponse createIncrementarVersaoClienteResponse() {
        return new IncrementarVersaoClienteResponse();
    }

    /**
     * Create an instance of {@link RealizouTreinoNovo }
     * 
     */
    public RealizouTreinoNovo createRealizouTreinoNovo() {
        return new RealizouTreinoNovo();
    }

    /**
     * Create an instance of {@link SincronizarUsuarioResponse }
     * 
     */
    public SincronizarUsuarioResponse createSincronizarUsuarioResponse() {
        return new SincronizarUsuarioResponse();
    }

    /**
     * Create an instance of {@link SincronizarCreditosAluno }
     * 
     */
    public SincronizarCreditosAluno createSincronizarCreditosAluno() {
        return new SincronizarCreditosAluno();
    }

    /**
     * Create an instance of {@link ObterDataUltimaAtualizacao }
     * 
     */
    public ObterDataUltimaAtualizacao createObterDataUltimaAtualizacao() {
        return new ObterDataUltimaAtualizacao();
    }

    /**
     * Create an instance of {@link AtualizarStatusAlunoResponse }
     * 
     */
    public AtualizarStatusAlunoResponse createAtualizarStatusAlunoResponse() {
        return new AtualizarStatusAlunoResponse();
    }

    /**
     * Create an instance of {@link EnviarNotificacoes }
     * 
     */
    public EnviarNotificacoes createEnviarNotificacoes() {
        return new EnviarNotificacoes();
    }

    /**
     * Create an instance of {@link ExcluirAlunoMatricula }
     * 
     */
    public ExcluirAlunoMatricula createExcluirAlunoMatricula() {
        return new ExcluirAlunoMatricula();
    }

    /**
     * Create an instance of {@link EstornarFechamentoCreditos }
     * 
     */
    public EstornarFechamentoCreditos createEstornarFechamentoCreditos() {
        return new EstornarFechamentoCreditos();
    }

    /**
     * Create an instance of {@link AtualizarCrossfit }
     * 
     */
    public AtualizarCrossfit createAtualizarCrossfit() {
        return new AtualizarCrossfit();
    }

    /**
     * Create an instance of {@link SincronizarCreditosAlunoResponse }
     * 
     */
    public SincronizarCreditosAlunoResponse createSincronizarCreditosAlunoResponse() {
        return new SincronizarCreditosAlunoResponse();
    }

    /**
     * Create an instance of {@link ExcluirAluno }
     * 
     */
    public ExcluirAluno createExcluirAluno() {
        return new ExcluirAluno();
    }

    /**
     * Create an instance of {@link ExcluirAlunoMatriculaResponse }
     * 
     */
    public ExcluirAlunoMatriculaResponse createExcluirAlunoMatriculaResponse() {
        return new ExcluirAlunoMatriculaResponse();
    }

    /**
     * Create an instance of {@link SincronizarParq }
     * 
     */
    public SincronizarParq createSincronizarParq() {
        return new SincronizarParq();
    }

    /**
     * Create an instance of {@link EstornarCreditos }
     * 
     */
    public EstornarCreditos createEstornarCreditos() {
        return new EstornarCreditos();
    }

    /**
     * Create an instance of {@link IncrementarVersaoCliente }
     * 
     */
    public IncrementarVersaoCliente createIncrementarVersaoCliente() {
        return new IncrementarVersaoCliente();
    }

    /**
     * Create an instance of {@link ObterTodosAlunosResponse }
     * 
     */
    public ObterTodosAlunosResponse createObterTodosAlunosResponse() {
        return new ObterTodosAlunosResponse();
    }

    /**
     * Create an instance of {@link ExcluirAlunoResponse }
     * 
     */
    public ExcluirAlunoResponse createExcluirAlunoResponse() {
        return new ExcluirAlunoResponse();
    }

    /**
     * Create an instance of {@link AtualizarCrossfitResponse }
     * 
     */
    public AtualizarCrossfitResponse createAtualizarCrossfitResponse() {
        return new AtualizarCrossfitResponse();
    }

    /**
     * Create an instance of {@link AddAlunoPesquisaTWResponse }
     * 
     */
    public AddAlunoPesquisaTWResponse createAddAlunoPesquisaTWResponse() {
        return new AddAlunoPesquisaTWResponse();
    }

    /**
     * Create an instance of {@link InserirCreditos }
     * 
     */
    public InserirCreditos createInserirCreditos() {
        return new InserirCreditos();
    }

    /**
     * Create an instance of {@link AtualizarStatusAluno }
     * 
     */
    public AtualizarStatusAluno createAtualizarStatusAluno() {
        return new AtualizarStatusAluno();
    }

    /**
     * Create an instance of {@link SincronizarProfessor }
     * 
     */
    public SincronizarProfessor createSincronizarProfessor() {
        return new SincronizarProfessor();
    }

    /**
     * Create an instance of {@link RealizouTreinoNovoResponse }
     * 
     */
    public RealizouTreinoNovoResponse createRealizouTreinoNovoResponse() {
        return new RealizouTreinoNovoResponse();
    }

    /**
     * Create an instance of {@link ObterDataUltimaAtualizacaoResponse }
     * 
     */
    public ObterDataUltimaAtualizacaoResponse createObterDataUltimaAtualizacaoResponse() {
        return new ObterDataUltimaAtualizacaoResponse();
    }

    /**
     * Create an instance of {@link ObterTodosProgramasAlunos }
     * 
     */
    public ObterTodosProgramasAlunos createObterTodosProgramasAlunos() {
        return new ObterTodosProgramasAlunos();
    }

    /**
     * Create an instance of {@link SincronizarParqResponse }
     * 
     */
    public SincronizarParqResponse createSincronizarParqResponse() {
        return new SincronizarParqResponse();
    }

    /**
     * Create an instance of {@link ObterConfiguracaoSistema }
     * 
     */
    public ObterConfiguracaoSistema createObterConfiguracaoSistema() {
        return new ObterConfiguracaoSistema();
    }

    /**
     * Create an instance of {@link AtualizarFrequenciaSemanalResponse }
     * 
     */
    public AtualizarFrequenciaSemanalResponse createAtualizarFrequenciaSemanalResponse() {
        return new AtualizarFrequenciaSemanalResponse();
    }

    /**
     * Create an instance of {@link ObterTodosAlunos }
     * 
     */
    public ObterTodosAlunos createObterTodosAlunos() {
        return new ObterTodosAlunos();
    }

    /**
     * Create an instance of {@link AdicionarPersonalResponse }
     * 
     */
    public AdicionarPersonalResponse createAdicionarPersonalResponse() {
        return new AdicionarPersonalResponse();
    }

    /**
     * Create an instance of {@link ObterTodosProgramasAlunosResponse }
     * 
     */
    public ObterTodosProgramasAlunosResponse createObterTodosProgramasAlunosResponse() {
        return new ObterTodosProgramasAlunosResponse();
    }

    /**
     * Create an instance of {@link InserirCreditosResponse }
     * 
     */
    public InserirCreditosResponse createInserirCreditosResponse() {
        return new InserirCreditosResponse();
    }

    /**
     * Create an instance of {@link EstornarFechamentoCreditosResponse }
     * 
     */
    public EstornarFechamentoCreditosResponse createEstornarFechamentoCreditosResponse() {
        return new EstornarFechamentoCreditosResponse();
    }

    /**
     * Create an instance of {@link SincronizarRiscoCliente }
     * 
     */
    public SincronizarRiscoCliente createSincronizarRiscoCliente() {
        return new SincronizarRiscoCliente();
    }

    /**
     * Create an instance of {@link SincronizarRiscoClienteResponse }
     * 
     */
    public SincronizarRiscoClienteResponse createSincronizarRiscoClienteResponse() {
        return new SincronizarRiscoClienteResponse();
    }

    /**
     * Create an instance of {@link DeleteAlunoPesquisaTW }
     * 
     */
    public DeleteAlunoPesquisaTW createDeleteAlunoPesquisaTW() {
        return new DeleteAlunoPesquisaTW();
    }

    /**
     * Create an instance of {@link AdicionarPersonal }
     * 
     */
    public AdicionarPersonal createAdicionarPersonal() {
        return new AdicionarPersonal();
    }

    /**
     * Create an instance of {@link AtualizarFrequenciaSemanal }
     * 
     */
    public AtualizarFrequenciaSemanal createAtualizarFrequenciaSemanal() {
        return new AtualizarFrequenciaSemanal();
    }

    /**
     * Create an instance of {@link EnviarNotificacoesResponse }
     * 
     */
    public EnviarNotificacoesResponse createEnviarNotificacoesResponse() {
        return new EnviarNotificacoesResponse();
    }

    /**
     * Create an instance of {@link DeleteAlunoPesquisaTWResponse }
     * 
     */
    public DeleteAlunoPesquisaTWResponse createDeleteAlunoPesquisaTWResponse() {
        return new DeleteAlunoPesquisaTWResponse();
    }

    /**
     * Create an instance of {@link EstornarCreditosResponse }
     * 
     */
    public EstornarCreditosResponse createEstornarCreditosResponse() {
        return new EstornarCreditosResponse();
    }

    /**
     * Create an instance of {@link UsuarioZW }
     * 
     */
    public UsuarioZW createUsuarioZW() {
        return new UsuarioZW();
    }

    /**
     * Create an instance of {@link ProfessorSintetico }
     * 
     */
    public ProfessorSintetico createProfessorSintetico() {
        return new ProfessorSintetico();
    }

    /**
     * Create an instance of {@link Email }
     * 
     */
    public Email createEmail() {
        return new Email();
    }

    /**
     * Create an instance of {@link ClienteZW }
     * 
     */
    public ClienteZW createClienteZW() {
        return new ClienteZW();
    }

    /**
     * Create an instance of {@link Pessoa }
     * 
     */
    public Pessoa createPessoa() {
        return new Pessoa();
    }

    /**
     * Create an instance of {@link Telefone }
     * 
     */
    public Telefone createTelefone() {
        return new Telefone();
    }

    /**
     * Create an instance of {@link Empresa }
     * 
     */
    public Empresa createEmpresa() {
        return new Empresa();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EstornarCreditosResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "estornarCreditosResponse")
    public JAXBElement<EstornarCreditosResponse> createEstornarCreditosResponse(EstornarCreditosResponse value) {
        return new JAXBElement<EstornarCreditosResponse>(_EstornarCreditosResponse_QNAME, EstornarCreditosResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DeleteAlunoPesquisaTWResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "deleteAlunoPesquisaTWResponse")
    public JAXBElement<DeleteAlunoPesquisaTWResponse> createDeleteAlunoPesquisaTWResponse(DeleteAlunoPesquisaTWResponse value) {
        return new JAXBElement<DeleteAlunoPesquisaTWResponse>(_DeleteAlunoPesquisaTWResponse_QNAME, DeleteAlunoPesquisaTWResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnviarNotificacoesResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "enviarNotificacoesResponse")
    public JAXBElement<EnviarNotificacoesResponse> createEnviarNotificacoesResponse(EnviarNotificacoesResponse value) {
        return new JAXBElement<EnviarNotificacoesResponse>(_EnviarNotificacoesResponse_QNAME, EnviarNotificacoesResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarFrequenciaSemanal }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "atualizarFrequenciaSemanal")
    public JAXBElement<AtualizarFrequenciaSemanal> createAtualizarFrequenciaSemanal(AtualizarFrequenciaSemanal value) {
        return new JAXBElement<AtualizarFrequenciaSemanal>(_AtualizarFrequenciaSemanal_QNAME, AtualizarFrequenciaSemanal.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AdicionarPersonal }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "adicionarPersonal")
    public JAXBElement<AdicionarPersonal> createAdicionarPersonal(AdicionarPersonal value) {
        return new JAXBElement<AdicionarPersonal>(_AdicionarPersonal_QNAME, AdicionarPersonal.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DeleteAlunoPesquisaTW }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "deleteAlunoPesquisaTW")
    public JAXBElement<DeleteAlunoPesquisaTW> createDeleteAlunoPesquisaTW(DeleteAlunoPesquisaTW value) {
        return new JAXBElement<DeleteAlunoPesquisaTW>(_DeleteAlunoPesquisaTW_QNAME, DeleteAlunoPesquisaTW.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SincronizarRiscoClienteResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "sincronizarRiscoClienteResponse")
    public JAXBElement<SincronizarRiscoClienteResponse> createSincronizarRiscoClienteResponse(SincronizarRiscoClienteResponse value) {
        return new JAXBElement<SincronizarRiscoClienteResponse>(_SincronizarRiscoClienteResponse_QNAME, SincronizarRiscoClienteResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SincronizarRiscoCliente }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "sincronizarRiscoCliente")
    public JAXBElement<SincronizarRiscoCliente> createSincronizarRiscoCliente(SincronizarRiscoCliente value) {
        return new JAXBElement<SincronizarRiscoCliente>(_SincronizarRiscoCliente_QNAME, SincronizarRiscoCliente.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EstornarFechamentoCreditosResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "estornarFechamentoCreditosResponse")
    public JAXBElement<EstornarFechamentoCreditosResponse> createEstornarFechamentoCreditosResponse(EstornarFechamentoCreditosResponse value) {
        return new JAXBElement<EstornarFechamentoCreditosResponse>(_EstornarFechamentoCreditosResponse_QNAME, EstornarFechamentoCreditosResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link InserirCreditosResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "inserirCreditosResponse")
    public JAXBElement<InserirCreditosResponse> createInserirCreditosResponse(InserirCreditosResponse value) {
        return new JAXBElement<InserirCreditosResponse>(_InserirCreditosResponse_QNAME, InserirCreditosResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterTodosProgramasAlunosResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "obterTodosProgramasAlunosResponse")
    public JAXBElement<ObterTodosProgramasAlunosResponse> createObterTodosProgramasAlunosResponse(ObterTodosProgramasAlunosResponse value) {
        return new JAXBElement<ObterTodosProgramasAlunosResponse>(_ObterTodosProgramasAlunosResponse_QNAME, ObterTodosProgramasAlunosResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AdicionarPersonalResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "adicionarPersonalResponse")
    public JAXBElement<AdicionarPersonalResponse> createAdicionarPersonalResponse(AdicionarPersonalResponse value) {
        return new JAXBElement<AdicionarPersonalResponse>(_AdicionarPersonalResponse_QNAME, AdicionarPersonalResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterTodosAlunos }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "obterTodosAlunos")
    public JAXBElement<ObterTodosAlunos> createObterTodosAlunos(ObterTodosAlunos value) {
        return new JAXBElement<ObterTodosAlunos>(_ObterTodosAlunos_QNAME, ObterTodosAlunos.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarFrequenciaSemanalResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "atualizarFrequenciaSemanalResponse")
    public JAXBElement<AtualizarFrequenciaSemanalResponse> createAtualizarFrequenciaSemanalResponse(AtualizarFrequenciaSemanalResponse value) {
        return new JAXBElement<AtualizarFrequenciaSemanalResponse>(_AtualizarFrequenciaSemanalResponse_QNAME, AtualizarFrequenciaSemanalResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterConfiguracaoSistema }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "obterConfiguracaoSistema")
    public JAXBElement<ObterConfiguracaoSistema> createObterConfiguracaoSistema(ObterConfiguracaoSistema value) {
        return new JAXBElement<ObterConfiguracaoSistema>(_ObterConfiguracaoSistema_QNAME, ObterConfiguracaoSistema.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SincronizarParqResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "sincronizarParqResponse")
    public JAXBElement<SincronizarParqResponse> createSincronizarParqResponse(SincronizarParqResponse value) {
        return new JAXBElement<SincronizarParqResponse>(_SincronizarParqResponse_QNAME, SincronizarParqResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterTodosProgramasAlunos }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "obterTodosProgramasAlunos")
    public JAXBElement<ObterTodosProgramasAlunos> createObterTodosProgramasAlunos(ObterTodosProgramasAlunos value) {
        return new JAXBElement<ObterTodosProgramasAlunos>(_ObterTodosProgramasAlunos_QNAME, ObterTodosProgramasAlunos.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterDataUltimaAtualizacaoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "obterDataUltimaAtualizacaoResponse")
    public JAXBElement<ObterDataUltimaAtualizacaoResponse> createObterDataUltimaAtualizacaoResponse(ObterDataUltimaAtualizacaoResponse value) {
        return new JAXBElement<ObterDataUltimaAtualizacaoResponse>(_ObterDataUltimaAtualizacaoResponse_QNAME, ObterDataUltimaAtualizacaoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RealizouTreinoNovoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "realizouTreinoNovoResponse")
    public JAXBElement<RealizouTreinoNovoResponse> createRealizouTreinoNovoResponse(RealizouTreinoNovoResponse value) {
        return new JAXBElement<RealizouTreinoNovoResponse>(_RealizouTreinoNovoResponse_QNAME, RealizouTreinoNovoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SincronizarProfessor }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "sincronizarProfessor")
    public JAXBElement<SincronizarProfessor> createSincronizarProfessor(SincronizarProfessor value) {
        return new JAXBElement<SincronizarProfessor>(_SincronizarProfessor_QNAME, SincronizarProfessor.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarStatusAluno }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "atualizarStatusAluno")
    public JAXBElement<AtualizarStatusAluno> createAtualizarStatusAluno(AtualizarStatusAluno value) {
        return new JAXBElement<AtualizarStatusAluno>(_AtualizarStatusAluno_QNAME, AtualizarStatusAluno.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link InserirCreditos }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "inserirCreditos")
    public JAXBElement<InserirCreditos> createInserirCreditos(InserirCreditos value) {
        return new JAXBElement<InserirCreditos>(_InserirCreditos_QNAME, InserirCreditos.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AddAlunoPesquisaTWResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "addAlunoPesquisaTWResponse")
    public JAXBElement<AddAlunoPesquisaTWResponse> createAddAlunoPesquisaTWResponse(AddAlunoPesquisaTWResponse value) {
        return new JAXBElement<AddAlunoPesquisaTWResponse>(_AddAlunoPesquisaTWResponse_QNAME, AddAlunoPesquisaTWResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarCrossfitResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "atualizarCrossfitResponse")
    public JAXBElement<AtualizarCrossfitResponse> createAtualizarCrossfitResponse(AtualizarCrossfitResponse value) {
        return new JAXBElement<AtualizarCrossfitResponse>(_AtualizarCrossfitResponse_QNAME, AtualizarCrossfitResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterTodosAlunosResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "obterTodosAlunosResponse")
    public JAXBElement<ObterTodosAlunosResponse> createObterTodosAlunosResponse(ObterTodosAlunosResponse value) {
        return new JAXBElement<ObterTodosAlunosResponse>(_ObterTodosAlunosResponse_QNAME, ObterTodosAlunosResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ExcluirAlunoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "excluirAlunoResponse")
    public JAXBElement<ExcluirAlunoResponse> createExcluirAlunoResponse(ExcluirAlunoResponse value) {
        return new JAXBElement<ExcluirAlunoResponse>(_ExcluirAlunoResponse_QNAME, ExcluirAlunoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link IncrementarVersaoCliente }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "incrementarVersaoCliente")
    public JAXBElement<IncrementarVersaoCliente> createIncrementarVersaoCliente(IncrementarVersaoCliente value) {
        return new JAXBElement<IncrementarVersaoCliente>(_IncrementarVersaoCliente_QNAME, IncrementarVersaoCliente.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EstornarCreditos }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "estornarCreditos")
    public JAXBElement<EstornarCreditos> createEstornarCreditos(EstornarCreditos value) {
        return new JAXBElement<EstornarCreditos>(_EstornarCreditos_QNAME, EstornarCreditos.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SincronizarParq }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "sincronizarParq")
    public JAXBElement<SincronizarParq> createSincronizarParq(SincronizarParq value) {
        return new JAXBElement<SincronizarParq>(_SincronizarParq_QNAME, SincronizarParq.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ExcluirAlunoMatriculaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "excluirAlunoMatriculaResponse")
    public JAXBElement<ExcluirAlunoMatriculaResponse> createExcluirAlunoMatriculaResponse(ExcluirAlunoMatriculaResponse value) {
        return new JAXBElement<ExcluirAlunoMatriculaResponse>(_ExcluirAlunoMatriculaResponse_QNAME, ExcluirAlunoMatriculaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SincronizarCreditosAlunoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "sincronizarCreditosAlunoResponse")
    public JAXBElement<SincronizarCreditosAlunoResponse> createSincronizarCreditosAlunoResponse(SincronizarCreditosAlunoResponse value) {
        return new JAXBElement<SincronizarCreditosAlunoResponse>(_SincronizarCreditosAlunoResponse_QNAME, SincronizarCreditosAlunoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ExcluirAluno }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "excluirAluno")
    public JAXBElement<ExcluirAluno> createExcluirAluno(ExcluirAluno value) {
        return new JAXBElement<ExcluirAluno>(_ExcluirAluno_QNAME, ExcluirAluno.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarCrossfit }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "atualizarCrossfit")
    public JAXBElement<AtualizarCrossfit> createAtualizarCrossfit(AtualizarCrossfit value) {
        return new JAXBElement<AtualizarCrossfit>(_AtualizarCrossfit_QNAME, AtualizarCrossfit.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EstornarFechamentoCreditos }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "estornarFechamentoCreditos")
    public JAXBElement<EstornarFechamentoCreditos> createEstornarFechamentoCreditos(EstornarFechamentoCreditos value) {
        return new JAXBElement<EstornarFechamentoCreditos>(_EstornarFechamentoCreditos_QNAME, EstornarFechamentoCreditos.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ExcluirAlunoMatricula }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "excluirAlunoMatricula")
    public JAXBElement<ExcluirAlunoMatricula> createExcluirAlunoMatricula(ExcluirAlunoMatricula value) {
        return new JAXBElement<ExcluirAlunoMatricula>(_ExcluirAlunoMatricula_QNAME, ExcluirAlunoMatricula.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link EnviarNotificacoes }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "enviarNotificacoes")
    public JAXBElement<EnviarNotificacoes> createEnviarNotificacoes(EnviarNotificacoes value) {
        return new JAXBElement<EnviarNotificacoes>(_EnviarNotificacoes_QNAME, EnviarNotificacoes.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AtualizarStatusAlunoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "atualizarStatusAlunoResponse")
    public JAXBElement<AtualizarStatusAlunoResponse> createAtualizarStatusAlunoResponse(AtualizarStatusAlunoResponse value) {
        return new JAXBElement<AtualizarStatusAlunoResponse>(_AtualizarStatusAlunoResponse_QNAME, AtualizarStatusAlunoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterDataUltimaAtualizacao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "obterDataUltimaAtualizacao")
    public JAXBElement<ObterDataUltimaAtualizacao> createObterDataUltimaAtualizacao(ObterDataUltimaAtualizacao value) {
        return new JAXBElement<ObterDataUltimaAtualizacao>(_ObterDataUltimaAtualizacao_QNAME, ObterDataUltimaAtualizacao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SincronizarCreditosAluno }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "sincronizarCreditosAluno")
    public JAXBElement<SincronizarCreditosAluno> createSincronizarCreditosAluno(SincronizarCreditosAluno value) {
        return new JAXBElement<SincronizarCreditosAluno>(_SincronizarCreditosAluno_QNAME, SincronizarCreditosAluno.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SincronizarUsuarioResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "sincronizarUsuarioResponse")
    public JAXBElement<SincronizarUsuarioResponse> createSincronizarUsuarioResponse(SincronizarUsuarioResponse value) {
        return new JAXBElement<SincronizarUsuarioResponse>(_SincronizarUsuarioResponse_QNAME, SincronizarUsuarioResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RealizouTreinoNovo }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "realizouTreinoNovo")
    public JAXBElement<RealizouTreinoNovo> createRealizouTreinoNovo(RealizouTreinoNovo value) {
        return new JAXBElement<RealizouTreinoNovo>(_RealizouTreinoNovo_QNAME, RealizouTreinoNovo.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link IncrementarVersaoClienteResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "incrementarVersaoClienteResponse")
    public JAXBElement<IncrementarVersaoClienteResponse> createIncrementarVersaoClienteResponse(IncrementarVersaoClienteResponse value) {
        return new JAXBElement<IncrementarVersaoClienteResponse>(_IncrementarVersaoClienteResponse_QNAME, IncrementarVersaoClienteResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SincronizarProfessorResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "sincronizarProfessorResponse")
    public JAXBElement<SincronizarProfessorResponse> createSincronizarProfessorResponse(SincronizarProfessorResponse value) {
        return new JAXBElement<SincronizarProfessorResponse>(_SincronizarProfessorResponse_QNAME, SincronizarProfessorResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterConfiguracaoSistemaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "obterConfiguracaoSistemaResponse")
    public JAXBElement<ObterConfiguracaoSistemaResponse> createObterConfiguracaoSistemaResponse(ObterConfiguracaoSistemaResponse value) {
        return new JAXBElement<ObterConfiguracaoSistemaResponse>(_ObterConfiguracaoSistemaResponse_QNAME, ObterConfiguracaoSistemaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SincronizarUsuario }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "sincronizarUsuario")
    public JAXBElement<SincronizarUsuario> createSincronizarUsuario(SincronizarUsuario value) {
        return new JAXBElement<SincronizarUsuario>(_SincronizarUsuario_QNAME, SincronizarUsuario.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AddAlunoPesquisaTW }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.pacto.com.br/", name = "addAlunoPesquisaTW")
    public JAXBElement<AddAlunoPesquisaTW> createAddAlunoPesquisaTW(AddAlunoPesquisaTW value) {
        return new JAXBElement<AddAlunoPesquisaTW>(_AddAlunoPesquisaTW_QNAME, AddAlunoPesquisaTW.class, null, value);
    }

}
