package servicos.integracao.treino.dto;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class SinteticoMsDTO {
    private String chave;
    private boolean processado;
    private String urlRequisitar;
    private List<JSONObject> usuariosAtualizar;

    public JSONObject toJSON(){
        JSONObject json = new JSONObject();
        json.put("chave", chave);
        json.put("processado", processado);
        json.put("urlRequisitar", urlRequisitar);
        JSONArray array = new JSONArray();
        if(usuariosAtualizar != null) {
            for (JSONObject obj : usuariosAtualizar) {
                array.put(obj);
            }
        }
        json.put("usuariosAtualizar", array);
        return json;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public boolean isProcessado() {
        return processado;
    }

    public void setProcessado(boolean processado) {
        this.processado = processado;
    }

    public List<JSONObject> getUsuariosAtualizar() {
        if(usuariosAtualizar == null){
            usuariosAtualizar = new ArrayList<>();
        }
        return usuariosAtualizar;
    }

    public void setUsuariosAtualizar(List<JSONObject> usuariosAtualizar) {
        this.usuariosAtualizar = usuariosAtualizar;
    }

    public String getUrlRequisitar() {
        return urlRequisitar;
    }

    public void setUrlRequisitar(String urlRequisitar) {
        this.urlRequisitar = urlRequisitar;
    }
}
