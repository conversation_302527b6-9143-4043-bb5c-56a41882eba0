/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.propriedades;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import negocio.comuns.utilitarias.AwsConsoleApp;
import negocio.comuns.utilitarias.Uteis;
import servicos.legolas.LegolasService;

import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.util.List;
import java.util.Properties;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public final class PropsService {

    public static final String VERSAO_SISTEMA = "VERSAO_SISTEMA";
    //Identificadores em arquivo .properties. Vide arquivo SuperControle.properties
    public static final String urlWikiRaiz = "urlWikiRaiz";
    public static final String urlWiki = "urlWiki";
    public static final String urlWikiCRM = "urlWikiCRM";
    public static final String urlWikiCE = "urlWikiCE";
    public static final String urlWikiFIN = "urlWikiFIN";
    public static final String urlBaseConhecimento = "urlBaseConhecimento";
    public static final String urlWikiCON = "urlWikiCON";
    public static final String urlWikiSO = "urlWikiSO";
    public static final String urlWikiES = "urlWikiES";
    public static final String urlWikiGST = "urlWikiGST";
    public static final String urlWikiVersoes = "urlWikiVersoes";
    public static final String urlWikiVersaoAtual = "urlWikiVersaoAtual";
    public static final String diretorioArquivos = "diretorioArquivos";
    public static final String pathLogsZAcesso = "pathLogsZAcesso";
    public static final String pathDataSourceZAcesso = "pathDataSourceZAcesso";
    public static final String urlDataSourceZAcesso = "urlDataSourceZAcesso";
    public static final String pathUtilsZAcesso = "pathUtilsZAcesso";
    public static final String urlUtilsZAcesso = "urlUtilsZAcesso";
    public static final String usuarioHTTPAcesso = "usuarioHTTPAcesso";
    public static final String senhaHTTPAcesso = "senhaHTTPAcesso";
    public static final String urlNotificacaoAcesso = "urlNotificacaoAcesso";
    public static final String instanciasNotificar = "instanciasNotificar";
    public static final String pathImagensEmail = "pathImagensEmail";
    public static final String urlNFSe = "urlNFSe";
    public static final String urlNFSeRestAdmin = "urlNFSeRestAdmin";
    public static final String urlNFSeRest = "urlNFSeRest";
    public static final String urlAplicacao= "urlAplicacao";
    public static final String urlServicoNotaFiscal= "urlServicoNotaFiscal";
    public static final String urlAplicacaoCentral = "urlAplicacaoCentral";
    public static final String urlSolicitacao = "urlSolicitacao";
    public static final String urlFinanceiroPacto = "urlFinanceiroPacto";
    public static final String urlBoletosFinanceiroPacto = "urlBoletosFinanceiroPacto";
    public static final String hostSFTP = "hostSFTP";
    public static final String portSFTP = "portSFTP";
    public static final String userSFTP = "userSFTP";
    public static final String pwdSFTP = "pwdSFTP";
    public static final String diretorioLocalAnt = "diretorioLocalAnt";
    public static final String diretorioLocalTIVIT = "diretorioLocalTIVIT";
    public static final String diretorioLocalDownloadTIVIT = "diretorioLocalDownloadTIVIT";
    public static final String diretorioLocalUploadTIVIT = "diretorioLocalUploadTIVIT";
    public static final String diretorioRemotoTIVIT_OUT = "diretorioRemotoTIVIT_OUT";
    public static final String diretorioRemotoTIVIT_IN = "diretorioRemotoTIVIT_IN";
    public static final String emailsNotificarValidacaoBI = "emailsNotificarValidacaoBI";
    public static final String emailSquadAdmAbrirTicket = "emailSquadAdmAbrirTicket";
    public static final String urlObterBanners = "urlObterBanners";
    public static final String urlStudioCalendar = "urlStudioCalendar";
    public static final String googleClientID = "googleClientID";
    public static final String robocontrole = "robocontrole";
    public static final String urlTreino = "urlTreino";//usado para Integração entre ZW e Pacto Treino
    public static final String urlGame = "urlGame";
    public static final String urlSiteApp = "urlSiteApp";
    public static final String urlGameGeral = "urlGameGeral";
    public static final String urlZWAuto = "urlZWAUTO";
    public static final String urlZWAPI = "urlAPI";
    public static final String urlTreinoWeb = "urlTreinoWeb";//usado para redirecionar para Web interface do Pacto Treino
    public static final String treinoFront = "treinofront";

    public static final String urlcrmFront = "urlcrmFront";
    public static final String zwFront = "zwfront";
    public static final String roboControle = "robocontrole";
    public static final String uriInicial = "uriInicial";
    public static final String uriCadastros = "uriCadastros";
    public static final String uriClientes = "uriClientes";
    public static final String uriCliente = "uriCliente";
    public static final String uriColaborador = "uriColaborador";
    public static final String uriUsuario = "uriUsuario";
    public static final String uriPerfil = "uriPerfil";
    public static final String uriPessoa = "uriPessoa";
    public static final String uriBI = "uriBI";
    public static final String uriBIMobile = "uriBIDetalhado";
    public static final String uriAgenda = "uriAgenda";
    public static final String uriVendaAvulsa = "uriVendaAvulsa";
    public static final String uriTurma = "uriTurma";
    public static final String uriCarteiras = "uriCarteiras";
    public static final String uriHistoricoBV = "uriHistoricoBV";
    public static final String uriFinan = "uriFinan";
    public static final String uriCRM = "uriCRM";
    public static final String uriNota = "uriNota";
    public static final String uriBICRM = "uriBICRM";
    public static final String uriBIFinan = "uriBIFinan";
    public static final String uriPlano = "uriPlano";
    public static final String uriConfiguracao = "uriConfiguracao";
    public static final String uriPrecadastro = "uriPrecadastro";
    public static final String uriCanal = "uriCanal";
    public static final String uriCompra = "uriCompra";
    public static final String uriStore = "uriStore";
    public static final String enableZawQueue = "enableZawQueue";
    public static final String cookieFailover = "cookieFailover";
    public static final String urlTreinoGooglePlay = "urlTreinoGooglePlay";
    public static final String urlTreinoAppleStore = "urlTreinoAppleStore";
    public static final String urlOamdEmpresas = "urlOamdEmpresas";

    public static final String urlMidiaSocial = "urlMidiaSocial";

    public static final String urlApiApp = "urlApiApp";

    public static final String urlIntegradorOamd = "urlIntegradorOamd";
    public static final String empresasPermitidasAlterarDataBase = "empresasPermitidasAlterarDataBase";
    public static final String dataLimiteEmpresaAlterarDataBase = "dataLimiteEmpresaAlterarDataBase";
    public static final String urlLogin = "urlLogin";
    public static final String enviarRemessaSFTPNow = "enviarRemessaSFTPNow";
    public static final String fotosParaNuvem = "fotosParaNuvem";
    public static final String urlFotosNuvem = "urlFotosNuvem";
    public static final String typeMidiasService = "typeMidiasService";
    public static final String diretorioFotos = "diretorioFotos";
    public static final String urlNFENuvem = "urlNFENuvem";
//    public static final String validarIps = "validarIps";
    public static final String redirectLoginServidorLocal = "redirectLoginServidorLocal";
    public static final String myFaqUrlBase = "myFaqUrlBase";
    public static final String myFaqEmpresas = "myFaqEmpresas";
    public static final String myUpUrlBase = "myUpUrlBase";
    public static final String validarUsuarioOAMD = "validarUsuarioOAMD";
    public static final String urlOamd = "urlOamd";
    public static final String urlOamdSegura = "urlOamdSegura";
    public static final String urlModuloNFSe = "urlModuloNFSe";
    public static final String urlSuporte = "urlSuporte";
    public static final String loadInstancesFromCloud = "loadInstancesFromCloud";
    public static final String prefixoIntanciasCloud = "prefixoIntanciasCloud";
    public static final String AWSAccessKeyId = "AWSAccessKeyId";
    public static final String AWSSecretKey = "AWSSecretKey";
    public static final String AWSRegion = "AWSRegion";
    public static final String integracaoWiki = "integracaoWiki";
    public static final String liberarAcessoFatorZW = "liberarAcessoFatorZW";
    public static final String urlMedidor = "urlMedidor";
    public static final String buscarConhecimentoUCP = "buscarConhecimentoUCP";
    public static final String atualizacaoCadastral = "atualizacaoCadastral";
    public static final String habilitarLembreteSolicitacoes = "habilitarLembreteSolicitacoes";
    public static final String validarVersaoBD = "validarVersaoBD";
    public static final String validarBloqueioOutros = "validarBloqueioOutros";
    public static final String emailComercialPacto = "emailComercialPacto";
    public static final String VALIDAR_TOKEN_API_ZW = "validarTokenApiZW";

    public static final String smtpEmailRobo = "smtpEmailRobo";
    public static final String smtpLoginRobo = "smtpLoginRobo";
    public static final String smtpSenhaRobo = "smtpSenhaRobo";
    public static final String smtpConexaoSeguraRobo = "smtpConexaoSeguraRobo";
    public static final String smtpServerRobo = "smtpServerRobo";
    public static final String iniciarTLS = "iniciarTLS";
    public static final String smtpEmailNoReply = "smtpEmailNoReply";
    public static final String arrayCaixasPostaisSFTP = "arrayCaixasPostaisSFTP";
    public static final String arrayCaixasPostaisExtratoSFTP = "arrayCaixasPostaisExtratoSFTP";
    public static final String serverSFTPSlave = "serverSFTPSlave";
    public static final String urlDashBoard = "urlDashBoard";
    public static final String authenticationKeyCappta = "authenticationKeyCappta";
    public static final String urlApiChatGoogleCappta = "urlApiChatGoogleCappta";
    public static final String cpftestevendasonline = "cpftestevendasonline";

    public static final String urlApiVindiProducao = "urlApiVindiProducao";
    public static final String urlApiVindiSandbox = "urlApiVindiSandbox";
    public static final String urlApiCieloRequisicaoProducao = "urlApiCieloRequisicaoProducao";
    public static final String urlApiCieloConsultaProducao = "urlApiCieloConsultaProducao";
    public static final String urlApiCieloRequisicaoSandbox = "urlApiCieloRequisicaoSandbox";
    public static final String urlApiCieloConsultaSandbox = "urlApiCieloConsultaSandbox";
    public static final String urlApiRedeProducao = "urlApiRedeProducao";
    public static final String urlApiRedeSandbox = "urlApiRedeSandbox";
    public static final String urlApiRedeConciliacaoProducao = "urlApiRedeConciliacaoProducao";
    public static final String clientIdRede = "clientIdRede";
    public static final String clientSecretRede = "clientSecretRede";
    public static final String userNameRede = "userNameRede";
    public static final String passwordRede = "passwordRede";
    public static final String urlApiGetnetProducao = "urlApiGetnetProducao";
    public static final String getNetECommerceOrgIdProducao = "getNetECommerceOrgIdProducao";
    public static final String urlApiGetnetSandbox = "urlApiGetnetSandbox";
    public static final String getNetECommerceOrgIdSandbox = "getNetECommerceOrgIdSandbox";
    public static final String utilizarIpServidorGetnet = "utilizarIpServidorGetnet";
    public static final String usarMockStone = "usarMockStone";
    public static final String urlApiStoneProducao = "urlApiStoneProducao";
    public static final String urlApiStoneSandbox = "urlApiStoneSandbox";
    public static final String urlApiStoneOnlineConciliationV1 = "urlApiStoneOnlineConciliationV1";
    public static final String urlApiStoneOnlineConciliationV2 = "urlApiStoneOnlineConciliationV2";
    public static final String urlApiStoneConnect = "urlApiStoneConnect";
    public static final String urlApiMundipagg = "urlApiMundipagg";
    public static final String urlApiPagarme = "urlApiPagarme";
    public static final String serviceRefererNamePagarMePacto = "serviceRefererNamePagarMePacto";
    public static final String urlApiStone_V5 = "urlApiStone_V5";
    public static final String urlApiStripe= "urlApiStripe";

    //PAGBANK ONLINE
    public static final String urlApiPagBankProducao= "urlApiPagBankProducao";
    public static final String urlApiPagBankSandbox= "urlApiPagBankSandbox";
    public static final String urlApiConnectPagBankProducao= "urlApiConnectPagBankProducao";
    public static final String urlApiConnectPagBankSandbox= "urlApiConnectPagBankSandbox";
    public static final String clientIdAplicacaoPactoPagbankProducao= "clientIdAplicacaoPactoPagbankProducao";
    public static final String clientIdAplicacaoPactoPagbankSandbox= "clientIdAplicacaoPactoPagbankSandbox";
    public static final String clientSecretAplicacaoPactoPagbankProducao= "clientSecretAplicacaoPactoPagbankProducao";
    public static final String clientSecretAplicacaoPactoPagbankSandbox= "clientSecretAplicacaoPactoPagbankSandbox";
    public static final String scopeUtilizarAutorizarConnectPagbank= "scopeUtilizarAutorizarConnectPagbank";
    public static final String redirectUriConnectPagBank= "redirectUriConnectPagBank";
    public static final String tokenContaPactoPagBankSandbox= "tokenContaPactoPagBankSandbox";
    public static final String tokenContaPactoPagBankProducao= "tokenContaPactoPagBankProducao";

    //DCC PagoLivre Online
    public static final String urlApiPagoLivreProducao = "urlApiPagoLivreProducao";
    public static final String urlApiPagoLivreSandbox = "urlApiPagoLivreSandbox";
    public static final String urlApiGatewayPagoLivreProducao = "urlApiGatewayPagoLivreProducao";
    public static final String urlApiGatewayPagoLivreSandbox = "urlApiGatewayPagoLivreSandbox";
    public static final String urlApiPagoLivreConciliacaoProducao = "urlApiPagoLivreConciliacaoProducao";
    public static final String tokenPagoLivreProducao = "tokenPagoLivreProducao";
    public static final String tokenPagoLivreSandbox = "tokenPagoLivreSandbox";
    public static final String tokenGatewayPagoLivreProducao = "tokenGatewayPagoLivreProducao";
    public static final String tokenGatewayPagoLivreSandbox = "tokenGatewayPagoLivreSandbox";
    public static final String chaveCriptSenhaUserPagoLivre = "chaveCriptSenhaUserPagoLivre";

    //DCC PagoLivre - FacilitePay Online
    public static final String tokenPagoLivreFacilitePayProducao = "tokenPagoLivreFacilitePayProducao";
    public static final String tokenPagoLivreFacilitePaySandbox = "tokenPagoLivreFacilitePaySandbox";

    //DCC PinBank Online
    public static final String urlApiPinBankProducao = "urlApiPinBankProducao";
    public static final String urlApiPinBankSandbox = "urlApiPinBankSandbox";

    //DCC One Payment
    public static final String urlApiOnePayment = "urlApiOnePayment";
    public static final String urlApiOnePaymentQuery = "urlApiOnePaymentQuery";

    //DCC Ceopag
    public static final String urlApiCeopagProducaoPortal = "urlApiCeopagProducaoPortal";
    public static final String urlApiCeopagProducaoGateway = "urlApiCeopagProducaoGateway";
    public static final String urlApiCeopagProducaoReconciliation = "urlApiCeopagProducaoReconciliation";
    public static final String urlApiCeopagProducaoWebhook = "urlApiCeopagProducaoWebhook";
    public static final String urlApiCeopagProducaoAuth = "urlApiCeopagProducaoAuth";
    public static final String urlApiCeopagSandboxPortal = "urlApiCeopagSandboxPortal";
    public static final String urlApiCeopagSandboxGateway = "urlApiCeopagSandboxGateway";
    public static final String urlApiCeopagSandboxReconciliation = "urlApiCeopagSandboxReconciliation";
    public static final String urlApiCeopagSandboxWebhook = "urlApiCeopagSandboxWebhook";
    public static final String urlApiCeopagSandboxAuth = "urlApiCeopagSandboxAuth";

    //DCC Caixa Pagamentos Transaes Online
    public static final String urlApiCaixaPagamentosRequisicaoProducao = "urlApiCaixaPagamentosRequisicaoProducao";
    public static final String urlApiCaixaPagamentosRequisicaoSandbox = "urlApiCaixaPagamentosRequisicaoSandbox";

    public static final String chaveDesencriptItauOnline = "chaveDesencriptItauOnline";
    public static final String urlApiItauToken = "urlApiItauToken";
    public static final String urlApiItauTokenSandBox = "urlApiItauTokenSandBox";
    public static final String urlApiItauRegistro = "urlApiItauRegistro";
    public static final String urlApiItauWebhook = "urlApiItauWebhook";
    public static final String urlApiItauRegistroSandBox = "urlApiItauRegistroSandBox";
    public static final String urlApiCaixaManutencao = "urlApiCaixaManutencao";
    public static final String urlApiCaixaConsulta = "urlApiCaixaConsulta";
    public static final String urlApiBoletoBancoBrasilSandbox = "urlApiBoletoBancoBrasilSandbox";
    public static final String urlApiBoletoBancoBrasilProducao = "urlApiBoletoBancoBrasilProducao";
    public static final String nomeProdutoPadraoVindi = "nomeProdutoPadraoVindi";
    public static final String useBounceService = "useBounceService";
    public static final String apresentarHotjar = "apresentarHotjar";
    public static final String utilizarSinteticoMs = "utilizarSinteticoMs";
    public static final String urlRecursoEmpresa = "urlRecursoEmpresa";
    public static final String usarUrlRecursoEmpresa = "usarUrlRecursoEmpresa";
    public static final String enviarEmailVendasOnline = "enviarEmailVendasOnline";
    public static final String urlVendasOnline = "urlVendasOnline";
    public static final String urlBaseOptIn = "urlBaseOptIn";
    public static final String urlDocumentacaoApi = "urlDocumentacaoApi";
    public static final String urlAPIGymPass = "urlAPIGymPass";
    public static final String urlGymPassMs = "urlGymPassMs";
    public static final String urlGoGood = "urlGoGood";
    public static final String goGoodAppId = "goGoodAppId";
    public static final String goGoodAppSecret = "goGoodAppSecret";
    public static final String urlIntegracaoProtheus = "urlIntegracaoProtheus";
    public static final String urlMockZWServer = "urlMockZWServer";
    public static final String urlAPIGympass = "urlAPIGympass";
    public static final String urlAPIDotz = "urlAPIDotz";
    public static final String urlAPIDotzToken = "urlAPIDotzToken";
    public static final String clientIdAPIDotz = "clientIdAPIDotz";
    public static final String clientSecretAPIDotz = "clientSecretAPIDotz";
    public static final String clientApplicationKeyConciliation = "clientApplicationKeyConciliation";
    public static final String secretKeyConciliation = "secretKeyConciliation";
    public static final String xAuthorizationRawData = "xAuthorizationRawData";
    public static final String urlAPIGeoitdDev = "urlAPIGeoitdDev";
    public static final String urlAragorn = "urlAragorn";
    public static final String urlPactoBank = "urlPactoBank";
    public static final String stoneOpenBankProducao = "stoneOpenBankProducao";
    public static final String tempoAguardarRemessa = "tempoAguardarRemessa";
    public static final String horarioEnvioRemessaGetnet = "horarioEnvioRemessaGetnet";


    //servico de validacao de inadimplencia, inicialmente desenvolvido para a just fit
    public static final String integraProtheus = "integraProtheus";
    public static final String validarInadimplencia = "validarInadimplencia";
    public static final String urlValidarInadimplencia = "urlValidarInadimplencia";
    public static final String tokenValidarInadimplencia = "tokenValidarInadimplencia";

    public static final String UTILIZAR_MOVI_DESK = "UTILIZAR_MOVI_DESK";
    public static final String UTILIZAR_CHAT_MOVI_DESK = "UTILIZAR_CHAT_MOVI_DESK";
    public static final String GRUPO_CHAT_MOVI_DESK = "GRUPO_CHAT_MOVIDESK";

    public static final String UTILIZAR_OCTADESK = "UTILIZAR_OCTADESK";

    public static final String UTILIZAR_GYMBOT = "UTILIZAR_GYMBOT";

    public static final String CORRIGIR_PROTOCOLO = "CORRIGIR_PROTOCOLO";

    public static final String GCLOUD_API_KEY = "GCLOUD_API_KEY";
    public static final String GERAR_PRINTS_ACOES_USUARIO = "GERAR_PRINTS_ACOES_USUARIO";
    public static final String LIMITE_PRINTS_ACOES_USUARIO = "LIMITE_PRINTS_ACOES_USUARIO";
    public static final String debugJDBC = "debugJDBC";

    public static final String URL_HTTP_PLATAFORMA_PACTO = "URL_HTTP_PLATAFORMA_PACTO";
    public static final String URL_HTTPS_PLATAFORMA_PACTO = "URL_HTTPS_PLATAFORMA_PACTO";
    public static final String URL_HTTP_ZW_HOMOLOGACAO = "URL_HTTP_ZW_HOMOLOGACAO";
    public static final String URL_HTTPS_ZW_HOMOLOGACAO = "URL_HTTPS_ZW_HOMOLOGACAO";
    public static final String modulos = "modulos";

    public static final String API_OAMD_KEY = "API_OAMD_KEY";

    public static final String GIT_COMMIT_HASH = "git.commit.id.full";
    public static final String GIT_SHORT_COMMIT_HASH = "git.commit.id.abbrev";
    public static final String GIT_COMMIT_TIME = "git.build.time";
    public static final String mockAragornCards = "MOCK_ARAGORN_CARDS";

    private static Properties props = null;
    private static Properties applicationProperties = null;
    private static Properties keyWordsProps= null;
    private static Properties gitProps= null;

    private static final String PATH_PROPS = "/servicos/propriedades/SuperControle.properties";
    private static final String PATH_KEY_WORDS = "/propriedades/KeyWords.properties";
    private static final String PATH_PROPS_APP = "/propriedades/Aplicacao.properties";
    private static final String PATH_PROPS_GIT = "/propriedades/git.properties";
    private static final String PATH_PROPS_OVERRIDE = "/props.override";

    public static final String ativarGoogleAnalytics = "ativarGoogleAnalytics";
    public static final String ativarWeHelp = "ativarWeHelp";
    public static final String ativarAnuncioVitio = "ativarAnuncioVitio";
    public static final String URL_SERVICO_INTEGRACAO_SENDY = "URL_SERVICO_INTEGRACAO_SENDY";
    public static final String EMAIL_PACTO_SENDY = "EMAIL_PACTO_SENDY";
    public static final String BUSCAR_REMOTO_EXTRATO_RETORNO_REMESSA = "BUSCAR_REMOTO_EXTRATO_RETORNO_REMESSA";
    public static final String enviarEmailErroCreditoDCC = "enviarEmailErroCreditoDCC";
    public static final String urlIntegracoesService = "urlIntegracoesService";
    public static final String urlIntegracoesMs = "urlIntegracoesMS";
    public static final String ambienteDesenvolvimentoTeste = "ambienteDesenvolvimentoTeste";
    public static final String ambienteSwarmParaSendy = "ambienteSwarmParaSendy";

    public static final String tokenMailgun = "tokenMailgun";

    public static  final  String domainMail = "domainMail";

    //Pix BB
    public static final String urlApiPixBBSandbox = "urlApiPixBBSandbox";
    public static final String urlApiPixBBProducao = "urlApiPixBBProducao";
    public static final String urlApiPixBBAuthSandbox = "urlApiPixBBAuthSandbox";
    public static final String urlApiPixBBAuthProducao = "urlApiPixBBAuthProducao";
    public static final String urlApiPixBBSandboxV2 = "urlApiPixBBSandboxV2";
    public static final String urlApiPixBBProducaoV2 = "urlApiPixBBProducaoV2";

    //Pix Bradesco
    public static final String urlApiPixBradescoSandbox = "urlApiPixBradescoSandbox";
    public static final String urlApiPixBradescoProducao = "urlApiPixBradescoProducao";
    public static final String urlApiPixBradescoAuthSandbox = "urlApiPixBradescoAuthSandbox";
    public static final String urlApiPixBradescoAuthProducao = "urlApiPixBradescoAuthProducao";
    public static final String urlApiWebhookPixBradesco = "urlApiWebhookPixBradesco";

    //Pix Inter
    public static final String urlApiPixInterProducao = "urlApiPixInterProducao";
    public static final String urlApiPixInterOAuthProducao = "urlApiPixInterOAuthProducao";

    //Pix Itau
    public static final String urlApiPixItauProducao = "urlApiPixItauProducao";
    public static final String urlApiPixItauSandbox = "urlApiPixItauSandbox";
    public static final String urlApiPixItauAuth = "urlApiPixItauAuth";
    public static final String urlApiPixItauAuthSandbox = "urlApiPixItauAuthSandbox";

    //API Pcert PACTO

    public static final String urlAPIPcertPacto = "urlAPIPcertPacto";
    public static final String senhaDecriptPCertPrivadoPfx = "senhaDecriptPCertPrivadoPfx";


    //Pix Santander
    public static final String urlApiPixSantanderSandbox = "urlApiPixSantanderSandbox";
    public static final String urlApiPixSantanderProducao = "urlApiPixSantanderProducao";
    public static final String urlApiPixSantanderAuthSandbox = "urlApiPixSantanderAuthSandbox";
    public static final String urlApiPixSantanderAuthProducao = "urlApiPixSantanderAuthProducao";

    //Certificado Privado Pacto
    public static final String uriCertPrivado = "uriCertPrivado";
    public static final String senhaCertPrivado = "senhaCertPrivado";
    public static final String chaveCriptCertPrivado = "chaveCriptCertPrivado";

    //Senha FTP360
    public static final String senhaCriptFTP360PasswordServer = "senhaCriptFTP360PasswordServer";
    public static final String chaveCriptFTP360PasswordServer = "chaveCriptFTP360PasswordServer";

    //Regua de Cobranca - FacilitePay
    public static  final  String userMailFacilitePay = "userMailFacilitePay";
    public static  final  String domainMailFacilitePay = "domainMailFacilitePay";
    public static  final  String passwordMailFacilitePay = "passwordMailFacilitePay";
    public static  final  String smsChaveFacilitePay = "smsChaveFacilitePay";
    public static  final  String smsTokenFacilitePay = "smsTokenFacilitePay";
    public static  final  String tokenBitlyFacilitePay = "tokenBitlyFacilitePay";
    //Bitly
    public static  final  String urlAPIBitlyV4 = "urlAPIBitlyV4";

    public static final String chaveCriptoBoleto = "chaveCriptoBoleto";

    public static final String loggingOutputMascarade = "loggingOutputMascarade";
    public static final String logOutputDebug = "logOutputDebug";
    public static final String biMs = "biMs";
    public static final String bloquearEmpresasNaoTipificadas = "bloquearEmpresasNaoTipificadas";
    public static final String pathPrivateKeyTemplate = "pathPrivateKeyTemplate";
    public static final String enableLastActionTime = "enableLastActionTime";

    public static final String protocoloHttps = "protocoloHttps";
    public static final String dominioHotsite = "dominioHotsite";
    public static final String ipServidorHotsite = "ipServidorHotsite";
    public static final String enableCountdown = "enableCountdown";
    public static final String tokenAcessoAPICliente = "tokenAcessoAPICliente";
    public static final String tokenAcessoAPIApps = "tokenAcessoAPIApps";
    public static final String pontoInterrogacaoMs = "pontoInterrogacaoMs";
    public static final String negociacaoHabilitado = "negociacaoHabilitado";
    public static final String tokenPushMobile = "tokenPushMobile";
    public static final String tokenPushMobileAppAluno = "tokenPushMobileAppAluno";
    public static final String urlAppDoAlunoUnificado = "urlAppDoAlunoUnificado";
    public static final String permitirEnvioNotificacaoPushAula = "permitirEnvioNotificacaoPushAula";
    public static final String tokenApiWagi = "tokenApiWagi";

    public static final String tokenApiWagiSandbox = "tokenApiWagiSandbox";

    public static final String postmasterMailgun = "postmasterMailgun";

    public static final String postmasterFrom = "postmasterFrom";

    public static final String brandChuckNorris = "brandChuckNorris";

    public static final String urlServicoGetCardScope = "urlServicoGetCardScope";

    private static PropsService _instance = null;

    public static final String urlPlanoMs = "urlPlanoMs";

    public static final String urlWebHookDiscordEnotas= "urlWebHookDiscordEnotas";
    public static final String urlWebHookDiscordEnotasHabilitar = "urlWebHookDiscordEnotasHabilitar";

    public static final String enableMenuZwUI = "enableMenuZwUI";
    public static final String habilitaMarketing = "habilitaMarketing";
    public static final String habilitaClubeDeBeneficios = "habilitaClubeDeBeneficios";
    public static final String tempoSegundosExpirarCacheBanners = "tempoSegundosExpirarCacheBanners";
    public static final String servidorMemCached = "servidorMemCached";
    public static final String urlMarketingMs = "urlMarketingMs";
    public static final String uteisEmailSend = "uteisEmailSend";
    public static final String qtdLimitePactoPay = "qtdLimitePactoPay";
    public static final String cancelarBoletoAoCancelarOuEstonarContrato = "cancelarBoletoAoCancelarOuEstonarContrato";

    public static final String AUTH_SECRET_ZW_PATH = "AUTH_SECRET_ZW_PATH";
    public static final String KEYWORD_PATH = "KEYWORD_PATH";

    public static final String urlApiAsaasProducao = "urlApiAsaasProducao";
    public static final String urlApiAsaasSandbox = "urlApiAsaasSandbox";
    public static final String apiKeyAsaasPactoProducao = "apiKeyAsaasPactoProducao";
    public static final String apiKeyAsaasPactoSandbox = "apiKeyAsaasPactoSandbox";
    public static final String URL_API_PLUGGY = "URL_API_PLUGGY";
    public static final String URL_API_KOBANA_PRODUCAO = "URL_API_KOBANA_PRODUCAO";
    public static final String URL_API_KOBANA_SANDBOX = "URL_API_KOBANA_SANDBOX";
    public static final String TOKEN_PACTO_API_KOBANA_PRODUCAO = "TOKEN_PACTO_API_KOBANA_PRODUCAO";
    public static final String TOKEN_PACTO_API_KOBANA_SANDBOX= "TOKEN_PACTO_API_KOBANA_SANDBOX";
    public static final String clientIdPluggy = "clientIdPluggy";
    public static final String clientSecretPluggy = "clientSecretPluggy";

    public static final String totalPassApi = "totalPassApi";
    public static final String totalPassApiValidate = "totalPassApiValidate";
    public static final String tempoSegundosExpiracaoTokenOperacao = "tempoSegundosExpiracaoTokenOperacao";
    public static final String habilitarNicho = "habilitarNicho";
    public static final String validadeCacheNichoEmMinutos = "validadeCacheNichoEmMinutos";
    public static final String habilitarCacheInitNicho = "habilitarCacheInitNicho";
    public static final String passWordImportadorTreino = "passWordImportadorTreino";
    public static final String habilitarRecursoPadraoTelaCliente = "habilitarRecursoPadraoTelaCliente";
    public static final String enableCompanyWebhook = "enableCompanyWebhook";
    public static final String verifyControllersAfterPhase = "verifyControllersAfterPhase";
    public static final String chaveCriptoImportCc = "chaveCriptoImportCc";
    public static final String tokenImportCc = "tokenImportCc";
    public static final String habilitarFuncionalidadesBeta = "habilitarFuncionalidadesBeta";

    public static final String minimoNotasSolicitacao = "minimoNotasSolicitacao";
    public static final String urlHubspotAPI = "urlHubspotAPI";

    public static final String senhaPactoBr = "senhaPactoBr";
    public static final String senhaPactoBrTeste = "senhaPactoBrTeste";
    public static final String RECORRENCIA_USER_PASSWORD = "RECORRENCIA_USER_PASSWORD";

    public static final String maxGpt = "maxGpt";

    public static final String biRiscoChurn = "biRiscoChurn";
    public static final String GOOGLE_APPLICATION_CREDENTIALS = "GOOGLE_APPLICATION_CREDENTIALS";
    public static final String URL_ARAGORN_MS = "URL_ARAGORN_MS";
    public static final String URL_AUTENTICACAO_MS = "URL_AUTENTICACAO_MS";
    public static final String URL_DISCOVERY_MS = "DISCOVERY_URL";
    public static final String URL_ENVIO_ACESSO_INTEG_PRATIQUE = "URL_ENVIO_ACESSO_INTEG_PRATIQUE";
    public static final String TOKEN_ENVIO_ACESSO_INTEG_PRATIQUE = "TOKEN_ENVIO_ACESSO_INTEG_PRATIQUE";

    static {
        refresh();
    }

    private static PropsService getInstance() {
        if (_instance == null) {
            _instance = new PropsService();
            _instance.loadProps();
        }
        return _instance;
    }

    private void loadProps() {
        props = new Properties();
        applicationProperties = new Properties();
        keyWordsProps = new Properties();
        gitProps = new Properties();
        try {
            props.load(new FileReader(new File(SuperControle.class.getResource(PATH_PROPS).toURI())));
            applicationProperties.load(new FileReader(new File(SuperControle.class.getResource(PATH_PROPS_APP).toURI())));
            keyWordsProps.load(new FileReader(new File(SuperControle.class.getResource(PATH_KEY_WORDS).toURI())));
        } catch (Exception ex) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        try {
            File f = new File(PropsService.getPropertyValue(diretorioArquivos) + PATH_PROPS_OVERRIDE);
            f.getParentFile().mkdirs();
            if (f.exists() && f.length() > 0) {
                Properties p = new Properties(props);
                p.load(new FileReader(f));
                //Uteis.logar("News Props => " + p.size() + " => " + p.toString());
                if (p.size() > 0) {
                    props.putAll(p);
                    Uteis.logar("Current Props (OVERRIDE) => " + props.size() + " => " + props.toString());
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    public static void refresh() {
        refresh(false);
    }

    public static void refresh(boolean forceReload) {
        _instance = null;
        getInstance();
        if (forceReload) LegolasService.init();
    }

    public static String getPropertyValue(final String name) {
        return props.getProperty(name);
    }

    public static String getKeyWordPropertyValue(final String name) {
        return keyWordsProps.getProperty(name);
    }

    public static String getPropertyValueApp(final String name) {
        return applicationProperties.getProperty(name);
    }

    public static String getGitProperty(final String name) {
        return gitProps.getProperty(name);
    }

    public static void setPropertyValue(final String name, final String value) {
        setProp(name, value);
    }

    public static boolean isTrue(final String name) {
        return getInstance().getPropertyValue(name) != null
                && getInstance().getPropertyValue(name).equalsIgnoreCase("true");
    }

    public static boolean isEmpty(final String name) {
        return getInstance().getPropertyValue(name) == null
                || getInstance().getPropertyValue(name).trim().isEmpty();
    }

    public static String asString(final String name) {
        return getInstance().getPropertyValue(name) != null
                ? getInstance().getPropertyValue(name) : "";
    }

    public static String getPropertyValue(final String chave, final String name) {
        final String propOAMD = DAO.getPropertyValue(chave, name);
        return propOAMD != null && !propOAMD.isEmpty() ? propOAMD
                : getInstance().getPropertyValue(name);
    }

    public static void setProp(final String name, final String value) {
        props.setProperty(name, value);
    }

    public void saveProps(boolean reload) {
        FileOutputStream fos;
        try {
            File f = new File(SuperControle.class.getResource(PATH_PROPS).toURI());
            fos = new FileOutputStream(f);
            props.store(fos, null);
            if (reload) {
                loadProps();
            }
        } catch (Exception ex) {
            Logger.getLogger(PropsService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void save(boolean reload){
        getInstance().saveProps(reload);
    }

    public static List<String> atualizarInfoInstancias() {
        List<String> lista = AwsConsoleApp.getInstancesAddresses(
                PropsService.getPropertyValue(PropsService.prefixoIntanciasCloud));
        String aux = lista.toString();
        aux = aux.replaceAll(" ", "").replaceAll("\\[", "").replaceAll("\\]", "");
        PropsService.setProp(PropsService.instanciasNotificar, aux);
        PropsService.save(true);
        return lista;
    }
    public static String getUrlServicoIntegracaoSendy() {
       return PropsService.getPropertyValue(PropsService.URL_SERVICO_INTEGRACAO_SENDY);
    }
    public static String getEmailIntegracaoSendy() {
       return PropsService.getPropertyValue(PropsService.EMAIL_PACTO_SENDY);
    }

    public static String getPassWordImportadorTreino() {
        return PropsService.getPropertyValue(PropsService.passWordImportadorTreino);
    }

    public static boolean isCompanyWebhookEnabled() {
        return PropsService.isTrue(PropsService.enableCompanyWebhook);
    }

    public static boolean isVerifyControllersAfterPhase() {
        return PropsService.isTrue(PropsService.verifyControllersAfterPhase);
    }
}
