package servicos.remessa.layouts;

import negocio.comuns.utilitarias.Calendario;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;
import servicos.remessa.to.ConvenioCobrancaTO;
import servicos.remessa.to.RemessaItemTO;
import servicos.remessa.to.RemessaTO;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Johnys on 05/02/2017.
 */
public class LayoutBase {

    protected RemessaTO remessa;

    public LayoutBase (){}

    public LayoutBase(RemessaTO remessa){
        this.remessa = remessa;
    }

    public LayoutBase setRemessa(RemessaTO remessa){
        this.remessa = remessa;
        return this;
    }

    /**
     * Retorna o nome do arquivo da remessa.
     * @return
     */
    public String getNomeArquivo(){
        return String.format("BRPACT01.REM%s-%s", Calendario.getData("yyMMddHHmmss"), this.remessa.getCodigo()) + "_" + remessa.getChave() + ".REM";
    }

    /**
     * Realiza a montagem do {@link StringBuilder} referente ao arquivo.
     * @param remessa
     * @return
     */
    public StringBuilder montarArquivo(){
        RegistroRemessa header = getHeader();
        List<RegistroRemessa> detail = getDeatail();
        RegistroRemessa trailer = getTrailer();
        StringBuilder arquivo = new StringBuilder();
        arquivo.append(header.toStringBuffer().toString());
        arquivo.append("\n");
        for(RegistroRemessa reg : detail){
            arquivo.append(reg.toStringBuffer().toString());
            arquivo.append("\n");
        }
        arquivo.append(trailer.toStringBuffer());
        arquivo.append("\n");
        return arquivo;
    }

    /**
     * Retorna o {@link RegistroRemessa} que compreende ao header do arquivo.
     * @return
     */
    protected RegistroRemessa getHeader(){
        return new RegistroRemessa(TipoRegistroEnum.HEADER);
    }

    /**
     * Retorna uma lista dos {@link RegistroRemessa} que compreende ao seu detalhe
     * @return
     */
    protected List<RegistroRemessa> getDeatail(){
        List<RegistroRemessa> details = new ArrayList<RegistroRemessa>();
        Integer linha = 0;
        for(RemessaItemTO item : remessa.getItens()){
            linha++;
            details.add(getDetail(item, linha));
        }
        return details;
    }

    /**
     * Retorna o {@link RegistroRemessa} a partir do {@link RemessaItemTO}
     * @param item
     * @return
     */
    protected RegistroRemessa getDetail(RemessaItemTO item, Integer linha) {
        return new RegistroRemessa(TipoRegistroEnum.DETALHE);
    }

    /**
     * Retorna o {@link RegistroRemessa} que corresponde ao trailer do arquivo.
     * @return
     */
    protected  RegistroRemessa getTrailer(){
        return new RegistroRemessa(TipoRegistroEnum.TRAILER);
    }

    public List<RemessaTO> processarRetorno(ConvenioCobrancaTO convenio) throws Exception{
        return new ArrayList<RemessaTO>();
    }

    public RemessaTO descobrirRemessa(StringBuilder sb) throws Exception{
        return null;
    }

    public void processarRetorno(RemessaTO remessa, StringBuilder arquivo) throws Exception{

    }

    public void processarAtributosRetorno(RemessaTO remessa, StringBuilder retorno) throws Exception{

    }

    /**
     * Cria um mapa de {@link RemessaItemTO} a partir de seus identificadores.
     * @param remessa
     * @return
     */
    protected Map<Long, RemessaItemTO> mapearItensRemessaPorIdentificador(RemessaTO remessa){
        Map<Long, RemessaItemTO> map = new HashMap<Long, RemessaItemTO>(remessa.getItens().size());
        for(RemessaItemTO item : remessa.getItens()){
            map.put(item.getIdentificador(), item);
        }
        return  map;
    }
}
