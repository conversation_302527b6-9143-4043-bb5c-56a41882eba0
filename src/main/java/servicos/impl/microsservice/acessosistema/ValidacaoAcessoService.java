package servicos.impl.microsservice.acessosistema;

import acesso.webservice.retorno.ResultadoWS;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import org.json.JSONObject;
import servicos.impl.microsservice.SuperMSService;
import servicos.util.ExecuteRequestHttpService;

import java.util.HashMap;

public class ValidacaoAcessoService extends SuperMSService {

    private ValidacaoAcessoService() {

    }

    public static ResultadoWS updateAccessAuthorization(final AutorizacaoAcessoGrupoEmpresarialVO autorizacao) throws Exception {
        String urlEndpoint = "%s/prest/validaracesso?key=%s&operacao=atualizarAssinaturaBiometriaDigitalPorCodAcesso";

        final String url = String.format(urlEndpoint, autorizacao.getIntegracao().getUrlZillyonWeb(),
                autorizacao.getIntegracao().getChave());
        try {
            String codAcessoOriginal = autorizacao.getCodAcesso().replace("NU", "");

            JSONObject body = new JSONObject();
            body.put("codigoAcesso", codAcessoOriginal);
            body.put("assinatura", autorizacao.getAssinaturaBiometriaDigital());

            String response = ExecuteRequestHttpService.post(url, body.toString(), new HashMap<>());
            JSONObject objRetorno = new JSONObject(response).optJSONObject("sucesso");
            return JSONMapper.getObject(objRetorno, ResultadoWS.class);
        } catch (Exception ex) {
            throw new Exception(ex);
        }
    }
}
