package servicos.impl.stone.xml.authorization.request;

import negocio.comuns.utilitarias.RecuperadorEnumPadrao;
import negocio.comuns.utilitarias.ValorRecuperavel;

/**
 * <AUTHOR>
 * @since 28/02/2019
 */
public enum InstalmentTypeInstlmtTp implements ValorRecuperavel {

    A_VISTA("NONE"),
    SEM_JUROS_LOJISTA("MCHT"),
    COM_JUROS_EMISSOR("ISSR");

    private final String value;

    InstalmentTypeInstlmtTp(String value) {
        this.value = value;
    }

    @Override
    public String getValor() {
        return value;
    }

    public static InstalmentTypeInstlmtTp fromValue(String value) {
        return RecuperadorEnumPadrao.fromValue(values(), value);
    }

    public boolean isAVista() {
        return this == A_VISTA;
    }
}
