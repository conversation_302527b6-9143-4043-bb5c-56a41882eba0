package servicos.impl.stone.xml.authorization.response;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.stone.xml.cancellation.response.CancellationAuthorisationResultAuthstnRslt;

/**
 * <AUTHOR>
 * @since 01/03/2019
 */
class AuthorisationResultAuthstnRslt extends CancellationAuthorisationResultAuthstnRslt {

    @XStreamAlias("AuthstnCd")
    private String authorisationCodeAuthstnCd;

    @XStreamAlias("RtryTxLmt")
    private String rtryTxLmt;

    @XStreamAlias("RtryTxDtLmt")
    private String rtryTxDtLmt;

    @XStreamAlias("AuthstnDtTm")
    private String authstnDtTm;
    public AuthorisationResultAuthstnRslt(ResponseToAuthorisationRspnToAuthstn responseToAuthorisationRspnToAuthstn,
                                          Boolean completionRequiredCmpltnReqrd) {
        super(responseToAuthorisationRspnToAuthstn, completionRequiredCmpltnReqrd);
    }

    String getAuthorisationCodeAuthstnCd() {
        return authorisationCodeAuthstnCd;
    }

    public String getRtryTxLmt() {
        return rtryTxLmt;
    }

    public void setRtryTxLmt(String rtryTxLmt) {
        this.rtryTxLmt = rtryTxLmt;
    }

    public String getRtryTxDtLmt() {
        return rtryTxDtLmt;
    }

    public void setRtryTxDtLmt(String rtryTxDtLmt) {
        this.rtryTxDtLmt = rtryTxDtLmt;
    }

    public String getAuthstnDtTm() {
        if (UteisValidacao.emptyString("authstnDtTm")) {
            return "";
        }
        return authstnDtTm;
    }

    public void setAuthstnDtTm(String authstnDtTm) {
        this.authstnDtTm = authstnDtTm;
    }
}
