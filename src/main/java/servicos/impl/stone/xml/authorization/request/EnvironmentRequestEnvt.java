package servicos.impl.stone.xml.authorization.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 26/02/2019
 */
class EnvironmentRequestEnvt {

    @XStreamAlias("Mrchnt")
    private MerchantMrchnt mrchnt;

    @XStreamAlias("POI")
    private PointInteractionPOI pointInteractionPOI;

    @XStreamAlias("Card")
    private Card card;

    EnvironmentRequestEnvt() {
    }

    EnvironmentRequestEnvt(MerchantMrchnt mrchnt,
                           PointInteractionPOI pointInteractionPOI,
                           Card card) {
        this.mrchnt = mrchnt;
        this.pointInteractionPOI = pointInteractionPOI;
        this.card = card;
    }

    Card getCard() {
        return card;
    }

    void setMrchnt(MerchantMrchnt mrchnt) {
        this.mrchnt = mrchnt;
    }

    void setPointInteractionPOI(PointInteractionPOI pointInteractionPOI) {
        this.pointInteractionPOI = pointInteractionPOI;
    }

    void setCard(Card card) {
        this.card = card;
    }
}
