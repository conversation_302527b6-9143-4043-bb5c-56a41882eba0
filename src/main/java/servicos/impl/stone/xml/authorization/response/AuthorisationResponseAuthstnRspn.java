package servicos.impl.stone.xml.authorization.response;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 26/02/2019
 */
class AuthorisationResponseAuthstnRspn {

    @XStreamAlias("Envt")
    private EnvironmentResponseEnvt envt;

    @XStreamAlias("Tx")
    private TransactionResponseTx transactionResponseTx;

    @XStreamAlias("TxRspn")
    private TransactionResponseTxRspn transactionResponseTxRspn;

    AuthorisationResponseAuthstnRspn(EnvironmentResponseEnvt envt,
                                     TransactionResponseTx transactionResponseTx,
                                     TransactionResponseTxRspn transactionResponseTxRspn) {
        this.envt = envt;
        this.transactionResponseTx = transactionResponseTx;
        this.transactionResponseTxRspn = transactionResponseTxRspn;
    }

    EnvironmentResponseEnvt getEnvt() {
        return envt;
    }

    TransactionResponseTx getTransactionResponseTx() {
        return transactionResponseTx;
    }

    TransactionResponseTxRspn getTransactionResponseTxRspn() {
        return transactionResponseTxRspn;
    }
}
