package servicos.impl.stone.xml;

import static java.lang.String.format;

/**
 * Lançada quando algum erro acontecer durante a conversão de um {@link AbstractStoneElementXML} para XML ou vice-verso.
 *
 * <AUTHOR>
 * @since 13/02/2019
 */
class ConversaoXMLStoneException extends StoneException {

    <T extends AbstractStoneElementXML> ConversaoXMLStoneException(Class<T> clazz, String xml, Throwable cause) {
        super(
                format("Houve uma falha na conversão do xml para a classe (%s). XML -> \n%s", clazz.getName(), xml),
                cause
        );
    }

    <T extends AbstractStoneElementXML> ConversaoXMLStoneException(Class<T> clazz, Throwable cause) {
        super(
                format("Houve uma falha na conversão da classe (%s) para XML", clazz.getName()),
                cause
        );
    }
}
