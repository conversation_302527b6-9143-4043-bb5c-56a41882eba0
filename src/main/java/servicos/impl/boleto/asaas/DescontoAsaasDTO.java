package servicos.impl.boleto.asaas;

import negocio.comuns.financeiro.enumerador.TipoDescontoAsaasEnum;
import org.json.JSONObject;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 05/05/2023
 */

public class DescontoAsaasDTO {
    private Double value = 0.0;
    private TipoDescontoAsaasEnum tipoDescontoAsaasEnum;

    public DescontoAsaasDTO() {
    }

    public DescontoAsaasDTO(JSONObject json) {
        setValue(json.optDouble("value"));
    }

    public Double getValue() {
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
    }

    public TipoDescontoAsaasEnum getTipoDescontoAsaasEnum() {
        return tipoDescontoAsaasEnum;
    }

    public void setTipoDescontoAsaasEnum(TipoDescontoAsaasEnum tipoDescontoAsaasEnum) {
        this.tipoDescontoAsaasEnum = tipoDescontoAsaasEnum;
    }
}
