package servicos.impl.dcc.santander;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.RemessaItemMovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by GlaucoT on 22/03/2016
 */
public class LayoutRemessaSantanderCNAB400 extends LayoutRemessaBase {

    public static void preencherArquivoRemessa(RemessaVO remessa) {

        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);
        Date dataDeposito = remessa.getDataRegistro();
        header.put(DCCAttEnum.TipoRegistro, "0");//1
        header.put(DCCAttEnum.CodigoRemessa, "1");//1
        header.put(DCCAttEnum.IdentificacaoRemessa, "REMESSA");//7
        header.put(DCCAttEnum.CodigoServico, "01");//2
        header.put(DCCAttEnum.IdentificacaoServico, StringUtilities.formatarCampoEmBranco("COBRANCA", 15));
        //Código de Transmissão
        header.put(DCCAttEnum.CodigoTransmissao, StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getCodigoTransmissao(), MathContext.UNLIMITED), 20));
        header.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(Uteis.removerEspacosInicioFimString(remessa.getConvenioCobranca().getEmpresa().getNome()), 30));
        header.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), MathContext.UNLIMITED), 3));
        header.put(DCCAttEnum.NomeBanco, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getBanco().getNome(), 15));
        header.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(dataDeposito, "ddMMyy"));//6
        header.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(16));
        header.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(275));
        header.put(DCCAttEnum.VersaoLayout, StringUtilities.formatarCampoZerado(3));
        header.put(DCCAttEnum.SequencialRegistro, "000001");//6

        List<RemessaItemVO> lista = remessa.getListaItens();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        int qtdAceito = 0;
        int seq = 2;
        for (RemessaItemVO item : lista) {
            if(item.getValorBoleto() > 0){
                EnderecoVO enderecoVO = new EnderecoVO();
                for (EnderecoVO endereco : item.getPessoa().getEnderecoVOs()) {
                    if (endereco.getEnderecoCorrespondencia() || endereco.getTipoEndereco().equals(TipoEnderecoEnum.RESIDENCIAL.getCodigo())) {
                        enderecoVO = endereco;
                    }
                }
                if(UteisValidacao.emptyNumber(enderecoVO.getCodigo())) {
                    if(!UteisValidacao.emptyList(item.getPessoa().getEnderecoVOs())){
                        enderecoVO = item.getPessoa().getEnderecoVOs().get(0);
                    }
                }

                RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);

                detail.put(DCCAttEnum.TipoRegistro, "1");
                detail.put(DCCAttEnum.CpfOuCnpj, "02");
                detail.put(DCCAttEnum.CnpjEmpresa, StringUtilities.formatarCampoForcandoZerosAEsquerda(Uteis.removerMascara(remessa.getConvenioCobranca().getEmpresa().getCNPJ()), 14));
//            Nota 1: Código de Transmissão
//            Informação cedida pelo banco que identifica o arquivo remessa do cliente
                detail.put(DCCAttEnum.CodigoTransmissao, StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getCodigoTransmissao(), MathContext.UNLIMITED), 20));

                String numComprovante =  StringUtilities.formatarCampo(new BigDecimal(item.getCodigo()), 25);
                detail.put(DCCAttEnum.NumeroComprovanteVenda, numComprovante);
                //   Número de controle do Participante, para controle por parte do cedente (25);
                // Multiplicar da direita para a esquerda, de 2 até 9, até o final do número,  reiniciando em 2, se necessário.
                // Dividir o total da soma por onze.
                // Assim sendo, se o resto igual a 10 (dez) o digito será 1 (um), se o resto igual a 1 (um) ou 0 (zero), o digito será 0 (zero).
                // Qualquer resto diferente de 0 (zero) , 1 (um) e 10 (dez), subtrair o resto de 11 para obter o digito.
                String nossoNumero =  StringUtilities.formatarCampo(new BigDecimal(item.getIdentificador()), 7);

                detail.put(DCCAttEnum.NossoNumero, (nossoNumero+item.obterDVNossoNumeroCNB400(nossoNumero)));//8
                detail.put(DCCAttEnum.DataSegundoDesconto, StringUtilities.formatarCampoZerado(6));
                detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
                detail.put(DCCAttEnum.CobrarMulta, remessa.getConvenioCobranca().getEmpresa().getCobrarAutomaticamenteMultaJuros() ? "4" :  StringUtilities.formatarCampoZerado(1));
                detail.put(DCCAttEnum.PercentualMulta,StringUtilities.formatarCampoMonetario(remessa.getConvenioCobranca().getEmpresa().getMultaCobrancaAutomatica(),4));
                detail.put(DCCAttEnum.Moeda, StringUtilities.formatarCampoZerado(2));

                detail.put(DCCAttEnum.ValorTituloOutraUnidade, StringUtilities.formatarCampoZerado(13));

                detail.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(4));

                detail.put(DCCAttEnum.DataMora, StringUtilities.formatarCampoZerado(6));

                String carteira = StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getCarteira()), 1);
                detail.put(DCCAttEnum.CodCarteira,StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getCarteira(),1));

                detail.put(DCCAttEnum.CodOcorrencia, "01");

                //Seu número (10);
                String numDocumento =  StringUtilities.formatarCampo(new BigDecimal(item.getCodigo()), 10);
                detail.put(DCCAttEnum.NumDocumento, numDocumento);

                detail.put(DCCAttEnum.DataVencimento, StringUtilities.formatarCampoData(item.getDataVencimentoBoleto(), "ddMMyy"));//6

                detail.put(DCCAttEnum.ValorVenda, StringUtilities.formatarCampoMonetario(item.getValorBoleto(), 13));

                detail.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getBanco().getCodigoBanco()), 3));

                detail.put(DCCAttEnum.AgenciaDepositaria, StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getContaEmpresa().getAgencia()), 5));

                detail.put(DCCAttEnum.EspecieTitulo, "01");

                detail.put(DCCAttEnum.Identificacao, "N");

                detail.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(Calendario.hoje(), "ddMMyy"));//6

                detail.put(DCCAttEnum.Instrucao1, remessa.getConvenioCobranca().getNrDiasProtesto() > 0 ? "06" : StringUtilities.formatarCampoZerado(2));

                detail.put(DCCAttEnum.Instrucao2, StringUtilities.formatarCampoZerado(2));//2

                Double valorJurosReal;
                if (remessa.getConvenioCobranca().getEmpresa().isUtilizarJurosValorAbsoluto()) {
                    valorJurosReal = remessa.getConvenioCobranca().getEmpresa().getJurosCobrancaAutomatica();
                } else {
                    valorJurosReal = item.getValorBoleto() * (remessa.getConvenioCobranca().getEmpresa().getJurosCobrancaAutomatica() / 100);
                }

                detail.put(DCCAttEnum.MoraDia, remessa.getConvenioCobranca().getEmpresa().getCobrarAutomaticamenteMultaJuros() ? StringUtilities.formatarCampoMonetario(valorJurosReal,13) : StringUtilities.formatarCampoZerado(13));

                detail.put(DCCAttEnum.DataLimiteDesconto, StringUtilities.formatarCampoZerado(6));

                detail.put(DCCAttEnum.ValorDesconto, StringUtilities.formatarCampoZerado(13));

                detail.put(DCCAttEnum.IOF, StringUtilities.formatarCampoZerado(13));

                detail.put(DCCAttEnum.ValorAbatimento, StringUtilities.formatarCampoZerado(13));

                detail.put(DCCAttEnum.TipoPagador, item.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? "01" : "02");

                String cpfCnpj = item.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? getCPF(item) : getCNPJ(item);
                detail.put(DCCAttEnum.CpfCliente, StringUtilities.formatarCpfCnjp(cpfCnpj, 14));

                detail.put(DCCAttEnum.NomePessoa, getNome(item, 40));

                detail.put(DCCAttEnum.EnderecoPagador, StringUtilities.formatarCampoEmBranco(Uteis.removerCaracteresNaoAscii(enderecoVO.getEndereco() + ", " + enderecoVO.getNumero()), 40));

                detail.put(DCCAttEnum.BairroPagador, StringUtilities.formatarCampoEmBranco(Uteis.removerCaracteresNaoAscii(enderecoVO.getBairro()), 12));

                detail.put(DCCAttEnum.CEPPagador, StringUtilities.formatarCampoEmBranco(Uteis.removerMascara(enderecoVO.getCep().trim()), 8));

                detail.put(DCCAttEnum.CidadePagador, StringUtilities.formatarCampoEmBranco(item.getPessoa().getCidade().getNome(), 15));

                detail.put(DCCAttEnum.EstadoPagador, StringUtilities.formatarCampoEmBranco(item.getPessoa().getEstadoVO().getSigla(), 2));

                detail.put(DCCAttEnum.SacadorAvalista, StringUtilities.formatarCampoEmBranco(30));

                detail.put(DCCAttEnum.EmBranco3, StringUtilities.formatarCampoEmBranco(1));

                detail.put(DCCAttEnum.IdentificadorComplemento, "I");

                detail.put(DCCAttEnum.Complemento, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getVariacao(),2));

                detail.put(DCCAttEnum.EmBranco4, StringUtilities.formatarCampoEmBranco(6));

                detail.put(DCCAttEnum.Protesto, remessa.getConvenioCobranca().getNrDiasProtesto() > 0 ? StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getNrDiasProtesto(), 2) : StringUtilities.formatarCampoZerado(2));

                detail.put(DCCAttEnum.EmBranco5, StringUtilities.formatarCampoEmBranco(1));

                detail.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampo(new BigDecimal(seq), 6));

                if (item.getCodigo() != 0) {
                    qtdAceito += 1;
                }
                seq++;
                //
                listaDetalhe.add(detail);
            }
        }
        remessa.setQtdAceito(qtdAceito);

        double valorTotalBruto = 0.0;
        for (RemessaItemVO boleto : remessa.getListaItens()) {
            valorTotalBruto += boleto.getValorItemRemessa();
        }



        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);
        trailer.put(DCCAttEnum.TipoRegistro, "9");
        trailer.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getListaItens().size()+2, 6));
        trailer.put(DCCAttEnum.ValorTotalBruto, StringUtilities.formatarCampoMonetario(valorTotalBruto, 13));
        trailer.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(374));
        trailer.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampo(new BigDecimal(seq), 6));

        remessa.setTrailerRemessa(trailer);
        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setHeaderRemessa(header);

        remessa.setTrailer(new StringBuilder(trailer.toString()));
        remessa.setHead(new StringBuilder(header.toString()));
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));
    }

    public static RegistroRemessa obterHeaderRetorno(final StringBuilder retorno) throws IOException {
        RegistroRemessa h = null;
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            h = new RegistroRemessa(TipoRegistroEnum.HEADER);

            while ((linha = br.readLine()) != null) {
                if (linha.toUpperCase().startsWith("02RETORNO")) {
                    lerAtributos(linha, h);
                }
                break;
            }
        }
        return h;
    }

    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("02RETORNO")) {
                    lerAtributos(linha, h);
                } else if (linha.startsWith("1")) {
                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                    lerAtributos(linha, detail);
                    listaDetalheRetorno.add(detail);
                } else if (linha.startsWith("9")) {
                    lerAtributos(linha, t);
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);

            validarArquivoRemessaRetorno(remessa);

            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
        }
    }

    public static void lerAtributos(final String linha, RegistroRemessa r) {
        if (r.getTipo() == TipoRegistroEnum.HEADER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, linha));//1
            r.put(DCCAttEnum.CodigoRetorno, StringUtilities.readString(1, linha));//1
            r.put(DCCAttEnum.IdentificacaoRetorno, StringUtilities.readString(2, 9, linha));//7
            r.put(DCCAttEnum.CodigoServico, StringUtilities.readString(9, 11, linha));//2
            r.put(DCCAttEnum.IdentificacaoServico, StringUtilities.readString(11, 26, linha));//15
            r.put(DCCAttEnum.AgenciaDepositaria, StringUtilities.readString(26, 30, linha));//4
            r.put(DCCAttEnum.ContaCorrente, StringUtilities.readString(30, 38, linha));//8
            r.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.readString(38, 46, linha));//8
            r.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(46, 76, linha));//30
            r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(76, 79, linha));//3
            r.put(DCCAttEnum.NomeBanco, StringUtilities.readString(79, 94, linha));//15
            r.put(DCCAttEnum.DataGeracao, StringUtilities.readString(94, 100, linha));//6
            r.put(DCCAttEnum.Zeros, StringUtilities.readString(100, 110, linha));//10
            r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(110, 117, linha));//266
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(117, 391, linha));//9
            r.put(DCCAttEnum.VersaoLayout, StringUtilities.readString(391, 394, linha));//6
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));//6
        } else if (r.getTipo() == TipoRegistroEnum.DETALHE) {
            if (linha.length() >= 400) {
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, linha));//1
                r.put(DCCAttEnum.TipoPagador, StringUtilities.readString(1, 3, linha));//2
                r.put(DCCAttEnum.InscricaoPagador, StringUtilities.readString(3, 17, linha));//14
                r.put(DCCAttEnum.AgenciaDebito, StringUtilities.readString(17, 21, linha));//4
                r.put(DCCAttEnum.ContaMovimentoCedente, StringUtilities.readString(21, 29, linha));//8
                r.put(DCCAttEnum.ContaCorrente, StringUtilities.readString(29, 37, linha));//8
                String aux = StringUtilities.readString(37, 62, linha);
                if(aux.replaceAll(" ", "").isEmpty()){
                    aux = "000000000000000000" + StringUtilities.readString(62, 69, linha);
                }
                r.put(DCCAttEnum.NumeroComprovanteVenda, aux);//25

                r.put(DCCAttEnum.NumDocumento, StringUtilities.readString(62, 70, linha));//8
                r.put(DCCAttEnum.EmBranco, StringUtilities.readString(70, 107, linha));//8
                r.put(DCCAttEnum.Carteira, StringUtilities.readString(107, linha));//1
                r.put(DCCAttEnum.StatusVenda, StringUtilities.readString(108, 110, linha));//8
                r.put(DCCAttEnum.DataOcorrencia, StringUtilities.readString(110, 116, linha));//1
                aux = StringUtilities.readString(116, 126, linha);
                if(aux.replaceAll(" ", "").isEmpty()){
                    aux = "000" + StringUtilities.readString(126, 133, linha);

                }
                r.put(DCCAttEnum.NossoNumero, aux);//10 //Seu Número
                r.put(DCCAttEnum.NossoNumero2, StringUtilities.readString(126, 134, linha));//10
                r.put(DCCAttEnum.CodigoRemessa, StringUtilities.readString(134, 136, linha));//2

                r.put(DCCAttEnum.CodigoErro, StringUtilities.readString(136, 139, linha));//12
                r.put(DCCAttEnum.CodigoErro2, StringUtilities.readString(139, 142, linha));//12
                r.put(DCCAttEnum.CodigoErro3, StringUtilities.readString(142, 145, linha));//12

                r.put(DCCAttEnum.EmBranco, StringUtilities.readString(146, linha));//6
                r.put(DCCAttEnum.DataVencimento, StringUtilities.readString(146, 152, linha));//10
                r.put(DCCAttEnum.ValorVenda, StringUtilities.readString(152, 165, linha));//20
                r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(165, 168, linha));//6
                r.put(DCCAttEnum.AgenciaDepositaria, StringUtilities.readString(168, 173, linha));//13
                r.put(DCCAttEnum.EspecieTitulo, StringUtilities.readString(173, 175, linha));//3
                r.put(DCCAttEnum.DespesaCobranca, StringUtilities.readString(175, 188, linha));//5
                r.put(DCCAttEnum.DespesaOutras, StringUtilities.readString(188, 201, linha));//2
                r.put(DCCAttEnum.Juros, StringUtilities.readString(201, 214, linha));//13
                r.put(DCCAttEnum.IOF, StringUtilities.readString(214, 227, linha));//13
                r.put(DCCAttEnum.ValorAbatimentoConcedido, StringUtilities.readString(227, 240, linha));//13
                r.put(DCCAttEnum.ValorDesconto, StringUtilities.readString(240, 253, linha));//13
                r.put(DCCAttEnum.ValorPago, StringUtilities.readString(253, 266, linha));//13
                r.put(DCCAttEnum.ValorMora, StringUtilities.readString(266, 279, linha));//13
                r.put(DCCAttEnum.OutrosCreditos, StringUtilities.readString(279, 292, linha));//13
                r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(293, linha));//13
                r.put(DCCAttEnum.Identificacao, StringUtilities.readString(294, linha));//13
                r.put(DCCAttEnum.EmBranco3, StringUtilities.readString(295, linha));//2
                r.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(295, 301, linha));//1
                r.put(DCCAttEnum.NomePessoa, StringUtilities.readString(301, 337, linha));//6
                r.put(DCCAttEnum.IdentificadorComplemento, StringUtilities.readString(337, 338, linha));//3
                r.put(DCCAttEnum.Moeda, StringUtilities.readString(338, 340, linha));//10
                r.put(DCCAttEnum.ValorTituloOutraUnidade, StringUtilities.readString(340, 353, linha));//4
                r.put(DCCAttEnum.ValorBaseIOF, StringUtilities.readString(353, 366, linha));//10
                r.put(DCCAttEnum.ValorTotalLiquido, StringUtilities.readString(366, 379, linha));//13
                r.put(DCCAttEnum.TipoOperacao, StringUtilities.readString(379, 380, linha));//1 //D = Débito ou C = Crédito
                r.put(DCCAttEnum.EmBranco5, StringUtilities.readString(380, 391, linha));//11
                r.put(DCCAttEnum.VersaoLayout, StringUtilities.readString(391, 394, linha));//3
                r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));//6
            }
        } else if (r.getTipo() == TipoRegistroEnum.TRAILER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, linha));//1
            r.put(DCCAttEnum.IdentificacaoRetorno, StringUtilities.readString(1, linha));//1
            r.put(DCCAttEnum.IdentificacaoServico, StringUtilities.readString(2, 4, linha));//2
            r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(4, 7, linha));//3
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(7, 17, linha));//10
            r.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(17, 25, linha));//8
//          Valor referente ao valor total de boletos registrados a serem recebidos na conta do cedente
            r.put(DCCAttEnum.ValorTotalBruto, StringUtilities.readString(25, 39, linha));//14
            r.put(DCCAttEnum.NumAvisoBancario, StringUtilities.readString(39, 47, linha));//8
            r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(47, 97, linha));//10
            r.put(DCCAttEnum.QuantidadeRegistrosCaucionados, StringUtilities.readString(97, 105, linha));//8
            r.put(DCCAttEnum.ValorRegistrosCaucionados, StringUtilities.readString(105, 119, linha));//12
            r.put(DCCAttEnum.NumAvisoBancarioCaucionados, StringUtilities.readString(119, 127, linha));//8
            r.put(DCCAttEnum.EmBranco3, StringUtilities.readString(127, 137, linha));//10
            r.put(DCCAttEnum.QuantidadeRegistrosDescontados, StringUtilities.readString(137, 145, linha));//8
            r.put(DCCAttEnum.ValorRegistrosDescontados, StringUtilities.readString(145, 159, linha));//12
            r.put(DCCAttEnum.NumAvisoBancarioDescontados, StringUtilities.readString(159, 167, linha));//8
            r.put(DCCAttEnum.EmBranco4, StringUtilities.readString(167, 391, linha));//224
            r.put(DCCAttEnum.VersaoLayout, StringUtilities.readString(391, 394, linha));//3
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));//6

        }
    }
}
