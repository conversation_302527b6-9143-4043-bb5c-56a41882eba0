/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.hsbc;

import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.dco.febraban.LayoutRemessaFebrabanDCO;

/**
 *
 * <AUTHOR>
 */
public class LayoutRemessaHSBCDCO extends LayoutRemessaFebrabanDCO {

    public static void preencherArquivoRemessa(RemessaVO remessa) {

        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);
        Date dataDeposito =  remessa.getDataRegistro();
        header.put(DCCAttEnum.TipoRegistro, "A");
        header.put(DCCAttEnum.CodigoRemessa, "1");
        header.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.formatarCampo(
                new BigDecimal(remessa.getConvenioCobranca().getNumeroContrato(), MathContext.UNLIMITED), 10));
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(10));
        
        header.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getEmpresa().getNome(), 20));
        header.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampo(
                new BigDecimal(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), MathContext.UNLIMITED), 3));
        header.put(DCCAttEnum.NomeBanco, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getBanco().getNome(), 20));
        header.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(dataDeposito, "yyyyMMdd"));

        String numeroResumoOperacoes = "";
        RegistroRemessa headerRemessa = remessa.getHeaderRemessa();
        for (ObjetoGenerico obj : headerRemessa.getAtributos()) {
            if (obj.getAtributo().equals(DCCAttEnum.NumeroResumoOperacoes.name())) {
                numeroResumoOperacoes = StringUtilities.formatarCampo(new BigDecimal(obj.getValor()), 6);
            }
        }
        numeroResumoOperacoes = UteisValidacao.emptyString(numeroResumoOperacoes) ? 
                StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getSequencialDoArquivo()), 6) 
                : numeroResumoOperacoes;
        
        header.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.formatarCampo(
                    new BigDecimal(numeroResumoOperacoes), 6));
        header.put(DCCAttEnum.VersaoLayout, "04");
        header.put(DCCAttEnum.IdentificacaoServico, "DEBITO AUTOMATICO");
        header.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.formatarCampoEmBranco(52));


        List<RemessaItemVO> lista = remessa.getListaItens();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        double soma = 0;
        int qtd = 0;
        for (RemessaItemVO item : lista) {
            RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
            detail.put(DCCAttEnum.TipoRegistro, "E");
            detail.put(DCCAttEnum.IdentificadorClienteEmpresa, StringUtilities.formatarCampo(new BigDecimal(item.getMovParcela().getPessoa().getCodigo()), 20));
            detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(5));

            detail.put(DCCAttEnum.AgenciaDebito, StringUtilities.formatarCampo(new BigDecimal(item.get(DCCAttEnum.AgenciaDebito.name()), MathContext.UNLIMITED), 4));
            String identificador;
            try {
                identificador = StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), MathContext.UNLIMITED), 3)
                        + StringUtilities.formatarCampo(new BigDecimal(item.get(DCCAttEnum.AgenciaDebito.name()), MathContext.UNLIMITED), 4)
                        + StringUtilities.formatarCampo(new BigDecimal(item.get(DCCAttEnum.ContaCorrente.name()) + item.get(DCCAttEnum.ContaCorrenteDigito.name()), MathContext.UNLIMITED), 7);
            } catch (NumberFormatException npe) {
                identificador = item.get(DCCAttEnum.IdentificadorClienteBanco.name());
            }

            detail.put(DCCAttEnum.IdentificadorClienteBanco, identificador);


            detail.put(DCCAttEnum.DataVencimento, StringUtilities.formatarCampoData(getDataVencimento(item.getMovParcela().getDataVencimento(), dataDeposito, remessa), "yyyyMMdd"));
            detail.put(DCCAttEnum.ValorVenda, StringUtilities.formatarCampoMonetario(item.getValorItemRemessa(), 15));
            detail.put(DCCAttEnum.Moeda, "03");

            //reservado para empresa = 60 caracteres
//            detail.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.formatarCampo(new BigDecimal(item.getMovParcela().getCodigo()), 7));
//            detail.put(DCCAttEnum.NomePessoa, StringUtilities.formatarCampoEmBranco(item.getMovParcela().getPessoa().getNome(), 53));
            detail.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.formatarCampoEmBranco(31));
            String cpfCnpj = item.get(DCCAttEnum.CpfOuCnpj.name());
            if(UteisValidacao.emptyString(cpfCnpj) || cpfCnpj.trim().isEmpty() || cpfCnpj.equalsIgnoreCase("null")){
                cpfCnpj = item.getPessoa().getCfp();
            }
            detail.put(DCCAttEnum.CpfCliente, StringUtilities.formatarCpfCnjp(cpfCnpj, 14));
            detail.put(DCCAttEnum.ValorBaseIOF, StringUtilities.formatarCampoMonetario(0, 15));
            detail.put(DCCAttEnum.TipoServico, "37");
            detail.put(DCCAttEnum.IOF, StringUtilities.formatarCampoMonetario(0, 7));
            detail.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(11));
            detail.put(DCCAttEnum.CodigoMovimento, 0);


            soma += item.getValorItemRemessa();
            if (item.getMovPagamento().getCodigo() != 0) {
                qtd += 1;
            }
            //
            listaDetalhe.add(detail);
        }
        remessa.setQtdAceito(qtd);

        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);
        trailer.put(DCCAttEnum.TipoRegistro, "Z");
        trailer.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampo(new BigDecimal(listaDetalhe.size() + 2), 6));
        trailer.put(DCCAttEnum.ValorTotalBruto, StringUtilities.formatarCampoMonetario(soma, 17));
        trailer.put(DCCAttEnum.QTDIOF, StringUtilities.formatarCampoMonetario(0, 17));
        trailer.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.formatarCampoEmBranco(109));

        remessa.setTrailerRemessa(trailer);
        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setHeaderRemessa(header);

        remessa.setTrailer(new StringBuilder(trailer.toString()));
        remessa.setHead(new StringBuilder(header.toString()));
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));

    }

    public static RegistroRemessa obterHeaderRetorno(final StringBuilder retorno) throws IOException {
        return LayoutRemessaFebrabanDCO.obterHeaderRetorno(retorno);
    }

    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        LayoutRemessaFebrabanDCO.lerRetorno(remessa);
    }

    public static void lerAtributos(final String linha, RegistroRemessa r) {
        if (r.getTipo() == TipoRegistroEnum.HEADER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
            r.put(DCCAttEnum.CodigoRemessa, StringUtilities.readString(1, 2, linha));
            r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(2, 13, linha));
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(13, 22, linha));
            r.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(22, 42, linha));
            r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(42, 45, linha));
            r.put(DCCAttEnum.NomeBanco, StringUtilities.readString(45, 65, linha));
            r.put(DCCAttEnum.DataGeracao, StringUtilities.readString(65, 73, linha));
            r.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.readString(73, 79, linha));
            r.put(DCCAttEnum.VersaoLayout, StringUtilities.readString(79, 81, linha));
            r.put(DCCAttEnum.IdentificacaoServico, StringUtilities.readString(81, 98, linha));
            r.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.readString(98, 150, linha));
        } else if (r.getTipo() == TipoRegistroEnum.DETALHE) {
            if (linha.length() >= 150) {
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
                r.put(DCCAttEnum.IdentificadorClienteEmpresa, StringUtilities.readString(1, 21, linha));
                r.put(DCCAttEnum.EmBranco, StringUtilities.readString(21, 26, linha));
                r.put(DCCAttEnum.AgenciaDebito, StringUtilities.readString(26, 30, linha));

                r.put(DCCAttEnum.IdentificadorClienteBanco, StringUtilities.readString(30, 44, linha));
                r.put(DCCAttEnum.DataVencimento, StringUtilities.readString(44, 52, linha));
                r.put(DCCAttEnum.ValorVenda, StringUtilities.readString(52, 67, linha));
                final String status = StringUtilities.readString(67, 69, linha);
                r.put(DCCAttEnum.StatusVenda, status);
                //Se o Débito foi efetuado, usar como Código de Autorização a Conta/Corrente do Débito realizado
                if (status != null && !status.isEmpty() && DCOHSBCStatusEnum.Status00.name().equals(status)) {
                    r.put(DCCAttEnum.CodigoAutorizacao, StringUtilities.readString(1, 26, linha));
                }
                r.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.readString(69, 100, linha));
                r.put(DCCAttEnum.CpfCliente, StringUtilities.readString(100, 114, linha));
                r.put(DCCAttEnum.ValorBaseIOF, StringUtilities.readString(114, 129, linha));
                r.put(DCCAttEnum.Moeda, StringUtilities.readString(129, 131, linha));
                r.put(DCCAttEnum.IOF, StringUtilities.readString(131, 138, linha));
                r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(138, 149, linha));
                r.put(DCCAttEnum.CodigoMovimento, StringUtilities.readString(149, 150, linha));

            }
        } else if (r.getTipo() == TipoRegistroEnum.TRAILER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
            r.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(1, 7, linha));
            r.put(DCCAttEnum.ValorTotalBruto, StringUtilities.readString(7, 24, linha));
            r.put(DCCAttEnum.QTDIOF, StringUtilities.readString(24, 41, linha));
            r.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.readString(41, 150, linha));

        }
    }
}
