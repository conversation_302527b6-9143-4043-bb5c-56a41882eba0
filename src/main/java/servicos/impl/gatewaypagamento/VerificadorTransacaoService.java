package servicos.impl.gatewaypagamento;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import negocio.interfaces.financeiro.TransacaoMovParcelaInterfaceFacade;
import servicos.SuperServico;
import servicos.impl.apf.APF;
import servicos.impl.apf.RecorrenciaService;
import servicos.impl.cieloecommerce.CieloeCommerceService;
import servicos.impl.vindi.VindiService;
import servicos.interfaces.AprovacaoServiceInterface;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Classe responsável por verificar as transações que estão aguardando processamento da vindi, para realizar seu pagamento.
 * Created by johnys on 03/03/2017.
 */
public class VerificadorTransacaoService extends SuperServico {

    private RecorrenciaService recorrenciaService;
    private TransacaoInterfaceFacade transacao;
    private TransacaoMovParcelaInterfaceFacade transacaoMovParcela;
    private Pessoa pessoa;
    private FormaPagamento formaPagamento;
    private ConvenioCobranca convenioCobrancaDAO;
    private Adquirente adquirenteDAO;
    private OperadoraCartao operadoraCartaoDAO;


    public VerificadorTransacaoService(Connection con) throws Exception {
        super(con);
        this.recorrenciaService = new RecorrenciaService(con);
        this.transacao = new Transacao(con);
        this.formaPagamento = new FormaPagamento(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.adquirenteDAO = new Adquirente(con);
        this.operadoraCartaoDAO = new OperadoraCartao(con);
        this.pessoa = new Pessoa(con);
        this.transacaoMovParcela = new TransacaoMovParcela(con);
    }

    public void processarPactoPay(Integer convenioCobranca, Integer empresa) throws Exception {
        processarTransacoesPendentesConfirmacao(TipoTransacaoEnum.PACTO_PAY, convenioCobranca, empresa);
    }

    public void processarVindi(Integer convenioCobranca, Integer empresa) throws Exception {
        processarTransacoesPendentesConfirmacao(TipoTransacaoEnum.VINDI, convenioCobranca, empresa);
        processarTransacoesCanceladasPendentesConfirmacao(convenioCobranca, empresa);
    }

    public void processarCielo(Integer convenioCobranca, Integer empresa) throws Exception {
        processarTransacoesSemSituacao(TipoTransacaoEnum.CIELO_ONLINE, convenioCobranca, empresa);
        processarTransacoesPendentesCapturaCielo(TipoTransacaoEnum.CIELO_ONLINE, convenioCobranca, empresa);
    }

    public void processarMundiPagg(Integer convenioCobranca, Integer empresa) throws Exception {
        processarTransacoesPendentesConfirmacao(TipoTransacaoEnum.MUNDIPAGG, convenioCobranca, empresa);
    }

    public void processarPagarMe(Integer convenioCobranca, Integer empresa) throws Exception {
        processarTransacoesPendentesConfirmacao(TipoTransacaoEnum.PAGAR_ME, convenioCobranca, empresa);
    }

    public void processarStripe(Integer convenioCobranca, Integer empresa) throws Exception {
        processarTransacoesPendentesConfirmacao(TipoTransacaoEnum.STRIPE, convenioCobranca, empresa);
    }

    public void processarPagoLivre(Integer convenioCobranca, Integer empresa) throws Exception {
        processarTransacoesPendentesConfirmacao(TipoTransacaoEnum.PAGOLIVRE, convenioCobranca, empresa);
    }

    public void processarFacilite(Integer convenioCobranca, Integer empresa) throws Exception {
        processarTransacoesPendentesConfirmacao(TipoTransacaoEnum.FACILITEPAY, convenioCobranca, empresa);
    }

    private void processarTransacoesPendentesConfirmacao(TipoTransacaoEnum tipoTransacaoEnum, Integer convenioCobranca, Integer empresa) throws Exception {
        List<TransacaoVO> transacoes = consultarTransacoesAprovadasAguardando(tipoTransacaoEnum, convenioCobranca, empresa);
        Uteis.logar(null, "TransacoesPendentesConfirmacao | " + tipoTransacaoEnum.getDescricao().toUpperCase() + " | Total " + transacoes.size() + " transações");
        int i = 0;
        for (TransacaoVO transacaoVO : transacoes) {
            Uteis.logar(null, "TransacoesPendentesConfirmacao | " + tipoTransacaoEnum.getDescricao().toUpperCase() + " | Atual: " + ++i + "/" + transacoes.size());
            transacaoVO.setOrigemSincronizacao("VERIFICADOR");
            verificarAlteracoesTransacao(transacaoVO, false);
        }
    }

    private void processarTransacoesCanceladasPendentesConfirmacao(Integer convenioCobranca, Integer empresa) throws Exception {
        List<TransacaoVO> transacoes = this.transacao.consultarPorTipoESituacao(TipoTransacaoEnum.VINDI, SituacaoTransacaoEnum.ESTORNADA, true, convenioCobranca, empresa, true, false, null);
        Uteis.logar(null, "Qtd TransacoesCanceladasPendentesConfirmacao VINDI: " + transacoes.size() + " transações");
        int i = 0;
        for (TransacaoVO transacao : transacoes) {
            Uteis.logar(null, "Atual: " + ++i + "/" + transacoes.size());
            processarCancelamentoVindi(transacao);
        }
    }

    private void processarCancelamentoVindi(TransacaoVO transacao) throws Exception {
        if (transacao != null && UteisValidacao.emptyNumber(transacao.getReciboPagamento())) {

            Uteis.logar(null, "Processando verificação de transação cancelada da VINDI: " + transacao.getCodigo());

            Transacao transacaoDAO = null;
            try {
                getCon().setAutoCommit(false);

                transacaoDAO = new Transacao(getCon());

                VindiService vindiService = new VindiService(getCon(), transacao.getEmpresa(), transacao.getConvenioCobrancaVO().getCodigo());
                vindiService.consultarSituacaoCobrancaTransacao(transacao);

                if (!transacao.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                    transacao.setAguardandoConfirmacao(false);
                    transacaoDAO.alterar(transacao);
                }

                transacaoDAO.excluirParcelaMultaJurosTransacao(transacao, null);

                getCon().commit();
            } catch (Exception ex) {
                getCon().rollback();
                getCon().setAutoCommit(true);
                ex.printStackTrace();
            } finally {
                getCon().setAutoCommit(true);
                transacaoDAO = null;
            }
        }
    }

    private void processarTransacoesPendentesCapturaCielo(TipoTransacaoEnum tipoTransacaoEnum, Integer convenioCobranca, Integer empresa) throws Exception {
        List<TransacaoVO> transacoes = consultarTransacoesAprovadasAguardando(tipoTransacaoEnum, convenioCobranca, empresa);
        Uteis.logar(null, "TransacoesPendentesCaptura CIELO: " + transacoes.size() + " transações");
        int i = 0;
        for (TransacaoVO transacao : transacoes) {
            Uteis.logar(null, "Atual: " + ++i + "/" + transacoes.size());
            realizarCapturaTransacao(transacao);
        }
    }

    private void processarTransacoesSemSituacao(TipoTransacaoEnum tipoTransacaoEnum, Integer convenioCobranca, Integer empresa) throws Exception {
        List<TransacaoVO> transacoes = consultarTransacoesSemSituacao(tipoTransacaoEnum, convenioCobranca, empresa);
        Uteis.logar(null, "TransacoesSemSituacao | " + tipoTransacaoEnum.getDescricao().toUpperCase() + " | Total " + transacoes.size() + " transações");
        int i = 0;
        for (TransacaoVO transacaoVO : transacoes) {
            Uteis.logar(null, "TransacoesSemSituacao | " + tipoTransacaoEnum.getDescricao().toUpperCase() + " | Atual: " + ++i + "/" + transacoes.size());
            transacaoVO.setOrigemSincronizacao("VERIFICADOR");
            verificarAlteracoesTransacao(transacaoVO, false);
        }
    }

    private void realizarCapturaTransacao(TransacaoVO transacaoVO) {
        if (transacaoVO != null && UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento())) {
            Uteis.logar(null, "Processando Realizar Captura Transacao CIELO ONLINE: " + transacaoVO.getCodigo());

            CieloeCommerceService service = null;
            Transacao transacaoDAO = null;
            try {
                transacaoDAO = new Transacao(getCon());

                TransacaoVO transacaoAnterior = (TransacaoVO) transacaoVO.getClone(true);
                service = new CieloeCommerceService(getCon(), transacaoVO.getEmpresa(), transacaoVO.getConvenioCobrancaVO().getCodigo());
                transacaoVO = service.realizarCapturaTransacao(transacaoVO);

                if (!transacaoAnterior.getSituacao().equals(transacaoVO.getSituacao())) {
                    if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                        Uteis.logar(null, "Transação CONCLUIDA_COM_SUCESSO: " + transacaoVO.getCodigo());

                        processarPagamentoTransacao(transacaoVO, transacaoVO.getTipo(), getCon());
                        transacaoDAO.alterar(transacaoVO);

                    } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
                        Uteis.logar(null, "Transação NAO_APROVADA: " + transacaoVO.getCodigo());

                        transacaoDAO.alterar(transacaoVO);

                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            } finally {
                transacaoDAO = null;
                service = null;
            }
        }
    }

    public ReciboPagamentoVO processarPagamentoTransacao(TransacaoVO transacao, TipoTransacaoEnum tipoTransacaoEnum, Connection con) throws Exception {
        Uteis.logar(null, "VerificadorTransacao Incluir Pagamento: " + transacao.getCodigo());
        List<MovParcelaVO> parcelas = this.transacao.obterParcelasDaTransacao(transacao);
        ContratoVO contrato = descobrirContratoParcelas(parcelas);
        ReciboPagamentoVO recibo = null;
        MovPagamento movPagamentoDAO = new MovPagamento(con);
        Transacao transacaoDAO = new Transacao(con);
        try {
            recibo = movPagamentoDAO.incluirListaPagamento(criarListaMovPagamento(transacao, parcelas, tipoTransacaoEnum), parcelas, null, contrato, false, 0.0, false, null);
            transacao.setReciboPagamento(recibo.getCodigo());
            transacao.setMovPagamento(recibo.getPagamentosDesteRecibo().get(0).getCodigo());

            if (UteisValidacao.emptyString(transacao.getCodigoExterno())) {
                transacao.setCodigoExterno(transacao.getValorAtributoResposta(APF.Transacao));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            transacaoDAO.alterarMessagemErro(transacao, ex.getMessage());
            throw ex;
        } finally {
            movPagamentoDAO = null;
            transacaoDAO = null;
        }
        return recibo;
    }

    private ContratoVO descobrirContratoParcelas(List<MovParcelaVO> parcelas) {
        ContratoVO contrato = null;
        if (parcelas.size() == 1) {
            contrato = parcelas.get(0).getContrato();
        } else {
            if (parcelas.size() > 1) {
                contrato = parcelas.get(0).getContrato();
                for (MovParcelaVO mov : parcelas) {
                    if (contrato != null && (mov.getContrato() == null || !contrato.getCodigo().equals(mov.getContrato().getCodigo()))) {
                        contrato = null;
                    }
                }
            }
        }
        return contrato;
    }

    private List<MovPagamentoVO> criarListaMovPagamento(TransacaoVO transacao, List<MovParcelaVO> parcelas, TipoTransacaoEnum tipoTransacaoEnum) throws Exception {
        List<MovPagamentoVO> listaPagamento = new ArrayList<MovPagamentoVO>();
        MovPagamentoVO movPagamentoVO = new MovPagamentoVO();

        if (transacao.getDataCobranca() != null) {
            movPagamentoVO.setDataCobrancaTransacao(transacao.getDataCobranca());
            movPagamentoVO.setDataPagamento(transacao.getDataCobranca());
            movPagamentoVO.setDataLancamento(transacao.getDataCobranca());
            movPagamentoVO.setDataQuitacao(transacao.getDataCobranca());
        }

        if (transacao.isPagamentoDinheiro()) {
            movPagamentoVO.setFormaPagamento(this.formaPagamento.consultarAVista());
        } else {

            boolean temFormaPagamento = false;
            List<FormaPagamentoVO> formasPgtConvenio = this.formaPagamento.consultarFormaPagamentoComConvenioCobranca(true, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            for (FormaPagamentoVO form : formasPgtConvenio) {
                if (form.getConvenioCobrancaVO() != null &&
                        !UteisValidacao.emptyNumber(form.getConvenioCobrancaVO().getCodigo()) &&
                        form.getConvenioCobrancaVO().getCodigo().equals(transacao.getConvenioCobrancaVO().getCodigo())) {
                    movPagamentoVO.setFormaPagamento(form);
                    temFormaPagamento = true;
                    break;
                }
            }
            if (!temFormaPagamento) {
                movPagamentoVO.setFormaPagamento(recorrenciaService.getFormaPagamento());
            }
        }

        try {
            movPagamentoVO.setAdquirenteVO(this.adquirenteDAO.obterAdquirenteTransacao(transacao));
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        movPagamentoVO.setMovPagamentoEscolhida(true);
        Double valor = somaValorParcelas(parcelas);
        movPagamentoVO.setValor(valor);
        movPagamentoVO.setValorTotal(valor);
        PessoaVO pessoa = verificarPessoaPagadora(transacao, parcelas);

        if (pessoa == null || UteisValidacao.emptyNumber(pessoa.getCodigo())) {
            throw new ConsistirException("Não foi possível definir a pessoa que sera utilizada para gerar o pagamento da transação.");
        }

        movPagamentoVO.setPessoa(pessoa);
        movPagamentoVO.setNomePagador(transacao.getPessoaPagador().getNome());

        if (transacao.isPagamentoDinheiro()) {
            movPagamentoVO.setOpcaoPagamentoDinheiro(true);
        } else {
            movPagamentoVO.setOpcaoPagamentoCartaoCredito(true);

            if (tipoTransacaoEnum.equals(TipoTransacaoEnum.VINDI)) {
                movPagamentoVO.setNrParcelaCartaoCredito(1);
            } else if (tipoTransacaoEnum.equals(TipoTransacaoEnum.CIELO_ONLINE)) {
                if (UteisValidacao.emptyNumber(transacao.getNrVezes())) {
                    movPagamentoVO.setNrParcelaCartaoCredito(1);
                } else {
                    movPagamentoVO.setNrParcelaCartaoCredito(transacao.getNrVezes());
                }
            } else {
                movPagamentoVO.setNrParcelaCartaoCredito(transacao.getNrVezes());
            }

            if (!UteisValidacao.emptyNumber(transacao.getConvenioCobrancaVO().getCodigo())) {
                ConvenioCobrancaVO convenioCobrancaVO = this.convenioCobrancaDAO.consultarPorChavePrimaria(transacao.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                OperadorasExternasAprovaFacilEnum operadorasExternasAprovaFacilEnum = OperadorasExternasAprovaFacilEnum.obterPorDescricao(transacao.getBandeira());
                if (operadorasExternasAprovaFacilEnum != null) {
                    movPagamentoVO.setOperadoraCartaoVO(obterOperadoraCartao(convenioCobrancaVO, transacao, transacao.getNrVezesCobranca(), operadorasExternasAprovaFacilEnum));
                }
            }

            movPagamentoVO.setNsu(transacao.getNSU());
            movPagamentoVO.setAutorizacaoCartao(transacao.getAutorizacao());
        }

        if (transacao.isUtilizarUsuarioGerarRecibo()) {
            movPagamentoVO.setResponsavelPagamento(transacao.getUsuarioResponsavel());
        } else {
            movPagamentoVO.setResponsavelPagamento(recorrenciaService.getUsuario());
        }
        movPagamentoVO.setEmpresa(transacao.getEmpresaVO());
        movPagamentoVO.setConvenio(transacao.getConvenioCobrancaVO());

        listaPagamento.add(movPagamentoVO);
        return listaPagamento;
    }

    private OperadoraCartaoVO obterOperadoraCartao(ConvenioCobrancaVO convenioCobrancaVO,  TransacaoVO transacaoVO,
                                                   Integer nrParcelas, OperadorasExternasAprovaFacilEnum operadoraEnum) {
        try {
            if (operadoraEnum == null) {
                operadoraEnum = transacaoVO.getBandeiraPagamento();
            }
            if (operadoraEnum == null) {
                throw new Exception("Bandeira não identificada.");
            }
            return this.operadoraCartaoDAO.consultarOuCriaPorCodigoIntegracao(convenioCobrancaVO.getTipo(), nrParcelas, operadoraEnum);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new OperadoraCartaoVO();
    }

    private PessoaVO verificarPessoaPagadora(TransacaoVO transacao, List<MovParcelaVO> parcelas) throws Exception {
        PessoaVO pessoa = transacao.getPessoaPagador();
        if (pessoa == null || UteisValidacao.emptyNumber(pessoa.getCodigo())) {
            pessoa = !parcelas.isEmpty() ? parcelas.get(0).getPessoa() : null;
        }
        if (pessoa != null && !UteisValidacao.emptyNumber(pessoa.getCodigo()) && UteisValidacao.emptyString(pessoa.getNome())) {
            pessoa = this.pessoa.consultarPorChavePrimaria(pessoa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        return pessoa;
    }

    private Double somaValorParcelas(List<MovParcelaVO> parcelas) {
        Double soma = 0.0;
        for (MovParcelaVO parcela : parcelas) {
            soma += parcela.getValorParcela();
        }
        return soma;
    }

    private List<TransacaoVO> consultarTransacoesAprovadasAguardando(TipoTransacaoEnum tipoTransacaoEnum, Integer convenioCobranca, Integer empresa) throws Exception {
        if (tipoTransacaoEnum == null || tipoTransacaoEnum.equals(TipoTransacaoEnum.NENHUMA)) {
            throw new Exception("Tipo não informado");
        }
        return this.transacao.consultarPorTipoESituacao(tipoTransacaoEnum, SituacaoTransacaoEnum.APROVADA, null, convenioCobranca, empresa, false, true, null);
    }

    private List<TransacaoVO> consultarTransacoesSemSituacao(TipoTransacaoEnum tipoTransacaoEnum, Integer convenioCobranca, Integer empresa) throws Exception {
        if (tipoTransacaoEnum == null || tipoTransacaoEnum.equals(TipoTransacaoEnum.NENHUMA)) {
            throw new Exception("Tipo não informado");
        }
        return this.transacao.consultarPorTipoESituacao(tipoTransacaoEnum, SituacaoTransacaoEnum.NENHUMA, null, convenioCobranca,
                empresa, false, true, Calendario.somarDias(Calendario.hoje(), -7));
    }

    public void processarWebhook(TipoTransacaoEnum tipoTransacaoEnum, String codigoExterno, Integer codigoTransacao) throws Exception {
        String log = "Webhook | " + tipoTransacaoEnum.getDescricao().toUpperCase() +
                (UteisValidacao.emptyString(codigoExterno) ? "" : (" | CodigoExterno " + codigoExterno)) +
                (UteisValidacao.emptyNumber(codigoTransacao) ? "" : (" | Codigo " + codigoTransacao));
        Uteis.logar(null, log);

        if (UteisValidacao.emptyNumber(codigoTransacao) &&
                UteisValidacao.emptyString(codigoExterno)) {
            throw new Exception("Codigo Externo ou Codigo da transação não informado.");
        }

        TransacaoVO transacaoVO = null;
        if (!UteisValidacao.emptyNumber(codigoTransacao)) {
            transacaoVO = this.transacao.consultarPorChavePrimaria(codigoTransacao, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        if (!UteisValidacao.emptyString(codigoExterno)) {
            transacaoVO = this.transacao.consultarPorCodigoExternoETipo(codigoExterno, tipoTransacaoEnum);
        }

        if (transacaoVO != null &&
                !UteisValidacao.emptyNumber(transacaoVO.getCodigo())) {
            transacaoVO.setOrigemSincronizacao("WEBHOOK");
            verificarAlteracoesTransacao(transacaoVO, true);
        } else {
            throw new Exception(log + " - Transação não encontrada!");
        }
    }

    public void verificarAlteracoesTransacao(TransacaoVO transacaoVO, boolean comExcecao) throws Exception {
        if (transacaoVO != null && !UteisValidacao.emptyNumber(transacaoVO.getCodigo())) {

            String tipo = transacaoVO.getTipo().getDescricao().toUpperCase();

            Transacao transacaoConvDAO = null;
            AprovacaoServiceInterface service = null;
            try {
                transacaoConvDAO = new Transacao(getCon());

                Uteis.logar(null, "VerificadorTransacaoService | " + tipo + " | Transação " + transacaoVO.getCodigo());

                TransacaoVO transacaoAnterior = (TransacaoVO) transacaoVO.getClone(true);

                Integer convenioCobranca = transacaoVO.getConvenioCobrancaVO().getCodigo();
                if (UteisValidacao.emptyNumber(convenioCobranca)) {
                    //obter o convênio de cobrança
                    ConvenioCobrancaVO convenioCobrancaVO = transacaoConvDAO.obterConvenioCobranca(transacaoVO);
                    if (convenioCobrancaVO == null ||
                            UteisValidacao.emptyNumber(convenioCobrancaVO.getCodigo())) {
                        throw new Exception("Convênio de Cobrança não encontrado.");
                    }
                }

                service = CobrancaOnlineService.getImplementacaoAprovacaoService(transacaoVO.getTipo(), transacaoVO.getEmpresa(),
                        convenioCobranca, transacaoVO.getTipo().equals(TipoTransacaoEnum.PACTO_PAY), getCon());

                if (service == null) {
                    throw new Exception("Não foi posssível cancelar a transação. AprovacaoService null");
                }

                transacaoVO.setVerificador(true);
                service.consultarSituacaoCobrancaTransacao(transacaoVO);

                transacaoVO.setDataAtualizacao(Calendario.hoje());
                transacaoConvDAO.alterarDataAtualizacao(transacaoVO, transacaoVO.isAtualizarParamsResposta());

                if (transacaoVO.getTipo().equals(TipoTransacaoEnum.VINDI)) {
                    String dataProximaString = transacaoVO.dataProximaTentativa();
                    if (!UteisValidacao.emptyString(dataProximaString)) {
                        Date dataProxima = Calendario.getDate("dd/MM/yyyy", dataProximaString);
                        if (dataProxima != null) {
                            transacaoVO.setProximaTentativa(Calendario.getDataComHoraZerada(dataProxima));
                            transacaoConvDAO.alterarProximaTentativa(transacaoVO);
                        }
                    }
                }

                //Transação sem situação
                if (transacaoAnterior.getSituacao().equals(SituacaoTransacaoEnum.NENHUMA) &&
                        !transacaoAnterior.getSituacao().equals(transacaoVO.getSituacao())) {
                    transacaoConvDAO.alterarSituacao(transacaoVO);
                }

                if (!transacaoAnterior.getSituacao().equals(transacaoVO.getSituacao())) {

                    Transacao transacaoDAO = null;
                    try {
                        getCon().setAutoCommit(false);
                        transacaoDAO = new Transacao(getCon());

                        if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA)) {
                            Uteis.logar(null, "Transação CANCELADA: " + transacaoVO.getCodigo());

                            transacaoDAO.validarMovimentacoesFinanceiras(transacaoVO);

                            transacaoVO.setIgnorarSituacaoCancelamento(true);
                            service.cancelarTransacao(transacaoVO, true);
                            transacaoDAO.alterar(transacaoVO);

                        } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                            Uteis.logar(null, "Transação CONCLUIDA_COM_SUCESSO: " + transacaoVO.getCodigo());

                            processarPagamentoTransacao(transacaoVO, transacaoVO.getTipo(), getCon());
                            transacaoDAO.alterar(transacaoVO);

                        } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
                            Uteis.logar(null, "Transação NAO_APROVADA: " + transacaoVO.getCodigo());

                            transacaoDAO.alterar(transacaoVO);

                        } else if (transacaoVO.getTipo().equals(TipoTransacaoEnum.PAGAR_ME) &&
                                transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO)) {
                            Uteis.logar(null, "Transação COM_ERRO: " + transacaoVO.getCodigo());
                            transacaoDAO.alterar(transacaoVO);
                        }

                        transacaoDAO.excluirParcelaMultaJurosTransacao(transacaoVO, null);

                        getCon().commit();
                    } catch (Exception ex) {
                        getCon().rollback();
                        getCon().setAutoCommit(true);
                        ex.printStackTrace();
                        if (comExcecao) {
                            throw ex;
                        }
                    } finally {
                        getCon().setAutoCommit(true);
                        transacaoDAO = null;
                    }
                } else {
                    Uteis.logar(null, "VerificadorTransacaoService | " + tipo + " | Transação " + transacaoVO.getCodigo() + " | Transação sem alteração de situação");
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                if (comExcecao) {
                    throw ex;
                }
            } finally {
                transacaoConvDAO = null;
                service = null;
            }
        }
    }

    public void cancelarTransacoesVerificacaoCartao(TipoTransacaoEnum tipoTransacaoEnum, Integer convenioCobranca, Integer empresa) throws Exception {
        List<TransacaoVO> transacoes = this.transacao.consultarTransacaoVerificacaoParaCancelar(tipoTransacaoEnum, convenioCobranca, empresa);
        Uteis.logar(null, "Qtd TransacoesVerificacaoCartaoCancelar: " + transacoes.size() + " transações");
        int i = 0;
        for (TransacaoVO transacao : transacoes) {
            Uteis.logar(null, "Atual: " + ++i + "/" + transacoes.size());
            processarCancelamentoTransacaoVerificacao(transacao);
        }
    }

    public void verificarECorrigirParcelasVinculadasEmTransacoesErradas(EmpresaVO empresaVO) {
        try {
            Uteis.logarDebug("***INÍCIO*** | VerificarPendenciasTransacaoOnline -> verificarECorrigirParcelasVinculadasEmTransacoesErradas");
            if (empresaVO == null || UteisValidacao.emptyNumber(empresaVO.getCodigo())) {
                Uteis.logarDebug("VerificarPendenciasTransacaoOnline -> verificarECorrigirParcelasVinculadasEmTransacoesErradas | Empresa não informada.");
                return;
            }
            Uteis.logarDebug("VerificarPendenciasTransacaoOnline -> verificarECorrigirParcelasVinculadasEmTransacoesErradas | " + empresaVO.getNome());
            Map<Integer, Integer> transacaoMovParcelasAExcluir = this.transacaoMovParcela.obterListaCodigosExcluirVinculadasATransacaoIncorretamente(
                    empresaVO.getCodigo());
            if (transacaoMovParcelasAExcluir != null && !transacaoMovParcelasAExcluir.isEmpty()) {
                Uteis.logarDebug("VerificarPendenciasTransacaoOnline -> verificarECorrigirParcelasVinculadasEmTransacoesErradas | TOTAL TransacoesMovParcelas EXCLUIR: " +
                        transacaoMovParcelasAExcluir.size());
                for (Integer codigoTransacaoMovParcela : transacaoMovParcelasAExcluir.keySet()) {
                    Integer codigoTransacao = transacaoMovParcelasAExcluir.get(codigoTransacaoMovParcela);
                    try {
                        if (!UteisValidacao.emptyNumber(codigoTransacao)) {
                            Uteis.logarDebug("VerificarPendenciasTransacaoOnline -> verificarECorrigirParcelasVinculadasEmTransacoesErradas | " +
                                    "EXCLUINDO TransacaoMovParcela: " + codigoTransacaoMovParcela + " da Transacao: " + codigoTransacao);
                            this.transacaoMovParcela.excluir(codigoTransacaoMovParcela);
                        }

                    } catch (Exception ex) {
                        ex.printStackTrace();
                        Uteis.logarDebug("VerificarPendenciasTransacaoOnline -> verificarECorrigirParcelasVinculadasEmTransacoesErradas | ERRO AO EXCLUIR: | " +
                                " TransacaoMovParcela: " + codigoTransacaoMovParcela + " da Transacao: " + codigoTransacao + " | " + ex.getMessage());
                    }
                }
            } else {
                Uteis.logarDebug("VerificarPendenciasTransacaoOnline -> verificarECorrigirParcelasVinculadasEmTransacoesErradas | NENHUMA TransacaoMovParcela para excluir.");
            }
            Uteis.logarDebug("***FIM*** | VerificarPendenciasTransacaoOnline -> verificarECorrigirParcelasVinculadasEmTransacoesErradas");
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("VerificarPendenciasTransacaoOnline -> verificarECorrigirParcelasVinculadasEmTransacoesErradas | ERRO: " + ex.getMessage());
            Uteis.logarDebug("***FIM*** | VerificarPendenciasTransacaoOnline -> verificarECorrigirParcelasVinculadasEmTransacoesErradas");
        }
    }

    private void processarCancelamentoTransacaoVerificacao(TransacaoVO transacaoVO) {
        Uteis.logar(null, "Processando cancelar transação de verificação: " + transacaoVO.getCodigo());

        String msgFinal = "";
        Transacao transacaoConvDAO = null;
        AprovacaoServiceInterface service = null;
        try {
            transacaoConvDAO = new Transacao(getCon());

            String tipo = transacaoVO.getTipo().getDescricao().toUpperCase();

            Uteis.logar(null, "processarCancelamentoTransacaoVerificacao | " + tipo + " | Transação " + transacaoVO.getCodigo());

            ConvenioCobrancaVO convenioCobrancaVO = transacaoVO.getConvenioCobrancaVO();
            if (convenioCobrancaVO == null || UteisValidacao.emptyNumber(convenioCobrancaVO.getCodigo())) {
                //obter o convênio de cobrança
                convenioCobrancaVO = transacaoConvDAO.obterConvenioCobranca(transacaoVO);
                if (convenioCobrancaVO == null || UteisValidacao.emptyNumber(convenioCobrancaVO.getCodigo())) {
                    throw new Exception("Convênio de Cobrança não encontrado.");
                }
            }

            service = CobrancaOnlineService.getImplementacaoAprovacaoService(transacaoVO.getTipo(), transacaoVO.getEmpresa(), convenioCobrancaVO.getCodigo(),
                    transacaoVO.getTipo().equals(TipoTransacaoEnum.PACTO_PAY), getCon());

            if (service == null) {
                throw new Exception("Não foi posssível cancelar a transação. AprovacaoService null");
            }

            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) ||
                    transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                service.cancelarTransacao(transacaoVO, false);
                if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) ||
                        transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                    msgFinal = "Transação Cancelada";
                } else {
                    throw new Exception("Não foi possível cancelar a transação de verificação do cartão.");
                }
            } else {
                throw new Exception("Não foi possível verificar o cartão. " + transacaoVO.getCodigoRetornoGestaoTransacaoMotivo());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            msgFinal = ex.getMessage();
        } finally {
            Uteis.logar(null, "processarCancelamentoTransacaoVerificacao | Transação " + transacaoVO.getCodigo() + " | " + msgFinal);
            transacaoConvDAO = null;
            service = null;
        }
    }

    public static void main(String[] args) {
        try {
            Conexao.guardarConexaoForJ2SE(new DAO().obterConexaoEspecifica("teste"));
            VerificadorTransacaoService v = new VerificadorTransacaoService(Conexao.getConexaoForJ2SE());
            v.processarVindi(null, null);
//            v.processarCielo(null, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
