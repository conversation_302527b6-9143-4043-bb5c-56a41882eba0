package servicos.impl.maxiPago;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.comuns.util.Formatador;
import controle.arquitetura.exceptions.CobrancaException;
import maxiPago.DataContract.NonTransactional.ApiResponse;
import maxiPago.DataContract.Reports.RapiResponse;
import maxiPago.DataContract.Reports.Record;
import maxiPago.DataContract.ResponseBase;
import maxiPago.DataContract.Transactional.TransactionResponse;
import maxiPago.Gateway.Api;
import maxiPago.Gateway.Report;
import maxiPago.Gateway.Transaction;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.TransacaoMaxiPagoVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.gatewaypagamento.AbstractCobrancaOnlineServiceComum;
import servicos.interfaces.MaxiPagoServiceInterface;

import java.sql.Connection;
import java.util.List;

public class MaxiPagoService extends AbstractCobrancaOnlineServiceComum implements MaxiPagoServiceInterface {

    private Api apiMaxiPago = new Api();
    private Transaction transactionMaxiPago = new Transaction();
    private Report reportMaxiPago = new Report();

    private Log logDAO;
    private Pessoa pessoaDAO;
    private Transacao transacaoDAO;
    private ConvenioCobranca convenioCobrancaDAO;
    private ConvenioCobrancaVO convenioMaxiPago;
    private String merchantId;
    private String merchantKey;

    public MaxiPagoService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        this.logDAO = new Log(con);
        this.pessoaDAO = new Pessoa(con);
        this.transacaoDAO = new Transacao(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.convenioMaxiPago = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        popularInformacoes();
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacao = null;
        Transacao transacaoDAO = null;
        try {
            transacaoDAO = new Transacao(getCon());
            transacao = criarTransacao(dadosCartao, new TransacaoMaxiPagoVO(), TipoTransacaoEnum.MAXIPAGO, this.convenioMaxiPago);
            transacao.setCodigo(0);
            transacaoDAO.incluir(transacao);

            PessoaVO pessoa = this.pessoaDAO.consultarPorChavePrimaria(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            verificarAlteracaoPessoa(pessoa);

            //realizar tentativa da cobrança
            ResponseBase responseBase = realizarVendaDireta(transacao, pessoa, dadosCartao);

            TransactionResponse retorno = (TransactionResponse) responseBase;
            processarRetorno(transacao, retorno);

            //ApiResponse apiResponse = adicionarCartaoCliente(pessoa, dadosCartao);

            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA) || transacao.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
                realizarRetentativa(3, transacao);
            }

            preencherOutrasInformacoes(transacao);
            transacaoDAO.alterar(transacao);
        } catch (
                CobrancaException ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex);
        } catch (Exception ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex.getMessage());
        } finally {
            gravarTentativaCartao(transacao);
            transacaoDAO = null;
        }
        return transacao;
    }

    private List<AutorizacaoCobrancaClienteVO> verificarAutorizacaoCobranca(PessoaVO pessoa) throws Exception{
        AutorizacaoCobrancaCliente auto = new AutorizacaoCobrancaCliente(getCon());
        return auto.consultarPorPessoaTipoConvenio(pessoa.getCodigo(), TipoConvenioCobrancaEnum.DCC_MAXIPAGO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    private void popularInformacoes() {
        this.merchantId = this.convenioMaxiPago.getCodigoAutenticacao01();
        this.merchantKey = this.convenioMaxiPago.getCodigoAutenticacao02();
        if (this.convenioMaxiPago != null) {
            //altera o ambiente da vaviável 'Environment' das classes da biblioteca da Maxipago
            if (this.convenioMaxiPago.getAmbiente().equals(AmbienteEnum.PRODUCAO)) {
                this.apiMaxiPago.setEnvironment("LIVE");
                this.transactionMaxiPago.setEnvironment("LIVE");
                this.reportMaxiPago.setEnvironment("LIVE");
            } else if (this.convenioMaxiPago.getAmbiente().equals(AmbienteEnum.HOMOLOGACAO)) {
                this.apiMaxiPago.setEnvironment("TEST");
                this.transactionMaxiPago.setEnvironment("TEST");
                this.reportMaxiPago.setEnvironment("TEST");
            }
        }
    }

    private void realizarRetentativa(int qtd, TransacaoVO transacao) throws Exception {
        if (qtd > 0) {
            consultarSituacaoCobrancaTransacao(transacao);
            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                Thread.sleep(1000);
                realizarRetentativa(qtd - 1, transacao);
            }
        }
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return this.transacaoDAO;
    }

    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        throw new Exception("Não disponibilizado para Maxi Pago.");
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        throw new Exception("Não disponibilizado para Maxi Pago.");
    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        throw new Exception("Não disponibilizado para Maxi Pago.");
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacao, Boolean estornarRecibo) throws Exception {
        if (transacao.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) || transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
            realizarCancelamentoTransacao(transacao, estornarRecibo);
        } else {
            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                consultarSituacaoCobrancaTransacao(transacao);
            }
        }
        return transacao;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacao) throws Exception {
        ResponseBase responseBase = reportMaxiPago.GetTransactionDetailReport(this.merchantId, this.merchantKey, transacao.getCodigoExterno());
        RapiResponse retorno = (RapiResponse) responseBase;
        processarRetornoConsultaSituacao(transacao, retorno);
    }

    private void realizarCancelamentoTransacao(TransacaoVO transacao, Boolean estornarRecibo) throws Exception {
        ResponseBase responseBase = transactionMaxiPago.Void(this.merchantId, this.merchantKey, transacao.getCodigoExterno(), "");
        TransactionResponse retorno = (TransactionResponse) responseBase;
        processarRetornoCancelamento(transacao, retorno, true);
        if (transacao.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) && estornarRecibo && transacao.getReciboPagamento() != 0) {
            estornarRecibo(transacao, estornarRecibo);
            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            }
        }
        new Transacao(getCon()).alterar(transacao);
    }

    private void processarRetornoCancelamento(TransacaoVO transacao, TransactionResponse retorno, Boolean realizarConsultaCasoErro) throws Exception {
        try {
            if (retorno.getResponseCode().equals(MaxiPagoRespostaTransacaoEnum.APROVADA.getId())) {
                JSONObject jsonObject = new JSONObject();
                try {
                    MaxiPagoParamsRetornoTO maxiPagoParamsRetornoTO = new MaxiPagoParamsRetornoTO(retorno);
                    jsonObject = maxiPagoParamsRetornoTO.toJSON();
                } catch (Exception ignored) {
                }
                transacao.setResultadoCancelamento(jsonObject.toString());
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                transacao.setAguardandoConfirmacao(false);
            } else {
                throw new Exception("Erro ao cancelar");
            }
        } catch (Exception e) {
            if (realizarConsultaCasoErro) {
                consultarSituacaoCobrancaTransacao(transacao);
            }
        }
    }

    private void processarRetornoConsultaSituacao(TransacaoVO transacao, RapiResponse retorno) {
        if (retorno.getResult().getRecords() != null) {
            Record record = retorno.getResult().getRecords().getRecord().get(0);



            transacao.setSituacao(MaxiPagoStatusTransacaoEnum.retornaSituacaoTransacaoPeloStatus(record.getTransactionState()));
            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA)) {
                transacao.setAguardandoConfirmacao(false);
            }
        }
    }

    private void processarRetorno(TransacaoVO transacao, TransactionResponse retorno) {
        JSONObject jsonObject = new JSONObject();
        try {
            MaxiPagoParamsRetornoTO maxiPagoParamsRetornoTO = new MaxiPagoParamsRetornoTO(retorno);
            jsonObject = maxiPagoParamsRetornoTO.toJSON();
        } catch (Exception ignored) {
        }
        transacao.setParamsResposta(jsonObject.toString());
        transacao.setCodigoExterno(retorno.getTransactionID());
        transacao.setOrderid(retorno.getOrderID());
        transacao.setCodigoAutorizacao(retorno.getAuthCode());
        transacao.setSituacao(MaxiPagoRespostaTransacaoEnum.retornaSituacaoTransacaoPeloStatus(retorno.getResponseCode()));
    }

    private void verificarAlteracaoPessoa(PessoaVO pessoa) throws Exception {
        if (dadosDesatualizados(pessoa)) {
            incluirPessoa(pessoa);
        }
    }

    private Boolean dadosDesatualizados(PessoaVO pessoa) throws Exception {
        Boolean dadosDesatualizados = UteisValidacao.emptyString(pessoa.getIdMaxiPago());
        if (!dadosDesatualizados) {
            LogVO ultimoLog = this.logDAO.consultarUltimoLogPessoa(pessoa.getCodigo(),"CLIENTE","PESSOA", "CLIENTE - EMPRESA");
            dadosDesatualizados = ultimoLog == null || pessoa.getDataAlteracaoMaxiPago().getTime() < ultimoLog.getDataAlteracao().getTime();
        }
        return dadosDesatualizados;
    }

    @Override
    public void incluirPessoa(PessoaVO pessoa) throws Exception {

        if (!SuperVO.verificaCPF(pessoa.getCfp())) {
            throw new Exception("O CPF cadastrado para essa pessoa é inválido");
        }

        ApiResponse resposta = new ApiResponse();
        if (UteisValidacao.emptyString(pessoa.getIdMaxiPago())) {
            resposta = adicionarCliente(pessoa);
        } else {
            resposta = atualizarCliente(pessoa);
        }

        if (resposta != null && resposta.getErrorMessage().contains("consumer already exists in database with this consumerId")) {
            resposta = atualizarCliente(pessoa);
        }

        if (resposta != null && resposta.getErrorMessage().contains("empty element detected in setPsInParams for field=vertical_id")) {
            throw new ConsistirException("Erro no MerchantKey, verifique se está usando o ambiente correto (Homologação/Produção) de acordo com as credenciais Maxipago.");
        }

        if (resposta != null && resposta.getErrorMessage().contains("Unable to authenticate merchant")) {
            throw new ConsistirException("MerchantId ou MerchantKey não existe ou está incorreto");
        }

        if (resposta != null && resposta.getErrorMessage().contains("empty element detected in setPsInParams for field=merchant_id")) {
            throw new ConsistirException("URL da requisição está incorreta ou Erro no MerchantId.");
        }

        if (resposta != null && resposta.getErrorMessage().contains("The content of element 'verification' is not complete.")) {
            throw new ConsistirException("O elemento merchantKey não foi enviado");
        }

        if (resposta != null && !resposta.getErrorCode().equals("0")) {
            throw new ConsistirException("Falha ao inserir a pessoa na MaxiPago. " + resposta.getErrorMessage());
        } else if (resposta != null && !UteisValidacao.emptyString(resposta.getResult().getCustomerId())) {
            pessoa.setIdMaxiPago(resposta.getResult().getCustomerId());
            new Pessoa(getCon()).alterarIdMaxiPago(pessoa);
        }

        if (UteisValidacao.emptyString(pessoa.getIdMaxiPago())) {
            throw new ConsistirException("Falha ao inserir a pessoa na MaxiPago.");
        }
    }

    private String obterNumeroCartao(CartaoCreditoTO dadosCartao) {
        String numeroCartao = "";
        if (!UteisValidacao.emptyString(dadosCartao.getNumero())) {
            numeroCartao = dadosCartao.getNumero();
        }
        return numeroCartao;
    }

    private ResponseBase realizarVendaDireta(TransacaoVO transacaoVO, PessoaVO pessoaVO, CartaoCreditoTO dadosCartao) throws Exception {

        String numeroCartao = obterNumeroCartao(dadosCartao);
        String referenceNum = "TRANSACAO" + transacaoVO.getCodigo().toString();
        String processorID = transacaoVO.getConvenioCobrancaVO().getAdquirenteMaxiPago().getId().toString();

        transacaoVO.setParamsEnvio(montarParamsEnvio(pessoaVO, dadosCartao, processorID, numeroCartao, referenceNum).toString());

        return transactionMaxiPago.Sale(
                this.merchantId,// String merchantId,
                this.merchantKey,// String merchantKey,
                referenceNum,// String referenceNum,
                dadosCartao.getValor(),// double chargeTotal,
                numeroCartao,// String creditCardNumber,
                String.valueOf(dadosCartao.getMesValidade()),// String expMonth,
                String.valueOf(dadosCartao.getAnoValidade()),// String expYear,
                null,// String cvvInd,
                dadosCartao.getCodigoSeguranca(),// String cvvNumber,
                null,// String authentication,
                processorID,// String processorId,
                dadosCartao.getParcelas().toString(),// String numberOfInstallments,
                "N",// String chargeInterest,
                null,// String ipAddress,
                Uteis.removerMascara(pessoaVO.getCfp()),// String customerIdExt,
                "BRL",// String currencyCode,
                "Y",// String fraudCheck,
                "",// String softDescriptor,
                null);// Double iataFee

    }

    private JSONObject montarParamsEnvio(PessoaVO pessoaVO, CartaoCreditoTO dadosCartao, String processorID,  String numeroCartao, String referenceNum){

        //montar objeto json manualmente para setar nos parâmetros de envio.
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("referenceNum", referenceNum);
        jsonObject.put("chargeTotal", dadosCartao.getValor());
        jsonObject.put("creditCardNumber", APF.getCartaoMascarado(numeroCartao));
        jsonObject.put("expMonth", dadosCartao.getMesValidade());
        jsonObject.put("expYear", dadosCartao.getAnoValidade());
        jsonObject.put("cvvInd", "Null");
        jsonObject.put("cvvNumber", "***");
        jsonObject.put("band", dadosCartao.getBand().getDescricao());
        jsonObject.put("authentication", "Null");
        jsonObject.put("processorID", processorID);
        jsonObject.put("numberOfInstallments", dadosCartao.getParcelas().toString());
        jsonObject.put("chargeInterest", "N");
        jsonObject.put("ipAddress", "Null");
        jsonObject.put("customerIdExt", pessoaVO.getCfp());
        jsonObject.put("currencyCode", "BRL");
        jsonObject.put("fraudCheck", "Y");
        jsonObject.put("iataFee", "Null");

        return jsonObject;
    }

    private ApiResponse atualizarCliente(PessoaVO pessoaVO) throws Exception {
        try {
            String idMaxiPago = pessoaVO.getIdMaxiPago();
            String cpfCliente = Uteis.removerMascara(pessoaVO.getCfp());
            String primeiroNomeCliente = Uteis.getPrimeiroNome(Uteis.retirarAcentuacao(pessoaVO.getNome()));
            String sobrenomeNomeCliente = Uteis.getSobrenome(Uteis.retirarAcentuacao(pessoaVO.getNome()));
            String enderecoCliente = obterEnderecoPessoa(pessoaVO).getEndereco();
            String complementoEndCliente = obterEnderecoPessoa(pessoaVO).getComplemento();
            String cidadeCliente = pessoaVO.getCidade().getNome();
            String ufCliente = pessoaVO.getEstadoVO().getSigla();
            String cepCliente = Uteis.removerMascara(obterEnderecoPessoa(pessoaVO).getCep());
            String telefoneCliente = Uteis.removerMascara(obterTelefonePessoa(pessoaVO).getNumero());
            String emailCliente = pessoaVO.getEmailCorrespondencia();
            String dataNascimentoCliente = Formatador.formatarData(pessoaVO.getDataNasc(), "MM/dd/yyyy");
            String sexoCliente = pessoaVO.getSexo();

            return apiMaxiPago.UpdateConsumer(this.merchantId, this.merchantKey, idMaxiPago, cpfCliente, primeiroNomeCliente, sobrenomeNomeCliente,
                    enderecoCliente, complementoEndCliente, cidadeCliente, ufCliente, cepCliente, telefoneCliente, emailCliente,
                    dataNascimentoCliente, cpfCliente, sexoCliente);

        } catch (Exception e) {
            return null;
        }
    }

    private ApiResponse adicionarCliente(PessoaVO pessoaVO) throws Exception {
        try {

            String cpfCliente = Uteis.removerMascara(pessoaVO.getCfp());
            String primeiroNomeCliente = Uteis.getPrimeiroNome(Uteis.retirarAcentuacao(pessoaVO.getNome()));
            String sobrenomeNomeCliente = Uteis.getSobrenome(Uteis.retirarAcentuacao(pessoaVO.getNome()));
            String enderecoCliente = obterEnderecoPessoa(pessoaVO).getEndereco();
            String complementoEndCliente = obterEnderecoPessoa(pessoaVO).getComplemento();
            String cidadeCliente = pessoaVO.getCidade().getNome();
            String ufCliente = pessoaVO.getEstadoVO().getSigla();
            String cepCliente = Uteis.removerMascara(obterEnderecoPessoa(pessoaVO).getCep());
            String telefoneCliente = Uteis.removerMascara(obterTelefonePessoa(pessoaVO).getNumero());
            String emailCliente = pessoaVO.getEmailCorrespondencia();
            String dataNascimentoCliente = Formatador.formatarData(pessoaVO.getDataNasc(), "MM/dd/yyyy");
            String sexoCliente = pessoaVO.getSexo();

            return apiMaxiPago.AddConsumer(this.merchantId, this.merchantKey, cpfCliente, primeiroNomeCliente, sobrenomeNomeCliente, enderecoCliente, complementoEndCliente,
                    cidadeCliente, ufCliente, cepCliente, telefoneCliente, emailCliente, dataNascimentoCliente, cpfCliente, sexoCliente);

        } catch (Exception e) {
            return null;
        }
    }

    private ApiResponse adicionarCartaoCliente(PessoaVO pessoaVO, CartaoCreditoTO dadosCartao) throws Exception {

        String numeroCartao = obterNumeroCartao(dadosCartao);
        String enderecoCliente = obterEnderecoPessoa(pessoaVO).getEndereco();
        String complementoEndCliente = obterEnderecoPessoa(pessoaVO).getComplemento();
        String cidadeCliente = pessoaVO.getCidade().getNome();
        String ufCliente = pessoaVO.getEstadoVO().getSigla();
        String cepCliente = Uteis.removerMascara(obterEnderecoPessoa(pessoaVO).getCep());
        String telefoneCliente = Uteis.removerMascara(obterTelefonePessoa(pessoaVO).getNumero());
        String emailCliente = pessoaVO.getEmailCorrespondencia();
        String comentariosAdicionais = null;
        String valorMaximoPermitidoCartao = null;
        String limite = "ongoing"; //DURACAO_LIMITE_DO_USO_CARTAO *ONGOING = INDEFINIDAMENTE USE_ONCE = APENAS UAM VEZ APOS 1 COBRANCA

        return apiMaxiPago.AddCardOnFile(this.merchantId, this.merchantKey, pessoaVO.getIdMaxiPago(), numeroCartao, String.valueOf(dadosCartao.getMesValidade()),
                String.valueOf(dadosCartao.getAnoValidade()), dadosCartao.getNomeTitular(), enderecoCliente, complementoEndCliente, cidadeCliente, ufCliente,
                cepCliente, "BR", telefoneCliente, emailCliente, null, limite, comentariosAdicionais, valorMaximoPermitidoCartao);
    }

    private EnderecoVO obterEnderecoPessoa(PessoaVO pessoaVO) {
        if (!UteisValidacao.emptyList(pessoaVO.getEnderecoVOs())) {
            return pessoaVO.getEnderecoVOs().get(0);
        } else {
            return new EnderecoVO();
        }
    }

    private TelefoneVO obterTelefonePessoa(PessoaVO pessoaVO) {
        if (!UteisValidacao.emptyList(pessoaVO.getTelefoneVOs())) {
            return pessoaVO.getTelefoneVOs().get(0);
        } else {
            return new TelefoneVO();
        }
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantKey() {
        return merchantKey;
    }

    public void setMerchantKey(String merchantKey) {
        this.merchantKey = merchantKey;
    }

    public ConvenioCobrancaVO getConvenioMaxiPago() {
        return convenioMaxiPago;
    }

    public void setConvenioMaxiPago(ConvenioCobrancaVO convenioMaxiPago) {
        this.convenioMaxiPago = convenioMaxiPago;
    }
}
