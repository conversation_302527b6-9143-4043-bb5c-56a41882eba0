package servicos.impl.gestaoaula;

import br.com.pacto.priv.sms.SMSControle;
import br.com.pacto.priv.sms.beans.BeanSMSExternal;
import br.com.pacto.priv.utils.Tricks;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import controle.arquitetura.SuperControle;
import controle.plano.ReposicaoControle;
import negocio.comuns.arquitetura.*;
import negocio.comuns.basico.AulaDesmarcadaVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.contrato.ContratoModalidadeHorarioTurmaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.plano.AlunoHorarioTurmaVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.*;
import negocio.facade.jdbc.basico.AulaDesmarcada;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Reposicao;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoModalidadeHorarioTurma;
import negocio.facade.jdbc.crm.ConfiguracaoSistemaCRM;
import negocio.facade.jdbc.plano.HorarioTurma;
import negocio.interfaces.contrato.ContratoInterfaceFacade;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import negocio.comuns.contrato.ControleCreditoTreinoVO;
import servicos.integracao.sms.Message;
import servicos.integracao.sms.SmsController;

/**
 * Created by ulisses on 02/12/2015.
 */
public class GestaoAulaService extends SuperEntidade {

    private UteisEmail uteisEmail;
    private ConfiguracaoSistemaCRMVO configCRM;
    private Reposicao reposicao;
    private HorarioTurma horarioTurma;
    private Empresa empresa;
    private AulaDesmarcada aulaDesmarcada;
    private ConfiguracaoSistemaCRM configuracaoSistemaCRM;
    private SituacaoClienteSinteticoDW situacaoClienteSinteticoDW;
    private ContratoModalidadeHorarioTurma contratoModalidadeHorarioTurma;
    private Cliente cliente;
    private String key;
    private Log log;
    private ContratoInterfaceFacade contratoDao;


    public GestaoAulaService() throws Exception {
        super();
    }

    public GestaoAulaService(Connection conexao, String key) throws Exception {
        super(conexao);
        this.key = key;
        reposicao = new Reposicao(conexao);
        aulaDesmarcada = new AulaDesmarcada(conexao);
        empresa = new Empresa(conexao);
        uteisEmail = new UteisEmail();
        configuracaoSistemaCRM = new ConfiguracaoSistemaCRM(conexao);
        this.situacaoClienteSinteticoDW = new SituacaoClienteSinteticoDW(con);
        this.horarioTurma = new HorarioTurma(con);
        configCRM = SuperControle.getConfiguracaoSMTPNoReply();
        this.contratoModalidadeHorarioTurma = new ContratoModalidadeHorarioTurma(con);
        this.cliente = new Cliente(con);
        uteisEmail.novo("", configCRM);
        log = new Log(con);
    }

    public void marcarAula(ReposicaoVO reposicaoVO) throws Exception {
        marcarAula(reposicaoVO, false);
    }
    public void marcarAula(ReposicaoVO reposicaoVO, boolean diaria) throws Exception {
        this.con.setAutoCommit(false);
        try {
            if (reposicaoVO.getContrato() != null && !(validarSaldoCreditoTreino(reposicaoVO.getContrato()))) {
                throw new ConsistirException("Operação não permitida. Saldo de créditos insuficiente.");
            }
            obterQuantidadeAlunosMatriculados(reposicaoVO);
            reposicao.incluirSemCommit(reposicaoVO, null, diaria);
            this.con.commit();

            this.situacaoClienteSinteticoDW.atualizarBaseOffLineZillyonAcesso(this.key, reposicaoVO.getCliente().getPessoa().getCodigo());
        } catch (Exception e) {
            this.con.rollback();
            throw e;
        } finally {
            this.con.setAutoCommit(true);
        }
    }

    private void obterQuantidadeAlunosMatriculados(ReposicaoVO reposicaoVO) throws Exception {
        //validar o limite de minutos de antecedência para marcar aula.
        boolean temPermissao = reposicaoVO.getOrigemSistemaEnum().equals(OrigemSistemaEnum.APP_TREINO) ? false : validarPermissaoMarcarAulaRetroativa(reposicaoVO);
        reposicaoVO.setPermitirLancarAulaRetroativa(temPermissao);
        if (!temPermissao) {
            TimeZone tz = TimeZone.getTimeZone(empresa.obterTimeZoneDefault(reposicaoVO.getTurmaDestino().getEmpresa().getCodigo()));
            Date agora = Calendario.hojeCalendar(tz).getTime();

            ReposicaoVO.validarMinutosAntecedenciaMarcarAula(reposicaoVO.getTurmaDestino(), reposicaoVO.getHorarioTurma(), reposicaoVO.getDataReposicao(),agora);
        }
        Set<Integer> codClientes = new HashSet<>();
        Set<Integer> codClientesDesmarcados = new HashSet<>();
        getHorarioTurma().nrAlunosMatriculadosRenovacaoRematricula(reposicaoVO.getHorarioTurma(), reposicaoVO.getDataReposicao(), codClientes);
        getAulaDesmarcada().consultarTotalAulasDesmarcadas(reposicaoVO.getTurmaDestino().getEmpresa().getCodigo(), reposicaoVO.getHorarioTurma().getCodigo(), reposicaoVO.getDataReposicao(), codClientesDesmarcados);
        getReposicao().consultarTotalReposicao(reposicaoVO.getHorarioTurma().getCodigo(), reposicaoVO.getDataReposicao(), codClientes);
        for(Integer desmarcados : codClientesDesmarcados){
            codClientes.remove(desmarcados);
        }
        reposicaoVO.getHorarioTurma().setNrAlunoMatriculado(codClientes.size());
    }

    private boolean validarPermissaoMarcarAulaRetroativa(ReposicaoVO reposicaoVO){
        boolean retorno = false;
        try {
            Usuario usuario = new Usuario(con);
            UsuarioVO usuarioValidado = usuario.consultarPorCodigo(reposicaoVO.getUsuario().getCodigo(), Uteis.NIVELMONTARDADOS_LOGIN);
            ControleAcesso controleAcesso = new ControleAcesso(con);
            Permissao permissao = new Permissao(con);
            Iterator i = usuarioValidado.getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {                
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(permissao.consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                controleAcesso.verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(), usuarioValidado, "PermiteMarcarAlunoForaTolerancia", "2.70 - Permite marcar e repor aula no passado");
            }
            retorno = true;
        } catch (Exception ex) {
            
        }
        
        return retorno;
    }
    
    private boolean validarSaldoCreditoTreino(ContratoVO contratoVO)throws Exception{
        if (contratoVO.isVendaCreditoTreino() && !contratoVO.getVigenciaDe().after(Calendario.hoje())){
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(" select quantidadecreditodisponivel from contratoduracaocreditotreino cdc \n"
                    + " INNER JOIN contratoduracao cd ON cd.codigo = cdc.contratoduracao \n"
                    + " where cd.contrato = " + contratoVO.getCodigo(), con)) {
                if (rs.next()) {
                    return rs.getInt("quantidadecreditodisponivel") > 0;
                }
            }
            SituacaoClienteSinteticoDWVO situacaoClienteSinteticoDWVO =  situacaoClienteSinteticoDW.consultarCliente(contratoVO.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            return situacaoClienteSinteticoDWVO.getSaldoCreditoTreino() > 0;
        }
        if (contratoVO.getVigenciaDe().after(Calendario.hoje()) && situacaoClienteSinteticoDW.qtdCreditoContratoFuturo(contratoVO.getCodigo()) == 0) {
            Integer qtdCreditos = situacaoClienteSinteticoDW.qtdCreditoCompra(contratoVO.getCodigo());
            throw new Exception(String.format("Número de aulas não pode exceder a quantidade de créditos. Aluno já tem %d aula(s) marcada(s) e %d créditos(s).", qtdCreditos, qtdCreditos));
        }
        return true;
    }

    public void desmarcarAulasPorAfastamento(UsuarioVO usuarioVO, OrigemSistemaEnum origemSistemaEnum, ContratoVO contratoVO, Date inicioAfastamento, Date fimAfastamnto)throws Exception{
        ClienteVO clienteVO = cliente.consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        List<ContratoModalidadeHorarioTurmaVO> listaHorarioTurma = contratoModalidadeHorarioTurma.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        List<Date> listaDias = Uteis.getDiasEntreDatas(inicioAfastamento, fimAfastamnto);
        Date dataLancamento = Calendario.hoje();
        for (Date dia: listaDias){
            Calendar diaVerificar = Calendario.getInstance();
            diaVerificar.setTime(dia);
            if (listaHorarioTurma.size() > 0){
                // CONTRATOS HORÁRIO TURMA.
                for (ContratoModalidadeHorarioTurmaVO horarioTurma: listaHorarioTurma){
                    if (horarioTurma.getHorarioTurma().getDiaSemanaNumero() == diaVerificar.get(Calendar.DAY_OF_WEEK)){
                        TurmaVO turmaVO = new TurmaVO();
                        turmaVO.setCodigo(horarioTurma.getHorarioTurma().getTurma());

                        ReposicaoVO reposicaoVO = reposicao.consultarReposicao(contratoVO.getCodigo(),horarioTurma.getHorarioTurma().getCodigo(), turmaVO.getCodigo(), dia,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if (UtilReflection.objetoMaiorQueZero(reposicaoVO, "getCodigo()")){
                            // Já foi realizada uma reposição para o dia.
                            AulaDesmarcadaVO aulaDesmarcadaVO = aulaDesmarcada.consultarPorReposicao(reposicaoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            reposicao.excluirSemValidarPermissao(reposicaoVO);
                            if (UtilReflection.objetoMaiorQueZero(aulaDesmarcadaVO, "getCodigo()")){
                                aulaDesmarcadaVO.setReposicaoVO(null);
                                aulaDesmarcadaVO.setDataReposicao(null);
                                aulaDesmarcada.alterar(aulaDesmarcadaVO);
                            }
                            continue;
                        }
                        AulaDesmarcadaVO aulaExistente = aulaDesmarcada.consultarAulaDesmarcadaPorDiaHorarioContrato(clienteVO.getCodigo(), clienteVO.getEmpresa().getCodigo(),
                                contratoVO.getCodigo(), horarioTurma.getHorarioTurma().getTurma(), horarioTurma.getHorarioTurma().getCodigo(),
                                diaVerificar.getTime(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA,false, false);

                        if (UtilReflection.objetoMaiorQueZero(aulaExistente, "getCodigo()")){
                            // A aula já foi desmarcada.
                            continue;
                        }
                        // se a aula não está desmarcada, então desmarcar.
                        AulaDesmarcadaVO aulaDesmarcadaVO = new AulaDesmarcadaVO();
                        aulaDesmarcadaVO.setEmpresaVO(clienteVO.getEmpresa());
                        aulaDesmarcadaVO.setClienteVO(clienteVO);
                        aulaDesmarcadaVO.setContratoVO(contratoVO);
                        aulaDesmarcadaVO.setTurmaVO(turmaVO);
                        aulaDesmarcadaVO.setHorarioTurmaVO(horarioTurma.getHorarioTurma());
                        aulaDesmarcadaVO.setDataLancamento(dataLancamento);
                        aulaDesmarcadaVO.setDataOrigem(dia);
                        aulaDesmarcadaVO.setUsuarioVO(usuarioVO);
                        aulaDesmarcadaVO.setOrigemSistemaEnum(origemSistemaEnum);
                        aulaDesmarcadaVO.setDesmarcadaPorAfastamento(true);
                        aulaDesmarcadaVO.setPermiteReporAulaDesmarcada(contratoVO.isVendaCreditoTreino());
                        aulaDesmarcada.incluirSemCommit(aulaDesmarcadaVO);
                    }
                }
                List<ReposicaoVO> listaReposicao = reposicao.consultar(diaVerificar.getTime(), null, clienteVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_WS);
                for (ReposicaoVO reposicaoVO: listaReposicao){
                    reposicao.excluirSemValidarPermissao(reposicaoVO);
                }
            }else{
                // CONTRATOS HORÁRIO LIVRE.
                List<ReposicaoVO> listaReposicao = reposicao.consultar(diaVerificar.getTime(), null, clienteVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_WS);
                for (ReposicaoVO reposicaoVO: listaReposicao){
                    reposicao.excluirSemValidarPermissao(reposicaoVO);
                }
            }
        }
    }

    public ContratoInterfaceFacade getContratoDao() throws Exception {
        if (contratoDao == null) {
            contratoDao = new Contrato(getCon());
        }
        contratoDao.setCon(getCon());
        return contratoDao;
    }

    public void reporAula(ReposicaoVO reposicaoVO, boolean enviarEmailESMS, ReposicaoVO reposicaoOrigem, Integer codEmpresa)throws Exception{
        this.con.setAutoCommit(false);
        try {

            // Verifica se a reposição já foi utilizada para a aula desmarcada
            if (aulaDesmarcada.existeAulaParaReposicao(reposicaoVO)) {
                throw new Exception("O aluno já está matriculado nessa aula!");
            }

            ContratoVO contratoOrigem = getContratoDao().consultarContratoVigentePorPessoaEData(reposicaoVO.getCliente().getPessoa().getCodigo(), reposicaoVO.getDataOrigem(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            Integer codContratoOrigem = contratoOrigem == null ? null : contratoOrigem.getCodigo();

            // verifica se a aula da reposição já foi desmarcada.
            AulaDesmarcadaVO aulaDesmarcadaVO = aulaDesmarcada.consultarAulaDesmarcadaPorDiaHorarioContratoOrigemEReposicao(
                    reposicaoVO.getCliente().getCodigo(),
                    codEmpresa != null ? codEmpresa : reposicaoVO.getContrato().getEmpresa().getCodigo(),
                    reposicaoVO.getContrato().getCodigo(),
                    reposicaoVO.getContrato() != null && reposicaoVO.getContrato().getCodigo() != null ? reposicaoVO.getContrato().getCodigo() : codContratoOrigem,
                    reposicaoVO.getTurmaOrigem().getCodigo(),
                    reposicaoVO.getHorarioTurmaOrigem().getCodigo(),
                    reposicaoVO.getDataOrigem(),
                    Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (!UtilReflection.objetoMaiorQueZero(aulaDesmarcadaVO, "getCodigo()" )){
                if (Calendario.menor(reposicaoVO.getDataOrigem(), Calendario.hoje()) && UteisValidacao.notEmptyNumber(reposicaoVO.getCliente().getEmpresa().getTempoAposFaltaReposicao())) {
                    if (!(Calendario.entre(reposicaoVO.getDataOrigem(), Uteis.obterDataAnterior(Calendario.hoje(), reposicaoVO.getCliente().getEmpresa().getTempoAposFaltaReposicao()), Calendario.hoje()))) {
                        throw new ConsistirException("Não é possível repôr para esta data. A falta do aluno foi há mais de " + reposicaoVO.getCliente().getEmpresa().getTempoAposFaltaReposicao() + " dias.");
                    }
                }
                //validar o limite de minutos de antecedência para desmarcar aula.
                if (reposicaoVO.getDataReposicao() == null || reposicaoVO.getDataReposicao().equals(reposicaoVO.getDataOrigem())) {
                    validarMinutosAntecedenciaDesmarcarAula(
                            reposicaoVO.getTurmaOrigem(),
                            reposicaoVO.getHorarioTurmaOrigem(),
                            reposicaoVO.getDataOrigem()
                    );
                }
               aulaDesmarcadaVO = new AulaDesmarcadaVO();
               aulaDesmarcadaVO.setClienteVO(reposicaoVO.getCliente());
               aulaDesmarcadaVO.setDataOrigem(reposicaoVO.getDataOrigem());
               aulaDesmarcadaVO.setHorarioTurmaVO(reposicaoVO.getHorarioTurmaOrigem());
               aulaDesmarcadaVO.setContratoVO(reposicaoVO.getContrato());
               aulaDesmarcadaVO.setDataLancamento(reposicaoVO.getDataLancamento());
               aulaDesmarcadaVO.setUsuarioVO(reposicaoVO.getUsuario());
               aulaDesmarcadaVO.setOrigemSistemaEnum(reposicaoVO.getOrigemSistemaEnum());
               aulaDesmarcadaVO.setEmpresaVO(reposicaoVO.getCliente().getEmpresa());
               aulaDesmarcadaVO.setTurmaVO(reposicaoVO.getTurmaOrigem());
               aulaDesmarcada.incluirSemCommit(aulaDesmarcadaVO);
            }
            

            //validar o limite de minutos de antecedência para marcar aula.
            obterQuantidadeAlunosMatriculados(reposicaoVO);

            int aulasDesmarcadas = getAulaDesmarcada().nrAlunosReposicao(reposicaoVO.getHorarioTurma(), Calendario.periodoSQL(reposicaoVO.getDataReposicao(), reposicaoVO.getDataReposicao()));
            reposicaoVO.getHorarioTurma().setNrAlunoSairamPorReposicao(aulasDesmarcadas);
            reposicao.incluirSemCommit(reposicaoVO, reposicaoOrigem);
            atualizarAulaDesmarcada(reposicaoVO, aulaDesmarcadaVO);
            this.con.commit();
            if (enviarEmailESMS) {
                enviarEmailESMS(reposicaoVO);
            }
            if ((Calendario.igual(Calendario.getDataComHoraZerada(reposicaoVO.getDataReposicao()), Calendario.getDataComHoraZerada(Calendario.hoje()))) ||
                   (Calendario.igual(Calendario.getDataComHoraZerada(reposicaoVO.getDataOrigem()), Calendario.getDataComHoraZerada(Calendario.hoje()))) ){
                this.situacaoClienteSinteticoDW.atualizarBaseOffLineZillyonAcesso(this.key, reposicaoVO.getCliente().getPessoa().getCodigo());
            }

        }catch (Exception e){
            this.con.rollback();
            throw e;
        }finally {
            this.con.setAutoCommit(true);
        }
    }

    public void desmarcarAula(AulaDesmarcadaVO aulaDesmarcadaVO, boolean ignorarAntecedencia) throws Exception {
        this.con.setAutoCommit(false);
        try {
            aulaDesmarcadaVO.validarDados(aulaDesmarcadaVO);
            aulaDesmarcada.validarConsistenciaDesmarcacao(aulaDesmarcadaVO);
            reposicao.verificarReposicaoJaLancada(aulaDesmarcadaVO.getContratoVO().getCodigo(), aulaDesmarcadaVO.getHorarioTurmaVO().getCodigo(), aulaDesmarcadaVO.getTurmaVO().getCodigo(), aulaDesmarcadaVO.getDataOrigem());
            AulaDesmarcadaVO aulaExistente = aulaDesmarcada.consultarAulaDesmarcadaPorDiaHorarioContrato(aulaDesmarcadaVO.getClienteVO().getCodigo(), aulaDesmarcadaVO.getEmpresaVO().getCodigo(),
                    aulaDesmarcadaVO.getContratoVO().getCodigo(), aulaDesmarcadaVO.getTurmaVO().getCodigo(), aulaDesmarcadaVO.getHorarioTurmaVO().getCodigo(),
                    aulaDesmarcadaVO.getDataOrigem(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA,true,true);
            if (!UteisValidacao.emptyNumber(aulaExistente.getCodigo())) {
                throw new ConsistirException("Existe uma Aula Desmarcada desta mesma data e horário, lançada no dia: " + Uteis.getDataComHHMM(aulaExistente.getDataLancamento()));
            }
            if (!ignorarAntecedencia) {
                //validar o limite de minutos de antecedência para desmarcar aula.
                validarMinutosAntecedenciaDesmarcarAula(aulaDesmarcadaVO.getTurmaVO(), aulaDesmarcadaVO.getHorarioTurmaVO(), aulaDesmarcadaVO.getDataOrigem());
            }
            if (aulaDesmarcadaVO.getContratoVO() != null && UteisValidacao.emptyNumber(aulaDesmarcadaVO.getContratoVO().getCodigo())) {
                throw new ConsistirException("O contrato não foi encontrado!");
            }
            aulaDesmarcada.incluirSemCommit(aulaDesmarcadaVO);
            incluirLogDesmarcaoAula(aulaDesmarcadaVO,"ALTERAÇÃO - DESMARCAÇÃO DE AULA.");
            this.con.commit();

            if (Calendario.igual(Calendario.getDataComHoraZerada(aulaDesmarcadaVO.getDataOrigem()), Calendario.getDataComHoraZerada(Calendario.hoje()))) {
                this.situacaoClienteSinteticoDW.atualizarBaseOffLineZillyonAcesso(this.key, aulaDesmarcadaVO.getClienteVO().getPessoa().getCodigo());
            }
        } catch (Exception e) {
            this.con.rollback();
            throw e;
        } finally {
            this.con.setAutoCommit(true);
        }
    }


    public void validarMinutosAntecedenciaDesmarcarAula(TurmaVO turmaVO, HorarioTurmaVO horarioTurmaVO, Date dataAula) throws Exception {
        if (turmaVO.getMinutosAntecedenciaDesmarcarAula() > 0) {
            Calendar dataHoraAula = Calendario.getInstance();
            dataHoraAula.setTime(Calendario.getDataComHoraZerada(dataAula));
            String[] HoraMinuto = horarioTurmaVO.getHoraInicial().split(":");
            dataHoraAula.set(Calendar.HOUR, Integer.parseInt(HoraMinuto[0]));
            dataHoraAula.set(Calendar.MINUTE, Integer.parseInt(HoraMinuto[1]));
            Calendar dataComparar = Calendario.getInstance();
            dataComparar.setTime(dataHoraAula.getTime());
            //retirar os minutos de antecedencia para desmarcar aula
            dataComparar.add(Calendar.MINUTE, (turmaVO.getMinutosAntecedenciaDesmarcarAula() * -1));
            if (!(Calendario.menorOuIgualComHora(Calendario.hoje(), dataComparar.getTime()))) {
                throw new ConsistirException("Operação não permitida. Para desmarcar a aula na turma " + turmaVO.getDescricao() + " é necessário uma antecedência mínima de (" + turmaVO.getMinutosAntecedenciaDesmarcarAula() + ") minutos antes do ínicio da aula.");
            }
        }
    }

    private void atualizarAulaDesmarcada(ReposicaoVO reposicaoVO,  AulaDesmarcadaVO aulaDesmarcadaVO)throws Exception{
        if (UtilReflection.objetoMaiorQueZero(aulaDesmarcadaVO, "getCodigo()" )){
            // Neste caso, foi feito uma reposição de uma aula que já tinha sido desmarcada.
            aulaDesmarcadaVO.setDataReposicao(reposicaoVO.getDataReposicao());
            aulaDesmarcadaVO.setReposicaoVO(reposicaoVO);
            aulaDesmarcada.atualizarDataReposicao(aulaDesmarcadaVO);
        }
    }

    private void enviarEmail(final ReposicaoVO repo, boolean exclusao) throws Exception {
        if (configCRM.getMailServer() != null && !configCRM.getMailServer().isEmpty()) {
            if (!UteisValidacao.emptyString(repo.getCliente().getPessoa().getEmail())) {
                String[] dest = new String[]{
                        repo.getCliente().getPessoa().getEmail()
                };
                uteisEmail.enviarEmailN(dest, repo.getResultadoReposicaoHTML(exclusao).toString(),
                        (exclusao ? "Aula Reposição CANCELADA: " : "Aula REPOSIÇÃO para: ") + repo.getDescricaoDestino(), repo.getCliente().getEmpresa_Apresentar());
            }
        }
    }

    private void enviarEmailESMS(ReposicaoVO reposicaoVO) {
        try {
            reposicaoVO.getCliente().setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(reposicaoVO.getCliente().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            enviarEmail(reposicaoVO, false);
            enviarSMS(reposicaoVO, false);
        } catch (Exception ex) {
            Logger.getLogger(ReposicaoControle.class.getName()).log(Level.SEVERE, null, ex);
        }

    }

    private void enviarSMS(final ReposicaoVO repo, boolean exclusao) throws Exception {
        String telefones = repo.getCliente().getPessoa().getTelefones();
        if (!UteisValidacao.emptyString(telefones)) {
            String token = Tricks.obterValorCampoConfiguracaoSistema("tokencontasms", getKey());
            if (token != null && !token.isEmpty()) {
                String[] fones = telefones.split(";");
                List<Message> beans = new ArrayList<Message>();
                for (int i = 0; i < fones.length; i++) {
                    String numero = fones[i];
                    Message b = new Message();
                    b.setMsg(repo.getResultadoReposicaoTEXTO(exclusao).toString());
                    b.setNumero(numero);
                    beans.add(b);
                }
                if (!beans.isEmpty()) {
                    SmsController smsController = new SmsController(repo.getCliente().getEmpresa().getTokenSMS(), getKey(), TimeZone.getTimeZone(repo.getCliente().getEmpresa().getTimeZoneDefault()));
                    smsController.sendMessage(null, beans);

                }
            }
        }
    }

    public UteisEmail getUteisEmail() {
        return uteisEmail;
    }

    public void setUteisEmail(UteisEmail uteisEmail) {
        this.uteisEmail = uteisEmail;
    }

    public ConfiguracaoSistemaCRMVO getConfigCRM() {
        return configCRM;
    }

    public void setConfigCRM(ConfiguracaoSistemaCRMVO configCRM) {
        this.configCRM = configCRM;
    }

    public Reposicao getReposicao() {
        return reposicao;
    }

    public void setReposicao(Reposicao reposicao) {
        this.reposicao = reposicao;
    }

    public AulaDesmarcada getAulaDesmarcada() {
        return aulaDesmarcada;
    }

    public void setAulaDesmarcada(AulaDesmarcada aulaDesmarcada) {
        this.aulaDesmarcada = aulaDesmarcada;
    }

    public ConfiguracaoSistemaCRM getConfiguracaoSistemaCRM() {
        return configuracaoSistemaCRM;
    }

    public void setConfiguracaoSistemaCRM(ConfiguracaoSistemaCRM configuracaoSistemaCRM) {
        this.configuracaoSistemaCRM = configuracaoSistemaCRM;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public SituacaoClienteSinteticoDW getSituacaoClienteSinteticoDW() {
        return situacaoClienteSinteticoDW;
    }

    public void setSituacaoClienteSinteticoDW(SituacaoClienteSinteticoDW situacaoClienteSinteticoDW) {
        this.situacaoClienteSinteticoDW = situacaoClienteSinteticoDW;
    }

    public HorarioTurma getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(HorarioTurma horarioTurma) {
        this.horarioTurma = horarioTurma;
    }


    public ContratoModalidadeHorarioTurma getContratoModalidadeHorarioTurma() {
        return contratoModalidadeHorarioTurma;
    }

    public void setContratoModalidadeHorarioTurma(ContratoModalidadeHorarioTurma contratoModalidadeHorarioTurma) {
        this.contratoModalidadeHorarioTurma = contratoModalidadeHorarioTurma;
    }

    public Cliente getCliente() {
        return cliente;
    }

    public void setCliente(Cliente cliente) {
        this.cliente = cliente;
    }

    public void desmarcarReposicao(ReposicaoVO reposicaoVO, boolean ignorarAntecedencia) throws Exception {
        this.con.setAutoCommit(false);
        try {
            desmarcarReposicao(reposicaoVO, ignorarAntecedencia, false);
            this.con.commit();
        } catch (Exception e) {
            this.con.rollback();
            throw e;
        } finally {
            this.con.setAutoCommit(true);
        }
    }
    
    public void desmarcarReposicao(ReposicaoVO reposicaoVO, boolean ignorarAntecedencia, boolean isCancelamento) throws Exception {
        if(!isCancelamento) {
            boolean usuarioTemPermissaoExclusao = true;
            try {
                reposicao.validarPermissaoExcluir(reposicaoVO);
            } catch (Exception ex) {
                usuarioTemPermissaoExclusao = false;
            }

            if (!reposicaoVO.getTurmaDestino().isPermitirDesmarcarReposicoes()) {
                    throw new Exception("Nesta turma não é permitido desmarcar aulas de reposição!");
            }
            if (!usuarioTemPermissaoExclusao) {
                    throw new Exception("Usuário não tem permissão para desmarcar aulas de reposição!");
            }

            if (!ignorarAntecedencia) {
                //validar o limite de minutos de antecedência para desmarcar aula.
                validarMinutosAntecedenciaDesmarcarAula(reposicaoVO.getTurmaDestino(), reposicaoVO.getHorarioTurma(), reposicaoVO.getDataReposicao());
            }
        }
        reposicao.excluirSemValidarPermissao(reposicaoVO);
        incluirLogExclusaoReposicao(reposicaoVO);

        if (Calendario.igual(Calendario.getDataComHoraZerada(reposicaoVO.getDataReposicao()), Calendario.getDataComHoraZerada(Calendario.hoje()))) {
            this.situacaoClienteSinteticoDW.atualizarBaseOffLineZillyonAcesso(this.key, reposicaoVO.getCliente().getPessoa().getCodigo());
        }
    }
    
    private void incluirLogExclusaoReposicao(ReposicaoVO reposicaoVO) throws Exception {
        try{
            LogVO logVO = new LogVO();
            logVO.setChavePrimaria( reposicaoVO.getContrato().getCodigo().toString());
            logVO.setNomeEntidade("CONTRATO");
            logVO.setOperacao("ALTERAÇÃO - EXCLUSÃO DE REPOSIÇÃO.");
            logVO.setNomeEntidadeDescricao("Contrato - Reposição");
            logVO.setResponsavelAlteracao(reposicaoVO.getUsuario().getNome());
            logVO.setUserOAMD(reposicaoVO.getUsuario().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            StringBuilder msgLog = new StringBuilder();
            msgLog.append("Reposição: "+  reposicaoVO.getCodigo());
            msgLog.append("\nDados da reposição: "+ reposicaoVO.getDescricaoDestinoCurta());
            logVO.setValorCampoAlterado(msgLog.toString());
            logVO.setPessoa( reposicaoVO.getCliente().getPessoa().getCodigo());

            log.incluirSemCommit(logVO);

        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO GRAVAR LOG DE EXLUSÃO DE NÃO COMPARECIMENTO AULA DE CREDITO. CONTRATO =" + reposicaoVO.getContrato().getCodigo());
        }
    }
    
    private void incluirLogDesmarcaoAula(AulaDesmarcadaVO aulaDesmarcaVO, String operacao) throws Exception {
        try{
            LogVO logVO = new LogVO();
            logVO.setChavePrimaria(aulaDesmarcaVO.getContratoVO().getCodigo().toString());
            logVO.setNomeEntidade("CONTRATO");
            logVO.setOperacao(operacao);
            logVO.setNomeEntidadeDescricao("Contrato - aula desmarcada");
            logVO.setResponsavelAlteracao(aulaDesmarcaVO.getUsuarioVO().getNome());
            logVO.setUserOAMD(aulaDesmarcaVO.getUsuarioVO().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            StringBuilder msgLog = new StringBuilder();
            msgLog.append("Aula Desmarcada: "+ aulaDesmarcaVO.getCodigo());
            msgLog.append("\nDados da aula desmarcada: "+aulaDesmarcaVO.getDescricaoOrigemCurta());
            logVO.setValorCampoAlterado(msgLog.toString());
            logVO.setPessoa(aulaDesmarcaVO.getClienteVO().getPessoa().getCodigo());

            log.incluirSemCommit(logVO);

        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO GRAVAR LOG DE EXLUSÃO DE NÃO COMPARECIMENTO AULA DE CREDITO. CONTRATO =" + aulaDesmarcaVO.getContratoVO().getCodigo());
        }
    }
    
    public void excluirAulaDesmarcada(AulaDesmarcadaVO aulaDesmarcadaVO, String fonteOperacao) throws Exception {
        this.con.setAutoCommit(false);
        try {
            excluirAulaDesmarcadaSemComit(aulaDesmarcadaVO, fonteOperacao);
            this.con.commit();
        } catch (Exception e) {
            this.con.rollback();
            throw e;
        } finally {
            this.con.setAutoCommit(true);
        }
    }
    
    public void excluirAulaDesmarcadaSemComit(AulaDesmarcadaVO aulaDesmarcadaVO, String fonteOperacao) throws Exception {
        aulaDesmarcada.excluirSemCommit(aulaDesmarcadaVO);
        if(aulaDesmarcadaVO.isPermiteReporAulaDesmarcada()){
            incluirLogDesmarcaoAula(aulaDesmarcadaVO, "ALTERAÇÃO - EXCLUSÂO DESMARCAÇÃO"+ (UteisValidacao.emptyString(fonteOperacao) ? "" : ""+fonteOperacao));
        }
        if (Calendario.igual(Calendario.getDataComHoraZerada(aulaDesmarcadaVO.getDataOrigem()), Calendario.getDataComHoraZerada(Calendario.hoje()))) {
            this.situacaoClienteSinteticoDW.atualizarBaseOffLineZillyonAcesso(this.key, aulaDesmarcadaVO.getClienteVO().getPessoa().getCodigo());
        }
    }
    public void incluirLogDesmarcaoAulaCancelamentoContrato(ClienteVO cliente, UsuarioVO usuario,AlunoHorarioTurmaVO horario) throws Exception {
        try{
            LogVO logVO = new LogVO();
            logVO.setChavePrimaria(horario.getHorarioTurma().getCodigo()+"_"+Calendario.getDataAplicandoFormatacao(horario.getData(), "dd/MM/yyyy"));
            logVO.setOperacao("EXCLUSÃO");
            logVO.setNomeEntidade("ALUNO_AULA_COLETIVA");
            logVO.setNomeEntidadeDescricao("Cancelamento Contrato - aula desmarcada");
            logVO.setResponsavelAlteracao(usuario.getNome());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAlterado("[matricula:"+cliente.getMatricula()+"<br/>[aluno:"+cliente.getPessoa().getNome()+"]<br/>[origem: Cancelamento Contrato - aula desmarcada]");
            logVO.setPessoa(cliente.getPessoa().getCodigo());
            log.incluirSemCommit(logVO);
            this.con.commit();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
}
