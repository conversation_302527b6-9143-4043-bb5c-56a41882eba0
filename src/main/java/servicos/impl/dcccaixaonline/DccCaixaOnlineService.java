package servicos.impl.dcccaixaonline;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.integracao.aragorn.AragornService;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTCreationException;
import controle.arquitetura.exceptions.CobrancaException;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.ConvenioCobrancaArquivoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.TransacaoCaixaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.ConvenioCobrancaArquivo;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.apf.APF;
import servicos.impl.gatewaypagamento.AbstractCobrancaOnlineServiceComum;
import servicos.interfaces.AprovacaoServiceInterface;
import servicos.propriedades.PropsService;

import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * Created by Maurin Noleto on 23/09/2024.
 */
public class DccCaixaOnlineService extends AbstractCobrancaOnlineServiceComum implements AprovacaoServiceInterface {

    private String urlAPIRequisicao = "";
    private String merchantId;
    private String merchantKey;
    private Transacao transacaoDAO;
    private Pessoa pessoaDAO;
    private ConvenioCobranca convenioCobrancaDAO;
    private ConvenioCobrancaArquivo convenioCobrancaArquivoDAO;
    private ConvenioCobrancaVO convenioDCCCaixaOnline;

    public DccCaixaOnlineService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        this.transacaoDAO = new Transacao(con);
        this.pessoaDAO = new Pessoa(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.convenioCobrancaArquivoDAO = new ConvenioCobrancaArquivo(con);
        this.convenioDCCCaixaOnline = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (UteisValidacao.emptyList(this.convenioDCCCaixaOnline.getListaConvenioCobrancaArquivoVO())) {
            this.convenioDCCCaixaOnline.setListaConvenioCobrancaArquivoVO(this.convenioCobrancaArquivoDAO.consultarListaPorConvenioCobranca(this.convenioDCCCaixaOnline.getCodigo(), false));
        }
        popularInformacoes();
    }

    private void popularInformacoes() {
        if (this.convenioDCCCaixaOnline != null) {
            this.merchantId = this.convenioDCCCaixaOnline.getCodigoAutenticacao01();
            this.merchantKey = this.convenioDCCCaixaOnline.getCodigoAutenticacao02();

            if (this.convenioDCCCaixaOnline.getAmbiente().equals(AmbienteEnum.PRODUCAO)) {
                this.urlAPIRequisicao = PropsService.getPropertyValue(PropsService.urlApiCaixaPagamentosRequisicaoProducao);
            } else {
                this.urlAPIRequisicao = PropsService.getPropertyValue(PropsService.urlApiCaixaPagamentosRequisicaoSandbox);
            }
        }
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacao = null;
        Transacao transacaoDAO = null;
        try {
            transacaoDAO = new Transacao(getCon());
            transacao = criarTransacao(dadosCartao, new TransacaoCaixaVO(), TipoTransacaoEnum.DCC_CAIXA_ONLINE, this.convenioDCCCaixaOnline);
            transacao.setCodigo(0);
            transacaoDAO.incluir(transacao);
            transacao.setBandeiraPagamento(dadosCartao.getBand());

            JSONObject outrasInformacoes = new JSONObject(transacao.getOutrasInformacoes());
            outrasInformacoes.put(AtributoTransacaoEnum.cartaoMascarado.name(), APF.getCartaoMascarado(dadosCartao.getNumero()));
            transacao.setOutrasInformacoes(outrasInformacoes.toString());

            JSONObject bodyTransaction = criarBodyTransaction(transacao, dadosCartao);
            transacao.setParamsEnvio(mascararParametrosEnvio(bodyTransaction));

            validarDados(dadosCartao, transacao);

            RespostaHttpDTO respostaHttpDTO = executarRequestCaixa(bodyTransaction.toString(), "/v2/payments", MetodoHttpEnum.POST, null);
            processarRetornoTransaction(transacao, respostaHttpDTO);

            transacaoDAO.alterar(transacao);

            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.NENHUMA) ||
                    transacao.getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO) ||
                    transacao.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {

                throw new Exception(transacao.getOutrasInformacoes());
            }
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex);
            verificarException(transacao, ex);
        } catch (Exception ex) {
            ex.printStackTrace();
            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO)) {
                marcarTransacaoComErro(transacao, ex.getMessage());
            }
            verificarException(transacao, ex);
        } finally {
            gravarTentativaCartao(transacao);
            transacaoDAO = null;
        }
        return transacao;
    }

    private void validarDados(CartaoCreditoTO dadosCartao, TransacaoVO transacao) throws Exception {
        validarDadosTransacao(transacao, dadosCartao);

        //Validar bandeira do cartão
        if (DCCCaixaBandeirasEnum.obterBandeiraFiserv(dadosCartao.getBand().getDescricao()).getCodigoFiserv().equals(DCCCaixaBandeirasEnum.NENHUM.getCodigoFiserv())) {
            transacao.setSituacao(SituacaoTransacaoEnum.COM_ERRO);
            throw new Exception("Bandeira não permitida para transações com a Caixa Econômica Federal.");
        }
    }

    private String mascararParametrosEnvio(JSONObject bodyTransaction) {
        // Cria uma cópia do objeto JSON original
        JSONObject bodyTransactionCopia = new JSONObject(bodyTransaction.toString());

        // Mascarar o número do cartão
        String cartaoOriginal = bodyTransactionCopia.getJSONObject("card").getString("number");
        String cartaoMascarado = cartaoOriginal.substring(0, 4) + " **** **** " + cartaoOriginal.substring(cartaoOriginal.length() - 4);
        bodyTransactionCopia.getJSONObject("card").put("number", cartaoMascarado);

        // Mascarar o código de segurança (cvv)
        bodyTransactionCopia.getJSONObject("card").put("security_code", "***");

        // Retornar a string com os parâmetros mascarados
        return bodyTransactionCopia.toString();
    }

    private JSONObject criarBodyTransaction(TransacaoVO transacao, CartaoCreditoTO dadosCartao) {

        JSONObject transaction = new JSONObject();
        transaction.put("order_id", transacao.getCodigo().toString());
        transaction.put("installments", transacao.getNrVezesCobranca().toString());
        transaction.put("installment_type", "4"); //valor 3 = parcelamento com juros da administradora do carto. valor 4 = parcelamento realizado pela loja e sem juros (valor como padro/default para transaes  vista).
        transaction.put("amount", obterValorParaBody(transacao.getValor(), dadosCartao.isVerificacaoZeroDollar()));
        transaction.put("soft_descriptor", StringUtilities.formatarCampoEmBranco(this.convenioDCCCaixaOnline.getEmpresa().getRazaoSocialParaSoftDescriptor(false), 13));
        transaction.put("authorizer_id", DCCCaixaBandeirasEnum.obterBandeiraFiserv(dadosCartao.getBand().getDescricao()).getCodigoFiserv());

        JSONObject card = new JSONObject();
        card.put("number", transacao.getCartaoCreditoTO().getNumero());
        card.put("expiry_date", obterMesAnoParaBody(dadosCartao));
//        card.put("security_code", transacao.getCartaoCreditoTO().getCodigoSeguranca());
        card.put("security_code", "");

        transaction.put("card", card);

        return transaction;
    }

    public String obterValorParaBody(Double valor, boolean transacaoZeroDollar) {
        //Transacao verificação retorna 0 para o valor
        if (transacaoZeroDollar) {
            return "0";
        }
        // Multiplica o valor por 100 e converte para inteiro, garantindo 2 casas decimais
        Integer valorInteiro = (int) (valor * 100);
        String valorString = String.valueOf(valorInteiro);
        return valorString;
    }

    public String obterMesAnoParaBody(CartaoCreditoTO dadosCartao) {
        String dataVencimentoCartaoString = "";
        dataVencimentoCartaoString = dadosCartao.getValidade().substring(0, 2);
        if (dadosCartao.getValidade().length() > 5) {
            dataVencimentoCartaoString = dataVencimentoCartaoString + dadosCartao.getValidade().substring(5);
        } else {
            dataVencimentoCartaoString = dataVencimentoCartaoString + dadosCartao.getValidade().substring(3);
        }
        return dataVencimentoCartaoString;
    }

    private RespostaHttpDTO executarRequestCaixa(String body, String metodo, MetodoHttpEnum metodoHttpEnum, String autorizacaoComAssinatura) throws Exception {
        validarDadosConvenio();

        String URL = this.urlAPIRequisicao;
        String path = URL + metodo;
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("merchant_id", this.merchantId);
        headers.put("merchant_key", this.merchantKey);

        if (!UteisValidacao.emptyString(autorizacaoComAssinatura)) {
            headers.put("Authorization", "Bearer " + autorizacaoComAssinatura);
        }

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, null, body, metodoHttpEnum);
        service = null;
        return respostaHttpDTO;
    }

    private void validarDadosConvenio() throws Exception {
        if (this.convenioDCCCaixaOnline == null || UteisValidacao.emptyNumber(this.convenioDCCCaixaOnline.getCodigo())) {
            throw new Exception("Convnio de cobrana no encontrado ou inativo.");
        }
        if (UteisValidacao.emptyString(this.merchantId)) {
            throw new Exception("MerchantId no convnio de cobrana no informado.");
        }
        if (UteisValidacao.emptyString(this.merchantKey)) {
            throw new Exception("MerchantKey no convnio de cobrana no informado.");
        }
    }

    public ConvenioCobrancaVO getConvenioDCCCaixaOnline() {
        return convenioDCCCaixaOnline;
    }

    public void setConvenioDCCCaixaOnline(ConvenioCobrancaVO convenioDCCCaixaOnline) {
        this.convenioDCCCaixaOnline = convenioDCCCaixaOnline;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantKey() {
        return merchantKey;
    }

    public void setMerchantKey(String merchantKey) {
        this.merchantKey = merchantKey;
    }

    private void processarRetornoTransaction(TransacaoVO transacao, RespostaHttpDTO respostaHttpDTO) {
        incluirHistoricoRetornoTransacao(transacao, respostaHttpDTO.getResponse(), "realizarCapturaTransacao");
        transacao.setParamsResposta(respostaHttpDTO.getResponse());
        try {

            Integer httpStatus = respostaHttpDTO.getHttpStatus();
            if (httpStatus == 504) {
                transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
                transacao.setPermiteRepescagem(false);
                throw new Exception("Erro Caixa criar Transaction: 504 Gateway Time-out. </br> Verifique no portal da Caixa a situação da transação e faça o ajuste manual da parcela.");
            }

            JSONObject retornoJSON = new JSONObject(respostaHttpDTO.getResponse());
            String codigoRetorno = retornoJSON.optString("code"); //Codigo retorno da API
            String mensagemRetorno = retornoJSON.optString("message");

            JSONObject payment = retornoJSON.getJSONObject("payment");
            DCCCaixaStatusEnum status = DCCCaixaStatusEnum.valueOff(payment.optString("status"));
            String paymentId = payment.getString("nit");
            transacao.setCodigoExterno(paymentId);

            String mensagemRetornoAdquirente = "";
            String codigoRetornoAdquirente = "";
            try {
                mensagemRetornoAdquirente = payment.getString("authorizer_message");
            } catch (Exception ex) {
                mensagemRetornoAdquirente = mensagemRetorno;
            }

            try {
                codigoRetornoAdquirente = payment.getString("authorizer_code"); //Codigo retorno da Adquirente
            } catch (Exception ex) {
                codigoRetornoAdquirente = codigoRetorno;
            }

            transacao.setCodigoRetorno(codigoRetornoAdquirente);
            transacao.setCodigoRetornoDescricao(mensagemRetorno);

            if (!codigoRetorno.equals("0") && !mensagemRetorno.equals("OK. Transaction successful.")) {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
                transacao.setPermiteRepescagem(true);
                throw new Exception("Erro Caixa criar Transaction: " + codigoRetorno + " - " + mensagemRetornoAdquirente);
            }

            preencherSituacaoTransacao(transacao, status, "");

            if (status.equals(DCCCaixaStatusEnum.StatusCON)) {
                String codigoAutorizacao = payment.optString("authorization_number");
                transacao.setCodigoAutorizacao(codigoAutorizacao);

                JSONObject envioJSON = new JSONObject(transacao.getParamsEnvio());
                Integer nrVezes = Integer.valueOf(envioJSON.optString("installments"));
                transacao.setNrVezes(nrVezes);
            }
        } catch (Exception e) {
            e.printStackTrace();
            transacao.adicionarOutrasInformacoes(AtributoTransacaoEnum.mensagemErro, e.getMessage());
        }
    }

    private void preencherSituacaoTransacao(TransacaoVO transacao, DCCCaixaStatusEnum status, String returnCode) {
        if (status.equals(DCCCaixaStatusEnum.StatusCON)) {
            transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
            transacao.setPermiteRepescagem(false);
        } else if (status.equals(DCCCaixaStatusEnum.StatusNEG)) {
            transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            transacao.setPermiteRepescagem(true);
        }
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacao, Boolean estornarRecibo) throws Exception {
        if ((transacao.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) ||
                transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) && !UteisValidacao.emptyNumber(transacao.getValor())) {
            realizarCancelamentoTransacao(transacao, estornarRecibo);
        } else if ((transacao.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) ||
                transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) && UteisValidacao.emptyNumber(transacao.getValor())) {
            //A transação foi realizada com valor zerado, então não é necessário realizar o cancelamento, pois vai retornar sempre erro da API da Fiserv.
            transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            if (transacaoDAO != null) {
                transacaoDAO.alterarSituacao(transacao);
            }
        }
        return transacao;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    @Override
    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacaoVO) throws Exception {

    }

    private void realizarCancelamentoTransacao(TransacaoVO transacao, Boolean estornarRecibo) throws Exception {
        if (!UteisValidacao.emptyString(transacao.getCodigoExterno())) {
            String autenticacaoComAssinatura = obterAutenticacaoComAssinatura(transacao);

            JSONObject bodyCancellation = criarBodyCancellation(transacao);

            RespostaHttpDTO respostaHttpDTO = executarRequestCaixa(bodyCancellation.toString(), "/v2/cancellations/" + transacao.getCodigoExterno(), MetodoHttpEnum.POST, autenticacaoComAssinatura);
            processarRetornoCancelamento(transacao, respostaHttpDTO);

            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) && estornarRecibo &&
                    !UteisValidacao.emptyNumber(transacao.getReciboPagamento())) {
                estornarRecibo(transacao, estornarRecibo);
                if (transacao.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                    transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                }
            }
        } else {
            transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
        }

        new Transacao(getCon()).alterar(transacao);
    }

    private String obterAutenticacaoComAssinatura(TransacaoVO transacaoVO) throws Exception {
        String autenticacaoComAssinatura = "";

        RSAPublicKey rsaPublicKey = null;
        RSAPrivateKey rsaPrivateKey = null;
        try {
            rsaPublicKey = (RSAPublicKey) obterChavePublicaRSA();
            rsaPrivateKey = (RSAPrivateKey) obterChavePrivadaRSA();
        } catch (Exception e) {
            throw new Exception(e);
        }

        try {
            Algorithm algorithm = Algorithm.RSA256(rsaPublicKey, rsaPrivateKey);

            // Monta o Map<String, Object> para o Header
            Map<String, Object> headerClaims = new HashMap<>();
            headerClaims.put("alg", "RS256");
            headerClaims.put("typ", "JWT");

            // Monta o Token JWT passando os dados do Payload nos métodos withClaim.
            Calendar calendario = Calendar.getInstance();
            long timestampAtual = calendario.getTimeInMillis();
            autenticacaoComAssinatura = JWT.create()
                    .withHeader(headerClaims)
                    .withClaim("merchant_id", merchantId)
                    .withClaim("merchant_key", merchantKey)
                    .withClaim("order_id", transacaoVO.getCodigo().toString())
                    .withClaim("timestamp", String.valueOf(timestampAtual))
                    .sign(algorithm);
        } catch (JWTCreationException exception){
            exception.printStackTrace();
        }

        return autenticacaoComAssinatura;
    }

    private PublicKey obterChavePublicaRSA() throws Exception {
        //usar certificado publico gerado através dos 2 certificados (público e privado) configurados dentro do convênio
        byte[] certificadoSSHRSA = null;

        if (!UteisValidacao.emptyList(this.convenioDCCCaixaOnline.getListaConvenioCobrancaArquivoVO())) {
            for (ConvenioCobrancaArquivoVO item : this.convenioDCCCaixaOnline.getListaConvenioCobrancaArquivoVO()) {
                if (item.getArquivo() == null) {
                    item.setArquivo(this.convenioCobrancaArquivoDAO.consultarArquivoBytePorChavePrimaria(item.getCodigo()));
                }
                if (item.getArquivo() != null && item.getNomeArquivoOriginal().endsWith(".pub")) {
                    certificadoSSHRSA = item.getArquivo();
                    break;
                }
            }
        } else {
            throw new Exception("Não encontrei nenhum arquivo/certificado Público cadastrado para o convênio: " + this.convenioDCCCaixaOnline.getDescricao());
        }

        if (certificadoSSHRSA == null) {
            throw new Exception("Não foi possível obter o certificado Público para criar a Autenticação com Assinatura.");
        }

        //Converte o byte[] para String, para tirar os dados desnescessários
        String certificadoSSHPEMString = new String(certificadoSSHRSA, StandardCharsets.UTF_8);
        certificadoSSHPEMString = certificadoSSHPEMString.replace("-----BEGIN PUBLIC KEY-----", "");
        certificadoSSHPEMString = certificadoSSHPEMString.replace("-----END PUBLIC KEY-----", "");
        certificadoSSHPEMString = certificadoSSHPEMString.replaceAll("\\s+", "");

        byte[] encoded = Base64.getDecoder().decode(certificadoSSHPEMString);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(encoded);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");

        return keyFactory.generatePublic(keySpec);
    }

    private PrivateKey obterChavePrivadaRSA() throws Exception {
        //usar certificado privado gerado através dos 2 certificados (público e privado) configurados dentro do convênio
        byte[] certificadoSSHRSA = null;

        if (!UteisValidacao.emptyList(this.convenioDCCCaixaOnline.getListaConvenioCobrancaArquivoVO())) {
            for (ConvenioCobrancaArquivoVO item : this.convenioDCCCaixaOnline.getListaConvenioCobrancaArquivoVO()) {
                if (item.getArquivo() == null) {
                    item.setArquivo(this.convenioCobrancaArquivoDAO.consultarArquivoBytePorChavePrimaria(item.getCodigo()));
                }
                if (item.getArquivo() != null && item.getNomeArquivoOriginal().endsWith(".key")) {
                    certificadoSSHRSA = item.getArquivo();
                    break;
                }
            }
        } else {
            throw new Exception("Não encontrei nenhum arquivo/certificado Privado cadastrado para o convênio: " + this.convenioDCCCaixaOnline.getDescricao());
        }

        if (certificadoSSHRSA == null) {
            throw new Exception("Não foi possível obter o certificado Privado para criar a Autenticação com Assinatura.");
        }

        //Converte o byte[] para String, para tirar os dados desnescessários
        String certificadoSSHPEMString = new String(certificadoSSHRSA, StandardCharsets.UTF_8);
        certificadoSSHPEMString = certificadoSSHPEMString.replace("-----BEGIN PRIVATE KEY-----", "");
        certificadoSSHPEMString = certificadoSSHPEMString.replace("-----END PRIVATE KEY-----", "");
        certificadoSSHPEMString = certificadoSSHPEMString.replaceAll("\\s+", "");

        byte[] encoded = Base64.getDecoder().decode(certificadoSSHPEMString);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(encoded);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");

        return keyFactory.generatePrivate(keySpec);
    }

    private JSONObject criarBodyCancellation(TransacaoVO transacao) throws Exception {

        List<AutorizacaoCobrancaClienteVO> autorizacoes = obterAutorizacoesCliente(transacao);

        JSONObject cancellation = new JSONObject();
        cancellation.put("amount", obterValorParaBody(transacao.getValor(), false));

        //Se no futuroa a lógica abaixo der algum problema, pode ser refeito utilizando o Convênio de Cobrança como Referencia.
        //O mesmo vem na Transação e nas Autorizações de Cobrança do Cliente.

        AutorizacaoCobrancaClienteVO autorizacao = null;
        for (AutorizacaoCobrancaClienteVO aut : autorizacoes) {
            JSONObject parametrosEnvioJson = new JSONObject(transacao.getParamsEnvio());
            String number = parametrosEnvioJson.getJSONObject("card").getString("number");

            String primeirosQuatro = number.substring(0, 4);
            String ultimosQuatro = number.substring(number.length() - 4);

            // Verifica se o cartão mascarado contém os 4 primeiros e os 4 últimos dígitos
            if (aut.getNazgDTO().getCard().startsWith(primeirosQuatro) && aut.getNazgDTO().getCard().endsWith(ultimosQuatro)) {
                autorizacao = aut;
                break;
            }
        }

        if (autorizacao == null) {
            throw new Exception("A Autorização de Cobrança do cartão, desta transação não foi encontrada. Ela é obrigatória para o cancelamento da transação.");
        }

        CartaoCreditoTO dadosCartao = new CartaoCreditoTO();
        dadosCartao.setValidade(autorizacao.getValidadeCartao());

        JSONObject card = new JSONObject();
        card.put("number", autorizacao.getNazgDTO().getCard());
        card.put("expiry_date", obterMesAnoParaBody(dadosCartao));
        card.put("security_code", "");

        cancellation.put("card", card);

        return cancellation;
    }

    private void processarRetornoCancelamento(TransacaoVO transacao, RespostaHttpDTO respostaHttpDTO) throws Exception {
        incluirHistoricoRetornoTransacao(transacao, respostaHttpDTO.getResponse(), "processarRetornoCancelamento");
        transacao.setResultadoCancelamento(respostaHttpDTO.getResponse());
        try {
            Integer httpStatus = respostaHttpDTO.getHttpStatus();
            if (httpStatus == 504) {
                throw new Exception("Erro Caixa criar cancelar Transação: 504 Gateway Time-out. Aguardar uns minutos e tentar novamente mais tarde.");
            }

            JSONObject retornoCancelamentoJSON;
            try {
                retornoCancelamentoJSON = new JSONObject(respostaHttpDTO.getResponse());
            } catch (Exception ex) {
                String msgErro = "Erro ao tentar obter o response do retorno do cancelamento da transação.";
                if (!UteisValidacao.emptyString(msgErro)) {
                    throw new Exception(msgErro);
                }
                throw ex;
            }

            String codigoRetorno = retornoCancelamentoJSON.optString("code");
            String mensagemRetorno = retornoCancelamentoJSON.optString("message");

            JSONObject cancellation = null;
            String mensagemRetornoAdquirente = "";
            DCCCaixaStatusEnum status = null;
            try {
                cancellation = retornoCancelamentoJSON.getJSONObject("cancellation");
                mensagemRetornoAdquirente = cancellation.getString("authorizer_message");
                status = DCCCaixaStatusEnum.valueOff(cancellation.optString("status"));
            } catch (Exception e) {
                mensagemRetornoAdquirente = mensagemRetorno;
            }

            if (!codigoRetorno.equals("0") && !mensagemRetorno.equals("OK. Transaction successful.")) {
                throw new Exception("Erro Caixa tentar cancelar transação: " + codigoRetorno + " - " + mensagemRetornoAdquirente);
            }

            if (status.equals(DCCCaixaStatusEnum.StatusCON) || status.equals(DCCCaixaStatusEnum.StatusEST) || status.equals(DCCCaixaStatusEnum.StatusCAN)) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                transacao.setDataHoraCancelamento(Calendario.hoje());
            } else {
                throw new Exception("Erro Caixa tentar cancelar transação: " + codigoRetorno + " - " + cancellation.getString("authorizer_message"));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            new Transacao(getCon()).alterar(transacao);
        }
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return this.transacaoDAO;
    }

    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        return null;
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        return null;
    }

    public List<AutorizacaoCobrancaClienteVO> obterAutorizacoesCliente(TransacaoVO transacaoVO) throws Exception {
        AutorizacaoCobrancaCliente autorizacaoCobrancaClienteDAO = null;
        List<AutorizacaoCobrancaClienteVO> autorizacoes = new ArrayList<AutorizacaoCobrancaClienteVO>();
        try {
            autorizacaoCobrancaClienteDAO = new AutorizacaoCobrancaCliente(getCon());
            PessoaVO pessoaPagamento = transacaoVO.getPessoaPagador();
            if (pessoaPagamento != null && !UteisValidacao.emptyNumber(pessoaPagamento.getCodigo())) {
                List<AutorizacaoCobrancaClienteVO> listaAutoCliente = autorizacaoCobrancaClienteDAO.consultarPorPessoaTipoAutorizacao(pessoaPagamento.getCodigo(), TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (!UteisValidacao.emptyList(listaAutoCliente)) {
                    AragornService aragornService = new AragornService();
                    for (AutorizacaoCobrancaClienteVO obj : listaAutoCliente) {
                        try {
                            obj.setNazgDTO(aragornService.obterNazg(transacaoVO.getTokenAragorn()));
                            autorizacoes.add(obj);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }
                    aragornService = null;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("Erro ao tentar obter as autorizações do cliente para realizar o cancelamento da transação.");
        } finally {
            autorizacaoCobrancaClienteDAO = null;
        }
        return autorizacoes;
    }

}
