package servicos.impl.stoneV5;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 20/01/2025
 */

public enum TipoConsultaPayablesStoneEnum {

    NENHUM("NENHUM"),
    DATA_VENDA("Data da Venda (Criação)"),
    DATA_PAGAMENTO("Data de Pagamento"),
    ;

    private String id;

    TipoConsultaPayablesStoneEnum(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

}
