package servicos.acesso;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import acesso.webservice.ValidacaoAcessoWS;
import acesso.webservice.retorno.ResultadoWS;
import negocio.comuns.acesso.enumerador.TipoAcessoEnum;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

public class BiometriaFacialService {

    public static ResultadoWS atualizarAssinaturaBiometriaFacial(String key, String codigoAcesso, String assinatura) {
        ResultadoWS result = new ResultadoWS();
        try{
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            String tipoAcesso = codigoAcesso.substring(0, 1);
            if (TipoAcessoEnum.TA_ALUNO.getId().equals(tipoAcesso)) {

                ClienteVO cliente = acessoControle.getClienteDao().consultarPorCodAcesso(codigoAcesso, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if(cliente == null){
                    result.setMensagem("O cliente com código de acesso "+ codigoAcesso +" não foi encontrado");
                    result.setSucesso(false);
                }else{
                    acessoControle.getPessoaDao().atualizarAssinaturaBiometriaFacial(cliente.getPessoa().getCodigo(), assinatura);
                    result.setMensagem("Assinatura Facial do cliente atualizada");
                    result.setSucesso(true);
                }

            } else if (TipoAcessoEnum.TA_COLABORADOR.getId().equals(tipoAcesso)) {

                ColaboradorVO colaborador = acessoControle.getColaboradorDao().consultarPorCodAcesso(codigoAcesso, 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if(colaborador == null){
                    result.setMensagem("O colaborador com código de acesso "+ codigoAcesso +" não foi encontrado");
                    result.setSucesso(false);
                }else{
                    acessoControle.getPessoaDao().atualizarAssinaturaBiometriaFacial(colaborador.getPessoa().getCodigo(), assinatura);
                    result.setMensagem("Assinatura do colaborador atualizada");
                    result.setSucesso(true);
                }

            } else {
                result.setMensagem("ERRO: Tipo de acesso não definido com o código de acesso " + codigoAcesso);
                result.setSucesso(false);
            }
        }catch (Exception e){
            result.setSucesso(false);
            result.setMensagem("Houve um erro ao gravar assinatura facial na base de dados");
            Uteis.logar(e, ValidacaoAcessoWS.class);
        }
        return result;
    }

    public static ResultadoWS obterAssinaturaBiometriaFacial( String key, String codigoAcesso) {
        ResultadoWS result = new ResultadoWS();
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);

            String tipoAcesso = codigoAcesso.substring(0, 1);
            if (TipoAcessoEnum.TA_ALUNO.getId().equals(tipoAcesso)) {

                ClienteVO cliente = acessoControle.getClienteDao().consultarPorCodAcesso(codigoAcesso, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                String assinaturaBiometriaFacial = cliente == null ? "" : acessoControle.getPessoaDao().obterAssinaturaBiometriaFacial(cliente.getPessoa().getCodigo());
                if (cliente == null) {
                    result.setMensagem("O cliente com código de acesso " + codigoAcesso + " não foi encontrado");
                    result.setSucesso(false);
                } else if (UteisValidacao.emptyString(assinaturaBiometriaFacial)) {
                    result.setMensagem("O cliente com código de acesso " + codigoAcesso + " não tem biometria cadastrada");
                    result.setSucesso(false);
                } else {
                    result.setMensagem(assinaturaBiometriaFacial);
                    result.setSucesso(true);
                }

            } else if (TipoAcessoEnum.TA_COLABORADOR.getId().equals(tipoAcesso)) {

                ColaboradorVO colaborador = acessoControle.getColaboradorDao().consultarPorCodAcesso(codigoAcesso, 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                String assinaturaBiometriaFacial = colaborador == null ? "" : acessoControle.getPessoaDao().obterAssinaturaBiometriaFacial(colaborador.getPessoa().getCodigo());
                if (colaborador == null) {
                    result.setMensagem("O colaborador com código de acesso " + codigoAcesso + " não foi encontrado");
                    result.setSucesso(false);
                } else if (UteisValidacao.emptyString(assinaturaBiometriaFacial)) {
                    result.setMensagem("O colaborador com código de acesso " + codigoAcesso + " não tem biometria cadastrada");
                    result.setSucesso(true);
                } else {
                    result.setMensagem(assinaturaBiometriaFacial);
                    result.setSucesso(true);
                }

            } else {
                result.setMensagem("ERRO: Tipo de acesso não definido com o código de acesso " + codigoAcesso);
                result.setSucesso(false);
            }
        } catch (Exception e) {
            result.setSucesso(false);
            result.setMensagem("Houve um erro ao obter a assinatura da biometria facial");
            Uteis.logar(e, ValidacaoAcessoWS.class);
        }

        return result;
    }
}
