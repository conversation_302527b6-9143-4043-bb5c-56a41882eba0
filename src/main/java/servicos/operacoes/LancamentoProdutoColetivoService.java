/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.operacoes;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import negocio.comuns.basico.AmostraClienteTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.LancamentoProdutoColetivoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.LancamentoProdutoColetivo;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.ItemVendaAvulsa;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.VendaAvulsa;

/**
 *
 * <AUTHOR>
 */
public class LancamentoProdutoColetivoService {

    private Connection con;
    private LancamentoProdutoColetivo lancamentoProdutoDao;
    private Cliente clienteDao;
    private Pessoa pessoaDao;
    private MovProduto movProdutoDao;
    private MovParcela movParcelaDao;
    private MovProdutoParcela movProdutoParcelaDao;
    private VendaAvulsa vendaAvulsoDao;
    private ItemVendaAvulsa itemVendaAvulsoDao;

    public LancamentoProdutoColetivoService(Connection con) {
        this.con = con;
    }
    private void inicializarFacades() throws Exception{
        lancamentoProdutoDao = new LancamentoProdutoColetivo(con);
        clienteDao = new Cliente(con);
        pessoaDao = new Pessoa(con);
        movProdutoDao = new MovProduto(con);
        movParcelaDao = new MovParcela(con);
        movProdutoParcelaDao = new MovProdutoParcela(con);
        itemVendaAvulsoDao = new ItemVendaAvulsa(con);
        vendaAvulsoDao = new VendaAvulsa(con);
    }
    
    public void lancarProdutosParaContrato(ContratoVO contrato) throws Exception{
            inicializarFacades();
            List<LancamentoProdutoColetivoVO> lancamentos = lancamentoProdutoDao.consultarLancamentosEscopoContrato(contrato);
            for(LancamentoProdutoColetivoVO lanc : lancamentos){
                if(!lanc.getProdutoVO().getDesativado()) {
                    ClienteVO cliente = clienteDao.consultarPorCodigoPessoa(contrato.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    MovParcelaVO parcela = null;
                    if (lanc.getTipoParcela()) {
                        parcela = movParcelaDao.consultarPorNumeroContrato(lanc.getParcela(), contrato.getCodigo());
                        if (parcela == null)
                            continue;
                    }
                    Date data = obterDataDoProduto(lanc, parcela);
                    MovProdutoVO movProduto = gerarMovProduto(cliente, lanc, data, contrato.getCodigo());
//                VendaAvulsaVO vendaAvulso = gerarVendaAvulso(lanc, movProduto, cliente);
                    gerarParcelas(cliente, movProduto, lanc, data, new VendaAvulsaVO(), contrato);
                    lancamentoProdutoDao.atualizarJaFoiLancado(lanc);
                }
            }
    }


    public int lancarProdutoColetivo(Integer codigoLancamento, boolean ignorarOsJaLancados) throws Exception {
        try {
            con.setAutoCommit(false);
            inicializarFacades();
            LancamentoProdutoColetivoVO lancamento = lancamentoProdutoDao.consultarPorChavePrimaria(codigoLancamento, Uteis.NIVELMONTARDADOS_TODOS);

            List<AmostraClienteTO> clientes = lancamentoProdutoDao.consultarAmostraLancamentoColetivo(lancamento, ignorarOsJaLancados);
            for (AmostraClienteTO clienteAmos : clientes) {
                ClienteVO cliente = clienteDao.consultarPorCodigoMatricula(Integer.valueOf(clienteAmos.getMatricula()),
                        lancamento.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                MovParcelaVO parcela = null;
                if(lancamento.getTipoParcela()){
                    parcela = movParcelaDao.consultarPorNumeroContrato(lancamento.getParcela(), clienteAmos.getContrato());
                    if(parcela == null)
                        continue;
                }
                Date data = obterDataDoProduto(lancamento, parcela);
                MovProdutoVO movProduto = gerarMovProduto(cliente, lancamento, data, clienteAmos.getContrato());
                if(UteisValidacao.emptyNumber(clienteAmos.getContrato())){
                    VendaAvulsaVO vendaAvulso = gerarVendaAvulso(lancamento, movProduto, cliente);
                    gerarParcelas(cliente, movProduto, lancamento, data,vendaAvulso, null);
                }else{
                    ContratoVO contrato = new ContratoVO();
                    contrato.setCodigo(clienteAmos.getContrato());
                    movProduto.setContrato(contrato);
                    gerarParcelas(cliente, movProduto, lancamento, data,new VendaAvulsaVO(), contrato);
                }
                
            }
            lancamentoProdutoDao.atualizarJaFoiLancado(lancamento);
            con.commit();
            return clientes.size();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public VendaAvulsaVO gerarVendaAvulso(LancamentoProdutoColetivoVO lancamento, MovProdutoVO movproduto, ClienteVO cliente) throws Exception{
        cliente.setPessoa(pessoaDao.consultarPorChavePrimaria(cliente.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        VendaAvulsaVO vendaAvulso = new VendaAvulsaVO();
        vendaAvulso.setValidarDados(false);
        vendaAvulso.setTipoComprador("CI");
        vendaAvulso.setNomeComprador(cliente.getPessoa().getNome());
        vendaAvulso.setDataRegistro(Calendario.hoje());
        vendaAvulso.setCliente(cliente);
        vendaAvulso.setEmpresa(lancamento.getEmpresa());
        vendaAvulso.setResponsavel(lancamento.getUsuario());
        vendaAvulso.setValorTotal(lancamento.getValor());
        try (ResultSet rs = vendaAvulsoDao.incluirSemCommitSomenteVendaAvulsa(vendaAvulso)) {
            ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
            item.setProduto(lancamento.getProdutoVO());
            item.setQuantidade(1);
            item.setVendaAvulsa(vendaAvulso.getCodigo());
            item.setValorParcial(vendaAvulso.getValorTotal());
            itemVendaAvulsoDao.incluir(item);
            return vendaAvulso;
        }
    }

    public List<MovParcelaVO> gerarParcelas(ClienteVO cliente, MovProdutoVO movProduto, 
            LancamentoProdutoColetivoVO lancamento, Date data,
            VendaAvulsaVO vendaAvulso, ContratoVO contrato) throws Exception {
        if(lancamento.getValor().equals(0.0)){
            return null;
        }
        
        Double valorParcela = Uteis.arredondarForcando2CasasDecimais(movProduto.getTotalFinal() / lancamento.getNrVezesParcelar());
        Double valorDiferenca = movProduto.getTotalFinal() - (valorParcela * lancamento.getNrVezesParcelar());
        Double valorPrimeiraParcela = valorDiferenca > 0 ? valorParcela - valorDiferenca : valorParcela + (-1 * valorDiferenca);
        List<MovParcelaVO> parcelas = new ArrayList<MovParcelaVO>();
        Date dataParcela = new Date(data.getTime());

        for (int i = 1; i <= lancamento.getNrVezesParcelar(); i++) {
            MovParcelaVO parcela = new MovParcelaVO();
            parcela.setValorParcela(i == 1 ? valorPrimeiraParcela : valorParcela);
            parcela.setPessoa(cliente.getPessoa());
            parcela.setDataVencimento(dataParcela);
            parcela.setDataRegistro(Calendario.hoje());
            parcela.setResponsavel(lancamento.getUsuario());
            parcela.setSituacao("EA");
            parcela.setEmpresa(lancamento.getEmpresa());
            parcela.setDescricao(movProduto.getDescricao() + (" L.C. "+lancamento.getCodigo()) + (lancamento.getNrVezesParcelar() > 1 ? " - " + i : ""));
            if(contrato != null && !UteisValidacao.emptyNumber(contrato.getCodigo())){
                parcela.setContrato(contrato);
            }else{
                parcela.setVendaAvulsaVO(vendaAvulso);
            }
            movParcelaDao.incluirSemCommit(parcela);

            parcelas.add(parcela);
            MovProdutoParcelaVO produtoParcela = new MovProdutoParcelaVO();
            produtoParcela.setMovParcela(parcela.getCodigo());
            produtoParcela.setMovProduto(movProduto.getCodigo());
            produtoParcela.setValorPago(parcela.getValorParcela());
            produtoParcela.setMovProdutoVO(movProduto);
            movProdutoParcelaDao.incluir(produtoParcela);

            dataParcela = Uteis.obterDataFuturaParcela(data, (i));
        }

        return parcelas;

    }

    public Date obterDataDoProduto(LancamentoProdutoColetivoVO lancamento,
            MovParcelaVO parcela) {
        Date data = null;
        if (lancamento.getTipoData() || lancamento.getTipoContratoSemProduto()) {
            data = lancamento.getDataEspecifica();
        }
        if (lancamento.getTipoMes()) {
            Calendar calendar = Calendar.getInstance();
            int ano = Uteis.getAnoData(Calendario.hoje());
            int mesAtual = Uteis.getMesData(Calendario.hoje());
            int mes = lancamento.getMes().getCodigo() - 1;
            ano = mesAtual > mes ? ++ano : ano;
            calendar.set(ano, mes, 01);
            data = calendar.getTime();
        }
        if (lancamento.getTipoParcela()) {
            data = parcela.getDataVencimento();
        }
        return data;
    }

    public MovProdutoVO gerarMovProduto(ClienteVO cliente, LancamentoProdutoColetivoVO lancamento, Date data, Integer codigoContrato) throws Exception {
        MovProdutoVO movproduto = new MovProdutoVO();
        movproduto.setEmpresa(lancamento.getEmpresa());
        movproduto.setAnoReferencia(Uteis.getAnoData(data));
        movproduto.setDataLancamento(Calendario.hoje());
        movproduto.setMesReferencia(Uteis.getMesReferenciaData(data));
        movproduto.setPessoa(cliente.getPessoa());
        movproduto.setPrecoUnitario(lancamento.getValor());
        movproduto.setProduto(lancamento.getProdutoVO());
        movproduto.setRenovavelAutomaticamente(lancamento.getProdutoVO().getRenovavelAutomaticamente());
        movproduto.setDescricao(lancamento.getProdutoVO().getDescricao());
        movproduto.setQuantidade(1);
        movproduto.setQuitado(true);
        movproduto.setSituacao(lancamento.getValor() > 0.0 ? "EA" : "PG");
        movproduto.setTotalFinal(lancamento.getValor());
        movproduto.setValorDesconto(0.0);
        movproduto.setResponsavelLancamento(lancamento.getUsuario());
        movproduto.setLancamentoColetivo(lancamento.getCodigo());
        if(!UteisValidacao.emptyNumber(codigoContrato)){
            movproduto.getContrato().setCodigo(codigoContrato);
        }
        movProdutoDao.incluirSemCommit(movproduto);
        return movproduto;
    }

    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("**************************************", "postgres", "pactodb");
            LancamentoProdutoColetivoService servico = new LancamentoProdutoColetivoService(con);
            servico.lancamentoProdutoDao = new LancamentoProdutoColetivo(con);
            servico.lancarProdutoColetivo(1, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
