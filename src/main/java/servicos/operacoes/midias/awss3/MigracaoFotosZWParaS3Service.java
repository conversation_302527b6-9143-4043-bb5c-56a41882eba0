/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.operacoes.midias.awss3;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.io.File;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.operacoes.midias.MidiaService;
import servicos.propriedades.PropsService;

/**
 *
 * <AUTHOR>
 */
public class MigracaoFotosZWParaS3Service {

    private static void uploadFotosPessoaBanco(final String chave) throws Exception {
        DAO oamd = new DAO();
        try {
            Connection c = null;
            try {
                c = oamd.obterConexaoEspecifica(chave);
                final String sql = "select %s from pessoa where fotokey is null and length(foto) > 2048 %s";
                int records = SuperFacadeJDBC.contar(String.format(sql, "count(*)", ""), c);
                int cont = 0;
                int n = records ;
                for (int i = 0; i < n; i++) {
                    ResultSet rs = SuperFacadeJDBC.criarConsulta(String.format(sql, "codigo,foto", " order by codigo "), c);
                    while (rs.next()) {
                        byte[] foto = rs.getBytes("foto");
                        Integer codpessoa = rs.getInt("codigo");
                        final String ident = MidiaService.getInstance().uploadObjectFromByteArray(chave,
                                MidiaEntidadeEnum.FOTO_PESSOA, codpessoa.toString(), foto);
                        SuperFacadeJDBC.executarConsulta(String.format("update pessoa set fotokey = '%s' where codigo = %s", ident, codpessoa), c);
                        cont++;
                        Uteis.logar(null, String.format("%s/%s Enviado %s", cont, records, ident));
                    }
                }
            } catch (Exception e) {
                Uteis.logar(null, "Erro ao migrar fotos da chave " + chave + " -> " + e.getMessage());
            } finally {
                if (c != null) {
                    c.close();
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(MigracaoFotosZWParaS3Service.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private static void uploadFotosPacoteBanco(final String chave) throws Exception {
        DAO oamd = new DAO();
        try {
            Connection c = null;
            try {
                c = oamd.obterConexaoEspecifica(chave);
                final String sql = "select %s from sch_estudio.pacote where length(imagem) > 0 %s";
                ResultSet rs = SuperFacadeJDBC.criarConsulta(String.format(sql, "id_pacote,imagem", " order by id_pacote"), c);
                int records = SuperFacadeJDBC.contar(String.format(sql, "count(*)", ""), c);
                int cont = 1;
                while (rs.next()) {
                    byte[] foto = rs.getBytes("imagem");
                    Integer codigoPacote = rs.getInt("id_pacote");
                    final String ident = MidiaService.getInstance().uploadObjectFromByteArray(chave,
                            MidiaEntidadeEnum.FOTO_PACOTE_STUDIO, codigoPacote.toString(), foto);
                    cont++;
                    Uteis.logar(null, String.format("%s/%s Enviado %s", cont, records, ident));
                }
            } catch (Exception e) {
                Uteis.logar(null, "Erro ao migrar fotos da chave " + chave + " -> " + e.getMessage());
            } finally {
                if (c != null) {
                    c.close();
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(MigracaoFotosZWParaS3Service.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private static void uploadFotosModeloMensagemBanco(final String chave) throws Exception {
        DAO oamd = new DAO();
        try {
            Connection c = null;
            try {
                c = oamd.obterConexaoEspecifica(chave);
                final String sql = "select %s from modelomensagem where length(imagemmodelo) > 0 %s";
                ResultSet rs = SuperFacadeJDBC.criarConsulta(String.format(sql, "codigo,imagemmodelo", " order by codigo"), c);
                int records = SuperFacadeJDBC.contar(String.format(sql, "count(*)", ""), c);
                int cont = 1;
                while (rs.next()) {
                    byte[] foto = rs.getBytes("imagemmodelo");
                    Integer codigo = rs.getInt("codigo");
                    final String ident = MidiaService.getInstance().uploadObjectFromByteArray(chave,
                            MidiaEntidadeEnum.FOTO_MODELO_MENSAGEM, codigo.toString(), foto);
                    Uteis.logar(null, String.format("%s/%s Enviado %s", cont, records, ident));
                    cont++;
                }
            } catch (Exception e) {
                Uteis.logar(null, "Erro ao migrar fotos da chave " + chave + " -> " + e.getMessage());
            } finally {
                if (c != null) {
                    c.close();
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(MigracaoFotosZWParaS3Service.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private static void uploadFotosEmpresaBanco(final String chave) throws Exception {
        DAO oamd = new DAO();
        try {
            Connection c = null;
            try {
                c = oamd.obterConexaoEspecifica(chave);
                final String sql = "select %s from empresa ";
                ResultSet rs = SuperFacadeJDBC.criarConsulta(String.format(sql, "codigo,foto,fotoRelatorio,fotoEmail,fotoRedeSocial,homeBackground640x551,homeBackground320x276", " order by codigo"), c);
                int records = SuperFacadeJDBC.contar(String.format(sql, "count(*)", ""), c);
                int cont = 1;
                while (rs.next()) {
                    byte[] foto = rs.getBytes("foto");
                    byte[] fotoRelatorio = rs.getBytes("fotoRelatorio");
                    byte[] fotoEmail = rs.getBytes("fotoEmail");
                    byte[] fotoRedeSocial = rs.getBytes("fotoRedeSocial");
                    byte[] homeBackground640x551 = rs.getBytes("homeBackground640x551");
                    byte[] homeBackground320x276 = rs.getBytes("homeBackground320x276");
                    Integer codempresa = rs.getInt("codigo");
                    //
                    final String identFoto = MidiaService.getInstance().uploadObjectFromByteArray(chave,
                            MidiaEntidadeEnum.FOTO_EMPRESA, codempresa.toString(), foto);
                    final String identFotoRelatorio = MidiaService.getInstance().uploadObjectFromByteArray(chave,
                            MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, codempresa.toString(), fotoRelatorio);
                    final String identFotoEmail = MidiaService.getInstance().uploadObjectFromByteArray(chave,
                            MidiaEntidadeEnum.FOTO_EMPRESA_EMAIL, codempresa.toString(), fotoEmail);
                    final String identFotoRedeSocial = MidiaService.getInstance().uploadObjectFromByteArray(chave,
                            MidiaEntidadeEnum.FOTO_EMPRESA_REDESOCIAL, codempresa.toString(), fotoRedeSocial);
                    final String identHomeBackground640x551 = MidiaService.getInstance().uploadObjectFromByteArray(chave,
                            MidiaEntidadeEnum.FOTO_EMPRESA_HomeBackground640x551, codempresa.toString(), homeBackground640x551);
                    final String identHomeBackground320x276 = MidiaService.getInstance().uploadObjectFromByteArray(chave,
                            MidiaEntidadeEnum.FOTO_EMPRESA_HomeBackground320x276, codempresa.toString(), homeBackground320x276);
                    //
                    Uteis.logar(null, String.format("%s/%s Enviado %s", cont, records, identFoto));
                    Uteis.logar(null, String.format("%s/%s Enviado %s", cont, records, identFotoRelatorio));
                    Uteis.logar(null, String.format("%s/%s Enviado %s", cont, records, identFotoEmail));
                    Uteis.logar(null, String.format("%s/%s Enviado %s", cont, records, identFotoRedeSocial));
                    Uteis.logar(null, String.format("%s/%s Enviado %s", cont, records, identHomeBackground640x551));
                    Uteis.logar(null, String.format("%s/%s Enviado %s", cont, records, identHomeBackground320x276));
                    cont++;
                }
            } catch (Exception e) {
                Uteis.logar(null, "Erro ao migrar fotos da chave " + chave + " -> " + e.getMessage());
            } finally {
                if (c != null) {
                    c.close();
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(MigracaoFotosZWParaS3Service.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void migracaoFotosPessoaAmazonParaLocal(final String chave) throws Exception {
        DAO oamd = new DAO();
        try {
            Connection c = null;
            try {
                c = oamd.obterConexaoEspecifica(chave);
                final String sql = "select %s from pessoa where fotokey is not null %s";
                int records = SuperFacadeJDBC.contar(String.format(sql, "count(*)", ""), c);
                int cont = 0;
                int n = records / 500;
                int resto = records % 500;
                if (resto != 0) {
                    n += 1;
                }
                AmazonS3Client s3 = new AmazonS3Client();
                Uteis.logar(null, System.getProperty("os.name"));
                boolean windows = System.getProperty("os.name").toUpperCase().contains("WINDOWS");
                for (int i = 0; i < n; i++) {
                    ResultSet rs = SuperFacadeJDBC.criarConsulta(String.format(sql,
                            "codigo,fotokey", String.format(" order by codigo offset %s * 500  limit 500 ", i)), c);
                    while (rs.next()) {
                        Integer codpessoa = rs.getInt("codigo");
                        String ident = rs.getString("fotokey");
                        if (windows) {
                            ident = ident.replaceAll("\\*", "+");
                        }
                        final String newFotokey = MidiaService.getInstance().genKey(
                                chave, MidiaEntidadeEnum.FOTO_PESSOA, codpessoa.toString(), ".jpg");
                        File f = new File(PropsService.getPropertyValue(
                                PropsService.diretorioFotos) + newFotokey);
                        f.getParentFile().mkdirs();
                        if (!f.exists() || f.length() < 1024) {
                            byte[] foto = s3.downloadObjectAsByteArray(chave,
                                    MidiaEntidadeEnum.FOTO_PESSOA, codpessoa.toString(), null);
                            if (foto != null) {
                                FileUtilities.saveToFile(foto, f.getAbsolutePath());
                                SuperFacadeJDBC.executarConsulta(String.format("update pessoa set fotokey = '%s' where codigo = %s", 
                                        newFotokey, codpessoa), c);
                                Uteis.logar(null, String.format("%s/%s Baixado %s", cont, records, ident));
                            } else {
                                Uteis.logar(null, String.format("%s/%s NÃO BAIXOU %s", cont, records, ident));
                            }
                        }
                        cont++;
                    }
                }
            } catch (Exception e) {
                Uteis.logar(null, "Erro ao migrar fotos da chave " + chave + " -> " + e.getMessage());
            } finally {
                if (c != null) {
                    c.close();
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(MigracaoFotosZWParaS3Service.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void migracaoFotosPessoaLocalParaAWS(final String chave, final String directory) {
        File folder = new File(directory);
        AmazonS3Client s3 = new AmazonS3Client();
        File[] arqs = folder.listFiles();
        int n = arqs.length;
        int i = 1;
        for (final File fileEntry : arqs) {
            final String identifier = Uteis.desencriptarAWS(fileEntry.getName().replace(".jpg", ""));
            try {
                s3.uploadObject(chave, MidiaEntidadeEnum.FOTO_PESSOA, fileEntry);
                System.out.println(String.format("%s -> %s %s/%s", fileEntry.getName(),
                        identifier, i, n));
                i++;
            } catch (Exception ex) {
                Logger.getLogger(MigracaoFotosZWParaS3Service.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    public static void migracaoFotosLocalParaAWS(final String chave, final String directory) {
        File folder = new File(directory);
        AmazonS3Client s3 = new AmazonS3Client();
        File[] dirs = folder.listFiles();
        for (final File dir : dirs) {
            if (dir.isDirectory()) {
                Uteis.logar(null, "Directory String: " + dir.getName());
                String tipoString = Uteis.desencriptarZWInternal(dir.getName());
                Uteis.logar(null, "Tipo String: " + tipoString);               
                File[] arqs = dir.listFiles();
                int n = arqs.length;
                int i = 1;
                for (final File arq : arqs) {
                    final String identifier = Uteis.desencriptarZWInternal(arq.getName().replace(".jpg", ""));
                    try {
                        s3.uploadObject(chave, null, arq);
                        System.out.println(String.format("Arquivo: %s -> %s %s/%s", arq.getName(),
                                identifier, i, n));
                        i++;
                    } catch (Exception ex) {
                        Logger.getLogger(MigracaoFotosZWParaS3Service.class.getName()).log(Level.SEVERE, null, ex);
                    }
                }               
            }

        }
    }

    public static void main(String[] args) {
        Uteis.debug = true;
        DAO oamd = new DAO();
        try {
            List<Map> lista = new ArrayList<Map>();
            //testando...
            //args = new String[]{"up", "5740e94e6f7cb4ace7e54158d4ed1650", "C:\\opt\\zw-photos\\5740e94e6f7cb4ace7e54158d4ed1650\\97jxGKuba0+PGeIPqAkibQ=="};
            if (args.length > 1 && args[0].equals("down")) {
                final String chave = args[1];
                migracaoFotosPessoaAmazonParaLocal(chave);
            } else if (args.length > 1 && args[0].equals("up")) {
                final String chave = args[1];
                final String diretorio = args[2];
                migracaoFotosLocalParaAWS(chave, diretorio);
            } else {
                if (args.length > 0 && args[0].equals("todas")) {
                    lista = oamd.buscarListaEmpresas();
                } else if (args.length > 0 && args.length == 1) {
                    Uteis.logar(null, "Chave[] -> " + args[0]);
                    lista.add(oamd.buscarConEmpresa(args[0]));
                }
                for (Map map : lista) {
                    Uteis.logar(null, map.toString());
                    final String chave = (String) map.get("chave");
                    uploadFotosEmpresaBanco(chave);
                    uploadFotosPacoteBanco(chave);
                    uploadFotosPessoaBanco(chave);
                    uploadFotosModeloMensagemBanco(chave);
                }
                if (args.length == 0) {
                    final String chave = "sollos";
                    uploadFotosEmpresaBanco(chave);
                    uploadFotosPacoteBanco(chave);
                    uploadFotosPessoaBanco(chave);
                    uploadFotosModeloMensagemBanco(chave);
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(MigracaoFotosZWParaS3Service.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
}
