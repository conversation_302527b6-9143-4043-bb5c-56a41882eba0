package servlet.appGestor.Interface;

import servlet.appGestor.appGestorDados.DadosEmpresa;
import servlet.appGestor.appGestorDados.LancamentoJSON;
import servlet.appGestor.appGestorDados.LancamentoTotal;
import servlet.appGestor.appGestorDados.TipoContaJSON;

import java.sql.Connection;
import java.util.Date;
import java.util.List;

public interface MSFinanceiroService extends AutoCloseable {

    Connection getCon();

    void setCon(Connection con);

    List<TipoContaJSON> consultarContas(Date inicio, Date fim) throws Exception;

    List<LancamentoJSON> obterDetalhamentoVencidos(Integer empresa, Date inicio, Date fim, Boolean incluirCheques) throws Exception;

    List<LancamentoJSON> obterLancamentos(Integer empresa, Date inicio, Date fim, Boolean incluirCheques) throws Exception;

    List<LancamentoJSON> obterRecebiveis(Integer empresa, Date inicio, Date fim) throws Exception;

    LancamentoTotal obterSaldoGeral(Integer empresa, Date inicio, Date fim) throws Exception;

    List<LancamentoJSON> obterLancamentosFuturos(Integer empresa, Date inicio, Date fim, Boolean incluirCheques) throws Exception;

    DadosEmpresa obterDadosEmpresaPeriodo(String key, Integer empresa, Date mes) throws Exception;
}
