package servlet.arquitetura;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioTO;
import org.json.JSONObject;
import servicos.usuario.UsuarioService;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.stream.Collectors;

public class UsuarioLoginServlet extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");

        String method = request.getMethod();
        try {
            switch (method) {
                case "GET":
                    throw new Exception("Não suportado");
                case "POST":
                    InputStream inputStream = request.getInputStream();
                    BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream , StandardCharsets.UTF_8));
                    JSONObject body = new JSONObject(reader.lines().collect(Collectors.joining(System.lineSeparator())));

                    String chave = body.getString("chave");
                    Integer usuarioZw = body.getInt("usuario_zw");

                    try (Connection connection = new DAO().obterConexaoEspecifica(chave);
                         UsuarioService usuarioService = new UsuarioService(connection)) {

                        try {
                            UsuarioTO usuarioTO = usuarioService.validarUsuarioV4(chave, usuarioZw);
                            response.getWriter().append(usuarioTO.toJSON());
                        } catch (Exception e) {
                            JSONObject json = new JSONObject();
                            json.put("invalido", true);
                            response.getWriter().append(json.toString());
                        }
                    }

                    break;
                default:
                    processarErro(
                            new UnsupportedOperationException("Methodo não suportado para esse recurso"),
                            request,
                            response,
                            null);
            }
        } catch (Exception e) {
            processarErro(e, request, response, null);
        }
    }

}
