package servlet.cobranca;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import negocio.comuns.ConfiguracaoEmpresaTotemVO;
import negocio.comuns.TotemTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.enumerador.ConfigTotemEnum;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ConfiguracaoEmpresaTotem;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.utilitarias.Conexao;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Rodrigo Estulano
 * Date: 11/10/2024
 */
public class AutorizacaoCobrancaServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Cliente clienteDAO;
        AutorizacaoCobrancaCliente autoDAO;
        ConfiguracaoEmpresaTotem totemDAO;
        ConvenioCobranca convenioDAO;
        Connection con = null;
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setContentType("application/json");

            String method = request.getMethod();

            if (!method.equalsIgnoreCase("POST") && !method.equalsIgnoreCase("GET")) {
                throw new Exception("Método não suportado.");
            }

            //PARÂMETROS
            String key = request.getParameter("key");
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Chave não informada.");
            }
            int cliente = Integer.parseInt(request.getParameter("cliente"));
            OperadorasExternasAprovaFacilEnum operadoraCartao = OperadorasExternasAprovaFacilEnum.valueOf(request.getParameter("operadoraCartao"));
            String numeroCartao = request.getParameter("numeroCartao");
            String validadeCartao = request.getParameter("validadeCartao");
            if (validadeCartao == null || validadeCartao.length() != 5) {
                throw new Exception("Validade do cartão inválida.");
            }
            int convenioCobranca = Integer.parseInt(request.getParameter("convenioCobranca"));
            String cpfTitular = request.getParameter("cpfTitular");
            String titularCartao = request.getParameter("titularCartao");
            String cvv = request.getParameter("cvv");
            boolean naoValidarCvv = Boolean.parseBoolean(request.getParameter("naoValidarCvv"));
            boolean usarConfConvEmpresa = Boolean.parseBoolean(request.getParameter("usarConfConvEmpresa"));

            OrigemCobrancaEnum origemAutorizacao = null;
            if (request.getParameter("origemCobrancaEnum") != null && !UteisValidacao.emptyString(request.getParameter("origemCobrancaEnum"))) {
                origemAutorizacao = OrigemCobrancaEnum.obterPorCodigo(Integer.parseInt(request.getParameter("origemCobrancaEnum")));
            }

            con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(DaoAuxiliar.retornarAcessoControle(key).getCon());
            con.setAutoCommit(false);

            clienteDAO = new Cliente(con);
            autoDAO = new AutorizacaoCobrancaCliente(con);
            totemDAO = new ConfiguracaoEmpresaTotem(con);
            convenioDAO = new ConvenioCobranca(con);

            String mesValidade = "";
            String anoValidade = "";
            mesValidade = validadeCartao.substring(0, 2);
            anoValidade = validadeCartao.substring(3, 5);
            validadeCartao = mesValidade + "/" + (Integer.parseInt(anoValidade) + 2000);

            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(cliente, Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA);

            String where = " ativa = true and cliente = " + clienteVO.getCodigo() + " and tipoautorizacao = " + TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId();
            List<AutorizacaoCobrancaClienteVO> listaAutorizacaoBD = autoDAO.consulta(where, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            boolean possuiMaisDeUmCartaoCadastrado = false;
            if (!UteisValidacao.emptyList(listaAutorizacaoBD) && listaAutorizacaoBD.size() > 1) {
                possuiMaisDeUmCartaoCadastrado = true;
            }
            for (AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO : listaAutorizacaoBD) {
                String msgLogExclusao = "Exclusão de autorização de cobrança devido a inclusão de novo cartão.";
                if (origemAutorizacao != null) {
                    msgLogExclusao += " | Origem: " + origemAutorizacao.getDescricao();
                } else {
                    msgLogExclusao += " | Origem: Não foi possível identificar a origem";
                }
                autoDAO.alterarSituacaoAutorizacaoCobranca(false, autorizacaoCobrancaClienteVO, msgLogExclusao);
            }

            AutorizacaoCobrancaClienteVO autorizacao = new AutorizacaoCobrancaClienteVO();
            autorizacao.setCliente(clienteVO);
            autorizacao.setCpfTitular(cpfTitular);
            autorizacao.setNomeTitularCartao(titularCartao);
            autorizacao.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
            autorizacao.setOperadoraCartao(operadoraCartao);
            autorizacao.setNumeroCartao(numeroCartao);
            autorizacao.setValidadeCartao(validadeCartao);
            if (!UteisValidacao.emptyString(cvv)) {
                autorizacao.setCvv(cvv);
            }
            if (naoValidarCvv) {
                autorizacao.setRealizandoImportacao(true);
            }

            autorizacao.setTipoACobrar(clienteVO.getEmpresa().getTipoParcelasCobrarVendaSite());
            if (origemAutorizacao != null) {
                autorizacao.setOrigemCobrancaEnum(origemAutorizacao);
            }
            if (!possuiMaisDeUmCartaoCadastrado) {
                autorizacao.setOrdem(1);
            }
            if (usarConfConvEmpresa) {
                List<TotemTO> totemTOS = totemDAO.obterConfigs(clienteVO.getEmpresa().getCodigo(), key);
                if (!UteisValidacao.emptyList(totemTOS)) {
                    Map<ConfigTotemEnum, ConfiguracaoEmpresaTotemVO> mapa = totemTOS.get(0).getConfigs();
                    autorizacao.getConvenio().setCodigo(mapa.get(ConfigTotemEnum.CONVENIO_COBRANCA).getValorAsInt());
                }
            } else {
                autorizacao.getConvenio().setCodigo(convenioCobranca);
            }


            if (UteisValidacao.emptyNumber(autorizacao.getConvenio().getCodigo())) {
                //não encontrou nenhum convenio.
                //verificar se a academia só existe 1 convênio, se sim.. utilizar ele.
                List<ConvenioCobrancaVO> convenios = convenioDAO.consultarPorEmpresaSituacaoTipoAutorizacao(clienteVO.getEmpresa().getCodigo(),
                        SituacaoConvenioCobranca.ATIVO, TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (convenios.size() == 1) {
                    autorizacao.setConvenio(convenios.get(0));
                }
            }

            autoDAO.incluir(autorizacao);
            con.commit();
            response.getWriter().append("ok");
        } catch (Exception ex) {
            try {
                if (con != null) {
                    con.rollback();
                }
            } catch (Exception ex1) {
                ex1.printStackTrace();
            }
            response.getWriter().append("ERRO: " + ex.getMessage());
        } finally {
            clienteDAO = null;
            autoDAO = null;
            totemDAO = null;
            convenioDAO = null;
            try {
                if (con != null) {
                    con.setAutoCommit(true);
                    con.close();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

}
