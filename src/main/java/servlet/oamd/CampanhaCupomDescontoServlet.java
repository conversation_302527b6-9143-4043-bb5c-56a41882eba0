package servlet.oamd;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.security.LoginControle;
import negocio.oamd.RedeEmpresaVO;
import servicos.impl.oamd.OAMDService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * Created by ulisses on 27/05/2016.
 */
public class CampanhaCupomDescontoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json = "";
        try {
            LoginControle loginControle = (LoginControle)getAttribute(request, "LoginControle");
            Integer idFavorecido = loginControle.getEmpresa().getCodEmpresaFinanceiro();
            RedeEmpresaVO redeEmpresaVO = loginControle.getRedeEmpresaVO();

            OAMDService oamdService = new OAMDService();
            try {
                json = oamdService.consultarCampanhaCupomDescontoJSON(redeEmpresaVO, idFavorecido);
            } catch (Exception e) {
                json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
            } finally {
                oamdService = null;
            }
        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
        }
        out.println(json);
    }

}
