/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servlet.acesso;

import org.json.JSONObject;
import java.io.IOException;
import java.io.PrintWriter;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import servlet.arquitetura.SuperServlet;

/**
 *
 * <AUTHOR>
 */
public class AcessoClienteServlet extends SuperServlet {
        @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json;
        try {
            String cliente = obterParametroString(request.getParameter("cliente"));
            json = ff(request).getAcessoCliente().consultarUltimosAcessosJSON(Integer.valueOf(cliente), null);

        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
        }
        out.println(json);
        json = null;
    }
}
