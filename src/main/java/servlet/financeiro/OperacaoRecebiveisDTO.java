package servlet.financeiro;

import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Date;

public class OperacaoRecebiveisDTO {

    private String operacao;
    private Integer empresa;
    private String cnpj;
    private String matricula;
    private String documento;
    private Date dataLancamentoInicial;
    private Date dataLancamentoFinal;

    public OperacaoRecebiveisDTO(JSONObject json) {
        this.operacao = json.optString("operacao");
        this.empresa = json.optInt("empresa");
        this.cnpj = json.optString("cnpj");
        this.matricula = json.optString("matricula");
        this.documento =  json.optString("documento");

        SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
        try {
            this.dataLancamentoInicial = format.parse(String.valueOf(json.get("dataLancamentoInicial")));
        } catch (Exception e) {}
        try {
            this.dataLancamentoFinal = format.parse(String.valueOf(json.get("dataLancamentoFinal")));
        } catch (Exception e) {}
    }

    public String getOperacao() {
        if (operacao == null) {
            operacao = "";
        }
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public Integer getEmpresa() {
        if (empresa == null) {
            empresa = 0;
        }
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getMatricula() {
        if (matricula == null) {
            matricula = "";
        }
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getDocumento() {
        return documento;
    }

    public void setDocumento(String documento) {
        this.documento = documento;
    }

    public Date getDataLancamentoInicial() {
        return dataLancamentoInicial;
    }

    public void setDataLancamentoInicial(Date dataLancamentoInicial) {
        this.dataLancamentoInicial = dataLancamentoInicial;
    }

    public Date getDataLancamentoFinal() {
        return dataLancamentoFinal;
    }

    public void setDataLancamentoFinal(Date dataLancamentoFinal) {
        this.dataLancamentoFinal = dataLancamentoFinal;
    }
}
