package controle.arquitetura.security;

import controle.arquitetura.SuperControle;

public class QRCodeControle extends SuperControle {

    private boolean apresentarQRCode = false;
    private String urlQRCode = "";

    public void prepararQRCodeAppGestor() {
        try {
            LoginControle loginControle = getControlador(LoginControle.class);
            loginControle.validarSenhaQRCodeZWUI();
            setUrlQRCode(loginControle.getQrCodeUsuarioChave());
            apresentarQRCode = true;
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void prepararQRCodeAssinaturaDigital() {
        try {
            LoginControle loginControle = getControlador(LoginControle.class);
            loginControle.preparaDadosAppAssinatura();
            setUrlQRCode(loginControle.getUrlAssinaturaDigital());
            apresentarQRCode = true;
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void prepararQRCodeCartaoVacinacao() {
        try {
            LoginControle loginControle = getControlador(LoginControle.class);
            loginControle.preparaDadosAppCartaoVacinacao();
            setUrlQRCode(loginControle.getUrlAssinaturaDigital());
            apresentarQRCode = true;
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void prepararQRCodeFormularioParQ() {
        try {
            LoginControle loginControle = getControlador(LoginControle.class);
            loginControle.preparaDadosFormularioParQ();
            setUrlQRCode(loginControle.getUrlAssinaturaDigital());
            apresentarQRCode = true;
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public String getUrlQRCode() {
        return urlQRCode;
    }

    public void setUrlQRCode(String urlQRCode) {
        this.urlQRCode = urlQRCode;
    }

    public boolean isApresentarQRCode() {
        return apresentarQRCode;
    }

    public void setApresentarQRCode(boolean apresentarQRCode) {
        this.apresentarQRCode = apresentarQRCode;
    }
}
