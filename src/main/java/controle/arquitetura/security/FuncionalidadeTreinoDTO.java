package controle.arquitetura.security;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class FuncionalidadeTreinoDTO {
    private String nome;
    private boolean possuiFuncionalidade;

    public FuncionalidadeTreinoDTO(JSONObject json) {
        this.nome = json.getString("nome");
        this.possuiFuncionalidade = json.getBoolean("possuiFuncionalidade");
    }

    public static List<FuncionalidadeTreinoDTO> fromJsonArray(JSONArray json) {
        List<FuncionalidadeTreinoDTO> funcionalidadeTreinoDTOS = new ArrayList<>();
        for (int i = 0; i < json.length(); i++) {
            funcionalidadeTreinoDTOS.add(new FuncionalidadeTreinoDTO(json.getJSONObject(i)));
        }

        return funcionalidadeTreinoDTOS;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isPossuiFuncionalidade() {
        return possuiFuncionalidade;
    }

    public void setPossuiFuncionalidade(boolean possuiFuncionalidade) {
        this.possuiFuncionalidade = possuiFuncionalidade;
    }
}
