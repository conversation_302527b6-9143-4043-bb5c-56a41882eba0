/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.view;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.MenuControle;
import controle.arquitetura.MenuFuncionalidadeEnum;
import controle.arquitetura.security.LoginControle;
import controle.basico.MenuAcessoFacilControle;
import controle.financeiro.GestaoRecebiveisControle;
import negocio.comuns.utilitarias.Uteis;
import servicos.propriedades.PropsService;

import javax.faces.event.PhaseEvent;
import javax.faces.event.PhaseId;
import javax.faces.event.PhaseListener;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public class ViewPhaseListener implements PhaseListener {

    private static final String PAGINA_GESTAO_RECEBIVEIS = "gestaoRecebiveis";
    private static final String EXTENSAO_JSP = "jsp";
    private static final String PAGINA_GESTAO_LOTES = "gestaoLotesForm.jsp";
    private static final String CONTROLE_GESTAO_RECEBIVEIS = "GestaoRecebiveisControle";

    @Override
    public void afterPhase(PhaseEvent phaseEvent) {
        HttpServletRequest request = ((HttpServletRequest) phaseEvent.getFacesContext().getExternalContext().getRequest());
        processarHistoricoAcessosPaginas(request);

        if (PropsService.isVerifyControllersAfterPhase() && request.getRequestURI().endsWith(EXTENSAO_JSP)) {
            if (!request.getRequestURI().contains(PAGINA_GESTAO_RECEBIVEIS)
                    && !request.getRequestURI().contains(PAGINA_GESTAO_LOTES)) {
                Map<String, Object> sessionMap = phaseEvent.getFacesContext().getExternalContext().getSessionMap();
                GestaoRecebiveisControle controle = (GestaoRecebiveisControle) sessionMap.get(CONTROLE_GESTAO_RECEBIVEIS);
                if (controle != null) {
                    sessionMap.remove(CONTROLE_GESTAO_RECEBIVEIS);
                }
            }
        }
    }

    public static void filterMenu(HttpServletRequest httpServletRequest) {

//        TODO: Refatorar para utilizar moduloAberto, quando o menu legado for removido, porque no menu novo (zwui) é somente um menu por modulo.
//        if (httpServletRequest.getRequestURI().contains(".jsp")) {
//            LoginControle loginControle  = (LoginControle) httpServletRequest.getSession().getAttribute("LoginControle");
//            Optional<List<String>> menus;
//
//            if(loginControle != null && loginControle != null && loginControle.getModuloAberto() != null && loginControle.getMenuZwUi()){
//                menus = MenuFuncionalidadeEnum.getMenusZwUiFromURI(loginControle.getModuloAberto());
//            }else{
//                menus = MenuFuncionalidadeEnum.getMenusFromURI(
//                        httpServletRequest.getRequestURI());
//            }
//
//            httpServletRequest.setAttribute("grupoFuncionalidadeMenu", menus.get().toString());
//        }

        if (httpServletRequest.getRequestURI().contains(".jsp")) {
            Optional<List<String>> menus = MenuFuncionalidadeEnum.getMenusFromURI(
                    httpServletRequest.getRequestURI());
            if (!menus.get().isEmpty()) {
                MenuControle menuControle = (MenuControle) httpServletRequest.getSession().getAttribute("MenuControle");
                if (menuControle != null && !menuControle.getMenuZwUi()) {
                    if (httpServletRequest.getRequestURI().contains("telaModulo.jsp")) {
                        menus.get().remove("ADM-INICIO");
                    }
                    if (httpServletRequest.getRequestURI().contains("relatorios.jsp")) {
                        menus.get().remove("FIN-BI");
                        menus.get().add("FIN-INICIO");
                    }
                    if (menus.get().stream().anyMatch(menu -> menu.equals("ADM-CLUBE_VANTAGENS")) &&
                            httpServletRequest.getRequestURI().contains("clubeVantagens.jsp") ||
                            httpServletRequest.getRequestURI().contains("indicadores.jsp")) {
                        menus.get().remove("ADM-CLUBE_VANTAGENS");
                        menus.get().add("ADM-INICIO");
                    }
                }
                if (menuControle != null && menuControle.getMenuZwUi() &&
                        httpServletRequest.getRequestURI().contains("preCadastro.jsp") ||
                        httpServletRequest.getRequestURI().contains("clientes.jsp")) {
                    if(menus.get().stream().anyMatch(menu -> menu.equals("ADM-INICIO"))){
                        menus.get().add("PESSOAS-INICIO");
                        menus.get().remove("ADM-INICIO");
                    }
                }
                httpServletRequest.setAttribute("grupoFuncionalidadeMenu", menus.get().toString());
            }
        }

    }

    private void processarHistoricoAcessosModulos(){
        LoginControle loginControle = JSFUtilities.getControlador(LoginControle.class);
        MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);

        if(loginControle != null && menuControle != null){
            menuControle.setUltimoModuloAcessado(loginControle.getModuloAberto());
        }
    }

    private String getPageExtension(HttpServletRequest request){
        try {
            if(request.getPathInfo() == null || !request.getPathInfo().contains(".")){
                return null;
            }
            return request.getPathInfo().substring(request.getPathInfo().indexOf(".", request.getPathInfo().length() -4), request.getPathInfo().length());
        }catch (Exception e){
            Uteis.logarDebug("Erro ao recuperar extensão da página: " + e.getMessage());
            return null;
        }
    }

    private boolean isPaginaJsp(HttpServletRequest request){
        return getPageExtension(request) != null && Objects.equals(getPageExtension(request), ".jsp");
    }

    private void processarHistoricoAcessosPaginas(HttpServletRequest request){
        String from = request.getParameter("from");
        String fromAttribute = (String) request.getAttribute("from");
        MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);

        if(isPaginaJsp(request) && menuControle != null && (from == null || from.trim().isEmpty() || !from.equals("popup")) &&
                (fromAttribute == null || fromAttribute.trim().isEmpty() || !fromAttribute.equals("popup"))){
            String pagina = request.getPathInfo().replaceFirst("/", "");
            menuControle.setUltimaPaginaAcessada(pagina);
            menuControle.addUlimasPaginasAcessas(pagina);
        }
    }


    @Override
    public void beforePhase(PhaseEvent phaseEvent) {
        HttpServletRequest request = ((HttpServletRequest) phaseEvent.getFacesContext().getExternalContext().getRequest());
        HttpSession session = request.getSession();

        session.setAttribute("com.sun.faces.renderkit.ServerSideStateHelper.LogicalViewMap", session.getAttribute("com.sun.faces.renderkit.ServerSideStateHelper.LogicalViewMap"));

        session.setAttribute(LoginControle.NomeChaveUsuarioSessao, session.getAttribute(LoginControle.NomeChaveUsuarioSessao));
        HttpServletRequest req = request;

        if (req.getRequestURI().contains(".jsp")
                && JSFUtilities.isJSFContext()
                && JSFUtilities.getUsuarioLogado(req) != null
                && JSFUtilities.getUsuarioLogado(req).getCodigo() > 0) {

            MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);

            processarHistoricoAcessosModulos();

            if (menuControle != null
                    && (!req.getRequestURI().equals(menuControle.getLastURI()) || menuControle.isReprocessar())) {
                menuControle.setLastURI(req.getRequestURI());
                Object menu = req.getAttribute("grupoFuncionalidadeMenu");
                if(menu != null){
                    String menuNomes = menu.toString().replaceAll("\\[", "").replaceAll("\\]", "");

                    if (menuControle != null && menuControle.getMenuZwUi() &&
                            request.getRequestURI().contains("preCadastro.jsp") ||
                            request.getRequestURI().contains("clientes.jsp")) {
                        if(menuNomes.contains("ADM-INICIO")){
                            menuNomes = "PESSOAS-INICIO";
                        }
                    }

                    if (menuNomes != null){
                        menuControle.prepararMenuExibir(menuNomes);
                    }

                    menuControle.setReprocessar(false);
                }

                try {
                    MenuAcessoFacilControle acessoFacilControle = JSFUtilities.getControlador(MenuAcessoFacilControle.class);
                    if (acessoFacilControle != null)
                        acessoFacilControle.inicializar();
                } catch (Exception e) {
                    //Uteis.logar(e.getMessage());
                }
            }

        }

    }

    @Override
    public PhaseId getPhaseId() {
        return PhaseId.RENDER_RESPONSE;
        //To change body of implemented methods use File | Settings | File Templates.
    }
}
