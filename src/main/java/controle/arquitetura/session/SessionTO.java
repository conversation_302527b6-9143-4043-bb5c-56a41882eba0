/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.session;

import annotations.arquitetura.Lista;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.utilitarias.Uteis;

import javax.servlet.http.HttpSession;
import java.lang.reflect.Field;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SessionTO extends SuperVO {

    private static final long serialVersionUID = -7110388073628773835L;
    boolean expandido = false;
    private String id;
    private Date dataCriacao;
    @Lista
    private List<ObjetoGenerico> objetos;
    private transient HttpSession session;
    private int procId = 0;
    private String empresa;
    private String usuario;
    private String ip;
    private String whois = "";
    private String browser = "";
    private String screenH = "";
    private String screenW = "";
    private String chave;
    private Integer codigoEmpresa;
    private String cnpj;
    private String uf;

    public SessionTO() {
    }

    public SessionTO(final String id, final Date dataCriacao, final HttpSession session) {
        this.id = id;
        this.dataCriacao = dataCriacao;
        this.session = session;
    }

    public JSONObject toJSON() {
        if (this == null) {
            return new JSONObject();
        }
        JSONObject o = new JSONObject();
        Field[] fields = this.getClass().getDeclaredFields();
        try {
            for (Field field : fields) {
                o.put(field.getName(), field.get(this));
            }
        } catch (Exception ignored) {
        }
        return o;
    }

    public Date getUltAcesso() {
        try {
            return session != null && session.getId() != null && session.getLastAccessedTime() > 0 ? new Date(session.getLastAccessedTime()) : null;
        } catch (Exception ignored) {
        }
        return null;
    }

    public String getUltAcessoApresentar() {
        return Uteis.getDataAplicandoFormatacao(getUltAcesso(), "yyyy-MM-dd HH:mm:ss");
    }

    public String getLastURI() {
        return session != null && session.getId() != null && session.getLastAccessedTime() > 0 ? (String) session.getAttribute("lastURI") : null;
    }

    public Date getDataCriacao() {
        return dataCriacao;
    }

    public String getDataCriacaoApresentar() {
        return Uteis.getDataAplicandoFormatacao(getDataCriacao(), "yyyy-MM-dd HH:mm:ss");
    }

    public void setDataCriacao(Date dataCriacao) {
        this.dataCriacao = dataCriacao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<ObjetoGenerico> getObjetos() {
        return objetos;
    }

    public void setObjetos(List<ObjetoGenerico> objetos) {
        this.objetos = objetos;
    }

    public HttpSession getSession() {
        return session;
    }

    public void setSession(HttpSession session) {
        this.session = session;
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public boolean isExpandido() {
        return expandido;
    }

    public void setExpandido(boolean expandido) {
        this.expandido = expandido;
    }

    public int getLength() {
        return objetos.toArray().length;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getWhois() {
        return whois;
    }

    public void setWhois(String whois) {
        this.whois = whois;
    }

    public int getProcId() {
        return procId;
    }

    public void setProcId(int procId) {
        this.procId = procId;
    }

    public String getBrowser() {
        return browser;
    }

    public void setBrowser(String browser) {
        this.browser = browser;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getUf() {
        return uf;
    }

    public void setUf(String uf) {
        this.uf = uf;
    }

    public String getScreenH() {
        return screenH;
    }

    public void setScreenH(String screenH) {
        this.screenH = screenH;
    }

    public String getScreenW() {
        return screenW;
    }

    public void setScreenW(String screenW) {
        this.screenW = screenW;
    }

    public boolean isInvalid() {
        long estimateInactiveTime = Uteis.somarCampoData(getUltAcesso(), Calendar.MINUTE, 120).getTime();
        return (UteisValidacao.emptyString(getEmpresa()) && UteisValidacao.emptyString(getUsuario()))
                || (System.currentTimeMillis() > estimateInactiveTime);
    }
}
