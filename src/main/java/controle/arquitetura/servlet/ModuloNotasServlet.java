/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.servlet;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import org.json.JSONArray;
import org.json.JSONObject;
import negocio.comuns.financeiro.NotasFiscaisTO;
import negocio.comuns.nfe.*;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.nfe.EmpresaNFe;
import negocio.facade.jdbc.nfe.NotaFiscalDeServico;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Felipe
 */
public class ModuloNotasServlet extends HttpServlet {

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        if (request.getParameter("key") != null && request.getParameter("cnpj") != null) {

            String cnpj = request.getParameter("cnpj");
            String key = request.getParameter("key");

            JSONObject jsonObject = new JSONObject();

            try {
                DAO dao = new DAO();
                Connection con = dao.obterConexaoEspecifica("");

                NotaFiscalDeServico nfseDao = new NotaFiscalDeServico(con);
                JSONObject retorno = nfseDao.consultarInformacoesEmpresa(key, cnpj);
                nfseDao = null;

                jsonObject.put("return", retorno);

                response.setContentType("application/json");
                response.getOutputStream().print(jsonObject.toString());
            } catch (Exception ex) {
                try {
                    jsonObject.put("error", ex.getMessage());
                } catch (Exception ignored) {
                }
                response.getOutputStream().print(jsonObject.toString());
                Logger.getLogger(NFSeServlet.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">

    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
