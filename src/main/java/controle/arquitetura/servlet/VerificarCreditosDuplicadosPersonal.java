package controle.arquitetura.servlet;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import servicos.integracao.IntegracaoCadastrosWSConsumer;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Joao Alcides on 06/12/2016.
 */
public class VerificarCreditosDuplicadosPersonal {



    public static void procurarCreditosDuplicados() throws SQLException, Exception {
        Connection oamd = DriverManager.getConnection("************************************", "postgres", "pactodb");
        Connection oamd12 = DriverManager.getConnection("jdbc:postgresql://*********:5432/OAMD2", "postgres", "pactodb");
        ResultSet rsOAMD2 = SuperFacadeJDBC.criarConsulta(" SELECT * FROM empresa", oamd);
        while (rsOAMD2.next()) {
            String nomeBD = rsOAMD2.getString("nomeBD");
            try {
                Connection conBancoTreino = DriverManager.getConnection("********************************/" + nomeBD,
                        "zillyonweb", "pactodb");


            }catch (Exception e){
                System.out.println("BANCO: "+nomeBD+" - "+e.getMessage());
            }
        }

    }


    public static void procurarPorRecibosInexistentes(Connection conBancoTreino, Connection conBancoZW, boolean deletar) throws Exception {
        ResultSet rsCreditos = SuperFacadeJDBC.criarConsulta(" SELECT p.nome, recibozw,professor_codigo, unidades,datalancamento, c.codigo FROM creditopersonal c" +
                " inner join professorsintetico p on p.codigo = c.professor_codigo and p.ativo " +
                "where recibozw is not null and datalancamento > '2016-01-01' order by c.codigo desc", conBancoTreino);
        while(rsCreditos.next()){
            ResultSet rsRecibos = SuperFacadeJDBC.criarConsulta(" SELECT codigo FROM recibopagamento " +
                    "where codigo = "+rsCreditos.getInt("recibozw"), conBancoZW);
            if(!rsRecibos.next()){
                if(deletar){
                    SuperFacadeJDBC.executarConsulta("UPDATE creditopersonal SET saldopersonal = (saldopersonal - " +rsCreditos.getInt("unidades")
                            +") where codigo > " +rsCreditos.getInt("codigo")
                            +" and professor_codigo = " +rsCreditos.getInt("professor_codigo"), conBancoTreino);
                    SuperFacadeJDBC.executarConsulta("DELETE FROM creditopersonal WHERE codigo = "+rsCreditos.getInt("codigo"), conBancoTreino);
                    System.out.println("RECIBO NÃO ENCONTRADO: "+rsCreditos.getInt("recibozw")
                            +" - PROFESSOR: "+rsCreditos.getString("nome")
                            +" - UNIDADES: "+rsCreditos.getInt("unidades")
                            +" - HORA: "+ Uteis.getDataAplicandoFormatacao(rsCreditos.getTimestamp("datalancamento"),"dd/MM/yyyy HH:mm")
                            +" \n DELETEI O CREDITO");
                }else{
                    System.out.println("RECIBO NÃO ENCONTRADO: "+rsCreditos.getInt("recibozw")
                            +" - PROFESSOR: "+rsCreditos.getString("nome")
                            +" - UNIDADES: "+rsCreditos.getInt("unidades")
                            +" - HORA: "+ Uteis.getDataAplicandoFormatacao(rsCreditos.getTimestamp("datalancamento"),"dd/MM/yyyy HH:mm"));
                }


            }
        }
    }

    public static void main(String ... args) throws Exception {
        Map<String, Map<String, String>> unidades = new HashMap<String, Map<String, String>>();
        Map<String, String> park = new HashMap<String, String>();
        park.put("zw", "**********************************************************************");
        park.put("tr", "jdbc:postgresql://*********:5432/bdmuscuniquepark");
        unidades.put("park", park);
        Map<String, String> shop = new HashMap<String, String>();
        shop.put("zw", "**********************************************************************shop");
        shop.put("tr", "jdbc:postgresql://*********:5432/bdmuscuniqueparkshop");
        unidades.put("shop", shop);
        Map<String, String> sudoeste = new HashMap<String, String>();
        sudoeste.put("zw", "**********************************************************************sudoeste");
        sudoeste.put("tr", "jdbc:postgresql://*********:5432/bdmuscuniqueparksudoeste");
        unidades.put("sudoeste", sudoeste);

        for(String u : unidades.keySet()){
            System.out.println(u);
            Connection conzw = DriverManager.getConnection(unidades.get(u).get("zw"), "postgres", "pactodb");
            Connection contr = DriverManager.getConnection(unidades.get(u).get("tr"), "postgres", "pactodb");
            procurarPorRecibosInexistentes(contr, conzw, true);
        }
//        procurarRecibosNaoExistemZW();
//        procurarQuemUsaPersonal();
    }


    public static void procurarRecibosNaoExistemZW(String chave) {
        try {
            Connection oamd = DriverManager.getConnection("************************************", "postgres", "pactodb");

            Connection oamd12 = DriverManager.getConnection("jdbc:postgresql://*********:5432/OAMD2", "postgres", "pactodb");
            Connection oamd22 = DriverManager.getConnection("**************************************", "postgres", "pactodb");

            ResultSet rsOAMD = SuperFacadeJDBC.criarConsulta(" SELECT * FROM empresa where chave = '"+chave+"'", oamd);
            while (rsOAMD.next()) {
                try {
                    System.out.println("BD: " + rsOAMD.getString("nomeBD"));
                    Connection conBancoTreino = null;
                    Connection conBancoZW = DriverManager.getConnection("jdbc:postgresql://"+rsOAMD.getString("hostBD")
                                    +":5432/" + rsOAMD.getString("nomeBD"),
                            rsOAMD.getString("userBD"), rsOAMD.getString("passwordBD"));
                    ResultSet rsOamdTreino = SuperFacadeJDBC.criarConsulta("SELECT * FROM empresa WHERE chave = '"
                            + chave + "'", oamd12);
                    if (rsOamdTreino.next()) {
                        conBancoTreino = DriverManager.getConnection("jdbc:postgresql://*********:5432/" + rsOamdTreino.getString("nomeBD"),
                                rsOamdTreino.getString("userBD"), rsOamdTreino.getString("passwordBD"));
                    } else {
                        rsOamdTreino = SuperFacadeJDBC.criarConsulta("SELECT * FROM empresa WHERE chave = '"
                                + chave + "'", oamd22);
                        if (rsOamdTreino.next()) {
                            conBancoTreino = DriverManager.getConnection("jdbc:postgresql://*********:5432/" + rsOamdTreino.getString("nomeBD"),
                                    rsOamdTreino.getString("userBD"), rsOamdTreino.getString("passwordBD"));
                        } else {
                            throw new Exception("Não encontrei o banco do treino desse aluno");
                        }
                    }

                    procurarPorRecibosInexistentes(conBancoTreino, conBancoZW, false);

                } catch (Exception e) {
                    System.err.println("erro " + e.getMessage());
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static void procurarQuemUsaPersonal() {
        try {
            Connection oamd12 = DriverManager.getConnection("jdbc:postgresql://*********:5432/OAMD2", "postgres", "pactodb");
            Connection oamd22 = DriverManager.getConnection("**************************************", "postgres", "pactodb");
            Connection[] cons = new Connection[]{oamd12, oamd22};
            for(Connection con : cons){
                ResultSet rsOamdTreino = SuperFacadeJDBC.criarConsulta("SELECT * FROM empresa ", con);
                while (rsOamdTreino.next()) {
                    try {
                        Connection conBancoTreino = DriverManager.getConnection("jdbc:postgresql://"+rsOamdTreino.getString("hostBD")
                                        +":5432/" + rsOamdTreino.getString("nomeBD"),
                                rsOamdTreino.getString("userBD"), rsOamdTreino.getString("passwordBD"));
                        ResultSet resultSet = SuperFacadeJDBC.criarConsulta("SELECT count(*) as cont FROM creditopersonal", conBancoTreino);
                        if(resultSet.next()){
                            if(resultSet.getInt("cont") > 0){
                                System.out.println(rsOamdTreino.getString("nomeBD") + " - " +resultSet.getInt("cont"));
                                procurarRecibosNaoExistemZW(rsOamdTreino.getString("chave"));
                            }
                        }
                    }catch (Exception e){
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
