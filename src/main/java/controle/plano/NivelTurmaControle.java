package controle.plano;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.facade.jdbc.plano.NivelTurma;
import negocio.comuns.utilitarias.*;
import negocio.comuns.plano.*;

import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;

import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.faces.event.ActionEvent;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas
 * nivelTurmaForm.jsp nivelTurmaCons.jsp) com as funcionalidades da classe <code>NivelTurma</code>.
 * Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see NivelTurma
 * @see NivelTurmaVO
 */
public class NivelTurmaControle extends SuperControle {

    private NivelTurmaVO nivelTurmaVO;
    private String msgAlert;
    protected List listaSelectItemCodigoMgb;
    private Boolean mgb = false;

    /**
     * Interface <code>NivelTurmaInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */


    public NivelTurmaControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        verificarIntegracao();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    private void verificarIntegracao(){
        try {
            mgb = getFacade().getMgbService().integradoMgb(getEmpresaLogado().getCodigo());
        }catch (Exception e){
            mgb = false;
        }
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>NivelTurma</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setNivelTurmaVO(new NivelTurmaVO());
        limparMsg();
        try {
            if (getFacade().getMgbService().integradoMgb(getEmpresaLogado().getCodigo())) {
                montarSelectItemNivelMgb();
            }
        } catch (Exception e) {
            inicializarListasSelectItemTodosComboBoxVazias();
        }
        return "editar";
    }

    private void inicializarListasSelectItemTodosComboBoxVazias() {
        List objs = new ArrayList();
        objs.add(new SelectItem(0, ""));

        if (getListaSelectItemCodigoMgb() == null) {
            setListaSelectItemCodigoMgb(objs);
        }
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>NivelTurma</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            NivelTurmaVO obj = getFacade().getNivelTurma().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setNovoObj(false);
            obj.registrarObjetoVOAntesDaAlteracao();
            setNivelTurmaVO(obj);
            if (getFacade().getMgbService().integradoMgb(getEmpresaLogado().getCodigo())) {
                montarSelectItemNivelMgb();
            }
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>NivelTurma</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar() {
        try {
            if (nivelTurmaVO.isNovoObj().booleanValue()) {
                getFacade().getNivelTurma().incluir(nivelTurmaVO);
                incluirLogInclusao();
            } else {
                getFacade().getNivelTurma().alterar(nivelTurmaVO);
                incluirLogAlteracao();
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP NivelTurmaCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getNivelTurma().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getNivelTurma().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>NivelTurmaVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getNivelTurma().excluir(nivelTurmaVO);
            incluirLogExclusao();
            setNivelTurmaVO(new NivelTurmaVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if (e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"nivelturma\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"nivelturma\" violates foreign key")) {
                setMensagemDetalhada("Este Nível de Turma não pode ser excluído, pois está sendo utilizado!");
            } else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("codigo", "Código"));

        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        setSucesso(false);
        setErro(false);
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public NivelTurmaVO getNivelTurmaVO() {
        return nivelTurmaVO;
    }

    public void setNivelTurmaVO(NivelTurmaVO nivelTurmaVO) {
        this.nivelTurmaVO = nivelTurmaVO;
        if (nivelTurmaVO.getCodigoMgb() == null) {
            nivelTurmaVO.setCodigoMgb(new NivelTurmaVO().getCodigoMgb());

        }
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getNivelTurma().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro,null );

    }

    public void incluirLogInclusao() throws Exception {
        try {
            nivelTurmaVO.setObjetoVOAntesAlteracao(new NivelTurmaVO());
            nivelTurmaVO.setNovoObj(true);
            registrarLogObjetoVO(nivelTurmaVO, nivelTurmaVO.getCodigo(), "NIVELTURMA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("NIVELTURMA", nivelTurmaVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE NIVELTURMA", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        nivelTurmaVO.setNovoObj(new Boolean(false));
        nivelTurmaVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void incluirLogExclusao() throws Exception {
        try {
            nivelTurmaVO.setObjetoVOAntesAlteracao(new NivelTurmaVO());
            nivelTurmaVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(nivelTurmaVO, nivelTurmaVO.getCodigo(), "NIVELTURMA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("NIVELTURMA", nivelTurmaVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE NIVELTURMA ", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     *
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(nivelTurmaVO, nivelTurmaVO.getCodigo(), "NIVELTURMA", 0);
            nivelTurmaVO.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            registrarLogErroObjetoVO("NIVELTURMA", nivelTurmaVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE NIVELTURMA ", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        nivelTurmaVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = nivelTurmaVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), nivelTurmaVO.getCodigo(), 0);
    }

    public void realizarConsultaLogObjetoGeral() {
        nivelTurmaVO = new NivelTurmaVO();
        realizarConsultaLogObjetoSelecionado();
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }

    public void confirmarExcluir() {
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Nível de Turma",
                "Deseja excluir o Nível de Turma?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

    public Boolean getIntegradoMgb(){
        try {
            return getFacade().getMgbService().integradoMgb(getEmpresaLogado().getCodigo());
        }catch (Exception e){
            return false;
        }
    }

    public void montarSelectItemNivelMgb() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem(0, ""));

        JSONObject jsonObject = getFacade().getMgbService().consultarNiveis(getEmpresaLogado().getCodigo());
        JSONArray lista = new JSONArray(jsonObject.get("data").toString());

        for (int e = 0; e < lista.length(); e++) {
            JSONObject obj = lista.getJSONObject(e);
            NivelTurmaVO nivelTurmaVO = new NivelTurmaVO();
            nivelTurmaVO.setCodigoMgb(obj.getString("publicId"));
            nivelTurmaVO.setDescricao(obj.getString("namePTBR"));
            ;
            objs.add(new SelectItem(nivelTurmaVO.getCodigoMgb(), nivelTurmaVO.getDescricao()));
        }
        setListaSelectItemCodigoMgb(objs);
    }

    public Boolean getMgb() {
        return mgb;
    }

    public void setMgb(Boolean mgb) {
        this.mgb = mgb;
    }

    public List getListaSelectItemCodigoMgb() {
        if(listaSelectItemCodigoMgb == null){
            listaSelectItemCodigoMgb = new ArrayList();
        }
        return listaSelectItemCodigoMgb;
    }

    public void setListaSelectItemCodigoMgb(List listaSelectItemCodigoMgb) {
        this.listaSelectItemCodigoMgb = listaSelectItemCodigoMgb;
    }
}
