package controle.financeiro;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.ModuloAberto;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.arquitetura.security.LoginControle;
import controle.basico.ClienteControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.financeiro.CartaoCreditoTO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeTO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.LoteVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaDetalhamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.PagamentoMovParcelaVO;
import negocio.comuns.financeiro.ReciboDevolucaoVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.financeiro.ProdutosPagosServico;
import negocio.facade.jdbc.financeiro.ReciboDevolucao;
import org.json.JSONArray;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class EdicaoPagamentoControle extends SuperControleRelatorio {

    private static final int CPF = 1;
    private static final int CNPJ = 2;
    private String banco;
    private ChequeVO cheque;
    private ClienteVO cliente;
    private ColaboradorVO colaborador;
    private EmpresaVO empresaVO;
    private ReciboPagamentoVO recibo;
    private MovPagamentoVO pagamento;
    private List<ChequeVO> cheques;
    private List<ChequeVO> chequesAuxiliar;
    private List<MovParcelaVO> parcelas;
    private List<MovPagamentoVO> pagamentos;
    private List<FormaPagamentoVO> formasPagamento;
    private List<SelectItem> listaSelectItemBanco;
    private List<SelectItem> listaSelectItemNrParcelaCartao;
    private List<SelectItem> listaSelectItemOperadoraCartaoCredito;
    private List<SelectItem> listaSelectItemOperadoraCartaoDebito;
    private List<SelectItem> listaSelectItemConvenioCobranca;
    private boolean apresentarCPF;
    private boolean emDinheiro;
    private boolean emCartaoCredito;
    private boolean emCartaoDebito;
    private boolean emBoleto;
    private boolean listaAlterada;
    private double residuo;
    private double totalOutras;
    private double totalCheques;
    private double totalDevolver;
    private double totalOriginal;
    private String abaSelecionada;
    private String origem = "";
    private int mudarCampoCpf = CPF;
    private String tipoEdicao = "";
    private Boolean readOnly = Boolean.FALSE;
    private String tituloPagina = "Edição de Pagamento";
    private Date dataLancamentoCartaoCredito;
    private Date dataLancamentoCartaoDebito;
    private Date dataNovaParcela = Calendario.hoje();
    private UsuarioVO userPermissao = new UsuarioVO();
    private List<BancoVO> listaBancos = null;
    private boolean processado = false;
    private List<Integer> recibosEnvolvidos;
    private ConfiguracaoSistemaVO configuracaoSistema;
    
    private String retorno = "";

    private String[] displayIdentificadorFront;
    private int codigoEdicao = 0;
    private String situacaoContratoRecibo;
    
    public EdicaoPagamentoControle() throws Exception {
        novo();
        inicializarConfiguracaoSistema();
        identificacaoPessoalInternacional();
    }

    public void inicializarConfiguracaoSistema(){
        try {
            setConfiguracaoSistema((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
        } catch (Exception e) {
            throw e;
        }
    }

    public final void novo() throws Exception {
        setUserPermissao(getFacade().getUsuario().consultarPorCodigo(getUsuarioLogado().getCodigo(), Uteis.NIVELMONTARDADOS_LOGIN));
        setBanco("");
        setCheque(new ChequeVO());
        setRecibo(new ReciboPagamentoVO());
        setPagamento(new MovPagamentoVO());
        getPagamento().setOperadoraCartaoVO(new OperadoraCartaoVO());
        getPagamento().getOperadoraCartaoVO().setCodigo(0);
        setCheques(new ArrayList<ChequeVO>());
        setParcelas(new ArrayList<MovParcelaVO>());
        setPagamentos(new ArrayList<MovPagamentoVO>());
        setApresentarCPF(true);
        setListaAlterada(false);
        setTotalCheques(0.0);
        setResiduo(0.0);
        setTotalOutras(0.0);
        setListaSelectItemNrParcelaCartao(new ArrayList<SelectItem>());
        montarListaSelectItemBanco();
        montarListaSelectItemOperadoraCartaoCredito();
        montarListaSelectItemOperadoraCartaoDebito();
        montarListaSelectItemConvenioCobranca();
        initFormasPagamento();
        processado = false;
        retorno = "";
        codigoEdicao = 0;
        setSituacaoContratoRecibo("");
    }

    /**
     * prepara lista de bancos para ser exibida pelos cheques
     *
     * @throws Exception
     */
    public void montarListaSelectItemBanco() throws Exception {
        setListaBancos(getFacade().getBanco().consultarPorNome("", false, Uteis.NIVELMONTARDADOS_TODOS));
        setListaSelectItemBanco(new ArrayList<SelectItem>());
        getListaSelectItemBanco().add(new SelectItem(0, ""));
        Iterator i = getListaBancos().iterator();
        while (i.hasNext()) {
            BancoVO obj = (BancoVO) i.next();
            getListaSelectItemBanco().add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }
    }

    /**
     * prepara lista de operadoras de cartao para ser exibida pelo pagamento com
     * cartao
     *
     * @throws Exception
     */
    public void montarListaSelectItemOperadoraCartaoCredito() throws Exception {
        setListaSelectItemOperadoraCartaoCredito(new ArrayList<SelectItem>());
        List resultadoConsulta = getFacade().getOperadoraCartao().consultarPorTipo(true, false, Uteis.NIVELMONTARDADOS_TODOS);
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            OperadoraCartaoVO obj = (OperadoraCartaoVO) i.next();
            getListaSelectItemOperadoraCartaoCredito().add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
    }

    /**
     * prepara lista de operadoras de cartao para ser exibida pelo pagamento com
     * cartao
     *
     * @throws Exception
     */
    public void montarListaSelectItemOperadoraCartaoDebito() throws Exception {
        setListaSelectItemOperadoraCartaoDebito(new ArrayList<SelectItem>());
        List resultadoConsulta = getFacade().getOperadoraCartao().consultarPorTipo(false, false, Uteis.NIVELMONTARDADOS_TODOS);
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            OperadoraCartaoVO obj = (OperadoraCartaoVO) i.next();
            getListaSelectItemOperadoraCartaoDebito().add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
    }

    /**
     * prepara lista de convenios de cobrança para serem exibidas pelo pagamento
     * com boleto
     *
     * @throws Exception
     */
    public void montarListaSelectItemConvenioCobranca() throws Exception {
        List resultadoConsulta = getFacade().getConvenioCobranca().consultarTodos(Uteis.NIVELMONTARDADOS_TODOS);
        setListaSelectItemConvenioCobranca(new ArrayList<SelectItem>());
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            ConvenioCobrancaVO obj = (ConvenioCobrancaVO) i.next();
            getListaSelectItemConvenioCobranca().add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
    }

    /**
     * prepara lista de qtde de vezes que um cartao pode ser dividido
     *
     * @return
     * @throws Exception
     */
    public void montarListaNrParcelasCartao(OperadoraCartaoVO obj) throws Exception {
        setListaSelectItemNrParcelaCartao(new ArrayList<SelectItem>());
        getListaSelectItemNrParcelaCartao().add(new SelectItem(0, ""));
        for (int i = 1; i <= obj.getQtdeMaxParcelas(); i++) {
            if (i == 1) {
                getListaSelectItemNrParcelaCartao().add(new SelectItem(i, "A Vista"));
            } else {
                getListaSelectItemNrParcelaCartao().add(new SelectItem(i, i + " vezes"));
            }
        }
    }

    public void atualizarOperadoraCartao() {
        try {
            if (pagamento.getOperadoraCartaoVO().getCodigo() == 0) {
                pagamento.getOperadoraCartaoVO().setQtdeMaxParcelas(0);
            } else {
                pagamento.setOperadoraCartaoVO(getFacade().getOperadoraCartao().consultarPorChavePrimaria(
                        pagamento.getOperadoraCartaoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
            // monta a lista de parcelas de acordo com a operador
            montarListaNrParcelasCartao(pagamento.getOperadoraCartaoVO());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * método para montar os dados relativos às formas de pagamento que existem
     * no BD
     */
    private void initFormasPagamento() throws Exception {
        setEmDinheiro(false);
        setEmCartaoCredito(false);
        setEmCartaoDebito(false);
        setEmBoleto(false);
        setFormasPagamento(getFacade().getFormaPagamento().
                consultarPorDescricaoTipoFormaPagamento("", false, true, true, true, Uteis.NIVELMONTARDADOS_MINIMOS));
        for (FormaPagamentoVO fp : getFormasPagamento()) {
            if (fp.getTipoFormaPagamento().equals("AV")) // se dinheiro
            {
                setEmDinheiro(true);
            } else if (fp.getTipoFormaPagamento().equals("CA")) // se cartao credito
            {
                setEmCartaoCredito(true);
            } else if (fp.getTipoFormaPagamento().equals("CD")) // se cartao debito
            {
                setEmCartaoDebito(true);
            } else if (fp.getTipoFormaPagamento().equals("BB")) // se boleto
            {
                setEmBoleto(true);
            }
        }
        setAbaSelecionada(isEmDinheiro() ? "abaDinheiro"
                : (isEmCartaoCredito() ? "abaCartaoCredito"
                : (isEmCartaoDebito() ? "abaCartaoDebito"
                : (isEmBoleto() ? "abaBoleto" : ""))));
    }

    /**
     * execução do botão voltar
     *
     * @return
     */
    public String voltar() {
        try {
            liberarBackingBeanMemoria("EdicaoPagamentoControle");
            if (origem.equals("editarCliente") || origem.equals("editarClienteNovo")) {
                abrirTelaClienteRedirect(getCliente().getMatricula());
                return "";
            }
            if(origem.equals("gestaoRecebiveis")){
                setarModuloFINAN();
            }
            return origem;
        } catch (Exception ex) {
            return "";
        }
    }

    public String irParaTelaInicial() {
        return "financeiro";
    }
    
    public String seguir() {
        return retorno.equals("") ? origem : retorno;
    }

    /**
     * método usado pela tela do cliente para inicializar os dados necessários
     *
     * @param evt
     * @throws Exception
     */
    public void prepareRecibo(ActionEvent evt) {
        notificarRecursoEmpresa(RecursoSistema.RECIBO_CLIENTE_EDITAR);
        try {
            novo();
            setMsgAlert("");
            MovPagamentoVO pagamento = (MovPagamentoVO) evt.getComponent().getAttributes().get("pagamentoVO");
            this.pagamento = (MovPagamentoVO) pagamento.getClone(true);
            setEmpresaVO(getFacade().getEmpresa().consultarPorChavePrimaria(pagamento.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            //necessario para evitar que pagamento de parcelas de unidade, sejam feitos por outra
            if(!UtilReflection.objetoMaiorQueZero(getEmpresaLogado(), "getCodigo()") || !getEmpresaLogado().getCodigo().equals(getEmpresaVO().getCodigo())) {
                throw new Exception("É necessário estar logado na unidade "+getEmpresaVO().getNome()+" para realizar essa operação, pois esse pagamento pertence a essa unidade.");
            }
            tipoEdicao = evt.getComponent().getAttributes().get("tipoEdicao").toString();
            if (pagamento == null) {
                throw new Exception("Recibo não foi posicionado corretamente. Contate o suporte técnico.");
            }
            ClienteControle cc = (ClienteControle) JSFUtilities.getManagedBeanValue("ClienteControle");
            if (cc == null) {
                throw new Exception("Cliente do Recibo não foi encontrado. Contate o suporte técnico.");
            }
            cc.pegarClienteTelaCliente();
            setCliente(cc.getClienteVO());
            setRecibo(getFacade().getReciboPagamento().consultarPorChavePrimaria(pagamento.getReciboPagamento().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));

            if (!UteisValidacao.emptyNumber(getRecibo().getContrato().getCodigo())) {
                ContratoOperacaoVO operacaoContratoVO = getFacade().getContratoOperacao().consultarPorTipoOperacaoCodigoContrato(getRecibo().getContrato().getCodigo(), "CA", false, Uteis.NIVELMONTARDADOS_MINIMOS);
                setSituacaoContratoRecibo(operacaoContratoVO != null ? "CA" : "");
            }

            setParcelas(getFacade().getMovParcela().consultarPorCodigoRecibo(
                    pagamento.getReciboPagamento().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setPagamentos(getFacade().getMovPagamento().consultarPorCodigoRecibo(
                    pagamento.getReciboPagamento().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
            tipoEdicao = obterTipoEdicao(evt);

            preparaEdicao();
            origem = "editarCliente";
            setMsgAlert("Richfaces.showModalPanel('panelAutorizarEdicaoPagamento');");
        }catch(Exception e){
            montarErro(e);
        }
    }

    public void prepareRecibo(Integer codigoPagamento) {
        notificarRecursoEmpresa(RecursoSistema.RECIBO_CLIENTE_EDITAR);
        try {
            novo();
            setMsgAlert("");
            MovPagamentoVO pagamento = getFacade().getMovPagamento().consultarPorChavePrimaria(codigoPagamento, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            this.pagamento = (MovPagamentoVO) pagamento.getClone(true);
            setEmpresaVO(getFacade().getEmpresa().consultarPorChavePrimaria(pagamento.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            //necessario para evitar que pagamento de parcelas de unidade, sejam feitos por outra
            if(!UtilReflection.objetoMaiorQueZero(getEmpresaLogado(), "getCodigo()") || !getEmpresaLogado().getCodigo().equals(getEmpresaVO().getCodigo())) {
                throw new Exception("É necessário estar logado na unidade "+getEmpresaVO().getNome()+" para realizar essa operação, pois esse pagamento pertence a essa unidade.");
            }
            tipoEdicao = this.pagamento.getFormaPagamento().getTipoFormaPagamento();
            if (pagamento == null) {
                throw new Exception("Recibo não foi posicionado corretamente. Contate o suporte técnico.");
            }
            ClienteControle cc = (ClienteControle) JSFUtilities.getManagedBeanValue("ClienteControle");
            if (cc == null) {
                throw new Exception("Cliente do Recibo não foi encontrado. Contate o suporte técnico.");
            }
            cc.pegarClienteTelaCliente();
            setCliente(cc.getClienteVO());
            setRecibo(getFacade().getReciboPagamento().consultarPorChavePrimaria(pagamento.getReciboPagamento().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));

            if (!UteisValidacao.emptyNumber(getRecibo().getContrato().getCodigo())) {
                ContratoOperacaoVO operacaoContratoVO = getFacade().getContratoOperacao().consultarPorTipoOperacaoCodigoContrato(getRecibo().getContrato().getCodigo(), "CA", false, Uteis.NIVELMONTARDADOS_MINIMOS);
                setSituacaoContratoRecibo(operacaoContratoVO != null ? "CA" : "");
            }

            setParcelas(getFacade().getMovParcela().consultarPorCodigoRecibo(
                    pagamento.getReciboPagamento().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setPagamentos(getFacade().getMovPagamento().consultarPorCodigoRecibo(
                    pagamento.getReciboPagamento().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));

            preparaEdicao();
            origem = "editarClienteNovo";
        }catch(Exception e){
            montarErro(e);
        }
    }

    /**
     * <AUTHOR> Alcides 05/08/2013
     */
    private String obterTipoEdicao(ActionEvent evt) {
        String tipoEdicao;
        try {
            tipoEdicao = evt.getComponent().getAttributes().get("tipoEdicao").toString();
        } catch (Exception e) {
            tipoEdicao = "";
        }
        return tipoEdicao;
    }

    /**
     * método usado pela tela de gestao de recebiveis para inicializar os dados
     * necessários
     *
     * @param evt
     * @throws Exception
     */
    public void prepareReciboGestaoRecebiveis(ActionEvent evt) throws Exception {
        GestaoRecebiveisControle controle = (GestaoRecebiveisControle) JSFUtilities.getManagedBeanValue(GestaoRecebiveisControle.class);
        Integer ch = (Integer) evt.getComponent().getAttributes().get("codigoCheque");
        Integer cc = (Integer) evt.getComponent().getAttributes().get("codigoCartao");
        Integer mp = (Integer) evt.getComponent().getAttributes().get("codigoMP");
        if (ch == null && cc == null) {
            for (MovPagamentoVO mpVO : controle.getListaOutros()) {
                if (mpVO.getCodigo().equals(mp)) {
                    evt.getComponent().getAttributes().put("movpagamentoVO", mpVO);
                    break;
                }
            }
        }else if (cc != null){
            for (CartaoCreditoTO ccTO : controle.getListaCartoes()) {
                if (ccTO.getCodigo().equals(cc)) {
                    evt.getComponent().getAttributes().put("cartaoTO", ccTO);
                    break;
                }
            }
        }else{
            for (ChequeTO chTO : controle.getListaCheques()) {
                if (chTO.getCodigo() == ch) {
                    evt.getComponent().getAttributes().put("chequeTO", chTO);
                    break;
                }
            }
        }
        prepareRecibo2(evt);
    }

    private void setarModuloZW(){
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        if(loginControle != null){
            loginControle.setModuloAberto(ModuloAberto.ZILLYONWEB);
        }
    }


    public void prepareRecibo2(ActionEvent evt) {

        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);

                GestaoRecebiveisControle gestaoRecebiveisControle = (GestaoRecebiveisControle) JSFUtilities.getManagedBeanValue(GestaoRecebiveisControle.class);
                gestaoRecebiveisControle.setResponsavelEdicaoPagamento(auto.getUsuario());
                setProcessandoOperacao(true);
                setarModuloZW();
                setPaginaDestino("editarPagamento");
                setProcessandoOperacao(false);

            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
            }
        };

        limparMsg();
        try {
            novo();
            setMsgAlert("");
            String nomePagador = "";
            int codigoRecibo = 0;
            ChequeTO ch = (ChequeTO) evt.getComponent().getAttributes().get("chequeTO");
            CartaoCreditoTO cc = (CartaoCreditoTO) evt.getComponent().getAttributes().get("cartaoTO");
            MovPagamentoVO mp = (MovPagamentoVO) evt.getComponent().getAttributes().get("movpagamentoVO");
            try {
                if (ch != null) {
                    codigoEdicao = ch.getCodigo();
                    if (UteisValidacao.emptyNumber(ch.getRecibo())) {
                        if (!UteisValidacao.emptyString(ch.getCodigosComposicao())) {
                            codigoRecibo = getFacade().getReciboPagamento().consultarPrimeiroReciboCheques(ch.getCodigosComposicao());
                        }
                        if (UteisValidacao.emptyNumber(codigoRecibo)) {
                            String codigos = ch.getCodigosComposicao();
                            codigos += UteisValidacao.emptyString(ch.getCodigosComposicao()) ? ch.getCodigo() : "," + ch.getCodigo();
                            montarMsgAlert("Cheque não está vinculado a nenhum recibo, portanto não pode ser editado. Ele está vinculado a crédito(s) do(s) seguinte(s) aluno(s): \n"
                                    + getFacade().getCheque().obterAvisosVinculos(codigos, null, true));
                            throw new Exception("Cheque não está vinculado a nenhum recibo.");
                        }

                    } else {
                        codigoRecibo = ch.getRecibo();
                    }
                    nomePagador = ch.getNomePagador();
                } else {
                    codigoEdicao = cc == null ? mp.getCodigo() : cc.getCodigo();
                    codigoRecibo = cc == null ? mp.getReciboPagamento().getCodigo() : cc.getRecibo();
                    nomePagador = cc == null ? mp.getNomePagador() : cc.getNomePagador();
                }
            } catch (Exception e) {
                codigoRecibo = 0;
            }
            if (codigoRecibo == 0) {
                throw new Exception("Recibo não foi posicionado corretamente. Contate o suporte técnico.");
            }
            setRecibo(getFacade().getReciboPagamento().consultarPorChavePrimaria(codigoRecibo, Uteis.NIVELMONTARDADOS_TELACONSULTA));
            setCliente(getFacade().getCliente().consultarPorCodigoPessoa(getRecibo().getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            if (getCliente().getCodigo().equals(0)) {
                setColaborador(getFacade().getColaborador().consultarPorCodigoPessoa(getRecibo().getPessoaPagador().getCodigo(), 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
            setEmpresaVO(getFacade().getEmpresa().consultarPorChavePrimaria(getRecibo().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            //necessario para evitar que pagamento de parcelas de unidade, sejam feitos por outra
            if (!UtilReflection.objetoMaiorQueZero(getEmpresaLogado(), "getCodigo()") || !getEmpresaLogado().getCodigo().equals(getEmpresaVO().getCodigo())) {
                throw new Exception("É necessário estar logado na unidade " + getEmpresaVO().getNome() + " para realizar essa operação, pois esse pagamento pertence a essa unidade.");
            }
            setParcelas(getFacade().getMovParcela().consultarPorCodigoRecibo(codigoRecibo, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setPagamentos(getFacade().getMovPagamento().consultarPorCodigoRecibo(codigoRecibo, false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
            tipoEdicao = obterTipoEdicao(evt);
            preparaEdicao2();
            origem = "gestaoRecebiveis";
            auto.autorizar("Confirmação de Edição de Pagamento", "EdicaoPagamento",
                    "Você precisa da permissão \"4.22 - Edição de Pagamento\"",
                    "form", listener);
        } catch (Exception e) {
            montarErro(e);
        }
    }
 
    /**
     * gera datas com intervalo de 1 mes para todos os cheques da lista
     */
    public void gerarDatas() {
        int i = 0;
        Date novaData = Calendario.hoje();
        // percorre o restantes dos cheques
        for (ChequeVO novoCheque : getCheques()) {
            if (novoCheque.getSituacao().equals("CA") || novoCheque.getTemLote()) {
                if (i == 0) {
                    i++;
                }
                novaData = novoCheque.getDataCompensacao();
                continue;
            }
            //se for a primeira data considerar a data informada
            if (i == 0) {
                novaData = novoCheque.getDataCompensacao();
                i++;
                continue;
            }
            // alterar a compensacao dos cheques seguintes
            novaData = Uteis.obterDataFutura3(novaData, 1);
            novoCheque.setDataCompensacao(novaData);
            //se não for a primeira data e for um cheque sem lote
        }
        setListaAlterada(true);
    }

    /**
     * reinicia a lista de cheques original
     */
    public void resetar() {
        preparaEdicao2();
        setTotalOutras(0.0);
        pagamento.setValor(0.0);
        setListaAlterada(false);
    }

    /**
     * inicializa a lista de cheques da tela usando a lista de cheques do recibo
     */
    public void preparaEdicao2() {
        try {
            setTituloPagina("Edição de Pagamento");
            setReadOnly(Boolean.FALSE);
            setMensagemDetalhada("", "");
            if (UteisValidacao.emptyString(tipoEdicao) || tipoEdicao.equals("CH")) {
                pagamento = new MovPagamentoVO();
                setCheques(new ArrayList<ChequeVO>());
                Iterator k = this.getPagamentos().iterator();
                LoteVO loteConsultado = new LoteVO();
                while (k.hasNext()) {
                    MovPagamentoVO movPagamento = (MovPagamentoVO) k.next();
                    if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                        for (ChequeVO aux : movPagamento.getChequeVOs()) {

                            if (!UteisValidacao.emptyNumber(aux.getLoteVO().getCodigo())
                                    && !loteConsultado.getCodigo().equals(aux.getLoteVO().getCodigo())) {
                                loteConsultado = getFacade().getLote().consultarPorChavePrimaria(
                                        aux.getLoteVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                                aux.setLoteVO(loteConsultado);

                                aux.setMarcadoDevolucao(
                                        getFacade().getLote().verificarContaDevolucao(loteConsultado.getCodigo()));
                            }

                            if (!aux.getCnpj().isEmpty()) {
                                aux.setCpfOuCnpj("CNPJ");
                            } else {
                                aux.setCpfOuCnpj("CPF");
                            }
                            //setApresentarCPF(!aux.getCnpj().isEmpty());
                            setMudarCampoCpf(!aux.getCpf().isEmpty() ? CPF : CNPJ);

                            if (!(aux.getValor() < aux.getValorTotal() && aux.getSituacao().equals("CA"))) {
                                if (!movPagamento.getCredito()) {
                                    aux.setValor(aux.getValorTotal());
                                }
                                getCheques().add((ChequeVO) aux.getClone(true));
                            }
                        }
                    }
                }
                if (getCheques().isEmpty()) {
                    throw new Exception("Não foram encontrados cheques para edição.");
                }
                calcularValorCheques(true);
            } else {
                for (MovPagamentoVO pag : pagamentos) {
                    if ((pag.getFormaPagamento().getTipoFormaPagamento().equals("CD") && pag.getCodigo().equals(codigoEdicao)) ||
                            (pag.getFormaPagamento().getTipoFormaPagamento().equals("CA") && validarCCEdicao(pag))) {
                        pagamento = pag;
                        setTotalOriginal(pag.getValor());
                        setTotalOutras(pag.getValor());
                        emDinheiro = tipoEdicao.equals("AV");
                        emCartaoCredito = tipoEdicao.equals("CA");
                        emCartaoDebito = tipoEdicao.equals("CD");
                        setReadOnly(Boolean.TRUE);
                        if (emCartaoCredito || emCartaoDebito) {
                            setTituloPagina("Edição do código de autorização de pagamento em cartão");
                            pag.setOperadoraCartaoVO(getFacade().getOperadoraCartao().consultarPorChavePrimaria(
                                    pag.getOperadoraCartaoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                            montarListaNrParcelasCartao(pag.getOperadoraCartaoVO());
                        } else {
                            throw new Exception("Esse pagamento não possui cheques ou cartões para edição");
                        }
                        pagamento.setObjetoVOAntesAlteracao(pagamento.getClone(true));
                    }
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private boolean validarCCEdicao(MovPagamentoVO pag) {
        if(pag.getCartaoCreditoVOs() != null && pag.getCartaoCreditoVOs().size() > 0){
            for(CartaoCreditoVO cc : pag.getCartaoCreditoVOs()){
                if(cc.getCodigo().equals(codigoEdicao)){
                    return true;
                }
            }
        }
        return false;
    }

    public void preparaEdicao() {
        try {
            setTituloPagina("Edição de Pagamento");
            setReadOnly(Boolean.FALSE);
            setMensagemDetalhada("", "");
            if (UteisValidacao.emptyString(tipoEdicao) || tipoEdicao.equals("CH")) {
                pagamento = new MovPagamentoVO();
                setCheques(new ArrayList<ChequeVO>());
                Iterator k = this.getPagamentos().iterator();
                LoteVO loteConsultado = new LoteVO();
                List<Integer> chequesAdicionados = new ArrayList<Integer>();
                while (k.hasNext()) {
                    MovPagamentoVO movPagamento = (MovPagamentoVO) k.next();
                    if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                        for (ChequeVO aux : movPagamento.getChequeVOs()) {

                            if (!UteisValidacao.emptyNumber(aux.getLoteVO().getCodigo())
                                    && !loteConsultado.getCodigo().equals(aux.getLoteVO().getCodigo())) {
                                loteConsultado = getFacade().getLote().consultarPorChavePrimaria(
                                        aux.getLoteVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                                aux.setLoteVO(loteConsultado);
                            }
                            aux.setMarcadoDevolucao(
                                    getFacade().getLote().verificarContaDevolucao(loteConsultado.getCodigo()));
                            if (!aux.getCnpj().isEmpty()) {
                                aux.setCpfOuCnpj("CNPJ");
                            } else {
                                aux.setCpfOuCnpj("CPF");
                            }
                            //setApresentarCPF(!aux.getCnpj().isEmpty());
                            setMudarCampoCpf(!aux.getCpf().isEmpty() ? CPF : CNPJ);
                            if (movPagamento.getMovPagamentoOrigemCredito() > 0) {
                                aux.setPagaOutroContrato(true); // usado nesse contexto para não deixar excluir cheque que são originados de outro pagamento
                            }
                            if (!(aux.getValor() < aux.getValorTotal() && aux.getSituacao().equals("CA") && UteisValidacao.emptyString(aux.getComposicao()))) {
                                boolean addCheque = true;
                                if (!movPagamento.getCredito()) {
                                    aux.setValor(aux.getValorTotal());
                                }
                                if (aux.getTemComposicao()) {
                                    String[] composicao = aux.getComposicao().split(",");
                                    for (String codCheque : composicao) {
                                        Integer codigo = Integer.parseInt(codCheque);
                                        if(chequesAdicionados.contains(codigo)){
                                            for(ChequeVO chequeAdd : getCheques()){
                                                if(chequeAdd.getCodigo().equals(codigo)){
                                                    chequeAdd.setValor(Uteis.arredondarForcando2CasasDecimais(chequeAdd.getValor() + aux.getValor()));
                                                    addCheque = false;
                                                    break;
                                                }
                                            }
                                        }
                                        if(!addCheque){
                                            break;
                                        }
                                    }
                                    if(addCheque){
                                        aux.setAvisoVinculos(getFacade().getCheque().obterAvisosVinculos(aux.getComposicao(),movPagamento.getReciboPagamento().getCodigo(), false));
                                    }
                                }
                                if(addCheque){
                                    getCheques().add((ChequeVO) aux.getClone(true));
                                    chequesAdicionados.add(aux.getCodigo());
                                }
                            }
                        }
                    }
                }
                if (getCheques().isEmpty()) {
                    throw new Exception("Não foram encontrados cheques para edição.");
                }
                calcularValorCheques(true);
                setCheques(Ordenacao.ordenarLista(getCheques(), "dataCompensacao"));
            } else {
                for (MovPagamentoVO pag : pagamentos) {
                    if (pag.getCodigo().equals(pagamento.getCodigo())) {
                        pagamento = pag;
                        setTotalOriginal(pag.getValor());
                        setTotalOutras(pag.getValor());

                        emDinheiro = tipoEdicao.equals("AV");
                        emCartaoCredito = tipoEdicao.equals("CA");
                        emCartaoDebito = tipoEdicao.equals("CD");
                        setReadOnly(Boolean.TRUE);
                        if (emCartaoCredito || emCartaoDebito) {
                            setTituloPagina("Edição do código de autorização de pagamento em cartão");
                            pag.setOperadoraCartaoVO(getFacade().getOperadoraCartao().consultarPorChavePrimaria(
                                    pag.getOperadoraCartaoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                            montarListaNrParcelasCartao(pag.getOperadoraCartaoVO());
                        } else {
                            throw new Exception("Esse pagamento não possui cheques ou cartões para edição");
                        }
                        pagamento.setObjetoVOAntesAlteracao((MovPagamentoVO) pagamento.getClone(true));
                    }
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * calcula o valor final dos cheques inicalizando a variavel de valor
     * original
     *
     * @param original
     */
    public void calcularValorCheques(boolean original) throws Exception {
        setMensagemDetalhada("", "");
        setTotalCheques(0.0);
        setResiduo(0.0);
        for (ChequeVO novoCheque : getCheques()) {
            if (novoCheque.getValor() < 0) {
                setResiduo(novoCheque.getValor());
                throw new Exception("Informe somente valores maiores que zero.");
            } else {
                setTotalCheques(Uteis.arredondarForcando2CasasDecimais(getTotalCheques() + novoCheque.getValor()));
            }
        }
        if (original) {
            setTotalOriginal(getTotalCheques());
        }
    }

    public void selecionarCheque() {
        try {
            ChequeVO obj = (ChequeVO) context().getExternalContext().getRequestMap().get("cheque");
            if (obj == null) {
                throw new Exception("Não foi possível posicionar o cheque selecionado.");
            }
            if (obj.getSituacao().equals("CA")) {
                obj.setSituacao("EA");
            } else {
                obj.setSituacao("CA");
            }
            calcularTotalFinal();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String devolverCheque(Connection con) throws Exception {
        ProdutosPagosServico produtos = new ProdutosPagosServico();
        produtos.setarProdutosPagos(con, getRecibo().getCodigo());

        List<String> produtosPagos = new ArrayList<String>();
        setChequesAuxiliar(new ArrayList<ChequeVO>());
        pagamento.registrarObjetoVOAntesDaAlteracao();

        List<NFSeEmitidaVO> notasEmitidas = new ArrayList<NFSeEmitidaVO>();
        existemChequesParaDevolver(notasEmitidas);
        JSONArray jsonNotasEmitidas = new JSONArray();
        for (NFSeEmitidaVO notaEmitida : notasEmitidas) {
            jsonNotasEmitidas.put(notaEmitida.toJSON());
        }

        StringBuilder chequesDevolvidos = new StringBuilder("");
        List <ChequeVO>  chequesReciboDevolucao = new ArrayList<ChequeVO>();
        JSONArray jsonChequesDevolvidos = new JSONArray();
        for (ChequeVO chequeVO : getCheques()) {
            if (chequeVO.isSelecionado()) {
                getChequesAuxiliar().add(chequeVO);
                chequesDevolvidos.append(chequeVO.getCodigo()).append(",");

                ChequeTO chequeTO = chequeVO.toTO();
                jsonChequesDevolvidos.put(chequeTO.toJSON());
                chequesReciboDevolucao.add(chequeVO);
                if (chequeVO.getComposicao() != null && !chequeVO.getComposicao().equals("")) {
                    String[] composicao = chequeVO.getComposicao().split(",");
                    for (String codCheque : composicao) {
                        Integer codigo = Integer.parseInt(codCheque);
                        ChequeVO chequeOriginal = getFacade().getCheque().consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        MovPagamentoVO movpagamentoCheque = getFacade().getMovPagamento().consultarPorChavePrimaria(chequeOriginal.getMovPagamento(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if(!UteisValidacao.emptyNumber(movpagamentoCheque.getReciboPagamento().getCodigo()) && !recibosEnvolvidos.contains(movpagamentoCheque.getReciboPagamento().getCodigo())){
                            recibosEnvolvidos.add(movpagamentoCheque.getReciboPagamento().getCodigo());
                            produtos.setarProdutosPagos(con, movpagamentoCheque.getReciboPagamento().getCodigo());
                            chequeOriginal = getFacade().getCheque().consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        }
                        getChequesAuxiliar().add(chequeOriginal);
                        
                    }
                }
                getFacade().getMovProduto().alterarDevolucaoCheque(chequeVO);
            }
        }

        if (chequesDevolvidos.length() > 1) {
            chequesDevolvidos.deleteCharAt(chequesDevolvidos.length() - 1);
        }

        validarDataNovaParcela();
        List <ChequeVO>  chequesEmContaCliente = new ArrayList<ChequeVO>();
       

        for (ChequeVO chequeVO : getChequesAuxiliar()) {
            String produtosPagosPeloCheque = chequeVO.getProdutosPagos();
            chequeVO.setSituacao("CA");
            if (!produtosPagosPeloCheque.trim().equals("")) {
                produtosPagos.add(chequeVO.getProdutosPagos());
            } else {
                chequesEmContaCliente.add(chequeVO);
            }

        }

        try {
            if(!chequesEmContaCliente.isEmpty()){
                tratarChequesEmContaCorrenteCliente(chequesEmContaCliente);
            }
            
            for (ChequeVO chequeVO : getChequesAuxiliar()) {
                getFacade().getCheque().alterarSituacao(chequeVO, true);
            }
            List <MovParcelaVO> parcelas = alterarProdutosPagos(produtosPagos, jsonChequesDevolvidos, jsonNotasEmitidas);

            MovPagamentoControle movPagamentoControle = (MovPagamentoControle) context().getExternalContext().getSessionMap().get("MovPagamentoControle");
            if (movPagamentoControle != null) {
                movPagamentoControle.liberarBackingBeanMemoria("MovPagamentoControle");
            }
            VendaAvulsaControle vendaAvulsaControle = (VendaAvulsaControle) context().getExternalContext().getSessionMap().get("VendaAvulsaControle");
            if (vendaAvulsaControle != null) {
                vendaAvulsaControle.liberarBackingBeanMemoria("VendaAvulsaControle");
            }
            ReciboDevolucao reciboDevDao = new ReciboDevolucao(con);
            ReciboDevolucaoVO reciboDevolucao = new ReciboDevolucaoVO();
            reciboDevolucao.montarReciboDevolucaoEdicaoPagamento(chequesReciboDevolucao, parcelas, getRecibo().getPessoaPagador().getCodigo(), getUsuarioLogado(),getRecibo().getCodigo());
            reciboDevDao.incluirSemCommit(reciboDevolucao, con);
            reciboDevDao = null;
            
            MovParcelaControle movParcelaControle = (MovParcelaControle) getControlador(MovParcelaControle.class);
            movParcelaControle.novo();
            for(MovParcelaVO parcela: parcelas){
                parcela = getFacade().getMovParcela().consultarPorChavePrimaria(parcela.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

                movParcelaControle.getListaParcelasPagar().add(parcela);
                movParcelaControle.setMovParcelaVO(parcela);
                movParcelaControle.setValorTotalParcela(Uteis.arredondarForcando2CasasDecimais(movParcelaControle.getValorTotalParcela() + parcela.getValorParcela()));
            }

            try {
                List<LogVO> listaLog = new ArrayList<LogVO>();
                LogVO obj = new LogVO();
                obj.setChavePrimaria(getCliente().getCodigo().toString());
                obj.setNomeEntidade("MOVPAGAMENTO");
                obj.setNomeEntidadeDescricao("Movimento do Pagamento");
                obj.setOperacao("EDIÇÃO DE PAGAMENTO");
                obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                obj.setUserOAMD(getUsuarioLogado().getUserOamd());
                obj.setNomeCampo("TODOS");
                if (getRecibo().getPessoaPagador()!= null && getRecibo().getPessoaPagador().getCodigo() != 0) {
                    obj.setPessoa(getRecibo().getPessoaPagador().getCodigo());
                } else {
                    obj.setPessoa(0);
                }

                StringBuilder sb = new StringBuilder("");
                sb.append("O(s) seguinte(s) cheque(s): ").append(chequesDevolvidos.toString());
                if (chequesDevolvidos.length() < 3) {
                    sb.append(" foi devolvido.\n\r");
                } else {
                    sb.append(" foram devolvidos.\n\r");
                }
                sb.append("Foi gerado uma nova parcela no valor de ").append(movParcelaControle.getValorTotalParcela_Apresentar());
                obj.setValorCampoAlterado(sb.toString());
                obj.setDataAlteracao(Calendario.hoje());
                listaLog.add(obj);

                registrarLogObjetoVO(listaLog, getRecibo().getPessoaPagador().getCodigo());
            } catch (Exception e) {
                registrarLogErroObjetoVO("MOVPAGAMENTO",getRecibo().getPessoaPagador().getCodigo(),
                        "ERRO AO GERAR LOG DE EDIÇAO DE MOVPAGAMENTO", getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                e.printStackTrace();
            }

            setChequesAuxiliar(new ArrayList<ChequeVO>());

        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
            setErro(true);
            throw ex;
        } finally {
        }

        return "pagamento";
    }

    private void existemChequesParaDevolver(List<NFSeEmitidaVO> notasEmitidas) throws Exception {
        boolean temChequeSelecionado = false;
        for (ChequeVO chequeVO : getCheques()) {
            if (chequeVO.isSelecionado()) {
                temChequeSelecionado = true;
                NFSeEmitidaVO nfSeEmitidaVO = getFacade().getNFSeEmitida().consultaPorCheque(chequeVO.getCodigo().toString());
                if (nfSeEmitidaVO != null) {
                    notasEmitidas.add(nfSeEmitidaVO);
                }else{
                    nfSeEmitidaVO = getFacade().getNFSeEmitida().consultaPorMovPagamentoOuReciboPagamento(chequeVO.getMovPagamento());
                    if (nfSeEmitidaVO != null) {
                        if (!notasEmitidas.contains(nfSeEmitidaVO)){
                            notasEmitidas.add(nfSeEmitidaVO);
                        }
                    }

                }
            }
        }
        if (!temChequeSelecionado) {
            throw new ConsistirException("Nenhum cheque foi selecionado para a devolução.");
        }
    }

    private void validarDataNovaParcela() throws Exception {

        if (getDataNovaParcela() == null) {
            setDataNovaParcela(Calendario.hoje());
        }

        if (Calendario.maior(getDataNovaParcela(), Calendario.hoje())) {
            throw new ConsistirException("A data não pode ser maior que hoje.");
        }

        Integer codigoContrato = null;
        boolean contaCorrente = false;
        boolean existeContrato = false;
        for (ChequeVO chequeVO : getChequesAuxiliar()) {
            String produtosPagosPeloCheque = chequeVO.getProdutosPagos();
            if (!produtosPagosPeloCheque.trim().equals("")) {
                String[] infoProdutosPagos = produtosPagosPeloCheque.split("\\|");
                for (String produto : infoProdutosPagos) {
                    if (!produto.trim().equals("")) {
                        String[] infoProduto = produto.split(",");
                        contaCorrente = contaCorrente || infoProduto[1].equals("AC");
                        if (!infoProduto[2].equals("0")) {
                            codigoContrato = Integer.parseInt(infoProduto[2]);
                            existeContrato = true;
                        }
                    }
                }
            }
        }

        if (contaCorrente && !existeContrato) {
            codigoContrato = null;
        }

        ContratoVO contratoVO = null;
        if (codigoContrato != null) {
            contratoVO = getFacade().getContrato().consultarPorChavePrimaria(codigoContrato, Uteis.NIVELMONTARDADOS_MINIMOS);
        }

        if (contratoVO != null) {
            if (Calendario.menor(getDataNovaParcela(), contratoVO.getDataLancamento())) {
                throw new ConsistirException("A data não pode ser menor que a data de lançamento do contrato.");
            }
        }
    }

    private MovProdutoVO criarProdutoDevolucaoRecebiveis(String tipoProduto, String descricaoProduto, Integer codigoContrato, Integer vendaAvulsa, Double valor) throws Exception {
        MovProdutoVO movProdutoVO = new MovProdutoVO();
        movProdutoVO.setProduto(getFacade().getProduto().consultarPorTipoProduto(tipoProduto, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        movProdutoVO.setApresentarMovProduto(false);
        movProdutoVO.setDescricao(descricaoProduto);
        movProdutoVO.setMesReferencia(Uteis.getMesReferenciaData(getDataNovaParcela()));
        movProdutoVO.setQuantidade(1);
        movProdutoVO.setAnoReferencia(Uteis.getAnoData(getDataNovaParcela()));
        movProdutoVO.setDataInicioVigencia(getDataNovaParcela());
        movProdutoVO.setDataFinalVigencia(getDataNovaParcela());
        movProdutoVO.setDataLancamento(getDataNovaParcela());
        movProdutoVO.setResponsavelLancamento(getUsuarioLogado());
        movProdutoVO.setEmpresa(getRecibo().getEmpresa());
        movProdutoVO.setPessoa(getRecibo().getPessoaPagador());
        movProdutoVO.getContrato().setCodigo(codigoContrato);
        movProdutoVO.setVendaAvulsa(vendaAvulsa);
        movProdutoVO.setQuitado(true);
        movProdutoVO.setSituacao("CA");
        movProdutoVO.setValorDesconto(0.0);
        movProdutoVO.setPrecoUnitario(valor);
        movProdutoVO.setTotalFinal(valor);
        getFacade().getMovProduto().incluirSemCommit(movProdutoVO);

        return movProdutoVO;
    }

    private MovParcelaVO criarParcelaParaPagar(String descricao, Integer codigoContrato,Integer vendaAvulsa, Integer diaria, Integer personal, double valor, JSONArray jsonChequesDevolvidos, JSONArray jsonNotasEmitidas) throws Exception {
        MovParcelaVO novaParcela = new MovParcelaVO();
        novaParcela.setDescricao(descricao);
        novaParcela.setResponsavel(getUsuarioLogado());
        novaParcela.setValorParcela(valor);
        novaParcela.setSituacao("EA");
        novaParcela.setDataVencimento(Calendario.hoje());
        novaParcela.setDataRegistro(getDataNovaParcela());
        novaParcela.setEmpresa(getRecibo().getEmpresa());
        novaParcela.setPessoa(getRecibo().getPessoaPagador());
        novaParcela.setDataCobranca(Calendario.hoje());
        novaParcela.getContrato().setCodigo(codigoContrato);
        novaParcela.getVendaAvulsaVO().setCodigo(vendaAvulsa);
        novaParcela.getAulaAvulsaDiariaVO().setCodigo(diaria);
        novaParcela.getPersonal().setCodigo(personal);

        if (jsonChequesDevolvidos.length() > 0) {
            MovParcelaDetalhamentoVO movParcelaDetalhamentoVO = new MovParcelaDetalhamentoVO();
            movParcelaDetalhamentoVO.setMovParcelaVO(novaParcela);
            movParcelaDetalhamentoVO.setChequesDevolvidos(jsonChequesDevolvidos);
            novaParcela.setMovParcelaDetalhamentoVO(movParcelaDetalhamentoVO);
        }

        if (jsonNotasEmitidas.length() > 0) {
            MovParcelaDetalhamentoVO movParcelaDetalhamentoVO = new MovParcelaDetalhamentoVO();
            movParcelaDetalhamentoVO.setMovParcelaVO(novaParcela);
            movParcelaDetalhamentoVO.setNotasEmitidasAnteriormente(jsonNotasEmitidas);
            novaParcela.setMovParcelaDetalhamentoVO(movParcelaDetalhamentoVO);
        }

        getFacade().getMovParcela().incluirComProdutosSemCommit(novaParcela);
        return novaParcela;
    }

    private List<MovParcelaVO> alterarProdutosPagos(List<String> listaProdutosPagos, JSONArray jsonChequesDevolvidos, JSONArray jsonNotasEmitidas) throws Exception {
        List<MovParcelaVO> parcelas = new ArrayList<MovParcelaVO>();
        Map<Integer, List<Integer>> mapContratoProdutos = new HashMap<Integer, List<Integer>>();
        Map<Integer, List<Integer>> mapVendaProdutos = new HashMap<Integer, List<Integer>>();
        Map<Integer, List<Integer>> mapDiariaProdutos = new HashMap<Integer, List<Integer>>();
        Map<Integer, List<Integer>> mapPersonalProdutos = new HashMap<Integer, List<Integer>>();
        Map<Integer, Double> mapProdutosValores = new HashMap<Integer, Double>();
        for (String produtosPagos : listaProdutosPagos) {
            if (!produtosPagos.trim().equals("")) {
                String[] infoProdutosPagos = produtosPagos.split("\\|");
                for (String produto : infoProdutosPagos) {
                    if (!produto.trim().equals("")) {
                        String[] infoProduto = produto.split(",");
                        int codigoProduto = Integer.parseInt(infoProduto[0]);
                        int codigoContrato = Integer.parseInt(infoProduto[2]);
                        double valorPago = Double.parseDouble(infoProduto[3]);
                        if (codigoContrato > 0) {
                            if (mapContratoProdutos.containsKey(codigoContrato)) {
                                if (!mapContratoProdutos.get(codigoContrato).contains(codigoProduto)) {
                                    mapContratoProdutos.get(codigoContrato).add(codigoProduto);
                                }
                            } else {
                                mapContratoProdutos.put(codigoContrato, new ArrayList<Integer>());
                                mapContratoProdutos.get(codigoContrato).add(codigoProduto);
                            }
                        } else {
                            MovParcelaVO parcela = getFacade().getMovParcela().consultarPorMovProduto(codigoProduto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            if (!UteisValidacao.emptyNumber(parcela.getVendaAvulsaVO().getCodigo())) {
                                if (mapVendaProdutos.containsKey(parcela.getVendaAvulsaVO().getCodigo())) {
                                    if (!mapVendaProdutos.get(parcela.getVendaAvulsaVO().getCodigo()).contains(codigoProduto)) {
                                        mapVendaProdutos.get(parcela.getVendaAvulsaVO().getCodigo()).add(codigoProduto);
                                    }
                                } else {
                                    mapVendaProdutos.put(parcela.getVendaAvulsaVO().getCodigo(), new ArrayList<Integer>());
                                    mapVendaProdutos.get(parcela.getVendaAvulsaVO().getCodigo()).add(codigoProduto);
                                }
                            } else if (!UteisValidacao.emptyNumber(parcela.getAulaAvulsaDiariaVO().getCodigo())) {
                                if (mapDiariaProdutos.containsKey(parcela.getAulaAvulsaDiariaVO().getCodigo())) {
                                    if (!mapDiariaProdutos.get(parcela.getAulaAvulsaDiariaVO().getCodigo()).contains(codigoProduto)) {
                                        mapDiariaProdutos.get(parcela.getAulaAvulsaDiariaVO().getCodigo()).add(codigoProduto);
                                    }
                                } else {
                                    mapDiariaProdutos.put(parcela.getAulaAvulsaDiariaVO().getCodigo(), new ArrayList<Integer>());
                                    mapDiariaProdutos.get(parcela.getAulaAvulsaDiariaVO().getCodigo()).add(codigoProduto);
                                }
                            } else if (!UteisValidacao.emptyNumber(parcela.getPersonal().getCodigo())) {
                                if (mapPersonalProdutos.containsKey(parcela.getPersonal().getCodigo())) {
                                    if (!mapPersonalProdutos.get(parcela.getPersonal().getCodigo()).contains(codigoProduto)) {
                                        mapPersonalProdutos.get(parcela.getPersonal().getCodigo()).add(codigoProduto);
                                    }
                                } else {
                                    mapPersonalProdutos.put(parcela.getPersonal().getCodigo(), new ArrayList<Integer>());
                                    mapPersonalProdutos.get(parcela.getPersonal().getCodigo()).add(codigoProduto);
                                }
                            }
                        }

                        if (mapProdutosValores.containsKey(codigoProduto)) {
                            mapProdutosValores.put(codigoProduto, Uteis.arredondarForcando2CasasDecimais(valorPago + mapProdutosValores.get(codigoProduto)));
                        } else {
                            mapProdutosValores.put(codigoProduto, valorPago);
                        }
                    }
                }
            }
        }
        int countParcela = 1;
        double valorParcela;
        MovProdutoVO produtoDevolucao;
        MovParcelaVO parcelaDevolucao;
        if (!mapContratoProdutos.isEmpty()) {
            for (Integer codigoContrato : mapContratoProdutos.keySet()) {
                valorParcela = 0.0;
                for (Integer produto : mapContratoProdutos.get(codigoContrato)) {
                    valorParcela = Uteis.arredondarForcando2CasasDecimais(valorParcela + mapProdutosValores.get(produto));
                }
                produtoDevolucao = criarProdutoDevolucaoRecebiveis("RD", "DEVOLUÇÃO DE CHEQUES", codigoContrato, null, valorParcela);
                parcelaDevolucao = criarParcelaParaPagar("PARCELA EDIÇÃO DE PAGAMENTO " + (countParcela++) + " - Rec. " + getRecibo().getCodigo(), codigoContrato, 0, 0, 0, valorParcela, jsonChequesDevolvidos, jsonNotasEmitidas);
                alterarMovprodutoParcelas(produtoDevolucao, parcelaDevolucao, mapContratoProdutos.get(codigoContrato), mapProdutosValores);
                parcelas.add(parcelaDevolucao);
            }
        }
        if (!mapVendaProdutos.isEmpty()) {
            for (Integer codigoVenda : mapVendaProdutos.keySet()) {
                valorParcela = 0.0;
                for (Integer produto : mapVendaProdutos.get(codigoVenda)) {
                    valorParcela = Uteis.arredondarForcando2CasasDecimais(valorParcela + mapProdutosValores.get(produto));
                }
                produtoDevolucao = criarProdutoDevolucaoRecebiveis("RD", "DEVOLUÇÃO DE CHEQUES", null, codigoVenda, valorParcela);
                parcelaDevolucao = criarParcelaParaPagar("PARCELA EDIÇÃO DE PAGAMENTO " + (countParcela++) + " - Rec. " + getRecibo().getCodigo(), 0, codigoVenda, 0, 0, valorParcela, jsonChequesDevolvidos, jsonNotasEmitidas);
                alterarMovprodutoParcelas(produtoDevolucao, parcelaDevolucao, mapVendaProdutos.get(codigoVenda), mapProdutosValores);
                parcelas.add(parcelaDevolucao);
            }
        }
        if (!mapDiariaProdutos.isEmpty()) {
            for (Integer codigoDiaria : mapDiariaProdutos.keySet()) {
                valorParcela = 0.0;
                for (Integer produto : mapDiariaProdutos.get(codigoDiaria)) {
                    valorParcela = Uteis.arredondarForcando2CasasDecimais(valorParcela + mapProdutosValores.get(produto));
                }
                produtoDevolucao = criarProdutoDevolucaoRecebiveis("RD", "DEVOLUÇÃO DE CHEQUES", null, null, valorParcela);
                parcelaDevolucao = criarParcelaParaPagar("PARCELA EDIÇÃO DE PAGAMENTO " + (countParcela++) + " - Rec. " + getRecibo().getCodigo(), 0, 0, codigoDiaria, 0, valorParcela, jsonChequesDevolvidos, jsonNotasEmitidas);
                alterarMovprodutoParcelas(produtoDevolucao, parcelaDevolucao, mapDiariaProdutos.get(codigoDiaria), mapProdutosValores);
                parcelas.add(parcelaDevolucao);
            }
        }
        if (!mapPersonalProdutos.isEmpty()) {
            for (Integer codigoPersonal : mapPersonalProdutos.keySet()) {
                valorParcela = 0.0;
                for (Integer produto : mapPersonalProdutos.get(codigoPersonal)) {
                    valorParcela = Uteis.arredondarForcando2CasasDecimais(valorParcela + mapProdutosValores.get(produto));
                }
                produtoDevolucao = criarProdutoDevolucaoRecebiveis("RD", "DEVOLUÇÃO DE CHEQUES", null, null, valorParcela);
                parcelaDevolucao = criarParcelaParaPagar("PARCELA EDIÇÃO DE PAGAMENTO " + (countParcela++) + " - Rec. " + getRecibo().getCodigo(), 0, 0, 0, codigoPersonal, valorParcela, jsonChequesDevolvidos, jsonNotasEmitidas);
                alterarMovprodutoParcelas(produtoDevolucao, parcelaDevolucao, mapPersonalProdutos.get(codigoPersonal), mapProdutosValores);
                parcelas.add(parcelaDevolucao);
            }
        }
        return parcelas;
    }

    /**
     * calcula o total final da edicao de cheques
     */
    public void calcularTotalFinal() {
        try {
            calcularTotalComExcecao();
            calcularValorParaDevolver();
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }
    }

    private void calcularValorParaDevolver() {
        double valor = 0.0;
        for (ChequeVO chequeVO : getCheques()) {
            if (chequeVO.isSelecionado()) {
                valor += chequeVO.getValor();
            }
        }
        setTotalDevolver(valor);
    }

    public void calcularTotalComExcecao() throws Exception {
        calcularValorCheques(false);
        atualizarOutrasFormasPagamento();
        setResiduo(Uteis.arredondarForcando2CasasDecimais(getTotalOriginal() - getTotalCheques() - getTotalOutras()));
        if (getResiduo() < 0) {
            throw new Exception("A Soma dos cheques mais o valor da Outra Forma de Pagamento não pode ser maior do que o valor original.");
        }
    }

    /**
     * atualiza as informações relacionadas a outra forma de pagamento usada
     */
    private void atualizarOutrasFormasPagamento() throws Exception {
        setTotalOutras(0.0);
        // se foi informado um valor invalido
        if (getPagamento().getValor() < 0.0) {
            throw new Exception("Valor informado inválido.");
        }
        // se nenhum valor foi informado é pq nao precisa ser usado
        if (getPagamento().getValor() == 0.0) {
            return;
        }
        // apenas a forma de pagamento que está na tela será usada
        for (FormaPagamentoVO fp : getFormasPagamento()) {
            if ((getAbaSelecionada().equals("abaDinheiro") && fp.getTipoFormaPagamento().equals("AV"))
                    || (getAbaSelecionada().equals("abaCartaoCredito") && fp.getTipoFormaPagamento().equals("CA"))
                    || (getAbaSelecionada().equals("abaCartaoDebito") && fp.getTipoFormaPagamento().equals("CD"))
                    || (getAbaSelecionada().equals("abaBoleto") && fp.getTipoFormaPagamento().equals("BB"))) {
                pagamento.setFormaPagamento(fp);
                break;
            }
        }
        // limpa os campos desnecessarios para cada forma de pagamento, visando manter integridade dos dados.
        if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("AV")) {
            pagamento.setConvenio(new ConvenioCobrancaVO());
            pagamento.setOperadoraCartaoVO(new OperadoraCartaoVO());
            pagamento.setNrParcelaCartaoCredito(0);
        } else if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
            pagamento.setConvenio(new ConvenioCobrancaVO());
        } else if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CD")) {
            pagamento.setConvenio(new ConvenioCobrancaVO());
            pagamento.setNrParcelaCartaoCredito(0);
        } else if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("BB")) {
            pagamento.setOperadoraCartaoVO(new OperadoraCartaoVO());
            pagamento.setNrParcelaCartaoCredito(0);
        }
        // prepara o restante dos dados
        pagamento.setEmpresa(cliente.getEmpresa());
        pagamento.setReciboPagamento(getRecibo());
        pagamento.setNomePagador(getRecibo().getNomePessoaPagador());
        pagamento.setPessoa(getRecibo().getPessoaPagador());
        validarAlteracaoDeData(getRecibo().getData(), false);
        pagamento.setResponsavelPagamento(getRecibo().getResponsavelLancamento());
        pagamento.setPagamentoMovParcelaVOs(new ArrayList<PagamentoMovParcelaVO>());
        for (MovParcelaVO mparc : getParcelas()) {
            PagamentoMovParcelaVO pmp = new PagamentoMovParcelaVO();
            pmp.setMovParcela(mparc);
            pmp.setReciboPagamento(recibo);
            pmp.setValorPago(pagamento.getValor());
            pmp.setMovPagamento(pagamento.getCodigo());
            pagamento.getPagamentoMovParcelaVOs().add(pmp);
        }
        setTotalOutras(getPagamento().getValor());
    }

    private void validarAlteracaoDeData(Date dataLancamento, boolean ehCartaoDebito) throws Exception {
        if (dataLancamento != null) {
            pagamento.setDataLancamento(dataLancamento);
            pagamento.setDataPagamento(dataLancamento);

            if (ehCartaoDebito) {
                EmpresaVO empresa = getFacade().getEmpresa().consultarPorChavePrimaria(pagamento.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                pagamento.setDataPagamento(Uteis.somarDias(dataLancamento, empresa.getNrDiasCompensacao()));
            }

            Iterator a = pagamento.getCartaoCreditoVOs().iterator();
            Date novaData = pagamento.getDataLancamento();
            while (a.hasNext()) {
                CartaoCreditoVO cartaoCreditoVO = (CartaoCreditoVO) a.next();
                novaData = Uteis.obterDataFutura3(novaData, 1);
                cartaoCreditoVO.setDataCompensacao(novaData);
            }
        }
    }

    /**
     * seta a variavel que indica se houve alteracao para um melhor controle no
     * final
     */
    public void alterado() {
        setListaAlterada(true);
    }

    /**
     * seta a variavel que indica se houve alteracao e recalcula os
     * totalizadores
     */
    public void alteradoComValor() {
        ChequeVO obj = (ChequeVO) context().getExternalContext().getRequestMap().get("cheque");
        obj.setValorTotal(obj.getValor());
        setListaAlterada(true);
        calcularTotalFinal();
    }

    public void consultarUsuario() {
        try {
            setUserPermissao(getFacade().getUsuario().consultarPorChavePrimaria(
                    getUsuario().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setMensagemDetalhada("msg_dados_consultados", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void validarPermissaoUsuario() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                setPaginaDestino(confirmar());
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {

            }
        };

        limparMsg();
        try {
            auto.autorizar("Confirmação de Edição de Pagamento", "EdicaoPagamento",
                    "Você precisa da permissão \"4.22 - Edição de Pagamento\"",
                    "form", listener);

        } catch (Exception e) {
            montarErro(e);
        }
    }

    /**
     * método para finalizar a edição
     */
    public String confirmar() {
        try {
            retorno = "";

            Connection con = null;
            try {
                con = getFacade().getZWFacade().getCon();
                con.setAutoCommit(false);
                recibosEnvolvidos = new ArrayList<Integer>();
                recibosEnvolvidos.add(getRecibo().getCodigo());
                if (isExistemChequesParaDevolver()) {
                    retorno = devolverCheque(con);
                }

                if (!UteisValidacao.emptyString(tipoEdicao) && (tipoEdicao.equals("CA") || tipoEdicao.equals("CD"))) {
                    if (getDataLancamentoCartaoCredito() != null) {
                        validarAlteracaoDeData(getDataLancamentoCartaoCredito(), false);
                    } else if (getDataLancamentoCartaoDebito() != null) {
                        validarAlteracaoDeData(getDataLancamentoCartaoDebito(), true);
                    }

                    if (pagamento.getFormaPagamento().isApresentarNSU() || pagamento.getFormaPagamento().isApresentarCodigoAutorizacao()) {
                        removerEspacosEmBranco(pagamento);
                    }

                    for (CartaoCreditoVO cartao : pagamento.getCartaoCreditoVOs()){
                        cartao.setOperadora(pagamento.getOperadoraCartaoVO());
                    }

                    getFacade().getMovPagamento().alterarSemCommit(pagamento);
                    if (pagamento.getMovPagamentoOrigemCredito() != null && pagamento.getMovPagamentoOrigemCredito() > 0) {
                        getFacade().getMovPagamento().incluirAutorizacao(pagamento.getMovPagamentoOrigemCredito(), pagamento.getAutorizacaoCartao(), pagamento.getNsu());
                    }
                    getFacade().getMovPagamento().incluirAutorizacaoDependentes(pagamento.getCodigo(), pagamento.getAutorizacaoCartao(), pagamento.getNsu());
                    try {
                        registrarLogObjetoVO(pagamento, getCliente().getCodigo(), "MOVPAGAMENTO", getRecibo().getPessoaPagador().getCodigo());
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("MOVPAGAMENTO", getRecibo().getPessoaPagador().getCodigo(),
                                "ERRO AO GERAR LOG DE EDIÇAO DE MOVPAGAMENTO", getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                    if (origem.equals("gestaoRecebiveis")) {
                        atualizarListaGestaoRecebiveis(recibo.getCodigo(), pagamento.getAutorizacaoCartao(), emCartaoCredito);
                    }
                } else {

                    validarDataCheque(userPermissao);
                    for (ChequeVO ch : getCheques()) {
                        if (!ch.isSelecionado()) {
                            if (ch.getCpfOuCnpj() != null && !ch.getCpfOuCnpj().isEmpty()) {
                                if (ch.getCpfOuCnpj().equalsIgnoreCase("cpf")) {
                                    ch.setCnpj("");
                                } else if (ch.getCpfOuCnpj().equalsIgnoreCase("cnpj")) {
                                    ch.setCpf("");
                                }
                            }
                            ch.setVistaOuPrazo(ch.getDataCompensacao().compareTo(Uteis.somarDias(getRecibo().getData(), getEmpresaVO().getNrDiasChequeAVista())) == 1 ? "PR"
                                    : "AV");
                            ch.setNomeNoCheque(ch.getNomeNoCheque());
                        }
                    }
                    for (int l = 0; l < (getCheques().size() - 1); l++) {
                        for (int k = l + 1; k < getCheques().size(); k++) {
                            if (getCheques().get(l).getNumero().equals(getCheques().get(k).getNumero())
                                    && getCheques().get(l).getAgencia().equals(getCheques().get(k).getAgencia())
                                    && getCheques().get(l).getBanco().getCodigo().equals(getCheques().get(k).getBanco().getCodigo())
                                    && getCheques().get(l).getConta().equals(getCheques().get(k).getConta())) {
                                throw new Exception("Dois ou mais cheques apresentam os mesmos dados. São os cheques de número: " + getCheques().get(l).getNumero()
                                        + ".\n Não é possível adicionar cheques com informações iguais, corrija os dados desses cheques antes de confirmar o pagamento.");
                            }
                        }
                    }
                    MovPagamentoVO pag = null;
                    String chequesAlterados ="";
                    for (ChequeVO ch : getCheques()) {
                        pag = null;
                        if (!ch.isSelecionado()) {
                            Iterator i = getPagamentos().iterator();
                            while (i.hasNext()){
                                 pag = (MovPagamentoVO) i.next();
                                 if(pag.getCodigo().equals(ch.getMovPagamento())){
                                     break;
                                 }
                            }
                            if ((ch.getComposicao() != null && !ch.getComposicao().equals("")) || pag.getCredito()){
                                Iterator j = pag.getChequeVOs().iterator();
                                boolean temOriginal = false;
                                while(j.hasNext()){
                                        ChequeVO original = (ChequeVO) j.next();
                                        if (ch.getCodigo().equals(original.getCodigo())){
                                                chequesAlterados = getFacade().getCheque().alterarChequeTransferido(ch, original, chequesAlterados);
                                                temOriginal = true;
                                                break;
                                        }
                                }
                                if(!temOriginal){
                                    getFacade().getCheque().alterar(ch);
                                }
                            } else {
                                getFacade().getCheque().alterar(ch);
                            }
                        }
                    }

                    ProdutosPagosServico produtosPagosServico = new ProdutosPagosServico();
                    for(Integer codigoRecibo : recibosEnvolvidos){
                        produtosPagosServico.setarProdutosPagos(con, codigoRecibo);
                    }
                }
                con.commit();
            } catch (Exception ex) {
                con.rollback();
                setMensagemDetalhada("msg_erro", ex.getMessage());
                setErro(true);
                return "";
            } finally {
                con.setAutoCommit(true);
            }

            processado = true;
            if (retorno.equals("")) {
                if(origem.equals("editarCliente")){
                    abrirTelaClienteRedirect(getCliente().getMatricula());
                    origem = "";
                } else if(origem.equals("gestaoRecebiveis")) {
                    setarModuloFINAN();
                }
            }
            return retorno.equals("") ? origem : retorno;

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }

    public void removerEspacosEmBranco(MovPagamentoVO movPagamentoVO) {
        movPagamentoVO.setNsu(Uteis.removerEspacosInicioFimString(movPagamentoVO.getNsu()));
        movPagamentoVO.setAutorizacaoCartao(Uteis.removerEspacosInicioFimString(movPagamentoVO.getAutorizacaoCartao()));
    }

    private void setarModuloFINAN(){
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        if(loginControle != null){
            loginControle.setModuloAberto(ModuloAberto.FINAN);
        }
    }
    public void atualizarBancoEvent(ActionEvent evt) {
        try {
            setMensagemDetalhada("", "");
            ChequeVO obj = (ChequeVO) context().getExternalContext().getRequestMap().get("cheque");
            if (obj != null) {
              if(UteisValidacao.emptyNumber(obj.getBanco().getCodigoBanco())){
                  obj.setBanco(new BancoVO());
                  throw new Exception("Informe um código para o banco");
              }
              for(BancoVO banco : getListaBancos()){
                if(banco.getCodigoBanco().equals(obj.getBanco().getCodigoBanco())) {
                    obj.setBanco((BancoVO) banco.getClone(true));
                    return;
                }
              }
              obj.setBanco(new BancoVO());
              throw new Exception("Codigo de banco invalido");
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void validarDataCheque(UsuarioVO usuario) throws Exception {
        List<MovPagamentoVO> list = new ArrayList<MovPagamentoVO>();
        MovPagamentoVO pag = new MovPagamentoVO();
        pag.setChequeVOs(cheques);
        pag.setMovPagamentoEscolhida(true);
        pag.getFormaPagamento().setTipoFormaPagamento(TipoFormaPagto.CHEQUE.getSigla());
        list.add(pag);
        getFacade().getMovPagamento().validarPermissaoDataCheque(usuario, parcelas, list);
    }

    private void atualizarListaGestaoRecebiveis(Integer recibo, String codigoAutorizacao, boolean credito) {
        try {
            GestaoRecebiveisControle gestaoRecebiveisControle = (GestaoRecebiveisControle) JSFUtilities.getFromSession(GestaoRecebiveisControle.class.getSimpleName());
            if (credito) {
                for (CartaoCreditoTO cc : gestaoRecebiveisControle.getListaCartoes()) {
                    if (cc.getRecibo() == recibo) {
                        cc.setAutorizacao(codigoAutorizacao);
                    }
                }
            } else {
                for (MovPagamentoVO mp : gestaoRecebiveisControle.getListaOutros()) {
                    if (mp.getReciboPagamento().getCodigo().equals(recibo)) {
                        mp.setAutorizacaoCartao(codigoAutorizacao);
                    }
                }
            }
        } catch (Exception ignored) {
        }
    }

    /**
     * atributo criado para facilitar o operacional, o codigo do banco no
     * chequeVO é um inteiro, e nos navegadores o campo era inicializado com 0.
     * Usando este atributo como String permite que o campo seja inicializado
     * vazio.
     */
    public void setBanco(String banco) {
        ChequeVO aux = (ChequeVO) context().getExternalContext().getRequestMap().get("cheque");
        if (aux != null) {
            aux.getBanco().setCodigoBanco(Integer.valueOf(banco));
        }
        this.banco = banco;
    }

    public boolean isExistemChequesParaDevolver() {
        boolean temChequeSelecionado = false;
        for (ChequeVO chequeVO : getCheques()) {
            if (chequeVO.isSelecionado()) {
                temChequeSelecionado = true;
            }
        }
        return temChequeSelecionado;
    }

    public String getBanco() {
        return banco;
    }

    public ReciboPagamentoVO getRecibo() {
        return recibo;
    }

    public void setRecibo(ReciboPagamentoVO recibo) {
        this.recibo = recibo;
    }

    public List<MovParcelaVO> getParcelas() {
        return parcelas;
    }

    public void setParcelas(List<MovParcelaVO> parcelas) {
        this.parcelas = parcelas;
    }

    public List<MovPagamentoVO> getPagamentos() {
        return pagamentos;
    }

    public void setPagamentos(List<MovPagamentoVO> pagamentos) {
        this.pagamentos = pagamentos;
    }

    public ChequeVO getCheque() {
        return cheque;
    }

    public void setCheque(ChequeVO cheque) {
        this.cheque = cheque;
    }

    public List<ChequeVO> getCheques() {
        return cheques;
    }

    public void setCheques(List<ChequeVO> cheques) {
        this.cheques = cheques;
    }

    public List<BancoVO> getListaBancos() {
        if(listaBancos==null)
        listaBancos = new ArrayList<BancoVO>();
        return listaBancos;
    }

    public void setListaBancos(List<BancoVO> listaBancos) {
        this.listaBancos = listaBancos;
    }

    public List<SelectItem> getListaSelectItemBanco() {
        return listaSelectItemBanco;
    }

    public void setListaSelectItemBanco(List<SelectItem> listaSelectItemBanco) {
        this.listaSelectItemBanco = listaSelectItemBanco;
    }
    public MovPagamentoVO getPagamento() {
        return pagamento;
    }

    public void setPagamento(MovPagamentoVO pagamento) {
        this.pagamento = pagamento;
    }

    public boolean isApresentarCPF() {
        return apresentarCPF;
    }

    public void setApresentarCPF(boolean apresentarCPF) {
        this.apresentarCPF = apresentarCPF;
    }

    public double getTotalCheques() {
        return totalCheques;
    }

    public void setTotalCheques(double totalCheques) {
        this.totalCheques = totalCheques;
    }

    public double getResiduo() {
        return residuo;
    }

    public void setResiduo(double residuo) {
        this.residuo = residuo;
    }

    public double getTotalOriginal() {
        return totalOriginal;
    }

    public void setTotalOriginal(double totalOriginal) {
        this.totalOriginal = totalOriginal;
    }

    public boolean isEmDinheiro() {
        return emDinheiro;
    }

    public void setEmDinheiro(boolean emDinheiro) {
        this.emDinheiro = emDinheiro;
    }

    public boolean isEmCartaoCredito() {
        return emCartaoCredito;
    }

    public void setEmCartaoCredito(boolean emCartaoCredito) {
        this.emCartaoCredito = emCartaoCredito;
    }

    public boolean isEmCartaoDebito() {
        return emCartaoDebito;
    }

    public void setEmCartaoDebito(boolean emCartaoDebito) {
        this.emCartaoDebito = emCartaoDebito;
    }

    public boolean isEmBoleto() {
        return emBoleto;
    }

    public void setEmBoleto(boolean emBoleto) {
        this.emBoleto = emBoleto;
    }

    public String getAbaSelecionada() {
        return abaSelecionada;
    }

    public void setAbaSelecionada(String abaSelecionada) {
        this.abaSelecionada = abaSelecionada;
    }

    public List<SelectItem> getListaSelectItemOperadoraCartaoCredito() {
        return listaSelectItemOperadoraCartaoCredito;
    }

    public void setListaSelectItemOperadoraCartaoCredito(List<SelectItem> listaSelectItemOperadoraCartaoCredito) {
        this.listaSelectItemOperadoraCartaoCredito = listaSelectItemOperadoraCartaoCredito;
    }

    public List<SelectItem> getListaSelectItemOperadoraCartaoDebito() {
        return listaSelectItemOperadoraCartaoDebito;
    }

    public void setListaSelectItemOperadoraCartaoDebito(List<SelectItem> listaSelectItemOperadoraCartaoDebito) {
        this.listaSelectItemOperadoraCartaoDebito = listaSelectItemOperadoraCartaoDebito;
    }

    public List<SelectItem> getListaSelectItemConvenioCobranca() {
        return listaSelectItemConvenioCobranca;
    }

    public void setListaSelectItemConvenioCobranca(List<SelectItem> listaSelectItemConvenioCobranca) {
        this.listaSelectItemConvenioCobranca = listaSelectItemConvenioCobranca;
    }

    public double getTotalOutras() {
        return totalOutras;
    }

    public void setTotalOutras(double totalOutras) {
        this.totalOutras = totalOutras;
    }

    public List<FormaPagamentoVO> getFormasPagamento() {
        return formasPagamento;
    }

    public void setFormasPagamento(List<FormaPagamentoVO> formasPagamento) {
        this.formasPagamento = formasPagamento;
    }

    public boolean isListaAlterada() {
        return listaAlterada;
    }

    public void setListaAlterada(boolean listaAlterada) {
        this.listaAlterada = listaAlterada;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public List<SelectItem> getListaSelectItemNrParcelaCartao() {
        return listaSelectItemNrParcelaCartao;
    }

    public void setListaSelectItemNrParcelaCartao(List<SelectItem> listaSelectItemNrParcelaCartao) {
        this.listaSelectItemNrParcelaCartao = listaSelectItemNrParcelaCartao;
    }

    public void setMudarCampoCpf(int mudarCampoCpf) {
        this.mudarCampoCpf = mudarCampoCpf;
    }

    public int getMudarCampoCpf() {
        return mudarCampoCpf;
    }

    public void setReadOnly(Boolean readOnly) {
        this.readOnly = readOnly;
    }

    public Boolean getReadOnly() {
        return readOnly;
    }

    public void setTituloPagina(String tituloPagina) {
        this.tituloPagina = tituloPagina;
    }

    public String getTituloPagina() {
        return tituloPagina;
    }

    public void setEmpresaVO(EmpresaVO empresa) {
        this.empresaVO = empresa;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setDataNovaParcela(Date dataNovaParcela) {
        this.dataNovaParcela = dataNovaParcela;
    }

    public Date getDataNovaParcela() {
        return dataNovaParcela;
    }

    public List<ChequeVO> getChequesAuxiliar() {
        return chequesAuxiliar;
    }

    public void setChequesAuxiliar(List<ChequeVO> chequesAuxiliar) {
        this.chequesAuxiliar = chequesAuxiliar;
    }

    public UsuarioVO getUserPermissao() {
        return userPermissao;
    }

    public void setUserPermissao(UsuarioVO userPermissao) {
        this.userPermissao = userPermissao;
    }


    public double getTotalDevolver() {
        return totalDevolver;
    }

    public void setTotalDevolver(double totalDevolver) {
        this.totalDevolver = totalDevolver;
    }

    public Date getDataLancamentoCartaoCredito() {
        return dataLancamentoCartaoCredito;
    }

    public void setDataLancamentoCartaoCredito(Date dataLancamentoCartaoCredito) {
        this.dataLancamentoCartaoCredito = dataLancamentoCartaoCredito;
    }

    public String getTipoEdicao() {
        return tipoEdicao;
    }

    public void setTipoEdicao(String tipoEdicao) {
        this.tipoEdicao = tipoEdicao;
    }

    public Date getDataLancamentoCartaoDebito() {
        return dataLancamentoCartaoDebito;
    }

    public void setDataLancamentoCartaoDebito(Date dataLancamentoCartaoDebito) {
        this.dataLancamentoCartaoDebito = dataLancamentoCartaoDebito;
    }

    private void tratarChequesEmContaCorrenteCliente(List<ChequeVO> chequesEmContaCliente) throws Exception {
        List<MovPagamentoVO> pagamentos = new ArrayList<MovPagamentoVO>();
        for (ChequeVO cheque : chequesEmContaCliente) {
            boolean pagamentoAdicionado = false;
            for (MovPagamentoVO pag : pagamentos) {
                if (pag.getCodigo().equals(cheque.getMovPagamento())) {
                    pagamentoAdicionado = true;
                    for (ChequeVO chequeMov : pag.getChequeVOs()) {
                        if (chequeMov.getCodigo().equals(cheque.getCodigo())) {
                            chequeMov.setSituacao("CA");
                            pag.setValor(Uteis.arredondarForcando2CasasDecimais(pag.getValor() - chequeMov.getValor()));
                            break;
                        }
                    }
                    break;
                }
            }
            if (!pagamentoAdicionado) {
                MovPagamentoVO pag = getFacade().getMovPagamento().consultarPorChavePrimaria(cheque.getMovPagamento(), Uteis.NIVELMONTARDADOS_TODOS);
                for (ChequeVO chequeMov : pag.getChequeVOs()) {
                    if (chequeMov.getCodigo().equals(cheque.getCodigo())) {
                        chequeMov.setSituacao("CA");
                        pag.setValor(Uteis.arredondarForcando2CasasDecimais(pag.getValor() - chequeMov.getValor()));
                        break;
                    }
                }
                pagamentos.add(pag);
            }
        }
        if(!pagamentos.isEmpty()){
            ajustarContaCorrenteClienteDevolucoes(pagamentos);
        }
    }

    private void ajustarContaCorrenteClienteDevolucoes(List<MovPagamentoVO> pagamentos) throws Exception {
        List<MovimentoContaCorrenteClienteVO> movimentos = new ArrayList<MovimentoContaCorrenteClienteVO>();
        MovPagamentoVO movClone = null;
         for(MovPagamentoVO pag :pagamentos){
             boolean movimentoAdicionado = false;
             movClone =null;
            if(Uteis.arredondarForcando2CasasDecimais(pag.getValor()) == 0.00){
                pag.setValor(pag.getValorTotal());
                getFacade().getMovPagamento().alterarSemCommit(pag);
            } else {
                movClone = (MovPagamentoVO) pag.getClone(true);
                movClone.setChequeVOs(new ArrayList<ChequeVO>());
                for (ChequeVO chequeMov : pag.getChequeVOs()) {
                    if (chequeMov.getSituacao().equals("CA")) {
                        movClone.getChequeVOs().add(chequeMov);
                    }
                }
                movClone.setValor(Uteis.arredondarForcando2CasasDecimais(pag.getValorTotal() - pag.getValor()));
                movClone.setValorTotal(movClone.getValor());
                pag.setValorTotal(pag.getValor());
                pag = getFacade().getMovPagamento().retiraChequesCancelados((MovPagamentoVO) pag.getClone(true));
                getFacade().getMovPagamento().alterarSemCommit(pag);
                getFacade().getMovPagamento().incluirSemCommit(movClone, true);
            }
            for(MovimentoContaCorrenteClienteVO movimento: movimentos){
                 for(MovPagamentoVO pagMovimento :movimento.getMovPagamentosVOs()){
                     if(pag.getCodigo().equals(pagMovimento.getCodigo())){
                         movimentoAdicionado =true;
                         if(movClone != null){
                             movimento.setValor(movClone.getValor()+movimento.getValor());
                             movimento.setSaldoAtual(movimento.getSaldoAtual()+pag.getValor());
                             movimento.getMovPagamentosVOsTemporario().add(pag);
                         }
                     }
                 }
             }
            if(!movimentoAdicionado){
                MovimentoContaCorrenteClienteVO movimento = getFacade().getMovimentoContaCorrenteCliente().consultarPorMovPagamento(pag.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                movimento.setDescricao("DEVOLUÇÃO DE CHEQUE");
                movimento.setResponsavelAutorizacao(getUsuarioLogado());
                movimento.setTipoMovimentacao("DE");
                movimento.setReciboPagamentoVO(new ReciboPagamentoVO());
                movimento.setValor(0.0);
                movimento.setSaldoAtual(0.0);
                movimento.setMovPagamentosVOsTemporario(new ArrayList<MovPagamentoVO>());
                for(MovPagamentoVO pagMovimento :movimento.getMovPagamentosVOs()){
                    if(pag.getCodigo().equals(pagMovimento.getCodigo())){
                             if(movClone != null){
                                 movimento.setValor(movClone.getValor()+movimento.getValor());
                                 movimento.setSaldoAtual(movimento.getSaldoAtual()+pag.getValor());
                                 movimento.getMovPagamentosVOsTemporario().add(pag);
                             } else {
                                 movimento.setValor(pag.getValor());
                                 pagMovimento.setValor(0.0);
                             }
                    }
                }
                movimentos.add(movimento);
            }
            
        }
        for(MovimentoContaCorrenteClienteVO movimento: movimentos){
             for(MovPagamentoVO pagMovimento :movimento.getMovPagamentosVOs()){
                 if(pagMovimento.getValor() > 0.00){
                    boolean pagamentoAdicionado =false;
                    for(MovPagamentoVO pagMovNovo :movimento.getMovPagamentosVOsTemporario()){
                       if(pagMovNovo.getCodigo().equals(pagMovimento.getCodigo())){
                           pagamentoAdicionado= true;
                           break;
                       }
                    }
                    if(!pagamentoAdicionado){
                        movimento.getMovPagamentosVOsTemporario().add(pagMovimento);
                        movimento.setSaldoAtual(movimento.getSaldoAtual()+pagMovimento.getValor());
                    }
                 }
            }
            movimento.setMovPagamentosVOs(movimento.getMovPagamentosVOsTemporario());
            movimento.setDataRegistro(Calendario.hoje());
            getFacade().getMovimentoContaCorrenteCliente().incluirSemCommit(movimento);
        }
    }

    public ColaboradorVO getColaborador() {
        return colaborador;
    }

    public void setColaborador(ColaboradorVO colaborador) {
        this.colaborador = colaborador;
    }

    private void alterarMovprodutoParcelas(MovProdutoVO produtoDevolucao, MovParcelaVO novaParcela, List<Integer> produtos, Map<Integer, Double> mapProdutosValores) throws Exception {
        for (Integer codigoProduto : produtos) {
            Double valorPago = mapProdutosValores.get(codigoProduto);
            MovProdutoVO movProduto = getFacade().getMovProduto().consultarPorChavePrimaria(codigoProduto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            movProduto.setSituacao("EA");
            getFacade().getMovProduto().alterarSomenteSituacaoSemCommit(movProduto.getCodigo(), movProduto.getSituacao());

            for (Object obj : movProduto.getMovProdutoParcelaVOs()) {
                if (valorPago <= 0.0) {
                    break;
                }
                MovProdutoParcelaVO mpp = (MovProdutoParcelaVO) obj;
                if(!recibosEnvolvidos.contains(mpp.getReciboPagamento().getCodigo())){
                    continue;
                }
                if (Uteis.arredondarForcando2CasasDecimais(mpp.getValorPago()) <= valorPago) {
                    MovProdutoParcelaVO mppNovo = (MovProdutoParcelaVO) mpp.getClone(true);

                    mpp.setMovProduto(produtoDevolucao.getCodigo());
                    getFacade().getMovProdutoParcela().alterar(mpp);

                    mppNovo.setMovParcela(novaParcela.getCodigo());
                    mppNovo.setReciboPagamento(new ReciboPagamentoVO());
                    getFacade().getMovProdutoParcela().incluir(mppNovo);
                    valorPago = Uteis.arredondarForcando2CasasDecimais(valorPago - mpp.getValorPago());
                } else {
                    MovProdutoParcelaVO mppNovoRestante = (MovProdutoParcelaVO) mpp.getClone(true);
                    MovProdutoParcelaVO mppNovoDevolucao = (MovProdutoParcelaVO) mpp.getClone(true);

                    mpp.setValorPago(Uteis.arredondarForcando2CasasDecimais(mpp.getValorPago() - valorPago));
                    getFacade().getMovProdutoParcela().alterar(mpp);

                    mppNovoRestante.setValorPago(valorPago);
                    mppNovoRestante.setMovProduto(produtoDevolucao.getCodigo());
                    getFacade().getMovProdutoParcela().incluir(mppNovoRestante);

                    mppNovoDevolucao.setValorPago(valorPago);
                    mppNovoDevolucao.setMovParcela(novaParcela.getCodigo());
                    mppNovoDevolucao.setReciboPagamento(new ReciboPagamentoVO());
                    getFacade().getMovProdutoParcela().incluir(mppNovoDevolucao);

                    valorPago = 0.0;
                }
            }
        }
    }

    public String[] identificacaoPessoalInternacional() {
        try {
            displayIdentificadorFront = identificadorPessoaInternacional(getEmpresaLogado().getPais().getNome());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return displayIdentificadorFront;

    }

    public String[] getDisplayIdentificadorFront() {
        return displayIdentificadorFront;
    }

    public void setDisplayIdentificadorFront(String[] displayIdentificadorFront) {
        this.displayIdentificadorFront = displayIdentificadorFront;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public boolean isProcessado() {
        return processado;
    }

    public void setProcessado(boolean processado) {
        this.processado = processado;
    }

    public String getTitleCheck(){
        return "Esta ação permitirá que o cheque seja trocado por outra forma de pagamento, mas isso não quer dizer que o cheque foi devolvido no banco." +
                "<i class=\"fa-icon-question-sign\" style=\"font-size: 18px\"><a target=\"_blank\" href=\"https://pactosolucoes.com.br/ajuda/conhecimento/como-lancar-uma-devolucao-de-cheque-no-sistema-sem-o-financeiro-avancado/\">clique aqui: Devolução de cheque<a></i>";
    }

    public String getSituacaoContratoRecibo() {
        return situacaoContratoRecibo;
    }

    public EdicaoPagamentoControle setSituacaoContratoRecibo(String situacaoContratoRecibo) {
        this.situacaoContratoRecibo = situacaoContratoRecibo;
        return this;
    }

    public String getOrigem() {
        return origem;
    }
}
