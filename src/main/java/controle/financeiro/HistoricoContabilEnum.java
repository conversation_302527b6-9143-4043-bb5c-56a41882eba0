package controle.financeiro;

public enum HistoricoContabilEnum {
    ITEM_0("00000", ""),
    ITEM_00001("00001", "00001 Vlr Ref. S"),
    ITEM_00002("00002", "00002 Vlr Pago N/Data S"),
    ITEM_00003("00003", "00003 Pagto n/data Ch.nº S"),
    ITEM_00004("00004", "00004 Vlr Ref. Receita de Serviço nf nº S"),
    ITEM_00005("00005", "00005 Vlr Ref Provisão S"),
    ITEM_00006("00006", "00006 Vlr Recebido n/Data S"),
    ITEM_00007("00007", "00007 Vlr Ref. Receita de Vendas S"),
    ITEM_00008("00008", "00008 Vlr Ref Compra Imobilizado n/data S"),
    ITEM_00009("00009", "00009 Vlr Ref CRTC nº S"),
    ITEM_00010("00010", "00010 Vlr Ref. ICMS n/Mês S"),
    ITEM_00011("00011", "00011 Vlr Ref ISS nf nº S"),
    ITEM_00012("00012", "00012 Vlr Ref IR Ret NFS Nº S"),
    ITEM_00013("00013", "00013 Vlr Ref CSLL Ret NFS Nº S"),
    ITEM_00014("00014", "00014 Vlr Ref PIS Ret NFS Nº S"),
    ITEM_00015("00015", "00015 Vlr Ref COFINS NFS Nº S"),
    ITEM_00016("00016", "00016 Vl. Rec. Ch. nº S"),
    ITEM_00017("00017", "00017 Vl. Apropriado n/ mês S"),
    ITEM_00018("00018", "00018 Vl. Ref. Compra conf. Nf nº S"),
    ITEM_00019("00019", "00019 Vl. compensado n/ mês S"),
    ITEM_00020("00020", "00020 Vlr.ref.Apropriação de encargos n/exercicio cont. S"),
    ITEM_00100("00100", "00100 Vlr.deposito nesta data S"),
    ITEM_00302("00302", "00302 Valores referente a lançamento de encerramento do exercício. N"),
    ITEM_00303("00303", "00303 Vlr Recebido n/Data Rdcard Visael S"),
    ITEM_00304("00304", "00304 Vlr Recebido n/Data Hipercard S"),
    ITEM_00305("00305", "00305 Vlr Recebido n/Data Rdcardmc S"),
    ITEM_00306("00306", "00306 Vlr Recebido n/Data Redcardvs S"),
    ITEM_00307("00307", "00307 Vlr ref a Aplic Auto Mais S"),
    ITEM_00308("00308", "00308 Vlr ref a Aplicaçao CDB S"),
    ITEM_00309("00309", "00309 Vlr ref a Suprimento de Caixa S"),
    ITEM_00310("00310", "00310 Vlr Recebido n/Data Rdacrd Mcdbto S"),
    ITEM_00311("00311", "00311 Vlr.ref a Res Aplic Auto Mais S"),
    ITEM_00312("00312", "00312 Vlr ref a Rend Pago Aplic Auto Mais S"),
    ITEM_00313("00313", "00313 Vlr ref a Recebimento Cielo Visael S"),
    ITEM_00314("00314", "00314 Vlr.Ref a Rendimento Aplic CDB-DI S"),
    ITEM_00315("00315", "00315 Vlr ref a Resgate CDB-DI Itaú S"),
    ITEM_00316("00316", "00316 Vlr ref a Suprimento de Caixa S"),
    ITEM_00317("00317", "00317 Vlr Pago Tarifas Bancarias nesta data S"),
    ITEM_00318("00318", "00318 Vlr.ref a Comprovante a Mutuário S"),
    ITEM_00319("00319", "00319 Vlr Recebido ref a Processo");

    private final String codigo;
    private final String descricao;

    HistoricoContabilEnum(String codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public String getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static HistoricoContabilEnum getPorCodigo(Integer codigo) {
        if (codigo == null) {
            return null;
        }
        String codigoStr = String.format("%05d", codigo);
        for (HistoricoContabilEnum item : HistoricoContabilEnum.values()) {
            if (item.getCodigo().equals(codigoStr)) {
                return item;
            }
        }
        throw new IllegalArgumentException("Código inválido: " + codigo);
    }

}
