package controle.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.utilitarias.FormatadorNumerico;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

public class MovParcelaTransacaoTO {
    private boolean novoObj;
    private String nome;
    private Integer codigoCliente;
    private Integer empresaCodigo;
    private Integer contrato;
    private Integer parcelaCodigo;
    private String descricao;
    private Date dataVencimentoParcela;
    private Double valorParcela;
    private String nomeEmpresa;
    private String nomeConvenioCobranca;
    private String emRemessa;
    private String nomeConsultor;

    public MovParcelaTransacaoTO() {
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getEmpresaCodigo() {
        return empresaCodigo;
    }

    public void setEmpresaCodigo(Integer empresaCodigo) {
        this.empresaCodigo = empresaCodigo;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getParcelaCodigo() {
        return parcelaCodigo;
    }

    public void setParcelaCodigo(Integer parcelaCodigo) {
        this.parcelaCodigo = parcelaCodigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricacao) {
        this.descricao = descricacao;
    }




    public Double getValorParcela() {
        return valorParcela;
    }

    public void setValorParcela(Double valorParcela) {
        this.valorParcela = valorParcela;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public boolean isNovoObj() {
        return novoObj;
    }

    public void setNovoObj(boolean novoObj) {
        this.novoObj = novoObj;
    }

    public Date getDataVencimentoParcela() {
        return dataVencimentoParcela;
    }

    public void setDataVencimentoParcela(Date dataVencimentoParcela) {
        this.dataVencimentoParcela = dataVencimentoParcela;
    }

    public String getDataVencimentoApresentar() {
        if (dataVencimentoParcela == null) {
            return "";
        }
        return Uteis.getData(dataVencimentoParcela);
    }


    public String getEmpresaCodigoApresentar() {
        if (empresaCodigo == null) {
            return "";
        }
        return empresaCodigo.toString();
    }

    public String getContratoApresentar() {
        if(contrato == null){
            return "";
        }
        return contrato.toString();
    }

    public String getParcelaCodigoApresentar() {
        if(parcelaCodigo == null){
            return "";
        }
        return parcelaCodigo.toString();
    }

    public String getValorParcelaApresentar() {
        if (valorParcela == null){
            return "";
        }
        return Formatador.formatarValorMonetarioSemMoeda(valorParcela);
    }

    public String getNomeConvenioCobranca() {
        return nomeConvenioCobranca;
    }

    public void setNomeConvenioCobranca(String nomeConvenioCobranca) {
        this.nomeConvenioCobranca = nomeConvenioCobranca;
    }

    public String getEmRemessa() {
        return emRemessa;
    }

    public void setEmRemessa(String emRemessa) {
        this.emRemessa = emRemessa;
    }

    public String getNomeConsultor() {
        return nomeConsultor;
    }

    public void setNomeConsultor(String nomeConsultor) {
        this.nomeConsultor = nomeConsultor;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }
}