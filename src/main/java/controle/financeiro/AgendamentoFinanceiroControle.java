
package controle.financeiro;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.threads.ThreadAgendamentoFinanceiro;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.financeiro.AgendamentoFinanceiroVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.enumerador.FrequenciaAgendamento;
import negocio.comuns.financeiro.enumerador.LayoutDescricao;
import negocio.comuns.financeiro.enumerador.TipoVisualizacaoAgendamentoFinanceiro;
import negocio.comuns.financeiro.enumerador.TipoVisualizacaoRelatorioDF;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.utilitarias.Conexao;

import javax.faces.model.SelectItem;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 *
 * <AUTHOR> Lhoji Shiozawa
 */
public class AgendamentoFinanceiroControle extends SuperControle {
    private boolean incluir = true;
    private boolean concluido = false;
    private boolean permiteEscolherData = true;
    private Date vencimentoOriginal = null;
    private MovContaVO lancamento = new MovContaVO();
    private AgendamentoFinanceiroVO agenda = new AgendamentoFinanceiroVO();
    private List<SelectItem> listaSelectItemFrequencia = new ArrayList<SelectItem>();
    private List<SelectItem> listaSelectItemLayout = new ArrayList<SelectItem>();
    private String fechaAgendamento;
    private TipoVisualizacaoAgendamentoFinanceiro tipoVisualizacaoAgendamentoFinanceiro;
    
    public AgendamentoFinanceiroControle() {
    }

    public void novo(MovContaVO lancamento, boolean incluir, AgendamentoFinanceiroVO agenda) {
        setMensagemDetalhada("", "");
        try {
            this.agenda = agenda;
            this.incluir = incluir;
            this.lancamento = lancamento;
            this.concluido = false;
            this.permiteEscolherData = true;
            atualizaVencimento();
            montarListaFrequencia();
            montarListaLayout();
            if(incluir) {
                this.agenda.setDescricao(lancamento.getDescricao());
                this.agenda.setVencimentoUltimaParcela(null);
            }
            setMensagem("Informe os dados do agendamento.");
        } catch(Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void montarListaFrequencia() {
        listaSelectItemFrequencia = new ArrayList<SelectItem>();
        for(FrequenciaAgendamento fa : FrequenciaAgendamento.values()) {
            listaSelectItemFrequencia.add(new SelectItem(fa, fa.getDescricao()));
            // seleciona o default
            if(incluir && fa.equals(FrequenciaAgendamento.MENSAL))
                agenda.setFrequencia(fa);
        }
    }

    private void montarListaLayout() {
        listaSelectItemLayout = new ArrayList<SelectItem>();
        for(LayoutDescricao ld : LayoutDescricao.values()) {
            listaSelectItemLayout.add(new SelectItem(ld, ld.getDescricao()));
            // seleciona o default
            if(incluir && ld.equals(LayoutDescricao.MES_REFERENCIA))
                agenda.setLayoutDescricao(ld);
        }
    }

// * Agendamento ***************************************************************

    public void agendar() {
        agendar(false);
    }

    public void gravarPerpetuando() {
        agendar(true);
    }

    public void agendar(boolean perpetuar) {
    	setFechaAgendamento("");
        setMensagemDetalhada("", "");
        List<MovContaVO> listaParaExcluir = null;
        try {
            notificarRecursoEmpresa(RecursoSistema.AGENDAMENTO_CONTA_PAGAR);
            if(concluido)
                throw new Exception("Agendamento ja foi realizado. Clique em fechar.");
            if(incluir) {
                agenda.validarDados();
                if(Calendario.menor(agenda.getProximoVencimento(), lancamento.getDataVencimento()))
                    throw new Exception("Data de vencimento da proxima parcela não pode ser anterior a "+Uteis.getData(lancamento.getDataVencimento()));
                if(agenda.getVencimentoUltimaParcela() != null && Calendario.maior(agenda.getProximoVencimento(), agenda.getVencimentoUltimaParcela()))
                    throw new Exception("Data de vencimento da ultima parcela tem que ser maior ou igual a "+Uteis.getData(agenda.getProximoVencimento()));
                lancamento = getFacade().getFinanceiro().getMovConta().consultarPorCodigo(lancamento.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                ThreadAgendamentoFinanceiro.gerarParcelasParaAgendamento(Conexao.getFromSession(), agenda, lancamento, Calendario.getInstance());
                registrarLogAgendamentoManualmente(true, listaParaExcluir);
                setMensagemDetalhada("msg_dados_gravados", "");
                setFechaAgendamento("Richfaces.hideModalPanel('agendamentoFinanceiroPanel');alert('Agendamento salvo com sucesso');");
            } else {
                // se a data de vencimento é posterior a data original
                if(vencimentoOriginal != null && Calendario.maior(agenda.getVencimentoUltimaParcela(), vencimentoOriginal))
                    throw new Exception("Data de vencimento da ultima parcela não pode ser maior que a data original "+Uteis.getData(vencimentoOriginal));
                // se a data de vencimento foi alterada
                if((vencimentoOriginal == null && agenda.getVencimentoUltimaParcela() != null) ||
                   (vencimentoOriginal != null && !vencimentoOriginal.equals(agenda.getVencimentoUltimaParcela()))) {
                    listaParaExcluir = getFacade().getFinanceiro().getMovConta().
                            consultarPeloAgendamentoVencimentoNaoQuitadas(agenda.getCodigo(),
                            agenda.getVencimentoUltimaParcela(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    // exclui todos os lancamentos posteriores ao vencimento da ultima
                    getFacade().getFinanceiro().getMovConta().excluirLista(listaParaExcluir);
                }
                // verifica se todos os lançamentos do agendamento foram excluidos
                List<MovContaVO> lista = getFacade().getFinanceiro().getMovConta().
                    consultarLancamentosPeloAgendamento(agenda.getCodigo(),null,null,
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                // se foram excluidos
                if(lista.isEmpty()) {
                    // exclui o agendamento tbm
                    getFacade().getFinanceiro().getAgendamentoFinanceiro().excluir(agenda.getCodigo());
                    setMensagemDetalhada("msg_dados_excluidos", "");
                    setFechaAgendamento("Richfaces.hideModalPanel('agendamentoFinanceiroPanel');alert('Agendamento excluído com sucesso');");
                } else {
                    // senao somente altera o agendamento
                    getFacade().getFinanceiro().getAgendamentoFinanceiro().alterar(agenda);
                    registrarLogAgendamentoManualmente(false, listaParaExcluir);
                    setMensagemDetalhada("msg_dados_gravados", "");
                    setFechaAgendamento("Richfaces.hideModalPanel('agendamentoFinanceiroPanel');alert('Agendamento editado com sucesso');");
                }
            }




            concluido = true;
            atualizarAgendamentosLancamento();
            if(perpetuar){
                MovContaControle controlMovConta = (MovContaControle) JSFUtilities.getFromSession(MovContaControle.class.getSimpleName());
                if (controlMovConta != null) {
                    Map<Integer, Date> mapaVencimento = getFacade().getFinanceiro().getAgendamentoFinanceiro().atualizarDatasVencimentoFuturo(agenda, controlMovConta.getMovContaVO().getEmpresaVO());
                    Date vencimento = mapaVencimento.get(controlMovConta.getMovContaVO().getCodigo());
                    if(vencimento != null){
                        controlMovConta.getMovContaVO().setDataVencimento(vencimento);
                    }

                    for(MovContaVO movconta : controlMovConta.getParcelasRelacionadas()){
                        if(Calendario.maiorOuIgual(movconta.getDataVencimento(), Calendario.hoje())){
                            movconta.setDescricao(ThreadAgendamentoFinanceiro.retornarDescricaoMovConta(agenda, movconta.getDataVencimento(), movconta.getNrParcela()));
                        }
                    }
                } else {
                    getFacade().getFinanceiro().getAgendamentoFinanceiro().atualizarDatasVencimentoFuturo(agenda, null);
                }
            }
        } catch(Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void registrarLogAgendamentoManualmente(boolean inclusao, List<MovContaVO> listaParcelaExcluir)throws Exception{
        LogVO obj = new LogVO();
        obj.setChavePrimaria(this.agenda.getCodigo().toString());
        obj.setNomeEntidade("AGENDAMENTOFINANCEIRO");
        obj.setNomeEntidadeDescricao("AGENDAMENTO FINANCEIRO");
        obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
        obj.setUserOAMD(getUsuarioLogado().getUserOamd());
        obj.setNomeCampo("MENSAGEM");
        obj.setValorCampoAlterado("");
        StringBuilder msg = new StringBuilder();
        StringBuilder listaPacelas = new StringBuilder();
        StringBuilder listaPacelasExcluir = new StringBuilder();
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        String proximoVencimento = sdf.format(agenda.getProximoVencimento());
        int qtdeParcelasGeradas = 0;
        if (inclusao){
            obj.setOperacao("INCLUSÃO");
            AgendamentoFinanceiroVO agendamentoFinanceiroVO = getFacade().getFinanceiro().getAgendamentoFinanceiro().consultarPorChavePrimaria(agenda.getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            proximoVencimento = sdf.format(agendamentoFinanceiroVO.getProximoVencimento());
            List<MovContaVO> listaMovConta = getFacade().getFinanceiro().getMovConta().consultarPorAgendamento(agenda.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            qtdeParcelasGeradas = listaMovConta.size();
            for (MovContaVO movContaVO: listaMovConta){
                listaPacelas.append("PARCELA ").append(movContaVO.getCodigo()).append(" - ").append(sdf.format(movContaVO.getDataVencimento())).append(" - ").append(Formatador.formatarValorMonetario(movContaVO.getValor())).append("\n");
            }

        } else{
            obj.setOperacao("ALTERAÇÃO");
        }
        if ((listaParcelaExcluir != null) && (listaParcelaExcluir.size() > 0)){
            for (MovContaVO movContaVO: listaParcelaExcluir){
                listaPacelasExcluir.append("PARCELA ").append(movContaVO.getCodigo()).append(" - ").append(sdf.format(movContaVO.getDataVencimento())).append(" - ").append(Formatador.formatarValorMonetario(movContaVO.getValor())).append("\n");
            }
        }
        msg.append("Descrição:").append(agenda.getDescricao()).append("\n");
        msg.append("Frequência:").append(agenda.getFrequencia().getDescricao()).append("\n");
        msg.append("Dia vencimento:").append(agenda.getDiaVencimento()).append("\n");
        msg.append("Próximo vencimento:").append(proximoVencimento).append("\n");
        if (agenda.getVencimentoUltimaParcela() != null){
            msg.append("Vencimento da última parcela:").append(sdf.format(agenda.getVencimentoUltimaParcela())).append("\n");
        }else{
            msg.append("Vencimento da última parcela: não informado").append("\n");
        }

        msg.append("Gerar ").append(agenda.getQtdeParcelasGerar()).append(" parcelas a cada nova geração") .append("\n");
        msg.append("Gerar novas parcelas quando faltar ").append(agenda.getQtdeDiasNovaGeracao()).append(" dias para vencer a última parcela") .append("\n");
        String simNao = (agenda.isUsaParcelasFixas()) ? "SIM" : "NÃO";
        msg.append("Gerar quantidade de parcelas fixas: ").append(simNao).append("\n");
        if (listaPacelas.length() > 0){
            msg.append("Parcelas geradas (").append(qtdeParcelasGeradas).append("):  \n").append(listaPacelas.toString()).append("\n");
        }
        if (listaPacelasExcluir.length() > 0){
            msg.append("Após ter alterado o campo vencimento da última parcela o sistema excluiu as parcelas abaixo (").append(listaParcelaExcluir.size()).append("): \n").append(listaPacelasExcluir.toString()).append("\n");
        }

        obj.setValorCampoAlterado(msg.toString());
        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        registrarLogObjetoVO(obj, agenda.getCodigo());
    }


    public void realizarConsultaLogAgendamentoFinanceiro() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = agenda.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse("Agendamento Financeiro");
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), this.agenda.getCodigo(), 0);
        setMsgAlert("abrirPopup('" + request().getContextPath() + "/faces/visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);");
    }
    
    public void atualizarAgendamentosLancamento() throws Exception{
    	MovContaControle controlMovConta = (MovContaControle) JSFUtilities.getFromSession(MovContaControle.class.getSimpleName());
        if (controlMovConta == null) {
        	controlMovConta = new MovContaControle();
        } else {
        	this.lancamento.setNrParcela(getFacade().getFinanceiro().getMovConta().obterNrParcela(this.lancamento.getCodigo()));
        	controlMovConta.setarMovConta(this.lancamento, true, false);
        }
    }
    
    public void usarParcelas() {
        if(agenda.isUsaParcelasFixas()) {
            agenda.setVencimentoUltimaParcela(null);
        } else {
            agenda.setLayoutDescricao(LayoutDescricao.MES_REFERENCIA);
            agenda.setParcelaInicial(0);
            agenda.setParcelaFinal(0);
        }
    }

    public void selecionaFrequencia() {
        agenda.setQtdeParcelasGerar(agenda.getFrequencia().getQtdeParcelasGerar());
        agenda.setQtdeDiasNovaGeracao(agenda.getFrequencia().getQtdeDiasNovaGeracao());
        permiteEscolherData = !agenda.getFrequencia().equals(FrequenciaAgendamento.SEMANAL);
        atualizaVencimento();
    }

    public void atualizaVencimento() {
        if(incluir) {
            agenda.setDiaVencimento(Uteis.getDiaMesData(lancamento.getDataVencimento()));
            if(agenda.getFrequencia().equals(FrequenciaAgendamento.SEMANAL)) {
                agenda.setProximoVencimento(Uteis.obterDataFutura2(lancamento.getDataVencimento(), agenda.getFrequencia().getQtdeDias()));
            } else {
                int qtdeMeses = (agenda.getFrequencia().equals(FrequenciaAgendamento.MENSAL) ? 1 :
                                (agenda.getFrequencia().equals(FrequenciaAgendamento.SEMESTRAL) ? 6 : 12));
                agenda.setProximoVencimento(Uteis.obterDataFutura3(lancamento.getDataVencimento(), qtdeMeses));
            }
        } else {
            if(agenda.getVencimentoUltimaParcela() == null)
                vencimentoOriginal = null;
            else
                vencimentoOriginal = (Date)agenda.getVencimentoUltimaParcela().clone();
        }
    }

    public void alterarVencimento() {
        Calendar diaV = Calendario.getInstance();
        diaV.set(Calendar.DAY_OF_MONTH, agenda.getDiaVencimento());
        agenda.setProximoVencimento(diaV.getTime());
        if(agenda.getFrequencia().equals(FrequenciaAgendamento.SEMANAL)) {
            agenda.setProximoVencimento(Uteis.obterDataFutura2(agenda.getProximoVencimento(), agenda.getFrequencia().getQtdeDias()));
        } else {
            int qtdeMeses = (agenda.getFrequencia().equals(FrequenciaAgendamento.MENSAL) ? 1 :
                            (agenda.getFrequencia().equals(FrequenciaAgendamento.SEMESTRAL) ? 6 : 12));
            agenda.setProximoVencimento(Uteis.obterDataFutura3(agenda.getProximoVencimento(), qtdeMeses));
        }
    }

    public void alterarQtdeDiasParaGerar() {
        agenda.setQtdeDiasNovaGeracao(agenda.getQtdeParcelasGerar()*agenda.getFrequencia().getQtdeDias()/3);
    }

    public List getTiposVisualizacao() throws Exception {
        List itens = new ArrayList();
        for (TipoVisualizacaoAgendamentoFinanceiro tipo : TipoVisualizacaoAgendamentoFinanceiro.values()) {
            itens.add(new SelectItem(TipoVisualizacaoAgendamentoFinanceiro.getTipoVisualizacao(tipo.getCodigo()), tipo.getDescricao()));
        }
        return itens;
    }

    public void acoesRadioBoxTipoAgendamento() {
        if (getTipoVisualizacaoAgendamentoFinanceiro() != null && !getTipoVisualizacaoAgendamentoFinanceiro().equals(TipoVisualizacaoAgendamentoFinanceiro.ATE_DETERMINADO_VENCIMENTO)) {
            getAgenda().setVencimentoUltimaParcela(null);
        }
        if (getTipoVisualizacaoAgendamentoFinanceiro() != null && getTipoVisualizacaoAgendamentoFinanceiro().equals(TipoVisualizacaoAgendamentoFinanceiro.QUANTIDADE_PARCELAS_FIXAS)) {
            getAgenda().setUsaParcelasFixas(true);
        } else {
            getAgenda().setUsaParcelasFixas(false);
        }
    }
// * gets e sets ***************************************************************

    public AgendamentoFinanceiroVO getAgenda() {
        return agenda;
    }

    public void setAgenda(AgendamentoFinanceiroVO agenda) {
        this.agenda = agenda;
    }

    public List<SelectItem> getListaSelectItemFrequencia() {
        return listaSelectItemFrequencia;
    }

    public void setListaSelectItemFrequencia(List<SelectItem> listaSelectItemFrequencia) {
        this.listaSelectItemFrequencia = listaSelectItemFrequencia;
    }

    public List<SelectItem> getListaSelectItemLayout() {
        return listaSelectItemLayout;
    }

    public void setListaSelectItemLayout(List<SelectItem> listaSelectItemLayout) {
        this.listaSelectItemLayout = listaSelectItemLayout;
    }

    public MovContaVO getLancamento() {
        return lancamento;
    }

    public void setLancamento(MovContaVO lancamento) {
        this.lancamento = lancamento;
    }

    public boolean isIncluir() {
        return incluir;
    }

    public void setIncluir(boolean incluir) {
        this.incluir = incluir;
    }

    public boolean isPermiteEscolherData() {
        return permiteEscolherData;
    }

    public void setPermiteEscolherData(boolean permiteEscolherData) {
        this.permiteEscolherData = permiteEscolherData;
    }

    public void setFechaAgendamento(String fechaAgendamento) {
       this.fechaAgendamento = fechaAgendamento;
    }

    public String getFechaAgendamento() {
        return fechaAgendamento;
    }

    public boolean isAlteracaoEmParcelasFixas() {
        return !incluir && agenda.isUsaParcelasFixas();
    }

    public TipoVisualizacaoAgendamentoFinanceiro getTipoVisualizacaoAgendamentoFinanceiro() {
        return tipoVisualizacaoAgendamentoFinanceiro;
    }

    public void setTipoVisualizacaoAgendamentoFinanceiro(TipoVisualizacaoAgendamentoFinanceiro tipoVisualizacaoAgendamentoFinanceiro) {
        this.tipoVisualizacaoAgendamentoFinanceiro = tipoVisualizacaoAgendamentoFinanceiro;
    }

    public boolean getTipoIntervaloParcelas() {
        return tipoVisualizacaoAgendamentoFinanceiro != null && tipoVisualizacaoAgendamentoFinanceiro.equals(TipoVisualizacaoAgendamentoFinanceiro.INTERVALO_PARCELAS);
    }

    public boolean getTipoQtdParcelasFixas() {
        return tipoVisualizacaoAgendamentoFinanceiro != null && tipoVisualizacaoAgendamentoFinanceiro.equals(TipoVisualizacaoAgendamentoFinanceiro.QUANTIDADE_PARCELAS_FIXAS);
    }

    public boolean getTipoAteDeterminadoVencimento() {
        return tipoVisualizacaoAgendamentoFinanceiro != null && tipoVisualizacaoAgendamentoFinanceiro.equals(TipoVisualizacaoAgendamentoFinanceiro.ATE_DETERMINADO_VENCIMENTO);
    }
}
