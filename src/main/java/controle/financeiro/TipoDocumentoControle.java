package controle.financeiro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.financeiro.TipoDocumentoVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;

import javax.faces.event.ActionEvent;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Classe controladora dos cadastros de tipos de conta
 *
 * <AUTHOR>
 */
public class TipoDocumentoControle extends SuperControle {
    // objeto do formulário de cadastro

    private TipoDocumentoVO tipoDocumentoVO;
    // consulta dos dados
    private List listaConsulta;

    public TipoDocumentoControle() throws Exception {
        setTipoDocumentoVO(new TipoDocumentoVO());
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    public void abrirTipoDocumento() {
        try {
            validarPermissaoTipoDocumento();
            setMsgAlert("abrirPopup('" + request().getContextPath() + "/faces/finanTipoDocumentoCons.jsp?modulo=financeiroWeb', 'TipoDocumento', 800, 595);");
        } catch (Exception e) {
            setMsgAlert("alert('" + e.getMessage() + "');");
        }
    }

    /**
     * Valida a permissão do usuário logado para a entidade TipoDocumento
     * que usa a permissão "Tipo de Documento"
     *
     * @throws Exception
     */
    public void validarPermissaoTipoDocumento() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "TipoDocumento", "9.10 - Tipo de Documento");
            }
        }
    }

    /**
     * Chamado para criar uma nova conta
     *
     * @return navigation-case
     */
    public String novo() {
        setTipoDocumentoVO(new TipoDocumentoVO());
        tipoDocumentoVO.setNovoObj(true);
        tipoDocumentoVO.setObjetoVOAntesAlteracao(new TipoDocumentoVO());
        setMensagemID("");
        setSucesso(true);
        setErro(false);
        return "editar";
    }

    public String editar() throws Exception {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        TipoDocumentoVO obj = getFacade().getFinanceiro().getTipoDocumento().consultarPorCodigo(codigoConsulta);
        obj.setNovoObj(false);
        obj.registrarObjetoVOAntesDaAlteracao();
        setTipoDocumentoVO(obj);
        return "editar";
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");
        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getFinanceiro().getTipoDocumento().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);
    }


    // /CONSULTA PAGINADA
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            tipoDocumentoVO.setDescricao(getControleConsulta().getValorConsulta());
            objs = getFacade().getFinanceiro().getTipoDocumento().consultar(tipoDocumentoVO);
            objs = ControleConsulta.obterSubListPaginaApresentar(objs,
                    controleConsulta);
            definirVisibilidadeLinksNavegacao(
                    controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Grava a conta
     *
     * @return
     */
    public String gravar() {
        try {
            if (tipoDocumentoVO.isNovoObj().booleanValue()) {
                getFacade().getFinanceiro().getTipoDocumento().incluir(tipoDocumentoVO);
                inicializarLog();
            } else {
                getFacade().getFinanceiro().getTipoDocumento().alterar(tipoDocumentoVO);
                incluirLogAlteracao();
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /**
     * Acionado na tela de edição, exclui a conta
     *
     * @return navigation-case
     */
    public String excluir() {
        try {
            getFacade().getFinanceiro().getTipoDocumento().excluir(tipoDocumentoVO);
            incluirLogExclusao();
            setTipoDocumentoVO(new TipoDocumentoVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    // GETTERS AND SETTERS
    public TipoDocumentoVO getTipoDocumentoVO() {
        return tipoDocumentoVO;
    }

    public void setTipoDocumentoVO(TipoDocumentoVO tipoDocumentoVO) {
        this.tipoDocumentoVO = tipoDocumentoVO;
    }

    public List getListaConsulta() {
        return listaConsulta;
    }

    public void setListaConsulta(List listaConsulta) {
        this.listaConsulta = listaConsulta;
    }

    public void realizarConsultaLogObjetoGeral() {
        tipoDocumentoVO = new TipoDocumentoVO();
        realizarConsultaLogObjetoSelecionado();
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = "TIPODOCUMENTO";
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(),
                getTipoDocumentoVO().getCodigo(), 0);
    }

    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(tipoDocumentoVO, tipoDocumentoVO.getCodigo(), "TIPODOCUMENTO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TIPODOCUMENTO", tipoDocumentoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAO DE TIPO DE DOCUMENTO ", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        tipoDocumentoVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void inicializarLog() throws Exception {
        try {
            tipoDocumentoVO.setNovoObj(true);
            registrarLogObjetoVO(tipoDocumentoVO, tipoDocumentoVO.getCodigo(), "TIPODOCUMENTO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TIPODOCUMENTO", tipoDocumentoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAO DE TIPO DE DOCUMENTO ", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        tipoDocumentoVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void incluirLogExclusao() throws Exception {
        try {
            tipoDocumentoVO.setObjetoVOAntesAlteracao(new TipoDocumentoVO());
            tipoDocumentoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(tipoDocumentoVO, tipoDocumentoVO.getCodigo(), "TIPODOCUMENTO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TIPODOCUMENTO", tipoDocumentoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE TIPO DE DOCUMENTO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();

        }
    }
}
