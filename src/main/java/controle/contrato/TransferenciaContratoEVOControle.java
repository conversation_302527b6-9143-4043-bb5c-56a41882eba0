package controle.contrato;

import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import controle.basico.ClienteControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas contratoOperacaoForm.jsp contratoOperacaoCons.jsp) com as
 * funcionalidades da classe
 * <code>ContratoOperacao</code>. Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see ContratoOperacao
 * @see ContratoOperacaoVO
 */
public class TransferenciaContratoEVOControle extends SuperControle {

    private ContratoVO contratoVO;
    private ClienteVO clienteVO;
    private ClienteVO clienteDestinoVO;
    private List<MovParcelaVO> parcelasEmAberto;

    public TransferenciaContratoEVOControle() {

    }

    public void novo() {
        try {
            setMensagemDetalhada("");
            setContratoVO(new ContratoVO());
            setClienteVO(new ClienteVO());
            setErro(false);
            setSucesso(true);

            prepararDados();

            setMsgAlert("Richfaces.showModalPanel('pnlTransferirContrato');");

            setMensagem("");
            setMensagemDetalhada("msg_dados_consultados", "");
        } catch (Exception e) {
            montarErro(e);
        }

    }

    public List<ClienteVO> consultarClientes(Object suggest) {
        String nomeOuMatricula = (String) suggest;
        List<ClienteVO> clientes = new ArrayList<>();
        int matricula = 0;

        try {
            matricula = Integer.parseInt(nomeOuMatricula);
        } catch (Exception ignored) {
        }

        try {
            if (matricula > 0) {
                clientes = getFacade().getCliente().consultarPorMatricula(nomeOuMatricula, 0, null, "VI", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                clientes = getFacade().getCliente().consultarPorNomePessoa(nomeOuMatricula, 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS, 50, "VI");
            }
        } catch (Exception e) {
            Uteis.logar(e, TransferenciaContratoEVOControle.class);
        }

        return clientes;
    }

    public void selecionarClienteDestino() {
        ClienteVO cliente = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
        setClienteDestinoVO(cliente);
    }

    public void prepararDados() throws Exception {
        ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
        if (clienteControle != null) {
            setErro(false);

            clienteControle.pegarClienteTelaCliente();

            setClienteVO(clienteControle.getClienteVO());
            setContratoVO(clienteControle.getContratoVO());

            setParcelasEmAberto(getFacade().getMovParcela()
                    .consultarEmAbertoPorContrato(getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
        }
    }

    public void transferirContrato() {
        try {
            limparMsg();

            if (clienteDestinoVO == null || UteisValidacao.emptyNumber(clienteDestinoVO.getCodigo())) {
                throw new ValidacaoException("Não foi informado para quem deseja transferir o contrato");
            }

            getFacade().getZWFacade().transferirContratoEVO(getContratoVO(), getClienteDestinoVO(), getClienteVO(), getUsuarioLogado());

            atualizarBaseOffline();

            getClienteVO().setDeveAtualizarDependentesSintetico(true);
            getFacade().getSituacaoClienteSinteticoDW().excluir(getClienteVO().getCodigo());
            getFacade().getZWFacade().atualizarSintetico(getClienteVO(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);

            getClienteDestinoVO().setDeveAtualizarDependentesSintetico(true);
            getFacade().getSituacaoClienteSinteticoDW().excluir(getClienteDestinoVO().getCodigo());
            getFacade().getZWFacade().atualizarSintetico(getClienteDestinoVO(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
            montarSucessoGrowl("Transferência realizada com sucesso.");
        } catch (Exception ex) {
            montarErro(ex);
            ex.printStackTrace();
        }
    }

    private void atualizarBaseOffline() throws Exception {
        getFacade().getSituacaoClienteSinteticoDW().atualizarBaseOffLineZillyonAcesso((String) JSFUtilities.getFromSession(JSFUtilities.KEY), getClienteVO().getPessoa().getCodigo());
        getFacade().getSituacaoClienteSinteticoDW().atualizarBaseOffLineZillyonAcesso((String) JSFUtilities.getFromSession(JSFUtilities.KEY), getClienteDestinoVO().getPessoa().getCodigo());
    }

    public ContratoVO getContratoVO() {
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public ClienteVO getClienteDestinoVO() {
        return clienteDestinoVO;
    }

    public void setClienteDestinoVO(ClienteVO clienteDestinoVO) {
        this.clienteDestinoVO = clienteDestinoVO;
    }

    public List<MovParcelaVO> getParcelasEmAberto() {
        if (parcelasEmAberto == null) {
            parcelasEmAberto = new ArrayList<>();
        }
        return parcelasEmAberto;
    }

    public void setParcelasEmAberto(List<MovParcelaVO> parcelasEmAberto) {
        this.parcelasEmAberto = parcelasEmAberto;
    }
}
