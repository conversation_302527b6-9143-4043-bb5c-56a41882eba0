package controle.contrato;
import negocio.interfaces.contrato.ContratoModalidadeTurmaInterfaceFacade;
import negocio.facade.jdbc.contrato.ContratoModalidadeTurma;
import negocio.comuns.utilitarias.*;
import negocio.comuns.contrato.*;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;
import negocio.comuns.plano.TurmaVO;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * contratoModalidadeTurmaForm.jsp contratoModalidadeTurmaCons.jsp) com as funcionalidades da classe <code>ContratoModalidadeTurma</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see ContratoModalidadeTurma
 * @see ContratoModalidadeTurmaVO
*/
public class ContratoModalidadeTurmaControle extends SuperControle {
    private ContratoModalidadeTurmaVO contratoModalidadeTurmaVO;
    protected List listaSelectItemTurma;
    /**
    * Interface <code>ContratoModalidadeTurmaInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
    * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
    */
    private ContratoModalidadeHorarioTurmaVO contratoModalidadeHorarioTurmaVO;

    public ContratoModalidadeTurmaControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
    }

    /**
    * Rotina responsável por disponibilizar um novo objeto da classe <code>ContratoModalidadeTurma</code>
    * para edição pelo usuário da aplicação.
    */
    public String novo() {
        setContratoModalidadeTurmaVO(new ContratoModalidadeTurmaVO());
    //    inicializarListasSelectItemTodosComboBox();
        setContratoModalidadeHorarioTurmaVO(new ContratoModalidadeHorarioTurmaVO());
        setMensagemID("msg_entre_dados");
        return "editar";
    }

    /**
    * Rotina responsável por disponibilizar os dados de um objeto da classe <code>ContratoModalidadeTurma</code> para alteração.
    * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
    */
    public String editar() {
        ContratoModalidadeTurmaVO obj = (ContratoModalidadeTurmaVO)context().getExternalContext().getRequestMap().get("contratoModalidadeTurma");
        inicializarAtributosRelacionados(obj);
        obj.setNovoObj(new Boolean(false));
        setContratoModalidadeTurmaVO(obj);
    //   inicializarListasSelectItemTodosComboBox();
        setContratoModalidadeHorarioTurmaVO(new ContratoModalidadeHorarioTurmaVO());
        setMensagemID("msg_dados_editar");
        return "editar";
    }

    /**
    * Método responsável inicializar objetos relacionados a classe <code>ContratoModalidadeTurmaVO</code>.
    * Esta inicialização é necessária por exigência da tecnologia JSF, que não trabalha com valores nulos para estes atributos.
    */
    public void inicializarAtributosRelacionados(ContratoModalidadeTurmaVO obj) {
        if (obj.getTurma() == null) {
            obj.setTurma(new TurmaVO());
        }
    }

    /**
    * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>ContratoModalidadeTurma</code>.
    * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
    * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
    */
    public String gravar() {
        try {
            if (contratoModalidadeTurmaVO.isNovoObj().booleanValue()) {
            	getFacade().getZWFacade().incluirContratoModalidadeTurma(contratoModalidadeTurmaVO);
            } else {
                getFacade().getContratoModalidadeTurma().alterar(contratoModalidadeTurmaVO);
            }
            setMensagemID("msg_dados_gravados");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /**
    * Rotina responsavel por executar as consultas disponiveis no JSP ContratoModalidadeTurmaCons.jsp.
    * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
    * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
    */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getContratoModalidadeTurma().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("contratoModalidade")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getContratoModalidadeTurma().consultarPorContratoModalidade(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("codigoContratoModalidadeTurma")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getContratoModalidadeTurma().consultarPorCodigoContratoModalidadeTurma(new Integer(valorInt), Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>ContratoModalidadeTurmaVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getContratoModalidadeTurma().excluir(contratoModalidadeTurmaVO);
            setContratoModalidadeTurmaVO( new ContratoModalidadeTurmaVO());

            setContratoModalidadeHorarioTurmaVO(new ContratoModalidadeHorarioTurmaVO());
            setMensagemID("msg_dados_excluidos");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /* Método responsável por adicionar um novo objeto da classe <code>ContratoModalidadeHorarioTurma</code>
     * para o objeto <code>contratoModalidadeTurmaVO</code> da classe <code>ContratoModalidadeTurma</code>
     */ 
    public String adicionarContratoModalidadeHorarioTurma() throws Exception {
        try {
            if (!getContratoModalidadeTurmaVO().getCodigo().equals(new Integer(0))) {
                contratoModalidadeHorarioTurmaVO.setContratoModalidadeTurma(getContratoModalidadeTurmaVO().getCodigo());
            }
            getContratoModalidadeTurmaVO().adicionarObjContratoModalidadeHorarioTurmaVOs( getContratoModalidadeHorarioTurmaVO());
            this.setContratoModalidadeHorarioTurmaVO(new ContratoModalidadeHorarioTurmaVO());
            setMensagemID("msg_dados_adicionados");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>ContratoModalidadeHorarioTurma</code>
     * para edição pelo usuário.
     */ 
    public String editarContratoModalidadeHorarioTurma() throws Exception {
        ContratoModalidadeHorarioTurmaVO obj = (ContratoModalidadeHorarioTurmaVO)context().getExternalContext().getRequestMap().get("contratoModalidadeHorarioTurma");
        setContratoModalidadeHorarioTurmaVO(obj);
        return "editar";
    }

    /* Método responsável por remover um novo objeto da classe <code>ContratoModalidadeHorarioTurma</code>
     * do objeto <code>contratoModalidadeTurmaVO</code> da classe <code>ContratoModalidadeTurma</code>
     */ 
    public String removerContratoModalidadeHorarioTurma() throws Exception {
        ContratoModalidadeHorarioTurmaVO obj = (ContratoModalidadeHorarioTurmaVO)context().getExternalContext().getRequestMap().get("contratoModalidadeHorarioTurma");
        getContratoModalidadeTurmaVO().excluirObjContratoModalidadeHorarioTurmaVOs(obj.getHorarioTurma().getCodigo().intValue());
        setMensagemID("msg_dados_excluidos");
        return "editar";
    }

    public void irPaginaInicial() throws Exception{
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

//    /**
//     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
//     * o comboBox relativo ao atributo <code>Turma</code>.
//    */
//    public void montarListaSelectItemTurma(Integer prm) throws Exception {
//        List resultadoConsulta = consultarContratoModalidadeTurmaPorCodigo(prm);
//        Iterator i = resultadoConsulta.iterator();
//        List objs = new ArrayList();
//        objs.add(new SelectItem(new Integer(0), ""));
//        while (i.hasNext()) {
//            ContratoModalidadeTurmaVO obj = (ContratoModalidadeTurmaVO)i.next();
//            objs.add(new SelectItem(obj.getCodigo(), obj.getCodigo().toString()));
//        }
//        setListaSelectItemTurma(objs);
//    }
//
//    /**
//     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Turma</code>.
//     * Buscando todos os objetos correspondentes a entidade <code>ContratoModalidadeTurma</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
//     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
//    */
//    public void montarListaSelectItemTurma() {
//        try {
//            montarListaSelectItemTurma(new Integer(0));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>codigo</code>
//     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
//    */
//    public List consultarContratoModalidadeTurmaPorCodigo(Integer codigoPrm) throws Exception {
//        List lista = contratoModalidadeTurmaFacade.consultarPorCodigo(codigoPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
//        return lista;
//    }
//
//    /**
//     * Método responsável por inicializar a lista de valores (<code>SelectItem</code>) para todos os ComboBox's.
//    */
//    public void inicializarListasSelectItemTodosComboBox() {
//        montarListaSelectItemTurma();
//    }

    /**
    * Rotina responsável por preencher a combo de consulta da telas.
    */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("contratoModalidade", "ContratoModalidade"));
        itens.add(new SelectItem("codigoContratoModalidadeTurma", "Turma"));
        return itens;
    }

    /**
    * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
    */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    /**
    * Operação que inicializa as Interfaces Façades com os respectivos objetos de
    * persistência dos dados no banco de dados. 
    */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public ContratoModalidadeHorarioTurmaVO getContratoModalidadeHorarioTurmaVO() {
        return contratoModalidadeHorarioTurmaVO;
    }
     
    public void setContratoModalidadeHorarioTurmaVO(ContratoModalidadeHorarioTurmaVO contratoModalidadeHorarioTurmaVO) {
        this.contratoModalidadeHorarioTurmaVO = contratoModalidadeHorarioTurmaVO;
    }

    public List getListaSelectItemTurma() {
        return (listaSelectItemTurma);
    }
     
    public void setListaSelectItemTurma( List listaSelectItemTurma ) {
        this.listaSelectItemTurma = listaSelectItemTurma;
    }

    public ContratoModalidadeTurmaVO getContratoModalidadeTurmaVO() {
        return contratoModalidadeTurmaVO;
    }
     
    public void setContratoModalidadeTurmaVO(ContratoModalidadeTurmaVO contratoModalidadeTurmaVO) {
        this.contratoModalidadeTurmaVO = contratoModalidadeTurmaVO;
    }
}