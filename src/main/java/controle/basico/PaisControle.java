package controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import java.util.ArrayList;
import java.util.List;

import javax.faces.model.SelectItem;

import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Pais;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import javax.faces.event.ActionEvent;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * paisForm.jsp paisCons.jsp) com as funcionalidades da classe <code>Pais</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see Pais
 * @see PaisVO
 */
public class PaisControle extends SuperControle {

    private PaisVO paisVO;
    /**
     * Interface <code>PaisInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */
    private EstadoVO estadoVO;
    private String msgAlert;

    public PaisControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>Pais</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        reset();
        return "editar";
    }

    public void reset() {
        setPaisVO(new PaisVO());
        setEstadoVO(new EstadoVO());
        setSucesso(false);
        setErro(false);
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>Pais</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            PaisVO obj = getFacade().getPais().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setNovoObj(new Boolean(false));
            obj.registrarObjetoVOAntesDaAlteracao();
            setPaisVO(new PaisVO());
            setPaisVO(obj);
            setEstadoVO(new EstadoVO());
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>Pais</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar(boolean centralEventos) {
        try {

            if (centralEventos) {
                this.verificarAutorizacao();
                if (paisVO.isNovoObj().booleanValue()) {
                    getFacade().getPais().incluir(paisVO, true);
                    incluirLogInclusao();
                } else {
                    getFacade().getPais().alterar(paisVO, true);
                    incluirLogAlteracao();
                }
            } else {
                if (paisVO.isNovoObj().booleanValue()) {
                    getFacade().getPais().incluir(paisVO);
                    incluirLogInclusao();
                } else {
                    getFacade().getPais().alterar(paisVO);
                    incluirLogAlteracao();
                }
            }

            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /**
     * Inclui o log de inclusão de categoria
     * @throws Exception
     */
    public void incluirLogInclusao() throws Exception {
        try {
            paisVO.setObjetoVOAntesAlteracao(new PaisVO());
            paisVO.setNovoObj(true);
            registrarLogObjetoVO(paisVO, paisVO.getCodigo(), "PAIS", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PAIS", paisVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE PAIS", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        paisVO.setNovoObj(false);
        paisVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            paisVO.setObjetoVOAntesAlteracao(new PaisVO());
            paisVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(paisVO, paisVO.getCodigo(), "PAIS", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PAIS", paisVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE PAIS", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de país
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(paisVO, paisVO.getCodigo(), "PAIS", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PAIS", paisVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE PAIS", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        paisVO.registrarObjetoVOAntesDaAlteracao();
    }

    /**
     * <AUTHOR>
     * 24/03/2011
     * @return
     */
    public String gravar() {
        return this.gravar(false);
    }

    /**
     * <AUTHOR>
     * 24/03/2011
     * @return
     */
    public String gravarCE() {
        return this.gravar(true);
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP PaisCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            
            //limpar espaços em branco antes de realizar a consulta
            getControleConsulta().setValorConsulta(getControleConsulta().getValorConsulta().trim());
            
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                	objs = getFacade().getPais().consultarPorCodigo(new Integer(0), true, Uteis.NIVELMONTARDADOS_TODOS);
                } else {
	                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
	                PaisVO pais = getFacade().getPais().consultarPorCodigoExato(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
	                if(pais != null) {
	                	objs.add(pais);
	                }
                }
            }
            if (getControleConsulta().getCampoConsulta().equals("nome")) {
                objs = getFacade().getPais().consultarPorNome(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>PaisVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir(boolean centralEventos) {
        try {
            if (centralEventos) {
                this.verificarAutorizacao();
                getFacade().getPais().excluir(paisVO);
                //registrar log
                registrarLogExclusaoObjetoVO(paisVO, paisVO.getCodigo().intValue(), "PAIS", 0);
            } else {
                getFacade().getPais().excluir(paisVO);
                incluirLogExclusao();
            }
            setPaisVO(new PaisVO());

            setEstadoVO(new EstadoVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
//            setMensagemDetalhada("msg_erro", e.getMessage());
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"pais\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"pais\" violates foreign key")){
                setMensagemDetalhada("Este país não pode ser excluído, pois está sendo utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /**
     * <AUTHOR>
     * 24/03/2011
     * @return
     */
    public String excluir() {
        return this.excluir(false);
    }

    /**
     * <AUTHOR>
     * 24/03/2011
     * @return
     */
    public String excluirCE() {
        return this.excluir(true);
    }
    /* Método responsável por adicionar um novo objeto da classe <code>Estado</code>
     * para o objeto <code>paisVO</code> da classe <code>Pais</code>
     */

    public void adicionarEstado() throws Exception {
        try {
            if (!getPaisVO().getCodigo().equals(new Integer(0))) {
                estadoVO.setPais(getPaisVO().getCodigo());
            }
            getPaisVO().adicionarObjEstadoVOs(getEstadoVO());
            this.setEstadoVO(new EstadoVO());
            setMensagemID("msg_dados_adicionados");

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());

        }
    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>Estado</code>
     * para edição pelo usuário.
     */
    public String editarEstado() throws Exception {
        EstadoVO obj = (EstadoVO) context().getExternalContext().getRequestMap().get("estado");
        setEstadoVO(obj);
        return "editar";
    }

    /* Método responsável por remover um novo objeto da classe <code>Estado</code>
     * do objeto <code>paisVO</code> da classe <code>Pais</code>
     */
    public String removerEstado() throws Exception {
        EstadoVO obj = (EstadoVO) context().getExternalContext().getRequestMap().get("estado");
        getPaisVO().excluirObjEstadoVOs(obj.getSigla());
        setMensagemID("msg_dados_excluidos");
        return "editar";
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nome", "Nome"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados. 
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public EstadoVO getEstadoVO() {
        return estadoVO;
    }

    public void setEstadoVO(EstadoVO estadoVO) {
        this.estadoVO = estadoVO;
    }

    public PaisVO getPaisVO() {
        return paisVO;
    }

    public void setPaisVO(PaisVO paisVO) {
        this.paisVO = paisVO;
    }

    /**
     * Consulta de logs de país
     */
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("País");
        loginControle.consultarLogObjetoSelecionado("PAIS", paisVO.getCodigo(), null);
    }
    
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoGeral() {
       paisVO = new PaisVO();
       realizarConsultaLogObjetoSelecionado();
    }
     public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getPais().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }

    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de País",
                "Deseja excluir o País?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

}
