/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.basico;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.Modulo;
import br.com.pactosolucoes.enumeradores.TipoInfoMigracaoEnum;
import com.google.common.base.Functions;
import com.google.common.collect.Lists;
import controle.arquitetura.MenuControle;
import controle.arquitetura.ModuloAberto;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import controle.contrato.ContratoControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConsultaClienteTO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.richfaces.event.DropEvent;

/**
 * <AUTHOR>
 */
public class ClientesMarcadosControle extends SuperControle {

    private List<ClienteVO> clientesMarcados = new ArrayList<>();
    private ClienteVO clienteParaObservacao;

    private List<ClienteVO> clientesRecentes;
    private List<ClienteVO> clientesFavoritos;
    private List<String> codigosClientesMarcadosRecentes;
    private List<String> codigosClientesMarcadosFavoritos;
    public void gravarObservacao() {
        try {
            clienteParaObservacao = (ClienteVO) context().getExternalContext().getRequestMap().get("clienteM");
            getFacade().getCliente().adicionarObservacaoMarcacao(clienteParaObservacao.getCodigo(), getUsuarioLogado().getCodigo(), clienteParaObservacao.getObservacao());
            clientesMarcados = getFacade().getCliente().obterClientesMarcados(getUsuarioLogado().getCodigo());
            this.setMensagemID("operacoes.lembrete.sucesso");
            this.setSucesso(true);
            processaClientesMarcados();
        } catch (Exception e) {
            this.setSucesso(false);
            setMensagemDetalhada("Erro ao salvar lembrete\n" + e);
        }
    }

    public void abrirCliente() throws Exception {
        try {
            ClienteVO cliente = (ClienteVO) context().getExternalContext().getRequestMap().get("clienteM");
            LoginControle loginControle = (LoginControle) JSFUtilities.getManagedBeanValue(LoginControle.class);
            loginControle.abrirZillyonWeb();
            redirect("/faces/clienteNav.jsp?page=cliente&matricula=" + cliente.getMatricula());
        } catch (Exception e) {
            redirect("/faces/preCadastro.jsp");
        }
    }

    public ClientesMarcadosControle() {
        try {
            atualizarClientes();
        } catch (Exception e) {
            //nao tratar excecao
        }
    }

    public void atualizarClientes() throws Exception {
        clientesMarcados = getFacade().getCliente().obterClientesMarcados(getUsuarioLogado().getCodigo());
    }

    public void processDrop(DropEvent arg0) {
        if (clientesMarcados.size() >= 5) {
            return;
        }
        ClienteVO clienteDrag = (ClienteVO) arg0.getDragValue();
        for (ClienteVO cli : clientesMarcados) {
            if (cli.getCodigo().equals(clienteDrag.getCodigo())) {
                return;
            }
        }
        ClienteVO clienteMarcado = new ClienteVO();
        clienteMarcado.setPessoa(new PessoaVO());
        clienteMarcado.getPessoa().setCodigo(clienteDrag.getPessoa().getCodigo());
        clienteMarcado.getPessoa().setNome(clienteDrag.getPessoa().getNome());
        clienteMarcado.getPessoa().setFotoKey(clienteDrag.getPessoa().getFotoKey());
        clienteMarcado.setMatricula(clienteDrag.getMatricula());
        clienteMarcado.setCodigo(clienteDrag.getCodigo());
        clientesMarcados.add(clienteMarcado);
        notificarRecursoEmpresa(RecursoSistema.ALUNO_MARCADO_PERFIL_ALUNO);
        try {
            getFacade().getCliente().marcarCliente(clienteDrag.getCodigo(), getUsuarioLogado().getCodigo());
        } catch (Exception e) {
            //nao tratar excecao
        }
    }

    public void processDropLista(DropEvent arg0) {
        if (clientesMarcados.size() >= 6) {
            return;
        }

        ConsultaClienteTO clienteDrag = (ConsultaClienteTO) arg0.getDragValue();
        for (ClienteVO cli : clientesMarcados) {
            if (cli.getCodigo().equals(clienteDrag.getCodCliente())) {
                return;
            }
        }
        ClienteVO clienteMarcado = new ClienteVO();
        clienteMarcado.setPessoa(new PessoaVO());
        clienteMarcado.getPessoa().setNome(clienteDrag.getNome());
        clienteMarcado.getPessoa().setFotoKey(clienteDrag.getFotoKey());
        clienteMarcado.setMatricula(clienteDrag.getMatricula());
        clienteMarcado.setCodigo(clienteDrag.getCodCliente());
        clientesMarcados.add(clienteMarcado);
        notificarRecursoEmpresa(RecursoSistema.ALUNO_MARCADO_TELA_CLIENTES);
        try {
            getFacade().getCliente().marcarCliente(clienteDrag.getCodCliente(), getUsuarioLogado().getCodigo());
        } catch (Exception e) {
            //nao tratar excecao
        }
    }

    public void processDropDelete(DropEvent arg0) {
        processDropDelete((ClienteVO) arg0.getDragValue());
    }

    public void processDropDelete(ClienteVO clienteVO) {
        for (ClienteVO cli : new ArrayList<ClienteVO>(clientesMarcados)) {
            if (cli.getCodigo().equals(clienteVO.getCodigo())) {
                clientesMarcados.remove(cli);
                notificarRecursoEmpresa(RecursoSistema.ALUNO_DESMARCADO);
                try {
                    getFacade().getCliente().desmarcarCliente(clienteVO.getCodigo(), getUsuarioLogado().getCodigo());
                } catch (Exception e) {
                    //nao tratar excecao
                }
            }
        }
    }

    public List<ClienteVO> getClientesMarcados() {
        return clientesMarcados;
    }

    public String getFotoNuvem() {
        ClienteVO cliente = (ClienteVO) context().getExternalContext().getRequestMap().get("clienteM");
        if (cliente == null) {
            return "";
        } else {
            return getPaintFotoDaNuvem(cliente.getPessoa().getFotoKey());
        }
    }

    public void setClientesMarcados(List<ClienteVO> clientesMarcados) {
        this.clientesMarcados = clientesMarcados;
    }


    public ClienteVO getClienteParaObservacao() {
        return clienteParaObservacao;
    }

    public void setClienteParaObservacao(ClienteVO clienteParaObservacao) {
        this.clienteParaObservacao = clienteParaObservacao;
    }

    public List<ClienteVO> getClientesRecentes() throws Exception {
        return setaNomeClientes(clientesRecentes);
    }

    public List<ClienteVO> getClientesFavoritos() throws Exception {
        return setaNomeClientes(clientesFavoritos);
    }

    private List<ClienteVO> setaNomeClientes(List<ClienteVO> clienteVOS) {
        if(clienteVOS == null){
            return null;
        }
        for (ClienteVO clienteVO : clienteVOS) {
            String nomeCliente = clienteVO.getNome_Apresentar();
            nomeCliente = Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(nomeCliente);
            //Primeiro e ultimo precisam ser completos
            if (UteisValidacao.emptyString(nomeCliente)) {
                continue;
            }

            StringBuilder sbNomeApresentar = new StringBuilder();
            String[] parts = nomeCliente.split(" ");
            int qtdPartes = parts.length;
            for (int i = 0; i <= qtdPartes - 1; i++) {
                if (i == 0) {
                    parts[i] = parts[i] + " ";
                } else if (i < (parts.length - 1)) {
                    boolean adicionarPonto = parts[i].length() > 1;
                    parts[i] = parts[i].charAt(0) + (adicionarPonto ? "." : "") + " ";
                }
                sbNomeApresentar.append(parts[i]);
            }
            nomeCliente = sbNomeApresentar.toString();
            if (nomeCliente.length() > 20) {
                nomeCliente = nomeCliente.substring(0, 16) + "...";
            }
            clienteVO.getPessoa().setNome(nomeCliente);
        }
        return clienteVOS;
    }

    public void marcaClienteFavorito() throws Exception {
        try {
            setSucesso(false);
            setMensagemDetalhada("", "");
            limparMsg();
            setMsgAlert("");
            List<ClienteVO> clienteVOS = setaNomeClientes(getFacade().getCliente().consultarClientesFavoritos(getUsuarioLogado().getCodigo()));
            if (clienteVOS.size() < 3) {
                ClienteVO cliente = (ClienteVO) context().getExternalContext().getRequestMap().get("clienteM");
                for (ClienteVO clienteFavorito : clienteVOS) {
                    if (cliente.getCodigo().equals(clienteFavorito.getCodigo())) {
                        throw new Exception("Aluno já marcado como favorito");
                    }
                }
                ClienteVO clienteFavoritoJaDesmarcado = getFacade().getCliente().consultaSeClienteJaFoiFavorito(cliente.getCodigo(), getUsuarioLogado().getCodigo());
                if(clienteFavoritoJaDesmarcado.getCodigo() != null && clienteFavoritoJaDesmarcado.getCodigo() != 0) {
                    getFacade().getCliente().remarcarClienteComoFavorito(clienteFavoritoJaDesmarcado.getCodigoMarcadoFavorito());
                    this.setMensagemID("operacoes.favorito.sucesso");
                    this.setSucesso(true);
                } else {
                    getFacade().getCliente().inserirClienteMarcado(cliente.getCodigo(), getUsuarioLogado().getCodigo(), true);
                    this.setMensagemID("operacoes.favorito.sucesso");
                    this.setSucesso(true);
                }
            } else {
                throw new Exception("Seus favoritos chegaram ao limite. Desmarque um aluno para adicionar outro. ");
            }
        } catch (Exception e) {
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
        processaClientesMarcados();
    }

    public void desmarcaClienteFavorito() throws Exception {
        try {
            setSucesso(false);
            setMensagemDetalhada("", "");
            limparMsg();
            setMsgAlert("");
            List<ClienteVO> clienteVOS = setaNomeClientes(getFacade().getCliente().consultarClientesFavoritos(getUsuarioLogado().getCodigo()));
            ClienteVO cliente = (ClienteVO) context().getExternalContext().getRequestMap().get("clienteM");
            for (ClienteVO clienteFavorito : clienteVOS) {
                if (cliente.getCodigo().equals(clienteFavorito.getCodigo())) {
                    getFacade().getCliente().desmarcarClienteComoFavorito(clienteFavorito.getCodigoMarcadoFavorito());
                    this.setMensagemID("operacoes.exclusao.favorito.sucesso");
                    this.setSucesso(true);
                    processaClientesMarcados();
                    return;
                }
            }
            throw new Exception("O cliente " + cliente.getPessoa().getNome() + " não está marcado como favorito! Atualize a página e tente novamente!");
        } catch (Exception e) {
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    public List<String> getCodigosClientesMarcadosRecentes() throws Exception {
        return codigosClientesMarcadosRecentes;
    }

    public List<String> getCodigosClientesMarcadosFavoritos() throws Exception {
        return codigosClientesMarcadosFavoritos;
    }

    public void codigosFavoritos(List<ClienteVO> clienteFavoritoVOS, List<ClienteVO> clientesRecentesVOS) throws Exception {
        List<String> codigosClientesFavoritos = new ArrayList<>();
        if(clienteFavoritoVOS != null && clienteFavoritoVOS.size() > 0) {
            codigosClientesFavoritos = Lists.transform(clienteFavoritoVOS.stream().map(ClienteVO::getCodigo).collect(Collectors.toList()), Functions.toStringFunction());
        }
        setCodigosClientesMarcadosFavoritos(codigosClientesFavoritos);

        List<String> finalCodigosClientesFavoritos = codigosClientesFavoritos;
        clientesRecentesVOS = clientesRecentesVOS.stream().filter(clienteVO -> !finalCodigosClientesFavoritos.contains(clienteVO.getCodigo().toString()))
                .collect(Collectors.toList());
        List<String> codigosClientesRecentes = new ArrayList<>();
        if(clientesRecentesVOS != null && clientesRecentesVOS.size() > 0) {
            codigosClientesRecentes = Lists.transform(clientesRecentesVOS.stream().map(ClienteVO::getCodigo).collect(Collectors.toList()), Functions.toStringFunction());
        }
        setCodigosClientesMarcadosRecentes(codigosClientesRecentes);
    }


    public void abrirClienteMarcandoComoRecente() throws Exception {
        try {
            setMensagemDetalhada("", "");
            limparMsg();
            setMsgAlert("");
            ConsultaClienteTO cliente = (ConsultaClienteTO) context().getExternalContext().getRequestMap().get("cliente");
            getFacade().getCliente().marcaCliente(cliente.getCodCliente(), getUsuarioLogado().getCodigo());
            LoginControle loginControle = (LoginControle) JSFUtilities.getManagedBeanValue(LoginControle.class);
            loginControle.setIgnorarHistoricoNavegacaoModulo(true);
            loginControle.abrirZillyonWeb();
            loginControle.setIgnorarHistoricoNavegacaoModulo(false);
            if(isApresentarMenuZWUI()){
                loginControle.setModuloAberto(ModuloAberto.PESSOAS);
            }
            String linkNovaTela = getLinkNovaTelaCliente(cliente.getMatricula());
            if (!UteisValidacao.emptyString(linkNovaTela)) {
                redirectUrl(linkNovaTela);
            } else {
                redirect("/faces/clienteNav.jsp?page=cliente&matricula=" + cliente.getMatricula());
            }
        } catch (Exception e) {
            redirect("/faces/preCadastro.jsp");
        }
        processaClientesMarcados();
    }

    public String getLinkNovaTelaCliente(String matricula) {
        try {
            if (!Uteis.isHabilitarRecursoPadraoTelaCliente() ||
                    UteisValidacao.emptyNumber(getUsuarioLogado().getCodigo()) ||
                    getUsuarioLogado() == null) {
                return "";
            }
            boolean novaTelaAlunoPadrao = getFacade().getUsuario().recursoHabilitado(TipoInfoMigracaoEnum.TELA_ALUNO, getUsuarioLogado().getCodigo(), getEmpresaLogado().getCodigo());
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");
            if (!novaTelaAlunoPadrao ||
                    loginControle == null ||
                    !loginControle.isApresentarModuloNovoTreino()) {
                return "";
            }
            MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);
            menuControle.setUrlGoBackRedirect(null);
            String openWindow = (loginControle.getAbrirNovaPlataforma(Modulo.NOVO_TREINO.getSiglaModulo()) + "&redirect=/pessoas/perfil-v2/" + matricula);
            notificarRecursoEmpresa(RecursoSistema.NOVA_TELA_ALUNO_ABRIR);
            return openWindow;
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public String abrirClienteMarcandoComoRecente(String matricula) throws Exception {
        try {
            setMensagemDetalhada("", "");
            limparMsg();
            setMsgAlert("");
            LoginControle loginControle = (LoginControle) JSFUtilities.getManagedBeanValue(LoginControle.class);
            loginControle.abrirZillyonWeb();
            return "/faces/clienteNav.jsp?page=cliente&matricula=" + matricula;
        } catch (Exception e) {
            return"/faces/preCadastro.jsp";
        }
    }

    public void setClientesRecentes(List<ClienteVO> clientesRecentes) {
        this.clientesRecentes = clientesRecentes;
    }

    public void setClientesFavoritos(List<ClienteVO> clientesFavoritos) {
        this.clientesFavoritos = clientesFavoritos;
    }

    public void setCodigosClientesMarcadosRecentes(List<String> codigosClientesMarcadosRecentes) {
        this.codigosClientesMarcadosRecentes = codigosClientesMarcadosRecentes;
    }

    public void setCodigosClientesMarcadosFavoritos(List<String> codigosClientesMarcadosFavoritos) {
        this.codigosClientesMarcadosFavoritos = codigosClientesMarcadosFavoritos;
    }

    public void processaClientesMarcados() {
        try {
            List<ClienteVO> clienteFavoritosVOS = getFacade().getCliente().consultarClientesFavoritos(getUsuarioLogado().getCodigo(), 3);
            setClientesFavoritos(clienteFavoritosVOS);
            List<ClienteVO> clienteRecentesVOS = getFacade().getCliente().consultarClientesRecentes(getUsuarioLogado().getCodigo(), 3);
            setClientesRecentes(clienteRecentesVOS);
            codigosFavoritos(getClientesFavoritos(), getClientesRecentes());
        } catch (Exception e) {
        }
    }
}
