package controle.basico;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.ce.comuns.enumerador.TipoTelefone;
import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.comuns.json.ItemCobrancaPactoJSON;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.*;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.notificacao.ServicoNotificacaoPush;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AlgoritmoCriptoEnum;
import controle.arquitetura.security.LoginControle;
import controle.asaas.AsaasEmpresaService;
import controle.basico.clube.MensagemGenericaControle;
import controle.financeiro.GestaoNFCeControle;
import negocio.comuns.TotemTO;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.ResultadoServicosVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.*;
import negocio.comuns.basico.enumerador.negocio.comuns.basico.enumerador.cadastrodinamico.LocaleEnum;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.AberturaMetaVO;
import negocio.comuns.crm.FecharMetaVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.crm.ModeloMensagemVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.plano.ConvenioCobrancaEmpresaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.CepVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Criptografia;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.ControleAcesso;
import negocio.facade.jdbc.arquitetura.ResultadoServicos;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.PluggyAccountBloqueio;
import negocio.facade.jdbc.notaFiscal.NotaFiscal;
import negocio.facade.jdbc.utilitarias.Cep;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.movidesk.MovideskService;
import negocio.movidesk.dto.EmpresaMovideskDTO;
import negocio.oamd.RedeEmpresaVO;
import org.apache.commons.lang.SerializationUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicHeader;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;
import servicos.AdministrativoRunner;
import servicos.adm.IntegracaoEstacionamento;
import servicos.adm.IntegracaoF360;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.boleto.asaas.DadosComerciaisContaAsaasDTO;
import servicos.impl.boleto.asaas.SituacaoCadastralContaAsaasDTO;
import servicos.impl.boleto.asaas.TaxasContaAsaasDTO;
import servicos.impl.oamd.OAMDService;
import servicos.impl.pagolivre.BankAccountPagoLivreDto;
import servicos.impl.pagolivre.MerchantPagoLivreDto;
import servicos.impl.pagolivre.MerchantPagoLivreRequisicaoDto;
import servicos.impl.pagolivre.MerchantRetornoDto;
import servicos.impl.pagolivre.PagoLivreService;
import servicos.impl.pagolivre.PortalUsersPagoLivreDto;
import servicos.impl.pluggy.PluggyService;
import servicos.integracao.impl.conciliadora.ConciliadoraServiceImpl;
import servicos.integracao.impl.parceirofidelidade.dotz.ParceiroFidelidadeAPIDotzImpl;
import servicos.integracao.pjbank.beanRecebimento.CredencialRecebimento;
import servicos.integracao.pjbank.enums.FormaRecebimento;
import servicos.integracao.pjbank.recebimento.Credenciamento;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static negocio.comuns.utilitarias.UteisEmail.retornarListaEmailStringSeparadoPorPontoVirgulaIgnorandoInvalidos;
import static org.apache.commons.lang.StringUtils.isNotBlank;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas empresaForm.jsp empresaCons.jsp) com as funcionalidades da classe
 * <code>Empresa</code>. Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see Empresa
 * @see EmpresaVO
 */
public class EmpresaControle extends SuperControle {

    private static final String MODAL_EDITAR_FORMAS_PAGAMENTO_NFCE = "'modalEditarFormasPagamentoNFCe'";
    private static final String RICHFACES_SHOW_MODAL_EDITAR_FORMAS_PAGAMENTO_NFCE = "Richfaces.showModalPanel(" + MODAL_EDITAR_FORMAS_PAGAMENTO_NFCE + ");";
    private static final String RICHFACES_HIDE_MODAL_EDITAR_FORMAS_PAGAMENTO_NFCE = "Richfaces.hideModalPanel(" + MODAL_EDITAR_FORMAS_PAGAMENTO_NFCE + ");";
    private static final String MODAL_EDITAR_FORMAS_PAGAMENTO_NFSE = "'modalEditarFormasPagamentoNFSe'";
    private static final String RICHFACES_SHOW_MODAL_EDITAR_FORMAS_PAGAMENTO_NFSE = "Richfaces.showModalPanel(" + MODAL_EDITAR_FORMAS_PAGAMENTO_NFSE + ");";
    private static final String RICHFACES_HIDE_MODAL_EDITAR_FORMAS_PAGAMENTO_NFSE = "Richfaces.hideModalPanel(" + MODAL_EDITAR_FORMAS_PAGAMENTO_NFSE + ");";

    protected List listaSelectItemQuestionarioPrimeiraVisita;
    protected List listaSelectItemQuestionarioRetorno;
    protected List listaSelectItemQuestionarioReMatricula;
    protected List listaSelectItemQuestionarioPrimeiraCompra;
    protected List listaSelectItemQuestionarioRetornoCompra;
    protected List listaSelectItemCidade;
    protected List listaSelectItemEstado;
    protected List listaSelectItemPais;
    protected List listaSelectItemConsultor;
    protected List listaSelectItemContaCorrente;
    protected List listaSelectItemProdutoDevolverCancelamentoEmpresa;
    protected List listaSelectItemLocalAcessoChamada;
    protected List listaSelectItemColetorChamada;
    protected List listaSelectItemConsultorComUsuario;
    private List<SelectItem> listaSelectItemTipoEmpresa;
    private List<SelectItem> listaSelectPlanosEmpresa;
    private List<SelectItem> listaSelectItemTipoGestaoNFSe = new ArrayList<SelectItem>();
    private List<SelectItem> listaSelectItemTipoParcelasACobrar = new ArrayList<SelectItem>();
    private EmpresaVO empresaVO;
    private String descricaoProcessoRealizado;
    private List<SelectItem> listaTipoCancelamento = new ArrayList<SelectItem>();
    private List<SelectItem> listaTipoAnexoCartaoVacina= new ArrayList<SelectItem>();
    private List<SelectItem> listaArredondamento = new ArrayList<SelectItem>();
    private List<SelectItem> listaSelectItemConvenioBoleto;
    private List<ConvenioCobrancaVO> listaConvenioCobrancaVO;
    private List<SelectItem> listaTipoParcelasCancelamento = new ArrayList<SelectItem>();
    private List<ContratoVO> listaContratosRenovacaoAutomaticaSemPlano = new ArrayList<ContratoVO>();
    private String textoLinkRenovacaoAutomaticaSemPlano = "";
    private String urlImagemAssinatura;
    private boolean impressaoCarteirinha = false;
    private List<SelectItem> listaUsuariosMeta = new ArrayList<SelectItem>();
    private List<SelectItem> listaAcaoObjecaoLead = new ArrayList<SelectItem>();
    private ConfigProdutoZillyon produtoZillyon;
    private boolean checkNotaAutoPgRetroativo;
    private int codigoEdicao = 0;
    private String senhaAntesAlterar = "";
    /**
     * Interface
     * <code>EmpresaInterfaceFacade</code> responsável pela interconexão da
     * camada de controle com a camada de negócio. Criando uma independência da
     * camada de controle com relação a tenologia de persistência dos dados
     * (DesignPatter: Facade).
     */
    private ContaCorrenteEmpresaVO contaCorrenteEmpresaVO;
    private ProdutoDevolverCancelamentoEmpresaVO produtoDevolverCancelamentoEmpresaVO;
    private boolean apresentarCampoConsultorVendaAvulsa = false;
    private String emailConfigGestaoPersonal = "";
    private List<SelectItem> listaSelectItemModeloMensagem;
    private TotemTO totem = new TotemTO();
    private List<TotemTO> totens = new ArrayList<TotemTO>();
    private List<SelectItem> formasPgto;
    private List<PinPadVO> listaPinPad;
    private List<SelectItem> listaSelectConvenioCobranca;
    private List<SelectItem> listaSelectConvenioCobrancaDCO;
    private ConfiguracaoReenvioMovParcelaEmpresaVO configuracaoReenvioMovParcelaEmpresaVO;
    private List<SelectItem> listaProdutos;
    private List<SelectItem> listaFormasPagamento;
    private TipoFormaPagto formaPagamentoNFCe;
    private List<SelectItem> listaFormasPagamentoNFCe = new ArrayList<SelectItem>();
    private TipoFormaPagto formaPagamentoNFSe;
    private List<PluggyConnectorDTO> conectoresAtivosPluggy;
    private List<SelectItem> listaFormasPagamentoNFSe = new ArrayList<SelectItem>();
    private List<Integer> empresasReplicar;
    private List<SelectItem> listaEmpresasGeral;
    private List<Integer> empresasReplicarContaCorrente;
    private String emailNotificacaoAutomaticaNotas = "";
    private List<String> emailsNotificacaoAutomaticaNotas = new ArrayList<String>();
    private List<ProdutoParceiroFidelidadeVO> listaProdutosParceiro;
    private boolean marcarTodosProdutosParceiro;
    private List<ItemCobrancaPactoJSON> itensCobrancaPacto;
    private List<SelectItem> listaConfigEmissaoNFSe;
    private List<SelectItem> listaConfigEmissaoNFCe;

    private ConfiguracaoSistemaVO configuracaoSistema;
    private Date dataInicialConciliadora;
    private Date dataFinalConciliadora;
    private boolean reprocessarConciliadora = false;
    private Integer reciboPagamentoConciliadora;

    private EmpresaVO empresaVOClone;
    private String codigoTipoProduto;
    private String codigoTipoProdutoNFCe;
    private List<SelectItem> listaTipoProduto = new ArrayList<>();
    private List<SelectItem> listaTipoProdutoNFCe = new ArrayList<>();
    private List<SelectItem> listaTipoProdutoNFSe = new ArrayList<>();
    private String emailWordPress;
    private String scriptEmail;
    private String scriptFormulario;
    private List<SelectItem> listaSelectItemContaEmpresa = new ArrayList<>();
    private boolean possuiCredenciaisPJBank = false;
    private boolean editandoMerchantPagoLivre = false;
    private boolean editandoContaBancariaPagoLivre = false;
    private ConvenioCobrancaVO convenioCobrancaPagoLivre;

    private BancoVO bancoVO;

    private String[] displayIdentificadorFront;

    private String callBackRDStation = "";

    private Integer codigoFinanceiro = 0;

    private List<String> tiposProdutosSelecionados;
    private String onComplete;
    private boolean alterouRetentativa = false;
    private List<SelectItem> listaTipoVigenciaMyWellnessGymPass = new ArrayList<SelectItem>();
    private List<ConvenioCobrancaVO> listaConvenioCobrancaVerificacaoCartao;

    private List<SelectItem> listaSelectItemModalidadeDayUse;
    private List<SelectItem> listaSelectItemProdutoDayUse;
    private List<SelectItem> listaSelectItemTipoPlano;

    private ProdutoVO produtoEstacionamento;

    /**
     * Utilizada para cadastrar o Merchant na PagoLivre
     * */
    private String cnpjPagoLivre;
    private String emailPagoLivre;
    private String nomeEmpresaPagoLivre;
    private Integer dddTelefonePagoLivre;
    private String telefonePagoLivre;
    private Integer contaCorrentePagoLivre;
    private String merchantIdPagoLivre;
    private MerchantPagoLivreDto merchantPagoLivreDto;
    private String emailUserPagoLivre;
    private String nomeUserPagoLivre;
    private AmbienteEnum ambientePagoLivre;
    private String contaBancariaPagoLivre;
    private String contaBancariaFacilitePay;
    private String textoSenhaPagoLivreOuFacilite;
    private PlanoVO planoEmpresaDelSoft;
    private AsaasEmpresaVO asaasEmpresaVO = new AsaasEmpresaVO();
    private List<SelectItem> listaSelectItemTipoEmpresaAsaas = new ArrayList<>();
    private boolean possuiCadastroAsaas = false;
    private AmbienteEnum ambienteAsaas;
    private SituacaoCadastralContaAsaasDTO situacaoCadastralContaAsaasDTO;
    private DadosComerciaisContaAsaasDTO dadosComerciaisContaAsaasDTO;
    private TaxasContaAsaasDTO taxasContaAsaasDTO;


    private String showCallBackHubsPost;

    private String callBackHubsPost;

    private String urlCallBackHubsPost;
    private boolean possuiModuloFacilitePay;
    private boolean editandoMerchantFacilitePay = false;
    private boolean editandoContaBancariaFacilitePay = false;
    private ConvenioCobrancaVO convenioCobrancaFacilitePay;
    private String cnpjFacilitePay;
    private String emailFacilitePay;
    private String nomeEmpresaFacilitePay;
    private Integer dddTelefoneFacilitePay;
    private String telefoneFacilitePay;
    private Integer contaCorrenteFacilitePay;
    private String merchantIdFacilitePay;
    private String emailUserFacilitePay;
    private String nomeUserFacilitePay;
    private AmbienteEnum ambienteFacilitePay;
    private MerchantPagoLivreDto merchantFacilitePayDto;
    boolean empresaPersistidaMovidesk = true;
    private String abrirFecharModalTokenOperacao;
    private String labelsCamposExibirNoModalTokenOperacao;
    private int qtdCamposSensiveisAlterados = 0;
    private String abrirFecharModalTokenOperacaoExplicacao;
    private boolean podeInformarTokenSMS = false;
    private boolean podeInformarTokenSMSShortCode = false;
    private static Map<String, RedeEmpresaVO> redeEmpresaVosJaConsultados = new HashMap<>();
    private static boolean ativouInativouEmpresa;
    private static boolean novaEmpresa;
    public EmpresaControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
        inicializarConfiguracaoSistema();
        identificacaoPessoalInternacional();
    }

    public void inicializarConfiguracaoSistema() throws Exception {
        setConfiguracaoSistema((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
        setShowCallBackHubsPost(PropsService.getPropertyValue(PropsService.urlZWAPI) + "/prest/lead/oauth-callback");
    }

    public void rodarAtualizacaoPagamentoCartaoDebito() {
        try {
            if (getEmpresaVO().getCodigo() == 0) {
                setDescricaoProcessoRealizado("Salve a empresa antes de rodar o processo.");
            } else {
                LogVO log = getFacade().getMovPagamento().alterarDataPagamentoCartaoDebito(
                        getEmpresaVO().getNrDiasCompensacao(), getUsuarioLogado(), getEmpresaVO());
                getFacade().getLog().incluir(log);
                getFacade().getEmpresa().atualizarNrdiascompensacao(getEmpresaVO().getNrDiasCompensacao(),
                        getEmpresaVO().getCodigo());
                setDescricaoProcessoRealizado(log.getValorCampoAlterado() + "<br/>Número de dias para compensação salvo.");

            }
        } catch (Exception e) {
            setMensagem(e.getMessage());
        }

    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe
     * <code>Empresa</code> para edição pelo usuário da aplicação.
     */
    public String novo() {
        setEmailConfigGestaoPersonal("");
        setEmpresaVO(new EmpresaVO());
        codigoEdicao = 0;
        inicializarListasSelectItemTodosComboBox();
        setContaCorrenteEmpresaVO(new ContaCorrenteEmpresaVO());
        setConfiguracaoReenvioMovParcelaEmpresaVO(new ConfiguracaoReenvioMovParcelaEmpresaVO());
        empresaVO.setNovoObj(true);
        empresaVO.setObjetoVOAntesAlteracao(new EmpresaVO());
        limparMsg();
        empresaVO.setConvenioBoletoPadrao(new ConvenioCobrancaVO());
        empresaVO.setEmpresaInternacional(configuracaoSistema.isUsarSistemaInternacional());
        obterURLAssinatura();
        montarComboConfigEmissao();
        return "editar";
    }

    /**
     * Rotina responsavel por disponibilizar os dados de um objeto da classe
     * <code>Empresa</code> para alteração. O objeto desta classe é
     * disponibilizado na session da página (request) para que o JSP
     * correspondente possa disponibilizá-lo para edição.
     */
    public String editar() throws Exception {
        setAbrirFecharModalTokenOperacao("");
        setAbrirFecharModalTokenOperacaoExplicacao("");
        setLabelsCamposExibirNoModalTokenOperacao("");
        setQtdCamposSensiveisAlterados(0);
        limparMsg();
        setAlterouRetentativa(false);
        setEmailConfigGestaoPersonal("");
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            EmpresaVO obj = getFacade().getEmpresa().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            setCodigoFinanceiro(obj.getCodEmpresaFinanceiro());
            inicializarAtributosRelacionados(obj);
            obj.setNovoObj(false);
            if(obj.isUtilizaSistemaEstacionamento()) {
                senhaAntesAlterar = obj.getConfigEstacionamento().getPass();
            }
            obj.registrarObjetoVOAntesDaAlteracao();
            obj.registrarContaCorrenteEmpresaVOsAntesAlteracao();
            obj.registrarConfiguracaoReenvioMovParcelaEmpresaVOSAntesAlteracao();
            obj.getConfiguracaoRDStation().registrarObjetoVOAntesDaAlteracao();

            setContaCorrenteEmpresaVO(new ContaCorrenteEmpresaVO());
            setConfiguracaoReenvioMovParcelaEmpresaVO(new ConfiguracaoReenvioMovParcelaEmpresaVO());
            setEmpresaVO(new EmpresaVO());
            setEmpresaVO(obj);
            codigoEdicao = getEmpresaVO().getCodigo();
            if (!isFotosNaNuvem()) {
                prepararFotos();
            }
            processarCamposSMS();
            obterURLAssinatura();
            inicializarListasSelectItemTodosComboBox();
            validarPermissaoAlterarSenha();
            totens = getFacade().getConfiguracaoEmpresaTotem().obterConfigs(getEmpresaVO().getCodigo(), getKey());

            //TODO: Por enquanto só existe a integração com o parceiro DOTZ, depois se  tornará uma lista
            List<ParceiroFidelidadeVO> listaParceiros = getFacade().getParceiroFidelidade().consultarPorEmpresa(
                    getEmpresaVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
            if (!UteisValidacao.emptyList(listaParceiros)) {
                getEmpresaVO().setParceiroFidelidade(listaParceiros.get(0));
            }

            try {
                if (getEmpresaVO().getTipoCobrancaPactoPrePagoRede()) {
                    JSONObject jsonObject = getFacade().getEmpresa().obterInfoRedeDCC(Uteis.getUrlOamd(), getKey());
                    getEmpresaVO().setCreditoDCC(jsonObject.getInt("creditos"));
                }
            } catch (Exception ex) {
                Uteis.logar(ex, EmpresaControle.class);
            }
            montarComboConfigEmissao();
            gerarScripEmail();
        } catch (Exception e) {
            setErro(true);
            setSucesso(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }finally {
            empresaVOClone = (EmpresaVO) empresaVO.getClone(true);
        }
        return "editar";
    }

    private void processarCamposSMS() {
        try {
            this.setPodeInformarTokenSMS(getUsuarioLogado().getUsuarioPactoSolucoes() && UteisValidacao.emptyString(this.getEmpresaVO().getTokenSMS()));
            this.setPodeInformarTokenSMSShortCode(getUsuarioLogado().getUsuarioPactoSolucoes() && UteisValidacao.emptyString(this.getEmpresaVO().getTokenSMSShortCode()));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    /**
     * Método responsável inicializar objetos relacionados a classe
     * <code>EmpresaVO</code>. Esta inicialização é necessária por exigência da
     * tecnologia JSF, que não trabalha com valores nulos para estes atributos.
     */
    public void inicializarAtributosRelacionados(EmpresaVO obj) {
        if (obj.getQuestionarioPrimeiraVisita() == null) {
            obj.setQuestionarioPrimeiraVisita(new QuestionarioVO());
        }
        if (obj.getQuestionarioRetorno() == null) {
            obj.setQuestionarioRetorno(new QuestionarioVO());
        }
        if (obj.getQuestionarioReMatricula() == null) {
            obj.setQuestionarioReMatricula(new QuestionarioVO());
        }
        if (obj.getLocalAcessoChamada() == null) {
            obj.setLocalAcessoChamada(new LocalAcessoVO());
        }
        if (obj.getColetorChamada() == null) {
            obj.setColetorChamada(new ColetorVO());
        }
        emailsNotificacaoAutomaticaNotas = retornarListaEmailStringSeparadoPorPontoVirgulaIgnorandoInvalidos(obj.getEmailsNotificacaoAutomaticaNotas());
    }

    public void validarFoneEmpresa() throws ConsistirException {
        TelefoneVO telefoneVO = new TelefoneVO();
        telefoneVO.setTipoTelefone(TipoTelefone.COMERCIAL.getCodigo());

        if (!empresaVO.getTelComercial1().equals(""))
            telefoneVO.validarTelefone(empresaVO.getTelComercial1(), telefoneVO.getTipoTelefone(), configuracaoSistema.isUsarSistemaInternacional());

        if (!empresaVO.getTelComercial2().equals(""))
            telefoneVO.validarTelefone(empresaVO.getTelComercial2(), telefoneVO.getTipoTelefone(), configuracaoSistema.isUsarSistemaInternacional());

        if (!empresaVO.getTelComercial3().equals(""))
            telefoneVO.validarTelefone(empresaVO.getTelComercial3(), telefoneVO.getTipoTelefone(), configuracaoSistema.isUsarSistemaInternacional());

        if (!empresaVO.getFax().equals("")) {
            telefoneVO.setTipoTelefone(TipoTelefone.FAX.getCodigo());
            telefoneVO.validarTelefone(empresaVO.getFax(), telefoneVO.getTipoTelefone(), configuracaoSistema.isUsarSistemaInternacional());
        }
    }

    /**
     * author: alcides 24/03/2011
     */
    public void gravarCE() {
        this.gravar(true, false);
    }

    /**
     * author: alcides 24/03/2011
     */
    public void gravar() {
        try {
            limparMsg();
            setMsgAlert("");
            setAbrirFecharModalTokenOperacao("");
//            validarGeolocalizacao();
            this.gravar(false, false);
        } catch (Exception ex) {
            MensagemGenericaControle control = getControlador(MensagemGenericaControle.class);
            control.setMensagemDetalhada("", "");
            setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
            control.init("Dados de Geolocalização",
                    "A latitude e longitude não aparentam ser no Brasil, deseja continuar?",
                    this, "gravarEmpresa", "", "", "", "form");
        }
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto
     * da classe
     * <code>Empresa</code>. Caso o objeto seja novo (ainda não gravado no BD) é
     * acionado a operação
     * <code>incluir()</code>. Caso contrário é acionado o
     * <code>alterar()</code>. Se houver alguma inconsistÂncia o objeto não é
     * gravado, sendo re-apresentado para o usuário juntamente com uma mensagem
     * de erro.
     */
    public void gravarEmpresa() {
        gravar(false, false);
    }
    public void gravar(boolean centralEventos, boolean origemModalTokenOperacao) {
        try{
            empresaVO.setEmpresaInternacional(((ConfiguracaoSistemaVO)getObjectInSession("configuracaoSistema")).isUsarSistemaInternacional());
        }catch (Exception e){
            e.printStackTrace();
        }

        if (empresaVO.isNaoGerarResiduoCancelamentoAutomatico()) {
            empresaVO.setGerarQuitacaoCancelamentoAuto(false);
        }

        try {
            if(empresaVO.isUsarConciliadora()){
                try{
                    Integer.parseInt(empresaVO.getEmpresaConciliadora());
                }catch (Exception e){
                    throw new Exception("Erro conciliadora: Id Empresa não é um número inteiro válido.");
                }
            }
            if (getUsuarioLogado().getUsuarioPactoSolucoes()
                    && UteisValidacao.emptyNumber(empresaVO.getCodEmpresaFinanceiro())
                    && empresaVO.isAtiva()
                    && !TipoEmpresaFinanceiro.TESTE.equals(empresaVO.getTipoEmpresa())) {
                throw new ConsistirException("O campo 'Código Empresa Financeiro' na aba 'Dados Empresa' deve ser informado, somente ADMINSTRADOR PACTO pode alterar, favor entrar em contato com a Pacto!");
            }
            this.empresaPersistidaMovidesk = true;

            validarFoneEmpresa();
            validarLatitudeLongitude();

            if (empresaVO.getDiasParaRetirarRelFechamentoDeCaixa() == null){
                throw new ConsistirException("O campo quantidade de dias limite para pesquisa no Fechamento de Caixa é obrigatório.");
            }

            limparMsg();
            if (empresaVO.getTipoGestaoNFSe() != TipoRelatorioDF.RECEITA.getCodigo()) {
                empresaVO.setEmiteNFSEPorDataCompensacao(false);
            }
            if (centralEventos) {
                this.verificarAutorizacao();
                if (empresaVO.isNovoObj()) {
                    getFacade().getEmpresa().incluir(empresaVO, true);

                    Map<String, String> dadosEvento = new HashMap<String, String>();

                    dadosEvento.put("NomeEmpresa", String.valueOf(empresaVO.getNome()));
                    dadosEvento.put("CNPJEmpresa", empresaVO.getCNPJ());

                    //LOG - INICIO
                    try {
                        empresaVO.setObjetoVOAntesAlteracao(new EmpresaVO());
                        empresaVO.setNovoObj(true);
                        registrarLogObjetoVO(empresaVO, empresaVO.getCodigo(), "EMPRESA", 0);
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("EMPRESA", empresaVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE EMPRESA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                    //LOG - FIM
                } else {
                    alterarPontosItemCampanha();
                    getFacade().getEmpresa().alterar(empresaVO, true);

                    Map<String, String> dadosEvento = new HashMap<String, String>();

                    dadosEvento.put("NomeEmpresa", String.valueOf(empresaVO.getNome()));
                    dadosEvento.put("CNPJEmpresa", empresaVO.getCNPJ());

                    //LOG - INICIO
                    try {
                        registrarLogObjetoVO(empresaVO, empresaVO.getCodigo(), "EMPRESA", 0);
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("EMPRESA", empresaVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE EMPRESA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                    //LOG - FIM
                }
            } else {
                limparMsg();

                empresaVO.validarConfiguracaoMultiplosConvenios();
                empresaVO.validarConfiguracaoRetentativa();

                if (empresaVO.isUsarGestaoCreditosPersonal() && empresaVO.getConfigsPersonal().getTempoCheckOutAutomatico() > 0
                        && empresaVO.getConfigsPersonal().getDuracaoCredito() > empresaVO.getConfigsPersonal().getTempoCheckOutAutomatico()) {
                    throw new Exception(getMensagemInternalizacao("tempoCheckOutMaiorDuracaoCredito"));
                }

                tratarGravarAbaModuloNotas();
                tratarGravarAbaLinkPagamento();
                tratarGravarAbaLinkPagamentoRegua();
                tratarGravarEmails();

                if (empresaVO.isNovoObj()) {
                    getFacade().getEmpresa().incluir(empresaVO);

                    Map<String, String> dadosEvento = new HashMap<String, String>();

                    dadosEvento.put("NomeEmpresa", String.valueOf(empresaVO.getNome()));
                    dadosEvento.put("CNPJEmpresa", empresaVO.getCNPJ());

                    gravarLogConfiguracaoRetentativa();
                    incluirLogInclusao();

                    this.empresaPersistidaMovidesk = StringUtils.isNotBlank(tentarPersistirMovidesk());

                } else {
                    if(empresaVO.isUtilizaSistemaEstacionamento()){
                        if(UteisValidacao.emptyString(empresaVO.getConfigEstacionamento().getPass())){
                            empresaVO.getConfigEstacionamento().setPass(senhaAntesAlterar);
                        }
                    }

                    alterarPontosItemCampanha();

                    boolean solicitarToken = !isAmbienteDesenvolvimento() && !origemModalTokenOperacao && alterouCampoSensivel() && !ignorarModalTokenOperacao() && !isRedePratique(getKey());

                    //###Validar se é para solicitar Token para prosseguir o gravar###
                    if (solicitarToken) {
                        //Validar dados antes de pedir o token (código de autenticação)
                        EmpresaVO.validarDados(empresaVO);
                        TokenOperacaoControle control = getControlador(TokenOperacaoControle.class);
                        setAbrirFecharModalTokenOperacao("Richfaces.showModalPanel('modalTokenOperacao');document.getElementById('formModalTokenOperacao:inputToken1').focus();");
                        control.init("",
                                this,"validarTokenEGravarEmpresa", "", "",
                                "", getLabelsCamposExibirNoModalTokenOperacao(), getQtdCamposSensiveisAlterados(), "",
                                RecursoSistema.CODIGO_AUTENTICACAO_EMPRESA_ABA_COBRANCA_GEROU);
                        //enviar notificação push depois que abrir o modal:
                        enviaNotificacaoPushTokenPactoApp();
                        return;
                    } else {
                        //Não precisa de token então é só ir pelo fluxo normal.
                        getFacade().getEmpresa().alterar(empresaVO);

                        Map<String, String> dadosEvento = new HashMap<String, String>();

                        dadosEvento.put("NomeEmpresa", String.valueOf(empresaVO.getNome()));
                        dadosEvento.put("CNPJEmpresa", empresaVO.getCNPJ());

                        gravarLogConfiguracaoRetentativa();
                        incluirLogAlteracao();
                    }
                }
            }

            processarCamposSMS();
            getProdutoZillyon().alterarConfigProduto();

            if (empresaVO.getUtilizarPactoPrint() && UteisValidacao.emptyNumber(empresaVO.getValidadeMesesCarteirinhaSocio())) {
                throw new ConsistirException("O campo \"Validade carteirinha\" deve ser informado.");
            }

            getFacade().getConfiguracaoEmpresaTotem().gravar(totens, empresaVO.getCodigo());

            try {
                if (getEmpresaVO().utilizaAlgumaIntegracaoLeadsCrm()) {
                    LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession(LoginControle.class.getSimpleName());
                    List list = getFacade().getAberturaMeta().consultarPorDia(Calendario.hoje(), Calendario.hoje(), true, false, Uteis.NIVELMONTARDADOS_TODOS, getEmpresaVO().getCodigo(), new ConfPaginacao());
                    for (Object obj : list) {
                        AberturaMetaVO metaHoje = (AberturaMetaVO) obj;
                        if (metaHoje.getFecharMetaVosLead() != null && metaHoje.getFecharMetaVosLead().size() > 0) {
                            break;
                        }
                        getFacade().getAberturaMeta().executarCalculoMetaLeads(metaHoje, loginControle.getPermissaoAcessoMenuVO());
                        for (FecharMetaVO fecharMetaLead : metaHoje.getFecharMetaVosLead()) {
                            getFacade().getFecharMeta().incluir(fecharMetaLead);
                        }
                    }
                }
            }catch (Exception ignore){
                Uteis.logar(null,"Não foi possivel realizar abertura meta leads hoje ao gravar empresa: "+getEmpresaVO().getNome());
            }
            try{
                notificarRecursoEmpresaContratosConcomitantes();
            }catch (Exception ignore){
            }
            getEmpresaVO().setFeitoUploadAlgumaFoto(false);
            setSucesso(true);
            setErro(false);
            enviarGymId();
            enviarParaTreinoConfiguracaoPersonalEmpresa();
            setMensagemID(this.empresaPersistidaMovidesk ? "msg_dados_gravados" : "msg_empresa_persistida_sem_movidesk");
            Uteis.reloadOamdImagem(getKey());
            Uteis.reloadOamdImagem(getKey(), getEmpresaVO().getCodigo());
            Uteis.reloadTreinoCacheUsuario(getKey(), null);
            notificarRecursoEmpresaEmpresaControle();
            empresaVOClone = (EmpresaVO) empresaVO.getClone(true);

        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            //           return "editar";
        }
    }

    private boolean isRedePratique(String key) {
        //Rede pratique não é pra pedir token para nenhuma operação
        OAMDService oamdService;
        RedeEmpresaVO rede;
        try {
            oamdService = new OAMDService();

            //Ver se já existe no mapa de já consultados anteriormente
            rede = redeEmpresaVosJaConsultados.getOrDefault(key, null);

            if (rede == null) {
                rede = oamdService.consultarRedeEmpresa(key);
            }

            if (rede != null) {
                redeEmpresaVosJaConsultados.put(key, rede);
                //CHAVEREDE DA PRATIQUE
                if (rede.getChaverede().equals("341b908afd7637c1d5b09f248d3498f1")) {
                    return true;
                }
            }

        } catch (Exception ex) {
            return false;
        } finally {
            oamdService = null;
        }
        return false;
    }

    private boolean ignorarModalTokenOperacao() throws Exception {
        return getUsuarioLogado().isIgnorarTokenOperacao();
    }

    public void enviaNotificacaoPushTokenPactoApp() {
        try {
            ServicoNotificacaoPush.enviaNotificacaoPushTokenPactoApp(getKey(),getEmpresaVO().getCodigo(), getUsuarioLogado().getCodigo());
        } catch (Exception ex) {
            Uteis.logarDebug("Não foi possível enviar notificação push do token para Pacto App: " + ex.getMessage());
        }
    }

    public void validarTokenEGravarEmpresa() {
        try {
            limparMsg();

            TokenOperacaoControle controlToken = (TokenOperacaoControle) JSFUtilities.getFromSession(TokenOperacaoControle.class.getSimpleName());

            try {
                controlToken.validarToken(RecursoSistema.CODIGO_AUTENTICACAO_EMPRESA_ABA_COBRANCA_SUCESSO, RecursoSistema.CODIGO_AUTENTICACAO_EMPRESA_ABA_COBRANCA_ERRO);
            } catch (Exception ex) {
                controlToken.montarAviso("Ops!", ex.getMessage());
                return;
            }

            //chegou até aqui então o token é valido, já pode continuar com a alteração da Empresa
            gravar(false, true);

            //colocar Token como "Utilizado" para não permitir utilizar o mesmo token mais;
            controlToken.inutilizarToken();
            controlToken.fecharModal();
            controlToken.montarSucessoGrowl("Dados gravados com sucesso!");

        } catch (Exception ex) {
            montarErro(ex.getMessage());
        }
    }

    public boolean alterouCampoSensivel() {
        //Varíaveis controlador
        setLabelsCamposExibirNoModalTokenOperacao("");
        setQtdCamposSensiveisAlterados(0);

       //Varíaveis método
        StringBuilder textoCamposExibirNoModal = new StringBuilder();
        boolean alterouCampoSensivel = false;
        int qtdCampoSensiveisAlterados = 0;
        EmpresaVO empresaVOAntesAlteracao = (EmpresaVO) empresaVO.getObjetoVOAntesAlteracao();

        if (!empresaVOAntesAlteracao.getConvenioCobrancaCartao().getCodigo().equals(empresaVO.getConvenioCobrancaCartao().getCodigo())) {
            alterouCampoSensivel = true;
            qtdCampoSensiveisAlterados++;
            textoCamposExibirNoModal.append("\"Convênio de cobrança Cartão de Crédito\"").append(", ");
        }

        if (!empresaVOAntesAlteracao.getConvenioCobrancaPix().getCodigo().equals(empresaVO.getConvenioCobrancaPix().getCodigo())) {
            qtdCampoSensiveisAlterados++;
            alterouCampoSensivel = true;
            textoCamposExibirNoModal.append("\"Convênio de cobrança Pix\"").append(", ");
        }

        if (!empresaVOAntesAlteracao.getConvenioCobrancaBoleto().getCodigo().equals(empresaVO.getConvenioCobrancaBoleto().getCodigo())) {
            qtdCampoSensiveisAlterados++;
            alterouCampoSensivel = true;
            textoCamposExibirNoModal.append("\"Convênio de cobrança Boleto\"").append(", ");
        }

        if (!empresaVOAntesAlteracao.getConvenioCobrancaCartaoRegua().getCodigo().equals(empresaVO.getConvenioCobrancaCartaoRegua().getCodigo())) {
            alterouCampoSensivel = true;
            qtdCampoSensiveisAlterados++;
            textoCamposExibirNoModal.append("\"Convênio de cobrança Cartão de Crédito (Régua)\"").append(", ");
        }

        if (!empresaVOAntesAlteracao.getConvenioCobrancaPixRegua().getCodigo().equals(empresaVO.getConvenioCobrancaPixRegua().getCodigo())) {
            qtdCampoSensiveisAlterados++;
            alterouCampoSensivel = true;
            textoCamposExibirNoModal.append("\"Convênio de cobrança Pix (Régua)\"").append(", ");
        }

        if (!empresaVOAntesAlteracao.getConvenioCobrancaBoletoRegua().getCodigo().equals(empresaVO.getConvenioCobrancaBoletoRegua().getCodigo())) {
            qtdCampoSensiveisAlterados++;
            alterouCampoSensivel = true;
            textoCamposExibirNoModal.append("\"Convênio de cobrança Boleto (Régua)\"").append(", ");
        }

        if (!(empresaVOAntesAlteracao.getTipoParcelasCobrarVendaSite().getId() == empresaVO.getTipoParcelasCobrarVendaSiteRegua().getId())) {
            qtdCampoSensiveisAlterados++;
            alterouCampoSensivel = true;
            textoCamposExibirNoModal.append("\"Tipo de parcelas a cobrar padrão\"").append(", ");
        }

        if (!empresaVOAntesAlteracao.getQtdDiasLimiteCobrancaParcelasRecorrencia().equals(empresaVO.getQtdDiasLimiteCobrancaParcelasRecorrencia())) {
            qtdCampoSensiveisAlterados++;
            alterouCampoSensivel = true;
            textoCamposExibirNoModal.append("\"Limite de dias após o vencimento para parcela entrar na Retentativa de Cobrança\"").append(", ");
        }

        if (!empresaVOAntesAlteracao.getQtdDiasRepetirCobrancaParcelasRecorrencia().equals(empresaVO.getQtdDiasRepetirCobrancaParcelasRecorrencia())) {
            qtdCampoSensiveisAlterados++;
            alterouCampoSensivel = true;
            textoCamposExibirNoModal.append("\"Intervalo de dias para Retentativa de Cobrança da parcela\"").append(", ");
        }

        if (alterouCampoSensivel) {
            setQtdCamposSensiveisAlterados(qtdCampoSensiveisAlterados);
            //remover a última vírgula
            textoCamposExibirNoModal = new StringBuilder(textoCamposExibirNoModal.toString().substring(0, textoCamposExibirNoModal.toString().lastIndexOf(", ")));
            setLabelsCamposExibirNoModalTokenOperacao(textoCamposExibirNoModal.toString());
        }
        return alterouCampoSensivel;
    }

    public boolean isAmbienteDesenvolvimento() {
        if (!isHttps()) {
            //ambiente de desenvolvimento (localhost ou swarm)
            return true;
        }
        return false;
    }

    private void validarGeolocalizacao() throws ConsistirException, ValidacaoException {
        BigDecimal lat = null;
        try {
            if (!UteisValidacao.emptyString(empresaVO.getLatitude())) {
                lat = new BigDecimal(empresaVO.getLatitude());
            }
        } catch (Exception ex) {
            throw new ConsistirException("Formato da latitude inválido");
        }

        BigDecimal lon = null;
        try {
            if (!UteisValidacao.emptyString(empresaVO.getLongitude())) {
                lon = new BigDecimal(empresaVO.getLongitude());
            }
        } catch (Exception ex) {
            throw new ConsistirException("Formato da longitude inválido");
        }

        if (lat != null && lon != null) {
            boolean foraBrasil = lat.compareTo(new BigDecimal("0")) > 0;
            if (lat.compareTo(new BigDecimal("0")) > 0) {
                foraBrasil = true;
            }
            if (foraBrasil) {
                throw new ValidacaoException("ForaBrasil");
            }
        }
    }

    private void enviarGymId() {
        try {
            if(UteisValidacao.emptyNumber(empresaVO.getCodigo()) || UteisValidacao.emptyString(empresaVO.getCodigoGymPass())){
                return;
            }
            JSONObject body = new JSONObject();
            body.put("chave", getKey());
            body.put("empresa", empresaVO.getCodigo());
            body.put("gymid", empresaVO.getCodigoGymPass());
            body.put("nome", empresaVO.getNome());
            body.put("cnpj", empresaVO.getCNPJ());
            final String urlMs = String.format("%s/gymid",
                    PropsService.getPropertyValue(PropsService.urlGymPassMs));
            ExecuteRequestHttpService.post(urlMs, body.toString(), new HashMap<>());
        }catch (Exception e){
            Uteis.logar(e, EmpresaControle.class);
        }
    }

    private void enviarParaTreinoConfiguracaoPersonalEmpresa() {
        try {
            String urlTreino = PropsService.getPropertyValue(getKey(), PropsService.urlTreino);

            JSONObject body = new JSONObject();
            body.put("codigoEmpresaZW", empresaVO.getCodigo());
            body.put("isConsumirCreditoPorAlunoVinculado", empresaVO.getConfigsPersonal().isConsumirCreditoPorAlunoVinculado());
            body.put("getDuracaoCredito", empresaVO.getConfigsPersonal().getDuracaoCredito());
            body.put("isUsarFotoPersonal", empresaVO.getConfigsPersonal().isUsarFotoPersonal());
            body.put("isMostrarFotosAlunosMonitor", empresaVO.getConfigsPersonal().isMostrarFotosAlunosMonitor());
            body.put("isObrigatorioAssociarAlunoAoCheckIn", empresaVO.getConfigsPersonal().isObrigatorioAssociarAlunoAoCheckIn());
            body.put("tempoCheckOutAutomatico", empresaVO.getConfigsPersonal().getTempoCheckOutAutomatico());

            final String fullUrl = urlTreino + "/prest/config/" + getKey() + "/atualizar-configuracao-personal-empresa";
            ExecuteRequestHttpService.post(fullUrl, body.toString(), new HashMap<>());
        } catch (Exception e) {
            Uteis.logar(e, EmpresaControle.class);
        }
    }

    private String tentarPersistirMovidesk() {
        final EmpresaMovideskDTO empresaMovideskDTO = new EmpresaMovideskDTO()
                .id(getKey() + "-" + empresaVO.getCodigo())
                .nomeFantasia(empresaVO.getNome())
                .razaoSocial(empresaVO.getRazaoSocial())
                .chave(getKey())
                .cpfCnpj(empresaVO.getCNPJ())
                .email(empresaVO.getEmail())
                .endereco(empresaVO.getEstadoSigla(), empresaVO.getCidadeNome())
                .empresaFinanceiro(empresaVO.getCodEmpresaFinanceiro());

        final String telefone = empresaVO.getPrimeiroTelefoneNaoNulo();
        if (StringUtils.isNotBlank(telefone) && !telefone.equalsIgnoreCase(EmpresaVO.SEM_TELEFONE)) {
            empresaMovideskDTO.telefone(telefone);
        }

        return MovideskService.getInstance().persistirEmpresa(empresaMovideskDTO);
    }

    private void alterarPontosItemCampanha() throws Exception{

        //validacao para verificar se existe item de campanha para os pontos de acesso e alterar tambem lá. PONTOS ACESSO, ACESSO CHUVA, FRIO e CALOR.
        if(getEmpresaLogado().isTrabalharComPontuacao() && getFacade().getItemCampanha().existeItemCampanha(getEmpresaLogado().getCodigo(), 3)) {
            getFacade().getItemCampanha().alterarPontos(getEmpresaLogado().getCodigo(), getEmpresaVO().getPontosAlunoAcesso(), 3);
        }

        if (getEmpresaLogado().isTrabalharComPontuacao() && getFacade().getItemCampanha().existeItemCampanha(getEmpresaLogado().getCodigo(), 6)){
            getFacade().getItemCampanha().alterarPontos(getEmpresaLogado().getCodigo(), getEmpresaVO().getPontosAlunoAcessoChuva(), 6);
        }

        if(getEmpresaLogado().isTrabalharComPontuacao() && getFacade().getItemCampanha().existeItemCampanha(getEmpresaLogado().getCodigo(), 7)){
            getFacade().getItemCampanha().alterarPontos(getEmpresaLogado().getCodigo(), getEmpresaVO().getPontosAlunoAcessoFrio(), 7);
        }

        if(getEmpresaLogado().isTrabalharComPontuacao() && getFacade().getItemCampanha().existeItemCampanha(getEmpresaLogado().getCodigo(), 8)){
            getFacade().getItemCampanha().alterarPontos(getEmpresaLogado().getCodigo(), getEmpresaVO().getPontosAlunoAcessoCalor(), 8);
        }

    }

    private void tratarGravarAbaModuloNotas() throws Exception {
        if ((empresaVO.isEnvioNotificacaoNotasNFSe() || empresaVO.isEnvioNotificacaoNotasNFCe()) && emailsNotificacaoAutomaticaNotas.isEmpty()) {
            throw new Exception("Preencha pelo menos um e-mail para ser enviado a notificação.");
        }

        if (!emailsNotificacaoAutomaticaNotas.isEmpty()) {
            empresaVO.setEmailsNotificacaoAutomaticaNotas(UteisEmail.retornarEmailsSeparadosPorPontoVirgula(emailsNotificacaoAutomaticaNotas));
        }
    }

    private void tratarGravarAbaLinkPagamento() throws Exception {
        if (!UteisValidacao.emptyNumber(empresaVO.getConvenioCobrancaCartao().getCodigo())) {
            ConvenioCobrancaEmpresaVO convenioCobrancaEmpresaVO = getFacade().getConvenioCobrancaEmpresa().consultarPorConvenioCobrancaEmpresa(empresaVO.getConvenioCobrancaCartao().getCodigo(), empresaVO.getCodigo());
            if (convenioCobrancaEmpresaVO == null) {
                throw new Exception("O convênio de cobrança selecionado no campo 'Convênio de cobrança Cartão de Crédito' (Aba Link de Pagamento e de cadastro de cartão - Tela do Aluno) não existe na empresa que você está editando.");
            }
        }
        if (!UteisValidacao.emptyNumber(empresaVO.getConvenioCobrancaBoleto().getCodigo())) {
            ConvenioCobrancaEmpresaVO convenioCobrancaEmpresaVO = getFacade().getConvenioCobrancaEmpresa().consultarPorConvenioCobrancaEmpresa(empresaVO.getConvenioCobrancaBoleto().getCodigo(), empresaVO.getCodigo());
            if (convenioCobrancaEmpresaVO == null) {
                throw new Exception("O convênio de cobrança selecionado no campo 'Convênio de cobrança Boleto' (Aba Link de Pagamento e de cadastro de cartão - Tela do Aluno) não existe na empresa que você está editando.");
            }
        }
        if (!UteisValidacao.emptyNumber(empresaVO.getConvenioCobrancaPix().getCodigo())) {
            ConvenioCobrancaEmpresaVO convenioCobrancaEmpresaVO = getFacade().getConvenioCobrancaEmpresa().consultarPorConvenioCobrancaEmpresa(empresaVO.getConvenioCobrancaPix().getCodigo(), empresaVO.getCodigo());
            if (convenioCobrancaEmpresaVO == null) {
                throw new Exception("O convênio de cobrança selecionado no campo 'Convênio de cobrança Pix' (Aba Link de Pagamento e de cadastro de cartão - Tela do Aluno) não existe na empresa que você está editando.");
            }
        }
    }

    private void tratarGravarAbaLinkPagamentoRegua() throws Exception {
        if (!UteisValidacao.emptyNumber(empresaVO.getConvenioCobrancaCartaoRegua().getCodigo())) {
            ConvenioCobrancaEmpresaVO convenioCobrancaEmpresaVO = getFacade().getConvenioCobrancaEmpresa().consultarPorConvenioCobrancaEmpresa(empresaVO.getConvenioCobrancaCartaoRegua().getCodigo(), empresaVO.getCodigo());
            if (convenioCobrancaEmpresaVO == null) {
                throw new Exception("O convênio de cobrança selecionado no campo 'Convênio de cobrança Cartão de Crédito' (Aba Link de Pagamento e de cadastro de cartão - Régua de Cobrança) não existe na empresa que você está editando.");
            }
        }
        if (!UteisValidacao.emptyNumber(empresaVO.getConvenioCobrancaBoletoRegua().getCodigo())) {
            ConvenioCobrancaEmpresaVO convenioCobrancaEmpresaVO = getFacade().getConvenioCobrancaEmpresa().consultarPorConvenioCobrancaEmpresa(empresaVO.getConvenioCobrancaBoletoRegua().getCodigo(), empresaVO.getCodigo());
            if (convenioCobrancaEmpresaVO == null) {
                throw new Exception("O convênio de cobrança selecionado no campo 'Convênio de cobrança Boleto' (Aba Link de Pagamento e de cadastro de cartão - Régua de Cobrança) não existe na empresa que você está editando.");
            }
        }
        if (!UteisValidacao.emptyNumber(empresaVO.getConvenioCobrancaPixRegua().getCodigo())) {
            ConvenioCobrancaEmpresaVO convenioCobrancaEmpresaVO = getFacade().getConvenioCobrancaEmpresa().consultarPorConvenioCobrancaEmpresa(empresaVO.getConvenioCobrancaPixRegua().getCodigo(), empresaVO.getCodigo());
            if (convenioCobrancaEmpresaVO == null) {
                throw new Exception("O convênio de cobrança selecionado no campo 'Convênio de cobrança Pix' (Aba Link de Pagamento e de cadastro de cartão - Régua de Cobrança) não existe na empresa que você está editando.");
            }
        }
    }

    private void tratarGravarEmails() throws Exception {
        if (getUsuarioLogado().getAdministrador()) {
            if (isNotBlank(empresaVO.getEmail())) {
                if (!UteisEmail.getValidEmail(empresaVO.getEmail())) {
                    throw new Exception(getMensagemInternalizacao("msg_erro_emailInvalido"));
                }
            }

            if (isNotBlank(empresaVO.getEmailNotificacaoVendasOnline())) {
                if (!UteisEmail.getValidEmail(empresaVO.getEmailNotificacaoVendasOnline())) {
                    throw new Exception(getMensagemInternalizacao("msg_erro_emailNotificacaoVendasOnlineInvalido"));
                } else {
                    empresaVO.setEmailNotificacaoVendasOnline(empresaVO.getEmailNotificacaoVendasOnline().toUpperCase());
                }
            }
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP
     * EmpresaCons.jsp. Define o tipo de consulta a ser executada, por meio de
     * ComboBox denominado campoConsulta, disponivel neste mesmo JSP. Como
     * resultado, disponibiliza um List com os objetos selecionados na sessao da
     * pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getEmpresa().consultarPorCodigo(valorInt, true, true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nome")) {
                objs = getFacade().getEmpresa().consultarPorNome(getControleConsulta().getValorConsulta(),true, true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("razaoSocial")) {
                objs = getFacade().getEmpresa().consultarPorRazaoSocial(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("cidade")) {
                objs = getFacade().getEmpresa().consultarPorCidade(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("inscEstadual")) {
                objs = getFacade().getEmpresa().consultarPorInscEstadual(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("questionarioPrimeira")) {
                objs = getFacade().getEmpresa().consultarPorQuestionarioPrimeiraVisita(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("questionarioRetorno")) {
                objs = getFacade().getEmpresa().consultarPorQuestionarioRetorno(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("questionarioRematricula")) {
                objs = getFacade().getEmpresa().consultarPorQuestionarioRematricula(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public String excluir() {
        return excluir(false);

    }

    public String excluirCE() {
        return excluir(true);

    }

    public void confirmarExcluir(){
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String obj = request.getParameter("metodochamar");

        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        if (obj.equals("excluir")){
            control.init("Exclusão de Empresa",
                    "Deseja excluir a empresa?",
                    this, obj, "", "", "", "form");
        }else {
            control.init("Atualização de Data de Compensação",
                    "Deseja Realizar a Atualização de Data de Compensação?",
                    this, obj, "", "", "", "form");
        }

    }


    /**
     * Operação responsível por processar a exclusão um objeto da classe
     * <code>EmpresaVO</code> Após a exclusão ela automaticamente aciona a
     * rotina para uma nova inclusão.
     */
    public String excluir(boolean centralEventos) {
        try {
            if (centralEventos) {
                getFacade().getEmpresa().excluir(empresaVO, true);
                //registrar log
                registrarLogExclusaoObjetoVO(empresaVO, empresaVO.getCodigo(), "EMPRESA", 0);
            } else {
                getFacade().getEmpresa().excluir(empresaVO);
                incluirLogExclusao();
            }

            setEmpresaVO(new EmpresaVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", "Não é permitido excluir uma empresa.");
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void upload(UploadEvent upload) throws Exception {
        // getEmpresaVO().upload(upload, obterCaminhoWebAplicacaoFoto(), true);
        boolean erroAoLerArquivo = true;
        UploadItem item = upload.getUploadItem();
        File item1 = item.getFile();
        try {
            BufferedImage outImage = ImageIO.read(item1);
            erroAoLerArquivo = false;
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[4096];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            getEmpresaVO().setAlturaFotoEmpresa(String.valueOf(outImage.getHeight()));
            getEmpresaVO().setLarguraFotoEmpresa(String.valueOf(outImage.getWidth()));
            getEmpresaVO().setFoto(arrayOutputStream.toByteArray());
            getEmpresaVO().getFotos().put(MidiaEntidadeEnum.FOTO_EMPRESA,getEmpresaVO().getFoto());
            getEmpresaVO().setFeitoUploadAlgumaFoto(true);
            arrayOutputStream.close();
            fi.close();

            setMensagemID("msg_upload_arquivo");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            tratarErroAoCarregarImagem(e, erroAoLerArquivo, item1, "upload");
        }
    }

    private void tratarErroAoCarregarImagem(Exception e, boolean erroAoLerArquivo, File file, String nomeMetodo){
        if ((erroAoLerArquivo) || (!imageEstaNoFormatoCMYK(file))){
            montarErro("Existe um erro não identificado no arquivo de imagem. Edite a imagem, crie outro arquivo e repita a operação.");
        }
        Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO CARREGAR IMAGEM EMPRESA. método:" + nomeMetodo + ". Erro: " + e.getMessage());

    }


    public void uploadRelatorio(UploadEvent upload) throws Exception {
        //  getEmpresaVO().upload(upload, obterCaminhoWebAplicacaoFoto(), false);
        UploadItem item = upload.getUploadItem();
        File item1 = item.getFile();
        boolean erroAoLerArquivo = true;
        try {
            BufferedImage outImage = ImageIO.read(item1);
            erroAoLerArquivo = false;
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[4096];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            getEmpresaVO().setAlturaFotoRelatorio(String.valueOf(outImage.getHeight()));
            getEmpresaVO().setLarguraFotoRelatorio(String.valueOf(outImage.getWidth()));
            getEmpresaVO().setFotoRelatorio(arrayOutputStream.toByteArray());
            arrayOutputStream.close();
            fi.close();
            getEmpresaVO().setFeitoUploadAlgumaFoto(true);
            setMensagemID("msg_upload_arquivo");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            tratarErroAoCarregarImagem(e, erroAoLerArquivo, item1, "uploadRelatorio");
        }
    }

    /**
     * Mostra a foto da empresa pesquisando no banco de dados
     *
     * @throws java.io.IOException
     * @throws java.lang.Exception trata o erro se a matricula não for gerada
     * para que a foto tenha o mesmo nome da matricula
     */
    public void paintFoto(OutputStream out, Object data) throws IOException, Exception {
        getEmpresaVO().setFoto(getFacade().getEmpresa().obterFoto(getKey(),
                getEmpresaVO().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA));
        SuperControle.paintFotoEmpresa(out, getEmpresaVO().getFoto());
    }

    /**
     * Mostra a foto do relatório da empresa pesquisando no banco de dados
     *
     * @throws java.io.IOException
     * @throws java.lang.Exception trata o erro se a matricula não for gerada
     * para que a foto tenha o mesmo nome da matricula
     */
    public void paintFotoRelatorio(OutputStream out, Object data) throws IOException, Exception {
        getEmpresaVO().setFotoRelatorio(getFacade().getEmpresa().obterFoto(getKey(),
                getEmpresaVO().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
        SuperControle.paintFotoRelatorio(out, getEmpresaVO().getFotoRelatorio());
    }

    /**
     * Mostra a foto do email pesquisando no banco de dados
     *
     * @throws java.io.IOException
     * @throws java.lang.Exception trata o erro se a matricula não for gerada
     * para que a foto tenha o mesmo nome da matricula
     */
    public void paintImgEmail(OutputStream out, Object data) throws IOException, Exception {
        getEmpresaVO().setFotoEmail(getFacade().getEmpresa().obterFoto(getKey(),
                getEmpresaVO().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_EMAIL));
        SuperControle.paintFotoEmpresa(out, getEmpresaVO().getFotoEmail());
    }

    /**
     * Mostra a foto da empresa sem pesquisar no banco de dados Obs.:Deve ser
     * usado somente quando precisa mostrar a foto antiga e fazer upload da nova
     * foto.
     *
     * @param out
     * @param data
     * @throws IOException
     * @throws Exception
     */
    public void paintFotoSemPesquisarNoBanco(OutputStream out, Object data) throws IOException, Exception {
        if (!getEmpresaVO().isFeitoUploadAlgumaFoto()) {
            try {
                getEmpresaVO().setFoto(getFacade().getEmpresa().obterFoto(getKey(), getEmpresaVO().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA));
            } catch (Exception e) {
                Uteis.logar(e, EmpresaControle.class);
            }
        }
        SuperControle.paintFotoEmpresa(out, getEmpresaVO().getFoto());
    }

    /**
     * Mostra a foto do relatório da empresa sem pesquisar no banco de dados
     * Obs.:Deve ser usado somente quando precisa mostrar a foto antiga e fazer
     * upload da nova foto.
     *
     * @throws java.io.IOException
     * @throws java.lang.Exception trata o erro se a matricula não for gerada
     * para que a foto tenha o mesmo nome da matricula
     */
    public void paintFotoRelatorioSemPesquisarNoBanco(OutputStream out, Object data) throws IOException, Exception {
        if (getEmpresaVO().getFotoRelatorio() == null && isFotosNaNuvem()) {
            try {
                getEmpresaVO().setFotoRelatorio(getFacade().getEmpresa().obterFoto(getKey(), getEmpresaVO().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
            } catch (Exception e) {
                Uteis.logar(e, EmpresaControle.class);
            }
        }
        SuperControle.paintFotoRelatorio(out, getEmpresaVO().getFotoRelatorio());
    }

    /**
     * Mostra a foto do email da empresa sem pesquisar no banco de dados
     * Obs.:Deve ser usado somente quando precisa mostrar a foto antiga e fazer
     * upload da nova foto.
     *
     * @throws java.io.IOException
     * @throws java.lang.Exception trata o erro se a matricula não for gerada
     * para que a foto tenha o mesmo nome da matricula
     */
    public void paintFotoEmailSemPesquisarNoBanco(OutputStream out, Object data) throws IOException, Exception {
        if (getEmpresaVO().getFotoEmail() == null && isFotosNaNuvem()) {
            try {
                getEmpresaVO().setFotoEmail(getFacade().getEmpresa().obterFoto(getKey(), getEmpresaVO().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_EMAIL));
            } catch (Exception e) {
                Uteis.logar(e, EmpresaControle.class);
            }
        }
        SuperControle.paintFotoEmpresa(out, getEmpresaVO().getFotoEmail());
    }

    public void paintFotoRedeSocialSemPesquisarNoBanco(OutputStream out, Object data) throws IOException, Exception {
        if (getEmpresaVO().getFotoRedeSocial()== null && isFotosNaNuvem()) {
            try {
                getEmpresaVO().setFotoRedeSocial(getFacade().getEmpresa().obterFoto(getKey(), getEmpresaVO().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_REDESOCIAL));
            } catch (Exception e) {
                Uteis.logar(e, EmpresaControle.class);
            }
        }
        SuperControle.paintFotoEmpresa(out, getEmpresaVO().getFotoRedeSocial());
    }

    public void paintFotoAppPequenaSemPesquisarNoBanco(OutputStream out, Object data) throws IOException, Exception {
        if (getEmpresaVO().getHomeBackground320x276() == null && isFotosNaNuvem()) {
            try {
                getEmpresaVO().setHomeBackground320x276(getFacade().getEmpresa().obterFoto(getKey(), getEmpresaVO().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_HomeBackground320x276));
            } catch (Exception e) {
                Uteis.logar(e, EmpresaControle.class);
            }
        }
        SuperControle.paintFotoEmpresa(out, getEmpresaVO().getHomeBackground320x276());
    }

    public void paintFotoAppGrandeSemPesquisarNoBanco(OutputStream out, Object data) throws IOException, Exception {
        if (getEmpresaVO().getHomeBackground640x551() == null && isFotosNaNuvem()) {
            try {
                getEmpresaVO().setHomeBackground640x551(getFacade().getEmpresa().obterFoto(getKey(), getEmpresaVO().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_HomeBackground640x551));
            } catch (Exception e) {
                Uteis.logar(e, EmpresaControle.class);
            }
        }
        SuperControle.paintFotoEmpresa(out, getEmpresaVO().getHomeBackground640x551());
    }

    public void paintFotoPropagandaBoletoSemPesquisarNoBanco(OutputStream out, Object data) throws Exception {
        if (getEmpresaVO().getPropagandaBoleto() == null && isFotosNaNuvem()) {
            try {
                getEmpresaVO().setPropagandaBoleto(getFacade().getEmpresa().obterFoto(getKey(), getEmpresaVO().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_PROPAGANDA_BOLETO));
            } catch (Exception e) {
                Uteis.logar(e, EmpresaControle.class);
            }
        }
        SuperControle.paintFotoEmpresa(out, getEmpresaVO().getPropagandaBoleto());
    }

    /**
     * Metodo responsavel por fazer o upload do arquivo de imagem para o
     * servidor
     * <p/>
     * Autor: Pedro Y. Saito Criado em 03/01/2011
     */
    public void uploadImgEmail(UploadEvent upload) throws Exception {
        UploadItem item = upload.getUploadItem();
        File item1 = item.getFile();
        boolean erroAoLerArquivo = true;
        try {
            BufferedImage outImage = ImageIO.read(item1);
            erroAoLerArquivo = false;
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[4096];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            getEmpresaVO().setAlturaFotoEmail(String.valueOf(outImage.getHeight()));
            getEmpresaVO().setLarguraFotoEmail(String.valueOf(outImage.getWidth()));
            getEmpresaVO().setFotoEmail(arrayOutputStream.toByteArray());

            arrayOutputStream.close();
            fi.close();
            getEmpresaVO().setFeitoUploadAlgumaFoto(true);
            setMensagemID("msg_upload_arquivo");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            tratarErroAoCarregarImagem(e, erroAoLerArquivo, item1, "uploadImgEmail");
        }
    }

    public void uploadImgRedeSocial(UploadEvent upload) throws Exception {
        UploadItem item = upload.getUploadItem();
        File item1 = item.getFile();
        boolean erroAoLerArquivo = true;
        try {
            BufferedImage outImage = ImageIO.read(item1);
            erroAoLerArquivo = false;
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[4096];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            getEmpresaVO().setAlturaFotoRedeSocial(String.valueOf(outImage.getHeight()));
            getEmpresaVO().setLarguraFotoRedeSocial(String.valueOf(outImage.getWidth()));
            getEmpresaVO().setFotoRedeSocial(arrayOutputStream.toByteArray());

            arrayOutputStream.close();
            fi.close();
            getEmpresaVO().setFeitoUploadAlgumaFoto(true);
            setMensagemID("msg_upload_arquivo");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            tratarErroAoCarregarImagem(e, erroAoLerArquivo, item1, "uploadImgRedeSocial");
        }
    }


    public void uploadImgPropagandaBoleto(UploadEvent upload) throws Exception {
        UploadItem item = upload.getUploadItem();
        File item1 = item.getFile();
        boolean erroAoLerArquivo = true;
        try {
            BufferedImage outImage = ImageIO.read(item1);
            erroAoLerArquivo = false;
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[4096];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            getEmpresaVO().setAlturaPropagandaBoleto(String.valueOf(outImage.getHeight()));
            getEmpresaVO().setLarguraPropagandaBoleto(String.valueOf(outImage.getWidth()));
            getEmpresaVO().setPropagandaBoleto(arrayOutputStream.toByteArray());

            arrayOutputStream.close();
            fi.close();
            getEmpresaVO().setFeitoUploadAlgumaFoto(true);
            setMensagemID("msg_upload_arquivo");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            tratarErroAoCarregarImagem(e, erroAoLerArquivo, item1, "uploadImgPropagandaBoleto");
        }
    }

    public String getUrlImagemAssinatura() {
        return urlImagemAssinatura;
    }

    public void setUrlImagemAssinatura(String urlImagemAssinatura) {
        this.urlImagemAssinatura = urlImagemAssinatura;
    }

    public void obterURLAssinatura() {
        try {
            String urlObject = MidiaService.getInstance().downloadObject(getKey(),
                    MidiaEntidadeEnum.ASSINATURA_EMPRESA,
                    getEmpresaVO().getCodigo().toString());
            urlImagemAssinatura = UteisValidacao.emptyString(urlObject)
                    ? "" : (Uteis.getPaintFotoDaNuvem(urlObject + "?time=" + getTimeStamp()));
        } catch (Exception e) {
            Uteis.logar(e, EmpresaControle.class);
        }
    }

    public void uploadImgAssinatura(UploadEvent upload) throws Exception {

        if(UteisValidacao.emptyNumber(getEmpresaVO().getCodigo())){
            throw new Exception("Salve a empresa antes de carregar a imagem da assinatura.");
        }

        UploadItem item = upload.getUploadItem();
        File item1 = item.getFile();
        boolean erroAoLerArquivo = true;
        try {
            BufferedImage outImage = ImageIO.read(item1);
            erroAoLerArquivo = false;
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[4096];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            MidiaService.getInstance().uploadObjectFromByteArray(getKey(),
                    MidiaEntidadeEnum.ASSINATURA_EMPRESA,
                    getEmpresaVO().getCodigo().toString(), arrayOutputStream.toByteArray());
            obterURLAssinatura();
            arrayOutputStream.close();
            fi.close();
            setMensagemID("msg_upload_arquivo");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            tratarErroAoCarregarImagem(e, erroAoLerArquivo, item1, "uploadImgAssinatura");
        }
    }

    private boolean imageEstaNoFormatoCMYK(File item1) {
        if (isCMYK(item1)) {
            montarErro("Essa imagem está no formato inadequado. Por favor converta para RGB.");
            return true;
        }
        return false;
    }

    /* Método responsável por adicionar um novo objeto da classe <code>ContaCorrenteEmpresa</code>
     * para o objeto <code>empresaVO</code> da classe <code>Empresa</code>
     */
    public void adicionarContaCorrenteEmpresa() throws Exception {
        try {
            if (!getEmpresaVO().getCodigo().equals(0)) {
                contaCorrenteEmpresaVO.setEmpresa(getEmpresaVO().getCodigo());
            }
            if (getContaCorrenteEmpresaVO().getContaCorrente().getCodigo() != 0) {
                Integer campoConsulta = getContaCorrenteEmpresaVO().getContaCorrente().getCodigo();
                ContaCorrenteVO contaCorrente = getFacade().getContaCorrente().consultarPorChavePrimaria(campoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
                getContaCorrenteEmpresaVO().setContaCorrente(contaCorrente);
            }
            getEmpresaVO().adicionarObjContaCorrenteEmpresaVOs(getContaCorrenteEmpresaVO());
            this.setContaCorrenteEmpresaVO(new ContaCorrenteEmpresaVO());
            setMensagemID("msg_dados_adicionados");
//            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            //          return "editar";
        }
    }

    public void adicionarProdutoDevolverCancelamentoeEmpresa() throws Exception {
        try {
            if (!getEmpresaVO().getCodigo().equals(0)) {
                getProdutoDevolverCancelamentoEmpresaVO().setEmpresa(getEmpresaVO().getCodigo());
            }
            if (getProdutoDevolverCancelamentoEmpresaVO().getProduto() != 0) {
                Integer campoConsulta = getProdutoDevolverCancelamentoEmpresaVO().getProduto();
                ProdutoVO produto = getFacade().getProduto().consultarPorChavePrimaria(
                        campoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                getProdutoDevolverCancelamentoEmpresaVO().setProdutoVO(produto);
            }
            getEmpresaVO().adicionarObjProdDevolvCancelEmpresaVOs(getProdutoDevolverCancelamentoEmpresaVO());
            this.setProdutoDevolverCancelamentoEmpresaVO(new ProdutoDevolverCancelamentoEmpresaVO());
            setMensagemID("msg_dados_adicionados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /* Método  responsável por disponibilizar dados de um objeto da classe <code>ContaCorrenteEmpresa</code>
     * para edição pelo usuário.
     */
    public void editarContaCorrenteEmpresa() throws Exception {
        ContaCorrenteEmpresaVO obj = (ContaCorrenteEmpresaVO) context().getExternalContext().getRequestMap().get("contaCorrenteEmpresa");
        setContaCorrenteEmpresaVO(obj);
//        return "editar";
    }



    /* Método responsável por remover um novo objeto da classe <code>ContaCorrenteEmpresa</code>
     * do objeto <code>empresaVO</code> da classe <code>Empresa</code>
     */
    public void removerContaCorrenteEmpresa() throws Exception {
        ContaCorrenteEmpresaVO obj = (ContaCorrenteEmpresaVO) context().getExternalContext().getRequestMap().get("contaCorrenteEmpresa");
        getEmpresaVO().excluirObjContaCorrenteEmpresaVOs(obj.getContaCorrente().getCodigo());
        setMensagemID("msg_dados_excluidos");
        //       return "editar";
    }

    public void removerProdDevolvCancelEmpresa() throws Exception {
        ProdutoDevolverCancelamentoEmpresaVO obj = (ProdutoDevolverCancelamentoEmpresaVO) context().getExternalContext().getRequestMap().get("produtoDevolvCancelamentoEmpresa");
        getEmpresaVO().excluirObjProdDevolvCancelEmpresaVOs(obj.getProduto());
        setMensagemID("msg_dados_excluidos");
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public void montarListaSelectItemModeloMensagem() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        try {
            List resultadoConsulta = getFacade().getModeloMensagem().consultarPorCodigo(0, MeioEnvio.EMAIL, false, Uteis.NIVELMONTARDADOS_MINIMOS);
            Iterator i = resultadoConsulta.iterator();
            objs.add(new SelectItem(0, ""));
            while (i.hasNext()) {
                ModeloMensagemVO obj = (ModeloMensagemVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getTitulo()));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        setListaSelectItemModeloMensagem(objs);
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>ContaCorrente</code>.
     */
    public void montarListaSelectItemContaCorrente(String prm) throws Exception {
        List resultadoConsulta = consultarContaCorrentePorContaCorrente(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            ContaCorrenteVO obj = (ContaCorrenteVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), "AG:" + obj.getAgencia() + "-" + obj.getAgenciaDV() + "/ CC:" + obj.getContaCorrente() + "-" + obj.getContaCorrenteDV() + " | " + obj.getBanco_Apresentar()));
        }
        setListaSelectItemContaCorrente(objs);
    }

    public void montarListaSelectItemProdutoDevolverCancelamentoEmpresa() {
        try {
            if (getProdutoDevolverCancelamentoEmpresaVO() == null) {
                setProdutoDevolverCancelamentoEmpresaVO(new ProdutoDevolverCancelamentoEmpresaVO());
            }
            List resultadoConsulta = getFacade().getProduto().consultarPorDescricaoTipoProdutoAtivo("", "SE", false, Uteis.NIVELMONTARDADOS_MINIMOS);
            Iterator i = resultadoConsulta.iterator();
            List objs = new ArrayList();
            objs.add(new SelectItem(new Integer(0), ""));
            while (i.hasNext()) {
                ProdutoVO obj = (ProdutoVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao() + " - R$: " + Uteis.arrendondarForcando2CadasDecimaisComVirgula(obj.getValorFinal())));
            }
            setListaSelectItemProdutoDevolverCancelamentoEmpresa(objs);
        } catch (Exception e) {
            setListaSelectItemProdutoDevolverCancelamentoEmpresa((List) new SelectItem(new Integer(0), ""));
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemTipoGestaoNFSe() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(TipoRelatorioDF.RECEITA.getCodigo(), "Por Receita"));
        objs.add(new SelectItem(TipoRelatorioDF.COMPETENCIA.getCodigo(), "Por Competência Quitada"));
        objs.add(new SelectItem(TipoRelatorioDF.FATURAMENTO_DE_CAIXA.getCodigo(), "Por Faturamento Recebido"));
        // PARA FATURAMENTO A DESCRIÇÃO É DIFERENTE --- POIS O FATURAMENTO DO NFSE É DIFERENTE DO ZILLYONWEB -- TICKET #3493
        objs.add(new SelectItem(TipoRelatorioDF.FATURAMENTO.getCodigo(), "Por Faturamento Recebido Por Parcela"));
        objs.add(new SelectItem(TipoRelatorioDF.COMPETENCIA_INDEPENDENTE_QUITACAO.getCodigo(), "Por Competência Independente da Quitação"));
        setListaSelectItemTipoGestaoNFSe(objs);
    }

    public void montarListaSelectItemTipoParcelasCobrar() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        for (TipoObjetosCobrarEnum tipoObjetosCobrarEnum : TipoObjetosCobrarEnum.values()) {
            if (!tipoObjetosCobrarEnum.equals(TipoObjetosCobrarEnum.TIPOS_PRODUTOS)) {
                objs.add(new SelectItem(tipoObjetosCobrarEnum.getId(), tipoObjetosCobrarEnum.getDescricao()));
            }
        }
        setListaSelectItemTipoParcelasACobrar(objs);
    }

    public void montarListaSelectItemQuestionarioPrimeiraCompra(String prm) throws Exception {
        List resultadoConsulta = consultarQuestionarioPorDescricao(prm, TipoServicoEnum.SERVICO.getTipo());
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            QuestionarioVO obj = (QuestionarioVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNomeInterno()));
        }

        setListaSelectItemQuestionarioPrimeiraCompra(objs);
    }

    public void montarListaSelectItemQuestionarioRetornoCompra(String prm) throws Exception {
        List resultadoConsulta = consultarQuestionarioPorDescricao(prm, TipoServicoEnum.SERVICO.getTipo());
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            QuestionarioVO obj = (QuestionarioVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNomeInterno()));
        }

        setListaSelectItemQuestionarioRetornoCompra(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>ContaCorrente</code>. Buscando todos os objetos correspondentes a
     * entidade
     * <code>ContaCorrente</code>. Esta rotina não recebe parâmetros para
     * filtragem de dados, isto é importante para a inicialização dos dados da
     * tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemContaCorrente() {
        try {
            montarListaSelectItemContaCorrente("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade
     * <code><code> e montar o atributo
     * <code>contaCorrente</code> Este atributo é uma lista (
     * <code>List</code>) utilizada para definir os valores a serem apresentados
     * no ComboBox correspondente
     */
    public List consultarContaCorrentePorContaCorrente(String contaCorrentePrm) throws Exception {
        return getFacade().getContaCorrente().consultarPorContaCorrente(contaCorrentePrm, false, Uteis.NIVELMONTARDADOS_TODOS);
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>QuestionarioReMatricula</code>.
     */
    public void montarListaSelectItemQuestionarioReMatricula(String prm) throws Exception {
        List resultadoConsulta = consultarQuestionarioPorDescricao(prm, TipoServicoEnum.PLANO.getTipo());
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            QuestionarioVO obj = (QuestionarioVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNomeInterno()));
        }
        setListaSelectItemQuestionarioReMatricula(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>QuestionarioReMatricula</code>. Buscando todos os objetos
     * correspondentes a entidade
     * <code>Questionario</code>. Esta rotina não recebe parâmetros para
     * filtragem de dados, isto é importante para a inicialização dos dados da
     * tela para o acionamento por meio requisiçães Ajax.
     */
    public void montarListaSelectItemQuestionarioReMatricula() {
        try {
            montarListaSelectItemQuestionarioReMatricula("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemQuestionarioPrimeiraCompra() {
        try {
            montarListaSelectItemQuestionarioPrimeiraCompra("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemQuestionarioRetornoCompra() {
        try {
            montarListaSelectItemQuestionarioRetornoCompra("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>QuestionarioRetorno</code>.
     */
    public void montarListaSelectItemQuestionarioRetorno(String prm) throws Exception {
        List resultadoConsulta = consultarQuestionarioPorDescricao(prm, TipoServicoEnum.PLANO.getTipo());
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            QuestionarioVO obj = (QuestionarioVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNomeInterno()));
        }
        setListaSelectItemQuestionarioRetorno(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>QuestionarioRetorno</code>. Buscando todos os objetos
     * correspondentes a entidade
     * <code>Questionario</code>. Esta rotina não recebe parâmetros para
     * filtragem de dados, isto é importante para a inicialização dos dados da
     * tela para o acionamento por meio requisiçães Ajax.
     */
    public void montarListaSelectItemQuestionarioRetorno() {
        try {
            montarListaSelectItemQuestionarioRetorno("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>QuestionarioPrimeiraVisita</code>.
     */
    public void montarListaSelectItemQuestionarioPrimeiraVisita(String prm) throws Exception {
        List resultadoConsulta = consultarQuestionarioPorDescricao(prm, TipoServicoEnum.PLANO.getTipo());
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            QuestionarioVO obj = (QuestionarioVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNomeInterno()));
        }
        setListaSelectItemQuestionarioPrimeiraVisita(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>QuestionarioPrimeiraVisita</code>. Buscando todos os objetos
     * correspondentes a entidade
     * <code>Questionario</code>. Esta rotina não recebe parâmetros para
     * filtragem de dados, isto é importante para a inicialização dos dados da
     * tela para o acionamento por meio requisiçães Ajax.
     */
    public void montarListaSelectItemQuestionarioPrimeiraVisita() {
        try {
            montarListaSelectItemQuestionarioPrimeiraVisita("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade
     * <code><code> e montar o atributo
     * <code>descricao</code> Este atributo é uma lista (
     * <code>List</code>) utilizada para definir os valores a serem apresentados
     * no ComboBox correspondente
     */
    public List consultarQuestionarioPorDescricao(String descricaoPrm, String tipoServico) throws Exception {
        return getFacade().getQuestionario().consultarPorDescricao(descricaoPrm, tipoServico, false, Uteis.NIVELMONTARDADOS_TODOS);
    }

    public void montarListaSelectItemConsultor() {
        try {
            montarListaSelectItemConsultor(null, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemConsultorComUsuario() {
        try {
            montarListaSelectItemConsultor(TipoColaboradorEnum.CONSULTOR, true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemConsultor(TipoColaboradorEnum tipoColaborador, boolean comUsuario) throws Exception {
        List resultadoConsulta = consultarConsultores(tipoColaborador);
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            ColaboradorVO obj = (ColaboradorVO) i.next();
            if (comUsuario) {
                boolean temUsuario = getFacade().getColaborador().verificarColaboradorTemUsuario(obj.getCodigo());
                if (temUsuario) {
                    objs.add(new SelectItem(obj.getCodigo(), obj.getPessoa().getNome()));
                }
            } else {
                objs.add(new SelectItem(obj.getCodigo(), obj.getPessoa().getNome()));
            }
        }
        if (comUsuario) {
            setListaSelectItemConsultorComUsuario(objs);
        } else {
            setListaSelectItemConsultor(objs);
        }
    }


    public List consultarConsultores(TipoColaboradorEnum tipoColaborador) throws Exception {
        return getFacade().getColaborador().consultarPorTipoColaboradorAtivo(tipoColaborador, "AT", empresaVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    public void montarListaSelectItemPais(String prm) throws Exception {
        List resultadoConsulta = consultarPaisPorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            PaisVO obj = (PaisVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }
        setListaSelectItemPais(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>Pais</code>. Buscando todos os objetos correspondentes a entidade
     * <code>Pais</code>. Esta rotina não recebe parâmetros para filtragem de
     * dados, isto é importante para a inicialização dos dados da tela para o
     * acionamento por meio requisiçães Ajax.
     */
    public void montarListaSelectItemPais() {
        try {
            montarListaSelectItemPais("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade
     * <code><code> e montar o atributo
     * <code>nome</code> Este atributo é uma lista (
     * <code>List</code>) utilizada para definir os valores a serem apresentados
     * no ComboBox correspondente
     */
    public List consultarPaisPorNome(String nomePrm) throws Exception {
        return getFacade().getPais().consultarPorNome(nomePrm, false, Uteis.NIVELMONTARDADOS_TODOS);
    }

    public List consultarEstadoPorCodigoPais(Integer nomePrm) throws Exception {
        return getFacade().getPais().consultarEstadoPorPais(nomePrm, Uteis.NIVELMONTARDADOS_TODOS);
    }

    public void montarListaSelectItemEstado(String prm) throws Exception {
        List resultadoConsulta = consultarEstadoPorCodigoPais(this.getEmpresaVO().getPais().getCodigo());
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            EstadoVO obj = (EstadoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
        }

        setListaSelectItemEstado(objs);
        if (this.getEmpresaVO().getPais().getCodigo().intValue() == 0) {
            this.getEmpresaVO().getEstado().setCodigo(new Integer(0));
            montarListaSelectItemCidade(prm);
        }
    }

    public void montarListaSelectItemEstado() {
        try {
            montarListaSelectItemEstado("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>Cidade</code>.
     */
    public void montarListaSelectItemCidade(String prm) throws Exception {
        List resultadoConsulta = consultarCidadePorCodigoEstado(this.getEmpresaVO().getEstado().getCodigo());
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            CidadeVO obj = (CidadeVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
        }
        setListaSelectItemCidade(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>Cidade</code>. Buscando todos os objetos correspondentes a entidade
     * <code>Cidade</code>. Esta rotina não recebe parâmetros para filtragem de
     * dados, isto é importante para a inicialização dos dados da tela para o
     * acionamento por meio requisiçães Ajax.
     */
    public void montarListaSelectItemCidade() {
        try {
            montarListaSelectItemCidade("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemConvenioBoleto() {
        try {
            this.listaSelectItemConvenioBoleto = new ArrayList<>();
            List<ConvenioCobrancaVO> listaConvenio = getFacade().getConvenioCobranca().consultarPorTipoCobranca(new TipoCobrancaEnum[]{TipoCobrancaEnum.BOLETO, TipoCobrancaEnum.BOLETO_ONLINE},
                    this.empresaVO.getCodigo(), null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (ConvenioCobrancaVO obj : listaConvenio) {
                this.listaSelectItemConvenioBoleto.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
            Ordenacao.ordenarLista(this.listaSelectItemConvenioBoleto, "label");
            this.listaSelectItemConvenioBoleto.add(0, new SelectItem(0, ""));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * Método responsável por consultar dados da entidade
     * <code><code> e montar o atributo
     * <code>nome</code> Este atributo é uma lista (
     * <code>List</code>) utilizada para definir os valores a serem apresentados
     * no ComboBox correspondente
     */
    public List consultarCidadePorCodigoEstado(Integer nomePrm) throws Exception {
        return getFacade().getCidade().consultarPorCodigoEstado(nomePrm, Uteis.NIVELMONTARDADOS_TODOS);
    }

    /**
     * Método responsável por inicializar a lista de valores (
     * <code>SelectItem</code>) para todos os ComboBox's.
     */
    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemQuestionarioPrimeiraVisita();
        montarListaSelectItemQuestionarioRetorno();
        montarListaSelectItemQuestionarioReMatricula();
        montarListaSelectItemQuestionarioPrimeiraCompra();
        montarListaSelectItemQuestionarioRetornoCompra();
        montarListaSelectItemPais();
        montarListaSelectItemEstado();
        montarListaSelectItemCidade();
        montarListaSelectItemContaCorrente();
        montarListaSelectItemProdutoDevolverCancelamentoEmpresa();
        montarListaSelectItemConsultor();
        montarListaSelectItemTipoGestaoNFSe();
        montarListaSelectItemLocalAcessoChamada();
        montarListaSelectItemColetorChamada();
        montarListaSelectItemConsultorComUsuario();
        montarListaSelectItemModeloMensagem();
        montarListaSelectItemTipoParcelasCobrar();
        montarListaSelectItemConvenioBoleto();
        montarListaSelectItemTipoParcelaCancelar();
        montarListaSelectItemFormasPagamento();
        montarListaSelectItemConvenio();
        montarListaSelectItemConvenioDCO();
        montarListaSelectItemFormaPagamento();
        montarListaTipoProduto();
        montarListaUsuariosMeta();
        montarAcaoObjecaoLead();
        montarListaSelectItemProdutos();
        montarListaConvenioCobrancaVerificacaoCartao();
        montarListaSelectItemTipoEmpresa();
        montarListaPlanosDelSoft();
        atualizarListaConvenioCobranca();
        montarListaSelectItemProdutoDayUse();
        montarListaSelectItemModalidadeDayUse();
        montarListaSelectItemTipoPlano();
        montarListaPinPad();
    }

    private void montarListaPinPad() {
        try {
            this.setListaPinPad(getFacade().getPinPad().consultarParaTotem());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void montarListaSelectItemFormasPagamento() {
        this.listaFormasPagamento = new ArrayList<SelectItem>();
    }

    private List<SelectItem> montarListaSelectItemFormasPagamentoDFe(final String formasPagamentoEmissaoDFe) {
        final List<SelectItem> listaFormasPagamentoDFe = new ArrayList<SelectItem>();
        if (StringUtils.isBlank(formasPagamentoEmissaoDFe)) {
            return listaFormasPagamentoDFe;
        }

        final String[] formas = formasPagamentoEmissaoDFe.replaceAll("\\\\", "").split("\\|");
        for (String forma : formas) {
            final TipoFormaPagto tipoFormaPagto = TipoFormaPagto.getTipoFormaPagtoSigla(forma);
            listaFormasPagamentoDFe.add(new SelectItem(tipoFormaPagto, tipoFormaPagto.getDescricao()));
        }
        return listaFormasPagamentoDFe;
    }

    public void montarListaSelectItemFormaPagamento(){
        try {
            List<FormaPagamentoVO> formas = getFacade().getFormaPagamento().consultarSimplesFormasPagamento(false);
            formasPgto = new ArrayList<SelectItem>();
            formasPgto.add(new SelectItem(0, ""));
            for(FormaPagamentoVO fp : formas){
                formasPgto.add(new SelectItem(fp.getCodigo(), fp.getDescricao()));
            }
        }catch (Exception e){
            montarErro(e);
        }
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("razaoSocial", "Razão Social"));
        itens.add(new SelectItem("cidade", "Cidade"));
        itens.add(new SelectItem("inscEstadual", "Inscrição Estadual"));
//        itens.add(new SelectItem("questionarioPrimeira", "Questionário de Primeira Visita"));
//        itens.add(new SelectItem("questionarioRetorno", "Questionário de Retorno"));
//        itens.add(new SelectItem("questionarioRematricula", "Questionário de Rematrícula"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes
     * de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        limparMsg();
        return "consultar";
    }

    public void consultarCEP() throws Exception {
        try {
            CepVO obj = new Cep().consultarPorNumeroCep(Uteis.removerMascara(getEmpresaVO().getCEP()), Uteis.NIVELMONTARDADOS_TODOS);
            getEmpresaVO().setSetor(obj.getBairroDescricao().trim());
            getEmpresaVO().setEndereco(obj.getEnderecoLogradouro().trim());
            getEmpresaVO().setComplemento(obj.getEnderecoCompleto().trim());
            CidadeVO objCidade = getFacade().getCidade().consultarPorNome(Uteis.retirarAcentuacao(obj.getCidadeDescricao().trim()), Uteis.NIVELMONTARDADOS_TODOS);
            if (objCidade != null) {
                getEmpresaVO().setPais(objCidade.getPais());
                montarListaSelectItemEstado();
                getEmpresaVO().setEstado(objCidade.getEstado());
                montarListaSelectItemCidade();
                getEmpresaVO().setCidade(objCidade);
            } else {
                objCidade = new CidadeVO();
                PaisVO objPais = getFacade().getPais().consultarPorNome("Brasil", Uteis.NIVELMONTARDADOS_TODOS);
                if (objPais == null) {
                    throw new Exception("O pais de nome Brasil deve ser cadastrado.");
                }
                EstadoVO objEstado = new Estado().consultarPorSiglaDescricaoEPais(obj.getUfSigla().trim(), obj.getUfDescricao().trim(), objPais.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
                if (objEstado == null) {
                    objEstado = new EstadoVO();
                    objEstado.setDescricao(obj.getUfDescricao());
                    objEstado.setSigla(obj.getUfSigla());
                    objEstado.setPais(objPais.getCodigo());
                    new Estado().incluir(objEstado);
                }
                objCidade.setPais(objPais);
                objCidade.setEstado(objEstado);
                objCidade.setNome(obj.getCidadeDescricao().trim());
                getFacade().getCidade().incluir(objCidade);
                consultarCEP();
            }
            setMensagemDetalhada("");
        } catch (Exception e) {
            getEmpresaVO().setSetor("");
            getEmpresaVO().setEndereco("");
            getEmpresaVO().setComplemento("");
            getEmpresaVO().setPais(new PaisVO());
            getEmpresaVO().setEstado(new EstadoVO());
            getEmpresaVO().setCidade(new CidadeVO());
            montarListaSelectItemEstado();
            montarListaSelectItemCidade();
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void validarPermissaoAlterarSenha() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setApresentarCampoConsultorVendaAvulsa(true);
                return;
            }
            setApresentarCampoConsultorVendaAvulsa(false);
            return;
        }
        try {
            for (Object o : getUsuarioLogado().getUsuarioPerfilAcessoVOs()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) o;
                if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                            getUsuarioLogado(), "ConsultorVendaAvulsa", "4.23 - Venda Avulsa - Consultor");
                }
            }
            setApresentarCampoConsultorVendaAvulsa(true);
        } catch (Exception e) {
            setApresentarCampoConsultorVendaAvulsa(false);
        }
    }

    public Object atualizarRecibosSemConsultores() throws Exception {
        try {
            EmpresaVO empresaSalva = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaVO().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
            if (empresaSalva.getConsultorVendaAvulsa().getCodigo() == 0
                    || getEmpresaVO().getConsultorVendaAvulsa().getCodigo() == 0) {
                throw new ConsistirException("Salve o consultor, antes de atualizar os registros.");
            } else if (!empresaSalva.getConsultorVendaAvulsa().getCodigo().equals(getEmpresaVO().getConsultorVendaAvulsa().getCodigo())) {
                throw new ConsistirException("Salve o consultor, antes de atualizar os registros.");
            } else {
                getFacade().getReciboClienteConsultor().atualizarRecibosSemConsultor(getEmpresaVO().getConsultorVendaAvulsa(), empresaSalva);
                setErro(false);
                setSucesso(true);
                setMensagem("O consultor selecionado foi colocado como responsável das vendas passadas sem consultor.");
            }
            return true;
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return false;
        }
    }

    /**
     * Operação que inicializa as Interfaces Facades com os respectivos objetos
     * de persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = empresaVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));

        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(),
                empresaVO.getCodigo(), null);
    }

    public void realizarConsultaLogObjetoGeral() {
        empresaVO = new EmpresaVO();
        realizarConsultaLogObjetoSelecionado();
    }


    public List getListaSelectItemQuestionarioReMatricula() {
        return (listaSelectItemQuestionarioReMatricula);
    }

    public void setListaSelectItemQuestionarioReMatricula(List listaSelectItemQuestionarioReMatricula) {
        this.listaSelectItemQuestionarioReMatricula = listaSelectItemQuestionarioReMatricula;
    }

    public PlanoVO getPlanoEmpresaDelSoft() {
        return planoEmpresaDelSoft;
    }

    public void setPlanoEmpresaDelSoft(PlanoVO planoEmpresaDelSoft) {
        this.planoEmpresaDelSoft = planoEmpresaDelSoft;
    }

    public List getListaSelectItemQuestionarioRetorno() {
        return listaSelectItemQuestionarioRetorno;
    }

    public void setListaSelectItemQuestionarioRetorno(List listaSelectItemQuestionarioRetorno) {
        this.listaSelectItemQuestionarioRetorno = listaSelectItemQuestionarioRetorno;
    }

    public List getListaSelectItemQuestionarioPrimeiraVisita() {
        return listaSelectItemQuestionarioPrimeiraVisita;
    }

    public void setListaSelectItemQuestionarioPrimeiraVisita(List listaSelectItemQuestionarioPrimeiraVisita) {
        this.listaSelectItemQuestionarioPrimeiraVisita = listaSelectItemQuestionarioPrimeiraVisita;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public List getListaSelectItemCidade() {
        return listaSelectItemCidade;
    }

    public void setListaSelectItemCidade(List listaSelectItemCidade) {
        this.listaSelectItemCidade = listaSelectItemCidade;
    }

    public List getListaSelectItemPais() {
        return listaSelectItemPais;
    }

    public void setListaSelectItemPais(List listaSelectItemPais) {
        this.listaSelectItemPais = listaSelectItemPais;
    }

    public List getListaSelectItemContaCorrente() {
        return (listaSelectItemContaCorrente);
    }

    public void setListaSelectItemContaCorrente(List listaSelectItemContaCorrente) {
        this.listaSelectItemContaCorrente = listaSelectItemContaCorrente;
    }

    public List getListaSelectItemProdutoDevolverCancelamentoEmpresa() {
        return listaSelectItemProdutoDevolverCancelamentoEmpresa;
    }

    public void setListaSelectItemProdutoDevolverCancelamentoEmpresa(List listaSelectItemProdutoDevolverCancelamentoEmpresa) {
        this.listaSelectItemProdutoDevolverCancelamentoEmpresa = listaSelectItemProdutoDevolverCancelamentoEmpresa;
    }

    public List getListaSelectItemConsultor() {
        return listaSelectItemConsultor;
    }

    public void setListaSelectItemConsultor(List listaSelectItemConsultor) {
        this.listaSelectItemConsultor = listaSelectItemConsultor;
    }

    public ContaCorrenteEmpresaVO getContaCorrenteEmpresaVO() {
        return contaCorrenteEmpresaVO;
    }

    public void setContaCorrenteEmpresaVO(ContaCorrenteEmpresaVO contaCorrenteEmpresaVO) {
        this.contaCorrenteEmpresaVO = contaCorrenteEmpresaVO;
    }

    public ProdutoDevolverCancelamentoEmpresaVO getProdutoDevolverCancelamentoEmpresaVO() {
        return produtoDevolverCancelamentoEmpresaVO;
    }

    public void setProdutoDevolverCancelamentoEmpresaVO(ProdutoDevolverCancelamentoEmpresaVO produtoDevolverCancelamentoEmpresaVO) {
        this.produtoDevolverCancelamentoEmpresaVO = produtoDevolverCancelamentoEmpresaVO;
    }

    public List getListaSelectItemEstado() {
        return listaSelectItemEstado;
    }

    public void setListaSelectItemEstado(List listaSelectItemEstado) {
        this.listaSelectItemEstado = listaSelectItemEstado;
    }

    public List<SelectItem> getListaTimeZones() {
        return TimeZoneEnum.getSelectListTimeZone();
    }

    public String getDescricaoProcessoRealizado() {
        return descricaoProcessoRealizado;
    }

    public void setDescricaoProcessoRealizado(String descricaoProcessoRealizado) {
        this.descricaoProcessoRealizado = descricaoProcessoRealizado;
    }

    public boolean isApresentarCampoConsultorVendaAvulsa() {
        return apresentarCampoConsultorVendaAvulsa;
    }

    public void setApresentarCampoConsultorVendaAvulsa(boolean apresentarCampoConsultorVendaAvulsa) {
        this.apresentarCampoConsultorVendaAvulsa = apresentarCampoConsultorVendaAvulsa;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getEmpresa().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public List getListaSelectItemQuestionarioPrimeiraCompra() {
        return listaSelectItemQuestionarioPrimeiraCompra;
    }

    public void setListaSelectItemQuestionarioPrimeiraCompra(List listaSelectItemQuestionarioPrimeiraCompra) {
        this.listaSelectItemQuestionarioPrimeiraCompra = listaSelectItemQuestionarioPrimeiraCompra;
    }

    public List getListaSelectItemQuestionarioRetornoCompra() {
        return listaSelectItemQuestionarioRetornoCompra;
    }

    public void setListaSelectItemQuestionarioRetornoCompra(List listaSelectItemQuestionarioRetornoCompra) {
        this.listaSelectItemQuestionarioRetornoCompra = listaSelectItemQuestionarioRetornoCompra;
    }

    public List<SelectItem> getListaSelectItemTipoGestaoNFSe() {
        return listaSelectItemTipoGestaoNFSe;
    }

    public void setListaSelectItemTipoGestaoNFSe(List<SelectItem> listaSelectItemTipoGestaoNFSe) {
        this.listaSelectItemTipoGestaoNFSe = listaSelectItemTipoGestaoNFSe;
    }

    public List<SelectItem> getListaTipoCancelamento() {
        if (listaTipoCancelamento == null || listaTipoCancelamento.isEmpty()) {
            listaTipoCancelamento = JSFUtilities.getSelectItemListFromEnum(TipoCancelamentoEnum.class, "descricao", false);
        }
        return listaTipoCancelamento;
    }

    public void setListaTipoCancelamento(List<SelectItem> listaTipoCancelamento) {
        this.listaTipoCancelamento = listaTipoCancelamento;
    }

    public List<SelectItem> getListaArredondamento() {
        if (listaArredondamento == null || listaArredondamento.isEmpty()) {
            listaArredondamento = JSFUtilities.getSelectItemListFromEnum(UsoArredondamentoEnum.class, "descricao", false);
        }
        return listaArredondamento;
    }

    public void setListaArredondamento(List<SelectItem> listaArredondamento) {
        this.listaArredondamento = listaArredondamento;
    }

    public String getEmailConfigGestaoPersonal() {
        return emailConfigGestaoPersonal;
    }

    public void setEmailConfigGestaoPersonal(String emailConfigGestaoPersonal) {
        this.emailConfigGestaoPersonal = emailConfigGestaoPersonal;
    }

    public void adicionarEmailGestaoPersonal() {
        getEmpresaVO().getConfigsPersonal().getEmails().add(new ConfiguracaoGestaoPersonalEmailVO(emailConfigGestaoPersonal));
        emailConfigGestaoPersonal = "";
    }

    public void removerEmail() {
        ConfiguracaoGestaoPersonalEmailVO email = (ConfiguracaoGestaoPersonalEmailVO) context().getExternalContext().getRequestMap().get("email");
        getEmpresaVO().getConfigsPersonal().getEmails().remove(email);
    }

    public List<SelectItem> getListaExigibilidadeISS() {
        return TipoExigibilidadeISSEnum.getSelectListExigibilidadeISS();
    }

    private byte[] uploadImagem(UploadEvent upload, boolean imgGrande) throws Exception {
        limparMsg();
        UploadItem item = upload.getUploadItem();
        File item1 = item.getFile();
        boolean erroAoLerArquivo = true;
        try {
            BufferedImage outImage = ImageIO.read(item1);
            erroAoLerArquivo = false;
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[4096];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            if (imgGrande) {
                getEmpresaVO().setAlturahomeBackground640x551(String.valueOf(outImage.getHeight()));
                getEmpresaVO().setLargurahomeBackground640x551(String.valueOf(outImage.getWidth()));
            } else {
                getEmpresaVO().setAlturahomeBackground320x276(String.valueOf(outImage.getHeight()));
                getEmpresaVO().setLargurahomeBackground320x276(String.valueOf(outImage.getWidth()));
            }

            byte[] foto = arrayOutputStream.toByteArray();
            arrayOutputStream.close();
            fi.close();
            getEmpresaVO().setFeitoUploadAlgumaFoto(true);
            setMensagemID("msg_upload_arquivo");
            setSucesso(true);
            setErro(false);
            return foto;
        } catch (Exception e) {
            tratarErroAoCarregarImagem(e, erroAoLerArquivo, item1, "uploadImagem");
            return null;
        }
    }

    public void uploadImgAppPequena(UploadEvent upload) throws Exception {
        getEmpresaVO().setHomeBackground320x276(uploadImagem(upload, false));
    }

    public void uploadImgAppGrande(UploadEvent upload) throws Exception {
        getEmpresaVO().setHomeBackground640x551(uploadImagem(upload, true));
    }

    public void prepararFotos() {
        try {
            getEmpresaVO().setFoto(getFacade().getEmpresa().obterFoto(getKey(), getEmpresaVO().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA));
            getEmpresaVO().setFotoRelatorio(getFacade().getEmpresa().obterFoto(getKey(), getEmpresaVO().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
            getEmpresaVO().setFotoEmail(getFacade().getEmpresa().obterFoto(getKey(), getEmpresaVO().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_EMAIL));
            getEmpresaVO().setFotoRedeSocial(getFacade().getEmpresa().obterFoto(getKey(), getEmpresaVO().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_REDESOCIAL));
            getEmpresaVO().setHomeBackground320x276(getFacade().getEmpresa().obterFoto(getKey(), getEmpresaVO().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_HomeBackground320x276));
            getEmpresaVO().setHomeBackground640x551(getFacade().getEmpresa().obterFoto(getKey(), getEmpresaVO().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_HomeBackground640x551));
            getEmpresaVO().setPropagandaBoleto(getFacade().getEmpresa().obterFoto(getKey(), getEmpresaVO().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_PROPAGANDA_BOLETO));
        } catch (Exception e) {
            Uteis.logar(e, EmpresaControle.class);
        }
    }

    public List getListaSelectItemLocalAcessoChamada() {
        return listaSelectItemLocalAcessoChamada;
    }

    public void setListaSelectItemLocalAcessoChamada(List listaSelectItemLocalAcessoChamada) {
        this.listaSelectItemLocalAcessoChamada = listaSelectItemLocalAcessoChamada;
    }

    public List getListaSelectItemColetorChamada() {
        return listaSelectItemColetorChamada;
    }

    public void setListaSelectItemColetorChamada(List listaSelectItemColetorChamada) {
        this.listaSelectItemColetorChamada = listaSelectItemColetorChamada;
    }

    public void montarListaSelectItemLocalAcessoChamada() {
        try {
            List resultadoConsulta = getFacade().getLocalAcesso().consultarPorEmpresa(this.getEmpresaVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
            Iterator i = resultadoConsulta.iterator();
            setListaSelectItemLocalAcessoChamada(new ArrayList());
            getListaSelectItemLocalAcessoChamada().add(new SelectItem(0, ""));
            while (i.hasNext()) {
                LocalAcessoVO obj = (LocalAcessoVO) i.next();
                getListaSelectItemLocalAcessoChamada().add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
            setListaSelectItemLocalAcessoChamada(getListaSelectItemLocalAcessoChamada());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemColetorChamada() {
        try {
            List resultadoConsulta = getFacade().getColetor().consultarColetores(this.getEmpresaVO().getLocalAcessoChamada().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            Iterator i = resultadoConsulta.iterator();
            setListaSelectItemColetorChamada(new ArrayList());
            getListaSelectItemColetorChamada().add(new SelectItem(new Integer(0), ""));
            while (i.hasNext()) {
                ColetorVO obj = (ColetorVO) i.next();
                getListaSelectItemColetorChamada().add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
            }
            setListaSelectItemColetorChamada(getListaSelectItemColetorChamada());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemTipoParcelaCancelar() {
        try {
            setListaTipoParcelasCancelamento(JSFUtilities.getSelectItemListFromEnum(TipoParcelaCancelamento.class,"sigla","descricao",false));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List getListaSelectItemConsultorComUsuario() {
        return listaSelectItemConsultorComUsuario;
    }

    public void setListaSelectItemConsultorComUsuario(List listaSelectItemConsultorComUsuario) {
        this.listaSelectItemConsultorComUsuario = listaSelectItemConsultorComUsuario;
    }

    public List<SelectItem> getListaSelectItemTipoEmpresa() {
        return listaSelectItemTipoEmpresa;
    }

    public List<SelectItem> getListaSelectPlanosEmpresa() {
        return listaSelectPlanosEmpresa;
    }

    public void setListaSelectPlanosEmpresa(List<SelectItem> listaPlanosEmpresa) {
        this.listaSelectPlanosEmpresa = listaPlanosEmpresa;
    }

    public void setListaSelectItemTipoEmpresa(List<SelectItem> listaSelectItemTipoEmpresa) {
        this.listaSelectItemTipoEmpresa = listaSelectItemTipoEmpresa;
    }

    public void setListaSelectItemModeloMensagem(List<SelectItem> listaSelectItemModeloMensagem) {
        this.listaSelectItemModeloMensagem = listaSelectItemModeloMensagem;
    }

    public List<SelectItem> getListaSelectItemModeloMensagem() {
        return listaSelectItemModeloMensagem;
    }

    public List<SelectItem> getListaSelectItemTipoParcelasACobrar() {
        return listaSelectItemTipoParcelasACobrar;
    }

    public void setListaSelectItemTipoParcelasACobrar(List<SelectItem> listaSelectItemTipoParcelasACobrar) {
        this.listaSelectItemTipoParcelasACobrar = listaSelectItemTipoParcelasACobrar;
    }

    public List<SelectItem> getListaSelectItemConvenioBoleto() {
        return listaSelectItemConvenioBoleto;
    }

    public void setListaSelectItemConvenioBoleto(List<SelectItem> listaSelectItemConvenioBoleto) {
        this.listaSelectItemConvenioBoleto = listaSelectItemConvenioBoleto;
    }

    public List<SelectItem> getListaTipoParcelasCancelamento() {
        return listaTipoParcelasCancelamento;
    }

    public void setListaTipoParcelasCancelamento(List<SelectItem> listaTipoParcelasCancelamento) {
        this.listaTipoParcelasCancelamento = listaTipoParcelasCancelamento;
    }

    public void incluirLogInclusao() throws Exception {
        try {
            empresaVO.setObjetoVOAntesAlteracao(new EmpresaVO());
            empresaVO.setNovoObj(true);
            registrarLogObjetoVO(empresaVO, empresaVO.getCodigo(), "EMPRESA", 0);
            empresaVO.getConfiguracaoRDStation().setObjetoVOAntesAlteracao(new ConfiguracaoEmpresaRDStationVO());
            empresaVO.getConfiguracaoRDStation().setNovoObj(true);
            registrarLogObjetoVO(empresaVO.getConfiguracaoRDStation(), empresaVO.getCodigo(), "EMPRESA", 0);
            incluirLogAlteracoesContaCorrenteEmpresa();
        } catch (Exception e) {
            registrarLogErroObjetoVO("EMPRESA", empresaVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE EMPRESA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        empresaVO.setNovoObj(new Boolean(false));
        empresaVO.registrarObjetoVOAntesDaAlteracao();
        empresaVO.registrarContaCorrenteEmpresaVOsAntesAlteracao();
        empresaVO.registrarConfiguracaoReenvioMovParcelaEmpresaVOSAntesAlteracao();
        empresaVO.getConfiguracaoRDStation().setNovoObj(new Boolean(false));
        empresaVO.getConfiguracaoRDStation().registrarObjetoVOAntesDaAlteracao();
    }

    public void incluirLogExclusao() throws Exception {
        try {
            empresaVO.setObjetoVOAntesAlteracao(new EmpresaVO());
            empresaVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(empresaVO, empresaVO.getCodigo(), "EMPRESA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("EMPRESA", empresaVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE EMPRESA ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     *
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(empresaVO, empresaVO.getCodigo(), "EMPRESA", 0);
            registrarLogObjetoVO(empresaVO.getConfiguracaoRDStation(), empresaVO.getCodigo(), "EMPRESA", 0);
            incluirLogAlteracoesContaCorrenteEmpresa();

            Integer idConvenioAtual = empresaVO.getConvenioCobrancaCartao().getCodigo();
            EmpresaVO EmpresavoAntesAlterar = (EmpresaVO) empresaVO.getObjetoVOAntesAlteracao();
            Integer idConvenioAntes = EmpresavoAntesAlterar.getConvenioCobrancaCartao().getCodigo();
            if (idConvenioAntes != idConvenioAtual) {
                incluirLogAlteracaoConvenioCobrancaCartao(idConvenioAntes, idConvenioAtual);
            }

        } catch (Exception e) {
            registrarLogErroObjetoVO("EMPRESA", empresaVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAçãO DE EMPRESA ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        empresaVO.registrarObjetoVOAntesDaAlteracao();
        empresaVO.registrarContaCorrenteEmpresaVOsAntesAlteracao();
        empresaVO.registrarConfiguracaoReenvioMovParcelaEmpresaVOSAntesAlteracao();
        empresaVO.getConfiguracaoRDStation().registrarObjetoVOAntesDaAlteracao();
    }

    private void incluirLogAlteracaoConvenioCobrancaCartao(Integer anterior, Integer atual) throws Exception {
        try{
            LogVO logVO = new LogVO();
            logVO.setOperacao("ALTERAçãO");
            logVO.setChavePrimaria(String.valueOf(empresaVO.getCodigo()));
//            logVO.setChavePrimariaEntidadeSubordinada(atual.getCodigo().toString());
            logVO.setNomeEntidade("EMPRESA");
            logVO.setNomeEntidadeDescricao("EMPRESA - ConvenioCobrancaCartao");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior("ConvenioCobrancaCartao= " + anterior);
            logVO.setValorCampoAlterado("ConvenioCobrancaCartao= " + atual);

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("EMPRESA", empresaVO.getCodigo(), "ERRO AO GERAR LOG  DE CONTA CORRENTE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void ativarVerificacaoClientesAtivos() {
        try {
            getFacade().getCliente().ativarVerificacaoClientesAtivos(empresaVO.getCodigo());

            LogVO obj = new LogVO();
            obj.setChavePrimaria(empresaVO.getCodigo().toString());
            obj.setNomeEntidade("EMPRESA - ATIVAR VERIFICAçãO");
            obj.setNomeEntidadeDescricao("Empresa");
            obj.setOperacao("ALTERAçãO");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setNomeCampo("VERIFICAçãO DE CLIENTES ATIVOS");
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setValorCampoAnterior("");
            obj.setValorCampoAlterado("Ativou a Verificação de Clientes Ativos");
            getFacade().getLog().incluir(obj);

            montarSucessoGrowl("Verificação de Clientes ATIVA");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void incluirLogAlteracoesContaCorrenteEmpresa() throws Exception {
        for(ContaCorrenteEmpresaVO atual : empresaVO.getContaCorrenteEmpresaVOs()){
            boolean nova = true;
            for(ContaCorrenteEmpresaVO anterior : empresaVO.getContaCorrenteEmpresaVOsAntesAlteracao()){
                if(anterior.getCodigo().equals(atual.getCodigo())){
                    if(!anterior.getEmpresa().equals(atual.getEmpresa())
                            || !anterior.getContaCorrente().getCodigo().equals(atual.getContaCorrente().getCodigo())){
                        incluirLogAlteracaoContas(anterior ,atual);
                    }
                    nova = false;
                    break;
                }
            }
            if(nova){
                incluirLogInclusaoContas(atual);
            }
        }
        for(ContaCorrenteEmpresaVO anterior : empresaVO.getContaCorrenteEmpresaVOsAntesAlteracao()){
            boolean excluida = true;
            for(ContaCorrenteEmpresaVO atual : empresaVO.getContaCorrenteEmpresaVOs()){
                if(anterior.getCodigo().equals(atual.getCodigo())){
                    excluida = false;
                    break;
                }
            }
            if(excluida){
                incluirLogExclusaoContas(anterior);
            }
        }

    }

    private void incluirLogAlteracaoContas(ContaCorrenteEmpresaVO anterior, ContaCorrenteEmpresaVO atual) throws Exception {
        try{
            LogVO logVO = new LogVO();
            logVO.setOperacao("ALTERAçãO");
            logVO.setChavePrimaria(String.valueOf(empresaVO.getCodigo()));
            logVO.setChavePrimariaEntidadeSubordinada(atual.getCodigo().toString());
            logVO.setNomeEntidade("EMPRESA");
            logVO.setNomeEntidadeDescricao("EMPRESA - ContaCorrente");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior("Empresa= " + anterior.getEmpresa() +"\n  conta corrente= "+anterior.getContaCorrente().getCodigo() );
            logVO.setValorCampoAlterado("Empresa= " + atual.getEmpresa() +"\n  conta corrente= "+ atual.getContaCorrente().getCodigo());

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("EMPRESA", empresaVO.getCodigo(), "ERRO AO GERAR LOG  DE CONTA CORRENTE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    private void incluirLogInclusaoContas(ContaCorrenteEmpresaVO atual) throws Exception {
        try{
            LogVO logVO = new LogVO();
            logVO.setOperacao("INCLUSÃO");
            logVO.setChavePrimaria(String.valueOf(empresaVO.getCodigo()));
            logVO.setChavePrimariaEntidadeSubordinada(atual.getCodigo().toString());
            logVO.setNomeEntidade("EMPRESA");
            logVO.setNomeEntidadeDescricao("EMPRESA - ContaCorrente");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior("");
            logVO.setValorCampoAlterado("Empresa= " + atual.getEmpresa() +"\n  conta corrente= "+ atual.getContaCorrente().getCodigo());

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("EMPRESA", empresaVO.getCodigo(), "ERRO AO GERAR LOG DE CONTA CORRENTE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    private void incluirLogExclusaoContas(ContaCorrenteEmpresaVO anterior) throws Exception {
        try{
            LogVO logVO = new LogVO();
            logVO.setOperacao("EXCLUSÃO");
            logVO.setChavePrimaria(String.valueOf(empresaVO.getCodigo()));
            logVO.setChavePrimariaEntidadeSubordinada(anterior.getCodigo().toString());
            logVO.setNomeEntidade("EMPRESA");
            logVO.setNomeEntidadeDescricao("EMPRESA - ContaCorrente");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior("");
            logVO.setValorCampoAlterado("Empresa= " + anterior.getEmpresa() +"\n  conta corrente= "+ anterior.getContaCorrente().getCodigo());

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("EMPRESA", empresaVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE CONTA CORRENTE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void consultarContratosRenovacaoAutomaticaSemPlano() {
        if (this.listaContratosRenovacaoAutomaticaSemPlano.isEmpty()) {
            EmpresaVO empresa;
            try {
                empresa = getFacade().getEmpresa().consultarPorNomeEmpresa(getEmpresaLogado().getNome(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                this.listaContratosRenovacaoAutomaticaSemPlano = getFacade().getContrato().consultarContratosAtivosPorPlano(null, empresa.getCodigo());
            } catch (Exception ex) {
                Logger.getLogger(EmpresaControle.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        this.textoLinkRenovacaoAutomaticaSemPlano = null;
        if (!this.listaContratosRenovacaoAutomaticaSemPlano.isEmpty()){
            for (ContratoVO contrato : listaContratosRenovacaoAutomaticaSemPlano) {
                boolean marcado = false;
                if (contrato.getRegimeRecorrencia() && contrato.getRenovavelAutomaticamente()) {
                    marcado = true;
                }
                if (marcado){
                    if (this.listaContratosRenovacaoAutomaticaSemPlano.size() == 1)
                        this.textoLinkRenovacaoAutomaticaSemPlano = "Atenção ! '" +this.listaContratosRenovacaoAutomaticaSemPlano.size() + "' contrato não será mais renovado automaticamente.";
                    else
                        this.textoLinkRenovacaoAutomaticaSemPlano = "Atenção ! '" +this.listaContratosRenovacaoAutomaticaSemPlano.size() + "' contratos não serão mais renovados automaticamente.";
                }else{
                    if (this.listaContratosRenovacaoAutomaticaSemPlano.size() == 1)
                        this.textoLinkRenovacaoAutomaticaSemPlano = "Atenção ! '" +this.listaContratosRenovacaoAutomaticaSemPlano.size() + "' contrato será renovado automaticamente.";
                    else
                        this.textoLinkRenovacaoAutomaticaSemPlano = "Atenção ! '" +this.listaContratosRenovacaoAutomaticaSemPlano.size() + "' contratos serão renovados automaticamente.";
                }
            }
        }

    }

    public String getTotalContratosRenovarAuto() {
        String retorno = "";
        if (!this.listaContratosRenovacaoAutomaticaSemPlano.isEmpty()) {
            for (ContratoVO contrato : listaContratosRenovacaoAutomaticaSemPlano) {
                boolean marcado = false;
                if (contrato.getRegimeRecorrencia() && contrato.getRenovavelAutomaticamente()) {
                    marcado = true;
                }
                if (marcado){
                    if (this.listaContratosRenovacaoAutomaticaSemPlano.size() == 1)
                        retorno = "Atenção ! '" +this.listaContratosRenovacaoAutomaticaSemPlano.size() + "' contrato não será mais renovado automaticamente.";
                    else
                        retorno = "Atenção ! '" +this.listaContratosRenovacaoAutomaticaSemPlano.size() + "' contratos não serão mais renovados automaticamente.";
                }else{
                    if (this.listaContratosRenovacaoAutomaticaSemPlano.size() == 1)
                        retorno = "Atenção ! '" +this.listaContratosRenovacaoAutomaticaSemPlano.size() + "' contrato será renovado automaticamente.";
                    else
                        retorno = "Atenção ! '" +this.listaContratosRenovacaoAutomaticaSemPlano.size() + "' contratos serão renovados automaticamente.";
                }
            }
        }
        return retorno;
    }

    public void setTextoLinkRenovacaoAutomaticaSemPlano(String textoLinkRenovacaoAutomaticaSemPlano) {
        this.textoLinkRenovacaoAutomaticaSemPlano = textoLinkRenovacaoAutomaticaSemPlano;
    }

    public String getTextoLinkRenovacaoAutomaticaSemPlano() {
        consultarContratosRenovacaoAutomaticaSemPlano();
        return textoLinkRenovacaoAutomaticaSemPlano;
    }

    public void setListaContratosRenovacaoAutomaticaSemPlano(List<ContratoVO> listaContratosRenovacaoAutomaticaSemPlano) {
        this.listaContratosRenovacaoAutomaticaSemPlano = listaContratosRenovacaoAutomaticaSemPlano;
    }

    public List<ContratoVO> getListaContratosRenovacaoAutomaticaSemPlano() {
        return listaContratosRenovacaoAutomaticaSemPlano;
    }

    public void desmarcarDefinirCpfComoSenha(){
        if (!getEmpresaVO().isSenhaAcessoOnzeDigitos()){
            getEmpresaVO().setDefinirCpfComoSenhaCatraca(false);
        }
    }


    public List<ConfigTotemEnum> getConfigsTotem(){
        return new ArrayList<>(ConfigTotemEnum.valores(ControleAcesso.isPermiteMultiEmpresas()));
    }


    public List<SelectItem> getListaUsuariosMeta() {
        return listaUsuariosMeta;
    }

    public void setListaUsuariosMeta(List<SelectItem> listaUsuariosMeta) {
        this.listaUsuariosMeta = listaUsuariosMeta;
    }

    public void montarListaUsuariosMeta() {
        try {
            List<UsuarioVO> usuarios = getFacade().getUsuario().consultarUsuarioAberturaMeta(empresaVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
            listaUsuariosMeta = new ArrayList<SelectItem>();
            boolean usuarioPresente = false;
            if(!UteisValidacao.emptyNumber(empresaVO.getConfiguracaoRDStation().getResponsavelPadrao().getCodigo())){
                listaUsuariosMeta.add(new SelectItem(empresaVO.getConfiguracaoRDStation().getResponsavelPadrao().getCodigo(), empresaVO.getConfiguracaoRDStation().getResponsavelPadrao().getNome()));
            } else {
                empresaVO.getConfiguracaoRDStation().getResponsavelPadrao().setCodigo(0);
                listaUsuariosMeta.add(new SelectItem(0, ""));
                usuarioPresente = true;
            }
            for(UsuarioVO usuario: usuarios){
                if(usuario.getCodigo().equals(empresaVO.getConfiguracaoRDStation().getResponsavelPadrao().getCodigo())){
                    usuarioPresente = true;
                } else {
                    listaUsuariosMeta.add(new SelectItem(usuario.getCodigo(), usuario.getNome()));
                }
            }
            empresaVO.getConfiguracaoRDStation().setUsuarioInativo(!usuarioPresente);
        }catch (Exception ignored) {

        }
    }

    public void montarAcaoObjecaoLead() {
        listaAcaoObjecaoLead = new ArrayList<SelectItem>();
        for(AcaoObjcaoLeadEnum acao : AcaoObjcaoLeadEnum.values()){
            listaAcaoObjecaoLead.add(new SelectItem(acao.getCodigo(), acao.getDescricao()));
        }
    }

    public List<SelectItem> getListaAcaoObjecaoLead() {
        return listaAcaoObjecaoLead;
    }

    public void setListaAcaoObjecaoLead(List<SelectItem> listaAcaoObjecaoLead) {
        this.listaAcaoObjecaoLead = listaAcaoObjecaoLead;
    }

    public void montarListaSelectItemConvenio(){
        try {
            setListaSelectConvenioCobranca(new ArrayList<SelectItem>());
            List<ConvenioCobrancaVO> resultadoConsulta = getFacade().getConvenioCobranca().obterListaConvenioCobrancaRetentativa(getEmpresaVO().getCodigo());
            for (ConvenioCobrancaVO obj : resultadoConsulta) {
                getListaSelectConvenioCobranca().add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
            Ordenacao.ordenarLista(getListaSelectConvenioCobranca(), "label");
            getListaSelectConvenioCobranca().add(0, new SelectItem(0, ""));
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public List<SelectItem> getListaSelectConvenioCobrancaRetentativa() throws Exception {
        List<SelectItem> lista = new ArrayList<>();

        for (SelectItem conv : getListaSelectConvenioCobranca()) {

            boolean existe = false;
            for (ConfiguracaoReenvioMovParcelaEmpresaVO confg : getEmpresaVO().getConfiguracaoReenvioMovParcelaEmpresaVOS()) {
                if (confg.getConvenioCobrancaVO().getCodigo().equals((Integer) conv.getValue())) {
                    existe = true;
                    break;
                }
            }

            if (!existe) {
                lista.add(conv);
            }
        }

        return lista;
    }


    public List<SelectItem> getListaSelectConvenioCobranca() throws Exception {
        if (listaSelectConvenioCobranca == null) {
            listaSelectConvenioCobranca  = new ArrayList<>();
        }
        return listaSelectConvenioCobranca;
    }

    public void setListaSelectConvenioCobranca(List<SelectItem> listaSelectConvenioCobranca) {
        this.listaSelectConvenioCobranca = listaSelectConvenioCobranca;
    }

    public List<SelectItem> getFormasPgto() {
        return formasPgto;
    }

    public void setFormasPgto(List<SelectItem> formasPgto) {
        this.formasPgto = formasPgto;
    }

    public List<SelectItem> getListaSelectConvenioCobrancaDCO() {
        if (listaSelectConvenioCobrancaDCO == null) {
            listaSelectConvenioCobrancaDCO = new ArrayList<SelectItem>();
        }
        return listaSelectConvenioCobrancaDCO;
    }

    public void setListaSelectConvenioCobrancaDCO(List<SelectItem> listaSelectConvenioCobrancaDCO) {
        this.listaSelectConvenioCobrancaDCO = listaSelectConvenioCobrancaDCO;
    }

    public void montarListaSelectItemConvenioDCO(){
        try {
            Integer tipos[] = {
                    TipoConvenioCobrancaEnum.DCO_BB.getCodigo(),
                    TipoConvenioCobrancaEnum.DCO_BRADESCO.getCodigo(),
                    TipoConvenioCobrancaEnum.DCO_ITAU.getCodigo(),
                    TipoConvenioCobrancaEnum.DCO_CAIXA.getCodigo(),
                    TipoConvenioCobrancaEnum.DCO_HSBC.getCodigo(),
                    TipoConvenioCobrancaEnum.DCO_SANTANDER.getCodigo()};

            List<ConvenioCobrancaVO> resultadoConsulta = getFacade().getConvenioCobranca().consultarPorEmpresaESituacao(getEmpresaVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS, tipos, SituacaoConvenioCobranca.ATIVO);
            listaSelectConvenioCobrancaDCO = new ArrayList<SelectItem>();
            listaSelectConvenioCobrancaDCO.add(new SelectItem(0, ""));
            for (ConvenioCobrancaVO obj : resultadoConsulta) {
                listaSelectConvenioCobrancaDCO.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        }catch (Exception e){
            montarErro(e);
        }
    }

    public void selecionarTotem(){
        totem = (TotemTO) context().getExternalContext().getRequestMap().get("totem");
    }

    public void removerTotem(){
        TotemTO t = (TotemTO) context().getExternalContext().getRequestMap().get("totem");
        totens.remove(t);
    }

    public void novoTotem(){
        totem = new TotemTO();
    }

    public void adicionarTotem() {
        try {
            if(UteisValidacao.emptyString(totem.getTotem())){
                throw new Exception("Informe um nome para o totem");
            }
            for (TotemTO tt : new ArrayList<TotemTO>(totens)) {
                if (tt.getTotem().equals(totem.getTotem())) {
                    totens.remove(tt);
                }
            }
            totem.setTotem(Uteis.retirarAcentuacaoRegex(totem.getTotem().toUpperCase()));
            totem.setEmpresa(empresaVO.getCodigo());
            totens.add(totem);
            totens = Ordenacao.ordenarLista(totens, "totem");
            totem = new TotemTO();
            limparMsg();
        } catch (Exception e) {
            montarErro(e);
        }

    }

    public List<TotemTO> getTotens() {
        return totens;
    }

    public void setTotens(List<TotemTO> totens) {
        this.totens = totens;
    }

    public TotemTO getTotem() {
        return totem;
    }

    public void setTotem(TotemTO totem) {
        this.totem = totem;
    }

    public void removerConfiguracaoReenvioMovParcelaEmpresa() {
        try {
            setAlterouRetentativa(true);
            limparMsg();
            ConfiguracaoReenvioMovParcelaEmpresaVO obj = (ConfiguracaoReenvioMovParcelaEmpresaVO) context().getExternalContext().getRequestMap().get("reenvio");
            getEmpresaVO().excluirObjConfiguracaoReenvioMovParcelaEmpresas(obj);
            getEmpresaVO().ordenarListaConfiguracaoReenvioMovParcelaEmpresa();
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void adicionarConfiguracaoReenvioMovParcelaEmpresa() {
        try {
            setAlterouRetentativa(true);
            limparMsg();
            if (UteisValidacao.emptyNumber(getConfiguracaoReenvioMovParcelaEmpresaVO().getConvenioCobrancaVO().getCodigo())) {
                throw new Exception("Convênio não encontrado");
            }
            getConfiguracaoReenvioMovParcelaEmpresaVO().setTentativasRealizar(1);
            getConfiguracaoReenvioMovParcelaEmpresaVO().getEmpresaVO().setCodigo(getEmpresaVO().getCodigo());
            getConfiguracaoReenvioMovParcelaEmpresaVO().setConvenioCobrancaVO(getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(getConfiguracaoReenvioMovParcelaEmpresaVO().getConvenioCobrancaVO().getCodigo(), getConfiguracaoReenvioMovParcelaEmpresaVO().getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            getConfiguracaoReenvioMovParcelaEmpresaVO().setPosicao(getEmpresaVO().getConfiguracaoReenvioMovParcelaEmpresaVOS().size() + 1);
            getEmpresaVO().adicionarObjConfiguracaoReenvioMovParcelaEmpresaVOs(getConfiguracaoReenvioMovParcelaEmpresaVO());
            this.setConfiguracaoReenvioMovParcelaEmpresaVO(new ConfiguracaoReenvioMovParcelaEmpresaVO());
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public ConfiguracaoReenvioMovParcelaEmpresaVO getConfiguracaoReenvioMovParcelaEmpresaVO() {
        return configuracaoReenvioMovParcelaEmpresaVO;
    }

    public void setConfiguracaoReenvioMovParcelaEmpresaVO(ConfiguracaoReenvioMovParcelaEmpresaVO configuracaoReenvioMovParcelaEmpresaVO) {
        this.configuracaoReenvioMovParcelaEmpresaVO = configuracaoReenvioMovParcelaEmpresaVO;
    }

    public void selecionarConvenioCobranca() {
        try {
            if (UteisValidacao.emptyNumber(getConfiguracaoReenvioMovParcelaEmpresaVO().getConvenioCobrancaVO().getCodigo())) {
                setConfiguracaoReenvioMovParcelaEmpresaVO(new ConfiguracaoReenvioMovParcelaEmpresaVO());
            } else {
                getConfiguracaoReenvioMovParcelaEmpresaVO().setConvenioCobrancaVO(getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(getConfiguracaoReenvioMovParcelaEmpresaVO().getConvenioCobrancaVO().getCodigo(), getConfiguracaoReenvioMovParcelaEmpresaVO().getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void moverConfiguracaoReenvioParaCima() {
        try {
            setAlterouRetentativa(true);
            limparMsg();
            ConfiguracaoReenvioMovParcelaEmpresaVO obj = (ConfiguracaoReenvioMovParcelaEmpresaVO) context().getExternalContext().getRequestMap().get("reenvio");
            for (ConfiguracaoReenvioMovParcelaEmpresaVO fase : getEmpresaVO().getConfiguracaoReenvioMovParcelaEmpresaVOS()) {
                if (obj.getPosicao() == fase.getPosicao() + 1) {
                    fase.setPosicao(fase.getPosicao() + 1);
                    obj.setPosicao(obj.getPosicao() - 1);
                    break;
                }
            }
            getEmpresaVO().setConfiguracaoReenvioMovParcelaEmpresaVOS(Ordenacao.ordenarLista(getEmpresaVO().getConfiguracaoReenvioMovParcelaEmpresaVOS(), "posicao"));
        } catch (Exception e) {
            montarErro(e.getMessage());
        }
    }

    public void moverConfiguracaoReenvioParaBaixo() {
        try {
            setAlterouRetentativa(true);
            limparMsg();
            ConfiguracaoReenvioMovParcelaEmpresaVO obj = (ConfiguracaoReenvioMovParcelaEmpresaVO) context().getExternalContext().getRequestMap().get("reenvio");
            for (ConfiguracaoReenvioMovParcelaEmpresaVO fase : getEmpresaVO().getConfiguracaoReenvioMovParcelaEmpresaVOS()) {
                if (fase.getPosicao() == obj.getPosicao() + 1) {
                    fase.setPosicao(fase.getPosicao() - 1);
                    obj.setPosicao(obj.getPosicao() + 1);
                    break;
                }
            }
            getEmpresaVO().setConfiguracaoReenvioMovParcelaEmpresaVOS(Ordenacao.ordenarLista(getEmpresaVO().getConfiguracaoReenvioMovParcelaEmpresaVOS(), "posicao"));
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public String getTokenBuzzLead(){
        if(UteisValidacao.emptyNumber(empresaVO.getCodigo())){
            return "";
        }
        String chavePlana = (String) JSFUtilities.getFromSession("key");
        chavePlana += ";" + empresaVO.getCodigo();
        try {
            String chave =  Criptografia.encrypt(chavePlana, SuperControle.chaveBuzzLead, AlgoritmoCriptoEnum.ALGORITMO_AES);
            String de = Criptografia.decrypt(chave, SuperControle.chaveBuzzLead, AlgoritmoCriptoEnum.ALGORITMO_AES);
            return chave;

        } catch (Exception ex) {
            return "problemas ao gerar token";
        }
    }

    public String getUrlWebHookBuzz() {
        String urlBase = PropsService.getPropertyValue(PropsService.urlZWAPI);
        String servlet = "/prest/lead/";
        String operacao = "addLeadBuzz";
        return urlBase+servlet+operacao;
    }

    public String getUrlWebHookRd() throws Exception {
        String modelo = "/lead/{chave}/{emp}/v2/addLead";

        String urlBase = PropsService.getPropertyValue(PropsService.urlZWAPI) + "/prest/lead";
        String chave = "/" + JSFUtilities.getFromSession("key");
        String empresa = "/" + empresaVO.getCodigo();
        String params = "/v2/addLeadWebhook";
        return urlBase + chave + empresa + params;
    }

    public String getUrlWebHookHubspot() throws Exception {
        String modelo = "/lead/{chave}/{emp}/v2/addLead";

        String urlBase = PropsService.getPropertyValue(PropsService.urlZWAPI) + "/prest/lead";
        String chave = "/" + JSFUtilities.getFromSession("key");
        String empresa = "/" + empresaVO.getCodigo();
        String params = "/v2/addLeadHubspot";
        return urlBase + chave + empresa + params;
    }
    public String getUrlApiLeadOldRD() {
        String urlBase = JSFUtilities.getFromSession("urlBrowser")+request().getContextPath();
        String servlet = "/prest/lead/rdstation?";
        String chaveZW = "key="+JSFUtilities.getFromSession("key");
        String operacao = "&op=persistirLeadRDOLD";
        String empresa = "&emp="+empresaVO.getCodigo();
        return urlBase+servlet+chaveZW+empresa+operacao;
    }

    public void urloauth2() {
        try {
            setCallBackRDStation("");
            limparMsg();
            setMsgAlert("");
            if(UteisValidacao.emptyString(empresaVO.getConfiguracaoRDStation().getEventWeebHook())){
                throw new Exception("Informe um gatilho para inserção do weebhook na plataforma RD.");
            }else{
                gravar(false, false);
                if(!getSucesso())
                    return;
            }

            ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
            String url = PropsService.getPropertyValue(PropsService.urlZWAPI) + "/prest/lead/povoarParansRD";
            JSONObject params = new JSONObject();
            params.put("key", (String) JSFUtilities.getFromSession("key"));
            params.put("empresa", empresaVO.getCodigo());
            params.put("idEvent", empresaVO.getConfiguracaoRDStation().getEventWeebHook());
            params.put("expiration", String.valueOf(new Date().getTime()+300000)); // 5 minutos.

            String response = executeRequestHttpService.executeRequestCupomDesconto(url, params.toString(), new HashMap<>());

            if (response.equals("OK")) {
                String urlBase = "https://app.rdstation.com.br/api/platform/auth?client_id=";
                String clientID = empresaVO.getConfiguracaoRDStation().getClientIdOauth();
                String urlBaseRetorno = "&redirect_url=" + PropsService.getPropertyValue(PropsService.urlZWAPI) + "/prest/lead/";
                String param = "oauth2redirect";
                setCallBackRDStation(urlBase + clientID + urlBaseRetorno + param);
                setMsgAlert("abrirPopupMaximizada('"+getCallBackRDStation()+"','pop')");
            }else if(response.equals("FAIL")){
                montarErro("Limite de requisições atingido. Tente novamente em 5 minutos.");
            }
        } catch (Exception e) {
            montarErro("Falha ao tentar validar RD: "+e.getMessage());
        }
    }

    public void urlHubsoauth2() {
        try {
            setCallBackRDStation("");
            limparMsg();
            setMsgAlert("");

            ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
            String url = PropsService.getPropertyValue(PropsService.urlZWAPI) + "/prest/lead/povoarParansHubspot";
            String urlConfiguracao = PropsService.getPropertyValue(PropsService.urlZWAPI) + "/prest/lead/config-hubspot";
            setUrlCallBackHubsPost(PropsService.getPropertyValue(PropsService.urlZWAPI) + "/prest/lead/oauth-callback");
            JSONObject params = new JSONObject();
            params.put("key", (String) JSFUtilities.getFromSession("key"));
            params.put("empresa", empresaVO.getCodigo());
            params.put("clientId", empresaVO.getConfiguracaoEmpresaHubspot().getClientId());
            params.put("expiration", String.valueOf(new Date().getTime()+300000)); // 5 minutos.
            params.put("redirect_uri", getUrlCallBackHubsPost());
            String response = executeRequestHttpService.executeRequestCupomDesconto(url, params.toString(), new HashMap<>());

            params = new JSONObject();
            params.put("empresa", empresaVO.getCodigo());
            params.put("empresausahub", true);
            params.put("horaexpiracao", "23:59");
            params.put("clientsecret", empresaVO.getConfiguracaoEmpresaHubspot().getClientsecret());
            params.put("clientId", empresaVO.getConfiguracaoEmpresaHubspot().getClientId());
            params.put("codigo", empresaVO.getConfiguracaoEmpresaHubspot().getCodigo());
            params.put("url_instalacao", empresaVO.getConfiguracaoEmpresaHubspot().getUrl_instalacao());
            params.put("url_redirect",  getUrlCallBackHubsPost());

            if(empresaVO.getConfiguracaoEmpresaHubspot().getClientId().isEmpty() || empresaVO.getConfiguracaoEmpresaHubspot().getUrl_instalacao().isEmpty()){
                  montarErro("Preencha os dados do aplicativo hubspot");
            }

             HashMap header = new HashMap<>();
             header.put("chave", (String) JSFUtilities.getFromSession("key"));
             executeRequestHttpService.post(urlConfiguracao, params.toString(), header);

            if (response.equals("OK")) {
                setCallBackHubsPost(empresaVO.getConfiguracaoEmpresaHubspot().getUrl_instalacao());
                setMsgAlert("abrirPopupMaximizada('"+getCallBackHubsPost()+"','pop')");
              }else if(response.equals("FAIL")){
                montarErro("Limite de requisições atingido. Tente novamente em 5 minutos.");
            }
        } catch (Exception e) {
            montarErro("Falha ao tentar validar Hubspot: "+e.getMessage());
        }
    }


    public String getCodigoWebHookWeHelp() {
        return (String) JSFUtilities.getFromSession("key");
    }

    public ConfigProdutoZillyon getProdutoZillyon() {
        if(produtoZillyon == null){
            produtoZillyon = new ConfigProdutoZillyon(null);
            try {
                List<ProdutoVO> produtos = getFacade().getProduto().consultarPorProdutoZillyon(false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if(!produtos.isEmpty()){
                    produtoZillyon.setProdutos(produtos);
                    ProdutoVO prod = produtos.iterator().next();
                    produtoZillyon.setAliquotaCOFINS(prod.getAliquotaCOFINS());
                    produtoZillyon.setAliquotaISSQN(prod.getAliquotaISSQN());
                    produtoZillyon.setAliquotaPIS(prod.getAliquotaPIS());
                    produtoZillyon.setCfop(prod.getCfop());
                    produtoZillyon.setCodigoListaServico(prod.getCodigoListaServico());
                    produtoZillyon.setCodigoTributacaoMunicipio(prod.getCodigoTributacaoMunicipio());
                    produtoZillyon.setNcmNFCe(prod.getNcmNFCe());
                }

            } catch (Exception e) {

            }


        }
        return produtoZillyon;
    }

    public void setProdutoZillyon(ConfigProdutoZillyon produtoZillyon) {
        this.produtoZillyon = produtoZillyon;
    }

    public TipoFormaPagto getCodigoFormaPagamentoNFCe() {
        return formaPagamentoNFCe;
    }

    public void setCodigoFormaPagamentoNFCe(TipoFormaPagto formaPagamentoNFCe) {
        this.formaPagamentoNFCe = formaPagamentoNFCe;
    }

    public TipoFormaPagto getCodigoFormaPagamentoNFSe() {
        return formaPagamentoNFSe;
    }

    public void setCodigoFormaPagamentoNFSe(TipoFormaPagto formaPagamentoNFSe) {
        this.formaPagamentoNFSe = formaPagamentoNFSe;
    }

    public List<SelectItem> getListaFormasPagamento() {
        return listaFormasPagamento;
    }

    public void setListaFormasPagamento(List<SelectItem> listaFormasPagamento) {
        this.listaFormasPagamento = listaFormasPagamento;
    }

    public void adicionarFormaPagamentoNFCe() {
        adicionarFormaPagamento(listaFormasPagamentoNFCe, formaPagamentoNFCe);
    }

    public void adicionarFormaPagamentoNFSe() {
        adicionarFormaPagamento(listaFormasPagamentoNFSe, formaPagamentoNFSe);
    }

    public void removerFormaPagamentoNFCe() {
        removerFormaPagamento(this.listaFormasPagamentoNFCe);
    }

    public void gravarFormasPagamentoNFCe() {
        gravarFormasPagamento(true);
    }

    public void alterarFormaPagamentoNFCe() throws Exception {
        montarListaFormasPagamentoNFCe();
        setMsgAlert(RICHFACES_SHOW_MODAL_EDITAR_FORMAS_PAGAMENTO_NFCE);
    }

    private void montarListaFormasPagamentoNFCe() throws Exception {
        this.formaPagamentoNFCe = null;
        montarListaFormasPagamento();
        this.listaFormasPagamentoNFCe = montarListaSelectItemFormasPagamentoDFe(getEmpresaVO().getFormasPagamentoEmissaoNFCe());
    }

    public void removerFormaPagamentoNFSe() {
        removerFormaPagamento(this.listaFormasPagamentoNFSe);
    }

    public void gravarFormasPagamentoNFSe() {
        gravarFormasPagamento(false);
    }

    public void alterarFormaPagamentoNFSe() throws Exception {
        montarListaFormasPagamentoNFSe();
        setMsgAlert(RICHFACES_SHOW_MODAL_EDITAR_FORMAS_PAGAMENTO_NFSE);
    }

    private void montarListaFormasPagamentoNFSe() throws Exception {
        this.formaPagamentoNFSe = null;
        montarListaFormasPagamento();
        this.listaFormasPagamentoNFSe = montarListaSelectItemFormasPagamentoDFe(getEmpresaVO().getFormasPagamentoEmissaoNFSe());
    }

    private void adicionarFormaPagamento(List<SelectItem> listaFormasPagamentoDFe, TipoFormaPagto formaPagto) {
        if (formaPagto == null) {
            return;
        }

        for (SelectItem formaPagamentoSelecionada : listaFormasPagamentoDFe) {
            if (formaPagamentoSelecionada.getValue().equals(formaPagto)) {
                return;
            }
        }

        listaFormasPagamentoDFe.add(new SelectItem(formaPagto, formaPagto.getDescricao()));
    }

    private void removerFormaPagamento(List<SelectItem> listaFormasPagamentoDFe) {
        try{
            SelectItem formaPagamento = (SelectItem) context().getExternalContext().getRequestMap().get("tpFormaPagamento");
            for (SelectItem formaPagamentoSelecionada : listaFormasPagamentoDFe) {
                if (formaPagamentoSelecionada.getValue().equals(formaPagamento.getValue())) {
                    listaFormasPagamentoDFe.remove(formaPagamentoSelecionada);
                    break;
                }
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void gravarFormasPagamento(Boolean isNFCe) {
        try {
            String separador = "";
            final StringBuilder tipos = new StringBuilder();
            for (SelectItem selectItem: isNFCe ? this.listaFormasPagamentoNFCe : this.listaFormasPagamentoNFSe) {
                tipos.append(separador).append(((TipoFormaPagto) selectItem.getValue()).getSigla());
                separador = "|";
            }

            if (isNFCe) {
                this.empresaVO.setFormasPagamentoEmissaoNFCe(tipos.toString());
            } else {
                this.empresaVO.setFormasPagamentoEmissaoNFSe(tipos.toString());
            }

            setMsgAlert(isNFCe ? RICHFACES_HIDE_MODAL_EDITAR_FORMAS_PAGAMENTO_NFCE  : RICHFACES_HIDE_MODAL_EDITAR_FORMAS_PAGAMENTO_NFSE);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void montarListaFormasPagamento() throws Exception {
        String tiposFormaPagamento = getTiposFormasPagamento();
        if (tiposFormaPagamento.trim().equals("")) {
            return;
        }

        this.listaFormasPagamento.clear();
        final String[] arrayTipos = tiposFormaPagamento.replaceAll("\\\\", "").split("\\|");
        for (final String formaPagamento: arrayTipos) {
            final TipoFormaPagto tipoFormaPagto = TipoFormaPagto.getTipoFormaPagtoSigla(formaPagamento);
            if (tipoPagamentoPermiteEmissao(tipoFormaPagto)) {
                this.listaFormasPagamento.add(new SelectItem(tipoFormaPagto, tipoFormaPagto.getDescricao()));
            }
        }
    }

    private boolean tipoPagamentoPermiteEmissao(TipoFormaPagto tipoFormaPagto) {
        return tipoFormaPagto != null && tipoFormaPagto.isNotCartao() && tipoFormaPagto.isNotCreditoContaCorrente();
    }

    @SuppressWarnings("unchecked")
    private String getTiposFormasPagamento() throws Exception {
        final Set<String> listaTipoFormaPagamento = getFacade().getFormaPagamento().consultarTipoTodosEmpresa(getEmpresaVO().getCodigo());

        final StringBuilder formasPagamento = new StringBuilder();
        String separador = "";
        for (String tipoPagamento : listaTipoFormaPagamento) {
            formasPagamento.append(separador).append(tipoPagamento);
            separador = "\\|";
        }

        return formasPagamento.toString();
    }

    public List<SelectItem> getListaFormasPagamentoNFCe() {
        return listaFormasPagamentoNFCe;
    }

    public void setListaFormasPagamentoNFCe(List<SelectItem> listaFormasPagamentoNFCe) {
        this.listaFormasPagamentoNFCe = listaFormasPagamentoNFCe;
    }

    public List<Integer> getEmpresasReplicar() {
        if (empresasReplicar == null) {
            empresasReplicar = new ArrayList<Integer>();
        }
        return empresasReplicar;
    }

    public void setEmpresasReplicar(List<Integer> empresasReplicar) {
        this.empresasReplicar = empresasReplicar;
    }

    public List<SelectItem> getListaEmpresasGeral() {
        if (listaEmpresasGeral == null) {
            listaEmpresasGeral = new ArrayList<SelectItem>();
        }
        return listaEmpresasGeral;
    }

    public void setListaEmpresasGeral(List<SelectItem> listaEmpresasGeral) {
        this.listaEmpresasGeral = listaEmpresasGeral;
    }

    public List<Integer> getEmpresasReplicarContaCorrente() {
        if (empresasReplicarContaCorrente == null) {
            empresasReplicarContaCorrente = new ArrayList<Integer>();
        }
        return empresasReplicarContaCorrente;
    }

    public void setEmpresasReplicarContaCorrente(List<Integer> empresasReplicarContaCorrente) {
        this.empresasReplicarContaCorrente = empresasReplicarContaCorrente;
    }

    public String getEmailNotificacaoAutomaticaNotas() {
        return emailNotificacaoAutomaticaNotas;
    }

    public void setEmailNotificacaoAutomaticaNotas(String emailNotificacaoAutomaticaNotas) {
        this.emailNotificacaoAutomaticaNotas = emailNotificacaoAutomaticaNotas;
    }

    public List<String> getEmailsNotificacaoAutomaticaNotas() {
        return emailsNotificacaoAutomaticaNotas;
    }

    public void setEmailsNotificacaoAutomaticaNotas(List<String> emailsNotificacaoAutomaticaNotas) {
        this.emailsNotificacaoAutomaticaNotas = emailsNotificacaoAutomaticaNotas;
    }

    public void adicionarEmailNotificacaoAutomaticaNotas() {
        String emailOcorrente = getEmailNotificacaoAutomaticaNotas();
        if (StringUtils.isBlank(emailOcorrente)) {
            setMensagemID("msg_erro_emailNotificacaoNotasVazio");
            return;
        }

        if (getEmailsNotificacaoAutomaticaNotas().contains(emailOcorrente)) {
            setMensagemID("msg_erro_emailResponsavelRecorrenciaExistente");
            return;
        }

        if (!UteisEmail.getValidEmail(emailOcorrente)) {
            setMensagemID("msg_erro_emailInvalido");
            return;
        }

        if (emailsNotificacaoAutomaticaNotas.size() >= 5) {
            setMensagemID("msg_erro_excedeuLimiteEmail");
            return;
        }

        emailsNotificacaoAutomaticaNotas.add(this.emailNotificacaoAutomaticaNotas);
        this.setEmailNotificacaoAutomaticaNotas("");
        clsMessages();
    }

    public void removerEmailNotificacaoAutomaticaNotas() {
        String email = (String) context().getExternalContext().getRequestMap().get("email");
        emailsNotificacaoAutomaticaNotas.remove(email);
    }

    public List<ProdutoParceiroFidelidadeVO> getListaProdutosParceiro() {
        if (listaProdutosParceiro == null) {
            listaProdutosParceiro = new ArrayList<ProdutoParceiroFidelidadeVO>();
        }
        return listaProdutosParceiro;
    }

    public void setListaProdutosParceiro(List<ProdutoParceiroFidelidadeVO> listaProdutosParceiro) {
        this.listaProdutosParceiro = listaProdutosParceiro;
    }

    public boolean isMarcarTodosProdutosParceiro() {
        return marcarTodosProdutosParceiro;
    }

    public void setMarcarTodosProdutosParceiro(boolean marcarTodosProdutosParceiro) {
        this.marcarTodosProdutosParceiro = marcarTodosProdutosParceiro;
    }

    public void atualizarAutorizacoesCobranca() {
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Atualizar autorizações de cobrança",
                "Deseja atualizar os tipos de produtos de todas as autorizações de cobranças do tipo 'Produtos especificados'?",
                this, "acaoAtualizarAutorizacoesCobranca", "", "", "", "form");
    }

    public void acaoAtualizarAutorizacoesCobranca() {
        try {
            getFacade().getAutorizacaoCobrancaCliente().atualizarAutorizacoesProdutosEspecificados(getEmpresaVO());
        } catch (Exception e) {
            montarErro(e.getMessage());
        }
    }

    public void sincronizarComFinanceiro() {
        try {
            limparMsg();
            if (!UteisValidacao.emptyNumber(empresaVO.getCodEmpresaFinanceiro())) {
                if (!getCodigoFinanceiro().equals(empresaVO.getCodEmpresaFinanceiro()) || empresaVO.getSincronizacaoFinanceiro() == null) {
                    JSONObject empresaFinanceiro = new JSONObject();
                    empresaFinanceiro.put("codFinanceiro", getEmpresaVO().getCodEmpresaFinanceiro());
                    empresaFinanceiro.put("chave", getKey());
                    empresaFinanceiro.put("codigoZW", getEmpresaVO().getCodigo());
                    empresaFinanceiro.put("nome", getEmpresaVO().getNome());
                    empresaFinanceiro.put("razaoSocialZW", getEmpresaVO().getRazaoSocial());
                    empresaFinanceiro.put("cnpjZW", getEmpresaVO().getCNPJ());
                    empresaFinanceiro.put("ativaZW", getEmpresaVO().isAtiva());
                    empresaFinanceiro.put("dataExpiracaoZW", getEmpresaVO().getDataExpiracao());
                    empresaFinanceiro.put("dataSuspensaoZW", getEmpresaVO().getDataSuspensao());
                    empresaFinanceiro.put("dataExpiracaoOutrosZW", getEmpresaVO().getDataExpiracaoOutros());
                    empresaFinanceiro.put("motivoExpiracaoOutrosZW", getEmpresaVO().getMotivoExpiracaoOutros());
                    empresaFinanceiro.put("codigoRedeZW", getEmpresaVO().getCodigoRede());

                    HttpPost post = new HttpPost(Uteis.getUrlOamd() + "/prest/empresaFinanceiro/v2/atualizarDadosZW");
                    HttpClient httpClient = ExecuteRequestHttpService.createConnector();

                    StringEntity entity = new StringEntity(empresaFinanceiro.toString(), "UTF8");
                    entity.setContentType(new BasicHeader(HTTP.CONTENT_TYPE, "application/json"));
                    post.setEntity(entity);

                    HttpResponse response = httpClient.execute(post);

                    if (response.getStatusLine().getStatusCode() != 200) {
                        throw new Exception(EntityUtils.toString(response.getEntity()));
                    } else {
                        String resposta = EntityUtils.toString(response.getEntity());
                        JSONObject jsonObject = new JSONObject(resposta);

                        if (jsonObject.has("erro")) {
                            try {
                                montarErro(jsonObject.getString("erro"));
                            }catch (Exception e) {
                                montarErro("Não foi possível sincronizar com o servidor");
                            }
                            return;
                        }


                        empresaVO.setSincronizacaoFinanceiro(Calendario.hoje());
                        getFacade().getEmpresa().alterarDataSincronizacaoFinanceiro(empresaVO);
                        setCodigoFinanceiro(empresaVO.getCodEmpresaFinanceiro());
                    }
                } else {
                    montarAviso("Modifique o ID Favorecido antes de sincronizar");
                }
            } else {
                montarAviso("Obrigatório inserir um ID Favorecido");
            }
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public List<SelectItem> getListaSelectItemConvenioCobrancaVerificacaoCartao() {
        List<SelectItem> lista = new ArrayList<>();
        for (ConvenioCobrancaVO obj : this.getListaConvenioCobrancaVerificacaoCartao()) {
            lista.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public List<ConvenioCobrancaVO> getListaConvenioCobrancaVerificacaoCartao() {
        if (listaConvenioCobrancaVerificacaoCartao == null) {
            listaConvenioCobrancaVerificacaoCartao = new ArrayList<>();
        }
        return listaConvenioCobrancaVerificacaoCartao;
    }

    public void setListaConvenioCobrancaVerificacaoCartao(List<ConvenioCobrancaVO> listaConvenioCobrancaVerificacaoCartao) {
        this.listaConvenioCobrancaVerificacaoCartao = listaConvenioCobrancaVerificacaoCartao;
    }

    public List<ConvenioCobrancaVO> getListaConvenioCobrancaVO() {
        if (listaConvenioCobrancaVO == null) {
            listaConvenioCobrancaVO = new ArrayList<>();
        }
        return listaConvenioCobrancaVO;
    }

    public void setListaConvenioCobrancaVO(List<ConvenioCobrancaVO> listaConvenioCobrancaVO) {
        this.listaConvenioCobrancaVO = listaConvenioCobrancaVO;
    }

    public ConvenioCobrancaVO getConvenioCobrancaPagoLivre() {
        return convenioCobrancaPagoLivre;
    }

    public void setConvenioCobrancaPagoLivre(ConvenioCobrancaVO convenioCobrancaPagoLivre) {
        this.convenioCobrancaPagoLivre = convenioCobrancaPagoLivre;
    }

    private void limparInformacoesPagoLivre() {
        this.setCnpjPagoLivre("");
        this.setEmailPagoLivre("");
        this.setNomeEmpresaPagoLivre("");
        this.setDddTelefonePagoLivre(null);
        this.setTelefonePagoLivre("");
        this.setContaCorrentePagoLivre(0);
        this.setMerchantIdPagoLivre("");
        this.setMerchantPagoLivreDto(null);
        this.setEmailUserPagoLivre("");
        this.setNomeUserPagoLivre("");
        this.setAmbientePagoLivre(AmbienteEnum.PRODUCAO);
        this.setEditandoMerchantPagoLivre(false);
        this.setEditandoContaBancariaPagoLivre(false);
    }

    private void limparInformacoesFacilitePay() {
        this.setCnpjFacilitePay("");
        this.setEmailFacilitePay("");
        this.setNomeEmpresaFacilitePay("");
        this.setDddTelefoneFacilitePay(null);
        this.setTelefoneFacilitePay("");
        this.setContaCorrenteFacilitePay(0);
        this.setMerchantIdFacilitePay("");
        this.setMerchantFacilitePayDto(null);
        this.setEmailUserFacilitePay("");
        this.setNomeUserFacilitePay("");
        this.setAmbienteFacilitePay(AmbienteEnum.PRODUCAO);
        this.setEditandoMerchantFacilitePay(false);
        this.setEditandoContaBancariaFacilitePay(false);
    }

    private void preencherInformacoesPagoLivre(EmpresaVO empresaVO, TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum) {

        String tel1 = getEmpresaVO().getTelComercial1().replaceAll("[^0-9]", "");
        String tel2 = getEmpresaVO().getTelComercial2().replaceAll("[^0-9]", "");
        String tel3 = getEmpresaVO().getTelComercial3().replaceAll("[^0-9]", "");

        if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE)) {
            this.setAmbientePagoLivre(AmbienteEnum.PRODUCAO);
            this.setCnpjPagoLivre(empresaVO.getCNPJ());
            this.setEmailUserPagoLivre(empresaVO.getEmail().toLowerCase());
            this.setNomeEmpresaPagoLivre(empresaVO.getNome());

            this.setContaCorrentePagoLivre(0);
            this.setMerchantIdPagoLivre("");
            this.setMerchantPagoLivreDto(null);
            this.setNomeUserPagoLivre("");


            if (!UteisValidacao.emptyString(tel1)) {
                try {
                    if (tel1.length() >= 10) {
                        this.setDddTelefonePagoLivre(Integer.parseInt(tel1.substring(0, 2)));
                        this.setTelefonePagoLivre(tel1.substring(2));
                    }
                } catch (Exception ignored) {
                }
            }

            if (UteisValidacao.emptyString(this.getTelefonePagoLivre()) &&
                    !UteisValidacao.emptyString(tel2)) {
                try {
                    if (tel2.length() >= 10) {
                        this.setDddTelefonePagoLivre(Integer.parseInt(tel2.substring(0, 2)));
                        this.setTelefonePagoLivre(tel2.substring(2));
                    }
                } catch (Exception ignored) {
                }
            }

            if (UteisValidacao.emptyString(this.getTelefonePagoLivre()) &&
                    !UteisValidacao.emptyString(tel3)) {
                try {
                    if (tel3.length() >= 10) {
                        this.setDddTelefonePagoLivre(Integer.parseInt(tel3.substring(0, 2)));
                        this.setTelefonePagoLivre(tel3.substring(2));
                    }
                } catch (Exception ignored) {
                }
            }

        } else {

            this.setAmbienteFacilitePay(AmbienteEnum.PRODUCAO);
            this.setCnpjFacilitePay(empresaVO.getCNPJ());
            this.setEmailUserFacilitePay(empresaVO.getEmail().toLowerCase());
            this.setNomeEmpresaFacilitePay(empresaVO.getNome());

            this.setContaCorrenteFacilitePay(0);
            this.setMerchantIdFacilitePay("");
            this.setMerchantFacilitePayDto(null);
            this.setNomeUserFacilitePay("");


            if (!UteisValidacao.emptyString(tel1)) {
                try {
                    if (tel1.length() >= 10) {
                        this.setDddTelefoneFacilitePay(Integer.parseInt(tel1.substring(0, 2)));
                        this.setTelefoneFacilitePay(tel1.substring(2));
                    }
                } catch (Exception ignored) {
                }
            }

            if (UteisValidacao.emptyString(this.getTelefoneFacilitePay()) &&
                    !UteisValidacao.emptyString(tel2)) {
                try {
                    if (tel2.length() >= 10) {
                        this.setDddTelefoneFacilitePay(Integer.parseInt(tel2.substring(0, 2)));
                        this.setTelefoneFacilitePay(tel2.substring(2));
                    }
                } catch (Exception ignored) {
                }
            }

            if (UteisValidacao.emptyString(this.getTelefoneFacilitePay()) &&
                    !UteisValidacao.emptyString(tel3)) {
                try {
                    if (tel3.length() >= 10) {
                        this.setDddTelefoneFacilitePay(Integer.parseInt(tel3.substring(0, 2)));
                        this.setTelefoneFacilitePay(tel3.substring(2));
                    }
                } catch (Exception ignored) {
                }
            }
        }

    }

    public String getCnpjPagoLivre() {
        if (cnpjPagoLivre == null) {
            cnpjPagoLivre = "";
        }
        return cnpjPagoLivre;
    }

    public void setCnpjPagoLivre(String cnpjPagoLivre) {
        this.cnpjPagoLivre = cnpjPagoLivre;
    }

    public String getEmailPagoLivre() {
        if (UteisValidacao.emptyString(getEmailUserPagoLivre())) {
            return "informe o email do usuário do portal primeiro";
        }
        try {
            String inicioEmail = getKey() + "-" + getEmpresaVO().getCodigo();
            return inicioEmail + "@fypay.com.br";
        } catch (Exception ex) {
            return "informe o email do usuário do portal primeiro";
        }
    }


    public void setEmailPagoLivre(String emailPagoLivre) {
        this.emailPagoLivre = emailPagoLivre;
    }

    public String getNomeEmpresaPagoLivre() {
        if (nomeEmpresaPagoLivre == null) {
            nomeEmpresaPagoLivre = "";
        }
        return nomeEmpresaPagoLivre;
    }

    public void setNomeEmpresaPagoLivre(String nomeEmpresaPagoLivre) {
        this.nomeEmpresaPagoLivre = nomeEmpresaPagoLivre;
    }

    public Integer getDddTelefonePagoLivre() {
        return dddTelefonePagoLivre;
    }

    public void setDddTelefonePagoLivre(Integer dddTelefonePagoLivre) {
        this.dddTelefonePagoLivre = dddTelefonePagoLivre;
    }

    public String getTelefonePagoLivre() {
        if (telefonePagoLivre == null) {
            telefonePagoLivre = "";
        }
        return telefonePagoLivre;
    }

    public void setTelefonePagoLivre(String telefonePagoLivre) {
        this.telefonePagoLivre = telefonePagoLivre;
    }

    public String getMerchantIdPagoLivre() {
        if (merchantIdPagoLivre == null) {
            merchantIdPagoLivre = "";
        }
        return merchantIdPagoLivre;
    }

    public void setMerchantIdPagoLivre(String merchantIdPagoLivre) {
        this.merchantIdPagoLivre = merchantIdPagoLivre;
    }

    public MerchantPagoLivreDto getMerchantPagoLivreDto() {
        return merchantPagoLivreDto;
    }

    public void setMerchantPagoLivreDto(MerchantPagoLivreDto merchantPagoLivreDto) {
        this.merchantPagoLivreDto = merchantPagoLivreDto;
    }

    public String getEmailUserPagoLivre() {
        if (emailUserPagoLivre == null) {
            emailUserPagoLivre = "";
        }
        return emailUserPagoLivre;
    }

    public void setEmailUserPagoLivre(String emailUserPagoLivre) {
        this.emailUserPagoLivre = emailUserPagoLivre;
    }

    public String getNomeUserPagoLivre() {
        if (nomeUserPagoLivre == null) {
            nomeUserPagoLivre = "";
        }
        return nomeUserPagoLivre;
    }

    public void setNomeUserPagoLivre(String nomeUserPagoLivre) {
        this.nomeUserPagoLivre = nomeUserPagoLivre;
    }

    public AmbienteEnum getAmbientePagoLivre() {
        if (ambientePagoLivre == null) {
            ambientePagoLivre = AmbienteEnum.PRODUCAO;
        }
        return ambientePagoLivre;
    }

    public void setAmbientePagoLivre(AmbienteEnum ambientePagoLivre) {
        this.ambientePagoLivre = ambientePagoLivre;
    }

    public AmbienteEnum getAmbienteAsaas() {
        if (ambienteAsaas == null) {
            ambienteAsaas = AmbienteEnum.PRODUCAO;
        }
        return ambienteAsaas;
    }

    public void setAmbienteAsaas(AmbienteEnum ambienteAsaas) {
        this.ambienteAsaas = ambienteAsaas;
    }

    public Integer getContaCorrentePagoLivre() {
        if (contaCorrentePagoLivre == null) {
            contaCorrentePagoLivre = 0;
        }
        return contaCorrentePagoLivre;
    }

    public void setContaCorrentePagoLivre(Integer contaCorrentePagoLivre) {
        this.contaCorrentePagoLivre = contaCorrentePagoLivre;
    }

    public List<SelectItem> getPinPadStoneConnect() {
        List<SelectItem> lista = new ArrayList<>();
        for (PinPadVO obj : this.getListaPinPad()) {
            if (obj.getPinpadEnum() != null && obj.isStoneConnect()) {
                lista.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public List<PinPadVO> getListaPinPad() {
        if (listaPinPad == null) {
            listaPinPad = new ArrayList<>();
        }
        return listaPinPad;
    }

    public void setListaPinPad(List<PinPadVO> listaPinPad) {
        this.listaPinPad = listaPinPad;
    }

    public String getCallBackHubsPost() {
        return callBackHubsPost;
    }

    public void setCallBackHubsPost(String callBackHubsPost) {
        this.callBackHubsPost = callBackHubsPost;
    }

    public String getUrlCallBackHubsPost() {
        return urlCallBackHubsPost;
    }

    public void setUrlCallBackHubsPost(String urlCallBackHubsPost) {
        this.urlCallBackHubsPost = urlCallBackHubsPost;
    }

    public String getShowCallBackHubsPost() {
        return showCallBackHubsPost;
    }

    public void setShowCallBackHubsPost(String showCallBackHubsPost) {
        this.showCallBackHubsPost = showCallBackHubsPost;
    }

    public static class ConfigProdutoZillyon{
        List<ProdutoVO> produtos;
        //UTILIZADOS PARA NFC-e
        private String cfop;
        private String codigoListaServico;
        private String codigoTributacaoMunicipio;
        private double aliquotaPIS = 0.0;
        private double aliquotaCOFINS = 0.0;
        private double aliquotaISSQN = 0.0;
        private String ncmNFCe;

        public ConfigProdutoZillyon(List<ProdutoVO> produtos) {
            this.produtos = produtos;
        }

        public void alterarConfigProduto() throws Exception {
            for (ProdutoVO p : getProdutos()){
                p.setAliquotaCOFINS(getAliquotaCOFINS());
                p.setAliquotaISSQN(getAliquotaISSQN());
                p.setAliquotaPIS(getAliquotaPIS());
                p.setCfop(getCfop());
                p.setCodigoListaServico(getCodigoListaServico());
                p.setCodigoTributacaoMunicipio(getCodigoTributacaoMunicipio());
                p.setNcmNFCe(getNcmNFCe());

                getFacade().getProduto().alterarConfigImpostos(p);
            }
        }

        public List<ProdutoVO> getProdutos() {
            if(null == produtos){
                produtos = new ArrayList<ProdutoVO>();
            }
            return produtos;
        }

        public void setProdutos(List<ProdutoVO> produtos) {
            this.produtos = produtos;
        }

        public String getCfop() {
            return cfop;
        }

        public void setCfop(String cfop) {
            this.cfop = cfop;
        }

        public String getCodigoListaServico() {
            return codigoListaServico;
        }

        public void setCodigoListaServico(String codigoListaServico) {
            this.codigoListaServico = codigoListaServico;
        }

        public String getCodigoTributacaoMunicipio() {
            return codigoTributacaoMunicipio;
        }

        public void setCodigoTributacaoMunicipio(String codigoTributacaoMunicipio) {
            this.codigoTributacaoMunicipio = codigoTributacaoMunicipio;
        }

        public double getAliquotaPIS() {
            return aliquotaPIS;
        }

        public void setAliquotaPIS(double aliquotaPIS) {
            this.aliquotaPIS = aliquotaPIS;
        }

        public double getAliquotaCOFINS() {
            return aliquotaCOFINS;
        }

        public void setAliquotaCOFINS(double aliquotaCOFINS) {
            this.aliquotaCOFINS = aliquotaCOFINS;
        }

        public double getAliquotaISSQN() {
            return aliquotaISSQN;
        }

        public void setAliquotaISSQN(double aliquotaISSQN) {
            this.aliquotaISSQN = aliquotaISSQN;
        }

        public String getNcmNFCe() {
            return ncmNFCe;
        }

        public void setNcmNFCe(String ncmNFCe) {
            this.ncmNFCe = ncmNFCe;
        }
    }

    public List<SelectItem> getListaProdutos() {
        if (listaProdutos == null) {
            listaProdutos = new ArrayList<SelectItem>();
        }
        return listaProdutos;
    }

    public void setListaProdutos(List<SelectItem> listaProdutos) {
        this.listaProdutos = listaProdutos;
    }

    public void montarListaSelectItemProdutos(){
        try {
            List<ProdutoVO> resultadoConsulta = getFacade().getProduto().consultarPorTodosProdutosComLimit(false, getEmpresaVO().getCodigo(), 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            listaProdutos = new ArrayList<SelectItem>();
            listaProdutos.add(new SelectItem(0, ""));
            for (ProdutoVO obj : resultadoConsulta) {
                listaProdutos.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        }catch (Exception e){
            montarErro(e);
        }
    }

    public void montarListaSelectItemProdutoDayUse() {
        try {
            List<ProdutoVO> resultadoConsulta = getFacade().getProduto().consultarProdutosPorTipoProduto("DI", false, Uteis.NIVELMONTARDADOS_MINIMOS);
            listaSelectItemProdutoDayUse = new ArrayList<>();
            listaSelectItemProdutoDayUse.add(new SelectItem(0, ""));
            for (ProdutoVO obj : resultadoConsulta) {
                listaSelectItemProdutoDayUse.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void montarListaSelectItemModalidadeDayUse() {
        try {
            List<ModalidadeVO> resultadoConsulta = getFacade().getModalidade().consultarPorNomeModalidadeSemDesativado("", getEmpresaVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
            listaSelectItemModalidadeDayUse = new ArrayList<>();
            listaSelectItemModalidadeDayUse.add(new SelectItem(0, ""));
            for (ModalidadeVO obj : resultadoConsulta) {
                listaSelectItemModalidadeDayUse.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void montarListaSelectItemTipoPlano() {
        try {
            List<PlanoTipoVO> resultadoConsulta = getFacade().getPlanoTipo().consultar(Uteis.NIVELMONTARDADOS_MINIMOS);
            listaSelectItemTipoPlano = new ArrayList<>();
            listaSelectItemTipoPlano.add(new SelectItem(0, ""));
            for (PlanoTipoVO obj : resultadoConsulta) {
                listaSelectItemTipoPlano.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public boolean isCheckNotaAutoPgRetroativo() {
        int tipoGestaoNFSe = empresaVO.getTipoGestaoNFSe();
        int codigoFaturamentoRecebido = TipoRelatorioDF.FATURAMENTO_DE_CAIXA.getCodigo();
        checkNotaAutoPgRetroativo = codigoFaturamentoRecebido == tipoGestaoNFSe;

        if (!checkNotaAutoPgRetroativo) {
            empresaVO.setNotasAutoPgRetroativo(true);
        }

        return checkNotaAutoPgRetroativo;
    }

    public void setCheckNotaAutoPgRetroativo(boolean checkNotaAutoPgRetroativo) {
        this.checkNotaAutoPgRetroativo = checkNotaAutoPgRetroativo;
    }

    public List<SelectItem> getListaFormasPagamentoNFSe() {
        return listaFormasPagamentoNFSe;
    }

    public void setListaFormasPagamentoNFSe(List<SelectItem> listaFormasPagamentoNFSe) {
        this.listaFormasPagamentoNFSe = listaFormasPagamentoNFSe;
    }

    public void replicarConfiguracaoRetentativa() {
        try {
            setMsgAlert("");
            limparMsg();

            if (UteisValidacao.emptyList(getEmpresasReplicar())) {
                throw new Exception("Nenhuma empresa selecionada!");
            }

            List<ConfiguracaoReenvioMovParcelaEmpresaVO> configuracao = getEmpresaVO().getConfiguracaoReenvioMovParcelaEmpresaVOS();
            for (Integer empresa : getEmpresasReplicar()) {
                List<ConfiguracaoReenvioMovParcelaEmpresaVO> novasConfiguracoes = new ArrayList<ConfiguracaoReenvioMovParcelaEmpresaVO>();
                for (ConfiguracaoReenvioMovParcelaEmpresaVO conf : configuracao) {
                    ConfiguracaoReenvioMovParcelaEmpresaVO novaCon = new ConfiguracaoReenvioMovParcelaEmpresaVO();
                    novaCon.setPosicao(conf.getPosicao());
                    novaCon.setTentativasRealizar(conf.getTentativasRealizar());
                    novaCon.setConvenioCobrancaVO(conf.getConvenioCobrancaVO());
                    novaCon.getEmpresaVO().setCodigo(empresa);
                    novasConfiguracoes.add(novaCon);
                    getFacade().getConvenioCobrancaEmpresa().incluirVinculoSeNaoExistir(conf.getConvenioCobrancaVO().getCodigo(), empresa);
                }
                getFacade().getEmpresa().alterarConfiguracoesReenvioAutomaticoRemessa( true, getEmpresaVO().getQtdExecucoesRetentativa(), empresa);
                getFacade().getConfiguracaoReenvioMovParcelaEmpresa().alterarConfiguracaoReenvioMovParcelaEmpresa(empresa, novasConfiguracoes);
            }
            setMsgAlert("Richfaces.hideModalPanel('modalReplicarRetentativa')");
            montarSucessoGrowl("Configuraçães foram replicadas com sucesso!");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void preparaReplicarConfiguracaoRetentativa() throws Exception {
        setMsgAlert("");
        limparMsg();
        setEmpresasReplicar(new ArrayList<Integer>());
        setListaEmpresasGeral(new ArrayList<SelectItem>());
        List<EmpresaVO> listaEmpresas = getFacade().getEmpresa().consultarTodas(null,Uteis.NIVELMONTARDADOS_MINIMOS);
        Ordenacao.ordenarLista(listaEmpresas, "nome");
        for (EmpresaVO empresaVO: listaEmpresas) {
            getListaEmpresasGeral().add(new SelectItem(empresaVO.getCodigo(), empresaVO.getNome()));
        }
    }

    public void atualizarAutorizacaoCobrancaCliente() {
        try {
            setMsgAlert("");
            limparMsg();

            List<EmpresaVO> listaEmpresas = getFacade().getEmpresa().consultarTodas(null,Uteis.NIVELMONTARDADOS_MINIMOS);
            for (EmpresaVO empresaVO: listaEmpresas) {
                getFacade().getAutorizacaoCobrancaCliente().atualizarAutorizacaoCobrancaClienteCartaoCreditoUsandoConfigReenvioCobrancaAutomatica( empresaVO.getCodigo(), null);
            }
            setMsgAlert("Richfaces.hideModalPanel('modalAtualizarAutorizacao')");
            montarSucessoGrowl("Autorizacao de cobrança dos alunos foram atualizadas com sucesso!");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void preparaReplicarConfiguracaoContaCorrente() throws Exception {
        setMsgAlert("");
        limparMsg();
        setEmpresasReplicarContaCorrente(new ArrayList<Integer>());
        setListaEmpresasGeral(new ArrayList<SelectItem>());
        List<EmpresaVO> listaEmpresas = getFacade().getEmpresa().consultarTodas(null,Uteis.NIVELMONTARDADOS_MINIMOS);
        Ordenacao.ordenarLista(listaEmpresas, "nome");
        for (EmpresaVO empresaVO: listaEmpresas) {
            getListaEmpresasGeral().add(new SelectItem(empresaVO.getCodigo(), empresaVO.getNome()));
        }
    }

    public void replicarConfiguracaoContaCorrente() {
        try {
            setMsgAlert("");
            limparMsg();

            if (UteisValidacao.emptyList(getEmpresasReplicarContaCorrente())) {
                throw new Exception("Nenhuma empresa selecionada!");
            }

            for (Integer empresa : getEmpresasReplicarContaCorrente()) {
                List<ContaCorrenteEmpresaVO> novasConfiguracoes = new ArrayList<ContaCorrenteEmpresaVO>();
                for (ContaCorrenteEmpresaVO conf : getEmpresaVO().getContaCorrenteEmpresaVOs()) {
                    ContaCorrenteEmpresaVO novaCon = new ContaCorrenteEmpresaVO();
                    novaCon.setContaCorrente(conf.getContaCorrente());
                    novaCon.setEmpresa(empresa);
                    novasConfiguracoes.add(novaCon);
                }
                getFacade().getContaCorrenteEmpresa().alterarContaCorrenteEmpresas(empresa, novasConfiguracoes);
            }
            setMsgAlert("Richfaces.hideModalPanel('modalReplicarContaCorrente')");
            montarSucessoGrowl("Configuraçães de conta corrente foram replicadas com sucesso!");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public ParceiroFidelidadeVO getParceiroFidelidade() {
        return getEmpresaVO().getParceiroFidelidade();
    }

    public void adicionarTabelaParceiro() {
        limparMsg();
        getParceiroFidelidade().addItem(new TabelaParceiroFidelidadeVO(
                String.format("TABELA %s %s", getParceiroFidelidade().getTipoParceiro().getNome(),
                        getParceiroFidelidade().getItens().size() + 1),
                getParceiroFidelidade()));
    }

    public void excluirTabelaParceiro() {
        limparMsg();
        TabelaParceiroFidelidadeVO tblParceiro = (TabelaParceiroFidelidadeVO) request().getAttribute("tblParceiro");
        if (tblParceiro != null) {
            try {
                if (!UteisValidacao.emptyNumber(tblParceiro.getCodigo())) {
                    getFacade().getTabelaParceiroFidelidade().excluir(tblParceiro);
                }
                getParceiroFidelidade().removeItem(tblParceiro);
            } catch (Exception ex) {
                setMensagemDetalhada(ex);
                Logger.getLogger(EmpresaControle.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    public void adicionarItemTabelaParceiro() {
        try {
            limparMsg();
            setMensagemDetalhada("");
            TabelaParceiroFidelidadeVO tblParceiro = (TabelaParceiroFidelidadeVO) request().getAttribute("tblParceiro");
            tblParceiro.addItem(tblParceiro.getItemAdicionar());
            tblParceiro.novoItemAdicionar(0.0, 0.0, 0.0);
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
            Logger.getLogger(EmpresaControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void novoItemTabelaParceiro() {
        try {
            limparMsg();
            setMensagemDetalhada("");
            TabelaParceiroFidelidadeVO tblParceiro = (TabelaParceiroFidelidadeVO) request().getAttribute("tblParceiro");
            tblParceiro.novoItemAdicionar(0.0, 0.0, 0.0);
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
            Logger.getLogger(EmpresaControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void editarItemTabelaParceiro() {
        limparMsg();
        setMensagemDetalhada("");
        TabelaParceiroFidelidadeVO tblParceiro = (TabelaParceiroFidelidadeVO) request().getAttribute("tblParceiro");
        tblParceiro.setItemAdicionar((TabelaParceiroFidelidadeItemVO) request().getAttribute("item"));
    }

    public void removerItem() {
        limparMsg();
        setMensagemDetalhada("");
        TabelaParceiroFidelidadeVO tblParceiro = (TabelaParceiroFidelidadeVO) request().getAttribute("tblParceiro");
        TabelaParceiroFidelidadeItemVO item = (TabelaParceiroFidelidadeItemVO) request().getAttribute("item");
        if (tblParceiro != null) {
            try {
                getFacade().getTabelaParceiroFidelidadeItem().excluir(item);
                tblParceiro.removeItem(item);
            } catch (Exception ex) {
                setMensagemDetalhada(ex);
                Logger.getLogger(EmpresaControle.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    public void removerProduto() {
        limparMsg();
        ProdutoParceiroFidelidadeVO produto = (ProdutoParceiroFidelidadeVO) request().getAttribute("produto");
        if (produto != null) {
            try {
                if (!UteisValidacao.emptyNumber(produto.getCodigo())) {
                    getFacade().getProdutoParceiroFidelidade().excluir(produto);
                }
                getParceiroFidelidade().removeProduto(produto);
            } catch (Exception ex) {
                setMensagemDetalhada(ex);
                Logger.getLogger(EmpresaControle.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    public void adicionarProdutoParceiro() {
        limparMsg();
        getParceiroFidelidade().addProduto(getParceiroFidelidade().getProdutoAdicionar());
        novoProdutoParceiro();
    }

    public void novoProdutoParceiro() {
        limparMsg();
        getParceiroFidelidade().setProdutoAdicionar(new ProdutoParceiroFidelidadeVO(getParceiroFidelidade()));
    }

    public void consultarProdutosParceiroFidelidade() {
        try {
            limparMsg();
            setMsgAlert("");
            setMarcarTodosProdutosParceiro(false);
            setListaProdutosParceiro(new ArrayList<ProdutoParceiroFidelidadeVO>());

            ParceiroFidelidadeAPIDotzImpl parceiroFidelidadeAPIDotz = new ParceiroFidelidadeAPIDotzImpl(getParceiroFidelidade());
            List<ProdutoParceiroFidelidadeVO> listaProdutos = parceiroFidelidadeAPIDotz.consultarProdutos(500000);
            if (UteisValidacao.emptyList(listaProdutos)) {
                throw new Exception("Nenhum produto encontrado no parceiro " + getParceiroFidelidade().getTipoParceiro().getNome() + "!");
            }

            for (ProdutoParceiroFidelidadeVO prod : listaProdutos) {
                ProdutoParceiroFidelidadeVO produtoParceiroFidelidadeVO = getFacade().getProdutoParceiroFidelidade().consultarPorParceiroPontosCodigoExterno(getParceiroFidelidade().getCodigo(), prod.getPontos(), "", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!UteisValidacao.emptyNumber(produtoParceiroFidelidadeVO.getCodigo())) {
                    prod.setValor(produtoParceiroFidelidadeVO.getValor());
                }
            }

            Ordenacao.ordenarLista(listaProdutos, "pontos");
            setListaProdutosParceiro(listaProdutos);
            setMsgAlert("Richfaces.showModalPanel('modalProdutoParceiro');");
        } catch (Exception ex) {
            setMsgAlert("");
            montarErro(ex);
        }
    }

    public void adicionarProdutosParceiroFidelidade() {
        try {
            limparMsg();
            setMsgAlert("");

            boolean adicionado = false;
            for (ProdutoParceiroFidelidadeVO prod : getListaProdutosParceiro()) {
                if (prod.isSelecionado()) {
                    getParceiroFidelidade().addProduto(prod);
                    adicionado = true;
                }
            }

            if (!adicionado) {
                throw new Exception("Nenhum produto selecionado!");
            }

            setMsgAlert("Richfaces.hideModalPanel('modalProdutoParceiro');");
            montarSucessoGrowl("Produtos foram adicionados com sucesso.");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void acaoMarcarTodosProdutosParceiroFidelidade() {
        try {
            limparMsg();
            setMsgAlert("");
            for (ProdutoParceiroFidelidadeVO prod : getListaProdutosParceiro()) {
                prod.setSelecionado(isMarcarTodosProdutosParceiro());
            }
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public List<ItemCobrancaPactoJSON> getItensCobrancaPacto() {
        if (itensCobrancaPacto == null) {
            itensCobrancaPacto = new ArrayList<ItemCobrancaPactoJSON>();
        }
        return itensCobrancaPacto;
    }

    public void setItensCobrancaPacto(List<ItemCobrancaPactoJSON> itensCobrancaPacto) {
        this.itensCobrancaPacto = itensCobrancaPacto;
    }

    public void abrirListaTotalItensCobranca() {
        try {
            limparMsg();
            setMsgAlert("");
            setItensCobrancaPacto(new ArrayList<ItemCobrancaPactoJSON>());
            List<ItemCobrancaPactoJSON> lista = new ArrayList<ItemCobrancaPactoJSON>();
            lista.addAll(getFacade().getCreditoDCC().consultarItensCobrancaPactoRemessasItem(getEmpresaVO().getCodigo(), getEmpresaVO().getTipoCobrancaPacto(), getMesReferenciaConsultaCreditoPacto()));
            lista.addAll(getFacade().getCreditoDCC().consultarItensCobrancaPactoTransacao(getEmpresaVO().getCodigo(), getEmpresaVO().getTipoCobrancaPacto(), getMesReferenciaConsultaCreditoPacto()));
            lista.addAll(getFacade().getCreditoDCC().consultarItensCobrancaPactoPix(getEmpresaVO().getCodigo(), getEmpresaVO().getTipoCobrancaPacto(), getMesReferenciaConsultaCreditoPacto()));
            setItensCobrancaPacto(lista);
            setMsgAlert("Richfaces.showModalPanel('modalItensCobranca');");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void abrirListaRemessaItem() {
        try {
            limparMsg();
            setMsgAlert("");
            setItensCobrancaPacto(new ArrayList<ItemCobrancaPactoJSON>());
            setItensCobrancaPacto(getFacade().getCreditoDCC().consultarItensCobrancaPactoRemessasItem(getEmpresaVO().getCodigo(), getEmpresaVO().getTipoCobrancaPacto(), getMesReferenciaConsultaCreditoPacto()));
            setMsgAlert("Richfaces.showModalPanel('modalItensCobranca');");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void abrirListaTransacao() {
        try {
            limparMsg();
            setMsgAlert("");
            setItensCobrancaPacto(new ArrayList<ItemCobrancaPactoJSON>());
            setItensCobrancaPacto(getFacade().getCreditoDCC().consultarItensCobrancaPactoTransacao(getEmpresaVO().getCodigo(), getEmpresaVO().getTipoCobrancaPacto(), getMesReferenciaConsultaCreditoPacto()));
            setMsgAlert("Richfaces.showModalPanel('modalItensCobranca');");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void abrirListaPix() {
        try {
            limparMsg();
            setMsgAlert("");
            setItensCobrancaPacto(new ArrayList<ItemCobrancaPactoJSON>());
            setItensCobrancaPacto(getFacade().getCreditoDCC().consultarItensCobrancaPactoPix(getEmpresaVO().getCodigo(), getEmpresaVO().getTipoCobrancaPacto(), getMesReferenciaConsultaCreditoPacto()));
            setMsgAlert("Richfaces.showModalPanel('modalItensCobranca');");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void abrirListaLogCobranca() {
        try {
            limparMsg();
            setMsgAlert("");
            setItensCobrancaPacto(new ArrayList<ItemCobrancaPactoJSON>());
            LogCobrancaPactoVO logCobrancaPactoVO = (LogCobrancaPactoVO) request().getAttribute("logCobranca");
            setItensCobrancaPacto(logCobrancaPactoVO.getListaItens());
            setMsgAlert("Richfaces.showModalPanel('modalItensCobranca');");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void processarConciliadora() {
        try {
            limparMsg();
            setMsgAlert("");

            if (UteisValidacao.emptyNumber(getReciboPagamentoConciliadora())) {

                if (getDataInicialConciliadora() == null) {
                    throw new Exception("Informe uma data inicial.");
                }

                if (getDataFinalConciliadora() == null) {
                    throw new Exception("Informe uma data final.");
                }

                if (Calendario.maior(getDataInicialConciliadora(), getDataFinalConciliadora())) {
                    throw new Exception("A data inicial deve ser inferior a data final.");
                }

                // Limitar periodo entre as datas (inicial e final). Nao pode ser maior que 31 dias
                int dias = Calendario.diferencaEmDias(getDataInicialConciliadora(), getDataFinalConciliadora());
                if (dias > 31) {
                    throw new Exception("O período não pode superior a 31 dias.");
                }

            } else {

                setDataInicialConciliadora(null);
                setDataFinalConciliadora(null);
            }

            ConciliadoraServiceImpl conciliadoraService = new ConciliadoraServiceImpl(getFacade().getContrato().getCon());
            conciliadoraService.processarConciliadora(getEmpresaVO().getCodigo(), getReciboPagamentoConciliadora(), getDataInicialConciliadora(), getDataFinalConciliadora(), true, true);
            montarSucessoGrowl("Processo finalizado!");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void estornarReciboConciliadora() {
        try {
            limparMsg();
            setMsgAlert("");

            if (UteisValidacao.emptyNumber(getReciboPagamentoConciliadora())) {
                throw new Exception("Informe um recibo.");
            }

            ConciliadoraServiceImpl conciliadoraService = new ConciliadoraServiceImpl(getFacade().getContrato().getCon());
            conciliadoraService.estornarReciboConciliadora(getReciboPagamentoConciliadora());
            montarSucessoGrowl("Processo finalizado!");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public boolean isApresentarReprocessarConciliadora() {
        try {

            if (!getEmpresaVO().isUsarConciliadora()) {
                return false;
            }

            if (getUsuarioLogado().getUsuarioAdminPACTO() || getUsuarioLogado().getUsuarioPACTOBR()) {
                return true;
            }

            if (getUsuarioLogado().getPossuiPerfilAcessoAdministrador()) {
                return true;
            }

            return false;
        } catch (Exception ex){
            return false;
        }
    }

    public void notificarRecursoEmpresaContratosConcomitantes(){

        if(getEmpresaVO().getPermiteContratosConcomintante()){
            notificarRecursoEmpresa(RecursoSistema.CONTRATO_CONCOMITANTE_MARCOU);
        }else{
            notificarRecursoEmpresa(RecursoSistema.CONTRATO_CONCOMITANTE_DESMARCOU);
        }
    }

    public void montarComboConfigEmissao() {
        try {
            listaConfigEmissaoNFSe = new ArrayList<SelectItem>();
            listaConfigEmissaoNFCe = new ArrayList<SelectItem>();

            listaConfigEmissaoNFSe.add(new SelectItem(0, ""));
            listaConfigEmissaoNFCe.add(new SelectItem(0, ""));

            List<ConfiguracaoNotaFiscalVO> listaTodos = getFacade().getConfiguracaoNotaFiscal().consultarConfiguracaoNotaFiscal(getEmpresaVO().getCodigo(),null, true, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (ConfiguracaoNotaFiscalVO confg : listaTodos) {
                if (confg.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFSE) || confg.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFE)) {
                    listaConfigEmissaoNFSe.add(new SelectItem(confg.getCodigo(), confg.getDescricao()));
                }
                if (confg.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFCE) || confg.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFE)) {
                    listaConfigEmissaoNFCe.add(new SelectItem(confg.getCodigo(), confg.getDescricao()));
                }
            }
            preencherConfigEmissao();
        } catch (Exception e) {
            Uteis.logar(e, EmpresaControle.class);
            listaConfigEmissaoNFSe = new ArrayList<SelectItem>();
            listaConfigEmissaoNFCe = new ArrayList<SelectItem>();
        }
    }

    public void preencherConfigEmissao() {
        try {
            getEmpresaVO().setConfiguracaoNotaFiscalNFSe(getFacade().getConfiguracaoNotaFiscal().consultarPorChavePrimaria(getEmpresaVO().getConfiguracaoNotaFiscalNFSe().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getEmpresaVO().setConfiguracaoNotaFiscalNFCe(getFacade().getConfiguracaoNotaFiscal().consultarPorChavePrimaria(getEmpresaVO().getConfiguracaoNotaFiscalNFCe().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception e) {
            Uteis.logar(e, EmpresaControle.class);
        }
    }

    public List<SelectItem> getListaConfigEmissaoNFSe() {
        if (listaConfigEmissaoNFSe == null) {
            listaConfigEmissaoNFSe = new ArrayList<SelectItem>();
        }
        return listaConfigEmissaoNFSe;
    }

    public void setListaConfigEmissaoNFSe(List<SelectItem> listaConfigEmissaoNFSe) {
        this.listaConfigEmissaoNFSe = listaConfigEmissaoNFSe;
    }

    public List<SelectItem> getListaConfigEmissaoNFCe() {
        if (listaConfigEmissaoNFCe == null) {
            listaConfigEmissaoNFCe = new ArrayList<SelectItem>();
        }
        return listaConfigEmissaoNFCe;
    }

    public void setListaConfigEmissaoNFCe(List<SelectItem> listaConfigEmissaoNFCe) {
        this.listaConfigEmissaoNFCe = listaConfigEmissaoNFCe;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public Date getDataInicialConciliadora() {
        return dataInicialConciliadora;
    }

    public void setDataInicialConciliadora(Date dataInicialConciliadora) {
        this.dataInicialConciliadora = dataInicialConciliadora;
    }

    public Date getDataFinalConciliadora() {
        return dataFinalConciliadora;
    }

    public void setDataFinalConciliadora(Date dataFinalConciliadora) {
        this.dataFinalConciliadora = dataFinalConciliadora;
    }

    public boolean isReprocessarConciliadora() {
        return reprocessarConciliadora;
    }

    public void setReprocessarConciliadora(boolean reprocessarConciliadora) {
        this.reprocessarConciliadora = reprocessarConciliadora;
    }

    public Integer getReciboPagamentoConciliadora() {
        if (reciboPagamentoConciliadora == null) {
            reciboPagamentoConciliadora = 0;
        }
        return reciboPagamentoConciliadora;
    }

    public void setReciboPagamentoConciliadora(Integer reciboPagamentoConciliadora) {
        this.reciboPagamentoConciliadora = reciboPagamentoConciliadora;
    }

    public void notificarRecursoEmpresaEmpresaControle(){
        if(!empresaVO.isPermitirEstornarContratoComParcelasPG()){
            if(!empresaVO.equals(empresaVOClone)){
                notificarRecursoEmpresa(RecursoSistema.NAO_ESTORNAR_CONTRATO_PARCELA_PAGA_DESMARCOU);
            }
        }
    }

    public void alterarTipoProdutoNFSe(){
        montarListaTipoProdutoNFSe();
        setMsgAlert("Richfaces.showModalPanel('modalEditarTipoProdutoNFSe')");
    }

    public void montarListaTipoProduto(){
        this.listaTipoProduto.clear();
        this.listaTipoProduto.add(new SelectItem("", ""));
        for (TipoProduto tipoProduto: TipoProduto.values()){
            this.listaTipoProduto.add( new SelectItem(tipoProduto.getCodigo(), tipoProduto.getDescricao()));
        }
    }

    private void montarListaTipoProdutoNFSe(){
        this.listaTipoProdutoNFSe.clear();
        this.codigoTipoProduto = "";
        String tipos = this.empresaVO.getTipoProdutoEmissaoNFSe();
        if ((tipos != null) && (!(tipos.trim().equals("")))){
            String[]arrayTipos = tipos.split("\\|");
            for (String tp: arrayTipos){
                TipoProduto tipoProduto = TipoProduto.getTipoProdutoCodigo(tp);
                if (tipoProduto != null){
                    this.listaTipoProdutoNFSe.add( new SelectItem(tipoProduto.getCodigo(), tipoProduto.getDescricao()));
                }
            }
        }
    }

    public void gravarTiposProdutoNFSe(){
        try{
            StringBuilder tipos = new StringBuilder();
            for (SelectItem selectItem: this.listaTipoProdutoNFSe){
                if (tipos.toString().equals("")){
                    tipos.append(selectItem.getValue());
                }else{
                    tipos.append("|").append(selectItem.getValue());
                }
            }
            this.empresaVO.setTipoProdutoEmissaoNFSe(tipos.toString());
            setMsgAlert("Richfaces.hideModalPanel('modalEditarTipoProdutoNFSe');");
        }catch (Exception e){
            montarErro(e);
        }
    }

    public void restaurarTiposProdutoNFSePadrao(){
        try{
            this.empresaVO.setTipoProdutoEmissaoNFSe(EmpresaVO.TIPOS_PRODUTOS_EMITIR_NFSe_PADRAO);
            montarListaTipoProdutoNFSe();
        }catch (Exception e){
            montarErro(e);
        }
    }


    public void removerTipoProdutoNFSe(){
        try{
            SelectItem tpProduto = (SelectItem) context().getExternalContext().getRequestMap().get("tpProduto");
            for (SelectItem selectItem: this.listaTipoProdutoNFSe){
                if (((String)selectItem.getValue()).equals((String)tpProduto.getValue())){
                    this.listaTipoProdutoNFSe.remove(selectItem);
                    break;
                }
            }
        }catch (Exception e){
            montarErro(e);
        }
    }

    public void adicionarTipoProduto(){
        try{
            TipoProduto tipoProduto = TipoProduto.getTipoProdutoCodigo(this.codigoTipoProduto);
            if (tipoProduto != null){
                boolean jaAdicionou = false;
                for (SelectItem item : this.listaTipoProdutoNFSe){
                    if  (((String)item.getValue()).equals(this.codigoTipoProduto)){
                        jaAdicionou = true;
                        break;
                    }
                }
                if (!jaAdicionou){
                    this.listaTipoProdutoNFSe.add( new SelectItem(tipoProduto.getCodigo(), tipoProduto.getDescricao()));
                }

            }
            this.codigoTipoProduto = "";

        }catch (Exception ex) {
            montarErro(ex);
        }
    }

    private void montarListaTipoProdutoNFCe(){
        this.listaTipoProdutoNFCe.clear();
        this.codigoTipoProdutoNFCe = "";
        String tipos = this.empresaVO.getTipoProdutoEmissaoNFCe();
        if ((tipos != null) && (!(tipos.trim().equals("")))){
            String[]arrayTipos = tipos.split("\\|");
            for (String tp: arrayTipos){
                TipoProduto tipoProduto = TipoProduto.getTipoProdutoCodigo(tp);
                if (tipoProduto != null){
                    this.listaTipoProdutoNFCe.add( new SelectItem(tipoProduto.getCodigo(), tipoProduto.getDescricao()));
                }
            }
        }
    }

    public void restaurarTiposProdutoNFCePadrao(){
        try{
            this.empresaVO.setTipoProdutoEmissaoNFCe(EmpresaVO.TIPOS_PRODUTOS_EMITIR_NFCe_PADRAO);
            montarListaTipoProdutoNFCe();
        }catch (Exception e){
            montarErro(e);
        }
    }

    public void removerTipoProdutoNFCe(){
        try{
            SelectItem tpProduto = (SelectItem) context().getExternalContext().getRequestMap().get("tpProduto");
            for (SelectItem selectItem: this.listaTipoProdutoNFCe){
                if (((String)selectItem.getValue()).equals((String)tpProduto.getValue())){
                    this.listaTipoProdutoNFCe.remove(selectItem);
                    break;
                }
            }
        }catch (Exception e){
            montarErro(e);
        }
    }

    public void adicionarTipoProdutoNFCe(){
        try{
            TipoProduto tipoProduto = TipoProduto.getTipoProdutoCodigo(this.codigoTipoProdutoNFCe);
            if (tipoProduto != null){
                boolean jaAdicionou = false;
                for (SelectItem item : this.listaTipoProdutoNFCe){
                    if  (((String)item.getValue()).equals(this.codigoTipoProdutoNFCe)){
                        jaAdicionou = true;
                        break;
                    }
                }
                if (!jaAdicionou){
                    this.listaTipoProdutoNFCe.add( new SelectItem(tipoProduto.getCodigo(), tipoProduto.getDescricao()));
                }

            }
            this.codigoTipoProdutoNFCe = "";
        }catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void alterarTipoProdutoNFCe(){
        montarListaTipoProdutoNFCe();
        setMsgAlert("Richfaces.showModalPanel('modalEditarTipoProdutoNFCe')");
    }

    public void gravarTiposProdutoNFCe(){
        try{
            StringBuilder tipos = new StringBuilder();
            for (SelectItem selectItem: this.listaTipoProdutoNFCe){
                if (tipos.toString().equals("")){
                    tipos.append(selectItem.getValue());
                }else{
                    tipos.append("|").append(selectItem.getValue());
                }
            }
            this.empresaVO.setTipoProdutoEmissaoNFCe(tipos.toString());
            setMsgAlert("Richfaces.hideModalPanel('modalEditarTipoProdutoNFCe');");
        }catch (Exception e){
            montarErro(e);
        }
    }

    public String[] identificacaoPessoalInternacional(){
        try {
            displayIdentificadorFront = identificadorPessoaInternacional(getEmpresaLogado().getPais().getNome());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return displayIdentificadorFront;
    }

    public void ativarClientesBI(){
        try {
            Integer codigoEmpresa = getEmpresaVO().getCodigo();
            if (codigoEmpresa > 0) {
                getFacade().getCliente().ativarVerificacaoClientesAtivos(codigoEmpresa);
                montarSucessoGrowl("Verificação de Clientes ATIVA");
            } else {
                montarSucessoGrowl("Empresa não localizada.");
                setMsgAlert("Selecione uma Empresa!");
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void gerarCredencialPJBank() {
        try {
            limparMsg();
            empresaVO.validarDadosPJBankCadastroEmpresa();
            Credenciamento credenciamento = new Credenciamento();

            ContaCorrenteVO contaCorrenteVO = null;
            for (ContaCorrenteEmpresaVO ccEmpresaVO : getEmpresaVO().getContaCorrenteEmpresaVOs()) {
                if(ccEmpresaVO.getContaCorrente().getCodigo().equals(empresaVO.getValueConta())) {
                    contaCorrenteVO = ccEmpresaVO.getContaCorrente();
                    break;
                }
            }

            if (contaCorrenteVO == null) {
                throw new Exception("Conta corrente não encontrada.");
            }

            String banco = StringUtilities.formatarCampoForcandoZerosAEsquerda(contaCorrenteVO.getBanco().getCodigoBanco(), 3);

            String agencia = contaCorrenteVO.getAgencia() +
                    (UteisValidacao.emptyString(contaCorrenteVO.getAgenciaDV()) ? "" : ("-" + contaCorrenteVO.getAgenciaDV()));

            String conta = StringUtilities.formatarCampoForcandoZerosAEsquerda(contaCorrenteVO.getContaCorrente(), 8) +
                    (UteisValidacao.emptyString(contaCorrenteVO.getContaCorrenteDV()) ? "" : ("-" + contaCorrenteVO.getContaCorrenteDV()));

            CredencialRecebimento cdr =  credenciamento.create(empresaVO.getNome(),  banco, agencia, conta,
                    Uteis.formatarCpfCnpj(empresaVO.getCNPJ(), true), empresaVO.getDdPjbank(), empresaVO.getFonePjbank(), empresaVO.getEmailPjbank(), FormaRecebimento.BOLETO_BANCARIO, "0796");

            gerarConveniosCobrancaPJBank(cdr, empresaVO, contaCorrenteVO);
            carregarDadosIntegracaoPJBank();
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void gerarConveniosCobrancaPJBank(CredencialRecebimento cdr, EmpresaVO empresaVO, ContaCorrenteVO contaCorrenteVO) throws Exception {
        //tentar gerar um convênio de boleto e outro de pix

        boolean gerouConvBoleto = false;
        boolean gerouConvpix = false;

        //BOLETO
        try {
            ConvenioCobrancaVO convenioBoleto = new ConvenioCobrancaVO();
            cadastrarBancoPJBank(); //cadastrar o banco antes de incluir os convênios
            List<ConvenioCobrancaEmpresaVO> convenioCobrancaEmpresaBoletoVO = new ArrayList<>();
            convenioCobrancaEmpresaBoletoVO.add(new ConvenioCobrancaEmpresaVO(convenioBoleto, empresaVO));
            convenioBoleto.setConfiguracoesEmpresa(convenioCobrancaEmpresaBoletoVO);
            convenioBoleto.setEmpresa(empresaVO);
            convenioBoleto.setTipo(TipoConvenioCobrancaEnum.BOLETO_PJBANK);
            convenioBoleto.setDescricao(TipoConvenioCobrancaEnum.BOLETO_PJBANK.getDescricao());
            convenioBoleto.setBanco(bancoVO);
            convenioBoleto.setAmbiente(AmbienteEnum.PRODUCAO);
            convenioBoleto.setContaEmpresa(contaCorrenteVO);
            convenioBoleto.setChavePJBank(cdr.getChave());
            convenioBoleto.setCredencialPJBank(cdr.getCredencial());
            convenioBoleto.setTipoBoletoPJBank(TipoBoletoPJBankEnum.BOLETO_PIX);

            getFacade().getConvenioCobranca().incluir(convenioBoleto);
            gerouConvBoleto = true;

        } catch (Exception e) {
            Uteis.logarDebug("Não foi possível criar o convênio de boleto da PjBank" + e.getMessage());
        }

        //PIX
        try {
            ConvenioCobrancaVO convenioPix = new ConvenioCobrancaVO();
            List<ConvenioCobrancaEmpresaVO> convenioCobrancaEmpresaPixVO = new ArrayList<>();
            convenioCobrancaEmpresaPixVO.add(new ConvenioCobrancaEmpresaVO(convenioPix, empresaVO));
            convenioPix.setConfiguracoesEmpresa(convenioCobrancaEmpresaPixVO);
            convenioPix.setEmpresa(empresaVO);
            convenioPix.setTipo(TipoConvenioCobrancaEnum.PIX_PJBANK);
            convenioPix.setDescricao(TipoConvenioCobrancaEnum.PIX_PJBANK.getDescricao());
            convenioPix.setAmbiente(AmbienteEnum.PRODUCAO);
            convenioPix.setChavePJBank(cdr.getChave());
            convenioPix.setCredencialPJBank(cdr.getCredencial());
            convenioPix.setDiasExpirarPix(2);

            getFacade().getConvenioCobranca().incluir(convenioPix);
            gerouConvpix = true;

        } catch (Exception e) {
            Uteis.logarDebug("Não foi possível criar o convênio de pix da PjBank" + e.getMessage());
        }

        //não gerou nenhum, lançar exceção
        if (!gerouConvBoleto && !gerouConvpix) {
            setPossuiCredenciaisPJBank(false);
            throw new Exception("Erro ao gerar credenciais com a PJBank: Não foi possível criar nem o convênio de Boleto e nem o Convênio de Pix.");
        } else {
            //gerou ao menos um, então considerar sucesso
            setPossuiCredenciaisPJBank(true);
        }

        //Tratamento das mensagens
        if (gerouConvBoleto && gerouConvpix) {
            montarSucessoGrowl("Integração realizada com sucesso! Gerei dois convênios de cobrança, um de BOLETO e um de de PIX.");
        }
        if (gerouConvBoleto && !gerouConvpix) {
            montarSucessoGrowl("Integração realizada com sucesso! Gerei um convênio de cobrança de BOLETO, mas não foi possível gerar um de PIX, você pode criá-lo manualmente.");
        }
        if (!gerouConvBoleto && gerouConvpix) {
            montarSucessoGrowl("Integração realizada com sucesso! Gerei um convênio de cobrança de PIX,  mas não foi possível gerar um de BOLETO, você pode criá-lo manualmente.");
        }
    }

    public void gerarConveniosCobrancaAsaas(String apiToken, EmpresaVO empresaVO, AmbienteEnum ambienteEnum) throws Exception {
        //tentar gerar um convênio de boleto e outro de pix

        boolean gerouConvBoleto = false;
        boolean gerouConvpix = false;

        //BOLETO
        try {
            ConvenioCobrancaVO convenioBoleto = new ConvenioCobrancaVO();
            List<ConvenioCobrancaEmpresaVO> convenioCobrancaEmpresaBoletoVO = new ArrayList<>();
            convenioCobrancaEmpresaBoletoVO.add(new ConvenioCobrancaEmpresaVO(convenioBoleto, empresaVO));
            convenioBoleto.setConfiguracoesEmpresa(convenioCobrancaEmpresaBoletoVO);
            convenioBoleto.setEmpresa(empresaVO);
            convenioBoleto.setTipo(TipoConvenioCobrancaEnum.BOLETO_ASAAS);
            convenioBoleto.setDescricao(TipoConvenioCobrancaEnum.BOLETO_ASAAS.getDescricao());
            convenioBoleto.setAmbiente(ambienteEnum);
            convenioBoleto.setCodigoAutenticacao01(apiToken);

            getFacade().getConvenioCobranca().incluir(convenioBoleto);
            gerouConvBoleto = true;

            try {
                incluirLogInclusaoConveniosAsaasBoleto(convenioBoleto); //log da inclusão
            } catch (Exception ex) {
            }

        } catch (Exception e) {
            Uteis.logarDebug("Não foi possível criar o convênio de boleto do Asaas" + e.getMessage());
            e.printStackTrace();
        }

        //PIX
        try {
            ConvenioCobrancaVO convenioPix = new ConvenioCobrancaVO();
            List<ConvenioCobrancaEmpresaVO> convenioCobrancaEmpresaPixVO = new ArrayList<>();
            convenioCobrancaEmpresaPixVO.add(new ConvenioCobrancaEmpresaVO(convenioPix, empresaVO));
            convenioPix.setConfiguracoesEmpresa(convenioCobrancaEmpresaPixVO);
            convenioPix.setEmpresa(empresaVO);
            convenioPix.setTipo(TipoConvenioCobrancaEnum.PIX_ASAAS);
            convenioPix.setDescricao(TipoConvenioCobrancaEnum.PIX_ASAAS.getDescricao());
            convenioPix.setAmbiente(ambienteEnum);
            convenioPix.setCodigoAutenticacao01(apiToken);
            convenioPix.setDiasExpirarPix(2);

            getFacade().getConvenioCobranca().incluir(convenioPix);
            gerouConvpix = true;

            try {
                incluirLogInclusaoConveniosAsaasPix(convenioPix); //log da inclusão
            } catch (Exception ex) {
            }

        } catch (Exception e) {
            Uteis.logarDebug("Não foi possível criar o convênio de pix do Asaas" + e.getMessage());
        }

        //não gerou nenhum, lançar exceção
        if (!gerouConvBoleto && !gerouConvpix) {
            throw new Exception("Não foi possível criar nem o convênio de Boleto e nem o Convênio de Pix.");
        }

        //Tratamento das mensagens
        if (gerouConvBoleto && gerouConvpix) {
            montarSucessoGrowl("Integração realizada com sucesso! Gerei dois convênios de cobrança, um de BOLETO e um de de PIX.");
        }
        if (gerouConvBoleto && !gerouConvpix) {
            montarSucessoGrowl("Integração realizada com sucesso! Gerei um convênio de cobrança de BOLETO, mas não foi possível gerar um de PIX, você pode criá-lo manualmente.");
        }
        if (!gerouConvBoleto && gerouConvpix) {
            montarSucessoGrowl("Integração realizada com sucesso! Gerei um convênio de cobrança de PIX,  mas não foi possível gerar um de BOLETO, você pode criá-lo manualmente.");
        }
    }

    public void incluirLogInclusaoConveniosAsaasPix(ConvenioCobrancaVO convenioPix) throws Exception {
        try {
            convenioPix.setObjetoVOAntesAlteracao(new ConvenioCobrancaVO());
            convenioPix.setNovoObj(true);
            registrarLogObjetoVO(convenioPix, convenioPix.getCodigo(), "CONVENIOCOBRANCA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CONVENIOCOBRANCA", convenioPix.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE CONVENIOCOBRANCA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void incluirLogInclusaoConveniosAsaasBoleto(ConvenioCobrancaVO convenioBoleto) throws Exception {
        try {
            convenioBoleto.setObjetoVOAntesAlteracao(new ConvenioCobrancaVO());
            convenioBoleto.setNovoObj(true);
            registrarLogObjetoVO(convenioBoleto, convenioBoleto.getCodigo(), "CONVENIOCOBRANCA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CONVENIOCOBRANCA", convenioBoleto.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE CONVENIOCOBRANCA", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    private void cadastrarBancoPJBank() {
        try {
            List<BancoVO> bancos = getFacade().getBanco().consultarPorCodigoBanco(TipoConvenioCobrancaEnum.BOLETO_PJBANK.getCodigoBanco(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if(!bancos.stream().anyMatch(r -> r.getCodigoBanco() == TipoConvenioCobrancaEnum.BOLETO_PJBANK.getCodigoBanco())){
                bancoVO = new BancoVO();
                bancoVO.setCodigoBanco(301);
                bancoVO.setNome("PJBank");
                getFacade().getBanco().incluir(bancoVO);
            }else{
                for (BancoVO banco : bancos) {
                    if(banco.getCodigoBanco() == TipoConvenioCobrancaEnum.BOLETO_PJBANK.getCodigoBanco()){
                        bancoVO = new BancoVO(banco.getCodigo(),banco.getCodigoBanco(), banco.getNome());
                        break;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaContaEmpresa() {
        List<Integer> codsJaInCluidos = new ArrayList<Integer>();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        for (ContaCorrenteEmpresaVO ccEmpresaVO : getEmpresaVO().getContaCorrenteEmpresaVOs()) {
            if (codsJaInCluidos.contains(ccEmpresaVO.getContaCorrente().getCodigo())) {
                continue;
            }
            codsJaInCluidos.add(ccEmpresaVO.getContaCorrente().getCodigo());
            objs.add(new SelectItem(ccEmpresaVO.getContaCorrente().getCodigo(), ccEmpresaVO.getContaCorrente().getApresentarDadosBancarios()));
        }
        setListaSelectItemContaEmpresa(objs);
    }

    public void carregarDadosIntegracaoPJBank(){
        try {
            setPossuiCredenciaisPJBank(false);
            this.getEmpresaVO().setEmailPjbank(this.empresaVO.getEmail());

            TipoConvenioCobrancaEnum[] tipos = new TipoConvenioCobrancaEnum[]{TipoConvenioCobrancaEnum.BOLETO_PJBANK, TipoConvenioCobrancaEnum.PIX_PJBANK};
            List<ConvenioCobrancaVO> lstConvenioCobranca = getFacade().getConvenioCobranca().consultarPorTiposESituacao(tipos, getEmpresaVO().getCodigo(), SituacaoConvenioCobranca.ATIVO, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, false);

            for (ConvenioCobrancaVO obj : lstConvenioCobranca) {
                if (obj.getCredencialPJBank() != null &&
                        !UteisValidacao.emptyString(obj.getCredencialPJBank().trim())) {
                    empresaVO.getConvenioBoletoPadrao().setCredencialPJBank(obj.getCredencialPJBank());
                    empresaVO.getConvenioBoletoPadrao().setChavePJBank(obj.getChavePJBank());
                    setPossuiCredenciaisPJBank(true);
                    break;
                }
            }

            montarListaContaEmpresa();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void carregarDadosMerchantPagoLivre() throws Exception {
        carregarDadosMerchantPagoLivreGeral(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE);
    }

    public void carregarDadosMerchantFacilitePay() throws Exception {
        carregarDadosMerchantPagoLivreGeral(TipoConvenioCobrancaEnum.DCC_FACILITEPAY);
    }

    private void carregarDadosMerchantPagoLivreGeral(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum) throws Exception {
        limparMsg();
        setMsgAlert("");

        boolean pagoLivre = tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE);
        if (pagoLivre) {
            limparInformacoesPagoLivre();
        } else {
            limparInformacoesFacilitePay();
        }
        montarListaContaEmpresa();

        if (pagoLivre) {
            setConvenioCobrancaPagoLivre(null);
        } else {
            setConvenioCobrancaFacilitePay(null);
        }

        List<ConvenioCobrancaVO> convenios = getFacade().getConvenioCobranca().consultarPorTipoECodEmpresa(tipoConvenioCobrancaEnum,
                getEmpresaVO().getCodigo(), SituacaoConvenioCobranca.ATIVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (!UteisValidacao.emptyList(convenios)) {
            if (pagoLivre) {
                setConvenioCobrancaPagoLivre(convenios.get(0));
                this.setAmbientePagoLivre(getConvenioCobrancaPagoLivre().getAmbiente());
            } else {
                setConvenioCobrancaFacilitePay(convenios.get(0));
                this.setAmbienteFacilitePay(getConvenioCobrancaFacilitePay().getAmbiente());
            }
        }

        if ((tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) && isPossuiMerchantPagoLivre()) ||
                (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY) && isPossuiMerchantFacilitePay())) {
            try {

                if (pagoLivre) {
                    this.setAmbientePagoLivre(getConvenioCobrancaPagoLivre().getAmbiente());
                    this.setMerchantIdPagoLivre(getConvenioCobrancaPagoLivre().getCodigoAutenticacao01());
                } else {
                    this.setAmbienteFacilitePay(getConvenioCobrancaFacilitePay().getAmbiente());
                    this.setMerchantIdFacilitePay(getConvenioCobrancaFacilitePay().getCodigoAutenticacao01());
                }

                PagoLivreService pagoLivreService = new PagoLivreService(getFacade().getEmpresa().getCon(), this.getEmpresaVO().getCodigo(),
                        pagoLivre ? getConvenioCobrancaPagoLivre().getCodigo() : getConvenioCobrancaFacilitePay().getCodigo(),
                        tipoConvenioCobrancaEnum.getTipoTransacao());
                RespostaHttpDTO resposta = pagoLivreService.consultarMerchant();

                MerchantPagoLivreRequisicaoDto merchantPagoLivreRequisicaoDto = new MerchantPagoLivreRequisicaoDto();
                Gson json = new GsonBuilder().setDateFormat("yyyy-MM-dd'T'HH:mm:ss").create();
                merchantPagoLivreRequisicaoDto.setMerchantPagoLivreDto(json.fromJson(resposta.getResponse(), MerchantPagoLivreDto.class));
                if (pagoLivre) {
                    this.setMerchantPagoLivreDto(merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto());
                    this.getMerchantPagoLivreDto().setCnpj(Uteis.formatarCpfCnpj(this.getMerchantPagoLivreDto().getCnpj(), false));
                } else {
                    this.setMerchantFacilitePayDto(merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto());
                    this.getMerchantFacilitePayDto().setCnpj(Uteis.formatarCpfCnpj(this.getMerchantFacilitePayDto().getCnpj(), false));
                }

                if (resposta.getHttpStatus() == 200) {

                    if (pagoLivre) {
                        this.setNomeEmpresaPagoLivre(merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getCompanyName());
                        this.setEmailPagoLivre(merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getEmail());
                        this.setDddTelefonePagoLivre(Integer.parseInt(merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getPhoneNumber().substring(0, 2)));
                        this.setTelefonePagoLivre(merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getPhoneNumber().substring(2));
                        if (!UteisValidacao.emptyList(merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getPortalUsers())) {
                            for (PortalUsersPagoLivreDto userPortal : merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getPortalUsers()) {
                                if (!UteisValidacao.emptyString(userPortal.getEmail()) && userPortal.getEmail().contains("portaluser_")) {
                                    this.setEmailUserPagoLivre(userPortal.getEmail().replace("portaluser_", ""));
                                } else {
                                    this.setEmailUserPagoLivre(userPortal.getEmail());
                                }
                                this.setNomeUserPagoLivre(userPortal.getName());
                                this.setTextoSenhaPagoLivreOuFacilite("A senha foi enviada para o email: " + this.getEmailUserPagoLivre().replace("portaluser_", ""));
                            }
                        } else {
                            this.setEmailUserPagoLivre("Nenhum email de usuário cadastrado para este merchant");
                            this.setNomeUserPagoLivre("Nenhum nome de usuário cadastrado para este merchant");
                            this.setTextoSenhaPagoLivreOuFacilite("Nenhuma senha cadastrada");
                        }
                        StringBuilder sb = new StringBuilder();
                        sb.append("B: " + merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getBankAccount().getBankNumber() + " / ");
                        sb.append("AG: " + merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getBankAccount().getAgencyNumber());
                        if (!UteisValidacao.emptyString(merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getBankAccount().getAgencyDigit())) {
                            sb.append("-" + merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getBankAccount().getAgencyDigit());
                        }
                        sb.append(" / CC: " + merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getBankAccount().getAccountNumber());
                        if (!UteisValidacao.emptyString(merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getBankAccount().getAccountDigit())) {
                            sb.append("-" + merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getBankAccount().getAccountDigit());
                        }
                        setContaBancariaPagoLivre(sb.toString());

                    } else {

                        this.setNomeEmpresaFacilitePay(merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getCompanyName());
                        this.setEmailFacilitePay(merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getEmail());
                        this.setDddTelefoneFacilitePay(Integer.parseInt(merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getPhoneNumber().substring(0, 2)));
                        this.setTelefoneFacilitePay(merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getPhoneNumber().substring(2));
                        if (!UteisValidacao.emptyList(merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getPortalUsers())) {
                            for (PortalUsersPagoLivreDto userPortal : merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getPortalUsers()) {
                                if (!UteisValidacao.emptyString(userPortal.getEmail()) && userPortal.getEmail().contains("portaluser_")) {
                                    this.setEmailUserFacilitePay(userPortal.getEmail().replace("portaluser_", ""));
                                } else {
                                    this.setEmailUserFacilitePay(userPortal.getEmail());
                                }
                                this.setNomeUserFacilitePay(userPortal.getName());
                                this.setTextoSenhaPagoLivreOuFacilite("A senha foi enviada para o email: " + this.getEmailUserFacilitePay().replace("portaluser_", ""));
                            }
                        } else {
                            this.setEmailUserFacilitePay("Nenhum email de usuário cadastrado para este merchant");
                            this.setNomeUserFacilitePay("Nenhum nome de usuário cadastrado para este merchant");
                            this.setTextoSenhaPagoLivreOuFacilite("Nenhuma senha cadastrada");
                        }
                        StringBuilder sb = new StringBuilder();
                        sb.append("B: " + merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getBankAccount().getBankNumber() + " / ");
                        sb.append("AG: " + merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getBankAccount().getAgencyNumber());
                        if (!UteisValidacao.emptyString(merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getBankAccount().getAgencyDigit())) {
                            sb.append("-" + merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getBankAccount().getAgencyDigit());
                        }
                        sb.append(" / CC: " + merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getBankAccount().getAccountNumber());
                        if (!UteisValidacao.emptyString(merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getBankAccount().getAccountDigit())) {
                            sb.append("-" + merchantPagoLivreRequisicaoDto.getMerchantPagoLivreDto().getBankAccount().getAccountDigit());
                        }
                        setContaBancariaFacilitePay(sb.toString());
                    }

                    montarSucessoGrowl("Dados da integração "+tipoConvenioCobrancaEnum.getTipoTransacao().getDescricao()+" consultados com sucesso!");
                } else {
                    throw new Exception("Erro ao carregar os dados do Merchant");
                }
            } catch (Exception e) {
                montarErro("Erro ao carregar os dados do Merchant");
            }
        } else {
            preencherInformacoesPagoLivre(getEmpresaVO(), tipoConvenioCobrancaEnum);
        }
    }

    public void carregarDadosEmpresaAsaas() throws Exception {
        try {
            limparMsg();
            setMsgAlert("");
            setPossuiCadastroAsaas(false);
            setAsaasEmpresaVO(new AsaasEmpresaVO());

            AsaasEmpresaVO obj = getFacade().getAsaasEmpresa().consultar(getEmpresaVO().getCodigo());

            if (obj != null && (obj.getAmbienteEnum() == null || obj.getAmbienteEnum().equals(AmbienteEnum.NENHUM))) {
                montarErro("Não foi possível obter o ambinete para consultar a subconta");
            }

            if (obj != null && !UteisValidacao.emptyString(obj.getId()) && !UteisValidacao.emptyString(obj.getApiKey())) {
                //Agora consultar objeto completo na API do Asaas
                AsaasEmpresaService service = new AsaasEmpresaService(getFacade().getEmpresa().getCon(), obj.getAmbienteEnum());

                boolean erro = false;
                try {
                    setAsaasEmpresaVO(service.consultar(obj.getId()));
                    getAsaasEmpresaVO().setAmbienteEnum(obj.getAmbienteEnum());
                    getAsaasEmpresaVO().setCompanyType(obj.getCompanyType());
                    getAsaasEmpresaVO().setApiKey(getFacade().getAsaasEmpresa().consultarApiKey(getEmpresaVO().getCodigo())); //apikey não vem em consultas na API, pegar a que foi gravada no banco no momento da criação
                    setPossuiCadastroAsaas(true);
                    montarSucessoGrowl("Dados da Integração Asaas consultados com sucesso!");
                } catch (Exception ex) {
                    setPossuiCadastroAsaas(false);
                    montarListaSelectItemTipoEmpresaAsaas();
                    erro = true;
                }
                if (erro) {
                    montarErro("Não foi possível consultar a conta da empresa Asaas");
                }
            } else {
                inicializarCamposNovoCadastroAsaas();
            }
        } catch (Exception ex) {
            montarErro(ex.getMessage());
        }
    }

    private void inicializarCamposNovoCadastroAsaas() {
        //http:// ambiente local ou swarm (não seguro) - Evitar de criar subcontas de teste na conta pai de produção da Pacto
        if (!UteisValidacao.emptyString(getUrl()) && (getUrl().contains("localhost") || getUrl().contains("http://"))) {
            getAsaasEmpresaVO().setAmbienteEnum(AmbienteEnum.HOMOLOGACAO);
        }
        getAsaasEmpresaVO().setName(getEmpresaVO().getNome());
        getAsaasEmpresaVO().setCpfCnpj(getEmpresaVO().getCNPJ());
        getAsaasEmpresaVO().setCompanyType(CompanyTypeAsaasEnum.LIMITED);
        getAsaasEmpresaVO().setEmail(getEmpresaVO().getEmail());
        getAsaasEmpresaVO().setPhone(getEmpresaVO().getTelComercial1());
        getAsaasEmpresaVO().setAddress(getEmpresaVO().getEndereco());
        getAsaasEmpresaVO().setAddressNumber(getEmpresaVO().getNumero());
        getAsaasEmpresaVO().setProvince(getEmpresaVO().getSetor());
        getAsaasEmpresaVO().setPostalCode(getEmpresaVO().getCEP());
        montarListaSelectItemTipoEmpresaAsaas();
    }

    public void consultarTaxasContaAsaas() {
        AsaasEmpresaService asaasEmpresaService;
        try {
            if (UteisValidacao.emptyString(getAsaasEmpresaVO().getApiKey())) {
                throw new Exception("Preencha o campo \"Chave da API\" e tente novamente");
            }
            limparMsg();
            setOnComplete("");
            setTaxasContaAsaasDTO(null);

            asaasEmpresaService = new AsaasEmpresaService(Conexao.getFromSession(), getAsaasEmpresaVO().getApiKey(), getAsaasEmpresaVO().getAmbienteEnum());
            setTaxasContaAsaasDTO(asaasEmpresaService.consultarTaxasConta());
            setOnComplete("Richfaces.showModalPanel('modalTaxasContaAsaasEmpresa');");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            asaasEmpresaService = null;
        }
    }

    public void consultarSituacaoCadastralContaAsaas() {
        AsaasEmpresaService asaasEmpresaService;
        try {
            if (UteisValidacao.emptyString(getAsaasEmpresaVO().getApiKey())) {
                throw new Exception("Preencha o campo \"Chave da API\" e tente novamente");
            }
            limparMsg();
            setOnComplete("");
            setSituacaoCadastralContaAsaasDTO(null);

            asaasEmpresaService = new AsaasEmpresaService(Conexao.getFromSession(), getAsaasEmpresaVO().getApiKey(), getAsaasEmpresaVO().getAmbienteEnum());
            setSituacaoCadastralContaAsaasDTO(asaasEmpresaService.consultarSituacaoCadastralConta());
            setOnComplete("Richfaces.showModalPanel('modalStatusContaAsaasEmpresa');");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            asaasEmpresaService = null;
        }
    }

    public void consultarDadosComerciaisContaAsaas() {
        AsaasEmpresaService asaasEmpresaService;
        try {
            if (UteisValidacao.emptyString(getAsaasEmpresaVO().getApiKey())) {
                throw new Exception("Preencha o campo \"Chave da API\" e tente novamente");
            }
            limparMsg();
            setOnComplete("");
            setDadosComerciaisContaAsaasDTO(null);

            asaasEmpresaService = new AsaasEmpresaService(Conexao.getFromSession(), getAsaasEmpresaVO().getApiKey(), getAsaasEmpresaVO().getAmbienteEnum());
            setDadosComerciaisContaAsaasDTO(asaasEmpresaService.consultarDadosComerciaisConta());
            setOnComplete("Richfaces.showModalPanel('modalDadosComerciaisContaAsaasEmpresa');");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            asaasEmpresaService = null;
        }
    }

    public void excluirContaAsaas() {
        AsaasEmpresa asaasEmpresaDAO;
        try {
            limparMsg();
            setTaxasContaAsaasDTO(null);

            asaasEmpresaDAO = new AsaasEmpresa(Conexao.getFromSession());
            asaasEmpresaDAO.excluir(getAsaasEmpresaVO().getId());
            setAsaasEmpresaVO(new AsaasEmpresaVO());
            setPossuiCadastroAsaas(false);
            montarSucesso("Conta excluída com sucesso!");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        } finally {
            asaasEmpresaDAO = null;
        }
    }


    public void montarListaSelectItemTipoEmpresaAsaas() {
        try {
            List<SelectItem> itens = new ArrayList<SelectItem>();

            List enums = JSFUtilities.getSelectItemListFromEnum(CompanyTypeAsaasEnum.class, "descricao", false);
            for (Object tipo : enums) {
                SelectItem item = (SelectItem) tipo;
                if (!item.getValue().equals(CompanyTypeAsaasEnum.NENHUMA)) {
                    itens.add(new SelectItem(item.getValue(), item.getLabel()));
                }
            }
            setListaSelectItemTipoEmpresaAsaas(itens);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void cadastrarEmpresaAsaas() throws Exception {
        try {
            limparMsg();
            setMsgAlert("");
            validarDadosCadastroEmpresaAsaas(getAsaasEmpresaVO());

            AsaasEmpresaService service = new AsaasEmpresaService(getFacade().getEmpresa().getCon(), getAsaasEmpresaVO().getAmbienteEnum());
            AsaasEmpresaVO retorno = new AsaasEmpresaVO();
            boolean criadoComSucesso = false;
            try {
                retorno = service.criar(getAsaasEmpresaVO(), getEmpresaVO().getCodigo());
                criadoComSucesso = true;
            } catch (Exception ex) {
                montarErro(ex.getMessage());
            }

            if (criadoComSucesso) {
                setPossuiCadastroAsaas(true);
                setAsaasEmpresaVO(retorno);
                gerarConveniosCobrancaAsaas(retorno.getApiKey(), getEmpresaVO(), getAsaasEmpresaVO().getAmbienteEnum()); //monta sucesso aqui dentro
                gerarLogIncusaoIntegracaoAsaas();

            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void gerarLogIncusaoIntegracaoAsaas() throws Exception {
        LogVO obj = new LogVO();
        obj.setChavePrimaria(getEmpresaVO().getCodigo().toString());
        obj.setNomeEntidade("EMPRESA - INTEGRAÇÃO ASAAS");
        obj.setNomeEntidadeDescricao("Empresa");
        obj.setOperacao("INCLUSÃO");
        obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
        obj.setUserOAMD(getUsuarioLogado().getUserOamd());
        obj.setNomeCampo("INTEGRAÇÃO ASAAS");
        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        obj.setValorCampoAnterior("");
        obj.setValorCampoAlterado("Ativou a INTEGRAÇÃO com o ASAAS");
        getFacade().getLog().incluir(obj);
    }

    private void validarDadosCadastroEmpresaAsaas(AsaasEmpresaVO obj) throws ConsistirException {
        if (obj.getAmbienteEnum() == null || obj.getAmbienteEnum().equals(AmbienteEnum.NENHUM)) {
            throw new ConsistirException("Informe o 'Ambiente' desejado para prosseguir com a integração");
        }
        if (UteisValidacao.emptyString(obj.getName())) {
            throw new ConsistirException("Informe o 'Nome da Empresa' para prosseguir com a integração");
        }
        if (UteisValidacao.emptyString(obj.getCpfCnpj())) {
            throw new ConsistirException("Informe o 'CNPJ' para prosseguir com a integração");
        }
        if (UteisValidacao.emptyString(obj.getEmail())) {
            throw new ConsistirException("Informe o 'Email' para prosseguir com a integração");
        }
        if (UteisValidacao.emptyString(obj.getPhone())) {
            throw new ConsistirException("Informe o 'Telefone Fixo' para prosseguir com a integração");
        }
        if (UteisValidacao.emptyString(obj.getMobilePhone())) {
            throw new ConsistirException("Informe o 'Telefone Celular' para prosseguir com a integração");
        }
        if (UteisValidacao.emptyString(obj.getAddress())) {
            throw new ConsistirException("Informe o 'Endereço' para prosseguir com a integração");
        }
        if (UteisValidacao.emptyString(obj.getAddressNumber())) {
            throw new ConsistirException("Informe o 'Nº' do endereço para prosseguir com a integração");
        }
        if (UteisValidacao.emptyString(obj.getProvince())) {
            throw new ConsistirException("Informe o 'Bairro' do endereço para prosseguir com a integração");
        }
        if (UteisValidacao.emptyString(obj.getPostalCode())) {
            throw new ConsistirException("Informe o 'CEP' do endereço para prosseguir com a integração");
        }
    }

    public void cadastrarMerchantPagoLivre() {
        cadastrarMerchantPagoLivreGeral(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE);
    }

    public void cadastrarMerchantFacilitePay() {
        cadastrarMerchantPagoLivreGeral(TipoConvenioCobrancaEnum.DCC_FACILITEPAY);
    }

    private void cadastrarMerchantPagoLivreGeral(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum) {
        boolean pagoLivre = tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE);
        Uteis.logarDebug("Início da criação de nova integração " + (pagoLivre ? TipoConvenioCobrancaEnum.DCC_PAGOLIVRE.getDescricao() : TipoConvenioCobrancaEnum.DCC_FACILITEPAY.getDescricao()));
        try {
            limparMsg();
            setMsgAlert("");

            ContaCorrenteVO contaCorrenteVO = null;
            for (ContaCorrenteEmpresaVO ccEmpresaVO : getEmpresaVO().getContaCorrenteEmpresaVOs()) {
                if (pagoLivre) {
                    if (ccEmpresaVO.getContaCorrente().getCodigo().equals(this.getContaCorrentePagoLivre())) {
                        contaCorrenteVO = ccEmpresaVO.getContaCorrente();
                        break;
                    }
                } else {
                    if (ccEmpresaVO.getContaCorrente().getCodigo().equals(this.getContaCorrenteFacilitePay())) {
                        contaCorrenteVO = ccEmpresaVO.getContaCorrente();
                        break;
                    }
                }
            }
            if (contaCorrenteVO == null) {
                throw new Exception("Conta corrente não encontrada.");
            }
            validarDadosPagoLivreCadastroEmpresa(tipoConvenioCobrancaEnum);

            PagoLivreService pagoLivreService = new PagoLivreService(getFacade().getEmpresa().getCon(), pagoLivre ? this.getAmbientePagoLivre() : this.getAmbienteFacilitePay(), tipoConvenioCobrancaEnum.getTipoTransacao());

            RespostaHttpDTO resposta = null;
            try {
                //primeira requisição com email montado (<EMAIL>)
                resposta = pagoLivreService.cadastrarMerchant(
                        pagoLivre ? this.getAmbientePagoLivre() : this.getAmbienteFacilitePay(),
                        pagoLivre ? this.getCnpjPagoLivre() : this.getCnpjFacilitePay(),
                        pagoLivre ? this.getNomeEmpresaPagoLivre() : this.getNomeEmpresaFacilitePay(),
                        pagoLivre ? this.getEmailPagoLivre() : this.getEmailFacilitePay(),
                        pagoLivre ? this.getDddTelefonePagoLivre().toString() : this.getDddTelefoneFacilitePay().toString(),
                        pagoLivre ? this.getTelefonePagoLivre() : this.getTelefoneFacilitePay(),
                        pagoLivre ? contaCorrenteVO : contaCorrenteVO,
                        pagoLivre ? this.getEmailUserPagoLivre() : this.getEmailUserFacilitePay(),
                        pagoLivre ? this.getNomeUserPagoLivre() : this.getNomeUserFacilitePay()
                );
            } catch (Exception ex) {
                throw ex;
            } finally {
                try {
                    if (resposta.getHttpStatus() == 200) {
                        InfoNovoMerchantPagoLivreVO info = new InfoNovoMerchantPagoLivreVO();
                        info.setDataRegistro(Calendario.hoje());
                        info.setParamsEnvio(resposta.getRequestBody());
                        info.setParamsResposta(resposta.getResponse());
                        info.setTipoConvenioCobranca(pagoLivre ? TipoConvenioCobrancaEnum.DCC_PAGOLIVRE.getCodigo() : TipoConvenioCobrancaEnum.DCC_FACILITEPAY.getCodigo());
                        getFacade().getInfoNovoMerchantPagoLivre().incluir(info);
                    } else if (!UteisValidacao.emptyString(resposta.getResponse()) &&
                            resposta.getResponse().contains("e-mail") &&
                            resposta.getResponse().contains("sendo utilizado na PagoLivre")) {

                        //tentar a requisição de novo com outro email (<EMAIL>)

                        //trocar fypay por fy
                        String email = pagoLivre ? this.getEmailPagoLivre().replace("@fypay", "@fy") :
                                this.getEmailFacilitePay().replace("@fypay", "@fy");
                        try {
                            resposta = pagoLivreService.cadastrarMerchant(
                                    pagoLivre ? this.getAmbientePagoLivre() : this.getAmbienteFacilitePay(),
                                    pagoLivre ? this.getCnpjPagoLivre() : this.getCnpjFacilitePay(),
                                    pagoLivre ? this.getNomeEmpresaPagoLivre() : this.getNomeEmpresaFacilitePay(),
                                    email,
                                    pagoLivre ? this.getDddTelefonePagoLivre().toString() : this.getDddTelefoneFacilitePay().toString(),
                                    pagoLivre ? this.getTelefonePagoLivre() : this.getTelefoneFacilitePay(),
                                    pagoLivre ? contaCorrenteVO : contaCorrenteVO,
                                    pagoLivre ? this.getEmailUserPagoLivre() : this.getEmailUserFacilitePay(),
                                    pagoLivre ? this.getNomeUserPagoLivre() : this.getNomeUserFacilitePay()
                            );

                            if (resposta.getHttpStatus() == 200) {
                                InfoNovoMerchantPagoLivreVO info = new InfoNovoMerchantPagoLivreVO();
                                info.setDataRegistro(Calendario.hoje());
                                info.setParamsEnvio(resposta.getRequestBody());
                                info.setParamsResposta(resposta.getResponse());
                                info.setTipoConvenioCobranca(pagoLivre ? TipoConvenioCobrancaEnum.DCC_PAGOLIVRE.getCodigo() : TipoConvenioCobrancaEnum.DCC_FACILITEPAY.getCodigo());
                                getFacade().getInfoNovoMerchantPagoLivre().incluir(info);
                            }
                        } catch (Exception ex) {
                            throw ex;
                        }

                    } else {
                        Uteis.logarDebug("falha ao criar um novo merchant pagolivre: status da resposta não é 200");
                    }
                } catch (Exception ex) {
                    Uteis.logarDebug("Não consegui incuir o registro na tabela infoNovoMerchantPagoLivre: " + ex.getMessage());
                }
            }

            MerchantPagoLivreRequisicaoDto merchantPagoLivreRequisicaoDto = new MerchantPagoLivreRequisicaoDto();
            Gson json = new GsonBuilder().setDateFormat("yyyy-MM-dd'T'HH:mm:ss").create();
            //seta o objeto de retorno
            merchantPagoLivreRequisicaoDto.setMerchantRetornoDto(json.fromJson(resposta.getResponse(), MerchantRetornoDto.class));

            if (resposta.getHttpStatus() == 200) {
                boolean enviouEmailComSucesso = false;
                String password = "";
                for (PortalUsersPagoLivreDto user : merchantPagoLivreRequisicaoDto.getMerchantRetornoDto().getPortalUsers()) {
                    try {
                        password = user.getPassword();
                        enviouEmailComSucesso = pagoLivreService.enviarEmailUserPagoLivre(user.getEmail(),
                                pagoLivre ? this.getNomeEmpresaPagoLivre() : this.getNomeEmpresaFacilitePay(),
                                pagoLivre ? this.getCnpjPagoLivre() : this.getCnpjFacilitePay(),
                                user);
                    } catch (Exception e) {
                        Uteis.logarDebug("##PAGOLIVRE##: Consegui criar o cadastro de Merchant mas não consegui enviar o email com os dados de acesso");
                    }
                }

                gerarConvenioCobrancaPagoLivre(empresaVO, contaCorrenteVO, pagoLivre ? this.getAmbientePagoLivre() : this.getAmbienteFacilitePay(),
                        merchantPagoLivreRequisicaoDto.getMerchantRetornoDto().getMerchantId(),
                        Uteis.encriptar(password, PropsService.getPropertyValue(PropsService.chaveCriptSenhaUserPagoLivre)), tipoConvenioCobrancaEnum);

                carregarDadosMerchantPagoLivreGeral(tipoConvenioCobrancaEnum);
                if (enviouEmailComSucesso) {
                    montarSucessoGrowl("Merchant cadastrado com Sucesso! Enviamos um email para o usuário informado com a senha de acesso");
                } else {
                    montarSucessoGrowl("Merchant cadastrado com Sucesso! Não conseguimos enviar o email com a senha, entre em contato com a Pacto");
                }
                Uteis.logarDebug("Fim Sucesso da criação de nova integração " + (pagoLivre ? TipoConvenioCobrancaEnum.DCC_PAGOLIVRE.getDescricao() : TipoConvenioCobrancaEnum.DCC_FACILITEPAY.getDescricao()));
            } else {
                throw new Exception("Não foi possível cadastrar o Merchant na "+tipoConvenioCobrancaEnum.getTipoTransacao().getDescricao()+": " + resposta.getResponse());
            }
        } catch (Exception e) {
            if (pagoLivre) {
                montarErro(e);
            } else {
                String erro = e.getMessage().replaceAll("PagoLivre", "Fypay");
                montarErro(erro);
            }
        }
    }

    public void editandoMerchant() {
        limparMsg();
        setEditandoMerchantPagoLivre(true);
    }

    public void editandoContaPagoLivre() {
        limparMsg();
        setEditandoContaBancariaPagoLivre(true);
    }

    public void editandoMerchantFacilite() {
        limparMsg();
        setEditandoMerchantFacilitePay(true);
    }

    public void editandoContaFacilitePay() {
        limparMsg();
        setEditandoContaBancariaFacilitePay(true);
    }

    public void alterarMerchantPagoLivre() {
        alterarMerchantGeral(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE);
    }

    public void alterarMerchantFacilitePay() {
        alterarMerchantGeral(TipoConvenioCobrancaEnum.DCC_FACILITEPAY);
    }

    private void alterarMerchantGeral(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum) {
        limparMsg();
        setMsgAlert("");
        try {
            boolean pagoLivre = tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE);

            validarDadosPagoLivreAlterarEmpresa(tipoConvenioCobrancaEnum);

            PagoLivreService pagoLivreService = new PagoLivreService(Conexao.getFromSession(),
                    empresaVO.getCodigo(),
                    pagoLivre ? this.getConvenioCobrancaPagoLivre().getCodigo() : this.getConvenioCobrancaFacilitePay().getCodigo(),
                    tipoConvenioCobrancaEnum.getTipoTransacao());

            RespostaHttpDTO resposta = pagoLivreService.alterarMerchant(
                    pagoLivre ? this.getMerchantIdPagoLivre() : this.getMerchantIdFacilitePay(),
                    pagoLivre ? this.getNomeEmpresaPagoLivre() : this.getNomeEmpresaFacilitePay(),
                    pagoLivre ? this.getEmailPagoLivre() : this.getEmailFacilitePay(),
                    pagoLivre ? this.getDddTelefonePagoLivre().toString() : this.getDddTelefoneFacilitePay().toString(),
                    pagoLivre ? this.getTelefonePagoLivre() : this.getTelefoneFacilitePay());

            if (resposta.getHttpStatus() == 200) {
                carregarDadosMerchantPagoLivreGeral(tipoConvenioCobrancaEnum);
                montarSucessoGrowl("Merchant alterado com Sucesso!");
                if (pagoLivre) {
                    setEditandoMerchantPagoLivre(false);
                } else {
                    setEditandoMerchantFacilitePay(false);
                }
            } else {
                throw new Exception("Não foi possível alterar o Merchant na "+tipoConvenioCobrancaEnum.getTipoTransacao().getDescricao()+": " + resposta.getResponse());
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void validarDadosPagoLivreCadastroEmpresa(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum) throws ConsistirException {
        if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE)) {
            if (getAmbientePagoLivre().equals(AmbienteEnum.NENHUM)) {
                throw new ConsistirException("Informe o 'Ambiente' para cadastrar o Merchant da PagoLivre");
            }
            if (UteisValidacao.emptyString(getCnpjPagoLivre().trim())) {
                throw new ConsistirException("Informe o 'CNPJ da empresa' para vincular ao Merchant da PagoLivre");
            }
            if (UteisValidacao.emptyString(getNomeEmpresaPagoLivre())) {
                throw new ConsistirException("Informe o 'Nome da empresa' para vincular ao Merchant da PagoLivre");
            }
            if (UteisValidacao.emptyString(getEmailPagoLivre().trim())) {
                throw new ConsistirException("Informe o 'email da empresa' para vincular ao Merchant da PagoLivre");
            }
            if (!Uteis.isValidEmailAddressRegex(getEmailPagoLivre())) {
                throw new ConsistirException("O 'email da empresa' informado não é válido!");
            }
            if (getDddTelefonePagoLivre() == null || getDddTelefonePagoLivre() == 0) {
                throw new ConsistirException("Informe um DDD para vincular ao Merchant da PagoLivre");
            }
            if (getTelefonePagoLivre() == null || UteisValidacao.emptyString(getTelefonePagoLivre())) {
                throw new ConsistirException("Informe um telefone para vincular ao Merchant da PagoLivre");
            }
            if (UteisValidacao.emptyNumber(this.getContaCorrentePagoLivre())) {
                throw new ConsistirException("Selecione uma conta para vincular ao Merchant da PagoLivre");
            }
            if (getEmailUserPagoLivre() == null || UteisValidacao.emptyString(getEmailUserPagoLivre())) {
                throw new ConsistirException("Informe o 'Usuário do portal (email)' para vincular ao Merchant da PagoLivre");
            }
            if (!Uteis.isValidEmailAddressRegex(getEmailUserPagoLivre())) {
                throw new ConsistirException("O email informado no campo 'Usuário do portal' não é válido!");
            }
            if (getNomeUserPagoLivre() == null || UteisValidacao.emptyString(getNomeUserPagoLivre())) {
                throw new ConsistirException("Informe o 'Nome do usuário do portal' para vincular ao Merchant da PagoLivre");
            }

        } else {

            if (getAmbienteFacilitePay().equals(AmbienteEnum.NENHUM)) {
                throw new ConsistirException("Informe o 'Ambiente' para cadastrar o Merchant da Fypay");
            }
            if (UteisValidacao.emptyString(getCnpjFacilitePay().trim())) {
                throw new ConsistirException("Informe o 'CNPJ da empresa' para vincular ao Merchant da Fypay");
            }
            if (UteisValidacao.emptyString(getNomeEmpresaFacilitePay())) {
                throw new ConsistirException("Informe o 'Nome da empresa' para vincular ao Merchant da Fypay");
            }
            if (UteisValidacao.emptyString(getEmailFacilitePay().trim())) {
                throw new ConsistirException("Informe o 'email da empresa' para vincular ao Merchant da Fypay");
            }
            if (!Uteis.isValidEmailAddressRegex(getEmailFacilitePay())) {
                throw new ConsistirException("O 'email da empresa' informado não é válido!");
            }
            if (getDddTelefoneFacilitePay() == null || getDddTelefoneFacilitePay() == 0) {
                throw new ConsistirException("Informe um DDD para vincular ao Merchant da Fypay");
            }
            if (getTelefoneFacilitePay() == null || UteisValidacao.emptyString(getTelefoneFacilitePay())) {
                throw new ConsistirException("Informe um telefone para vincular ao Merchant da Fypay");
            }
            if (UteisValidacao.emptyNumber(this.getContaCorrenteFacilitePay())) {
                throw new ConsistirException("Selecione uma conta para vincular ao Merchant da Fypay");
            }
            if (getEmailUserFacilitePay() == null || UteisValidacao.emptyString(getEmailUserFacilitePay())) {
                throw new ConsistirException("Informe o 'Usuário do portal (email)' para vincular ao Merchant da Fypay");
            }
            if (!Uteis.isValidEmailAddressRegex(getEmailUserFacilitePay())) {
                throw new ConsistirException("O email informado no campo 'Usuário do portal' não é válido!");
            }
            if (getNomeUserFacilitePay() == null || UteisValidacao.emptyString(getNomeUserFacilitePay())) {
                throw new ConsistirException("Informe o 'Nome do usuário do portal' para vincular ao Merchant da Fypay");
            }
        }
    }

    private void validarDadosPagoLivreAlterarEmpresa(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum) throws ConsistirException {
        if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE)) {
            if (UteisValidacao.emptyString(getEmailPagoLivre().trim())) {
                throw new ConsistirException("Informe o 'email da empresa' para vincular ao Merchant da PagoLivre");
            }
            if (!Uteis.isValidEmailAddressRegex(getEmailPagoLivre())) {
                throw new ConsistirException("O 'email da empresa' informado não é válido!");
            }
            if (UteisValidacao.emptyString(getNomeEmpresaPagoLivre())) {
                throw new ConsistirException("Informe o 'Nome da empresa' para vincular ao Merchant da PagoLivre");
            }
            if (getDddTelefonePagoLivre() == null || getDddTelefonePagoLivre() == 0) {
                throw new ConsistirException("Informe um DDD para vincular ao Merchant da PagoLivre");
            }
            if (getTelefonePagoLivre() == null || UteisValidacao.emptyString(getTelefonePagoLivre())) {
                throw new ConsistirException("Informe um telefone para vincular ao Merchant da PagoLivre");
            }
        } else {
            if (UteisValidacao.emptyString(getEmailFacilitePay().trim())) {
                throw new ConsistirException("Informe o 'email da empresa' para vincular ao Merchant da Fypay");
            }
            if (!Uteis.isValidEmailAddressRegex(getEmailFacilitePay())) {
                throw new ConsistirException("O 'email da empresa' informado não é válido!");
            }
            if (UteisValidacao.emptyString(getNomeEmpresaFacilitePay())) {
                throw new ConsistirException("Informe o 'Nome da empresa' para vincular ao Merchant da Fypay");
            }
            if (getDddTelefoneFacilitePay() == null || getDddTelefoneFacilitePay() == 0) {
                throw new ConsistirException("Informe um DDD para vincular ao Merchant da Fypay");
            }
            if (getTelefoneFacilitePay() == null || UteisValidacao.emptyString(getTelefoneFacilitePay())) {
                throw new ConsistirException("Informe um telefone para vincular ao Merchant da Fypay");
            }
        }
    }

    public void alterarContaBancariaMerchantFacilitePay() {
        alterarContaBancariaMerchant(TipoConvenioCobrancaEnum.DCC_FACILITEPAY);
    }

    public void alterarContaBancariaMerchantPagoLivre() {
        alterarContaBancariaMerchant(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE);
    }

    private void alterarContaBancariaMerchant(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum) {
        limparMsg();
        setMsgAlert("");
        try {
            ContaCorrenteVO contaCorrenteVO = null;
            for (ContaCorrenteEmpresaVO ccEmpresaVO : getEmpresaVO().getContaCorrenteEmpresaVOs()) {
                if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE)) {
                    if (ccEmpresaVO.getContaCorrente().getCodigo().equals(this.getContaCorrentePagoLivre())) {
                        contaCorrenteVO = ccEmpresaVO.getContaCorrente();
                        break;
                    }
                } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) {
                    if (ccEmpresaVO.getContaCorrente().getCodigo().equals(this.getContaCorrenteFacilitePay())) {
                        contaCorrenteVO = ccEmpresaVO.getContaCorrente();
                        break;
                    }
                }
            }
            if (contaCorrenteVO == null) {
                throw new Exception("Conta corrente não encontrada.");
            }

            String codBanco = contaCorrenteVO.getBanco().getCodigoBanco().toString();
            String agencia = contaCorrenteVO.getAgencia();
            String digAgencia = (UteisValidacao.emptyString(contaCorrenteVO.getAgenciaDV()) ? "" : (contaCorrenteVO.getAgenciaDV()));
            String conta = contaCorrenteVO.getContaCorrente();
            String digConta = (UteisValidacao.emptyString(contaCorrenteVO.getContaCorrenteDV()) ? "" : (contaCorrenteVO.getContaCorrenteDV()));

            BankAccountPagoLivreDto bankDto = new BankAccountPagoLivreDto();
            bankDto.setBankNumber(codBanco);
            bankDto.setAgencyNumber(agencia);
            bankDto.setAgencyDigit(digAgencia);
            bankDto.setAccountNumber(conta);
            bankDto.setAccountDigit(digConta);
            bankDto.setType(TipoContaMerchantPagoLivre.CONTA_CORRENTE.getCodigo().toString());
            bankDto.setCnpj(Uteis.formatarCpfCnpj(empresaVO.getCNPJ(), true));

            PagoLivreService pagoLivreService = new PagoLivreService(getFacade().getEmpresa().getCon(),
                    tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) ? this.getAmbientePagoLivre() : this.getAmbienteFacilitePay(),
                    tipoConvenioCobrancaEnum.getTipoTransacao());
            String contaBancaria = pagoLivreService.body(bankDto);

            JSONObject jsonEnvio = new JSONObject();
            jsonEnvio.put("merchantId", tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) ? this.getMerchantIdPagoLivre() : this.getMerchantIdFacilitePay());
            jsonEnvio.put("bankAccount", new JSONObject(contaBancaria));

            RespostaHttpDTO resposta = pagoLivreService.alterarContaBancariaMerchant(
                    tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) ? this.getAmbientePagoLivre() : this.getAmbienteFacilitePay(), jsonEnvio);

            if (resposta.getHttpStatus() == 200) {
                carregarDadosMerchantPagoLivreGeral(tipoConvenioCobrancaEnum);
                montarSucessoGrowl("Conta Bancária alterada com Sucesso!");
                if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE)) {
                    setEditandoContaBancariaPagoLivre(false);
                } else {
                    setEditandoContaBancariaFacilitePay(false);
                }
            } else {
                throw new Exception("Não foi possível alterar a conta bancária na "+tipoConvenioCobrancaEnum.getTipoTransacao().getDescricao()+": " + resposta.getResponse());
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void gerarConvenioCobrancaPagoLivre(EmpresaVO empresaVO, ContaCorrenteVO contaCorrenteVO,
                                               AmbienteEnum ambiente, String merchantId, String senha,
                                               TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum) throws Exception {
        ConvenioCobrancaVO convenioCobrancaVO = new ConvenioCobrancaVO();
        List<ConvenioCobrancaEmpresaVO> convenioCobrancaEmpresaVO = new ArrayList<>();
        convenioCobrancaEmpresaVO.add(new ConvenioCobrancaEmpresaVO(convenioCobrancaVO, empresaVO));
        convenioCobrancaVO.setConfiguracoesEmpresa(convenioCobrancaEmpresaVO);
        convenioCobrancaVO.setEmpresa(empresaVO);
        convenioCobrancaVO.setTipo(tipoConvenioCobrancaEnum);
        convenioCobrancaVO.setDescricao(tipoConvenioCobrancaEnum.getDescricao());
        convenioCobrancaVO.setAmbiente(ambiente);
        convenioCobrancaVO.setContaEmpresa(contaCorrenteVO);
        convenioCobrancaVO.setCodigoAutenticacao01(merchantId);
        convenioCobrancaVO.setCodigoAutenticacao02(senha);
        convenioCobrancaVO.setUsuarioVO(getUsuarioLogado());

        try {
            getFacade().getConvenioCobranca().incluir(convenioCobrancaVO);
            setConvenioCobrancaPagoLivre(convenioCobrancaVO);
        } catch (Exception e) {
            montarErro("Erro ao gerar convênio de cobrança " + tipoConvenioCobrancaEnum.getTipoTransacao().getDescricao() + ", não foi possível prosseguir com o cadastro do Merchant: " + e);
            e.printStackTrace();
        }
    }

    public void abrirAbaConciliacaoFacilite() {
        setConectoresAtivosPluggy(new ArrayList<>());
        PluggyService service;
        try {
            service = new PluggyService();
            List<PluggyConnectorDTO> conectoresAtivos = service.obterContasConectadas(getEmpresaVO().getCodigo());

            if (UteisValidacao.emptyList(conectoresAtivos)) {
                return;
            }

            setConectoresAtivosPluggy(conectoresAtivos);

        } catch (Exception ex) {
        } finally {
            service = null;
        }
    }

    public void bloquearExibicaoAccountPluggy(ActionEvent evt) {
        PluggyAccountBloqueio pluggyAccountBloqueioDAO;
        try {
            PluggyAccountDTO pluggyAccountDTO = (PluggyAccountDTO) evt.getComponent().getAttributes().get("account");

            if (pluggyAccountDTO == null) {
                throw new Exception("Não foi possível fazer o bloqueio da exibição deste item");
            }

            PluggyAccountBloqueioVO obj = new PluggyAccountBloqueioVO();
            obj.setId(pluggyAccountDTO.getId());
            obj.setPluggyItem(pluggyAccountDTO.getItemId());

            pluggyAccountBloqueioDAO = new PluggyAccountBloqueio(Conexao.getFromSession());
            pluggyAccountBloqueioDAO.incluir(obj);

            montarSucessoGrowl("Item bloqueado com sucesso!");

            //atualizar objeto da tela
            for (PluggyConnectorDTO item : getConectoresAtivosPluggy()) {
                for (PluggyAccountDTO account : item.getPluggyItemVO().getPluggyAccountsDTO())
                    if (account.getId().equals(pluggyAccountDTO.getId()))
                        account.setBloqueadaParaExibicao(true);
            }

        } catch (Exception ex) {
            montarErro(ex.getMessage());
        } finally {
            pluggyAccountBloqueioDAO = null;
        }
    }

    public boolean isPossuiAccountsConectadas() {
        return !UteisValidacao.emptyList(getConectoresAtivosPluggy());
    }

    public void abrirConhecimentoConciliacaoFaciliteNovaAba() {
        setOnComplete("window.open('" + "https://pactosolucoes.com.br/ajuda/conhecimento/conciliacao-bancaria-fypay-contas-a-pagar/" + "', '_blank');");
    }

    public void desbloquearExibicaoAccountPluggy(ActionEvent evt) {
        PluggyAccountBloqueio pluggyAccountBloqueioDAO;
        try {
            PluggyAccountDTO pluggyAccountDTO = (PluggyAccountDTO) evt.getComponent().getAttributes().get("account");

            if (pluggyAccountDTO == null) {
                throw new Exception("Não foi possível fazer o bloqueio da exibição deste item");
            }

            pluggyAccountBloqueioDAO = new PluggyAccountBloqueio(Conexao.getFromSession());
            pluggyAccountBloqueioDAO.excluirById(pluggyAccountDTO.getId());

            montarSucessoGrowl("Item desbloqueado com sucesso!");

            //atualizar objeto da tela
            for (PluggyConnectorDTO item : getConectoresAtivosPluggy()) {
                for (PluggyAccountDTO account : item.getPluggyItemVO().getPluggyAccountsDTO())
                    if (account.getId().equals(pluggyAccountDTO.getId()))
                        account.setBloqueadaParaExibicao(false);
            }

        } catch (Exception ex) {
            montarErro(ex.getMessage());
        } finally {
            pluggyAccountBloqueioDAO = null;
        }
    }

    public String[] getDisplayIdentificadorFront() {
        return displayIdentificadorFront;
    }

    public void setDisplayIdentificadorFront(String[] displayIdentificadorFront) {
        this.displayIdentificadorFront = displayIdentificadorFront;
    }

    public List<SelectItem> getListaTipoProdutoNFSe() {
        return listaTipoProdutoNFSe;
    }

    public void setListaTipoProdutoNFSe(List<SelectItem> listaTipoProdutoNFSe) {
        this.listaTipoProdutoNFSe = listaTipoProdutoNFSe;
    }

    public List<SelectItem> getListaTipoProduto() {
        return listaTipoProduto;
    }

    public void setListaTipoProduto(List<SelectItem> listaTipoProduto) {
        this.listaTipoProduto = listaTipoProduto;
    }

    public String getCodigoTipoProduto() {
        return codigoTipoProduto;
    }

    public void setCodigoTipoProduto(String codigoTipoProduto) {
        this.codigoTipoProduto = codigoTipoProduto;
    }

    public String getCodigoTipoProdutoNFCe() {
        return codigoTipoProdutoNFCe;
    }

    public void setCodigoTipoProdutoNFCe(String codigoTipoProdutoNFCe) {
        this.codigoTipoProdutoNFCe = codigoTipoProdutoNFCe;
    }

    public List<SelectItem> getListaTipoProdutoNFCe() {
        return listaTipoProdutoNFCe;
    }

    public void setListaTipoProdutoNFCe(List<SelectItem> listaTipoProdutoNFCe) {
        this.listaTipoProdutoNFCe = listaTipoProdutoNFCe;
    }

    public List<SelectItem> getListaSelectItemtipoParcelasCobrarVendaSite() {
        List<SelectItem> lista = new ArrayList<>();
        for (TipoObjetosCobrarEnum tipo : TipoObjetosCobrarEnum.values()) {
            if (!tipo.equals(TipoObjetosCobrarEnum.NENHUM) && !tipo.equals(TipoObjetosCobrarEnum.TIPOS_PRODUTOS)) {
                lista.add(new SelectItem(tipo, tipo.getDescricao()));
            }
        }
        return lista;
    }

    public List<SelectItem> getListaSelectItemtipoParcelasCobrarVendaSiteRegua() {
        List<SelectItem> lista = new ArrayList<>();
        for (TipoObjetosCobrarEnum tipo : TipoObjetosCobrarEnum.values()) {
            if (!tipo.equals(TipoObjetosCobrarEnum.NENHUM) && !tipo.equals(TipoObjetosCobrarEnum.TIPOS_PRODUTOS)) {
                lista.add(new SelectItem(tipo, tipo.getDescricao()));
            }
        }
        return lista;
    }

    public List<SelectItem> getLocales(){
        List<SelectItem> is = new ArrayList<>();
        is.add(new SelectItem("", ""));
        for(LocaleEnum le : LocaleEnum.values()){
            is.add(new SelectItem(le.getLocale(), le.getDescricao()));
        }
        return is;
    }

    public String getScriptEmail(){
        return  scriptEmail;
    }

    public void setScriptEmail(String scriptEmail){
        this.scriptEmail= scriptEmail;
    }

    public String getScriptFormulario(){
        return  scriptFormulario;
    }

    public String getEmailWordPress() {
        return emailWordPress;
    }

    public void setEmailWordPress(String emailWordPress) {
        this.emailWordPress = emailWordPress;
    }

    public void setScriptFormulario(String scriptFormulario){
        this.scriptFormulario = scriptFormulario;
    }

    public void gerarScripEmail() throws Exception {
        String chaveEmpresa = getKey();
        String empresa = getEmpresaVO().getCodigo().toString();
        setScriptEmail(""+empresa+";[nome];[email];[telefone];" +
                "COLOQUE_O_NOME_DA_LANDING_PAGE_AQUI;COLOQUE_A_URL_DA_LANDING_PAGE_AQUI;"+chaveEmpresa+"");

        setScriptFormulario("<label> Seu nome (obrigatório) [text* nome] </label>" +
                " <label> Seu e-mail (obrigatório) [email* email] </label> " +
                "<label> Seu telefone (obrigatório) [tel* telefone] </label> " +
                "[submit \"Enviar\"]");

        setEmailWordPress("<EMAIL>");
    }

    public List<SelectItem> getListaSelectItemTipoEmpresaAsaas() {
        return listaSelectItemTipoEmpresaAsaas;
    }

    public void setListaSelectItemTipoEmpresaAsaas(List<SelectItem> listaSelectItemTipoEmpresaAsaas) {
        this.listaSelectItemTipoEmpresaAsaas = listaSelectItemTipoEmpresaAsaas;
    }

    public List<SelectItem> getListaSelectItemContaEmpresa() {
        return listaSelectItemContaEmpresa;
    }

    public void setListaSelectItemContaEmpresa(List<SelectItem> listaSelectItemContaEmpresa) {
        this.listaSelectItemContaEmpresa = listaSelectItemContaEmpresa;
    }

    public boolean isPossuiCredenciaisPJBank() {
        return possuiCredenciaisPJBank;
    }

    public void setPossuiCredenciaisPJBank(boolean possuiCredenciaisPJBank) {
        this.possuiCredenciaisPJBank = possuiCredenciaisPJBank;
    }

    public boolean isPossuiMerchantPagoLivre() {
        return getConvenioCobrancaPagoLivre() != null && !UteisValidacao.emptyNumber(getConvenioCobrancaPagoLivre().getCodigo());
    }

    public boolean isPossuiMerchantFacilitePay() {
        return getConvenioCobrancaFacilitePay() != null && !UteisValidacao.emptyNumber(getConvenioCobrancaFacilitePay().getCodigo());
    }

    public BancoVO getBancoVO() {
        return bancoVO;
    }

    public void setBancoVO(BancoVO bancoVO) {
        this.bancoVO = bancoVO;
    }

    public void clonar() {
        try {
            limparMsg();
            empresaVO = (EmpresaVO) SerializationUtils.clone(empresaVO);
            empresaVO.setObjetoVOAntesAlteracao(new EmpresaVO());
            empresaVO.setCodigo(0);
            empresaVO.setNovoObj(true);
            empresaVO.setNome("CÓPIA - " + getEmpresaVO().getNome());
            empresaVO.setRazaoSocial("CÓPIA - " + getEmpresaVO().getRazaoSocial());
            empresaVO.setCreditoDCC(0);
            empresaVO.setCodigoGymPass("");
            empresaVO.setTokenApiGymPass("");
            empresaVO.setTokenSMS("");
            empresaVO.setTokenBuzzLead("");
            empresaVO.setChaveNFSe("");
            empresaVO.setServiceUsuario("");
            empresaVO.setServiceSenha("");
            empresaVO.setTempoSaidaAcademiaFormatada(Uteis.converteMinutosParaHora(empresaVO.getTempoSaidaAcademia()));
            empresaVO.setCodEmpresaFinanceiro(null);
            empresaVO.setConfiguracaoNotaFiscalNFSe(null);
            empresaVO.setConfiguracaoNotaFiscalNFCe(null);
            empresaVO.setIntegracaoSpiviHabilitada(false);
            empresaVO.setEmpresaConciliadora("");
            empresaVO.setSenhaConciliadora("");
            empresaVO.setConfiguracaoReenvioMovParcelaEmpresaVOS(new ArrayList<>());
            empresaVO.setContaCorrenteEmpresaVOs(new ArrayList<>());
            empresaVO.setConfigEstacionamento(new EmpresaConfigEstacionamentoVO());
            empresaVO.setParceiroFidelidade(new ParceiroFidelidadeVO());
            empresaVO.setConfigsPersonal(new ConfiguracaoGestaoPersonalVO());
            empresaVO.setConfiguracaoRDStation(new ConfiguracaoEmpresaRDStationVO());
            montarSucessoGrowl("Dados clonados com sucesso!");
        } catch (Exception ex) {
            Logger.getLogger(EmpresaControle.class.getName()).log(Level.SEVERE, null, ex);
            montarErro(ex);
        }
    }

    public String getCallBackRDStation() {
        return callBackRDStation;
    }

    public void setCallBackRDStation(String callBackRDStation) {
        this.callBackRDStation = callBackRDStation;
    }

    public Integer getCodigoFinanceiro() {
        if (codigoFinanceiro == null) {
            codigoFinanceiro = 0;
        }
        return codigoFinanceiro;
    }

    public void setCodigoFinanceiro(Integer codigoFinanceiro) {
        this.codigoFinanceiro = codigoFinanceiro;
    }

    public void marcouHabilitarReenvioAutomaticoRemessa() {
        if (getEmpresaVO().isHabilitarReenvioAutomaticoRemessa()) {
            getEmpresaVO().setTentativaUnicaDeCobranca(false);
        }
    }

    public List<String> getTiposProdutosSelecionados() {
        if (tiposProdutosSelecionados == null) {
            tiposProdutosSelecionados = new ArrayList<>();
        }
        return tiposProdutosSelecionados;
    }

    public void setTiposProdutosSelecionados(List<String> tiposProdutosSelecionados) {
        this.tiposProdutosSelecionados = tiposProdutosSelecionados;
    }

    public List<SelectItem> getTiposProdutosDisponiveis() {
        return TipoProduto.getTiposProdutosParaAutorizacaoCobranca(false);
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public void abrirModalTiposProduto() {
        try {
            limparMsg();
            setOnComplete("");
            setTiposProdutosSelecionados(new ArrayList<>());

            String[] tipos = getEmpresaVO().getTiposProduto().split(",");
            for (String tipo : tipos) {
                if (!UteisValidacao.emptyString(tipo)) {
                    getTiposProdutosSelecionados().add(tipo);
                }
            }

            setOnComplete("Richfaces.showModalPanel('modalTipoProdutoEmpresa')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void confirmarTiposProduto() {
        try {
            limparMsg();
            setOnComplete("");
            getEmpresaVO().setTiposProduto("");


            for (String tipo : getTiposProdutosSelecionados()) {
                if (!UteisValidacao.emptyString(tipo)) {
                    getEmpresaVO().setTiposProduto(getEmpresaVO().getTiposProduto() + "," + tipo);
                }
            }
            getEmpresaVO().setTiposProduto(getEmpresaVO().getTiposProduto().replaceFirst(",", ""));

            setOnComplete("Richfaces.hideModalPanel('modalTipoProdutoEmpresa')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void replicarConfigAcessoSomenteComAgendamento() {
        try {
            setMsgAlert("");
            limparMsg();

            getFacade().getEmpresa().alterarAcessoSomenteComAgendamentoTodasEmpresas(getEmpresaVO().isAcessoSomenteComAgendamento());

            montarSucessoGrowl("Configuração foi replicada para todas as empresas com sucesso!");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void abrirCreditoPacto() {
        try {
            getEmpresaVO().setQtdCreditoPix(getFacade().getCreditoDCC().consultarItensCobrancaPactoPixTotal(getEmpresaVO().getCodigo(), getEmpresaVO().getTipoCobrancaPacto(), getMesReferenciaConsultaCreditoPacto()));
            getEmpresaVO().setQtdCreditoTransacao(getFacade().getCreditoDCC().consultarItensCobrancaPactoTransacaoTotal(getEmpresaVO().getCodigo(), getEmpresaVO().getTipoCobrancaPacto(), getMesReferenciaConsultaCreditoPacto()));
            getEmpresaVO().setQtdCreditoRemessa(getFacade().getCreditoDCC().consultarItensCobrancaPactoRemessasItemTotal(getEmpresaVO().getCodigo(), getEmpresaVO().getTipoCobrancaPacto(), getMesReferenciaConsultaCreditoPacto()));
            getEmpresaVO().setLogCobrancaPacto(getFacade().getLogCobrancaPacto().consultarTodas(getEmpresaVO().getCodigo(), null, Uteis.NIVELMONTARDADOS_DADOSBASICOS));

            if (getEmpresaVO().getTipoCobrancaPactoPosPagoMensal()) {
                getEmpresaVO().setConfigCobrancaMensalJSON(getFacade().getCreditoDCC().consultarConfigCobrancaMensalJSON(getKey(), getEmpresaVO()));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public boolean isAlterouRetentativa() {
        return alterouRetentativa;
    }

    public void setAlterouRetentativa(boolean alterouRetentativa) {
        this.alterouRetentativa = alterouRetentativa;
    }

    private void gravarLogConfiguracaoRetentativa() throws Exception {
        try {
            if (!isAlterouRetentativa()) {
                return;
            }

            getFacade().getConfiguracaoReenvioMovParcelaEmpresa().gravarLogConfiguracaoRetentativa(this.empresaVO, getUsuarioLogado(),
                    this.empresaVO.getConfiguracaoReenvioMovParcelaEmpresaVOSAntesAlteracao(),
                    this.empresaVO.getConfiguracaoReenvioMovParcelaEmpresaVOS());

            setAlterouRetentativa(false);
        } catch (Exception e) {
            registrarLogErroObjetoVO("EMPRESA", empresaVO.getCodigo(), "ERRO AO GERAR LOG DE CONFIGURACAO RETENTATIVA", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    private StringBuilder obterLogListaConfiguracaoReenvioMovParcelaEmpresaVO(List<ConfiguracaoReenvioMovParcelaEmpresaVO> lista) {
        StringBuilder log = new StringBuilder();
        if (UteisValidacao.emptyList(lista)) {
            return log;
        }
        for (ConfiguracaoReenvioMovParcelaEmpresaVO obj : lista) {
            log.append("Posição: ").append(obj.getPosicao()).append(" \n");
            log.append("Convênio: ").append(obj.getConvenioCobrancaVO().getCodigo()).append(" - ").append(obj.getConvenioCobrancaVO().getDescricao()).append(" \n\n");
        }
        return log;
    }

    public List<SelectItem> getListaTipoVigenciaMyWellnessGymPass() {
        if (listaTipoVigenciaMyWellnessGymPass == null || listaTipoVigenciaMyWellnessGymPass.isEmpty()) {
            listaTipoVigenciaMyWellnessGymPass = JSFUtilities.getSelectItemListFromEnum(TipoVigenciaMyWellnessGymPassEnum.class, "descricao", false);
        }
        return listaTipoVigenciaMyWellnessGymPass;
    }

    public void setListaTipoVigenciaMyWellnessGymPass(List<SelectItem> listaTipoVigenciaMyWellnessGymPass) {
        this.listaTipoVigenciaMyWellnessGymPass = listaTipoVigenciaMyWellnessGymPass;
    }

    private void montarListaConvenioCobrancaVerificacaoCartao() {
        try {
            setListaConvenioCobrancaVerificacaoCartao(new ArrayList<>());
            List<ConvenioCobrancaVO> convenios = getFacade().getConvenioCobranca().consultarPorEmpresaESituacao(this.getEmpresaVO().getCodigo(),
                    false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null, SituacaoConvenioCobranca.ATIVO);
            for (ConvenioCobrancaVO obj : convenios) {
                if (obj.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE) &&
                        !obj.getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI)) {
                    this.getListaConvenioCobrancaVerificacaoCartao().add(obj);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            setListaConvenioCobrancaVerificacaoCartao(new ArrayList<>());
        }
    }

    private void montarListaSelectItemTipoEmpresa() {
        setListaSelectItemTipoEmpresa(new ArrayList<>());
        for (TipoEmpresaFinanceiro tipo : TipoEmpresaFinanceiro.values()) {
            getListaSelectItemTipoEmpresa().add(new SelectItem(tipo, tipo.getDescricao()));
        }
    }

    private void montarListaPlanosDelSoft() {
        try {
            setPlanoEmpresaDelSoft(getFacade().getPlano().consultaPlanoDelSoft(empresaVO.getCodigo(), empresaVO.getPlanoAplicacaoDelsoft().getCodigo()));
            setListaSelectPlanosEmpresa(new ArrayList<>());
            List<PlanoVO> listaDePlanos =  getFacade().getPlano().consultarPlanosComBolsa(empresaVO.getCodigo());
            for (PlanoVO plano : listaDePlanos) {
                getListaSelectPlanosEmpresa().add(new SelectItem(plano.getCodigo(), plano.getDescricao()));
            }
        } catch (Exception e) {
        }
    }

    public void atualizarListaConvenioCobranca() {
        try{
            this.setListaConvenioCobrancaVO(getFacade().getConvenioCobranca().consultarPorEmpresaESituacao(this.getEmpresaVO().getCodigo(),
                    false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null, SituacaoConvenioCobranca.ATIVO));
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public List<SelectItem> getListaSelectItemConvenioCartao() {
        List<SelectItem> lista = new ArrayList<>();
        for (ConvenioCobrancaVO obj : getListaConvenioCobrancaVO()) {
            if (!obj.isSomenteExtrato() &&
                    obj.getSituacao().equals(SituacaoConvenioCobranca.ATIVO) &&
                    obj.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE)) {
                lista.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public List<SelectItem> getListaSelectItemConvenioPix() {
        List<SelectItem> lista = new ArrayList<>();
        for (ConvenioCobrancaVO obj : getListaConvenioCobrancaVO()) {
            if (obj.getSituacao().equals(SituacaoConvenioCobranca.ATIVO) && obj.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.PIX)) {
                lista.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public List<SelectItem> getListaSelectItemConvenioBoletoOnline() {
        List<SelectItem> lista = new ArrayList<>();
        for (ConvenioCobrancaVO obj : getListaConvenioCobrancaVO()) {
            if (obj.getSituacao().equals(SituacaoConvenioCobranca.ATIVO) && obj.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO_ONLINE) &&
                    !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE) && !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE)) {
                lista.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public List<SelectItem> getListaSelectItemConvenioCartaoRegua() {
        List<SelectItem> lista = new ArrayList<>();
        for (ConvenioCobrancaVO obj : getListaConvenioCobrancaVO()) {
            if (!obj.isSomenteExtrato() &&
                    obj.getSituacao().equals(SituacaoConvenioCobranca.ATIVO) &&
                    obj.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE)) {
                lista.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public List<SelectItem> getListaSelectItemConvenioPixRegua() {
        List<SelectItem> lista = new ArrayList<>();
        for (ConvenioCobrancaVO obj : getListaConvenioCobrancaVO()) {
            if (obj.getSituacao().equals(SituacaoConvenioCobranca.ATIVO) && obj.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.PIX)) {
                lista.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public List<SelectItem> getListaSelectItemConvenioBoletoOnlineRegua() {
        List<SelectItem> lista = new ArrayList<>();
        for (ConvenioCobrancaVO obj : getListaConvenioCobrancaVO()) {
            if (obj.getSituacao().equals(SituacaoConvenioCobranca.ATIVO) && obj.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO_ONLINE) &&
                    !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE) && !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE)) {
                lista.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public List<SelectItem> getListaTipoAnexoCartaoVacina() {
        if (listaTipoAnexoCartaoVacina == null || listaTipoAnexoCartaoVacina.isEmpty()) {
            listaTipoAnexoCartaoVacina = JSFUtilities.getSelectItemListFromEnum(PessoaAnexoEnum.class, "descricao", false);
        }
        return listaTipoAnexoCartaoVacina;
    }

    public List<SelectItem> getListaSelectItemModalidadeDayUse() {
        if (listaSelectItemModalidadeDayUse == null) {
            listaSelectItemModalidadeDayUse = new ArrayList<>();
        }
        return listaSelectItemModalidadeDayUse;
    }

    public void setListaSelectItemModalidadeDayUse(List<SelectItem> listaSelectItemModalidadeDayUse) {
        this.listaSelectItemModalidadeDayUse = listaSelectItemModalidadeDayUse;
    }

    public List<SelectItem> getListaSelectItemProdutoDayUse() {
        if (listaSelectItemProdutoDayUse == null) {
            listaSelectItemProdutoDayUse = new ArrayList<>();
        }
        return listaSelectItemProdutoDayUse;
    }

    public void setListaSelectItemProdutoDayUse(List<SelectItem> listaSelectItemProdutoDayUse) {
        this.listaSelectItemProdutoDayUse = listaSelectItemProdutoDayUse;
    }


    public List<SelectItem> getListaSelectItemTipoPlano() {
        if (listaSelectItemTipoPlano == null) {
            listaSelectItemTipoPlano = new ArrayList<>();
        }
        return listaSelectItemTipoPlano;
    }

    public void setListaSelectItemTipoPlano(List<SelectItem> listaSelectItemTipoPlano) {
        this.listaSelectItemTipoPlano = listaSelectItemTipoPlano;
    }

    public List<SelectItem> getListaSelectItemAmbiente() {
        return AmbienteEnum.obterListSelectItem();
    }

    public boolean isEditandoMerchantPagoLivre() {
        return editandoMerchantPagoLivre;
    }

    public void setEditandoMerchantPagoLivre(boolean editandoMerchantPagoLivre) {
        this.editandoMerchantPagoLivre = editandoMerchantPagoLivre;
    }

    public boolean isEditandoContaBancariaPagoLivre() {
        return editandoContaBancariaPagoLivre;
    }

    public void setEditandoContaBancariaPagoLivre(boolean editandoContaBancariaPagoLivre) {
        this.editandoContaBancariaPagoLivre = editandoContaBancariaPagoLivre;
    }

    public ProdutoVO getProdutoEstacionamento() {
        if (produtoEstacionamento == null) {
            produtoEstacionamento = new ProdutoVO();
        }
        return produtoEstacionamento;
    }

    public void setProdutoEstacionamento(ProdutoVO produtoEstacionamento) {
        this.produtoEstacionamento = produtoEstacionamento;
    }

    public boolean isPossuiCadastroAsaas() {
        return possuiCadastroAsaas;
    }

    public void setPossuiCadastroAsaas(boolean possuiCadastroAsaas) {
        this.possuiCadastroAsaas = possuiCadastroAsaas;
    }

    public AsaasEmpresaVO getAsaasEmpresaVO() {
        if (asaasEmpresaVO == null) {
            return new AsaasEmpresaVO();
        }
        return asaasEmpresaVO;
    }

    public SituacaoCadastralContaAsaasDTO getSituacaoCadastralContaAsaasDTO() {
        return situacaoCadastralContaAsaasDTO;
    }

    public void setSituacaoCadastralContaAsaasDTO(SituacaoCadastralContaAsaasDTO situacaoCadastralContaAsaasDTO) {
        this.situacaoCadastralContaAsaasDTO = situacaoCadastralContaAsaasDTO;
    }

    public DadosComerciaisContaAsaasDTO getDadosComerciaisContaAsaasDTO() {
        return dadosComerciaisContaAsaasDTO;
    }

    public void setDadosComerciaisContaAsaasDTO(DadosComerciaisContaAsaasDTO dadosComerciaisContaAsaasDTO) {
        this.dadosComerciaisContaAsaasDTO = dadosComerciaisContaAsaasDTO;
    }

    public TaxasContaAsaasDTO getTaxasContaAsaasDTO() {
        return taxasContaAsaasDTO;
    }

    public void setTaxasContaAsaasDTO(TaxasContaAsaasDTO taxasContaAsaasDTO) {
        this.taxasContaAsaasDTO = taxasContaAsaasDTO;
    }

    public void setAsaasEmpresaVO(AsaasEmpresaVO asaasEmpresaVO) {
        this.asaasEmpresaVO = asaasEmpresaVO;
    }

    public String getTitleTaxaNotificacoesAsaas() {
        return "O valor é cobrado uma vez por aluno. Para desativar a cobrança dessa taxa para cada aluno </br> é necessário que a configuração \"Enviar Notificações Email/SMS para o cliente\" esteja desmarcada.";
    }

    public String getCobrancaOnlineAutomaticamenteCaixaEmAberto() {
        StringBuilder sb = new StringBuilder();
        sb.append("Com essa configuração <b>marcada</b>, quando estiver no <b>caixa em aberto</b> e clicar para receber uma <b>parcela de um contrato recorrência</b>, </br>");
        sb.append("a primeira tela à ser apresentada será a de Pagamento Online, ou seja, <b>não irá passar pela tela de Formas de Pagamento</b>.</br></br>");
        sb.append("Com essa configuração <b>desmarcada</b>, quando estiver no <b>caixa em aberto</b> e clicar para receber a parcela <b>(independente do tipo dessa parcela)</b>, </br>");
        sb.append("será sempre redirecionado para a tela de Formas de Pagamento normalmente.</b>");
        return sb.toString();
    }

    public String getCobrancaOnlineAutomaticamenteFormaDePagamento() {
        StringBuilder sb = new StringBuilder();
        sb.append("Com essa configuração <b>marcada</b>, quando estiver na <b>tela de Formas de Pagamento</b>, ao selecionar uma Forma de Pagamento de <b>Cartão de Crédito</b>,</br>");
        sb.append("depois selecionar adquirente, operadora e quantidade de parcelas, será redirecionado automaticmaente para a tela de Pagamento Online.</br>");
        sb.append("<b>Obs:</b> Necessário que a operadora selecionada tenha bandeira configurada.</br></br>");
        sb.append("Com essa configuração <b>desmarcada</b>, quando estiver na <b>tela de Formas de Pagamento</b>, ao selecionar uma Forma de Pagamento de <b>Cartão de Crédito</b>,</br>");
        sb.append("independente de você selecionar adquirente, operadora e quantidade de parcelas, você <b>não</b> será redirecionado automaticamente para a tela de Pagamento Online.</br>");
        sb.append("<b>Obs:</b> Caso queira usar a tela de Pagamento Online neste cenário, basta você clicar no botão \"Pagamento Online\" que aparece assim que você seleciona a forma de pagamento Cartão de Crédito.</br></br>");
        return sb.toString();
    }

    public List<ProdutoVO> executarAutocompleteProduto(Object suggest) {
        List<ProdutoVO> result = new ArrayList<>();
        try {
            String pref = (String) suggest;
            result = getFacade().getProduto().consultarPorDescricaoTipoProdutoAtivo(pref, "SE", true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            result.removeAll(getEmpresaVO().getConfigEstacionamento().getProdutosVOAdicionar());
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void selecionarProdutoSuggestionBox() throws Exception {
        ProdutoVO produtoVO = (ProdutoVO) request().getAttribute("result");
        if (produtoVO != null) {
            setProdutoEstacionamento(produtoVO);
        }
    }

    public void testarIntegracaoEstacionamento() {
        limparMsg();
        try {
            if (getEmpresaVO().getConfigEstacionamento().getPass().isEmpty()) {
                getEmpresaVO().getConfigEstacionamento().setPass(senhaAntesAlterar);
            }

            IntegracaoEstacionamento.gerarArquivoEnviarFTP(false,
                    getEmpresaVO().getConfigEstacionamento(),
                    getFacade().getContrato().getCon(),
                    getEmpresaVO().getCodigo());

            montarSucessoGrowl("Arquivo entregue ao destinatário");
        } catch (Exception ex) {
            montarErro(ex);
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
    }

    public void testarIntegracaoF360() {
        limparMsg();
        try {
            getEmpresaVO().validarDadosF360(getEmpresaVO());

            IntegracaoF360.gerarArquivoEnviarFTPTeste(getEmpresaVO().getIntegracaoF360FtpServer(),
                    getEmpresaVO().getIntegracaoF360User(),
                    getEmpresaVO().getIntegracaoF360Password(),
                    getEmpresaVO().getIntegracaoF360FtpPort(),
                    getEmpresaVO().getIntegracaoF360Dir(),
                    getFacade().getContrato().getCon());

            montarSucessoGrowl("Arquivo entregue ao destinatário");
        } catch (Exception ex) {
            montarErro(ex);
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
    }

    public void adicionarProdutoEstacionamento() throws Exception {
        boolean existe = false;
        for (ProdutoVO produtoVO : getEmpresaVO().getConfigEstacionamento().getProdutosVOAdicionar()) {
            if (produtoVO.getCodigo().equals(getProdutoEstacionamento().getCodigo())) {
                existe = true;
                break;
            }
        }

        if (!existe && getProdutoEstacionamento().getCodigo() > 0) {
            getEmpresaVO().getConfigEstacionamento().getProdutosVOAdicionar().add(getProdutoEstacionamento());
        }
        setProdutoEstacionamento(new ProdutoVO());
    }

    public void removerProdutoEstacionamento() throws Exception {
        ProdutoVO obj = (ProdutoVO) context().getExternalContext().getRequestMap().get("produto");
        getEmpresaVO().getConfigEstacionamento().getProdutosVOAdicionar().remove(obj);
        setMensagemID("msg_dados_excluidos");
    }

    public String getBoletoNomeCPFResponsavelTitle() {
        StringBuilder sb = new StringBuilder();
        sb.append("Marque essa configuração caso queira que no momento de gerar o Boleto, seja gerado no nome e também no CPF do responsável<br>");
        sb.append("pelo aluno CASO ESTE ALUNO FOR MENOR DE IDADE E TIVER UM RESPONSÁVEL VINCULADO.");
        sb.append("<br> Se o aluno não tiver responsável vinculado, for menor de idade e também sem CPF cadastrado, o Boleto não poderá ser gerado.");
        return sb.toString();
    }

    public String getBoletoNomeCPFResponsavelMaiorTitle() {
        StringBuilder sb = new StringBuilder();
        sb.append("Marque essa configuração caso queira que no momento de gerar o Boleto, seja gerado no nome e também no CPF do responsável<br>");
        sb.append("pelo aluno CASO ESTE ALUNO FOR MAIOR DE IDADE, ESTAR SEM CPF PREENCHIDO E TER UM RESPONSÁVEL VINCULADO.");
        sb.append("<br> Se o aluno não tiver responsável vinculado, for maior de idade e também sem CPF cadastrado, o Boleto não poderá ser gerado.");
        return sb.toString();
    }

    public String getPixNomeCPFResponsavelMenorTitle() {
        StringBuilder sb = new StringBuilder();
        sb.append("Marque essa configuração caso queira que no momento de gerar o Pix, seja gerado no nome e também no CPF do responsável</br>");
        sb.append("pelo aluno CASO ESTE ALUNO FOR MENOR DE IDADE E TIVER UM RESPONSÁVEL VINCULADO.");
        sb.append("<br> Se o aluno não tiver responsável vinculado, for menor de idade e também sem CPF cadastrado, o Pix não poderá ser gerado.");
        return sb.toString();
    }

    public String getPixNomeCPFResponsavelMaiorTitle() {
        StringBuilder sb = new StringBuilder();
        sb.append("Marque essa configuração caso queira que no momento de gerar o Pix, seja gerado no nome e também no CPF do responsável<br>");
        sb.append("pelo aluno CASO ESTE ALUNO FOR MAIOR DE IDADE, ESTAR SEM CPF PREENCHIDO E TER UM RESPONSÁVEL VINCULADO.");
        sb.append("<br> Se o aluno não tiver responsável vinculado, for maior de idade e também sem CPF cadastrado, o Pix não poderá ser gerado.");
        return sb.toString();
    }

    public String getMultaJurosAsaasTitle() {
        StringBuilder sb = new StringBuilder();
        sb.append("<b>Atenção</b></br>");
        sb.append("Marque essa configuração apenas se utilizar a integração com o Asaas (geração de boleto e pix ASAAS) e queira configurar Multa e Juros.</br>");
        sb.append("No Asaas, a multa/juros é sempre em porcentual, por isso é necessário configurar multa/juros para integração Asaas aqui separadamente da padrão do sistema.</br>");
        return sb.toString();
    }

    public String getQtdDiasVencBoletoTitle() {
        StringBuilder sb = new StringBuilder();
        sb.append("Informe a quantidade de dias que será acrescida no vencimento do boleto em relação a data atual.</br>");
        sb.append("<b>Ex:</b> Caso definido 5 dias, o vencimento do boleto gerado Hoje ficará para o dia " + Uteis.getData(Uteis.somarDias(Calendario.hoje(), 5)));
        sb.append("</br> Você também poderá definir uma data personalizada no momento da geração caso preferir ou então</br>");
        sb.append(" gerar boletos em massa com vencimentos de acordo com os vencimentos de cada parcela do aluno");
        return sb.toString();
    }

    public void acoesAbaFacilitePay() throws Exception {
        try {
            setPossuiModuloFacilitePay(false);
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            if (loginControle != null && !loginControle.isApresentarModuloFacilitePay()) {
                throw new Exception("Você ainda não possui Integração Fypay habilitada! Entre em contato com a Pacto para adquirir!");
            } else {
                setPossuiModuloFacilitePay(true);
            }
            carregarDadosMerchantPagoLivreGeral(TipoConvenioCobrancaEnum.DCC_FACILITEPAY);
        } catch (Exception ex) {
            montarAviso(ex.getMessage());
        }
    }

    public boolean isPossuiModuloFacilitePay() {
        return possuiModuloFacilitePay;
    }

    public void setPossuiModuloFacilitePay(boolean possuiModuloFacilitePay) {
        this.possuiModuloFacilitePay = possuiModuloFacilitePay;
    }

    public boolean isEditandoMerchantFacilitePay() {
        return editandoMerchantFacilitePay;
    }

    public void setEditandoMerchantFacilitePay(boolean editandoMerchantFacilitePay) {
        this.editandoMerchantFacilitePay = editandoMerchantFacilitePay;
    }

    public boolean isEditandoContaBancariaFacilitePay() {
        return editandoContaBancariaFacilitePay;
    }

    public void setEditandoContaBancariaFacilitePay(boolean editandoContaBancariaFacilitePay) {
        this.editandoContaBancariaFacilitePay = editandoContaBancariaFacilitePay;
    }

    public ConvenioCobrancaVO getConvenioCobrancaFacilitePay() {
        return convenioCobrancaFacilitePay;
    }

    public void setConvenioCobrancaFacilitePay(ConvenioCobrancaVO convenioCobrancaFacilitePay) {
        this.convenioCobrancaFacilitePay = convenioCobrancaFacilitePay;
    }

    public String getCnpjFacilitePay() {
        if (cnpjFacilitePay == null) {
            cnpjFacilitePay = "";
        }
        return cnpjFacilitePay;
    }

    public void setCnpjFacilitePay(String cnpjFacilitePay) {
        this.cnpjFacilitePay = cnpjFacilitePay;
    }

    public String getEmailFacilitePay() {
        if (UteisValidacao.emptyString(getEmailUserFacilitePay())) {
            return "informe o email do usuário do portal primeiro";
        }
        try {
            String inicioEmail = getKey() + "-" + getEmpresaVO().getCodigo();
            return inicioEmail + "@fypay.com.br";
        } catch (Exception ex) {
            return "informe o email do usuário do portal primeiro";
        }
    }

    public void setEmailFacilitePay(String emailFacilitePay) {
        this.emailFacilitePay = emailFacilitePay;
    }

    public String getNomeEmpresaFacilitePay() {
        if (nomeEmpresaFacilitePay == null) {
            nomeEmpresaFacilitePay = "";
        }
        return nomeEmpresaFacilitePay;
    }

    public void setNomeEmpresaFacilitePay(String nomeEmpresaFacilitePay) {
        this.nomeEmpresaFacilitePay = nomeEmpresaFacilitePay;
    }

    public Integer getDddTelefoneFacilitePay() {
        return dddTelefoneFacilitePay;
    }

    public void setDddTelefoneFacilitePay(Integer dddTelefoneFacilitePay) {
        this.dddTelefoneFacilitePay = dddTelefoneFacilitePay;
    }

    public String getTelefoneFacilitePay() {
        if (telefoneFacilitePay == null) {
            telefoneFacilitePay = "";
        }
        return telefoneFacilitePay;
    }

    public void setTelefoneFacilitePay(String telefoneFacilitePay) {
        this.telefoneFacilitePay = telefoneFacilitePay;
    }

    public Integer getContaCorrenteFacilitePay() {
        if (contaCorrenteFacilitePay == null) {
            contaCorrenteFacilitePay = 0;
        }
        return contaCorrenteFacilitePay;
    }

    public void setContaCorrenteFacilitePay(Integer contaCorrenteFacilitePay) {
        this.contaCorrenteFacilitePay = contaCorrenteFacilitePay;
    }

    public String getMerchantIdFacilitePay() {
        if (merchantIdFacilitePay == null) {
            merchantIdFacilitePay = "";
        }
        return merchantIdFacilitePay;
    }

    public void setMerchantIdFacilitePay(String merchantIdFacilitePay) {
        this.merchantIdFacilitePay = merchantIdFacilitePay;
    }

    public String getEmailUserFacilitePay() {
        if (emailUserFacilitePay == null) {
            emailUserFacilitePay = "";
        }
        return emailUserFacilitePay;
    }

    public void setEmailUserFacilitePay(String emailUserFacilitePay) {
        this.emailUserFacilitePay = emailUserFacilitePay;
    }

    public String getNomeUserFacilitePay() {
        if (nomeUserFacilitePay == null) {
            nomeUserFacilitePay = "";
        }
        return nomeUserFacilitePay;
    }

    public void setNomeUserFacilitePay(String nomeUserFacilitePay) {
        this.nomeUserFacilitePay = nomeUserFacilitePay;
    }

    public AmbienteEnum getAmbienteFacilitePay() {
        if (ambienteFacilitePay == null) {
            ambienteFacilitePay = AmbienteEnum.PRODUCAO;
        }
        return ambienteFacilitePay;
    }

    public void setAmbienteFacilitePay(AmbienteEnum ambienteFacilitePay) {
        this.ambienteFacilitePay = ambienteFacilitePay;
    }

    public MerchantPagoLivreDto getMerchantFacilitePayDto() {
        return merchantFacilitePayDto;
    }

    public void setMerchantFacilitePayDto(MerchantPagoLivreDto merchantFacilitePayDto) {
        this.merchantFacilitePayDto = merchantFacilitePayDto;
    }

    public String getContaBancariaPagoLivre() {
        if (UteisValidacao.emptyString(contaBancariaPagoLivre)) {
            return "";
        }
        return contaBancariaPagoLivre;
    }

    public void setContaBancariaPagoLivre(String contaBancariaPagoLivre) {
        this.contaBancariaPagoLivre = contaBancariaPagoLivre;
    }

    public String getContaBancariaFacilitePay() {
        if (UteisValidacao.emptyString(contaBancariaFacilitePay)) {
            return "";
        }
        return contaBancariaFacilitePay;
    }

    public void setContaBancariaFacilitePay(String contaBancariaFacilitePay) {
        this.contaBancariaFacilitePay = contaBancariaFacilitePay;
    }

    public String getTextoSenhaPagoLivreOuFacilite() {
        if (UteisValidacao.emptyString(textoSenhaPagoLivreOuFacilite)) {
            return "";
        }
        return textoSenhaPagoLivreOuFacilite;
    }

    public void setTextoSenhaPagoLivreOuFacilite(String textoSenhaPagoLivreOuFacilite) {
        this.textoSenhaPagoLivreOuFacilite = textoSenhaPagoLivreOuFacilite;
    }

    public int getCodigoEdicao() {
        return codigoEdicao;
    }

    public String getAbrirFecharModalTokenOperacao() {
        return abrirFecharModalTokenOperacao;
    }

    public void setAbrirFecharModalTokenOperacao(String abrirFecharModalTokenOperacao) {
        this.abrirFecharModalTokenOperacao = abrirFecharModalTokenOperacao;
    }

    public String getLabelsCamposExibirNoModalTokenOperacao() {
        if (!UteisValidacao.emptyString(labelsCamposExibirNoModalTokenOperacao)) {
            return labelsCamposExibirNoModalTokenOperacao;
        }
        return "";
    }

    public void setLabelsCamposExibirNoModalTokenOperacao(String labelsCamposExibirNoModalTokenOperacao) {
        this.labelsCamposExibirNoModalTokenOperacao = labelsCamposExibirNoModalTokenOperacao;
    }

    public int getQtdCamposSensiveisAlterados() {
        return qtdCamposSensiveisAlterados;
    }

    public void setQtdCamposSensiveisAlterados(int qtdCamposSensiveisAlterados) {
        this.qtdCamposSensiveisAlterados = qtdCamposSensiveisAlterados;
    }

    public String getAbrirFecharModalTokenOperacaoExplicacao() {
        return abrirFecharModalTokenOperacaoExplicacao;
    }

    public void setAbrirFecharModalTokenOperacaoExplicacao(String abrirFecharModalTokenOperacaoExplicacao) {
        this.abrirFecharModalTokenOperacaoExplicacao = abrirFecharModalTokenOperacaoExplicacao;
    }

    public boolean isPodeInformarTokenSMS() {
        return podeInformarTokenSMS;
    }

    public void setPodeInformarTokenSMS(boolean podeInformarTokenSMS) {
        this.podeInformarTokenSMS = podeInformarTokenSMS;
    }

    public boolean isPodeInformarTokenSMSShortCode() {
        return podeInformarTokenSMSShortCode;
    }

    public void setPodeInformarTokenSMSShortCode(boolean podeInformarTokenSMSShortCode) {
        this.podeInformarTokenSMSShortCode = podeInformarTokenSMSShortCode;
    }

    public static Map<String, RedeEmpresaVO> getRedeEmpresaVosJaConsultados() {
        return redeEmpresaVosJaConsultados;
    }

    public static void setRedeEmpresaVosJaConsultados(Map<String, RedeEmpresaVO> redeEmpresaVosJaConsultados) {
        EmpresaControle.redeEmpresaVosJaConsultados = redeEmpresaVosJaConsultados;
    }

    public List<PluggyConnectorDTO> getConectoresAtivosPluggy() {
        if (UteisValidacao.emptyList(conectoresAtivosPluggy)) {
            return new ArrayList<>();
        }
        return conectoresAtivosPluggy;
    }

    public void setConectoresAtivosPluggy(List<PluggyConnectorDTO> conectoresAtivosPluggy) {
        this.conectoresAtivosPluggy = conectoresAtivosPluggy;
    }

    public void validarLatitudeLongitude() throws ConsistirException {
        String latitude = empresaVO.getLatitude();
        String longitude = empresaVO.getLongitude();

        if (!UteisValidacao.emptyString(latitude) && !latitude.matches("\\d+(\\.\\d+)?|-\\d+(\\.\\d+)?")) {
            throw new ConsistirException("Latitude contém caracteres inválidos. Utilize ponto como separador decimal.");
        }
        if (!UteisValidacao.emptyString(latitude)) {
            double latitudeValue = Double.parseDouble(latitude);
            if (latitudeValue < -90 || latitudeValue > 90) {
                throw new ConsistirException("Latitude deve estar entre -90 e 90.");
            }
        }

        if (!UteisValidacao.emptyString(longitude) && !longitude.matches("\\d+(\\.\\d+)?|-\\d+(\\.\\d+)?")) {
            throw new ConsistirException("Longitude contém caracteres inválidos. Utilize ponto como separador decimal.");
        }
        if (!UteisValidacao.emptyString(longitude)) {
            double longitudeValue = Double.parseDouble(longitude);
            if (longitudeValue < -180 || longitudeValue > 180) {
                throw new ConsistirException("Longitude deve estar entre -180 e 180.");
            }
        }
    }

    private Date getMesReferenciaConsultaCreditoPacto() {
        //Tratativa, se o tipo de cobrança for pós pago e a cobrança do mês já tiver sido feita, então consultar os créditos do mês corrente.
        Date mesReferencia = null;
        if(getEmpresaVO() != null && (getEmpresaVO().getDtUltimaCobrancaPacto() != null && Calendario.getMes(getEmpresaVO().getDtUltimaCobrancaPacto()) == Calendario.getMes(new Date())) &&
                getEmpresaVO().getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.POS_PAGO_EFETIVADO_MENSAL.getCodigo())) {
            mesReferencia = new Date();
        }

        return mesReferencia;

    }

    public String getMensagemTooltipsterTotaisCreditoPacto() {
        String msg = "Consulta realizada para o Mês de Referência: ";

        Date mesReferencia = getMesReferenciaConsultaCreditoPacto();

        if(mesReferencia == null) {
            mesReferencia = Calendario.somarMeses(new Date(), -1);
        }

        msg = msg + Calendario.getMesAno(mesReferencia);

        return msg;

    }


}
