/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.basico.clube;

import controle.arquitetura.SuperControle;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Objeto genérico para controlar um ModalPanel de Mensagens
 * Para utilizar esse controlador existe alguns pré-requisitos do controlador que irá integrá-lo:
 * 1. Deverá iniciar os seus parâmetros através do método 'init'
 * 1.1 Titulo: nome que será exibido no título do ModalPanel;
 * 1.2 Control: referência do objeto Controlador que estará integrado no momento (cuidado com conflitos);
 * 1.3 MetodoInvocar: nome do método void sem argumentos que será invocado no botão "SIM";
 * 2.
 * 3.
 *
 * <AUTHOR>
 */
public class MensagemGenericaControle extends SuperControle {

    private String  titulo;
    private String  mensagemApresentar;
    private transient Object control;
    private String  metodoInvocarAoClicarBotaoSim;
    private String  metodoInvocarAoClicarBotaoNao;
    private String  reRenderComponents;
    private String  labelBotaoFecharTela;
    private String  onCompleteBotaoSim;
    private String  onCompleteBotaoNao;
    private String  onCompleteBotaoFechar;
    private Boolean mostrarBotaoFechar = Boolean.TRUE;
    private boolean apresentarModalMensagemGenerica = false;

    public void init(final String titulo, final
                     String mensagemApresentar,
                     Object control,
                     String labelBotaoFecharTela,
                     String onCompleteBotaoFechar,
                     final String reRenderComponents) {
        this.titulo = titulo;
        this.mensagemApresentar = mensagemApresentar;
        this.control = control;
        this.metodoInvocarAoClicarBotaoSim = null;
        this.metodoInvocarAoClicarBotaoNao = null;
        this.labelBotaoFecharTela = labelBotaoFecharTela;
        this.mostrarBotaoFechar = Boolean.TRUE;
        this.onCompleteBotaoFechar = onCompleteBotaoFechar;
        this.reRenderComponents = reRenderComponents + ",mdlMensagemGenerica";
        apresentarModalMensagemGenerica();
    }

    public void init(final String titulo,
                     final String mensagemApresentar,
                     Object control,
                     final String metodoInvocarAoClicarBotaoSim, final String onCompleteBotaoSim,
                     final String metodoInvocarAoClicarBotaoNao, final String onCompleteBotaoNao,
                     final String reRenderComponents) {
        this.titulo = titulo;
        this.mensagemApresentar = mensagemApresentar;
        this.control = control;
        this.mostrarBotaoFechar = Boolean.TRUE;
        this.metodoInvocarAoClicarBotaoSim = metodoInvocarAoClicarBotaoSim;
        this.onCompleteBotaoSim = onCompleteBotaoSim;
        this.metodoInvocarAoClicarBotaoNao = metodoInvocarAoClicarBotaoNao;
        this.onCompleteBotaoNao = onCompleteBotaoNao;
        this.reRenderComponents = reRenderComponents + ",mdlMensagemGenerica";
        this.labelBotaoFecharTela = null;
        this.onCompleteBotaoFechar = null;
        apresentarModalMensagemGenerica();
    }


    public String fechar() throws Exception {
        try {
            esconderModalMensagemGenerica();
            UtilReflection.invoke(control, "fechar");
        } catch (Exception ignored) {
        }

        return "";
    }

    public Object getControl() {
        return control;
    }

    public void setControl(Object control) {
        this.control = control;
    }


    public String getMetodoInvocarAoClicarBotaoSim() {
        return metodoInvocarAoClicarBotaoSim;
    }

    public void setMetodoInvocarAoClicarBotaoSim(String metodoInvocarAoClicarBotaoSim) {
        this.metodoInvocarAoClicarBotaoSim = metodoInvocarAoClicarBotaoSim;
    }

    public String getMetodoInvocarAoClicarBotaoNao() {
        return metodoInvocarAoClicarBotaoNao;
    }

    public void setMetodoInvocarAoClicarBotaoNao(String metodoInvocarAoClicarBotaoNao) {
        this.metodoInvocarAoClicarBotaoNao = metodoInvocarAoClicarBotaoNao;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getReRenderComponents() {
        return reRenderComponents;
    }

    public void setReRenderComponents(String reRenderComponents) {
        this.reRenderComponents = reRenderComponents;
    }


    public String invokeBotaoSim() {
        setMensagemDetalhada("", "");
        try {
            if (this.metodoInvocarAoClicarBotaoSim != null) {
                Object objRetorno = UtilReflection.invoke(control, metodoInvocarAoClicarBotaoSim);
                if ((objRetorno instanceof String)) {
                    return (String) objRetorno;
                }

                SuperControle superControle = (SuperControle) control;
                if (superControle.getErro()) {
                    montarErro(superControle.getMensagemDetalhada());
                }

            }

        } catch (Exception ex) {
            setMensagemDetalhada(ex);
            Logger.getLogger(MensagemGenericaControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            esconderModalMensagemGenerica();
        }
        return null;
    }

    public String invokeBotaoNao() {
        setMensagemDetalhada("", "");
        try {
            if (!UteisValidacao.emptyString(this.metodoInvocarAoClicarBotaoNao)){
                Object objRetorno = UtilReflection.invoke(control, metodoInvocarAoClicarBotaoNao);
                if ((objRetorno != null) && (objRetorno instanceof String)){
                    return (String)objRetorno;
                }
            }

        } catch (Exception ex) {
            setMensagemDetalhada(ex);
            Logger.getLogger(MensagemGenericaControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            esconderModalMensagemGenerica();
        }
        return null;
    }


    public String getMensagemApresentar() {
        return mensagemApresentar;
    }

    public void setMensagemApresentar(String mensagemApresentar) {
        this.mensagemApresentar = mensagemApresentar;
    }

    public String getLabelBotaoFecharTela() {
        return labelBotaoFecharTela;
    }

    public void setLabelBotaoFecharTela(String labelBotaoFecharTela) {
        this.labelBotaoFecharTela = labelBotaoFecharTela;
    }

    public String getOnCompleteBotaoSim() {
        if (onCompleteBotaoSim == null) {
            onCompleteBotaoSim = "";
        }
        return onCompleteBotaoSim;
    }

    public void setOnCompleteBotaoSim(String onCompleteBotaoSim) {
        this.onCompleteBotaoSim = onCompleteBotaoSim;
    }

    public String getOnCompleteBotaoNao() {
        if (onCompleteBotaoNao == null) {
            onCompleteBotaoNao = "";
        }
        return onCompleteBotaoNao;
    }

    public void setOnCompleteBotaoNao(String onCompleteBotaoNao) {
        this.onCompleteBotaoNao = onCompleteBotaoNao;
    }

    public String getOnCompleteBotaoFechar() {
        if (onCompleteBotaoFechar == null) {
            onCompleteBotaoFechar = "";
        }
        return onCompleteBotaoFechar;
    }

    public void setOnCompleteBotaoFechar(String onCompleteBotaoFechar) {
        this.onCompleteBotaoFechar = onCompleteBotaoFechar;
    }

    public Boolean getMostrarBotaoFechar() {
        return mostrarBotaoFechar;
    }

    public void setMostrarBotaoFechar(Boolean mostrarBotaoFechar) {
        this.mostrarBotaoFechar = mostrarBotaoFechar;
    }

    public boolean isApresentarModalMensagemGenerica() {
        return apresentarModalMensagemGenerica;
    }

    public void setApresentarModalMensagemGenerica(boolean apresentarModalMensagemGenerica) {
        this.apresentarModalMensagemGenerica = apresentarModalMensagemGenerica;
    }

    private void apresentarModalMensagemGenerica() {
        this.apresentarModalMensagemGenerica = true;
    }

    private void esconderModalMensagemGenerica() {
        this.apresentarModalMensagemGenerica = false;
    }
}
