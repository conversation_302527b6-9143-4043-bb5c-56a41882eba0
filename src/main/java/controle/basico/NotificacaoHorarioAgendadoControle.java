package controle.basico;

import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import controle.arquitetura.SuperControle;
import controle.crm.MetaCRMControle;
import negocio.comuns.crm.NotificacaoLigacaoAgendadaJSON;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisJSON;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Descrição: Controle responsável pela exibição das mensagens referêntes aos agendamentos de ligações
 * Projeto: ZillyonWeb-T
 *
 * <AUTHOR> - 27/mar/2018 às 14:55
 * Pacto Soluções - Todos os direitos reservados
 */
public class NotificacaoHorarioAgendadoControle extends SuperControle {

    private final static SimpleDateFormat SDF = new SimpleDateFormat("dd/MM/yyyy hh:mm");
    private Date dataUltimaConsulta;
    private List<NotificacaoLigacaoAgendadaJSON> notificacoes;
    private String notificacaoJSON;

    public void carregarNotificacoes() {
        if (notificacoes == null) {
            notificacoes = new ArrayList<>();
        } else {
            notificacoes.clear();
        }
        Date dataAtual = Calendario.hoje();
        if (dataUltimaConsultaDifereAtual(dataAtual)) {
            try {

                notificacoes.addAll(getFacade().getAberturaMeta().consultarAgendamentosLigacao(
                        getUsuarioLogado(),
                        getEmpresaLogado(),
                        dataAtual));

                this.dataUltimaConsulta = dataAtual;
            } catch (Exception e) {
                System.err.println("Erro ao consultar ligações agendadas[" + e.getMessage() + "]");
            }

        }

    }

    public String getNotificacoesJSON() {
        String notificacoesJSON = UteisJSON.toJSON(notificacoes);
        return notificacoesJSON == null ? "{}" : notificacoesJSON;
    }

    public String irParaAgendamento() {
        if (notificacaoJSON != null && !notificacaoJSON.isEmpty()) {
            try {
                JSONObject jsonObject = new JSONObject(notificacaoJSON);
                int codigoAgendamento = jsonObject.getInt("codigoAgendamento");
                MetaCRMControle metaCRMControle = getControlador(MetaCRMControle.class);
                metaCRMControle.consultarMetas();
                metaCRMControle.selecionarMetasUsuario(getUsuarioLogado());
                metaCRMControle.consultarMetasUsuariosSelecionados();
                metaCRMControle.selecionarMetaPorFase(FasesCRMEnum.AGENDAMENTOS_LIGACOES);
                metaCRMControle.consultarHistoricoContatoClienteManualCodigoAgendamento(codigoAgendamento);
                return "telaPrincipalCRM";
            } catch (Exception ex) {
                montarErro(ex);
            }
        }
        return null;
    }


    private boolean dataUltimaConsultaDifereAtual(Date dataAtual) {
        if (dataUltimaConsulta != null) {
            String stringDataUltimaConsulta = SDF.format(dataUltimaConsulta);
            String stringDataAtual = SDF.format(dataAtual);
            return !stringDataUltimaConsulta.equals(stringDataAtual);
        }
        return true;
    }

    public void setNotificacaoJSON(String notificacaoJSON) {
        this.notificacaoJSON = notificacaoJSON;
    }
}
