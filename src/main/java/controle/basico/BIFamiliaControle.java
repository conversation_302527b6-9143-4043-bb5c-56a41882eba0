package controle.basico;

import controle.arquitetura.SuperControle;
import negocio.comuns.basico.FamiliaSinteticoVO;
import negocio.comuns.basico.FamiliaTO;
import negocio.comuns.basico.FamiliarTO;
import negocio.comuns.basico.enumerador.CausaSugestaoEnum;
import negocio.comuns.utilitarias.Uteis;

import javax.faces.context.FacesContext;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BIFamiliaControle extends SuperControle {

    private FamiliaTO sugestao = new FamiliaTO();
    private Map<Integer, CausaSugestaoEnum> sugestoes = new HashMap<Integer, CausaSugestaoEnum>();
    private Integer index = 0;
    private String classe = "";
    private CausaSugestaoEnum causaSugestaoEnum = CausaSugestaoEnum.PAGAMENTO_CONJUNTO;
    private BIFamiliaTO bi;
    private List<FamiliaSinteticoVO> familias;


    public BIFamiliaControle(){

    }

    public void familias() throws Exception{
        familias = getFacade().getFamiliar().familias(TipoBIFamiliaEnum.FAMILIAS, getEmpresaLogado().getCodigo());
    }
    public void familiasAtivas() throws Exception{
        familias = getFacade().getFamiliar().familias(TipoBIFamiliaEnum.FAMILIAS, getEmpresaLogado().getCodigo());
    }
    public void familiasRisco() throws Exception{
        familias = getFacade().getFamiliar().familias(TipoBIFamiliaEnum.FAMILIAS, getEmpresaLogado().getCodigo());
    }
    public void familiasInativo() throws Exception{
        familias = getFacade().getFamiliar().familias(TipoBIFamiliaEnum.FAMILIAS, getEmpresaLogado().getCodigo());
    }

    public void negarSugestao(){
        try {
            getFacade().getFamiliar().negarSugestaoFamilia(sugestao);
            proxima();
            sugestoes = getFacade().getFamiliar().sugestoes();
        }catch (Exception e){
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void confirmarSugestao(){
        try {
            getFacade().getFamiliar().tranformarSugestaoEmFamilia(sugestao, getEmpresaLogado().getCodigo());
            proxima();
            sugestoes = getFacade().getFamiliar().sugestoes();
            gerarBI();
        }catch (Exception e){
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void gerarBI() throws Exception{
        bi = getFacade().getFamiliar().montarGestao(getEmpresaLogado().getCodigo());
    }

    public void abrirSugestoes(){
        setMsgAlert("");
        try {
            index = 0;
            sugestoes = getFacade().getFamiliar().sugestoes();
            navegar();
            gerarBI();
        }catch (Exception e){
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void anterior() throws Exception{
        index = index - 1;
        index = index < 0 ? (sugestoes.size() - 1) : index;
        navegar();
    }

    public void navegar() throws Exception{
        try {
            causaSugestaoEnum = sugestoes.get(new ArrayList<Integer>(sugestoes.keySet()).get(index));
            sugestao = getFacade().getFamiliar().detalheFamiliaSugerida(new ArrayList<Integer>(sugestoes.keySet()).get(index));
            classe = sugestao.getIntegrantes().size() > 3 ? "quatro" : sugestao.getIntegrantes().size() == 3 ? "tres" : "";
        }catch (IndexOutOfBoundsException in){
            in.printStackTrace();
        }
    }

    public void proxima() throws Exception{
        index = index + 1;
        index = index >= sugestoes.size() ? 0 : index;
        navegar();
    }

    public CausaSugestaoEnum getCausaSugestaoEnum() {
        return causaSugestaoEnum;
    }

    public void setCausaSugestaoEnum(CausaSugestaoEnum causaSugestaoEnum) {
        this.causaSugestaoEnum = causaSugestaoEnum;
    }

    public Map<Integer, CausaSugestaoEnum> getSugestoes() {
        return sugestoes;
    }

    public void setSugestoes(Map<Integer, CausaSugestaoEnum> sugestoes) {
        this.sugestoes = sugestoes;
    }

    public FamiliaTO getSugestao() {
        return sugestao;
    }

    public void setSugestao(FamiliaTO sugestao) {
        this.sugestao = sugestao;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getClasse() {
        return classe;
    }

    public void setClasse(String classe) {
        this.classe = classe;
    }

    public BIFamiliaTO getBi() {
        return bi;
    }

    public void setBi(BIFamiliaTO bi) {
        this.bi = bi;
    }

    public List<FamiliaSinteticoVO> getFamilias() {
        return familias;
    }

    public void setFamilias(List<FamiliaSinteticoVO> familias) {
        this.familias = familias;
    }
}
