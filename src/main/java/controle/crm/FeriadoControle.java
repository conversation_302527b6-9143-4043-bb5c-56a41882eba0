package controle.crm;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import controle.plano.PlanoControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.CidadeVO;

import java.util.*;

import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.crm.FeriadoRedeEmpresaVO;
import negocio.comuns.utilitarias.*;
import javax.faces.model.SelectItem;

import controle.arquitetura.SuperControle;

import java.util.logging.Level;
import java.util.logging.Logger;

import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.crm.FeriadoVO;
import negocio.facade.jdbc.crm.Feriado;
import org.json.JSONObject;
import servicos.discovery.RedeDTO;
import servicos.impl.microsservice.cadaux.CadAuxMsService;
import servicos.oamd.OamdMsService;
import servicos.oamd.RedeEmpresaDataDTO;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * feriadoForm.jsp feriadoCons.jsp) com as funcionalidades da classe <code>Feriado</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see Feriado
 * @see FeriadoVO
 */
public class FeriadoControle extends SuperControle {
    private static final String ENTIDADE_LOG = "FERIADO";
    private String oncompleteLog;
    private FeriadoVO feriadoVO;
    private String campoConsultarCidade;
    private String valorConsultarCidade;
    private List listaConsultarCidade;
    private List listaSelectItemEstado;
    private List listaSelectItemCidade;
    private List listaSelectItemPais;
    private Date dataConsulta;
    private Boolean estadoObrigatorio;
    private Boolean cidadeObrigatorio;
    private List<SelectItem> listSelectItemRecorrente;
    private String msgAlert;
    private List<FeriadoRedeEmpresaVO> listaFeriadoRedeEmpresa;
    private ConfiguracaoSistemaVO configuracaoSistema;

    /**
     * Interface <code>FeriadoInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */

    public FeriadoControle() throws Exception {
        setDataConsulta(negocio.comuns.utilitarias.Calendario.hoje());
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        montarSelectItemRecorrente();
        setMensagemID("");
        inicializarConfiguracoesSistema();
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>Feriado</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setFeriadoVO(new FeriadoVO());
        setListaSelectItemCidade(new ArrayList());
        setListaSelectItemEstado(new ArrayList());
        setListaSelectItemPais(new ArrayList());
        setMsgAlert("");
        inicializarListasSelectItemTodosComboBox();
        setEstadoObrigatorio(true);
        setCidadeObrigatorio(true);
        setMensagemID("msg_entre_dados");
        getFeriadoVO().registrarObjetoVOAntesDaAlteracao();
        return "editar";
    }

    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemPais();
        montarListaSelectItemEstado();
        montarListaSelectItemCidade();
    }

    /**
    * Rotina responsável por disponibilizar os dados de um objeto da classe <code>Feriado</code> para alteração.
    * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
    */
    public String editar() throws Exception {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            FeriadoVO obj = getFacade().getFeriado().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            inicializarAtributosRelacionados(obj);
            obj.setNovoObj(new Boolean(false));
            setMsgAlert("");
            setFeriadoVO(obj);
            inicializarListasSelectItemTodosComboBox();
            inicializarTipoFeriado();
            escolherFeriadoEstadual();
            if (isExibirReplicarRedeEmpresa()) {
                prepararListaReplicarEmpresa();
            }
            obj.registrarObjetoVOAntesDaAlteracao();
            setMensagemID("msg_dados_editar");
        } catch (Exception e) {
            setSucesso(true);
            setErro(false);
            setMsgAlert("");
            setMensagemID("msg_dados_editar");
            return "consultar";
        }
        return "editar";
    }

    /**
     * Método responsável inicializar objetos relacionados a classe <code>FeriadoVO</code>.
     * Esta inicialização é necessária por exigência da tecnologia JSF, que não trabalha com valores nulos para estes atributos.
     */
    public void inicializarAtributosRelacionados(FeriadoVO obj) {
        if (obj.getCidade() == null) {
            obj.setCidade(new CidadeVO());
        }
        if (obj.getEstado() == null) {
            obj.setEstado(new EstadoVO());
        }
        if (obj.getPais() == null) {
            obj.setPais(new PaisVO());
        }

    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>Feriado</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public void gravar() {
        try {
            if (feriadoVO.isNovoObj().booleanValue()) {
                getFacade().getFeriado().incluir(feriadoVO);
                incluirLogInclusao();
            } else {
                getFacade().getFeriado().alterar(feriadoVO);
                incluirLogAlteracao();
            }
            getFeriadoVO().registrarObjetoVOAntesDaAlteracao();
            if (isExibirReplicarRedeEmpresa()) {
                prepararListaReplicarEmpresa();
            }
            setMensagemID("msg_dados_gravados");
            montarMsgAlert(getMensagem());
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void escolherFeriadoNacional(){
        if(feriadoVO.getNacional()){
            feriadoVO.setEstadual(false);
            setEstadoObrigatorio(false);
            setCidadeObrigatorio(false);

        } else {
            setEstadoObrigatorio(true);
            setCidadeObrigatorio(true);
        }
    }


    public void escolherFeriadoEstadual(){
        if(feriadoVO.getEstadual()){
            feriadoVO.setNacional(false);
            setEstadoObrigatorio(true);
            setCidadeObrigatorio(false);
        } else {
            setEstadoObrigatorio(true);
            setCidadeObrigatorio(true);
        }
    }

    public Boolean getApresentarCalendarDia(){
        if(getControleConsulta().getCampoConsulta().equals("dia")){
            return true;
        }
        return false;
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP FeriadoCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getFeriado().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getFeriado().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("dia")) {
                objs = getFacade().getFeriado().consultarPorDia(getDataConsulta(), getDataConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nomeCidade")) {
                objs = getFacade().getFeriado().consultarPorNomeCidade(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nomeEstado")) {
                objs = getFacade().getFeriado().consultarPorNomeEstado(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nomePais")) {
                objs = getFacade().getFeriado().consultarPorNomePais(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>FeriadoVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            registrarLogExclusaoObjetoVO(this.feriadoVO, this.feriadoVO.getCodigo().intValue(), ENTIDADE_LOG, 0);
            getFacade().getFeriado().excluir(feriadoVO);
            novo();
            setMensagemID("msg_dados_excluidos");
            return "consultar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /**
     * Método responsável por processar a consulta na entidade <code>Cidade</code> por meio dos parametros informados no richmodal.
     * Esta rotina é utilizada fundamentalmente por requisições Ajax, que realizam busca pelos parâmentros informados no richModal
     * montando automaticamente o resultado da consulta para apresentação.
     */
    public void consultarCidade() {
        try {
            List objs = new ArrayList();
            if (getCampoConsultarCidade().equals("codigo")) {
                if (getValorConsultarCidade().equals("")) {
                    setValorConsultarCidade("0");
                }
                Integer valorInt = Integer.parseInt(getValorConsultarCidade());
                objs = getFacade().getCidade().consultarPorCodigo(valorInt, false, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getCampoConsultarCidade().equals("nome")) {
                objs = getFacade().getCidade().consultarPorNome(getValorConsultarCidade(), false, Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsultarCidade(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarCidade(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarCidade() throws Exception {
        CidadeVO obj = (CidadeVO) context().getExternalContext().getRequestMap().get("cidade");
        if (getMensagemDetalhada().equals("")) {
            this.getFeriadoVO().setCidade(obj);
        }
        Uteis.liberarListaMemoria(this.getListaConsultarCidade());
        this.setValorConsultarCidade(null);
        this.setCampoConsultarCidade(null);
    }

    public void limparCampoCidade() {
        this.getFeriadoVO().setCidade( new CidadeVO());
    }

    /**
     * Rotina responsável por preencher a combo de consulta dos RichModal da telas.
     */
    public List getTipoConsultarComboCidade() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("nomePais", "Pais"));
        itens.add(new SelectItem("estado", "Estado"));
        return itens;
    }

    /**
     * Rotina responsável por atribui um javascript com o método de mascara para campos do tipo Data, CPF, CNPJ, etc.
     */
    public String getMascaraConsulta() {
        return "";
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("dia", "Dia"));
        itens.add(new SelectItem("nomeCidade", "Cidade"));
        itens.add(new SelectItem("nomeEstado", "Estado"));
        itens.add(new SelectItem("nomePais", "País"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setListaConsulta(new ArrayList());
        setMensagemID("");
        return "consultar";
    }

    public void montarListaSelectItemEstado(String prm) throws Exception {
        getFeriadoVO().setPais(this.getFeriadoVO().getPais());
        List resultadoConsulta = consultarEstadoPorCodigoPais(this.getFeriadoVO().getPais().getCodigo().intValue());
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            EstadoVO obj = (EstadoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
        }

        setListaSelectItemEstado(objs);
        if (this.getFeriadoVO().getPais().getCodigo().intValue() == 0) {
            this.getFeriadoVO().getEstado().setCodigo(new Integer(0));
            montarListaSelectItemCidade(prm);
        }

        // setListaSelectItemCidade(new ArrayList());
    }

    public void montarListaSelectItemEstado() {
        try {
            montarListaSelectItemEstado("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public void inicializarTipoFeriado(){
        if(getFeriadoVO().getNaoRecorrente() == null){
            getFeriadoVO().setFeriadoRecorrenteItem("null");
        } else if(getFeriadoVO().getNaoRecorrente()){
            getFeriadoVO().setFeriadoRecorrenteItem("Não");
        } else if(!getFeriadoVO().getNaoRecorrente()){
            getFeriadoVO().setFeriadoRecorrenteItem("Sim");
        }
    }

    public void selecionarTipoFeriado(){
        if(getFeriadoVO().getFeriadoRecorrenteItem().equals("null")){
            getFeriadoVO().setNaoRecorrente(null);
        } else if(getFeriadoVO().getFeriadoRecorrenteItem().equalsIgnoreCase("Não")){
            getFeriadoVO().setNaoRecorrente(true);
        } else if(getFeriadoVO().getFeriadoRecorrenteItem().equalsIgnoreCase("Sim")){
            getFeriadoVO().setNaoRecorrente(false);
        }
    }

    public List consultarEstadoPorCodigoPais(Integer nomePrm) throws Exception {
        List lista = getFacade().getPais().consultarEstadoPorPais(nomePrm, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    public List consultarCidadePorCodigoEstado(Integer nomePrm) throws Exception {
        List lista = getFacade().getCidade().consultarPorCodigoEstado(nomePrm, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    public void montarListaSelectItemCidade(String prm) throws Exception {
        getFeriadoVO().getCidade().setEstado(this.getFeriadoVO().getEstado());
        List resultadoConsulta = consultarCidadePorCodigoEstado(this.getFeriadoVO().getEstado().getCodigo());
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            CidadeVO obj = (CidadeVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
        }
        setListaSelectItemCidade(objs);
    }

    public void montarListaSelectItemCidade() {
        try {
            montarListaSelectItemCidade("");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void montarListaSelectItemPais(String prm) throws Exception {
        List resultadoConsulta = consultarPaisPorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            PaisVO obj = (PaisVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
        }

        setListaSelectItemPais(objs);
    }

    public void montarListaSelectItemPais() {
        try {
            montarListaSelectItemPais("");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public List consultarPaisPorNome(String nomePrm) throws Exception {
        List lista = getFacade().getPais().consultarPorNome(nomePrm, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    /**
     * Inclui o log de inclusão de feriado
     * @throws Exception
     */
    public void incluirLogInclusao() throws Exception {
        try {
            this.feriadoVO.setObjetoVOAntesAlteracao(new FeriadoVO());
            this.feriadoVO.setNovoObj(true);
            registrarLogObjetoVO(this.feriadoVO, this.feriadoVO.getCodigo(), ENTIDADE_LOG, 0);
            this.feriadoVO.setNovoObj(false);
            this.feriadoVO.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            registrarLogErroObjetoVO(ENTIDADE_LOG, this.feriadoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE " + ENTIDADE_LOG, this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de feriado
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
             registrarLogObjetoVO(this.feriadoVO, this.feriadoVO.getCodigo(), ENTIDADE_LOG, 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(ENTIDADE_LOG, this.feriadoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE " + ENTIDADE_LOG, this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }
    private void montarSelectItemRecorrente(){
        listSelectItemRecorrente = new ArrayList<SelectItem>();
        listSelectItemRecorrente.add(new SelectItem("null",""));
        listSelectItemRecorrente.add(new SelectItem("Sim","Sim"));
        listSelectItemRecorrente.add(new SelectItem("Não","Não"));
    }
    /**
     * Consulta de logs de feriado
     */
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Feriado");
        if (feriadoVO.getCodigo() != null && feriadoVO.getCodigo() != 0) {
            loginControle.consultarLogObjetoSelecionado(ENTIDADE_LOG, feriadoVO.getCodigo(), null);
        } else {
            loginControle.setListaConsultaLog(new ArrayList());
            loginControle.getListaConsultaLog().clear();
        }
        setOncompleteLog("abrirPopup('" + request().getContextPath() + "/faces/visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);");

    }

    /**
     * @param oncompleteLog the oncompleteLog to set
     */
    public void setOncompleteLog(String oncompleteLog) {
        if (oncompleteLog == null) {
            oncompleteLog = "";
        }
        this.oncompleteLog = oncompleteLog;
    }

    public String getOncompleteLog() {
        return oncompleteLog;
    }

    /**
     * Operação que libera todos os recursos (atributos, listas, objetos) do backing bean.
     * Garantindo uma melhor atuação do Garbage Coletor do Java. A mesma é automaticamente
     * quando realiza o logout.
     */
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        feriadoVO = null;
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public void replicarFeriadoRedeEmpresa(FeriadoRedeEmpresaVO obj) throws Exception {

        FeriadoRedeEmpresaVO feriadoRedeEmpresaVO = getFacade().getFeriadoRedeEmpresa().consultarPorChaveFeriado(obj.getChave(), feriadoVO.getCodigo());
        if (feriadoRedeEmpresaVO == null) {
            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
            String urlOrigemCadAuxMs = redeEmpresaDataDTO.getServiceUrls().getCadastroAuxiliarUrl();

            JSONObject cloneFeriadoOrigem = CadAuxMsService.clonar(feriadoVO.getCodigo(), urlOrigemCadAuxMs, "feriados", getKey());
            if (!Uteis.isAmbienteDesenvolvimentoTeste()) {
                cloneFeriadoOrigem.put("descricao", cloneFeriadoOrigem.getString("descricao").toUpperCase().replace("CÓPIA DE ", ""));
            }
            feriadoRedeEmpresaVO = new FeriadoRedeEmpresaVO(feriadoVO.getCodigo(), obj.getChave(), null);
            getFacade().getFeriadoRedeEmpresa().inserir(feriadoRedeEmpresaVO);
            // Plano não tem na outra academia da rede, então inclui
            JSONObject novoPlano = CadAuxMsService.replicar(cloneFeriadoOrigem, obj.getRedeDTO().getUrlCadAuxMs(), "feriados", obj.getRedeDTO().getChave(), "");
            feriadoRedeEmpresaVO.setFeriadoReplicado(novoPlano.getInt("codigo"));
            feriadoRedeEmpresaVO.setDataatualizacao(new Date());
            feriadoRedeEmpresaVO.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(feriadoRedeEmpresaVO.getDataatualizacao()));
            obj.setFeriadoReplicado(novoPlano.getInt("codigo"));
            obj.setDataatualizacao(new Date());
            obj.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(obj.getDataatualizacao()));
            getFacade().getFeriadoRedeEmpresa().alterarDataAtualizacao(feriadoVO.getCodigo(), obj.getChave(), novoPlano.getInt("codigo"), obj.getMensagemSituacao());
        } else if (!feriadoRedeEmpresaVO.getDataAtualizacaoInformada()) {
            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
            String urlOrigemCadAuxMs = redeEmpresaDataDTO.getServiceUrls().getCadastroAuxiliarUrl();

            JSONObject cloneFeriadoOrigem = CadAuxMsService.clonar(feriadoVO.getCodigo(), urlOrigemCadAuxMs, "feriados", getKey());
            if (!Uteis.isAmbienteDesenvolvimentoTeste()) {
                cloneFeriadoOrigem.put("descricao", cloneFeriadoOrigem.getString("descricao").toUpperCase().replace("CÓPIA DE ", ""));
            }
            JSONObject novoFeriado = CadAuxMsService.replicar(cloneFeriadoOrigem, obj.getRedeDTO().getUrlCadAuxMs(), "feriados", obj.getRedeDTO().getChave(), "");
            feriadoRedeEmpresaVO.setFeriadoReplicado(novoFeriado.getInt("codigo"));
            feriadoRedeEmpresaVO.setDataatualizacao(new Date());
            feriadoRedeEmpresaVO.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(feriadoRedeEmpresaVO.getDataatualizacao()));
            obj.setFeriadoReplicado(novoFeriado.getInt("codigo"));
            obj.setDataatualizacao(new Date());
            obj.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(obj.getDataatualizacao()));
            getFacade().getFeriadoRedeEmpresa().alterarDataAtualizacao(feriadoVO.getCodigo(), obj.getChave(), novoFeriado.getInt("codigo"), obj.getMensagemSituacao());
        }
    }

    public void replicarFeriadoRedeEmpresaUnica() {
        FeriadoRedeEmpresaVO obj = (FeriadoRedeEmpresaVO) context().getExternalContext().getRequestMap().get("feriadoRedeEmpresaReplicacao");
        try {
            replicarFeriadoRedeEmpresa(obj);
            limparMsg();
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
            obj.setMensagemSituacao("ERRO: " + ex.getMessage());
            try {
                getFacade().getFeriadoRedeEmpresa().alterarMensagemSituacao(feriadoVO.getCodigo(), obj.getChave(), obj.getMensagemSituacao());
            } catch (Exception e) {
            }
            Logger.getLogger(PlanoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public Integer getListaFeriadoRedeEmpresaSize() {
        return getListaFeriadoRedeEmpresa().size();
    }

    public Integer getListaFeriadoRedeEmpresaSincronizado() {
        Integer cont = 0;
        for (FeriadoRedeEmpresaVO unid : getListaFeriadoRedeEmpresa()) {
            if (unid.getDataAtualizacaoInformada()) {
                cont++;
            }
        }
        return cont;
    }

    public void prepararListaReplicarEmpresa() {
        try {
            getListaFeriadoRedeEmpresa().clear();

            Map<String, FeriadoRedeEmpresaVO> mapaUnidadesPorChave = new HashMap<>();

            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());

            for (RedeDTO redeDTO : redeEmpresaDataDTO.getRedeEmpresas()) {
                if (redeDTO.getChave().toLowerCase().trim().equals(getKey().toLowerCase().trim())) {
                    continue;
                }

                FeriadoRedeEmpresaVO feriadoRedeEmpresaVO = getFacade().getFeriadoRedeEmpresa().consultarPorChaveFeriado(redeDTO.getChave(), feriadoVO.getCodigo());

                if (feriadoRedeEmpresaVO == null) {
                    feriadoRedeEmpresaVO = new FeriadoRedeEmpresaVO();
                    feriadoRedeEmpresaVO.setMensagemSituacao("AGUARDANDO REPLICAR FERIADO");
                }

                feriadoRedeEmpresaVO.setChave(redeDTO.getChave().toLowerCase().trim());
                feriadoRedeEmpresaVO.setNomeUnidade(redeDTO.getNomeFantasia());
                feriadoRedeEmpresaVO.setRedeDTO(redeDTO);

                mapaUnidadesPorChave.put(feriadoRedeEmpresaVO.getChave(), feriadoRedeEmpresaVO);
            }
            getListaFeriadoRedeEmpresa().addAll(mapaUnidadesPorChave.values());

            limparMsg();
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
            Logger.getLogger(PlanoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }


    public boolean isExibirReplicarRedeEmpresa() {
        boolean integranteFranqueadoraRedeEmpresa = false;
        boolean permiteReplicarRedeEmpresa = false;
        boolean usuarioAdministrador = false;
        try {
            for (UsuarioPerfilAcessoVO userPerfAcess : getControladorTipado(LoginControle.class).getUsuarioLogado().getUsuarioPerfilAcessoVOs()) {
                if (userPerfAcess.getPerfilAcesso().getNome().toUpperCase().contains("ADMINISTRADOR")) {
                    usuarioAdministrador = true;
                }
            }
        } catch (Exception e) {
            usuarioAdministrador = false;
        }
        try {
            integranteFranqueadoraRedeEmpresa = OamdMsService.integranteFranqueadoraRedeEmpresa(getKey());
            permiteReplicarRedeEmpresa = getFacade().getConfiguracaoSistema().obterReplicarRedeEmpresa("permitirreplicarferiadoredeempresa");
        } catch (Exception e) {
            e.printStackTrace();
        }

        boolean usuarioAdminPacto = false;
        try {
            usuarioAdminPacto = getUsuarioLogado().getUsuarioAdminPACTO();
            if (!usuarioAdminPacto) {
                usuarioAdminPacto = getUsuarioLogado().getUsuarioPACTOBR();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (integranteFranqueadoraRedeEmpresa && !feriadoVO.isNovoObj() && permiteReplicarRedeEmpresa && (usuarioAdminPacto || usuarioAdministrador)) {
            return true;
        } else {
            return false;
        }
    }

    public void inicializarConfiguracoesSistema() throws Exception {
        setConfiguracaoSistema(getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_TODOS));
    }

    public String getCampoConsultarCidade() {
        return campoConsultarCidade;
    }

    public void setCampoConsultarCidade(String campoConsultarCidade) {
        this.campoConsultarCidade = campoConsultarCidade;
    }

    public String getValorConsultarCidade() {
        return valorConsultarCidade;
    }

    public void setValorConsultarCidade(String valorConsultarCidade) {
        this.valorConsultarCidade = valorConsultarCidade;
    }

    public List getListaConsultarCidade() {
        return listaConsultarCidade;
    }

    public void setListaConsultarCidade(List listaConsultarCidade) {
        this.listaConsultarCidade = listaConsultarCidade;
    }

    public FeriadoVO getFeriadoVO() {
        return feriadoVO;
    }

    public void setFeriadoVO(FeriadoVO feriadoVO) {
        this.feriadoVO = feriadoVO;
    }

    /**
     * @return the listaSelectItemEstado
     */
    public List getListaSelectItemEstado() {
        return listaSelectItemEstado;
    }

    /**
     * @param listaSelectItemEstado the listaSelectItemEstado to set
     */
    public void setListaSelectItemEstado(List listaSelectItemEstado) {
        this.listaSelectItemEstado = listaSelectItemEstado;
    }

    /**
     * @return the listaSelectItemPais
     */
    public List getListaSelectItemCidade() {
        return listaSelectItemCidade;
    }

    /**
     * @param listaSelectItemPais the listaSelectItemPais to set
     */
    public void setListaSelectItemCidade(List listaSelectItemCidade) {
        this.listaSelectItemCidade = listaSelectItemCidade;
    }

    /**
     * @return the listaSelectItemPais
     */
    public List getListaSelectItemPais() {
        return listaSelectItemPais;
    }

    /**
     * @param listaSelectItemPais the listaSelectItemPais to set
     */
    public void setListaSelectItemPais(List listaSelectItemPais) {
        this.listaSelectItemPais = listaSelectItemPais;
    }

    /**
     * @return the dataConsulta
     */
    public Date getDataConsulta() {
        return dataConsulta;
    }

    /**
     * @param dataConsulta the dataConsulta to set
     */
    public void setDataConsulta(Date dataConsulta) {
        this.dataConsulta = dataConsulta;
    }

    public Boolean getCidadeObrigatorio() {
        return cidadeObrigatorio;
    }

    public void setCidadeObrigatorio(Boolean cidadeObrigatorio) {
        this.cidadeObrigatorio = cidadeObrigatorio;
    }

    public Boolean getEstadoObrigatorio() {
        return estadoObrigatorio;
    }

    public void setEstadoObrigatorio(Boolean estadoObrigatorio) {
        this.estadoObrigatorio = estadoObrigatorio;
    }

    public List<SelectItem> getListSelectItemRecorrente() {
        return listSelectItemRecorrente;
    }

    public void setListSelectItemRecorrente(List<SelectItem> listSelectItemRecorrente) {
        this.listSelectItemRecorrente = listSelectItemRecorrente;
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Feriado",
                "Deseja excluir o Feriado?",
                this, "excluir", "", "", "", "grupoBtnExcluir,mensagem");
    }

    public List<FeriadoRedeEmpresaVO> getListaFeriadoRedeEmpresa() {
        if (listaFeriadoRedeEmpresa == null) {
            listaFeriadoRedeEmpresa = new ArrayList<>();
        }
        return listaFeriadoRedeEmpresa;
    }

    public void setListaFeriadoRedeEmpresa(List<FeriadoRedeEmpresaVO> listaFeriadoRedeEmpresa) {
        this.listaFeriadoRedeEmpresa = listaFeriadoRedeEmpresa;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }
}
