/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.estoque;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


import negocio.comuns.utilitarias.Uteis;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import java.util.Map;
import javax.faces.model.SelectItem;
import negocio.comuns.basico.EmpresaVO;

import negocio.comuns.estoque.BalancoVO;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 *
 * <AUTHOR>
 */
public class RelatorioBalancoControle extends SuperControleRelatorio {

    private BalancoVO balancoVO = null;
    private String retorno = "";
    private Integer codigoEmpresa;
    private Integer codigoCategoria;
    protected List listaSelectItemEmpresa;
    protected List listaSelectItemCategoria;

    public RelatorioBalancoControle() throws Exception {
        montarListaSelectItemEmpresa();
        montarListaSelectItemCategoriaProduto();
        obterUsuarioLogado();
    }

    public void limparMensagens() throws Exception {
        if (!getUsuarioLogado().getAdministrador()) {
            this.codigoEmpresa = getEmpresaLogado().getCodigo();
        }
        setMensagemDetalhada("", "");
        setMensagem("");
        setMensagemID("msg_entre_prmrelatorio");
        setSucesso(true);
        setErro(false);
    }

    public void montarListaSelectItemCategoriaProduto() throws Exception {
        List lista = getFacade().getCategoriaProduto().consultarPorDescricao("", false, Uteis.NIVELMONTARDADOS_TODOS);
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), "Todos"));
        for (Object obj : lista) {
            CategoriaProdutoVO categoria = (CategoriaProdutoVO) obj;
            objs.add(new SelectItem(categoria.getCodigo(), categoria.getDescricao().toString()));
        }
        setListaSelectItemCategoria(objs);
    }

    public void montarListaSelectItemEmpresa() throws Exception {
        List listaEmpresa = new ArrayList();
        listaEmpresa.add(new SelectItem(new Integer(0), ""));
        List listaConsulta = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_TODOS);
        for (Object obj : listaConsulta) {
            EmpresaVO empresa = (EmpresaVO) obj;
            listaEmpresa.add(new SelectItem(empresa.getCodigo(), empresa.getNome()));
        }
        setListaSelectItemEmpresa(listaEmpresa);
    }

    public void imprimirRelatorioBalancoPDF() {
        try {
            this.balancoVO = (BalancoVO) context().getExternalContext().getRequestMap().get("balanco");
            if (balancoVO == null) {
                BalancoControle balancoCon = (BalancoControle) JSFUtilities.getFromSession(BalancoControle.class.getSimpleName());
                balancoVO = balancoCon.getBalancoVO();
            }
            this.balancoVO = getFacade().getBalanco().consultarPorChavePrimaria(this.balancoVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

            setListaRelatorio(new ArrayList());
            Map<String, Object> parametros = new HashMap<String, Object>();
            parametros.put("listaObjetos", this.balancoVO.getItensList());
            parametros.put("nomeRelatorio", "RelatorioBalanco");
            parametros.put("nomeEmpresa", balancoVO.getEmpresa().getNome());
            parametros.put("nomeDesignIReport", getDesignIReportRelatorio());
            parametros.put("SUBREPORT_DIR", getCaminhoSubRelatorio());
            parametros.put("SUBREPORT_DIR1", getCaminhoSubRelatorio());
            parametros.put("SUBREPORT_DIR2", getCaminhoSubRelatorio());

            apresentarRelatorioObjetos(parametros);
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        } finally {
            this.balancoVO = null;
        }
    }

    public void imprimirFormularioConfereBalancoPDF() {
        try {
            if ((this.codigoEmpresa == null) || (this.codigoEmpresa <= 0)) {
                throw new ConsistirException("É necessário informar a empresa para imprimir o formulário.");
            }
            EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            List<ProdutoVO> listaProdutos = getFacade().getProduto().consultarProdutosComControleEstoque(this.codigoEmpresa, this.codigoCategoria, "", true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            if (listaProdutos.size() == 0) {
                throw new ConsistirException("Não há dados para serem exibidos ! verifique os parâmetros informados.");
            }
            setListaRelatorio(new ArrayList());
            Map<String, Object> parametros = new HashMap<String, Object>();
            parametros.put("listaObjetos", listaProdutos);
            parametros.put("nomeRelatorio", "RelatorioConfereBalanco");
            parametros.put("nomeEmpresa", empresaVO.getNome());
            parametros.put("enderecoEmpresa", empresaVO.getEndereco());
            parametros.put("cidadeEmpresa", empresaVO.getCidade().getNome());
            parametros.put("nomeDesignIReport", getDesignIReportRelatorioConfereBalanco());
            parametros.put("SUBREPORT_DIR", getCaminhoSubRelatorio());
            parametros.put("SUBREPORT_DIR1", getCaminhoSubRelatorio());
            parametros.put("SUBREPORT_DIR2", getCaminhoSubRelatorio());

            apresentarRelatorioObjetos(parametros);
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
            setSucesso(true);
            setErro(false);

        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        } finally {
            this.balancoVO = null;
        }
    }

    public String getIrPara() {
        return retorno;
    }

    public static String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio"
                + File.separator + "estoque" + File.separator + "RelatorioBalanco.jrxml");
    }

    public static String getDesignIReportRelatorioConfereBalanco() {
        return ("relatorio" + File.separator + "designRelatorio"
                + File.separator + "estoque" + File.separator + "RelatorioConferirBalanco.jrxml");
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio"
                + File.separator + "estoque" + File.separator);
    }

    public String getRetorno() {
        return retorno;
    }

    public void setRetorno(String retorno) {
        this.retorno = retorno;
    }

    public BalancoVO getBalancoVO() {
        return balancoVO;
    }

    public void setBalancoVO(BalancoVO balancoVO) {
        this.balancoVO = balancoVO;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public List getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public List getListaSelectItemCategoria() {
        return listaSelectItemCategoria;
    }

    public void setListaSelectItemCategoria(List listaSelectItemCategoria) {
        this.listaSelectItemCategoria = listaSelectItemCategoria;
    }

    public Integer getCodigoCategoria() {
        return codigoCategoria;
    }

    public void setCodigoCategoria(Integer codigoCategoria) {
        this.codigoCategoria = codigoCategoria;
    }
}
