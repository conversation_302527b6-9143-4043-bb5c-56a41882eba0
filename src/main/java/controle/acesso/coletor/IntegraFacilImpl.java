package controle.acesso.coletor;

import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.utilitarias.validator.IPAddressValidator;

public class IntegraFacilImpl extends ValidadorPadrao {

    @Override
    public void valida(ColetorVO coletorVO) throws Exception {

        if (null == coletorVO.getIp() || coletorVO.getIp().isEmpty() || !IPAddressValidator.validate(coletorVO.getIp())) {
            throw new Exception(String.format("IP de comunicação com a catraca (%s) é obrigatória e deve ser válida", coletorVO.getModelo().getDescricao()));
        }

        if (null == coletorVO.getPorta() || coletorVO.getPorta() < 1) {
            throw new Exception(String.format("Porta de comunicação com a catraca (%s) é obrigatória", coletorVO.getModelo().getDescricao()));
        }


    }
}
