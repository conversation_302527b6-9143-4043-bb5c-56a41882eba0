package br.com.pactosolucoes.turmas.servico.dto;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class ModalidadeTurmaDTO {
    private Integer codigo;
    private String descricao;
    private List<TurmaDTO> turmas;

    public ModalidadeTurmaDTO() {
    }

    public ModalidadeTurmaDTO(JSONObject json) {
        this.codigo = json.optInt("codigo");
        this.descricao = json.getString("descricao");
        this.turmas = new ArrayList<>();
        try {
            JSONArray array = json.optJSONArray("turmas");
            for (int e = 0; e < array.length(); e++) {
                JSONObject obj = array.getJSONObject(e);
                this.turmas.add(new TurmaDTO(obj));
            }
        } catch (Exception ignored) {
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public List<TurmaDTO> getTurmas() {
        return turmas;
    }

    public void setTurmas(List<TurmaDTO> turmas) {
        this.turmas = turmas;
    }
}
