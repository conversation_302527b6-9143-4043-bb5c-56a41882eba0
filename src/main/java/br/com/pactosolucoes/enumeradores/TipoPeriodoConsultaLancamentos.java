/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;
/**
 * Enumerador usado na tela de consulta de lançamentos da combo de tipo de pesquisa
 * <AUTHOR>
 */
public enum TipoPeriodoConsultaLancamentos {

    LANCAMENTOS_DIA(1,"Lançamentos do Dia"),
    LANCAMENTOS_SEMANA(2,"Lançamentos da Semana"),
    LANCAMENTOS_MES(3,"Lançamentos do Mês"),
    CONTAS_PAGAR_SEMANA(4,"Contas a Pagar da Semana"),
    CONTAS_PAGAR_MES(5,"Contas a Pagar do Mês"),
    CONTAS_RECEBER_SEMANA(6,"Contas a Receber da Semana"),
    CONTAS_RECEBER_MES(7,"Contas a Receber do Mês");

    private Integer codigo;
    private String descricao;

    private TipoPeriodoConsultaLancamentos(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    /**
     * @return the codigo
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * @param codigo the codigo to set
     */
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the descricao
     */
    public String getDescricao() {
        return descricao;
    }

    /**
     * @param descricao the descricao to set
     */
    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static TipoPeriodoConsultaLancamentos getTipoPeriodoConsultaLancamentos(final Integer codigo) {
		TipoPeriodoConsultaLancamentos obj = null;
		for (TipoPeriodoConsultaLancamentos status : TipoPeriodoConsultaLancamentos.values()) {
			if (status.getCodigo()== codigo) {
				obj = status;
			}
		}
		return obj;
	}
}
