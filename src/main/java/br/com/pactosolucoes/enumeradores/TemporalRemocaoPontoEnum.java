package br.com.pactosolucoes.enumeradores;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by 1Berto
 */
public enum TemporalRemocaoPontoEnum {
    NUNCA(0, "Nunca"),
    QUANDO_CANCELADO_VENCIDO(1, "Cancelados e Vencidos"),
    QUANDO_CANCELADO_DESISTENTE(2, "Cancelados e Desistentes");

    TemporalRemocaoPontoEnum(int codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    private int codigo;
    private String descricao;

    public int getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static TemporalRemocaoPontoEnum of(Integer codigo) {
        for (TemporalRemocaoPontoEnum temporalRemocaoPontoEnum : values()) {
            if (temporalRemocaoPontoEnum.codigo == codigo)
                return temporalRemocaoPontoEnum;
        }
        return null;
    }

    public static List<SelectItem> toSelectedItens() {
        ArrayList<SelectItem> itens = new ArrayList<SelectItem>();
        for (TemporalRemocaoPontoEnum temporalRemocaoPontoEnum : values()) {
            itens.add(new SelectItem(temporalRemocaoPontoEnum.codigo, temporalRemocaoPontoEnum.descricao));
        }
        return itens;
    }

    public static TemporalRemocaoPontoEnum fromSelectItens(SelectItem iten){
        return of((Integer)iten.getValue());
    }
}
