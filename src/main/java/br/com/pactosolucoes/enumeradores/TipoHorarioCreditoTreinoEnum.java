package br.com.pactosolucoes.enumeradores;

/**
 * Created by ulisses on 10/11/2015.
 */
public enum TipoHorarioCreditoTreinoEnum {
    LIVRE(1, "LIVR<PERSON>", "marcar"),
    LIVRE_OBRIGATORIO_MARCAR_AULA(2, "LIVRE OBRIGATÓRIO MARCAR AULA", "marcar"),
    HORARIO_TURMA(3, "HORÁRIO DA TURMA", "repor");

    TipoHorarioCreditoTreinoEnum(Integer codigo, String descricao, String tpMsg){
        this.codigo = codigo;
        this.descricao = descricao;
        this.tipoMsg = tpMsg;
    }
    private Integer codigo;
    private String descricao;
    private String tipoMsg;

    public static TipoHorarioCreditoTreinoEnum getTipo(Integer codigo){
        if (codigo == null){
            return null;
        }
        TipoHorarioCreditoTreinoEnum tipoHorarioCreditoTreino = null;
        for (TipoHorarioCreditoTreinoEnum obj: TipoHorarioCreditoTreinoEnum.values()){
            if(obj.getCodigo().equals(codigo)){
                return obj;
            }
        }
        return tipoHorarioCreditoTreino;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTipoMsg() {
        return tipoMsg;
    }

    public void setTipoMsg(String tipoMsg) {
        this.tipoMsg = tipoMsg;
    }
    
    
}
