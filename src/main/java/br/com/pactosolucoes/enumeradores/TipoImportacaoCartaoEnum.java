package br.com.pactosolucoes.enumeradores;

public enum TipoImportacaoCartaoEnum {

    PADRAO_0(                     0, "<PERSON><PERSON><PERSON>"),
    MATRICULA_EXTERNA_1(          1, "Usando Matricula Externa (Considera que o \"idClienteMundi\" é a matriculaexterna no zw)"),
    TABELA_EXTERNA_2(             2, "Usando Tabela Externa (Usa a \"importacaocartaoauxiliar\" que contem informação de nome e ultimos 4 digitos do cartão)"),
    VINDI_3(                      3, "Arqui<PERSON> da Vindi"),
    TABELA_EXTERNA_MODELO_MOUVE_4(4, "Usando Tabela Externa Modelo Mouve"),
    CUSTOMERNAME_5(               5, "Busca o cliente pelo nome CustomerName"),
    PADRAO_CPF_TERCEIRO_6(        6, "Busca padrão se não encontrar busca pelo CPF do Terceiro"),
    EVO_7(                        7, "Importacao EVO - Busca pelo nome CustomerName e caso não encontre então busca pela tabela auxiliar \"ProcessoPreencherTabelaAuxiliarImportacaoCartaoEVO\""),
    CLOUD_GYM_8(                  8, "Importacao Cloud Gym - Importação via api"),
    CLOUD_GYM_TOKEN_CARTAO_9(     9, "Importacao Cloud Gym - Importação tokens PagoLivre, FacilitePay e Cielo"),
    ;

    private Integer codigo;
    private String observacao;

    TipoImportacaoCartaoEnum(final Integer codigo, final String observacao) {
        this.setCodigo(codigo);
        this.setObservacao(observacao);
    }

    public static TipoImportacaoCartaoEnum getConsultarPorCodigo(Integer codigo) {
        for (TipoImportacaoCartaoEnum tipoCobranca : values()) {
            if (tipoCobranca.getCodigo().equals(codigo)) {
                return tipoCobranca;
            }
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }
}
