/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.agendatotal.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.plano.HistoricoProfessorTurmaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 *
 * <AUTHOR>
 */
public class AgendaTotalJSON extends SuperJSON{
    private Integer empresa;
    private Integer ocupacao;
    private String inicio;
    private String fim;
    private String titulo;
    private String id;
    private String responsavel;
    private Integer codigoResponsavel;
    private Integer codigoPessoaResponsavel;
    private Date inicioVigencia;
    private Date fimVigencia;
    private String diaSemana;
    
    private String tipo;
    private Integer codigoTipo;
    private Integer identificadorModalidade;

    private String local;
    private Integer codigoLocal;
    
    private Integer nrVagas;
    private Integer nrVagasPreenchidas;
    private Integer nrVagasPreenchidasExperimental;
    private String nivel;
    private Integer codigoNivel;
    
    private String cor;
    private Boolean aulaCheia;
    private Boolean permitirAulaExperimental;

    private Integer tolerancia;
    private Integer tipoTolerancia;
    private String mensagem;
    private Double bonificacao;
    private Integer pontosBonus;
    private Double meta;
    private List<AgendadoJSON> alunos;

    private Integer codigoContrato;

    //aqui é só pros casos do retorno do aula cheia ao adicionar aluno
    private Integer aulasExperimentais;
    private Integer matricula;
    private String nomeAluno;
    private boolean jaMarcouEuQuero;
    private String fotoProfessor;
    private boolean validarRestricoesMarcacao;
    private boolean naoValidarModalidadeContrato;
    private Integer toleranciaApresentarApp;
    private boolean integracaoSpivi = false;
    private boolean aulaExperimental = false;
    private boolean permiteAlunoOutraEmpresa = false;
    private Integer codigoTurma;
    private String situacao;
    private Date dataEntrouTurma;
    private Date dataSaiuTurma;
    private Date datalancamento;
    private boolean alunoEstaNaAula = false;
    private Double valorProduto;
    private Integer codigoProduto;
    private String fotoModalidade;
    private boolean existeOutraAulaMarcadaMesmoHorarioEAmbiente;
    private String urlVideoYoutube;
    private String imageUrl;
    private String niveis;
    private Integer idadeMaxima;
    private Integer idadeMinima;
    private Integer idadeMaximaMeses;
    private Integer idadeMinimaMeses;
    private Integer qtdeMaximaAlunoExperimental;
    private boolean bloquearMatriculasAcimaLimite;
    private String mapaEquipamentos;



    public AgendaTotalJSON() {
    }
    
    public AgendaTotalJSON(Date data, AgendaTotalJSON agenda) {
        this(data, agenda, null);
    }
    public AgendaTotalJSON(Date data, AgendaTotalJSON agenda, HistoricoProfessorTurmaVO historicoProfessorTurmaVO) {
        this.empresa = agenda.empresa;
        this.inicio = Uteis.getData(data) + " "+agenda.inicio;
        this.fim = Uteis.getData(data) + " "+agenda.fim;
        this.titulo = agenda.titulo;
        this.responsavel = historicoProfessorTurmaVO == null ? agenda.responsavel : historicoProfessorTurmaVO.getProfessor().getPessoa().getNome();
        this.codigoResponsavel = historicoProfessorTurmaVO == null ? agenda.codigoResponsavel : historicoProfessorTurmaVO.getProfessor().getCodigo();
        this.tipo = agenda.tipo;
        this.identificadorModalidade = agenda.identificadorModalidade;
        this.codigoTipo = agenda.codigoTipo;
        this.local = agenda.local;
        this.codigoLocal = agenda.codigoLocal;
        this.nivel = agenda.nivel;
        this.codigoNivel = agenda.codigoNivel;
        this.nrVagas = agenda.nrVagas;
        this.inicioVigencia= agenda.inicioVigencia;
        this.fimVigencia= agenda.fimVigencia;
        this.nrVagasPreenchidas = agenda.nrVagasPreenchidas;
        this.nrVagasPreenchidasExperimental = agenda.nrVagasPreenchidasExperimental;
        this.cor = agenda.cor;
        this.id = agenda.id;
        this.aulaCheia = agenda.aulaCheia;
        this.permitirAulaExperimental = agenda.permitirAulaExperimental;
        this.tolerancia = agenda.tolerancia;
        this.tipoTolerancia = agenda.tipoTolerancia;
        this.mensagem = agenda.mensagem;
        this.bonificacao = agenda.bonificacao;
        this.pontosBonus = agenda.pontosBonus;
        this.meta = agenda.meta;
        this.jaMarcouEuQuero = agenda.jaMarcouEuQuero;
        this.diaSemana = agenda.diaSemana;
        this.ocupacao = agenda.ocupacao;
        this.fotoProfessor = historicoProfessorTurmaVO == null ? agenda.fotoProfessor : historicoProfessorTurmaVO.getUrlFoto();
        this.fotoModalidade = agenda.fotoModalidade;
        this.validarRestricoesMarcacao = agenda.validarRestricoesMarcacao;
        this.naoValidarModalidadeContrato = agenda.naoValidarModalidadeContrato;
        this.integracaoSpivi = agenda.integracaoSpivi;
        this.aulaExperimental = agenda.isAulaExperimental();
        this.setCodigoContrato(agenda.getCodigoContrato());
        this.permiteAlunoOutraEmpresa = agenda.isPermiteAlunoOutraEmpresa();
        this.codigoTurma = agenda.getCodigoTurma();
        this.alunoEstaNaAula = agenda.isAlunoEstaNaAula();
        this.urlVideoYoutube = agenda.getUrlVideoYoutube();
        this.imageUrl = agenda.getImageUrl();
        try{
            this.situacao = agenda.getSituacao();
            this.dataEntrouTurma = agenda.getDataEntrouTurma();
            this.dataSaiuTurma = agenda.getDataSaiuTurma();
        }catch(Exception ignore){}

        this.niveis = agenda.getNiveis();
        this.idadeMaxima = agenda.getIdadeMaxima();
        this.idadeMinima = agenda.getIdadeMinima();
        this.idadeMaximaMeses = agenda.getIdadeMaximaMeses();
        this.idadeMinimaMeses = agenda.getIdadeMinimaMeses();
        this.qtdeMaximaAlunoExperimental = agenda.getQtdeMaximaAlunoExperimental();
        this.bloquearMatriculasAcimaLimite = agenda.isBloquearMatriculasAcimaLimite();
        this.mapaEquipamentos = agenda.getMapaEquipamentos();

    }

    public void setIntegracaoSpivi(final boolean integracaoSpivi) {
        this.integracaoSpivi = integracaoSpivi;
    }

    public boolean isIntegracaoSpivi() {
        return integracaoSpivi;
    }

    public String getInicio() {
        return inicio;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public String getFim() {
        return fim;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(String responsavel) {
        this.responsavel = responsavel;
    }

    public Integer getCodigoResponsavel() {
        return codigoResponsavel;
    }

    public void setCodigoResponsavel(Integer codigoResponsavel) {
        this.codigoResponsavel = codigoResponsavel;
    }

    public Integer getCodigoPessoaResponsavel() {
        return codigoPessoaResponsavel;
    }

    public void setCodigoPessoaResponsavel(Integer codigoPessoaResponsavel) {
        this.codigoPessoaResponsavel = codigoPessoaResponsavel;
    }

    public String getLocal() {
        return local;
    }

    public void setLocal(String local) {
        this.local = local;
    }

    public Integer getCodigoLocal() {
        return codigoLocal;
    }

    public void setCodigoLocal(Integer codigoLocal) {
        this.codigoLocal = codigoLocal;
    }

    public Integer getNrVagas() { 
        return nrVagas;
    }

    public void setNrVagas(Integer nrVagas) {
        this.nrVagas = nrVagas;
    }

    public Integer getNrVagasPreenchidas() {
        return nrVagasPreenchidas;
    }

    public void setNrVagasPreenchidas(Integer nrVagasPreenchidas) {
        this.nrVagasPreenchidas = nrVagasPreenchidas;
    }

    public Integer getNrVagasPreenchidasExperimental() {
        return nrVagasPreenchidasExperimental;
    }

    public void setNrVagasPreenchidasExperimental(Integer nrVagasPreenchidasExperimental) {
        this.nrVagasPreenchidasExperimental = nrVagasPreenchidasExperimental;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Integer getIdentificadorModalidade() {
        return identificadorModalidade;
    }

    public void setIdentificadorModalidade(Integer identificadorModalidade) {
        this.identificadorModalidade = identificadorModalidade;
    }

    public Integer getCodigoTipo() {
        return codigoTipo;
    }

    public void setCodigoTipo(Integer codigoTipo) {
        this.codigoTipo = codigoTipo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public Integer getCodigoNivel() {
        return codigoNivel;
    }

    public void setCodigoNivel(Integer codigoNivel) {
        this.codigoNivel = codigoNivel;
    }

    public Date getInicioVigencia() {
        return inicioVigencia;
    }

    public void setInicioVigencia(Date inicioVigencia) {
        this.inicioVigencia = inicioVigencia;
    }

    public Date getFimVigencia() {
        return fimVigencia;
    }

    public void setFimVigencia(Date fimVigencia) {
        this.fimVigencia = fimVigencia;
    }

    public Boolean getAulaCheia() {
        return aulaCheia;
    }

    public void setAulaCheia(Boolean aulaCheia) {
        this.aulaCheia = aulaCheia;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public Integer getTolerancia() {
        return tolerancia;
    }

    public void setTolerancia(Integer tolerancia) {
        this.tolerancia = tolerancia;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public Double getBonificacao() {
        return bonificacao;
    }

    public void setBonificacao(Double bonificacao) {
        this.bonificacao = bonificacao;
    }

    public Integer getPontosBonus() {
        return pontosBonus;
    }

    public void setPontosBonus(Integer pontosBonus) {
        this.pontosBonus = pontosBonus;
    }

    public Double getMeta() {
        return meta;
    }

    public void setMeta(Double meta) {
        this.meta = meta;
    }

    public Integer getAulasExperimentais() {
        return aulasExperimentais;
    }

    public void setAulasExperimentais(Integer aulasExperimentais) {
        this.aulasExperimentais = aulasExperimentais;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNomeAluno() {
        return nomeAluno;
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public boolean isJaMarcouEuQuero() {
        return jaMarcouEuQuero;
    }

    public void setJaMarcouEuQuero(boolean jaMarcouEuQuero) {
        this.jaMarcouEuQuero = jaMarcouEuQuero;
    }

    public Integer getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(Integer ocupacao) {
        this.ocupacao = ocupacao;
    }

    public Boolean getPermitirAulaExperimental() {
        return permitirAulaExperimental;
    }

    public void setPermitirAulaExperimental(Boolean permitirAulaExperimental) {
        this.permitirAulaExperimental = permitirAulaExperimental;
    }

    public String getFotoProfessor() {
        return fotoProfessor;
    }

    public void setFotoProfessor(String fotoProfessor) {
        this.fotoProfessor = fotoProfessor;
    }

    public String getFotoModalidade() {
        return fotoModalidade;
    }

    public void setFotoModalidade(String fotoModalidade) {
        this.fotoModalidade = fotoModalidade;
    }

    public String getCodDia() {
        try {
            String dia = Uteis.getData(Uteis.getDate(getInicio(), "dd/MM/yyyy"));
            return UteisValidacao.emptyString(dia) ? "" : (getId() + "_" + dia);
        } catch (Exception e) {
            return "";
        }
    }

    public boolean isValidarRestricoesMarcacao() {
        return validarRestricoesMarcacao;
    }

    public void setValidarRestricoesMarcacao(boolean validarRestricoesMarcacao) {
        this.validarRestricoesMarcacao = validarRestricoesMarcacao;
    }

    public Integer getToleranciaApresentarApp() {
        return toleranciaApresentarApp;
    }

    public void setToleranciaApresentarApp(Integer toleranciaApresentarApp) {
        this.toleranciaApresentarApp = toleranciaApresentarApp;
    }

    public List<AgendadoJSON> getAlunos() {
        if (this.alunos == null) {
            this.alunos = new ArrayList<>();
        }
        return alunos;
    }

    public void setAlunos(List<AgendadoJSON> alunos) {
        this.alunos = alunos;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public boolean isAulaExperimental() {
        return aulaExperimental;
    }

    public void setAulaExperimental(boolean aulaExperimental) {
        this.aulaExperimental = aulaExperimental;
    }

    public boolean isPermiteAlunoOutraEmpresa() {
        return permiteAlunoOutraEmpresa;
    }

    public void setPermiteAlunoOutraEmpresa(boolean permiteAlunoOutraEmpresa) {
        this.permiteAlunoOutraEmpresa = permiteAlunoOutraEmpresa;
    }

    public Integer getCodigoTurma() {
        return codigoTurma;
    }

    public void setCodigoTurma(Integer codigoTurma) {
        this.codigoTurma = codigoTurma;
    }

    public boolean isNaoValidarModalidadeContrato() {
        return naoValidarModalidadeContrato;
    }

    public void setNaoValidarModalidadeContrato(boolean naoValidarModalidadeContrato) {
        this.naoValidarModalidadeContrato = naoValidarModalidadeContrato;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Date getDataEntrouTurma() {
        return dataEntrouTurma;
    }

    public void setDataEntrouTurma(Date dataEntrouTurma) {
        this.dataEntrouTurma = dataEntrouTurma;
    }

    public Date getDataSaiuTurma() {
        return dataSaiuTurma;
    }

    public void setDataSaiuTurma(Date dataSaiuTurma) {
        this.dataSaiuTurma = dataSaiuTurma;
    }

    public Date getDatalancamento() { return datalancamento; }

    public void setDatalancamento(Date datalancamento) { this.datalancamento = datalancamento; }

    public Integer getTipoTolerancia() {
        return tipoTolerancia;
    }

    public void setTipoTolerancia(Integer tipoTolerancia) {
        this.tipoTolerancia = tipoTolerancia;
    }

    public boolean isAlunoEstaNaAula() {
        return alunoEstaNaAula;
    }

    public void setAlunoEstaNaAula(boolean alunoEstaNaAula) {
        this.alunoEstaNaAula = alunoEstaNaAula;
    }

    public Double getValorProduto() {
        return valorProduto;
    }

    public void setValorProduto(Double valorProduto) {
        this.valorProduto = valorProduto;
    }

    public Integer getCodigoProduto() {
        return codigoProduto;
    }

    public void setCodigoProduto(Integer codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public boolean isExisteOutraAulaMarcadaMesmoHorarioEAmbiente() {
        return existeOutraAulaMarcadaMesmoHorarioEAmbiente;
    }

    public void setExisteOutraAulaMarcadaMesmoHorarioEAmbiente(boolean existeOutraAulaMarcadaMesmoHorarioEAmbiente) {
        this.existeOutraAulaMarcadaMesmoHorarioEAmbiente = existeOutraAulaMarcadaMesmoHorarioEAmbiente;
    }

    public String getUrlVideoYoutube() {
        return urlVideoYoutube;
    }

    public void setUrlVideoYoutube(String urlVideoYoutube) {
        this.urlVideoYoutube = urlVideoYoutube;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getNiveis() {
        return niveis;
    }

    public void setNiveis(String niveis) {
        this.niveis = niveis;
    }

    public Integer getIdadeMaxima() {
        return idadeMaxima;
    }

    public void setIdadeMaxima(Integer idadeMaxima) {
        this.idadeMaxima = idadeMaxima;
    }

    public Integer getIdadeMinima() {
        return idadeMinima;
    }

    public void setIdadeMinima(Integer idadeMinima) {
        this.idadeMinima = idadeMinima;
    }

    public Integer getIdadeMaximaMeses() {
        return idadeMaximaMeses;
    }

    public void setIdadeMaximaMeses(Integer idadeMaximaMeses) {
        this.idadeMaximaMeses = idadeMaximaMeses;
    }

    public Integer getIdadeMinimaMeses() {
        return idadeMinimaMeses;
    }

    public void setIdadeMinimaMeses(Integer idadeMinimaMeses) {
        this.idadeMinimaMeses = idadeMinimaMeses;
    }

    public Integer getQtdeMaximaAlunoExperimental() {
        return qtdeMaximaAlunoExperimental;
    }

    public void setQtdeMaximaAlunoExperimental(Integer qtdeMaximaAlunoExperimental) {
        this.qtdeMaximaAlunoExperimental = qtdeMaximaAlunoExperimental;
    }

    public boolean isBloquearMatriculasAcimaLimite() {
        return bloquearMatriculasAcimaLimite;
    }

    public void setBloquearMatriculasAcimaLimite(boolean bloquearMatriculasAcimaLimite) {
        this.bloquearMatriculasAcimaLimite = bloquearMatriculasAcimaLimite;
    }

    public String getMapaEquipamentos() {
        return mapaEquipamentos;
    }

    public void setMapaEquipamentos(String mapaEquipamentos) {
        this.mapaEquipamentos = mapaEquipamentos;
    }
}

