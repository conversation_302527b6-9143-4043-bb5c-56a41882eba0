/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.oamd.bean;

import org.json.JSONException;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.UtilReflection;

/**
 *
 * <AUTHOR>
 */
public class EmpresaTO extends SuperTO {

    private static final long serialVersionUID = 2424217456279335468L;
    private Integer codigo = 0;
    private String chave;
    private String hostBD;
    private Integer porta = 5432;
    private String nomeBD;
    private String userBD;
    private Boolean ativa;
    private Boolean usarBDLocal = true;
    private String passwordBD;
    private String robocontrole;
    private String modulos;
    private Boolean permitemultiempresas = true;
    private String urlgestaonotas = "";
    private String urlgestaonotasrest = "";

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getAtiva() {
        return ativa;
    }

    public void setAtiva(Boolean ativa) {
        this.ativa = ativa;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getHostBD() {
        return hostBD;
    }

    public void setHostBD(String hostBD) {
        this.hostBD = hostBD;
    }

    public String getModulos() {
        return modulos;
    }

    public void setModulos(String modulos) {
        this.modulos = modulos;
    }

    public String getNomeBD() {
        return nomeBD;
    }

    public void setNomeBD(String nomeBD) {
        this.nomeBD = nomeBD;
    }

    public String getPasswordBD() {
        return passwordBD;
    }

    public void setPasswordBD(String passwordBD) {
        this.passwordBD = passwordBD;
    }

    public Boolean getPermitemultiempresas() {
        return permitemultiempresas;
    }

    public void setPermitemultiempresas(Boolean permitemultiempresas) {
        this.permitemultiempresas = permitemultiempresas;
    }

    public Integer getPorta() {
        return porta;
    }

    public void setPorta(Integer porta) {
        this.porta = porta;
    }

    public String getRobocontrole() {
        return robocontrole;
    }

    public void setRobocontrole(String robocontrole) {
        this.robocontrole = robocontrole;
    }

    public Boolean getUsarBDLocal() {
        return usarBDLocal;
    }

    public void setUsarBDLocal(Boolean usarBDLocal) {
        this.usarBDLocal = usarBDLocal;
    }

    public String getUserBD() {
        return userBD;
    }

    public void setUserBD(String userBD) {
        this.userBD = userBD;
    }

    @Override
    public String toString() {
        try {
            return UtilReflection.toJSON(this).toString();
        } catch (JSONException ex) {
            Logger.getLogger(EmpresaTO.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }

    public String getUrlgestaonotas() {
        return urlgestaonotas;
    }

    public void setUrlgestaonotas(String urlgestaonotas) {
        this.urlgestaonotas = urlgestaonotas;
    }

    public String getUrlgestaonotasrest() {
        return urlgestaonotasrest;
    }

    public void setUrlgestaonotasrest(String urlgestaonotasrest) {
        this.urlgestaonotasrest = urlgestaonotasrest;
    }
}
