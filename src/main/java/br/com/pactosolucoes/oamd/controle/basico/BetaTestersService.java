package br.com.pactosolucoes.oamd.controle.basico;

import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.io.Charsets;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class BetaTestersService {

    public static List<String> testers = new ArrayList<>();
    public static Boolean applyAll = Boolean.FALSE;


    public static JSONObject caminhos() throws Exception {
        String result = ExecuteRequestHttpService.executeHttpRequest(PropsService.getPropertyValue("DISCOVERY_URL") + "/paths", null,
                new HashMap<>(), ExecuteRequestHttpService.METODO_GET,
                Charsets.UTF_8.name());
        return new JSONObject(result).getJSONObject("content");
    }

    public void init(){
        if(!Uteis.isAmbienteDesenvolvimentoTeste()){
            try {
                testers = new ArrayList<>();
                JSONArray resultOAMD = consultaOAMD();
                for(int i = 0; i < resultOAMD.length(); i++){
                    testers.add(resultOAMD.getString(i));
                }
            }catch (Exception e){
                System.out.println("problema ao obter os testers - " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    public static boolean isBetaTester(String key){
        return applyAll || (key != null && testers.contains(key)) || Uteis.isAmbienteDesenvolvimentoTeste();
    }

    public JSONArray consultaOAMD() throws Exception {
        JSONObject caminhos = caminhos();
        String oamdApp = caminhos.optString("oamd");
        if(UteisValidacao.emptyString(oamdApp)){
            throw new Exception(" o discovery não retornou a url do oamd, verifique se está preenchida no banco corretamente");
        }
        String url =  oamdApp + "/prest/empresa/betatesters";
        String result = ExecuteRequestHttpService.executeHttpRequest(url, null,
                new HashMap<>(), ExecuteRequestHttpService.METODO_GET,
                Charsets.UTF_8.name());
        return new JSONObject(result).getJSONArray("return");
    }

}
