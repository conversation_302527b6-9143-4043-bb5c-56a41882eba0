package br.com.pactosolucoes.graduacao.service.interf;

import org.json.JSONArray;
import org.json.JSONObject;

import java.sql.Connection;

public interface GraduacaoService extends AutoCloseable {

    Connection getCon();

    void setCon(Connection con);

    JSONArray alunosG<PERSON>ua<PERSON>o(Integer empresa,  JSONObject filtros, boolean todos) throws Exception;

    Integer totalAlunosGraduacao(Integer empresa,  JSONObject filtros) throws Exception;

    JSONObject filtrosAlunosGraduacao(Integer empresa, String modalidades) throws Exception;

    JSONArray consultarInformacoesAluno(Integer matricula) throws Exception;

    JSONObject consultarHorariosEDiasAlunoPorPeriodo(Integer matricula, Long data) throws Exception;

}
