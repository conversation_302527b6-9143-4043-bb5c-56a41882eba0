package br.com.pactosolucoes.aas.authorization.entity;

import negocio.comuns.arquitetura.SuperTO;

public class Autorizacao extends SuperTO {

	private int codigo;
	private int perfilAcesso;
	private int entidade;
	private int funcionalidade;
	private String operacao;
	private String descricao;
	private boolean selecionado;
	
	public int getCodigo() {
		return codigo;
	}

	public void setCodigo(int codigo) {
		this.codigo = codigo;
	}

	public int getPerfilAcesso() {
		return perfilAcesso;
	}

	public void setPerfilAcesso(int perfilAcesso) {
		this.perfilAcesso = perfilAcesso;
	}

	public int getEntidade() {
		return entidade;
	}

	public void setEntidade(int entidade) {
		this.entidade = entidade;
	}

	public int getFuncionalidade() {
		return funcionalidade;
	}

	public void setFuncionalidade(int funcionalidade) {
		this.funcionalidade = funcionalidade;
	}

	public String getOperacao() {
		if(operacao == null){
			operacao = "";
		}
		return operacao;
	}

	public void setOperacao(String operacao) {
		this.operacao = operacao;
	}

	public String getDescricao() {
		return descricao;
	}

	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}

	public void setSelecionado(boolean selecionado) {
		this.selecionado = selecionado;
	}

	public boolean getSelecionado() {
		return selecionado;
	}
}
