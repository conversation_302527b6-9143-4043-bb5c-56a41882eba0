package br.com.pactosolucoes.estudio.interfaces;

import br.com.pactosolucoes.estudio.modelo.AmbienteIndispVO;
import java.util.List;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface AmbienteIndispInterfaceFacade extends SuperInterface {

    /**
     * Método que exclui as configurações de indisponibilidade do ambiente
     * passada como parâmetro
     *
     * @param Integer idempresa
     * @throws Exception
     */
    void apagar(Integer codigoAmbiente) throws Exception;

    List<AmbienteIndispVO> listarAmbiente(Integer codigoAmbiente) throws Exception;

    /**
     * Método que salva configuração de indisponibilidade do ambiente
     *
     * @param ambienteIndispVOVO ambienteIndispVO
     * @throws Exception
     */
    void salvar(AmbienteIndispVO ambienteIndispVO) throws Exception;

}
