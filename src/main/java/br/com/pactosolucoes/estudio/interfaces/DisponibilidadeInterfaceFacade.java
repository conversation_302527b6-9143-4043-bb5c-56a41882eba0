package br.com.pactosolucoes.estudio.interfaces;

import br.com.pactosolucoes.estudio.enumeradores.DiaDaSemanaEnum;
import br.com.pactosolucoes.estudio.modelo.ConfiguracaoEstudioVO;
import br.com.pactosolucoes.estudio.modelo.DisponibilidadeVO;
import br.com.pactosolucoes.estudio.modelo.IndisponibilidadeVO;
import java.sql.Date;
import java.util.List;
import negocio.comuns.plano.ProdutoVO;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR> Eugênio - GeoInova Soluções
 */
public interface DisponibilidadeInterfaceFacade extends SuperInterface {

    /**
     * Método que seleciona a função de disponibilidade, trazendo a relação
     * passada e os dias disponíveis dentro de uma tabela
     *
     * @param listaProduto
     * @param listaColaborador
     * @param listaAmbiente
     * @param listaHora
     * @param listaDiaDaSemana
     * @param periodoInicial
     * @param periodoFinal
     * @param configuracao
     * @return novaLista
     * @throws Exception
     */
    List<DisponibilidadeVO> funcaoRelProdutoColaboradorAmbiente(List<ProdutoVO> listaProduto, 
            List<Integer> listaColaborador, List<Integer> listaAmbiente, List<DiaDaSemanaEnum> listaDiaDaSemana,
            Date periodoInicial, Date periodoFinal, ConfiguracaoEstudioVO configuracao) throws Exception;

    /**
     * Método principal que retorna a lista de disponibilidade organizada com o
     * número de matriculados e sua capacidade
     *
     * @param listaProduto
     * @param listaColaborador
     * @param listaAmbiente
     * @param listaHora
     * @param listaDiaDaSemana
     * @param periodoInicial
     * @param periodoFinal
     * @param configuracao
     * @return listaFuncao
     * @throws Exception
     */
    public List<DisponibilidadeVO> acaoPrincipal(List<ProdutoVO> listaProduto, List<Integer> listaColaborador, List<Integer> listaAmbiente,
            List<String> listaHora, List<DiaDaSemanaEnum> listaDiaDaSemana, Date periodoInicial, Date periodoFinal, ConfiguracaoEstudioVO configuracao,
            List<DisponibilidadeVO> listaDisponibilidadeAgendado, boolean apresentarHorariosQuebrados, Integer codigoAgenda) throws Exception ;

    /**
     * Método que busca a indisponibilidade do ambiente ou colaborador,
     * dependendo do filtro
     *
     * @param String nome Filtro
     * @param int id Filtro
     * @param Date data Inicial
     * @param Date data Final
     * @return List<IndisponibilidadeVO> lista de indisponibilidade
     * @throws Exception
     */
    List<IndisponibilidadeVO> buscarIndisponibilidadeFiltro(String nomeFiltro, String nomeTabela, int idFiltro, Date dataInicial, Date dataFinal) throws Exception;
    
    /**
     * Seleciona os feriados do banco de dados
     * @param dataInicial
     * @param dataFinal
     * @return
     * @throws Exception 
     */
    List<IndisponibilidadeVO> buscarFeriados(Date dataInicial, Date dataFinal) throws Exception;
}