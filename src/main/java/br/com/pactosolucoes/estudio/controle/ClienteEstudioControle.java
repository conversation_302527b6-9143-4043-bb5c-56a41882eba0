package br.com.pactosolucoes.estudio.controle;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.estudio.enumeradores.DiaDaSemanaEnum;
import br.com.pactosolucoes.estudio.enumeradores.OrigemOperacaoEnum;
import br.com.pactosolucoes.estudio.enumeradores.StatusEnum;
import br.com.pactosolucoes.estudio.modelo.AgendaVO;
import br.com.pactosolucoes.estudio.modelo.ConfiguracaoEstudioVO;
import br.com.pactosolucoes.estudio.modelo.DisponibilidadeVO;
import br.com.pactosolucoes.estudio.modelo.TipoHorarioVO;
import br.com.pactosolucoes.estudio.util.Validador;
import java.io.OutputStream;
import controle.arquitetura.SuperControle;
import controle.basico.ClienteControle;
import controle.financeiro.VendaAvulsaControle;
import java.io.IOException;
import java.sql.Time;
import java.util.*;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpSession;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.plano.AmbienteVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.AcessoException;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

/**
 *
 * <AUTHOR> Eugênio - GeoInova Soluções
 */
public class ClienteEstudioControle extends SuperControle {

    private AgendaVO agendaEstudio;
    private ConfiguracaoEstudioVO configuracaoEstudioVO;
    private List<AgendaVO> listaAFaturar;
    private List<AgendaVO> listaAAgendar;
    private List<AgendaVO> listaHistoricoAgenda;
    private List<AgendaVO> listaAgendaExcecao;
    private List<ItemVendaAvulsaVO> listaItemVenda;
    private List<ClienteVO> listaCliente;
    private List<ColaboradorVO> profissionalSuggest;
    private List<SelectItem> profissionalCombo;
    private List<SelectItem> servicoCombo;
    private List<SelectItem> ambienteCombo;
    private List<SelectItem> selectItemHorarios;
    private List<SelectItem> tiposHorarios;
    private HashMap<Integer, Integer> mapQuantidade;
    private Boolean selecionarTodosFaturar;
    private Boolean selecionarTodosAgendar;
    private Boolean verTodosModal;
    private Boolean verTodosModalAgenda;
    private Boolean mostrarPanelAluno;
    private Boolean apresentarRichModalErro;
    private Integer nmrPaginaItemVenda;
    private Integer nmrPaginaAgendar;
    private Integer nmrPaginaFaturar;
    private Integer codigoCliente;
    private Integer codigoClienteAgendar;
    private Integer codigoClienteFaturar;
    private Integer codigoClienteModal;
    private Integer timeSelecionado;
    private List<AgendaVO> listaAgendamentosDeVendas;
    private ItemVendaAvulsaVO itemVendaVO;
    private Integer qtdUtilizadaSessoes = 0;
    private Integer qtdRestanteSessoes = 0;
    private Integer qtdRestanteAAgendar = 0;
    private String apresentarBotaoRetirarHorario = "";
    private String timeHorarioInicial;
    private String timeHorarioFinal;
    private List<SelectItem> servicoGenericoCombo;

    public ClienteEstudioControle() throws Exception {
        acaoLimpar();
    }

    /**
     * Método para inicializar os objetos
     */
    public void acaoLimpar() {
        try {
            setConfPaginacao(new ConfPaginacao());
            setAgendaEstudio(new AgendaVO());
            setNmrPaginaAgendar(10);
            setNmrPaginaFaturar(10);
            setCodigoCliente(null);
            setCodigoClienteAgendar(null);
            setCodigoClienteFaturar(null);
            setCodigoClienteModal(null);
            setListaItemVenda(new ArrayList<ItemVendaAvulsaVO>());
            setListaAAgendar(new ArrayList<AgendaVO>());
            setListaAFaturar(new ArrayList<AgendaVO>());
            setListaCliente(new ArrayList<ClienteVO>());
            setProfissionalCombo(new ArrayList<SelectItem>());
            setServicoCombo(new ArrayList<SelectItem>());
            setAmbienteCombo(new ArrayList<SelectItem>());
            setSelectItemHorarios(new ArrayList<SelectItem>());
            setTiposHorarios(new ArrayList<SelectItem>());
            setSelecionarTodosFaturar(Boolean.FALSE);
            setSelecionarTodosAgendar(Boolean.FALSE);
            setVerTodosModal(Boolean.FALSE);
            setVerTodosModalAgenda(Boolean.FALSE);
            setMostrarPanelAluno(Boolean.FALSE);
            setApresentarRichModalErro(Boolean.FALSE);
            setListaHistoricoAgenda(null);

            ClienteControle clienteControle = (ClienteControle) getControlador(ClienteControle.class);
            if (clienteControle.getClienteVO() == null || UteisValidacao.emptyNumber(clienteControle.getClienteVO().getCodigo())) {
                clienteControle.pegarClienteTelaCliente();
            }
            setListaAgendaExcecao(getFacade().getAgendaEstudio().buscarAgendaExcecao(getEmpresaLogado().getCodigo(), null, null,
                    null, "", clienteControle.getClienteVO(), "0", getVerTodosModalAgenda(), false));

            //setListaAgendaExcecao(null);

            FacesContext context = FacesContext.getCurrentInstance();
            HttpSession session = (HttpSession) context.getExternalContext().getSession(false);
            setConfiguracaoEstudioVO((ConfiguracaoEstudioVO) session.getAttribute("configuracaoEstudio"));
            setItemVendaVO(new ItemVendaAvulsaVO());
        } catch (Exception e) {
            setMensagemDetalhada("", e.getMessage());
        }
    }
    public void atualizar() throws Exception{
        ClienteControle clienteControle = (ClienteControle) getControlador(ClienteControle.class);
        clienteControle.pegarClienteTelaCliente();
        setListaAgendaExcecao(getFacade().getAgendaEstudio().buscarAgendaExcecao(getEmpresaLogado().getCodigo(), null, null,
                null, "", clienteControle.getClienteVO(), "0", getVerTodosModalAgenda(), false));
    }
    public void acaoListarHistorico() {
        try {
            ClienteControle clienteControle = (ClienteControle) JSFUtilities.getManagedBean("ClienteControle");
            clienteControle.pegarClienteTelaCliente();
            setCodigoClienteModal(clienteControle.getClienteVO().getCodigo());

            Date dataFim = Calendario.hoje();
            if (getVerTodosModal().equals(Boolean.TRUE)) {
                dataFim = null;
            }
            List<Integer> idCliente = new ArrayList<Integer>();
            idCliente.add(getCodigoClienteModal());

            setListaHistoricoAgenda(getFacade().getAgendaEstudio().buscarAgenda(getEmpresaLogado().getCodigo(), null, Uteis.getSQLData(dataFim),
                    idCliente, "cliente", null, true, false, false));
        } catch (Exception e) {
            setMensagemDetalhada("", e.getMessage());
        }
    }

    public List<AgendaVO> getListaHistoricoAgenda() throws Exception {
        try {
            if (listaHistoricoAgenda == null || listaHistoricoAgenda.isEmpty()) {
                listaHistoricoAgenda = new ArrayList<AgendaVO>();
                ClienteControle clienteControle = (ClienteControle) JSFUtilities.getManagedBean("ClienteControle");
                clienteControle.pegarClienteTelaCliente();
                setCodigoClienteModal(clienteControle.getClienteVO().getCodigo());

                Date dataFim = Calendario.hoje();

                List<Integer> idCliente = new ArrayList<Integer>();
                idCliente.add(getCodigoClienteModal());

                setListaHistoricoAgenda(getFacade().getAgendaEstudio().buscarAgenda(getEmpresaLogado().getCodigo(), null, Uteis.getSQLData(dataFim),
                        idCliente, "cliente", null, true, false, false));
            }

            return listaHistoricoAgenda;
        } catch (Exception e) {
            setMensagemDetalhada("", e.getMessage());
        }
        return null;
    }

    public void acaoListarAgendaExcecao() {
        try {
            ClienteControle clienteControle = (ClienteControle) JSFUtilities.getManagedBean("ClienteControle");
            clienteControle.pegarClienteTelaCliente();
            setListaAgendaExcecao(getFacade().getAgendaEstudio().buscarAgendaExcecao(getEmpresaLogado().getCodigo(), null, null,
                    null, "", clienteControle.getClienteVO(), "0", getVerTodosModalAgenda(), false));
        } catch (Exception e) {
            setMensagemDetalhada("", e.getMessage());
        }
    }

    public void fecharModalAgendaAluno() {
        AgendaAmbienteColaboradorControle agenda = (AgendaAmbienteColaboradorControle) getControlador(AgendaAmbienteColaboradorControle.class.getSimpleName());
        agenda.acaoFecharModalPanelAluno();
        agenda.setApresentarRichModalErro(false);
        agenda.acaoFecharToolTip();
    }

    public List<AgendaVO> getListaAgendaExcecao() {
        try {
            if (listaAgendaExcecao == null) {
                listaAgendaExcecao = new ArrayList<AgendaVO>();
                ClienteControle clienteControle = (ClienteControle) JSFUtilities.getManagedBean("ClienteControle");
                clienteControle.pegarClienteTelaCliente();
                setListaAgendaExcecao(getFacade().getAgendaEstudio().buscarAgendaExcecao(getEmpresaLogado().getCodigo(), null, null,
                        null, "", clienteControle.getClienteVO(), "0", false, false));
            }

            return listaAgendaExcecao;
        } catch (Exception e) {
            setMensagemDetalhada("", e.getMessage());
        }
        return null;
    }

    public List<AgendaVO> consultarAgendamentos() {
        try {
            itemVendaVO = (ItemVendaAvulsaVO) context().getExternalContext().getRequestMap().get("item");
            setListaAgendamentosDeVendas(new ArrayList<AgendaVO>());
            setQtdRestanteSessoes(0);
            setQtdUtilizadaSessoes(0);
            setQtdRestanteAAgendar(0);
            if(itemVendaVO.getPacoteVO() != null  && itemVendaVO.getPacoteVO().getPacoteGenerico() && itemVendaVO.getPacoteVO().getCodigo() > 0){
            	setListaAgendamentosDeVendas(getFacade().getAgendaEstudio().buscarAgendaPorCodigoItemVendaAvulsaCodigoProduto(itemVendaVO.getCodigo(), itemVendaVO.getProduto().getCodigo(), true));	
            } else {
            	setListaAgendamentosDeVendas(getFacade().getAgendaEstudio().buscarAgendaPorCodigoItemVendaAvulsaCodigoProduto(itemVendaVO.getCodigo(), itemVendaVO.getProduto().getCodigo(), false));
            }
            setItemVendaVO(itemVendaVO);
            setQtdRestanteAAgendar(itemVendaVO.getQuantidade() - listaAgendamentosDeVendas.size());
            for (AgendaVO agendaVO : listaAgendamentosDeVendas) {
                if (agendaVO.getStatus().equals(StatusEnum.MINISTRADA) || agendaVO.getStatus().equals(StatusEnum.FALTAECONSIDERA)) {
                    setQtdUtilizadaSessoes(qtdUtilizadaSessoes + 1);
                } 
                if (agendaVO.getStatus().equals(StatusEnum.FALTAJUSTIFICADA)) {
                    setQtdRestanteAAgendar(qtdRestanteAAgendar + 1);
                }
            }
            setQtdRestanteSessoes(itemVendaVO.getQuantidade() - getQtdUtilizadaSessoes());
            return listaAgendamentosDeVendas;
        } catch (Exception e) {
            setMensagemDetalhada("", e.getMessage());
        }
        return null;
    }

    /**
     * Metodo para capturar a foto da empresa para mostrar na impressão de agendamentos na tela do cliente
     * @param event
     * @throws java.io.IOException
     * @throws java.lang.Exception
     */
    public void paintFoto(OutputStream out, Object data) throws IOException, Exception {
        Integer codigoEmpresa = getEmpresaLogado().getCodigo();
        if (codigoEmpresa != 0) {
            setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(
                    codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
        getEmpresa().setFotoRelatorio(getFacade().getEmpresa().obterFoto(getKey(), 
                getEmpresa().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
        super.paintFotoRelatorio(out, getEmpresa().getFotoRelatorio());
    }

    /*
     * COMPRADOS
     */
    /**
     * Método que lista todas as compras feitas, do produto tipo SESSÃO
     *
     * @return
     * @throws Exception
     */
    public List<ItemVendaAvulsaVO> getListaItemVenda() {
        /*try {
        ClienteControle clienteControle = (ClienteControle) JSFUtilities.getManagedBean("ClienteControle");
        * clienteControle.pegarClienteTelaCliente();
        if (!Validador.isValidaInteger(getCodigoCliente()) || !getCodigoCliente().equals(clienteControle.getClienteVO().getCodigo())) {
        setCodigoCliente(clienteControle.getClienteVO().getCodigo());

        listaItemVenda = new ArrayList<ItemVendaAvulsaVO>();

        getConfPaginacao().setItensPorPagina(5);

        listaItemVenda = getFacade().getVendaAvulsa().consultarPaginado(
        getCodigoCliente(), getEmpresaLogado().getCodigo(), "SS", getConfPaginacao());
        }

        } catch (Exception e) {
        setMensagemDetalhada("", e.getMessage());
        }*/

        if (listaItemVenda == null) {
            listaItemVenda = new ArrayList<>();
        }
        return listaItemVenda;
    }

    public Integer getListaItemVendaSize() {
        return getListaItemVenda().size();
    }

    /*
     * A AGENDAR
     */
    /**
     * Ação que vai para tela de disponibilidade com o produto selecionado já
     * adicionado no filtro
     *
     * @return
     * @throws Exception
     */
    public String acaoDisponibilidade() throws Exception {
        DisponibilidadeControle disponibilidadeControle = (DisponibilidadeControle) JSFUtilities.getManagedBean("disponibilidadeControle");
        List<ProdutoVO> listaProduto = new ArrayList<ProdutoVO>();

        ClienteControle clienteControle = (ClienteControle) JSFUtilities.getManagedBean("ClienteControle");
        clienteControle.pegarClienteTelaCliente();
        for (AgendaVO agenda : getListaAAgendar()) {
            if (agenda.getSelecionarAgendar()) {

                agenda.getProdutoVO().setSelecionado(Boolean.TRUE);
                listaProduto.add(agenda.getProdutoVO());
            }
        }

        disponibilidadeControle.acaoEntrar();

        disponibilidadeControle.setListaServicoTabela(listaProduto);
        disponibilidadeControle.setClienteVO(clienteControle.getClienteVO());

        return "disponibilidade";
    }

    /**
     * Retorna a lista de agenda A Agendar do cliente respectivo
     *
     * @return
     */
    public void montarListaAgendar() {
        try {
            ClienteControle clienteControle = (ClienteControle) JSFUtilities.getManagedBean("ClienteControle");
            clienteControle.pegarClienteTelaCliente();
            if (!Validador.isValidaInteger(getCodigoClienteAgendar()) || !getCodigoClienteAgendar().equals(clienteControle.getClienteVO().getCodigo())) {
                listaAAgendar = new ArrayList<AgendaVO>();
                listaAAgendar = getFacade().getAgendaEstudio().aAgendarCliente(clienteControle.getClienteVO().getCodigo(), getEmpresaLogado().getCodigo(), null,null);
                setCodigoClienteAgendar(clienteControle.getClienteVO().getCodigo());
                try {
                    validarPermissao("AgendarPacoteForaPeriodoAgendamento", "11.04 - Agendar Pacote fora do Período de Agendamento", getUsuarioLogado());
                }catch (AcessoException erro){
                    //Se o usuário logado não possuir a permissão
                    //Será trago somente as vendas que não tenham informado o periodo de Agendamento
                    // ou estiverem dentre do período.
                    AgendaVO agenda;
                     for(int e = 0 ; e < listaAAgendar.size() ; e++){
                         agenda = listaAAgendar.get(e);
                         if(agenda.getDataIniAgendar() != null && agenda.getDataFimAgendar() != null){
                              if(!Calendario.entre(Calendario.hoje(),agenda.getDataIniAgendar(),agenda.getDataFimAgendar())){
                                  listaAAgendar.remove(e);
                              }
                         }
                     }
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("", e.getMessage());
            listaAAgendar.clear();
        }
    }

    /**
     * Método para selecionar todos os items da tabela de faturar
     */
    public void acaoSelecionarTodosAgendar() {
        for (AgendaVO agenda : getListaAAgendar()) {
            agenda.setSelecionarAgendar(getSelecionarTodosAgendar());
        }
    }

    /**
     * Método para selecionar apenas um item da tabela de faturar
     */
    public void acaoSelecionarUmAgendar() {

        int selecionados = 0;
        for (AgendaVO agenda : getListaAAgendar()) {
            if (agenda.getSelecionarAgendar()) {
                selecionados++;
            }
        }

        if (selecionados == getListaAAgendar().size()) {
            setSelecionarTodosAgendar(Boolean.TRUE);
        } else {
            setSelecionarTodosAgendar(Boolean.FALSE);
        }
    }

    /*
     * A FATURAR
     */
    /**
     * Retorna a lista de agenda A Faturar do cliente respectivo
     *
     * @return
     */
    public List<AgendaVO> getListaAFaturar() {
        try {
            ClienteControle clienteControle = (ClienteControle) JSFUtilities.getManagedBean("ClienteControle");
            clienteControle.pegarClienteTelaCliente();
            if (!Validador.isValidaInteger(getCodigoClienteFaturar()) || !getCodigoClienteFaturar().equals(clienteControle.getClienteVO().getCodigo())) {
                listaAFaturar = new ArrayList<AgendaVO>();

                listaAFaturar = getFacade().getAgendaEstudio().listarAFaturarCliente(clienteControle.getClienteVO().getCodigo(), getEmpresaLogado().getCodigo());
                setCodigoClienteFaturar(clienteControle.getClienteVO().getCodigo());
            }
            return listaAFaturar;
        } catch (Exception e) {
            setMensagemDetalhada("", e.getMessage());
        }
        return null;
    }

    /**
     * Prepara tela de venda com seus itens selecionados de a faturar e vai para
     * tela de venda avulsa com os itens selecionados
     *
     * @return
     */
    public String acaoPagarFaturar() {
        try {
            ClienteControle clienteControle = (ClienteControle) getControlador(ClienteControle.class.getSimpleName());
            clienteControle.pegarClienteTelaCliente();
            VendaAvulsaControle vendaAvulsaControle = (VendaAvulsaControle) getControlador(VendaAvulsaControle.class.getSimpleName());
            vendaAvulsaControle.novo();
            vendaAvulsaControle.getVendaAvulsaVO().setCliente(clienteControle.getClienteVO());
            vendaAvulsaControle.getVendaAvulsaVO().setNomeComprador(clienteControle.getClienteVO().getPessoa().getNome());

            setMapQuantidade(new HashMap<Integer, Integer>());
            for (AgendaVO agenda : getListaAFaturar()) {
                if (agenda.getSelecionarPagarFatura()) {

                    Integer key = agenda.getProdutoVO().getCodigo();
                    try {
                        getMapQuantidade().put(key, getMapQuantidade().get(key) + 1);
                    } catch (Exception e) {
                        getMapQuantidade().put(key, 1);
                    }
                }
            }

            List<DisponibilidadeVO> listaDisponibilidade = new ArrayList<DisponibilidadeVO>();
            for (AgendaVO agenda : getListaAFaturar()) {
                if (agenda.getSelecionarPagarFatura()) {
                    Integer key = agenda.getProdutoVO().getCodigo();
                    vendaAvulsaControle.getItemVendaAvulsaVO().setQuantidade(getMapQuantidade().get(key));

                    vendaAvulsaControle.getItemVendaAvulsaVO().setProduto(agenda.getProdutoVO());

                    vendaAvulsaControle.adicionarItemVendaAvulsa();

                    DisponibilidadeVO dis = new DisponibilidadeVO();
                    dis.setVerificadorAgendamento(Boolean.FALSE);
                    dis.setProdutoVO(agenda.getProdutoVO());
                    dis.setIdAgenda(agenda.getCodigo());
                    listaDisponibilidade.add(dis);
                }
            }

            FacesContext facesContext = FacesContext.getCurrentInstance();
            HttpSession session = (HttpSession) facesContext.getExternalContext().getSession(true);
            session.setAttribute("listaDisponibilidade", listaDisponibilidade);
        } catch (Exception e) {
            setMensagemDetalhada("", e.getMessage());
        }

        return "vendaAvulsa";
    }

    public void editarAgendamento() {
        try {
            AgendaAmbienteColaboradorControle agendaControle = (AgendaAmbienteColaboradorControle) getControlador(AgendaAmbienteColaboradorControle.class.getSimpleName());
            if (agendaControle.getConfiguracaoEstudioVO() == null) {
                ConfiguracaoEstudioVO obj = new ConfiguracaoEstudioVO();
                obj.setEmpresaVO(getEmpresaLogado());
                agendaControle.setConfiguracaoEstudioVO(getFacade().getConfiguracaoEstudio().consultarPorCodigo(configuracaoEstudioVO, false));
            }
            AgendaVO agendaVO = (AgendaVO) context().getExternalContext().getRequestMap().get("item");
            agendaControle.setItemAgenda(agendaVO);
            agendaControle.acaoAbrirToolTip();
            agendaControle.setApresentarTodosCampos(true);
            agendaControle.setOrigem(OrigemOperacaoEnum.HISTORICO_ALUNO);
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
        }
    }

    /**
     * Método para selecionar todos os items da tabela de faturar
     */
    public void acaoSelecionarTodosFaturar() {
        for (AgendaVO agenda : getListaAFaturar()) {
            agenda.setSelecionarPagarFatura(getSelecionarTodosFaturar());
        }
    }

    /**
     * Método para selecionar apenas um item da tabela de faturar
     */
    public void acaoSelecionarUmFaturar() {

        int selecionados = 0;
        for (AgendaVO agenda : getListaAFaturar()) {
            if (agenda.getSelecionarPagarFatura()) {
                selecionados++;
            }
        }

        if (selecionados == getListaAFaturar().size()) {
            setSelecionarTodosFaturar(Boolean.TRUE);
        } else {
            setSelecionarTodosFaturar(Boolean.FALSE);
        }
    }

    /*
     * AGENDA
     */
    /**
     * Método que traz a lista de horários disponíveis para o colaborador,
     * ambiente e data selecionada.
     *
     * @throws Exception
     */
    public void listarHorarios() throws Exception {
        try {
            List<SelectItem> listaHorariosAux = new ArrayList<SelectItem>();
            List<DisponibilidadeVO> listaHorarios = new ArrayList<DisponibilidadeVO>();
            if (getAgendaEstudio().getDataAula() != null && getAgendaEstudio().getAmbienteVO().getCodigo() != 0
                    && getAgendaEstudio().getColaboradorVO().getCodigo() != 0 && getAgendaEstudio().getProdutoVO().getCodigo() != 0) {
                List<ProdutoVO> listaProdutos = new ArrayList<ProdutoVO>();
                listaProdutos.add(getAgendaEstudio().getProdutoVO());
                List<Integer> listaColaboradoresLocal = new ArrayList<Integer>();
                listaColaboradoresLocal.add(getAgendaEstudio().getColaboradorVO().getCodigo());
                List<Integer> listaAmbientesLocal = new ArrayList<Integer>();
                listaAmbientesLocal.add(getAgendaEstudio().getAmbienteVO().getCodigo());
                listaHorarios = getFacade().getDisponibilidade().acaoPrincipal(listaProdutos, listaColaboradoresLocal, listaAmbientesLocal, new ArrayList<String>(),
                        new ArrayList<DiaDaSemanaEnum>(), Uteis.getSQLData(getAgendaEstudio().getDataAula()), Uteis.getSQLData(getAgendaEstudio().getDataAula()), getConfiguracaoEstudioVO(),
                        new ArrayList<DisponibilidadeVO>(), false, null);
            }
            SelectItem selectItem;
            Calendar calendar = Calendar.getInstance();
            for (DisponibilidadeVO disponibilidades : listaHorarios) {
                calendar.setTime(disponibilidades.getHoraInicial());
                selectItem = new SelectItem(calendar.get(Calendar.HOUR_OF_DAY), String.valueOf(calendar.get(Calendar.HOUR_OF_DAY)) + ":00");
                listaHorariosAux.add(selectItem);
            }
            getSelectItemHorarios().clear();
            getSelectItemHorarios().addAll(listaHorariosAux);
//            if (Validador.isValidaList(listaHorarios)) {
//                calendar.setTime(listaHorarios.get(0).getHoraInicial());
//                setTimeSelecionado(calendar.get(Calendar.HOUR_OF_DAY));
//            }
        } catch (NoSuchElementException e) {
            getSelectItemHorarios().clear();
        }
    }

    public void listarProfissionalCombo() throws Exception {
        try {
            List<SelectItem> profissionalComboAux = new ArrayList<SelectItem>();
            List<ColaboradorVO> listaColaboradorCombo = getFacade().getAgendaEstudio().buscarColaboradoresProduto(getAgendaEstudio().getProdutoVO().getCodigo(),
                    Uteis.getSQLData(getAgendaEstudio().getDataAula()), false);
            SelectItem selectItem;
            for (ColaboradorVO colaboradorAux : listaColaboradorCombo) {
                selectItem = new SelectItem(colaboradorAux, colaboradorAux.getPessoa().getNome());
                profissionalComboAux.add(selectItem);
            }
            getProfissionalCombo().clear();
            getProfissionalCombo().addAll(profissionalComboAux);
        } catch (Exception e) {
            setMensagemDetalhada(e.toString());
            setApresentarRichModalErro(Boolean.TRUE);
        }
    }

    public void listarAmbientesCombo() throws Exception {
        try {
            List<SelectItem> ambienteComboAux = new ArrayList<SelectItem>();
            List<AmbienteVO> listaAmbienteCombo = getFacade().getAgendaEstudio().buscarAmbientesProduto(getAgendaEstudio().getProdutoVO().getCodigo(),
                    Uteis.getSQLData(getAgendaEstudio().getDataAula()), false);
            SelectItem selectItem;
            for (AmbienteVO ambienteAux : listaAmbienteCombo) {
                selectItem = new SelectItem(ambienteAux, ambienteAux.getDescricao());
                ambienteComboAux.add(selectItem);
            }
            getAmbienteCombo().clear();
            getAmbienteCombo().addAll(ambienteComboAux);
        } catch (Exception e) {
            setMensagemDetalhada(e.toString());
            setApresentarRichModalErro(Boolean.TRUE);
        }
    }

    /**
     * Método que busca lista dos tipos de horários cadastrados
     *
     * @return Lista de SelectItem para Tipo horário
     * @throws Exception
     */
    public List<SelectItem> getBuscarListaTipoHorarios() throws Exception {
        try {
            setTiposHorarios(new ArrayList<SelectItem>());
            List<TipoHorarioVO> listaLegenda = getFacade().getTipoHorario().procurarTodosTiposHorario(getEmpresaLogado().getCodigo(), false, false);
            SelectItem selectItem;
            if(listaLegenda.size()==1)
            getAgendaEstudio().setTipoHorarioVO(listaLegenda.get(0));
            for (TipoHorarioVO legendas : listaLegenda) {
                selectItem = new SelectItem(legendas.getCodigo(), legendas.getDescricao());
                getTiposHorarios().add(selectItem);
            }
            return getTiposHorarios();
        } catch (NoSuchElementException e) {
            return new ArrayList<SelectItem>();
        }
    }

    /**
     * Método que atualiza os filtros do modal de agenda.
     *
     * @throws Exception
     */
    public void mudancaData() throws Exception {
        try {
            listarFiltroCombo();
        } catch (NoSuchElementException e) {
            getSelectItemHorarios().clear();
        }
    }

    public void listarFiltroCombo() throws Exception {
        listarProfissionalCombo();
        listarAmbientesCombo();
        listarHorarios();
    }

    /**
     * Ação para Inserir agenda.
     */
    public void acaoSalvarAgenda() {
        try {
            AgendaVO.validarDados(getTimeSelecionado(), getTimeHorarioInicial(), getTimeHorarioFinal(), getConfiguracaoEstudioVO().getHoraInicial());
            getAgendaEstudio().preencherHorario(getTimeSelecionado(),getTimeHorarioInicial(), getTimeHorarioFinal());
            if(Validador.isValidaInteger(getAgendaEstudio().getProdutoGenericoEscolhidoVO().getCodigo())){
                getAgendaEstudio().setProdutoVO(getAgendaEstudio().getProdutoGenericoEscolhidoVO());
            }
            if (getAgendaEstudio().getAmbienteVO().getCodigo() != 0 && getAgendaEstudio().getProdutoVO().getCodigo() != 0
                    && getAgendaEstudio().getColaboradorVO().getCodigo() != 0 && getAgendaEstudio().getDataAula() != null
                    && getAgendaEstudio().getTipoHorarioVO().getCodigo() != 0 && getAgendaEstudio().getHoraInicio() != null
                    && getAgendaEstudio().getHoraTermino() != null && getAgendaEstudio().getClienteVO().getCodigo() != 0
                    && getAgendaEstudio().getStatus() != null) {
                FacesContext context = FacesContext.getCurrentInstance();
                HttpSession sessao = (HttpSession) context.getExternalContext().getSession(false);
                UsuarioVO usuario = (UsuarioVO) sessao.getAttribute("logado");

                //Faz validação se pode fazer o agendamento
                List<ProdutoVO> listaProdutosAgenda = new ArrayList<ProdutoVO>();
                listaProdutosAgenda.add(getAgendaEstudio().getProdutoVO());
                List<Integer> listaColaboradoresAgenda = new ArrayList<Integer>();
                listaColaboradoresAgenda.add(getAgendaEstudio().getColaboradorVO().getCodigo());
                List<Integer> listaAmbientesAgenda = new ArrayList<Integer>();
                listaAmbientesAgenda.add(getAgendaEstudio().getAmbienteVO().getCodigo());
                List<String> listaHorasAgenda = new ArrayList<String>();
                listaHorasAgenda.add(getAgendaEstudio().getHoraInicio() + "/" + getAgendaEstudio().getHoraTermino());
                List<DisponibilidadeVO> disponivel = getFacade().getDisponibilidade().acaoPrincipal(listaProdutosAgenda, listaColaboradoresAgenda, listaAmbientesAgenda, listaHorasAgenda,
                        new ArrayList<DiaDaSemanaEnum>(), Uteis.getSQLData(getAgendaEstudio().getDataAula()),
                        Uteis.getSQLData(getAgendaEstudio().getDataAula()), getConfiguracaoEstudioVO(), new ArrayList<DisponibilidadeVO>(), false,getAgendaEstudio().getCodigo());

                if (disponivel.isEmpty()) {
                    throw new Exception("Não é possivel fazer o agendamento, Ambiente ou Produto lotado");
                }

                getAgendaEstudio().setUsuario(usuario);
                getAgendaEstudio().setEmpresaVO(getEmpresaLogado());
                //buscar os produtos que são permitidos em pacotes genericos vendidos
                List<ProdutoVO> listaProdutosPacoteGenerico = getFacade().getProduto().obterProdutosDePacotesGenericosDoCliente(getAgendaEstudio().getClienteVO().getCodigo());
                //se o produto escolhido pelo usuario for permitido em algum pacote vendido entao adicionar o produto do tipo generico
                ProdutoVO prodGenerico = null;
                for(ProdutoVO produto:listaProdutosPacoteGenerico){
                    if(produto.getCodigo()==getAgendaEstudio().getProdutoVO().getCodigo()){
                        prodGenerico = new ProdutoVO();
                    }
                }
                if(prodGenerico!=null){
                    getAgendaEstudio().setProdutoGenerico(true);
                }
                getAgendaEstudio().setCodigo(getFacade().getAgendaEstudio().incluirAgenda(getAgendaEstudio()));
                getAgendaEstudio().setResolvidoComo("1"); // Remarcação
                getFacade().getAgendaEstudio().atualizarAgendaExcecao(getAgendaEstudio());

                if (Validador.isValidaInteger(getAgendaEstudio().getVendaAvulsa())) {
                    getFacade().getAgendaEstudio().salvarRelacaoAgendaVenda(getAgendaEstudio().getCodigo(), getAgendaEstudio().getVendaAvulsa(), true);
                }


                //LOG - INICIO
                try {
                    getAgendaEstudio().setObjetoVOAntesAlteracao(new AgendaVO());
                    getAgendaEstudio().setNovoObj(true);
                    registrarLogObjetoVO(getAgendaEstudio(), getAgendaEstudio().getCodigo().intValue(), "AGENDA", getAgendaEstudio().getClienteVO().getPessoa().getCodigo());
                } catch (Exception e) {
                    registrarLogErroObjetoVO("AGENDA", getAgendaEstudio().getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE AGENDA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
                //LOG - FIM

                setMostrarPanelAluno(Boolean.FALSE);

                ClienteControle clienteControle = (ClienteControle) JSFUtilities.getManagedBean("ClienteControle");
                clienteControle.pegarClienteTelaCliente();
                setListaAgendaExcecao(getFacade().getAgendaEstudio().buscarAgendaExcecao(getEmpresaLogado().getCodigo(), null, null,
                        null, "", clienteControle.getClienteVO(), "0", false, false));
            } else {
                setApresentarRichModalErro(Boolean.TRUE);
                setMensagemDetalhada("É necessário preencher todos campos obrigatórios!");
            }
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
            setApresentarRichModalErro(Boolean.TRUE);
        }
    }

    public void acaoPanelAgendaAluno() throws Exception {
        try {
            ClienteControle clienteControle = (ClienteControle) JSFUtilities.getManagedBean("ClienteControle");
            clienteControle.pegarClienteTelaCliente();
            setCodigoClienteModal(clienteControle.getClienteVO().getCodigo());

            for (AgendaVO agenda : getListaAgendaExcecao()) {
                if (agenda.getSelecionarPendente()) {
                    if (!agenda.getResolvidoComo().equals("0")) {
                        throw new Exception("Aula não está pendente!");
                    }
                    setAgendaEstudio(agenda);

                    setMostrarPanelAluno(Boolean.TRUE);
                    getAgendaEstudio().setDataAula(Calendario.hoje());
                    getAgendaEstudio().setStatus(StatusEnum.REMARCADA);
                    listarFiltroCombo();
                    getAgendaEstudio().setClienteVO(clienteControle.getClienteVO());
                    getAgendaEstudio().setCodigo(null);

                }
            }

            //gera o horário quebrado
            if (!(agendaEstudio.getHoraInicio().toString().
                    equals(Calendario.gerarHorarioInicial(agendaEstudio.getHoraInicio()) + ":00")
                    && agendaEstudio.getHoraTermino().toString().
                    equals(Calendario.gerarHorarioFinal(agendaEstudio.getHoraTermino()) + ":59"))) {
                Calendar calendarInicio = Calendar.getInstance();
                calendarInicio.setTime(agendaEstudio.getHoraInicio());
                setTimeHorarioInicial(Calendario.gerarHorarioInicial(agendaEstudio.getHoraInicio()));
                setTimeHorarioFinal(Calendario.gerarHorarioFinal(agendaEstudio.getHoraTermino()));
                setTimeSelecionado(calendarInicio.get(Calendar.HOUR_OF_DAY));
                setApresentarBotaoRetirarHorario("S");
            } else {
                //gera o horário normal
                Calendar calendarInicio = Calendar.getInstance();
                calendarInicio.setTime(agendaEstudio.getHoraInicio());
                setTimeSelecionado(calendarInicio.get(Calendar.HOUR_OF_DAY));
                setTimeHorarioInicial("");
                setTimeHorarioFinal("");
                setApresentarBotaoRetirarHorario("N");
            }

            if(getAgendaEstudio().getProdutoVO().getDescricao().equals("PRODUTO GENÉRICO")){
                List<ProdutoVO> listaProdutos = getFacade().getProduto().obterProdutosDePacotesGenericosDoCliente(getAgendaEstudio().getCodigo());
                setServicoGenericoCombo(new ArrayList<SelectItem>());
                for(ProdutoVO produto: listaProdutos){
                    getServicoGenericoCombo().add(new SelectItem(produto.getCodigo(), produto.getDescricao()));
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
            setApresentarRichModalErro(Boolean.TRUE);
        }
    }

    public void acaoFecharPanelAgendaAluno() {
        setMostrarPanelAluno(Boolean.FALSE);
    }

    /**
     * Método para selecionar apenas um item da tabela de faturar
     */
    public void acaoSelecionarUmPendente() {

        for (AgendaVO agenda : getListaAgendaExcecao()) {
            if (agenda.getSelecionarPendente()) {
                if (!agenda.getCodigoAgendaExcecao().equals(getAgendaEstudio().getCodigoAgendaExcecao())) {
                    agenda.setSelecionarPendente(Boolean.FALSE);
                }
            }
        }
    }

    /*
     *
     */
    /**
     * Acão para fechar modal.
     */
    public void acaoFecharModalErro() {
        setApresentarRichModalErro(Boolean.FALSE);
    }

    public Boolean getSelecionarTodosFaturar() {
        return selecionarTodosFaturar;
    }

    public void setSelecionarTodosFaturar(Boolean selecionarTodosFaturar) {
        this.selecionarTodosFaturar = selecionarTodosFaturar;
    }

    public Integer getNmrPaginaFaturar() {
        return nmrPaginaFaturar;
    }

    public void setNmrPaginaFaturar(Integer nmrPaginaFaturar) {
        this.nmrPaginaFaturar = nmrPaginaFaturar;
    }

    public Integer getNmrPaginaAgendar() {
        return nmrPaginaAgendar;
    }

    public void setNmrPaginaAgendar(Integer nmrPaginaAgendar) {
        this.nmrPaginaAgendar = nmrPaginaAgendar;
    }

    public HashMap<Integer, Integer> getMapQuantidade() {
        return mapQuantidade;
    }

    public void setMapQuantidade(HashMap<Integer, Integer> mapQuantidade) {
        this.mapQuantidade = mapQuantidade;
    }

    public void setListaAFaturar(List<AgendaVO> listaAFaturar) {
        this.listaAFaturar = listaAFaturar;
    }

    public Integer getCodigoClienteAgendar() {
        return codigoClienteAgendar;
    }

    public void setCodigoClienteAgendar(Integer codigoClienteAgendar) {
        this.codigoClienteAgendar = codigoClienteAgendar;
    }

    public Integer getCodigoClienteFaturar() {
        return codigoClienteFaturar;
    }

    public void setCodigoClienteFaturar(Integer codigoClienteFaturar) {
        this.codigoClienteFaturar = codigoClienteFaturar;
    }

    public void setListaItemVenda(List<ItemVendaAvulsaVO> listaItemVenda) {
        this.listaItemVenda = listaItemVenda;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getNmrPaginaItemVenda() {
        return nmrPaginaItemVenda;
    }

    public void setNmrPaginaItemVenda(Integer nmrPaginaItemVenda) {
        this.nmrPaginaItemVenda = nmrPaginaItemVenda;
    }

    public Boolean getVerTodosModal() {
        return verTodosModal;
    }

    public void setVerTodosModal(Boolean verTodosModal) {
        this.verTodosModal = verTodosModal;
    }

    public void setListaHistoricoAgenda(List<AgendaVO> listaHistoricoAgenda) {
        this.listaHistoricoAgenda = listaHistoricoAgenda;
    }

    public Integer getCodigoClienteModal() {
        return codigoClienteModal;
    }

    public void setCodigoClienteModal(Integer codigoClienteModal) {
        this.codigoClienteModal = codigoClienteModal;
    }

    public Boolean getSelecionarTodosAgendar() {
        return selecionarTodosAgendar;
    }

    public void setSelecionarTodosAgendar(Boolean selecionarTodosAgendar) {
        this.selecionarTodosAgendar = selecionarTodosAgendar;
    }

    public AgendaVO getAgendaEstudio() {
        return agendaEstudio;
    }

    public void setAgendaEstudio(AgendaVO agendaEstudio) {
        this.agendaEstudio = agendaEstudio;
    }

    public List<ClienteVO> getListaCliente() {
        if (listaCliente == null) {
            listaCliente = new ArrayList<ClienteVO>();
        }
        return listaCliente;
    }

    public void setListaCliente(List<ClienteVO> listaCliente) {
        this.listaCliente = listaCliente;
    }

    public List<SelectItem> getAmbienteCombo() {
        return ambienteCombo;
    }

    public void setAmbienteCombo(List<SelectItem> ambienteCombo) {
        this.ambienteCombo = ambienteCombo;
    }

    public List<SelectItem> getProfissionalCombo() {
        return profissionalCombo;
    }

    public void setProfissionalCombo(List<SelectItem> profissionalCombo) {
        this.profissionalCombo = profissionalCombo;
    }

    public List<SelectItem> getSelectItemHorarios() {
        return selectItemHorarios;
    }

    public void setSelectItemHorarios(List<SelectItem> selectItemHorarios) {
        this.selectItemHorarios = selectItemHorarios;
    }

    public List<SelectItem> getServicoCombo() {
        return servicoCombo;
    }

    public void setServicoCombo(List<SelectItem> servicoCombo) {
        this.servicoCombo = servicoCombo;
    }

    public List<SelectItem> getTiposHorarios() {
        return tiposHorarios;
    }

    public void setTiposHorarios(List<SelectItem> tiposHorarios) {
        this.tiposHorarios = tiposHorarios;
    }

    public ConfiguracaoEstudioVO getConfiguracaoEstudioVO() {
        return configuracaoEstudioVO;
    }

    public void setConfiguracaoEstudioVO(ConfiguracaoEstudioVO configuracaoEstudioVO) {
        this.configuracaoEstudioVO = configuracaoEstudioVO;
    }

    public Boolean getVerTodosModalAgenda() {
        return verTodosModalAgenda;
    }

    public void setVerTodosModalAgenda(Boolean verTodosModalAgenda) {
        this.verTodosModalAgenda = verTodosModalAgenda;
    }

    public void setListaAgendaExcecao(List<AgendaVO> listaAgendaExcecao) {
        this.listaAgendaExcecao = listaAgendaExcecao;
    }

    public Integer getTimeSelecionado() {
        return timeSelecionado;
    }

    public void setTimeSelecionado(Integer timeSelecionado) {
        this.timeSelecionado = timeSelecionado;
    }

    public List<ColaboradorVO> getProfissionalSuggest() {
        return profissionalSuggest;
    }

    public void setProfissionalSuggest(List<ColaboradorVO> profissionalSuggest) {
        this.profissionalSuggest = profissionalSuggest;
    }

    public Boolean getMostrarPanelAluno() {
        return mostrarPanelAluno;
    }

    public void setMostrarPanelAluno(Boolean mostrarPanelAluno) {
        this.mostrarPanelAluno = mostrarPanelAluno;
    }

    public boolean isApresentarRichModalErro() {
        return apresentarRichModalErro;
    }

    public void setApresentarRichModalErro(boolean apresentarRichModalErro) {
        this.apresentarRichModalErro = apresentarRichModalErro;
    }

    public String getTitle() {
        StringBuilder str = new StringBuilder();
        int agendar = listaAAgendar == null ? 0 : listaAAgendar.size();
        int faturar = listaAFaturar == null ? 0 : listaAFaturar.size();
        str.append("[").append(agendar).append(" - A Agendar] ");
        str.append("[").append(faturar).append(" - A Faturar] ");
        return str.toString();
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public void consultarPaginadoListener(ActionEvent evt) {
        // VERIFICACAO NECESSARIA POR CAUSA DOS FILTROS
        Object compPaginaInicial = evt.getComponent().getAttributes().get(
                "paginaInicial");

        if (compPaginaInicial != null
                && !"".equals(compPaginaInicial.toString())) {
            if (compPaginaInicial.toString().equals("paginaInicial")) {
                setConfPaginacao(new ConfPaginacao());

            }
        }

        // Obtendo qual pagina deverá ser exibida
        Object component = evt.getComponent().getAttributes().get(
                "pagNavegacao");

        if (component != null && !"".equals(component.toString())) {
            getConfPaginacao().setPagNavegacao(component.toString());

        } // ==================================================================================================================================

        Integer cod = (Integer) evt.getComponent().getAttributes().get("codigoCliente");
        setCodigoCliente(cod);
        consultarPaginado();

    }

    /**
     * Metodo responsavel por retornar uma consulta paginada em banco
     *
     * Autor: Pedro Y. Saito Criado em 27/12/2010
     */
    @SuppressWarnings("unchecked")
    public void consultarPaginado() {
        try {
            super.consultar();
            List objs = new ArrayList();
            getConfPaginacao().setItensPorPagina(10);

            objs = getFacade().getVendaAvulsa().consultarPaginado(getCodigoCliente(),
                    getEmpresaLogado().getCodigo(), getConfPaginacao());

            setListaItemVenda(objs);

            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);

        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            System.out.println("WARNING -> " + this.getClass().getSimpleName()
                    + " => " + e.getMessage());

        }
    }

    public void adicionarHorario() {
        try {
            if (getTimeSelecionado() != -1 && getTimeHorarioInicial().isEmpty() && getTimeHorarioFinal().isEmpty()) {
                Time horaInicial = Formatador.obterTime((getTimeSelecionado() < 10 ? "0" + getTimeSelecionado() : getTimeSelecionado()) + ":00");
                Time horaFinal = Formatador.obterTimeComSegundos((getTimeSelecionado() < 10 ? "0" + getTimeSelecionado() : getTimeSelecionado()) + ":59:59");
                setTimeHorarioInicial(Calendario.gerarHorarioInicial(horaInicial));
                setTimeHorarioFinal(Calendario.gerarHorarioFinal(horaFinal));
            }
            setApresentarBotaoRetirarHorario("S");
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
        }
    }

    public void retirarHorario() {
        try {
            setTimeHorarioInicial("");
            setTimeHorarioFinal("");
            setApresentarBotaoRetirarHorario("N");
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
        }

    }

    /**
     * @return the listaAgendamentosDeVendas
     */
    public List<AgendaVO> getListaAgendamentosDeVendas() {
        return listaAgendamentosDeVendas;
    }

    /**
     * @param listaAgendamentosDeVendas the listaAgendamentosDeVendas to set
     */
    public void setListaAgendamentosDeVendas(List<AgendaVO> listaAgendamentosDeVendas) {
        this.listaAgendamentosDeVendas = listaAgendamentosDeVendas;
    }

    /**
     * @return the itemVendaVO
     */
    public ItemVendaAvulsaVO getItemVendaVO() {
        return itemVendaVO;
    }

    /**
     * @param itemVendaVO the itemVendaVO to set
     */
    public void setItemVendaVO(ItemVendaAvulsaVO itemVendaVO) {
        this.itemVendaVO = itemVendaVO;
    }

    /**
     * @return the qtdUtilizadaSessoes
     */
    public Integer getQtdUtilizadaSessoes() {
        return qtdUtilizadaSessoes;
    }

    /**
     * @param qtdUtilizadaSessoes the qtdUtilizadaSessoes to set
     */
    public void setQtdUtilizadaSessoes(Integer qtdUtilizadaSessoes) {
        this.qtdUtilizadaSessoes = qtdUtilizadaSessoes;
    }

    /**
     * @return the qtdRestanteSessoes
     */
    public Integer getQtdRestanteSessoes() {
        return qtdRestanteSessoes;
    }

    /**
     * @param qtdRestanteSessoes the qtdRestanteSessoes to set
     */
    public void setQtdRestanteSessoes(Integer qtdRestanteSessoes) {
        this.qtdRestanteSessoes = qtdRestanteSessoes;
    }

    /**
     * @return the qtdRestanteAAgendar
     */
    public Integer getQtdRestanteAAgendar() {
        return qtdRestanteAAgendar;
    }

    /**
     * @param qtdRestanteAAgendar the qtdRestanteAAgendar to set
     */
    public void setQtdRestanteAAgendar(Integer qtdRestanteAAgendar) {
        this.qtdRestanteAAgendar = qtdRestanteAAgendar;
    }

    /**
     * @return the apresentarBotaoRetirarHorario
     */
    public String getApresentarBotaoRetirarHorario() {
        return apresentarBotaoRetirarHorario;
    }

    /**
     * @param apresentarBotaoRetirarHorario the apresentarBotaoRetirarHorario to set
     */
    public void setApresentarBotaoRetirarHorario(String apresentarBotaoRetirarHorario) {
        this.apresentarBotaoRetirarHorario = apresentarBotaoRetirarHorario;
    }

    /**
     * @return the timeHorarioInicial
     */
    public String getTimeHorarioInicial() {
        return timeHorarioInicial;
    }

    /**
     * @param timeHorarioInicial the timeHorarioInicial to set
     */
    public void setTimeHorarioInicial(String timeHorarioInicial) {
        this.timeHorarioInicial = timeHorarioInicial;
    }

    /**
     * @return the timeHorarioFinal
     */
    public String getTimeHorarioFinal() {
        return timeHorarioFinal;
    }

    /**
     * @param timeHorarioFinal the timeHorarioFinal to set
     */
    public void setTimeHorarioFinal(String timeHorarioFinal) {
        this.timeHorarioFinal = timeHorarioFinal;
    }

    /**
     * @return the servicoGenericoCombo
     */
    public List<SelectItem> getServicoGenericoCombo() {
        return servicoGenericoCombo;
    }

    /**
     * @param servicoGenericoCombo the servicoGenericoCombo to set
     */
    public void setServicoGenericoCombo(List<SelectItem> servicoGenericoCombo) {
        this.servicoGenericoCombo = servicoGenericoCombo;
    }
    public List<AgendaVO> getListaAAgendar(){
        if(listaAAgendar == null){
            listaAAgendar = new ArrayList<AgendaVO>();
        }
        return listaAAgendar;
    }
    public void setListaAAgendar(List<AgendaVO> listaAAgendar){
        this.listaAAgendar = listaAAgendar;
    }
}
