package br.com.pactosolucoes.estudio.controle;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.SituacaoParcelaEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.estudio.enumeradores.OrigemOperacaoEnum;
import br.com.pactosolucoes.estudio.enumeradores.PeriodoEnum;
import br.com.pactosolucoes.estudio.enumeradores.StatusEnum;
import br.com.pactosolucoes.estudio.modelo.AgendaVO;
import br.com.pactosolucoes.estudio.modelo.ConfiguracaoEstudioVO;
import br.com.pactosolucoes.estudio.modelo.RelatorioComissaoVO;
import br.com.pactosolucoes.estudio.modelo.TipoHorarioVO;
import br.com.pactosolucoes.estudio.util.Validador;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.sql.Time;
import java.util.*;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpSession;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.AmbienteVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import relatorio.arquitetura.GeradorRelatorio;
import relatorio.controle.arquitetura.SuperControleRelatorio;

/**
 *
 * <AUTHOR> Eugênio - GeoInova Soluções
 */
public class RelatorioComissaoControle extends SuperControleRelatorio {

    private ConfiguracaoEstudioVO configuracaoEstudioVO;
    private AmbienteVO ambienteVO;
    private ColaboradorVO colaboradorVO;
    private ProdutoVO produtoVO;
    private PeriodoEnum periodoEnum;
    private List<AmbienteVO> listaAmbiente;
    private List<Integer> listaAmbienteSelecionado;
    private List<String> listaNomeAmbiente;
    private List<ColaboradorVO> listaColaboradorVO;
    private List<Integer> listaColaboradorSelecionado;
    private List<String> listaNomeColaborador;
    private List<ProdutoVO> listaServico;
    private List<ProdutoVO> listaServicoTabela;
    private List<String> listaNomeServico;
    private List<TipoHorarioVO> listaTipoHorario;
    private List<Integer> listaTipoHorarioSelecionado;
    private List<String> listaNomeTipoHorario;
    private List<String> listaStatusSelecionado;
    private List<String> listaNomeStatus;
    private List<SelectItem> selectHorario;
    private List<RelatorioComissaoVO> listaRelatorioComissao;
    private Boolean selecionarTodosItemServico = Boolean.FALSE;
    private Boolean selecionarTodosItemAmbiente = Boolean.FALSE;
    private Boolean selecionarTodosItemPessoa = Boolean.FALSE;
    private Boolean apresentarRichModalErro;
    private Boolean abrirPopupDownload;
    private Integer numeroClientes;
    private BigDecimal somaUnitario;
    private BigDecimal somaComissao;
    private Date periodoInicial;
    private Date periodoFinal;
    private Time horaInicial;
    private Time horaFinal;
    private SituacaoParcelaEnum situacaoEnum;

    private List<Integer> listaUmColaboradorSelecionado;
    private List<Integer> listaDoisColaboradorSelecionado;

    /**
     * Método de entrada para limpar e iniciar os campos, objetos da tela e
     * preencher a configuração do módulo para construção das horas inicial e
     * final
     *
     * @param
     * @return
     * @throws Exception
     */
    public String acaoEntrar() throws Exception {

        setMensagemDetalhada("", "");
        setPeriodoInicial(null);
        setPeriodoFinal(null);
        setProdutoVO(new ProdutoVO());
        setApresentarRichModalErro(Boolean.FALSE);
        setListaRelatorioComissao(new ArrayList<RelatorioComissaoVO>());

        FacesContext facesContext = FacesContext.getCurrentInstance();
        HttpSession session = (HttpSession) facesContext.getExternalContext().getSession(true);
        setConfiguracaoEstudioVO((ConfiguracaoEstudioVO) session.getAttribute("configuracaoEstudio"));
        AgendaAmbienteColaboradorControle agendaControle = (AgendaAmbienteColaboradorControle) getControlador(AgendaAmbienteColaboradorControle.class.getSimpleName());
        agendaControle.setApresentarTodosCampos(false);
        return "relatorioComissaoEstudio";
    }

    /**
     * Método para pesquisar e preencher objetos de lista da tela
     *
     * @throws Exception
     */
    public void acaoPesquisar(){
        try {

            if (getPeriodoInicial() == null) {
                setPeriodoInicial(Calendario.getDataComHoraZerada(Calendario.hoje()));
            }

            if (getPeriodoFinal() == null) {
                setPeriodoFinal(Uteis.obterUltimoDiaMes(Calendario.getDataComHoraZerada(Calendario.hoje())));
            }

            setListaRelatorioComissao(getFacade().getRelatorioComissao().listarComissao(getEmpresaLogado().getCodigo(), Uteis.getSQLData(getPeriodoInicial()), Uteis.getSQLData(getPeriodoFinal()),
                    getHoraInicial(), getHoraFinal(), getListaColaboradorSelecionado(), getListaAmbienteSelecionado(), acaoRetornaListaServico(getListaServicoTabela()), getListaStatusSelecionado(),
                    getListaTipoHorarioSelecionado(), situacaoEnum));

            // Totalizador de Clientes
            setNumeroClientes(getFacade().getRelatorioComissao().contagemClientes(getEmpresaLogado().getCodigo(), Uteis.getSQLData(getPeriodoInicial()), Uteis.getSQLData(getPeriodoFinal()),
                    getHoraInicial(), getHoraFinal(), getListaColaboradorSelecionado(), getListaAmbienteSelecionado(), acaoRetornaListaServico(getListaServicoTabela()), getListaStatusSelecionado(), getListaTipoHorarioSelecionado()));

            if (Validador.isValidaList(getListaRelatorioComissao())) {
                setSomaComissao(getListaRelatorioComissao().get(0).getSomaComissao().setScale(2, RoundingMode.HALF_UP));
                setSomaUnitario(getListaRelatorioComissao().get(0).getSomaUnitario().setScale(2, RoundingMode.HALF_UP));
            }

        } catch (Exception e) {
             setListaRelatorioComissao(new ArrayList<RelatorioComissaoVO>());
            setApresentarRichModalErro(Boolean.TRUE);
            setMensagemDetalhada("", e.getMessage());
        }
    }

    public void acaoImprimir() {
        try {
            Map<String, Object> parametros = new HashMap<String, Object>();
            prepareParams(parametros);
            apresentarRelatorioObjetos(parametros);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsavel por gerar a impressão da Meta Passivo em Excel
     *
     * <AUTHOR>
     */
    public void acaoImprimirExcel() {
        try {
            setAbrirPopupDownload(true);
            Map<String, Object> parametros = new HashMap<String, Object>();
            prepareParams(parametros);
            GeradorRelatorio obj = new GeradorRelatorio();
            obj.montarRelatorio("OBJETO", "EXCEL", "comissaoEstudioPactoExcel", getIsDesignIReportRelatorioExcel("comissaoEstudioPactoExcel"), getListaRelatorioComissao(), parametros);
        } catch (Exception e) {
            setAbrirPopupDownload(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método que prepara os parâmetros do relatório em PDF
     *
     * @param params
     * @throws Exception
     */
    private void prepareParams(Map<String, Object> params) throws Exception {
        Integer emp = getUsuarioLogado().getAdministrador() ? 0 : getEmpresaLogado().getCodigo();
        EmpresaVO empre = new EmpresaVO();
        if (emp != 0) {
            empre = getFacade().getEmpresa().consultarPorChavePrimaria(emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        String filtros = "Período: " + Formatador.formatarDataPadrao(getPeriodoInicial()) + " a " + Formatador.formatarDataPadrao(getPeriodoFinal());
        filtros += "<br>Horários: " + (Validador.isValidaObject(getHoraInicial()) && Validador.isValidaObject(getHoraFinal()) ? (Formatador.formatarHorario(getHoraInicial()) + " a " + Formatador.formatarHorario(getHoraFinal())) : "TODOS");
        filtros += "<br>Ambiente: " + (Validador.isValidaList(getListaNomeAmbiente()) ? getListaNomeAmbiente().toString() : "TODOS");
        filtros += "<br>Profissional: " + (Validador.isValidaList(getListaNomeColaborador()) ? getListaNomeColaborador().toString() : "TODOS");
        filtros += "<br>Produto: " + (Validador.isValidaList(getListaNomeServico()) ? getListaNomeServico().toString() : "TODOS");
        filtros += "<br>Tipos de Horário: " + (Validador.isValidaList(getListaNomeTipoHorario()) ? getListaNomeTipoHorario().toString() : "TODOS");
        filtros += "<br>Status da Sessão: " + (Validador.isValidaList(getListaNomeStatus()) ? getListaNomeStatus().toString() : "TODOS");

        params.put("nomeRelatorio", "comissaoEstudioPacto");
        params.put("nomeEmpresa", empre.getNome());
        params.put("enderecoEmpresa", empre.getEndereco());
        params.put("cidadeEmpresa", empre.getCidade().getNome());

        params.put("tituloRelatorio", "Gestão da Comissão");
        params.put("nomeDesignIReport", getDesignIReportRelatorio());

        params.put("nomeUsuario", getUsuarioLogado().getNomeAbreviado());
        params.put("usuario", getUsuarioLogado().getNomeAbreviado());

        params.put("listaObjetos", getListaRelatorioComissao());
        params.put("totalComissao", getSomaComissao());
        params.put("totalUnitario", getSomaUnitario());
        params.put("totalCliente", getNumeroClientes());
        params.put("filtros", filtros.replace("[", "").replace("]", ""));
    }

    /**
     * Método para preencher a combolist de horário.
     *
     * @return
     */
    public List<SelectItem> getSelectHorario() {
        if (selectHorario == null) {
            selectHorario = new ArrayList<SelectItem>();
        }
        try {
            if (selectHorario.isEmpty()) {
                if (getConfiguracaoEstudioVO().getHoraInicial() == null || getConfiguracaoEstudioVO().getHoraFinal() == null) {
                    throw new Exception("Informe o horário de abertura e fechamento da academia em configurações de empresa");
                }
                Calendar calendarInicial = Calendar.getInstance();
                calendarInicial.setTime(getConfiguracaoEstudioVO().getHoraInicial());
                Calendar calendarFinal = Calendar.getInstance();
                calendarFinal.setTime(getConfiguracaoEstudioVO().getHoraFinal());
                int calFinal = (calendarFinal.get(Calendar.HOUR_OF_DAY) == 00 ? 24 : calendarFinal.get(Calendar.HOUR_OF_DAY));

                SelectItem selectItem;
                for (int i = calendarInicial.get(Calendar.HOUR_OF_DAY); i < calFinal; i++) {
                    Calendar calAux = new GregorianCalendar();
                    calAux.set(Calendar.HOUR_OF_DAY, i);

                    String hora = String.valueOf(calAux.get(Calendar.HOUR_OF_DAY)).length() == 1 ? "0" + String.valueOf(calAux.get(Calendar.HOUR_OF_DAY)) : String.valueOf(calAux.get(Calendar.HOUR_OF_DAY));

                    selectItem = new SelectItem(Formatador.obterTime(hora + ":00"), hora + ":00");
                    selectHorario.add(selectItem);
                }
            }
        } catch (Exception e) {
            setApresentarRichModalErro(Boolean.TRUE);
            setMensagemDetalhada("", e.getMessage());
        }

        return selectHorario;
    }

    /**
     * Método básico para limpar todos os objetos e campos da tela sendo
     * utilizado ao iniciar a tela ou ao apertar o botão de limpar
     *
     * @param
     * @return
     * @throws Exception
     */
    public void limparDados() throws Exception {
        setMensagemDetalhada("", "");
        setPeriodoInicial(null);
        setPeriodoFinal(null);
        setProdutoVO(new ProdutoVO());
        setPeriodoEnum(PeriodoEnum.EMBRANCO);
        setApresentarRichModalErro(Boolean.FALSE);
        setListaServico(new ArrayList<ProdutoVO>());
        setListaServicoTabela(new ArrayList<ProdutoVO>());
        setListaAmbienteSelecionado(new ArrayList<Integer>());
        setListaColaboradorSelecionado(new ArrayList<Integer>());
        setListaStatusSelecionado(new ArrayList<String>());
        setListaTipoHorarioSelecionado(new ArrayList<Integer>());
        setListaRelatorioComissao(new ArrayList<RelatorioComissaoVO>());
    }

    /**
     * Método que calcula e seta nos objetos de periodo os intervalos
     * correspondentes a partir da data de hoje
     *
     * @param
     * @return
     */
    public void acaoPeriodoIntervalo() {
        Calendar calendar = Calendar.getInstance();
        if (getPeriodoEnum().equals(PeriodoEnum.HOJE)) {
            setPeriodoFinal(calendar.getTime());
            setPeriodoInicial(calendar.getTime());
        }else if (getPeriodoEnum().equals(PeriodoEnum.PROXSEMANA)) {
            setPeriodoInicial(calendar.getTime());
            calendar.add(Calendar.DAY_OF_MONTH, 7);
            setPeriodoFinal(calendar.getTime());
        } else if (getPeriodoEnum().equals(PeriodoEnum.PROXMES)) {
            setPeriodoInicial(calendar.getTime());
            calendar.add(Calendar.MONTH, 1);
            setPeriodoFinal(calendar.getTime());
        } else if (getPeriodoEnum().equals(PeriodoEnum.BIMESTRE)) {
            setPeriodoInicial(calendar.getTime());
            calendar.add(Calendar.MONTH, 2);
            setPeriodoFinal(calendar.getTime());
        } else if (getPeriodoEnum().equals(PeriodoEnum.TRIMESTRE)) {
            setPeriodoInicial(calendar.getTime());
            calendar.add(Calendar.MONTH, 3);
            setPeriodoFinal(calendar.getTime());
        } else if (getPeriodoEnum().equals(PeriodoEnum.SEMESTRE)) {
            setPeriodoInicial(calendar.getTime());
            calendar.add(Calendar.MONTH, 6);
            setPeriodoFinal(calendar.getTime());
        } else if (getPeriodoEnum().equals(PeriodoEnum.ANO)) {
            setPeriodoInicial(calendar.getTime());
            calendar.add(Calendar.YEAR, 1);
            setPeriodoFinal(calendar.getTime());
        }
    }

    /**
     * Método para setar a variavel do modal de erro como FALSE
     *
     * @return
     */
    public void acaoFecharModalErro() {
        setApresentarRichModalErro(Boolean.FALSE);
    }

    /*
     * Ações lista Servico
     */
    public String acaoFecharServico() {
        setListaNomeServico(new ArrayList<String>());
        if (!getListaServicoTabela().isEmpty()) {
            for (ProdutoVO produto : getListaServicoTabela()) {
                if (produto.getSelecionado()) {
                    getListaNomeServico().add(produto.getDescricao());
                }
            }
        }
        return "";
    }

    /**
     * Método que busca o produto pelo código digitado na tela e preenche na
     * tela com a descrição completa
     *
     * @param
     * @return
     * @throws Exception
     */
    public void acaoProcurarServico() throws Exception {
        try {
            if (getProdutoVO() != null && getProdutoVO().getCodigo() != 0) {
                setProdutoVO(getFacade().getProduto().consultarPorCodigoProdutoAtivo(getProdutoVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            } else {
                setProdutoVO(new ProdutoVO());
            }
        } catch (Exception e) {
            setApresentarRichModalErro(Boolean.TRUE);
            setMensagemDetalhada("", e.getMessage());
        }
    }

    /**
     * Método que adiciona o objeto ProdutoVO, passando pela ação do suggestion,
     * do botão Adicionar
     *
     * @throws Exception
     */
    public void acaoAdicionarServico() throws Exception {
        this.adicionaServicoALista(getProdutoVO());
    }

    /**
     * Método que responsável por retirar o objeto da lista de Produtos
     * sugeridos e passá-lo para a lista de Produtos selecionados
     *
     * @param objeto
     */
    private void adicionaServicoALista(ProdutoVO objeto) throws Exception {
        try {
            setApresentarRichModalErro(Boolean.FALSE);
            if (getProdutoVO().getValidarDados() != null && !getProdutoVO().getDescricao().isEmpty()) {

                if (!getListaServicoTabela().contains(objeto) && objeto.getCodigo() != 0) {
                    objeto.setSelecionado(Boolean.TRUE);
                    getListaServicoTabela().add(objeto);
                    setProdutoVO(new ProdutoVO());
                }
                acaoSelecionarUmServico();
            } else {
                throw new Exception("É necessário selecionar um Produto!");
            }
        } catch (Exception e) {
            setApresentarRichModalErro(Boolean.TRUE);
            setMensagemDetalhada("", e.getMessage());
        }
    }

    /**
     * Método ação remover, retira o objeto da lista de selecionados e o volta
     * para a lista de ambientes disponíveis.
     */
    public void acaoRemoverServico() {
        getListaServicoTabela().remove(getProdutoVO());
        setProdutoVO(new ProdutoVO());
    }

    /**
     * Método utilizado pelo componente Suggest do RichFaces, traz a lista de
     * Produtos sugeridos
     *
     * @param suggest, parametro digitado no inputText
     * @return Lista de Produtos sugeridos
     * @throws Exception
     */
    public List<ProdutoVO> listarServico(Object suggest) throws Exception {
        String pref = (String) suggest;
        setListaServico(getFacade().getProduto().consultarPorDescricaoTipoProdutoAtivo("", "SS", false, Uteis.NIVELMONTARDADOS_MINIMOS));
        ArrayList<ProdutoVO> result = new ArrayList<ProdutoVO>();

        Iterator<ProdutoVO> iterator = getListaServico().iterator();
        while (iterator.hasNext()) {
            ProdutoVO elem = ((ProdutoVO) iterator.next());
            if ((elem.getDescricao() != null && elem.getDescricao().toLowerCase().indexOf(pref.toLowerCase()) == 0) || "".equals(pref)) {
                result.add(elem);
            }
        }

        return result;

    }

    /**
     * Método para selecionar todos os items da tabela de produto
     */
    public void acaoSelecionarTodosServico() {
        for (ProdutoVO prod : getListaServicoTabela()) {
            prod.setSelecionado(getSelecionarTodosItemServico());
        }
    }

    /**
     * Método para selecioanr apenas um item da tabela de produto
     */
    public void acaoSelecionarUmServico() {
        int selecionados = 0;
        for (ProdutoVO prod : getListaServicoTabela()) {
            if (prod.getSelecionado()) {
                selecionados++;
            }
        }

        if (selecionados == getListaServicoTabela().size()) {
            setSelecionarTodosItemServico(Boolean.TRUE);
        } else {
            setSelecionarTodosItemServico(Boolean.FALSE);
        }
    }

    /**
     *
     * @param listaServicoTabela
     * @return
     */
    public List<Integer> acaoRetornaListaServico(List<ProdutoVO> listaServicoTabela) {

        List<Integer> listaProdutoSelecionado = new ArrayList<Integer>();

        for (ProdutoVO produto : listaServicoTabela) {
            if (produto.getSelecionado()) {
                listaProdutoSelecionado.add(produto.getCodigo());
            }
        }

        return listaProdutoSelecionado;

    }

    /*
     * Ações lista Profissional
     */
    public String acaoFecharPessoa() {
        setListaNomeColaborador(new ArrayList<String>());
        preencherListaColaboradorSelecionado();

        if (!getListaColaboradorSelecionado().isEmpty()) {
            for (ColaboradorVO colab : getListaColaboradorVO()) {
                for (int i = 0; i < getListaColaboradorSelecionado().size(); i++) {
                    if (String.valueOf(colab.getCodigo()).equals(String.valueOf(getListaColaboradorSelecionado().get(i)))) {
                        getListaNomeColaborador().add(colab.getPessoa().getNome());
                    }
                }
            }
        }
        return "";
    }

    /**
     * consultarPorTipoProduto Ação para retornar no modal com a descrição do
     * colaborador
     *
     * @param
     * @return SelectItem[] de Ambiente
     * @throws Exception
     */
    public SelectItem[] getListaPessoaSelect() {
        SelectItem[] status = new SelectItem[getListaColaboradorVO().size()];
        int i = -1;
        for (ColaboradorVO mp : getListaColaboradorVO()) {
            status[++i] = new SelectItem(mp.getCodigo(), mp.getPessoa().getNome().trim());
        }
        return status;
    }

    /**
     * Ação para preencher lista de ambiente
     * @throws Exception
     */
    public void acaoBuscarPessoa() throws Exception {
        if (getListaColaboradorVO() == null || getListaColaboradorVO().isEmpty()) {
            List<ColaboradorVO> lista = getFacade().getColaborador().consultarPorTipoColaboradorAtivo(TipoColaboradorEnum.ESTUDIO, "AT", getConfiguracaoEstudioVO().getEmpresaVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Ordenacao.ordenarLista(lista,"pessoa_Apresentar");
            setListaColaboradorVO(lista);
        }
    }

    /*
     * Ações lista Ambiente
     */
    public String acaoFecharAmbiente() {
        setListaNomeAmbiente(new ArrayList<String>());
        if (!getListaAmbienteSelecionado().isEmpty()) {
            for (AmbienteVO amb : getListaAmbiente()) {
                for (int i = 0; i < getListaAmbienteSelecionado().size(); i++) {
                    if (String.valueOf(amb.getCodigo()).equals(String.valueOf(getListaAmbienteSelecionado().get(i)))) {
                        getListaNomeAmbiente().add(amb.getDescricao());
                    }
                }
            }
        }
        return "";
    }

    /**
     * Ação para retornar no modal com a descrição do ambiente
     *
     * @param
     * @return SelectItem[] de Ambiente
     * @throws Exception
     */
    public SelectItem[] getListaAmbienteSelect() {
        SelectItem[] status = new SelectItem[getListaAmbiente().size()];
        int i = -1;
        for (AmbienteVO mp : getListaAmbiente()) {
            status[++i] = new SelectItem(mp.getCodigo(), mp.getDescricao().trim());
        }
        return status;
    }

    /**
     * Ação para preencher lista de ambiente
     *
     * @param
     * @return
     * @throws Exception
     */
    public void acaoBuscarAmbiente() throws Exception {
        try {
            if (getListaAmbiente() == null || getListaAmbiente().isEmpty()) {
                List<AmbienteVO> lista = getFacade().getAmbiente().consultarPorDescricaoPorAtivoEstudio("", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                Ordenacao.ordenarLista(lista, "descricao");
                setListaAmbiente(lista);
            }
        } catch (Exception e) {
            setApresentarRichModalErro(Boolean.TRUE);
            setMensagemDetalhada("", e.getMessage());
        }
    }

    /*
     * Ações lista Tipo Horario
     */
    /**
     * Método que busca lista dos tipos de horários cadastrados
     *
     * @return Lista de SelectItem para Tipo horário
     * @throws Exception
     */
    public void acaoBuscarTipoHorario() throws Exception {
        try {
            if (getListaTipoHorario() == null || getListaTipoHorario().isEmpty()) {
                setListaTipoHorario(getFacade().getTipoHorario().procurarTodosTiposHorario(getEmpresaLogado().getCodigo(), false, false));
            }
        } catch (NoSuchElementException e) {
            setApresentarRichModalErro(Boolean.TRUE);
            setMensagemDetalhada("", e.getMessage());
        }
    }

    public String acaoFecharTipoHorario() {
        setListaNomeTipoHorario(new ArrayList<String>());
        if (!getListaTipoHorarioSelecionado().isEmpty()) {
            for (TipoHorarioVO tph : getListaTipoHorario()) {
                for (int i = 0; i < getListaTipoHorarioSelecionado().size(); i++) {
                    if (String.valueOf(tph.getCodigo()).equals(String.valueOf(getListaTipoHorarioSelecionado().get(i)))) {
                        getListaNomeTipoHorario().add(tph.getDescricao().trim().toUpperCase() + " (" + tph.getSigla().trim().toUpperCase() + ")");
                    }
                }
            }
        }
        return "";
    }

    /**
     * Método que busca lista dos tipos de horários cadastrados
     *
     * @return Lista de SelectItem para Tipo horário
     * @throws Exception
     */
    public SelectItem[] getListaTipoHorarioSelect() throws Exception {
        SelectItem[] tipo = new SelectItem[getListaTipoHorario().size()];
        int i = -1;
        for (TipoHorarioVO tp : getListaTipoHorario()) {
            tipo[++i] = new SelectItem(tp.getCodigo(), tp.getDescricao().trim().toUpperCase() + " (" + tp.getSigla().trim().toUpperCase() + ")");
        }
        return tipo;

    }

    /*
     * Ações lista Status
     */
    public String acaoFecharStatus() {
        setListaNomeStatus(new ArrayList<String>());
        if (!getListaStatusSelecionado().isEmpty()) {
            for (StatusEnum st : StatusEnum.values()) {
                for (int i = 0; i < getListaStatusSelecionado().size(); i++) {
                    if (String.valueOf(st.getId()).equals(String.valueOf(getListaStatusSelecionado().get(i)))) {
                        getListaNomeStatus().add((st.getDescricao().isEmpty() ? "SEM STATUS" : st.getDescricao()));
                    }
                }
            }
        }
        return "";
    }

    /**
     * Método que busca lista dos Status
     *
     * @return Vetor de Status
     * @throws Exception
     */
    public SelectItem[] getListaStatusSelect() {
        SelectItem[] status = new SelectItem[StatusEnum.values().length];
        int i = -1;
        for (StatusEnum mp : StatusEnum.values()) {
            status[++i] = new SelectItem(mp.getId(), (mp.getDescricao().isEmpty() ? "SEM STATUS" : mp.getDescricao()));
        }
        return status;
    }

    /*
     *
     */
    public SelectItem[] getPeriodoSelect() {
        SelectItem[] status = new SelectItem[PeriodoEnum.values().length];
        int i = -1;
        for (PeriodoEnum mp : PeriodoEnum.values()) {
            status[++i] = new SelectItem(mp, mp.getDescricao());
        }
        return status;
    }

    public List<SelectItem> getSituacaoSelect() {
        List<SelectItem> status = new ArrayList<SelectItem>();
        status.add(new SelectItem("", ""));
        for (SituacaoParcelaEnum mp : SituacaoParcelaEnum.values()) {
            status.add(new SelectItem(mp, mp.getDescricao()));
        }
        return status;
    }

    public void editarAgendamento() {
        try {
            AgendaAmbienteColaboradorControle agendaControle = (AgendaAmbienteColaboradorControle) getControlador(AgendaAmbienteColaboradorControle.class.getSimpleName());
            if (agendaControle.getConfiguracaoEstudioVO() == null) {
                ConfiguracaoEstudioVO obj = new ConfiguracaoEstudioVO();
                obj.setEmpresaVO(getEmpresaLogado());
                agendaControle.setConfiguracaoEstudioVO(getFacade().getConfiguracaoEstudio().consultarPorCodigo(configuracaoEstudioVO, false));
            }
            RelatorioComissaoVO relatorioComissao = (RelatorioComissaoVO) context().getExternalContext().getRequestMap().get("item");
            agendaControle.setItemAgenda(getFacade().getAgendaEstudio().buscarAgendaPorCodigo(relatorioComissao.getCodigoAgenda()));
            agendaControle.acaoAbrirToolTip();
            agendaControle.setApresentarTodosCampos(true);
            agendaControle.setOrigem(OrigemOperacaoEnum.COMISSAO);
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
        }
    }

    private void preencherListaColaboradorSelecionado() {
        listaColaboradorSelecionado = new ArrayList<Integer>();
        for (int i = 0; i < listaUmColaboradorSelecionado.size(); i++) {
            Object col = listaUmColaboradorSelecionado.get(i);
            Integer colaborador = Integer.parseInt(col.toString());
            listaColaboradorSelecionado.add(colaborador);
        }
        for (int i = 0; i < listaDoisColaboradorSelecionado.size(); i++) {
            Object col = listaDoisColaboradorSelecionado.get(i);
            Integer colaborador = Integer.parseInt(col.toString());
            listaColaboradorSelecionado.add(colaborador);
        }
    }

    public List<SelectItem> getListaUmPessoaSelect() {
        int posicaoMeio = calcularPosicaoDoMeioDaLista(getListaColaboradorVO());
        return montarListaSelectItem(getListaColaboradorVO().subList(0, posicaoMeio));
    }

    public List<SelectItem> getListaDoisPessoaSelect() {
        int posicaoMeio = calcularPosicaoDoMeioDaLista(getListaColaboradorVO());
        return montarListaSelectItem(getListaColaboradorVO().subList(posicaoMeio, getListaColaboradorVO().size()));
    }

    private int calcularPosicaoDoMeioDaLista(List lista) {
        if (lista.size() % 2 == 0) {
            return lista.size() / 2;
        } else {
            return (lista.size() + 1) / 2;
        }
    }

    private List<SelectItem> montarListaSelectItem(List<ColaboradorVO> lista) {
        List<SelectItem> items = new ArrayList<SelectItem>();
        for (ColaboradorVO col : lista) {
            SelectItem item = new SelectItem(col.getCodigo(), col.getPessoa().getNome().trim());
            items.add(item);
        }
        return items;
    }


    public AmbienteVO getAmbienteVO() {
        return ambienteVO;
    }

    public void setAmbienteVO(AmbienteVO ambienteVO) {
        this.ambienteVO = ambienteVO;
    }

    public Date getPeriodoFinal() {
        return periodoFinal;
    }

    public void setPeriodoFinal(Date periodoFinal) {
        this.periodoFinal = periodoFinal;
    }

    public Date getPeriodoInicial() {
        return periodoInicial;
    }

    public void setPeriodoInicial(Date periodoInicial) {
        this.periodoInicial = periodoInicial;
    }

    public List<AmbienteVO> getListaAmbiente() {
        if (listaAmbiente == null) {
            listaAmbiente = new ArrayList<AmbienteVO>();
        }
        return listaAmbiente;
    }

    public void setListaAmbiente(List<AmbienteVO> listaAmbiente) {
        this.listaAmbiente = listaAmbiente;
    }

    public List<ProdutoVO> getListaServico() {
        if (listaServico == null) {
            listaServico = new ArrayList<ProdutoVO>();
        }
        return listaServico;
    }

    public void setListaServico(List<ProdutoVO> listaServico) {
        this.listaServico = listaServico;
    }

    public ProdutoVO getProdutoVO() {
        return produtoVO;
    }

    public void setProdutoVO(ProdutoVO produtoVO) {
        this.produtoVO = produtoVO;
    }

    public Boolean getSelecionarTodosItemServico() {
        return selecionarTodosItemServico;
    }

    public void setSelecionarTodosItemServico(Boolean selecionarTodosItemServico) {
        this.selecionarTodosItemServico = selecionarTodosItemServico;
    }

    public Boolean getSelecionarTodosItemAmbiente() {
        return selecionarTodosItemAmbiente;
    }

    public void setSelecionarTodosItemAmbiente(Boolean selecionarTodosItemAmbiente) {
        this.selecionarTodosItemAmbiente = selecionarTodosItemAmbiente;
    }

    public Boolean getSelecionarTodosItemPessoa() {
        return selecionarTodosItemPessoa;
    }

    public void setSelecionarTodosItemPessoa(Boolean selecionarTodosItemPessoa) {
        this.selecionarTodosItemPessoa = selecionarTodosItemPessoa;
    }

    public List<Integer> getListaAmbienteSelecionado() {
        if (listaAmbienteSelecionado == null) {
            listaAmbienteSelecionado = new ArrayList<Integer>();
        }
        return listaAmbienteSelecionado;
    }

    public void setListaAmbienteSelecionado(List<Integer> listaAmbienteSelecionado) {
        this.listaAmbienteSelecionado = listaAmbienteSelecionado;
    }

    public List<Integer> getListaColaboradorSelecionado() {
        if (listaColaboradorSelecionado == null) {
            listaColaboradorSelecionado = new ArrayList<Integer>();
        }
        return listaColaboradorSelecionado;
    }

    public void setListaColaboradorSelecionado(List<Integer> listaColaboradorSelecionado) {
        this.listaColaboradorSelecionado = listaColaboradorSelecionado;
    }

    public ColaboradorVO getColaboradorVO() {
        return colaboradorVO;
    }

    public void setColaboradorVO(ColaboradorVO colaboradorVO) {
        this.colaboradorVO = colaboradorVO;
    }

    public List<ColaboradorVO> getListaColaboradorVO() {
        if (listaColaboradorVO == null) {
            listaColaboradorVO = new ArrayList<ColaboradorVO>();
        }
        return listaColaboradorVO;
    }

    public void setListaColaboradorVO(List<ColaboradorVO> listaColaboradorVO) {
        this.listaColaboradorVO = listaColaboradorVO;
    }

    public Boolean getApresentarRichModalErro() {
        return apresentarRichModalErro;
    }

    public void setApresentarRichModalErro(Boolean apresentarRichModalErro) {
        this.apresentarRichModalErro = apresentarRichModalErro;
    }

    public List<String> getListaNomeColaborador() {
        if (listaNomeColaborador == null) {
            listaNomeColaborador = new ArrayList<String>();
        }
        return listaNomeColaborador;
    }

    public void setListaNomeColaborador(List<String> listaNomeColaborador) {
        this.listaNomeColaborador = listaNomeColaborador;
    }

    public List<String> getListaNomeAmbiente() {
        if (listaNomeAmbiente == null) {
            listaNomeAmbiente = new ArrayList<String>();
        }
        return listaNomeAmbiente;
    }

    public void setListaNomeAmbiente(List<String> listaNomeAmbiente) {
        this.listaNomeAmbiente = listaNomeAmbiente;
    }

    public List<String> getListaNomeServico() {
        if (listaNomeServico == null) {
            listaNomeServico = new ArrayList<String>();
        }
        return listaNomeServico;
    }

    public void setListaNomeServico(List<String> listaNomeServico) {
        this.listaNomeServico = listaNomeServico;
    }

    public List<ProdutoVO> getListaServicoTabela() {
        if (listaServicoTabela == null) {
            listaServicoTabela = new ArrayList<ProdutoVO>();
        }
        return listaServicoTabela;
    }

    public void setListaServicoTabela(List<ProdutoVO> listaServicoTabela) {
        this.listaServicoTabela = listaServicoTabela;
    }

    public ConfiguracaoEstudioVO getConfiguracaoEstudioVO() {
        return configuracaoEstudioVO;
    }

    public void setConfiguracaoEstudioVO(ConfiguracaoEstudioVO configuracaoEstudioVO) {
        this.configuracaoEstudioVO = configuracaoEstudioVO;
    }

    public Time getHoraFinal() {
        return horaFinal;
    }

    public void setHoraFinal(Time horaFinal) {
        this.horaFinal = horaFinal;
    }

    public Time getHoraInicial() {
        return horaInicial;
    }

    public void setHoraInicial(Time horaInicial) {
        this.horaInicial = horaInicial;
    }

    public PeriodoEnum getPeriodoEnum() {
        return periodoEnum;
    }

    public void setPeriodoEnum(PeriodoEnum periodoEnum) {
        this.periodoEnum = periodoEnum;
    }

    public void setSelectHorario(List<SelectItem> selectHorario) {
        this.selectHorario = selectHorario;
    }

    public List<TipoHorarioVO> getListaTipoHorario() {

        if (listaTipoHorario == null) {
            listaTipoHorario = new ArrayList<TipoHorarioVO>();
        }
        return listaTipoHorario;
    }

    public void setListaTipoHorario(List<TipoHorarioVO> listaTipoHorario) {
        this.listaTipoHorario = listaTipoHorario;
    }

    public List<Integer> getListaTipoHorarioSelecionado() {
        return listaTipoHorarioSelecionado;
    }

    public void setListaTipoHorarioSelecionado(List<Integer> listaTipoHorarioSelecionado) {
        this.listaTipoHorarioSelecionado = listaTipoHorarioSelecionado;
    }

    public List<String> getListaNomeTipoHorario() {
        if (listaNomeTipoHorario == null) {
            listaNomeTipoHorario = new ArrayList<String>();
        }
        return listaNomeTipoHorario;
    }

    public void setListaNomeTipoHorario(List<String> listaNomeTipoHorario) {
        this.listaNomeTipoHorario = listaNomeTipoHorario;
    }

    public List<String> getListaNomeStatus() {
        if (listaNomeStatus == null) {
            listaNomeStatus = new ArrayList<String>();
        }
        return listaNomeStatus;
    }

    public void setListaNomeStatus(List<String> listaNomeStatus) {
        this.listaNomeStatus = listaNomeStatus;
    }

    public List<String> getListaStatusSelecionado() {
        if (listaStatusSelecionado == null) {
            listaStatusSelecionado = new ArrayList<String>();
        }
        return listaStatusSelecionado;
    }

    public void setListaStatusSelecionado(List<String> listaStatusSelecionado) {
        this.listaStatusSelecionado = listaStatusSelecionado;
    }

    public List<RelatorioComissaoVO> getListaRelatorioComissao() {
        if (listaRelatorioComissao == null) {
            listaRelatorioComissao = new ArrayList<RelatorioComissaoVO>();
        }
        return listaRelatorioComissao;
    }

    public void setListaRelatorioComissao(List<RelatorioComissaoVO> listaRelatorioComissao) {
        this.listaRelatorioComissao = listaRelatorioComissao;
    }

    public BigDecimal getSomaComissao() {
        return somaComissao;
    }

    public void setSomaComissao(BigDecimal somaComissao) {
        this.somaComissao = somaComissao;
    }

    public BigDecimal getSomaUnitario() {
        return somaUnitario;
    }

    public void setSomaUnitario(BigDecimal somaUnitario) {
        this.somaUnitario = somaUnitario;
    }

    public String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "estudio" + File.separator + "comissaoEstudioPacto.jrxml");
    }

    @Override
    public String getNomeArquivoRelatorioGeradoAgora() {
        if (request().getAttribute("nomeArquivoRelatorioGeradoAgora") != null) {
            return request().getAttribute("nomeArquivoRelatorioGeradoAgora").toString();
        } else {
            return "";
        }
    }

    /**
     * Método responsavel por adquirir o caminho onde se encontra o relatorio
     * jasper
     *
     * @param nomeRel
     * @return
     */
    public static String getIsDesignIReportRelatorioExcel(String nomeRel) {
        return ("designRelatorio" + File.separator + "estudio" + File.separator + nomeRel);
    }

    public Integer getNumeroClientes() {
        return numeroClientes;
    }

    public void setNumeroClientes(Integer numeroClientes) {
        this.numeroClientes = numeroClientes;
    }

    public Boolean getAbrirPopupDownload() {
        if (abrirPopupDownload == null) {
            abrirPopupDownload = false;
        }
        return abrirPopupDownload;
    }

    public void setAbrirPopupDownload(Boolean abrirPopupDownload) {
        this.abrirPopupDownload = abrirPopupDownload;
    }

    /**
     * @return the situacaoEnum
     */
    public SituacaoParcelaEnum getSituacaoEnum() {
        return situacaoEnum;
    }

    /**
     * @param situacaoEnum the situacaoEnum to set
     */
    public void setSituacaoEnum(SituacaoParcelaEnum situacaoEnum) {
        this.situacaoEnum = situacaoEnum;
    }

    public List<Integer> getListaUmColaboradorSelecionado() {
        return listaUmColaboradorSelecionado;
    }

    public void setListaUmColaboradorSelecionado(List<Integer> listaUmColaboradorSelecionado) {
        this.listaUmColaboradorSelecionado = listaUmColaboradorSelecionado;
    }

    public List<Integer> getListaDoisColaboradorSelecionado() {
        return listaDoisColaboradorSelecionado;
    }

    public void setListaDoisColaboradorSelecionado(List<Integer> listaDoisColaboradorSelecionado) {
        this.listaDoisColaboradorSelecionado = listaDoisColaboradorSelecionado;
    }
}
