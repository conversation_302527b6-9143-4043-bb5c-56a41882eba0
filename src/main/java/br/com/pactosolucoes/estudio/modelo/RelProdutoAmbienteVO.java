package br.com.pactosolucoes.estudio.modelo;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.AmbienteVO;
import negocio.comuns.plano.ProdutoVO;

/**
 *
 * <AUTHOR> - GeoInova Soluções
 */
public class RelProdutoAmbienteVO extends SuperVO {

    private ProdutoVO produtoVO;
    private AmbienteVO ambienteVO;
    private EmpresaVO empresaVO;

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public AmbienteVO getAmbienteVO() {
        if (ambienteVO == null) {
            ambienteVO = new AmbienteVO();
        }
        return ambienteVO;
    }

    public void setAmbienteVO(AmbienteVO ambienteVO) {
        this.ambienteVO = ambienteVO;
    }

    public ProdutoVO getProdutoVO() {
        if (produtoVO == null) {
            produtoVO = new ProdutoVO();
        }
        return produtoVO;
    }

    public void setProdutoVO(ProdutoVO produtoVO) {
        this.produtoVO = produtoVO;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final RelProdutoAmbienteVO other = (RelProdutoAmbienteVO) obj;
        if (this.produtoVO.getCodigo().intValue() != other.produtoVO.getCodigo().intValue()) {
            return false;
        }
        if (this.ambienteVO.getCodigo().intValue() != other.ambienteVO.getCodigo().intValue()) {
            return false;
        }
        return true;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        return hash;
    }
}
