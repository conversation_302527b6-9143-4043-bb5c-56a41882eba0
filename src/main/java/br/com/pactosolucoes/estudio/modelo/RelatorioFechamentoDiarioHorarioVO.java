package br.com.pactosolucoes.estudio.modelo;

import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR> - GeoInova Soluções
 */
public class RelatorioFechamentoDiarioHorarioVO extends SuperVO {

    private String descAmbiente;
    private Integer idAmbiente;
    private Integer horarios;
    private Integer agendado;
    private Integer confirmado;
    private Integer realizado;
    private Integer pendente;
    private Integer falta;
    private Integer justificada;
    private Integer totalHorarios;
    private Integer totalAgendado;
    private Integer totalConfirmado;
    private Integer totalRealizado;
    private Integer totalPendente;
    private Integer totalFalta;
    private Integer totalJustificada;

    public String getDescAmbiente() {
        return descAmbiente;
    }

    public void setDescAmbiente(String descAmbiente) {
        this.descAmbiente = descAmbiente;
    }

    public Integer getHorarios() {
        return horarios;
    }

    public void setHorarios(Integer horarios) {
        this.horarios = horarios;
    }

    public Integer getAgendado() {
        return agendado;
    }

    public void setAgendado(Integer agendado) {
        this.agendado = agendado;
    }

    public Integer getConfirmado() {
        return confirmado;
    }

    public void setConfirmado(Integer confirmado) {
        this.confirmado = confirmado;
    }

    public Integer getPendente() {
        return pendente;
    }

    public void setPendente(Integer pendente) {
        this.pendente = pendente;
    }

    public Integer getRealizado() {
        return realizado;
    }

    public void setRealizado(Integer realizado) {
        this.realizado = realizado;
    }

    public Integer getIdAmbiente() {
        return idAmbiente;
    }

    public void setIdAmbiente(Integer idAmbiente) {
        this.idAmbiente = idAmbiente;
    }

    public Integer getTotalAgendado() {
        if (totalAgendado == null) {
            totalAgendado = 0;
        }
        return totalAgendado;
    }

    public void setTotalAgendado(Integer totalAgendado) {
        this.totalAgendado = totalAgendado;
    }

    public Integer getTotalConfirmado() {
        if (totalConfirmado == null) {
            totalConfirmado = 0;
        }
        return totalConfirmado;
    }

    public void setTotalConfirmado(Integer totalConfirmado) {
        this.totalConfirmado = totalConfirmado;
    }

    public Integer getTotalHorarios() {
        if (totalHorarios == null) {
            totalHorarios = 0;
        }
        return totalHorarios;
    }

    public void setTotalHorarios(Integer totalHorarios) {
        this.totalHorarios = totalHorarios;
    }

    public Integer getTotalPendente() {
        if (totalPendente == null) {
            totalPendente = 0;
        }
        return totalPendente;
    }

    public void setTotalPendente(Integer totalPendente) {
        this.totalPendente = totalPendente;
    }

    public Integer getTotalRealizado() {
        if (totalRealizado == null) {
            totalRealizado = 0;
        }
        return totalRealizado;
    }

    public void setTotalRealizado(Integer totalRealizado) {
        this.totalRealizado = totalRealizado;
    }

    public Integer getFalta() {
        return falta;
    }

    public void setFalta(Integer falta) {
        this.falta = falta;
    }

    public Integer getJustificada() {
        return justificada;
    }

    public void setJustificada(Integer justificada) {
        this.justificada = justificada;
    }

    public Integer getTotalFalta() {
        return totalFalta;
    }

    public void setTotalFalta(Integer totalFalta) {
        this.totalFalta = totalFalta;
    }

    public Integer getTotalJustificada() {
        return totalJustificada;
    }

    public void setTotalJustificada(Integer totalJustificada) {
        this.totalJustificada = totalJustificada;
    }
}
