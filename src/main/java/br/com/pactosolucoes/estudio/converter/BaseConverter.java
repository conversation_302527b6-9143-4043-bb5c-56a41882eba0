package br.com.pactosolucoes.estudio.converter;

import javax.faces.application.FacesMessage;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import negocio.facade.jdbc.arquitetura.FacadeFactory;

public abstract class BaseConverter implements Converter {

    private static FacadeFactory facadeFactory;

    protected void showMessage(String msg, FacesContext context) {
        FacesMessage facesMessage = new FacesMessage();
        facesMessage.setSummary(msg);
        facesMessage.setDetail(msg);
        facesMessage.setSeverity(FacesMessage.SEVERITY_ERROR);
        context.addMessage("msgMessages", facesMessage);
    }

    protected void showMessage(Exception e, FacesContext context) {
        showMessage(e.getMessage(), context);
    }

    public static FacadeFactory getFacade() {
        FacesContext fc = FacesContext.getCurrentInstance();
        if (fc != null) {
            FacadeFactory facade = (FacadeFactory) fc.getExternalContext().getSessionMap().get(FacadeFactory.class.getSimpleName());
            if (facade == null) {
                facade = new FacadeFactory();
                FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put(FacadeFactory.class.getSimpleName(), facade);
            }
            return facade;
        } else {
            if (facadeFactory == null) {
                facadeFactory = new FacadeFactory();
            }

            return facadeFactory;
        }
    }
}