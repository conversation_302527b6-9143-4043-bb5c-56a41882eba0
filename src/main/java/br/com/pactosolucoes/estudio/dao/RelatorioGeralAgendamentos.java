package br.com.pactosolucoes.estudio.dao;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.SiglaTipoHorarioEnum;
import br.com.pactosolucoes.enumeradores.SituacaoParcelaEnum;
import br.com.pactosolucoes.estudio.enumeradores.StatusEnum;
import br.com.pactosolucoes.estudio.interfaces.RelatorioGeralAgendamentosInterfaceFacade;
import br.com.pactosolucoes.estudio.modelo.RelatorioGeralAgendamentosVO;
import br.com.pactosolucoes.estudio.util.Validador;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

/**
 *
 * <AUTHOR>
 */
public class RelatorioGeralAgendamentos extends SuperEntidade implements RelatorioGeralAgendamentosInterfaceFacade {

	private String filtros(Date dataInicial, Date dataFinal, Time horaInicial, Time horaFinal, List<Integer> listaColaborador,
			List<Integer> listaAmbiente, List<Integer> listaProduto, List<String> listaStatus, List<Integer> listaTipoHorario,
			SituacaoParcelaEnum situacaoParcela, String nomeCliente) {
		String whereData = "";
        if (Validador.isValidaObject(dataInicial) && Validador.isValidaObject(dataFinal)) {
            whereData = " AND (agenda.data_aula BETWEEN '" + Formatador.formatarData(dataInicial, "yyyy-MM-dd") + "' AND '" + Formatador.formatarData(dataFinal, "yyyy-MM-dd") + "')";
        }

        SimpleDateFormat format = new SimpleDateFormat("HH:mm");
        String whereHora = "";
        if (Validador.isValidaObject(horaInicial) && Validador.isValidaObject(horaFinal)) {
            whereHora = " AND (agenda.hora_inicio BETWEEN '" + format.format(horaInicial) + "' AND '" + format.format(horaFinal) + "')";
        }

        String whereColab = "";
        if (Validador.isValidaList(listaColaborador)) {
            whereColab = " AND (colab.codigo IN " + listaColaborador.toString().replace("[", "(").replace("]", ")") + ")";
        }

        String whereAmb = "";
        if (Validador.isValidaList(listaAmbiente)) {
            whereAmb = " AND (amb.codigo IN " + listaAmbiente.toString().replace("[", "(").replace("]", ")") + ")";
        }

        String whereProd = "";
        if (Validador.isValidaList(listaProduto)) {
            whereProd = " AND (prod.codigo IN " + listaProduto.toString().replace("[", "(").replace("]", ")") + ")";
        }

        String whereStatus = "";
        if (Validador.isValidaList(listaStatus)) {
            whereStatus = " AND (agenda.status IN " + listaStatus.toString().replace("[", "('").replace("]", "')").replace(",", "','").replace(" ", "") + ")";
        }

        String whereTipo = "";
        if (Validador.isValidaList(listaTipoHorario)) {
            whereTipo = " AND (agenda.id_tipo_horario IN " + listaTipoHorario.toString().replace("[", "(").replace("]", ")") + ")";
        }
        
        String whereSituacao = "";
        if(situacaoParcela!=null){
            if(situacaoParcela.getCodigo().equals("PG")){
                whereSituacao = " and movp.situacao = 'PG' ";
            }else if(situacaoParcela.getCodigo().equals("NPF")){
                whereSituacao = " and  af.id_agenda_faturar IS not NULL ";
            }else if(situacaoParcela.getCodigo().equals("NPR")){
                whereSituacao = " and movp.situacao = 'EA' ";
            }else if(situacaoParcela.getCodigo().equals("NP")){
                whereSituacao = " and (movp.situacao = 'EA' or af.id_agenda_faturar IS not NULL) ";
            }else if(situacaoParcela.getCodigo().equals("SF")){
                whereSituacao = " and agenda.status = 'S' ";
            }
        }

        String whereNomeCliente = "";
        if (Validador.isValidaString(nomeCliente)) {
            whereNomeCliente = " AND pessoaCli.nome ilike '%" + nomeCliente + "%'";
        }
        
        return whereData + whereHora + whereStatus + whereProd + whereAmb + whereColab + whereTipo + whereSituacao + whereNomeCliente;
	}
	
	
	/**
     * Operação para totalizar a quantidade de sessões ministradas, agendadas, em remarcadas, etc.
     *
     * @param idEmpresa
     * @param dataInicial
     * @param dataFinal
     * @param horaInicial
     * @param horaFinal
     * @param listaColaborador
     * @param listaAmbiente
     * @param listaProduto
     * @param listaTipoHorario
     *
     * @return List<RelatorioGeralAgendamentosVO> listaTotalizadorStatus
     * @throws Exception
     */
    @Override
    public List<RelatorioGeralAgendamentosVO> totalizarStatusAgendamentos(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial,
    		Time horaFinal, List<Integer> listaColaborador, List<Integer> listaAmbiente, List<Integer> listaProduto, List<String> listaStatus,
            List<Integer> listaTipoHorario,  SituacaoParcelaEnum situacaoParcela, String nomeCliente) throws Exception {
    	
    	List<RelatorioGeralAgendamentosVO> listaTotalizadorStatus = new ArrayList<RelatorioGeralAgendamentosVO>();

        String sql = " SELECT COUNT(agenda.status) AS totalizador, agenda.status AS status"

                   + " FROM sch_estudio.agenda agenda "

                   + " INNER JOIN sch_estudio.tipo_horario th ON th.id_tipo_horario = agenda.id_tipo_horario "
                   + " INNER JOIN public.ambiente amb ON amb.codigo = agenda.id_ambiente "
                   + " INNER JOIN public.produto prod ON prod.codigo = agenda.id_produto AND prod.desativado = FALSE AND prod.tipoproduto = 'SS' "
                   + " INNER JOIN public.colaborador colab ON colab.codigo = agenda.id_colaborador AND colab.situacao = 'AT' "
                   + " LEFT JOIN public.tipocolaborador tipoColab ON tipoColab.colaborador = colab.codigo AND tipoColab.descricao = 'ES' "
                   + " LEFT JOIN sch_estudio.produto_colaborador relprod ON relprod.id_produto = agenda.id_produto AND relprod.id_colaborador = agenda.id_colaborador "
                   + " INNER JOIN public.cliente cli ON cli.codigo = agenda.id_cliente "
                   + " INNER JOIN sch_estudio.agenda_venda venda ON venda.id_agenda = agenda.id_agenda "
                   + " INNER JOIN public.itemvendaavulsa item ON item.vendaavulsa = venda.id_vendaavulsa AND item.produto = agenda.id_produto "
                   + " INNER JOIN public.pessoa pessoaColab ON pessoaColab.codigo = colab.pessoa "
                   + " INNER JOIN public.pessoa pessoaCli ON pessoaCli.codigo = cli.pessoa "
                   + " LEFT JOIN movparcela movp ON movp.vendaavulsa = venda.id_vendaavulsa "
                   + " LEFT JOIN sch_estudio.agenda_faturar af ON af.id_agenda = agenda.id_agenda "

            	   + " WHERE agenda.id_empresa = ? "
            	   + filtros(dataInicial, dataFinal, horaInicial, horaFinal, listaColaborador, listaAmbiente, listaProduto, listaStatus, listaTipoHorario, situacaoParcela, nomeCliente)
            	   + " GROUP BY agenda.status ";

        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ResultSet resultDados = ps.executeQuery();
        while (resultDados.next()) {
            RelatorioGeralAgendamentosVO obj = new RelatorioGeralAgendamentosVO();
            obj = montarDados(obj, resultDados, Uteis.NIVELMONTARDADOS_MINIMOS);
            listaTotalizadorStatus.add(obj);
        }
    	
    	return listaTotalizadorStatus;
    }
    
    /**
     * Operação para totalizar a quantidade de sessões pagas, a receber, sem faturamento, etc.
     *
     * @param idEmpresa
     * @param dataInicial
     * @param dataFinal
     * @param horaInicial
     * @param horaFinal
     * @param listaColaborador
     * @param listaAmbiente
     * @param listaProduto
     * @param listaStatus
     * @param listaTipoHorario
     *
     * @return List<RelatorioGeralAgendamentosVO> listaTotalizadorSituacao
     * @throws Exception
     */
    public List<RelatorioGeralAgendamentosVO> totalizarSituacaoAgendamentos(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial,
    		Time horaFinal, List<Integer> listaColaborador, List<Integer> listaAmbiente, List<Integer> listaProduto, List<String> listaStatus,
            List<Integer> listaTipoHorario,  SituacaoParcelaEnum situacaoParcela, String nomeCliente) throws Exception {
    	List<RelatorioGeralAgendamentosVO> listaTotalizadorSituacao = new ArrayList<RelatorioGeralAgendamentosVO>();
    	
    	//asdf
    	String sql = " SELECT (CASE WHEN movp.situacao = 'PG' THEN 'PG' WHEN af.id_agenda_faturar IS NOT NULL THEN 'NPF' "
                   + " WHEN movp.situacao = 'EA' THEN 'NPR' WHEN agenda.status = 'S' THEN 'SF' END) AS situacaoPagamento, COUNT(agenda.status = 'S') AS totalizador "

		    	   + " FROM sch_estudio.agenda agenda "
		
		           + " INNER JOIN sch_estudio.tipo_horario th ON th.id_tipo_horario = agenda.id_tipo_horario "
		           + " INNER JOIN public.ambiente amb ON amb.codigo = agenda.id_ambiente "
		           + " INNER JOIN public.produto prod ON prod.codigo = agenda.id_produto AND prod.desativado = FALSE AND prod.tipoproduto = 'SS' "
		           + " INNER JOIN public.colaborador colab ON colab.codigo = agenda.id_colaborador AND colab.situacao = 'AT' "
		           + " LEFT JOIN public.tipocolaborador tipoColab ON tipoColab.colaborador = colab.codigo AND tipoColab.descricao = 'ES' "
		           + " LEFT JOIN sch_estudio.produto_colaborador relprod ON relprod.id_produto = agenda.id_produto AND relprod.id_colaborador = agenda.id_colaborador "
		           + " INNER JOIN public.cliente cli ON cli.codigo = agenda.id_cliente "
		           + " INNER JOIN sch_estudio.agenda_venda venda ON venda.id_agenda = agenda.id_agenda "
		           + " INNER JOIN public.itemvendaavulsa item ON item.vendaavulsa = venda.id_vendaavulsa AND item.produto = agenda.id_produto "
		           + " INNER JOIN public.pessoa pessoaColab ON pessoaColab.codigo = colab.pessoa "
		           + " INNER JOIN public.pessoa pessoaCli ON pessoaCli.codigo = cli.pessoa "
		           + " LEFT JOIN movparcela movp ON movp.vendaavulsa = venda.id_vendaavulsa "
		           + " LEFT JOIN sch_estudio.agenda_faturar af ON af.id_agenda = agenda.id_agenda "
		
		           + " WHERE agenda.id_empresa = ? "
		           + filtros(dataInicial, dataFinal, horaInicial, horaFinal, listaColaborador, listaAmbiente, listaProduto, listaStatus, listaTipoHorario, situacaoParcela, nomeCliente)
		           + " GROUP BY situacaoPagamento ";

		PreparedStatement ps = con.prepareStatement(sql);
		ps.setInt(1, idEmpresa);
		ResultSet resultDados = ps.executeQuery();
		while (resultDados.next()) {
		    RelatorioGeralAgendamentosVO obj = new RelatorioGeralAgendamentosVO();
		    obj = montarDados(obj, resultDados, Uteis.NIVELMONTARDADOS_PARCELA);
		    listaTotalizadorSituacao.add(obj);
		}
    	
		return listaTotalizadorSituacao;
    }
    
	/**
     * Operação para totalizar o tipo de horário.
     *
     * @param idEmpresa
     * @param dataInicial
     * @param dataFinal
     * @param horaInicial
     * @param horaFinal
     * @param listaColaborador
     * @param listaAmbiente
     * @param listaProduto
     * @param listaStatus
     * @param listaTipoHorario
     *
     * @return List<RelatorioGeralAgendamentosVO> listaTotalizadorTipoHorario
     * @throws Exception
     */
    @Override
    public List<RelatorioGeralAgendamentosVO> totalizarTipoHorarioAgendamentos(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial,
    		Time horaFinal, List<Integer> listaColaborador, List<Integer> listaAmbiente, List<Integer> listaProduto, List<String> listaStatus,
            List<Integer> listaTipoHorario,  SituacaoParcelaEnum situacaoParcela, String nomeCliente) throws Exception {
    	
    	List<RelatorioGeralAgendamentosVO> listaTotalizadorTipoHorario = new ArrayList<RelatorioGeralAgendamentosVO>();
    	
        String sql = " SELECT COUNT(th.sigla) AS totalizador, th.sigla "

                   + " FROM sch_estudio.agenda agenda "

                   + " INNER JOIN sch_estudio.tipo_horario th ON th.id_tipo_horario = agenda.id_tipo_horario "
                   + " INNER JOIN public.ambiente amb ON amb.codigo = agenda.id_ambiente "
                   + " INNER JOIN public.produto prod ON prod.codigo = agenda.id_produto AND prod.desativado = FALSE AND prod.tipoproduto = 'SS' "
                   + " INNER JOIN public.colaborador colab ON colab.codigo = agenda.id_colaborador AND colab.situacao = 'AT' "
                   + " LEFT JOIN public.tipocolaborador tipoColab ON tipoColab.colaborador = colab.codigo AND tipoColab.descricao = 'ES' "
                   + " LEFT JOIN sch_estudio.produto_colaborador relprod ON relprod.id_produto = agenda.id_produto AND relprod.id_colaborador = agenda.id_colaborador "
                   + " INNER JOIN public.cliente cli ON cli.codigo = agenda.id_cliente "
                   + " INNER JOIN sch_estudio.agenda_venda venda ON venda.id_agenda = agenda.id_agenda "
                   + " INNER JOIN public.itemvendaavulsa item ON item.vendaavulsa = venda.id_vendaavulsa AND item.produto = agenda.id_produto "
                   + " INNER JOIN public.pessoa pessoaColab ON pessoaColab.codigo = colab.pessoa "
                   + " INNER JOIN public.pessoa pessoaCli ON pessoaCli.codigo = cli.pessoa "
                   + " LEFT JOIN movparcela movp ON movp.vendaavulsa = venda.id_vendaavulsa "
                   + " LEFT JOIN sch_estudio.agenda_faturar af ON af.id_agenda = agenda.id_agenda "

            	   + " WHERE agenda.id_empresa = ? "
            	   + filtros(dataInicial, dataFinal, horaInicial, horaFinal, listaColaborador, listaAmbiente, listaProduto, listaStatus, listaTipoHorario, situacaoParcela, nomeCliente)
            	   + " GROUP BY th.sigla ";
        
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ResultSet resultDados = ps.executeQuery();
        while (resultDados.next()) {
            RelatorioGeralAgendamentosVO obj = new RelatorioGeralAgendamentosVO();
            obj = montarDados(obj, resultDados, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            listaTotalizadorTipoHorario.add(obj);
        }
        
    	return listaTotalizadorTipoHorario;
    }
	
    /**
     * Operação para buscar a lista de agendamentos
     *
     * @param idEmpresa
     * @param dataInicial
     * @param dataFinal
     * @param horaInicial
     * @param horaFinal
     * @param listaColaborador
     * @param listaAmbiente
     * @param listaProduto
     * @param listaStatus
     * @param listaTipoHorario
     *
     * @return List<RelatorioGeralAgendamentosVO> listarGeralAgendamentos
     * @throws Exception
     */
    @Override
    public List<RelatorioGeralAgendamentosVO> listarGeralAgendamentos(Integer idEmpresa, Date dataInicial, Date dataFinal, Time horaInicial,
    		Time horaFinal, List<Integer> listaColaborador, List<Integer> listaAmbiente, List<Integer> listaProduto, List<String> listaStatus,
            List<Integer> listaTipoHorario,  SituacaoParcelaEnum situacaoParcela, String nomeCliente) throws Exception {

        List<RelatorioGeralAgendamentosVO> listaGeralAgendamentos = new ArrayList<RelatorioGeralAgendamentosVO>();

        String whereDataSub = "";
        if (Validador.isValidaObject(dataInicial) && Validador.isValidaObject(dataFinal)) {
            whereDataSub = " AND (agenda_sub.data_aula BETWEEN '" + Formatador.formatarData(dataInicial, "yyyy-MM-dd")
                         + "' AND '" + Formatador.formatarData(dataFinal, "yyyy-MM-dd") + "')";
        }

        String sql = " SELECT pessoaColab.nome AS desc_colab, prod.descricao AS desc_prod, cli.codigomatricula, pessoaCli.nome AS desc_cliente, "
                   + " agenda.data_aula, agenda.hora_inicio, agenda.hora_termino, amb.descricao AS desc_amb, agenda.status, agenda.id_agenda, "
                   + " th.sigla, item.quantidade AS qtd_vendida, (CASE WHEN movp.situacao = 'PG' THEN 'PG' WHEN af.id_agenda_faturar IS NOT NULL "
                   + " THEN 'NPF' WHEN movp.situacao = 'EA' THEN 'NPR' WHEN agenda.status = 'S' THEN 'SF' END) AS situacao, "

                   + " (SELECT COUNT(agenda_sub.status) "
                   + " FROM sch_estudio.agenda agenda_sub "
                   + " INNER JOIN sch_estudio.agenda_venda av_sub ON av_sub.id_agenda = agenda_sub.id_agenda "
                   + " INNER JOIN vendaavulsa venda_sub ON venda_sub.codigo = av_sub.id_vendaavulsa "
                   + " INNER JOIN itemvendaavulsa it_sub ON it_sub.vendaavulsa = venda_sub.codigo "
                   + " INNER JOIN sch_estudio.tipo_horario th_sub ON th_sub.id_tipo_horario = agenda_sub.id_tipo_horario "
                   + " INNER JOIN produto pd_sub ON pd_sub.codigo = agenda_sub.id_produto "
                   + " LEFT JOIN colaborador col_sub ON col_sub.codigo = agenda_sub.id_colaborador "
                   + " LEFT JOIN pessoa pesCol_sub ON pesCol_sub.codigo = col_sub.pessoa "
                   + " LEFT JOIN ambiente amb_sub ON amb_sub.codigo = agenda_sub.id_ambiente "
                   + " LEFT JOIN usuario usu_sub ON usu_sub.codigo = agenda_sub.id_usuario_lancamento "
                   + " WHERE agenda.id_empresa = ? AND it_sub.codigo = item.codigo AND pd_sub.codigo = prod.codigo " 
                   + " AND (agenda_sub.status = 'M' OR agenda_sub.status = 'F')) AS qtd_utilizada, "
                   
                   + " (SELECT count(agenda_sub.id_agenda_agendar) FROM sch_estudio.agenda_agendar  agenda_sub  \n"
                   +"  WHERE  agenda_sub.id_vendaavulsa = venda.id_vendaavulsa) AS qtd_agendar "

                   + " FROM sch_estudio.agenda agenda "

                   + " INNER JOIN sch_estudio.tipo_horario th ON th.id_tipo_horario = agenda.id_tipo_horario "
                   + " INNER JOIN public.ambiente amb ON amb.codigo = agenda.id_ambiente "
                   + " INNER JOIN public.produto prod ON prod.codigo = agenda.id_produto AND prod.desativado = FALSE AND prod.tipoproduto = 'SS' "
                   + " INNER JOIN public.colaborador colab ON colab.codigo = agenda.id_colaborador AND colab.situacao = 'AT' "
                   + " LEFT JOIN public.tipocolaborador tipoColab ON tipoColab.colaborador = colab.codigo AND tipoColab.descricao = 'ES' "
                   + " LEFT JOIN sch_estudio.produto_colaborador relprod ON relprod.id_produto = agenda.id_produto AND relprod.id_colaborador = agenda.id_colaborador "
                   + " INNER JOIN public.cliente cli ON cli.codigo = agenda.id_cliente "
                   + " LEFT JOIN sch_estudio.agenda_venda venda ON venda.id_agenda = agenda.id_agenda "
                   + " LEFT JOIN public.itemvendaavulsa item ON item.vendaavulsa = venda.id_vendaavulsa AND item.produto = agenda.id_produto "
                   + " INNER JOIN public.pessoa pessoaColab ON pessoaColab.codigo = colab.pessoa "
                   + " INNER JOIN public.pessoa pessoaCli ON pessoaCli.codigo = cli.pessoa "
                   + " LEFT JOIN movparcela movp ON movp.vendaavulsa = venda.id_vendaavulsa "
                   + " LEFT JOIN sch_estudio.agenda_faturar af ON af.id_agenda = agenda.id_agenda "

            	   + " WHERE agenda.id_empresa = ? "
            	   + filtros(dataInicial, dataFinal, horaInicial, horaFinal, listaColaborador, listaAmbiente, listaProduto, listaStatus, listaTipoHorario, situacaoParcela, nomeCliente)
            	   + " ORDER BY desc_colab, desc_prod, agenda.data_aula, agenda.hora_inicio ";

        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, idEmpresa);
        ps.setInt(2, idEmpresa);
        ResultSet resultDados = ps.executeQuery();
        while (resultDados.next()) {
            RelatorioGeralAgendamentosVO obj = new RelatorioGeralAgendamentosVO();
            obj = montarDados(obj, resultDados, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            listaGeralAgendamentos.add(obj);
        }

        return listaGeralAgendamentos;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>RelatorioGeralAgendamentosVO</code>.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     * @param rs Objeto no está contido o resultado do BD
     * @return RelatorioGeralAgendamentosVO
     * @throws Exception
     *
     */
    public RelatorioGeralAgendamentosVO montarDados(RelatorioGeralAgendamentosVO obj, ResultSet rs, int nivelMontarDados) throws Exception {

    	if(nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
    		obj.setTotalizador(rs.getInt("totalizador"));
            obj.setDescStatus(StatusEnum.get(rs.getString("status")));
    	}
    	if(nivelMontarDados == Uteis.NIVELMONTARDADOS_PARCELA) {
    		obj.setTotalizador(rs.getInt("totalizador"));
    		obj.setSituacaoParcela(SituacaoParcelaEnum.getSituacaoParcela(rs.getString("situacaoPagamento")));
    	}
    	if(nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
    		obj.setTotalizador(rs.getInt("totalizador"));
            obj.setDescTipoHorario(SiglaTipoHorarioEnum.get(rs.getString("sigla").trim()));
    	}
    	if(nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
	        obj.setDescColaborador(Uteis.obterPrimeiroNomeConcatenadoSobreNome(rs.getString("desc_colab")));
	        obj.setDescProduto(rs.getString("desc_prod"));
	        obj.setDescAmbiente(rs.getString("desc_amb"));
	        obj.setDescCliente(rs.getString("desc_cliente"));
	        obj.setCodgMatricula(rs.getInt("codigomatricula"));
	        obj.setDataAula(rs.getDate("data_aula"));
	        obj.setHoraInicio(rs.getTime("hora_inicio"));
	        if(!(obj.getHoraInicio().toString().equals(Calendario.gerarHorarioInicial(obj.getHoraInicio()) + ":00")
	           && rs.getTime("hora_termino").toString().equals(Calendario.gerarHorarioFinal(rs.getTime("hora_termino")) + ":59"))){
	           obj.setHoraTermino(rs.getTime("hora_termino"));
	        }
	        obj.setDescStatus(StatusEnum.get(rs.getString("status")));
	        obj.setDescTipoHorario(SiglaTipoHorarioEnum.get(rs.getString("sigla").trim()));
	        obj.setQtdVendida(rs.getInt("qtd_vendida"));
	        obj.setQtdUtilizada(rs.getInt("qtd_utilizada"));
	        obj.setQtdAgendar(rs.getInt("qtd_agendar"));
	        obj.setCodigoAgenda(rs.getInt("id_agenda"));
	        obj.setSituacaoParcela(SituacaoParcelaEnum.getSituacaoParcela(rs.getString("situacao")));
    	}

    	return obj;
    }

    public RelatorioGeralAgendamentos() throws Exception {
        super();
    }

    public RelatorioGeralAgendamentos(Connection con) throws Exception {
        super(con);
    }
}
