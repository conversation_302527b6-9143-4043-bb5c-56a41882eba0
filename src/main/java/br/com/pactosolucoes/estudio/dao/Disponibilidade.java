package br.com.pactosolucoes.estudio.dao;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.estudio.enumeradores.DiaDaSemanaEnum;
import br.com.pactosolucoes.estudio.interfaces.DisponibilidadeInterfaceFacade;
import br.com.pactosolucoes.estudio.modelo.ConfiguracaoEstudioVO;
import br.com.pactosolucoes.estudio.modelo.DisponibilidadeVO;
import br.com.pactosolucoes.estudio.modelo.IndisponibilidadeVO;
import br.com.pactosolucoes.estudio.modelo.TipoHorarioVO;
import br.com.pactosolucoes.estudio.util.Validador;
import java.sql.Date;
import java.sql.*;
import java.util.*;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

/**
 *
 * <AUTHOR> GeoInova Soluções
 */
public class Disponibilidade extends SuperEntidade implements DisponibilidadeInterfaceFacade {

    /**
     * Método que seleciona a função de disponibilidade, trazendo a relação
     * passada e os dias disponíveis dentro de uma tabela
     *
     * @param listaProduto
     * @param listaColaborador
     * @param listaAmbiente
     * @param listaHora
     * @param listaDiaDaSemana
     * @param periodoInicial
     * @param periodoFinal
     * @param configuracao
     * @return novaLista
     * @throws Exception
     */
    @Override
    public List<DisponibilidadeVO> funcaoRelProdutoColaboradorAmbiente(List<ProdutoVO> listaProduto, List<Integer> listaColaborador,
            List<Integer> listaAmbiente, List<DiaDaSemanaEnum> listaDiaDaSemana,
            Date periodoInicial, Date periodoFinal, ConfiguracaoEstudioVO configuracao) throws Exception {
        try {
            List<DisponibilidadeVO> listaDisponibilidade = new ArrayList<DisponibilidadeVO>();

            String sqlP = "";
            for (int i = 0; i < listaProduto.size(); i++) {
                sqlP += listaProduto.get(i).getCodigo().toString();
                if (i != listaProduto.size() - 1) {
                    sqlP += ";";
                }
            }

            String sqlC = "";
            for (int i = 0; i < listaColaborador.size(); i++) {
                sqlC += String.valueOf(listaColaborador.get(i));
                if (i != listaColaborador.size() - 1) {
                    sqlC += ";";
                }
            }

            String sqlA = "";
            for (int i = 0; i < listaAmbiente.size(); i++) {
                sqlA += String.valueOf(listaAmbiente.get(i));
                if (i != listaAmbiente.size() - 1) {
                    sqlA += ";";
                }
            }

            String where = "";
            // Filtrando os Socios
            for (int cont = 0; cont < listaDiaDaSemana.size(); ++cont) {
                if (where.trim().isEmpty()) {
                    where += "WHERE (";
                }

                if ((cont + 1) == listaDiaDaSemana.size()) {
                    where += "((extract(dow from dia_mes_table::date) +1) = " + listaDiaDaSemana.get(cont).getId() + ")) ";
                } else {
                    where += "((extract(dow from dia_mes_table::date) +1) = " + listaDiaDaSemana.get(cont).getId() + ") OR ";
                }
            }

            String sql = "SELECT * FROM sch_estudio.fc_disponibilidade(?,?,?,?,?,?) " + where;

            PreparedStatement ps = con.prepareStatement(sql);
            ps.setString(1, sqlP);
            ps.setString(2, sqlC);
            ps.setString(3, sqlA);
            ps.setTimestamp(4, new Timestamp(periodoInicial.getTime()));
            ps.setTimestamp(5, new Timestamp(periodoFinal.getTime()));
            ps.setInt(6, configuracao.getEmpresaVO().getCodigo());
            ResultSet resultDados = ps.executeQuery();
            while (resultDados.next()) {
                DisponibilidadeVO obj = new DisponibilidadeVO();
                obj = montarDados(obj, resultDados, false, true);
                listaDisponibilidade.add(obj);
            }

            return listaDisponibilidade;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Método principal que retorna a lista de disponibilidade organizada com o
     * número de matriculados e sua capacidade
     *
     * @param listaProduto
     * @param listaColaborador
     * @param listaAmbiente
     * @param listaHora
     * @param listaDiaDaSemana
     * @param periodoInicial
     * @param periodoFinal
     * @param configuracao
     * @return listaFuncao
     * @throws Exception
     */
    @Override
    public List<DisponibilidadeVO> acaoPrincipal(List<ProdutoVO> listaProduto, List<Integer> listaColaborador, List<Integer> listaAmbiente,
            List<String> listaHora, List<DiaDaSemanaEnum> listaDiaDaSemana, Date periodoInicial, Date periodoFinal, ConfiguracaoEstudioVO configuracao,
            List<DisponibilidadeVO> listaDisponibilidadeAgendado, boolean apresentarHorariosQuebrados, Integer codigoAgenda) throws Exception {
        try {

            List<DisponibilidadeVO> listaFuncao = acaoHora(funcaoRelProdutoColaboradorAmbiente(listaProduto, listaColaborador, listaAmbiente, listaDiaDaSemana, periodoInicial, periodoFinal, configuracao),
                    listaHora, configuracao, periodoInicial, periodoFinal, listaDisponibilidadeAgendado, apresentarHorariosQuebrados, codigoAgenda);

            return listaFuncao;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Método de horas que detalha e distribui os registros em cada hora,
     * retirando os registros que se encontram na faixa de horário indisponível,
     * relacionado para cada objeto, colaborador, ambiente e empresa fechada
     *
     * @param lista
     * @param listaHoraMain
     * @param configuracao
     * @param periodoInicial
     * @param periodoFinal
     * @param listaDisponibilidadeAgendado
     * @param duplicado
     * @return
     * @throws Exception
     */
    private List<DisponibilidadeVO> acaoHora(List<DisponibilidadeVO> lista, List<String> listaHorarioMain, ConfiguracaoEstudioVO configuracao,
            Date periodoInicial, Date periodoFinal, List<DisponibilidadeVO> listaDisponibilidadeAgendado, boolean apresentarHorariosQuebrados, Integer codigoAgenda) throws Exception {
        try {
            TipoHorarioVO tipoHorarioVO = definirTipoHorario();
            List<String> listaHora = new ArrayList<String>();
            Calendar calendarInicial = Calendar.getInstance();
            Calendar calendarFinal = Calendar.getInstance();
            if (listaHorarioMain.isEmpty()) {
                calendarInicial.setTime(configuracao.getHoraInicial());
                calendarFinal.setTime(configuracao.getHoraFinal());
                int calFinal = (calendarFinal.get(Calendar.HOUR_OF_DAY) == 00 ? 24 : calendarFinal.get(Calendar.HOUR_OF_DAY));

                for (Integer i = calendarInicial.get(Calendar.HOUR_OF_DAY); i < calFinal; i++) {
                    listaHora.add(i < 10 ? "0" + String.valueOf(i + ":00")
                            : "" + String.valueOf(i + ":00"));
                }
            } else {
                for (int i = 0; i < listaHorarioMain.size(); i++) {
                    String hora = String.valueOf(listaHorarioMain.get(i));

                    listaHora.add(hora);
                }
            }

            List<DisponibilidadeVO> listaVisao = acaoBuscaContadorVisao(periodoInicial, periodoFinal, codigoAgenda);
            List<DisponibilidadeVO> listaAmbienteVisao = acaoBuscaContadorAmbienteVisao(periodoInicial, periodoFinal, codigoAgenda);
            List<DisponibilidadeVO> listaColaboradorVisao = acaoBuscaContadorColaboradorVisao(periodoInicial, periodoFinal, codigoAgenda);
            boolean validacaoEmp = true;
            boolean validacaoColaborador = true;
            boolean validacaoAmbiente = true;

            List<DisponibilidadeVO> novaLista = new ArrayList<DisponibilidadeVO>();
            Iterator<DisponibilidadeVO> iterator = lista.iterator();
            DisponibilidadeVO dis = new DisponibilidadeVO();

            while (iterator.hasNext()) {
                boolean gerarMaisHorariosQuebradosNessaHora = false;
                dis = ((DisponibilidadeVO) iterator.next());
                FOR_LISTAHORARIOS:
                for (int i = 0; i < listaHora.size(); i++) {
                    String[] listaHoras = listaHora.get(i).split("/");
                    Time horaInicial = Formatador.obterTime(listaHoras[0]);
                    Time horaFinal;
                    if (listaHoras.length == 2) {
                        horaFinal = Formatador.obterTimeComSegundos(listaHoras[1]);
                    } else {
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(horaInicial);
                        horaFinal = Formatador.obterTimeComSegundos(calendar.get(Calendar.HOUR_OF_DAY) < 10 ? "0" + String.valueOf(calendar.get(Calendar.HOUR_OF_DAY) + ":59:59")
                                : "" + String.valueOf(calendar.get(Calendar.HOUR_OF_DAY) + ":59:59"));
                    }
                    DisponibilidadeVO dis2 = (DisponibilidadeVO) dis.getClone(false);

                    //COLABORADOR INDISPONIVEL
                    validacaoColaborador = true;
                    if (Validador.isValidaString(dis2.getHoraIndispInicialColabString())) {
                        String horaAuxInicial[] = dis2.getHoraIndispInicialColabString().split(";");
                        String horaAuxFinal[] = dis2.getHoraIndispFinalColabString().split(";");

                        for (int u = 0; u < horaAuxInicial.length; u++) {
                            dis2.setHoraIndispInicialColab(Formatador.obterTime(horaAuxInicial[u]));
                            dis2.setHoraIndispFinalColab(Formatador.obterTime(horaAuxFinal[u]));
                            if (//VERIFICA SE A HORA INICIAL DA DISPONIBILIDADE ESTÁ ENTRE A HORA INICIAL E FINAL DA INDISPONIBILIDADE
                                    (horaInicial.after(dis2.getHoraIndispInicialColab())
                                    && horaInicial.before(dis2.getHoraIndispFinalColab()))
                                    ||
                                    //VERIFICA SE A HORA FINAL DA DISPONIBILIDADE ESTÁ ENTRE A HORA INICIAL E FINAL DA INDISPONIBILIDADE
                                    (horaFinal.after(dis2.getHoraIndispInicialColab())
                                    && horaFinal.before(dis2.getHoraIndispFinalColab()))
                                    ||
                                    //VERIFICA SE A HORA INICIAL DA INDISPONIBILIDADE ESTÁ ENTRE A HORA INICIAL E FINAL DA DISPONIBILIDADE
                                    (dis2.getHoraIndispInicialColab().after(horaInicial)
                                    && dis2.getHoraIndispInicialColab().before(horaFinal))
                                    ||
                                    //VERIFICA SE A HORA FINAL DA DISPONIBILIDADE ESTÁ ENTRE A HORA INICIAL E FINAL DA INDISPONIBILIDADE
                                    (dis2.getHoraIndispFinalColab().after(horaInicial)
                                    && dis2.getHoraIndispFinalColab().before(horaFinal))
                                    ||
                                    //OU SE SÃO IGUAIS
                                    (horaInicial.equals(dis2.getHoraIndispInicialColab())
                                    && horaFinal.equals(dis2.getHoraIndispFinalColab()))
                                    ){ // COLABORADOR
                                validacaoColaborador = false;
                                break;
                            }
                        }
                    }
                    //AMBIENTE INDISPONIVEL
                    validacaoAmbiente = true;
                    if (Validador.isValidaString(dis2.getHoraIndispInicialAmbienteString())) {
                        String horaAuxInicial[] = dis2.getHoraIndispInicialAmbienteString().split(";");
                        String horaAuxFinal[] = dis2.getHoraIndispFinalAmbienteString().split(";");

                        for (int u = 0; u < horaAuxInicial.length; u++) {
                            dis2.setHoraIndispInicialAmbiente(Formatador.obterTime(horaAuxInicial[u]));
                            dis2.setHoraIndispFinalAmbiente(Formatador.obterTime(horaAuxFinal[u]));

                            if ((horaInicial.after(dis2.getHoraIndispInicialAmbiente()) || horaInicial.equals(dis2.getHoraIndispInicialAmbiente()))
                                    && horaFinal.before(dis2.getHoraIndispFinalAmbiente())) { // AMBIENTE
                                validacaoAmbiente = false;
                                break;
                            }
                        }
                    }
                    //EMPRESA INDISPONIVEL
                    validacaoEmp = true;
                    if (Validador.isValidaString(dis2.getHoraInicialEmpresaString())) {
                        String horaAuxInicial[] = dis2.getHoraInicialEmpresaString().split(";");
                        String horaAuxFinal[] = dis2.getHoraFinalEmpresaString().split(";");

                        for (int u = 0; u < horaAuxInicial.length; u++) {
                            dis2.setHoraInicialEmpresa(Formatador.obterTime(horaAuxInicial[u]));
                            dis2.setHoraFinalEmpresa(Formatador.obterTime(horaAuxFinal[u]));

                            calendarInicial.setTime(dis2.getHoraInicialEmpresa()); // HORA INICIAL EMPRESA INDISPONIVEL
                            calendarFinal.setTime(dis2.getHoraFinalEmpresa()); // HORA FINAL EMPRESA INDISPONIVEL
                            if ((horaInicial.after(dis2.getHoraInicialEmpresa()) || horaInicial.equals(dis2.getHoraInicialEmpresa()))
                                    && horaFinal.before(dis2.getHoraFinalEmpresa())) { // AMBIENTE
                                validacaoEmp = false;
                                break;
                            }
                        }
                    }

                    //validar agendamentos feitos
                    if (validacaoEmp && validacaoColaborador && validacaoAmbiente) {
                        dis2.setHoraInicial(horaInicial);
                        dis2.setHoraFinal(horaFinal);
                        // capacidade ambiente

                        FOR_AMBIENTE:
                        for (DisponibilidadeVO disponibilidadeVO : listaAmbienteVisao) {
                            if (disponibilidadeVO.equalsDiaAmbiente(dis2) && disponibilidadeVO.validarIntervaloHoras(dis2)) {
                                gerarMaisHorariosQuebradosNessaHora = gerarHorariosQuebrados(apresentarHorariosQuebrados,
                                        disponibilidadeVO, dis2, dis, novaLista, tipoHorarioVO, gerarMaisHorariosQuebradosNessaHora);
                                if (dis2.getAmbienteVO().getCapacidade() <= disponibilidadeVO.getMatriculados()) {
                                    if (apresentarHorariosQuebrados) {
                                        if(gerarMaisHorariosQuebradosNessaHora)
                                        break FOR_AMBIENTE;
                                    }
                                    continue FOR_LISTAHORARIOS;
                                }
                            }
                        }


                        // capacidade colaborador

                        FOR_COLABORADOR:
                        for (DisponibilidadeVO disponibilidadeVO : listaColaboradorVisao) {
                            if (disponibilidadeVO.equalsDiaColaborador(dis2) && disponibilidadeVO.validarIntervaloHoras(dis2)) {
                                gerarMaisHorariosQuebradosNessaHora = gerarHorariosQuebrados(apresentarHorariosQuebrados, disponibilidadeVO, dis2, dis,
                                        novaLista, tipoHorarioVO, gerarMaisHorariosQuebradosNessaHora);
//                                if ((!dis2.getAmbienteVO().getCodigo().equals(disponibilidadeVO.getAmbienteVO().getCodigo()))
//                                        || (!dis2.getProdutoVO().getCodigo().equals(disponibilidadeVO.getProdutoVO().getCodigo()))) {
                                if (!dis2.getAmbienteVO().getCodigo().equals(disponibilidadeVO.getAmbienteVO().getCodigo())) {
                                    if (apresentarHorariosQuebrados) {
                                        if(gerarMaisHorariosQuebradosNessaHora)break FOR_COLABORADOR;
                                    }
                                    continue FOR_LISTAHORARIOS;
                                }
                            }
                        }

                        // capacidade produto
                            int indice = -1;
                        for (DisponibilidadeVO disp : listaVisao) {
                            if (disp.equalsDiaColaboradorAmbienteHora(dis2)) {
                                indice = listaVisao.indexOf(disp);
                            }
                        }
                        Calendar calendarInicio = Calendar.getInstance();
                        calendarInicio.setTime(dis2.getHoraInicial());
                        Calendar calendarTermino = Calendar.getInstance();
                        calendarTermino.setTime(dis2.getHoraFinal());
                        if (indice >= 0) {
                            if (dis2.getProdutoVO().getCapacidade() > listaVisao.get(indice).getMatriculados()
                                    || !(dis2.getHoraInicial().toString().equals(Calendario.gerarHorarioInicial(dis2.getHoraInicial()) + ":00")
                                    && dis2.getHoraFinal().toString().equals(Calendario.gerarHorarioFinal(dis2.getHoraFinal()) + ":59")
                                    && calendarInicio.get(Calendar.HOUR_OF_DAY) == calendarTermino.get(Calendar.HOUR_OF_DAY))) {
                                dis2.setMatriculados(listaVisao.get(indice).getMatriculados());
                            } else {
                                continue;
                            }
                        } else {
                            dis2.setMatriculados(0);
                        }
                        String key = String.valueOf(dis2.getDataMes()) + String.valueOf(dis2.getHoraInicial())
                                + String.valueOf(dis2.getHoraFinal()) + String.valueOf(dis2.getProdutoVO().getCodigo())
                                + String.valueOf(dis2.getColaboradorVO().getCodigo()) + String.valueOf(dis2.getAmbienteVO().getCodigo());
                        dis2.setIdString(key);

                        // AGENDADO
                        if (!listaDisponibilidadeAgendado.isEmpty()) {
                            if (listaDisponibilidadeAgendado.contains(dis2)) {
                                dis2.setSelecionado(Boolean.TRUE);
                                dis2.setVerificador(Boolean.TRUE);
                            }
                        }
                        dis2.setTipoHorarioVO((TipoHorarioVO) tipoHorarioVO.getClone(true));
                        novaLista.add(dis2);
                    }
                }
            }
            return novaLista;

        } catch (Exception e) {
            throw e;
        }
    }

    public boolean gerarHorariosQuebrados(boolean gerarHorariosQuebrados, DisponibilidadeVO disponibilidadeVO,
            DisponibilidadeVO dis2, DisponibilidadeVO dis, List<DisponibilidadeVO> novaLista, TipoHorarioVO tipoHorario,
            boolean gerarMaisHorariosQuebradosNessaHora) throws Exception {
        if (gerarHorariosQuebrados) {
            Calendar calendarInicio = Calendar.getInstance();
            calendarInicio.setTime(disponibilidadeVO.getHoraInicial());
            Calendar calendarTermino = Calendar.getInstance();
            calendarTermino.setTime(disponibilidadeVO.getHoraFinal());
            boolean contemAgenda = getFacade().getAgendaEstudio().existeAgendamento(Uteis.getDataJDBC(dis2.getDataMes()), disponibilidadeVO.getHoraInicial(),
                    disponibilidadeVO.getHoraFinal(), dis2.getColaboradorVO().getCodigo().intValue(), dis2.getAmbienteVO().getCodigo().intValue(), dis2.getProdutoVO().getCodigo());
            if (!(disponibilidadeVO.getHoraInicial().toString().
                    equals(Calendario.gerarHorarioInicial(disponibilidadeVO.getHoraInicial()) + ":00")
                    && disponibilidadeVO.getHoraFinal().toString().equals(
                    Calendario.gerarHorarioFinal(disponibilidadeVO.getHoraFinal()) + ":59")
                    && calendarInicio.get(Calendar.HOUR_OF_DAY) == calendarTermino.get(Calendar.HOUR_OF_DAY))
                    && contemAgenda) {

                if (disponibilidadeVO.getHoraInicial().after(
                        Formatador.obterTime(Calendario.gerarHorarioInicial(disponibilidadeVO.getHoraInicial()) + ":00"))) {

                    DisponibilidadeVO disp1 = (DisponibilidadeVO) dis2.getClone(false);
                    disp1.setHoraInicial(Formatador.obterTimeComSegundos(Calendario.gerarHorarioInicial(disponibilidadeVO.getHoraInicial()) + ":00"));
                    calendarInicio.set(Calendar.MINUTE, calendarInicio.get(Calendar.MINUTE) - 1);
                    disp1.setHoraFinal(Formatador.obterTimeComSegundos(Formatador.formatarHorario(calendarInicio.getTime()) + ":59"));
                    disp1.setHorarioQuebrado(true);
                    if (!novaLista.contains(disp1) || getFacade().getAgendaEstudio().existemOutrosAgendamentosNessaHora(
                            Uteis.getDataJDBC(disp1.getDataMes()), disp1.getHoraInicial(), disp1.getHoraFinal(),
                            disp1.getColaboradorVO().getCodigo(), disp1.getAmbienteVO().getCodigo(), disp1.getProdutoVO().getCodigo())) {
                        disp1.setTipoHorarioVO(tipoHorario);
                        disp1.setMatriculados(0);
                        novaLista.add(disp1);
                    }
                    if (disponibilidadeVO.getHoraFinal().before(
                            Formatador.obterTime(Calendario.gerarHorarioFinal(disponibilidadeVO.getHoraFinal()) + ":59"))) {
                        DisponibilidadeVO disp3 = (DisponibilidadeVO) dis2.getClone(false);
                        calendarTermino.set(Calendar.MINUTE, calendarTermino.get(Calendar.MINUTE) + 1);
                        disp3.setHoraInicial(Formatador.obterTimeComSegundos(Formatador.formatarHorario(calendarTermino.getTime()) + ":00"));
                        disp3.setHoraFinal(Formatador.obterTimeComSegundos(Calendario.gerarHorarioFinal(disponibilidadeVO.getHoraFinal()) + ":59"));
                        disp3.setHorarioQuebrado(true);
                        disp3.setTipoHorarioVO(tipoHorario);
                        disp3.setMatriculados(0);
                        if (!novaLista.contains(disp3) || getFacade().getAgendaEstudio().existemOutrosAgendamentosNessaHora(
                                Uteis.getDataJDBC(disp3.getDataMes()), disp3.getHoraInicial(), disp3.getHoraFinal(),
                                disp3.getColaboradorVO().getCodigo(), disp3.getAmbienteVO().getCodigo(), disp3.getProdutoVO().getCodigo())) {
                            novaLista.add(disp3);
                        }
                    }
                } else if (disponibilidadeVO.getHoraInicial().equals(
                        Formatador.obterTime(Calendario.gerarHorarioInicial(disponibilidadeVO.getHoraInicial()) + ":00"))) {
                    if (disponibilidadeVO.getHoraFinal().after(
                            Formatador.obterTimeComSegundos(Calendario.gerarHorarioFinal(disponibilidadeVO.getHoraFinal()) + ":59"))) {
                        DisponibilidadeVO disp4 = (DisponibilidadeVO) dis2.getClone(false);
                        calendarInicio.set(Calendar.MINUTE, calendarInicio.get(Calendar.MINUTE) - 1);
                        disp4.setHoraInicial(Formatador.obterTimeComSegundos(Formatador.formatarHorario(calendarInicio.getTime()) + ":00"));
                        disp4.setHoraFinal(Formatador.obterTimeComSegundos(Calendario.gerarHorarioFinal(disponibilidadeVO.getHoraFinal()) + ":59"));
                        disp4.setHorarioQuebrado(true);
                        disp4.setTipoHorarioVO(tipoHorario);
                        disp4.setMatriculados(0);
                        if (!novaLista.contains(disp4) || getFacade().getAgendaEstudio().existemOutrosAgendamentosNessaHora(
                                Uteis.getDataJDBC(disp4.getDataMes()), disp4.getHoraInicial(), disp4.getHoraFinal(),
                                disp4.getColaboradorVO().getCodigo(), disp4.getAmbienteVO().getCodigo(), disp4.getProdutoVO().getCodigo())) {
                            novaLista.add(disp4);
                        }
                    } else if (disponibilidadeVO.getHoraFinal().before(
                            Formatador.obterTimeComSegundos(Calendario.gerarHorarioFinal(disponibilidadeVO.getHoraFinal()) + ":59"))) {
                        DisponibilidadeVO disp5 = (DisponibilidadeVO) dis2.getClone(false);
                        calendarTermino.set(Calendar.MINUTE, calendarTermino.get(Calendar.MINUTE) + 1);
                        disp5.setHoraInicial(Formatador.obterTimeComSegundos(Formatador.formatarHorario(calendarTermino.getTime()) + ":00"));
                        disp5.setHoraFinal(Formatador.obterTimeComSegundos(Calendario.gerarHorarioFinal(disponibilidadeVO.getHoraFinal()) + ":59"));
                        disp5.setHorarioQuebrado(true);
                        disp5.setTipoHorarioVO(tipoHorario);
                        disp5.setMatriculados(0);
                        if (!novaLista.contains(disp5) || getFacade().getAgendaEstudio().existemOutrosAgendamentosNessaHora(
                                Uteis.getDataJDBC(disp5.getDataMes()), disp5.getHoraInicial(), disp5.getHoraFinal(),
                                disp5.getColaboradorVO().getCodigo(), disp5.getAmbienteVO().getCodigo(), disp5.getProdutoVO().getCodigo())) {
                            novaLista.add(disp5);
                        }
                    }
                }
            }
            gerarMaisHorariosQuebradosNessaHora = verificarSeExisteMaisRegistrosDentroDessaHora(disponibilidadeVO, dis2);
        }
        return gerarMaisHorariosQuebradosNessaHora;
    }

    public boolean verificarSeExisteMaisRegistrosDentroDessaHora(DisponibilidadeVO disponibilidadeVO, DisponibilidadeVO dis2) throws Exception {
        return getFacade().getAgendaEstudio().existemOutrosAgendamentosNessaHora(Uteis.getDataJDBC(dis2.getDataMes()), disponibilidadeVO.getHoraInicial(),
                    disponibilidadeVO.getHoraFinal(), dis2.getColaboradorVO().getCodigo().intValue(), dis2.getAmbienteVO().getCodigo().intValue(), dis2.getProdutoVO().getCodigo());

    }

    /**
     * Método que seleciona a visão buscando dentro de uma faixa de datas
     * específicas
     *
     * @param periodoInicial
     * @param periodoFinal
     * @return
     * @throws Exception
     */
    private List<DisponibilidadeVO> acaoBuscaContadorVisao(Date periodoInicial, Date periodoFinal, Integer codigoAgenda) throws Exception {

        try {

            List<DisponibilidadeVO> listaDisponibilidade = new ArrayList<DisponibilidadeVO>();
            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT count(*) AS matricula, agenda.data_aula AS dia_mes_table, agenda.hora_inicio, agenda.hora_termino, agenda.id_colaborador AS colaborador, agenda.id_ambiente AS ambiente, agenda.id_produto AS produto ");
            sql.append(" FROM sch_estudio.agenda ");
            sql.append(" WHERE agenda.status <> 'F'::bpchar AND agenda.status <> 'X'::bpchar AND agenda.status <> 'S'::bpchar ");
            if(!UteisValidacao.emptyNumber(codigoAgenda)){
                sql.append("       and agenda.id_agenda <> ").append(codigoAgenda);
            }
            sql.append("       AND    agenda.data_aula BETWEEN ? AND ? ");
            sql.append("       GROUP BY agenda.data_aula, agenda.hora_inicio, agenda.hora_termino, agenda.id_colaborador, agenda.id_ambiente, agenda.id_produto  ");

            PreparedStatement ps = con.prepareStatement(sql.toString());
            ps.setTimestamp(1, new Timestamp(periodoInicial.getTime()));
            ps.setTimestamp(2, new Timestamp(periodoFinal.getTime()));
            ResultSet resultDados = ps.executeQuery();
            while (resultDados.next()) {
                DisponibilidadeVO obj = new DisponibilidadeVO();
                obj = montarDados(obj, resultDados, true, true);
                listaDisponibilidade.add(obj);
            }
            return listaDisponibilidade;
        } catch (Exception e) {
            throw e;


        }
    }

    /**
     * Método que seleciona a visão buscando dentro de uma faixa de datas
     * específicas
     *
     * @param periodoInicial
     * @param periodoFinal
     * @return
     * @throws Exception
     */
    private List<DisponibilidadeVO> acaoBuscaContadorColaboradorVisao(Date periodoInicial, Date periodoFinal, Integer codigoAgenda) throws Exception {

        try {

            List<DisponibilidadeVO> listaDisponibilidade = new ArrayList<DisponibilidadeVO>();
            
             StringBuilder sql = new StringBuilder();
            sql.append(" SELECT count(*) AS matricula, agenda.data_aula AS dia_mes_table, agenda.hora_inicio, agenda.hora_termino, agenda.id_colaborador AS colaborador, agenda.id_produto AS produto, agenda.id_ambiente AS ambiente ");
            sql.append(" FROM sch_estudio.agenda ");
            sql.append(" WHERE agenda.status <> 'F'::bpchar AND agenda.status <> 'X'::bpchar AND agenda.status <> 'S'::bpchar ");
            if(!UteisValidacao.emptyNumber(codigoAgenda)){
                sql.append("       and agenda.id_agenda <> ").append(codigoAgenda);
            }
            sql.append("       AND    agenda.data_aula BETWEEN ? AND ? ");
            sql.append("        GROUP BY agenda.data_aula, agenda.hora_inicio, agenda.hora_termino, agenda.id_colaborador, agenda.id_produto, agenda.id_ambiente ");
            sql.append("        ORDER BY agenda.data_aula; ");
            
            PreparedStatement ps = con.prepareStatement(sql.toString());
            ps.setTimestamp(1, new Timestamp(periodoInicial.getTime()));
            ps.setTimestamp(2, new Timestamp(periodoFinal.getTime()));
            ResultSet resultDados = ps.executeQuery();
            while (resultDados.next()) {
                DisponibilidadeVO obj = new DisponibilidadeVO();
                obj = montarDados(obj, resultDados, true, true);
                listaDisponibilidade.add(obj);
            }
            return listaDisponibilidade;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Método que seleciona a visão buscando agrupamento de ambiente, dentro de
     * uma faixa de datas específicas
     *
     * @param periodoInicial
     * @param periodoFinal
     * @return
     * @throws Exception
     */
    private List<DisponibilidadeVO> acaoBuscaContadorAmbienteVisao(Date periodoInicial, Date periodoFinal, Integer codigoAgenda) throws Exception {

        try {

            List<DisponibilidadeVO> listaDisponibilidade = new ArrayList<DisponibilidadeVO>();
            
            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT count(*) AS matricula, agenda.data_aula AS dia_mes_table, agenda.hora_inicio, agenda.hora_termino, agenda.id_ambiente AS ambiente ");
            sql.append(" FROM sch_estudio.agenda ");
            sql.append(" WHERE agenda.status <> 'F'::bpchar AND agenda.status <> 'X'::bpchar AND agenda.status <> 'S'::bpchar ");
            if(!UteisValidacao.emptyNumber(codigoAgenda)){
                sql.append("       and agenda.id_agenda <> ").append(codigoAgenda);
            }
            sql.append("       AND    agenda.data_aula BETWEEN ? AND ? ");
            sql.append("       GROUP BY agenda.data_aula, agenda.hora_inicio, agenda.hora_termino, agenda.id_ambiente ");
            
            PreparedStatement ps = con.prepareStatement(sql.toString());
            ps.setTimestamp(1, new Timestamp(periodoInicial.getTime()));
            ps.setTimestamp(2, new Timestamp(periodoFinal.getTime()));
            ResultSet resultDados = ps.executeQuery();
            while (resultDados.next()) {
                DisponibilidadeVO obj = new DisponibilidadeVO();
                obj = montarDados(obj, resultDados, true, false);
                listaDisponibilidade.add(obj);
            }
            return listaDisponibilidade;

        } catch (Exception e) {
            throw e;


        }
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>DisponibilidadeVO</code>.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     * @param rs Objeto no está contido o resultado do BD
     * @param cont boolean para condição de contador
     * @return DisponibilidadeVO
     * @throws Exception
     *
     */
    public DisponibilidadeVO montarDados(DisponibilidadeVO obj, ResultSet rs, boolean contador, boolean produto) throws Exception {

        if (produto) {
            obj.getProdutoVO().setCodigo(rs.getInt("produto"));
            obj.getColaboradorVO().setCodigo(rs.getInt("colaborador"));
        }

        obj.getAmbienteVO().setCodigo(rs.getInt("ambiente"));
        obj.setDataMes(rs.getDate("dia_mes_table"));
        if (contador) {
            obj.setHoraInicial(rs.getTime("hora_inicio"));
            obj.setHoraFinal(rs.getTime("hora_termino"));
            obj.setMatriculados(rs.getInt("matricula"));
        } else {
            obj.getProdutoVO().setDescricao(rs.getString("desc_produto"));
            obj.getProdutoVO().setCapacidade(rs.getInt("capacidade_produto"));
            obj.getProdutoVO().setValorFinal(rs.getDouble("valor"));
            obj.getColaboradorVO().getPessoa().setNome(rs.getString("desc_colab"));
            obj.setHoraIndispInicialColabString(rs.getString("horainicol"));
            obj.setHoraIndispFinalColabString(rs.getString("horaficol"));
            obj.getAmbienteVO().setDescricao(rs.getString("desc_ambiente"));
            obj.getAmbienteVO().setCapacidade(rs.getInt("capacidade_ambiente"));
            obj.setHoraIndispInicialAmbienteString(rs.getString("horainiamb"));
            obj.setHoraIndispFinalAmbienteString(rs.getString("horafiamb"));
            obj.setHoraInicialEmpresaString(rs.getString("horainiemp"));
            obj.setHoraFinalEmpresaString(rs.getString("horafiemp"));

            Calendar calen = Calendar.getInstance();
            calen.setTime(obj.getDataMes());
            obj.setDiaSemanaEnum(DiaDaSemanaEnum.get(calen.get(Calendar.DAY_OF_WEEK)));
        }
        return obj;
    }

    /**
     * Método que busca a indisponibilidade do ambiente ou colaborador,
     * dependendo do filtro
     *
     * @param String nome Filtro
     * @param int id Filtro
     * @param Date data Inicial
     * @param Date data Final
     * @return List<IndisponibilidadeVO> lista de indisponibilidade
     * @throws Exception
     */
    @Override
    public List<IndisponibilidadeVO> buscarIndisponibilidadeFiltro(String nomeFiltro, String nomeTabela, int idFiltro, Date dataInicial, Date dataFinal) throws Exception {
        List<IndisponibilidadeVO> listaIndisponibilidades = new ArrayList<IndisponibilidadeVO>();
        String sql = "SELECT dia_mes_serie, hora_inicial, hora_final  "
                + "FROM sch_estudio." + nomeTabela.toLowerCase() + " "
                + "INNER JOIN (SELECT to_char(t, 'yyyy-MM-dd') as dia_mes_serie "
                + "FROM generate_series(?::timestamp, ?::timestamp, '1 day') as t ) AS TT ON (dia_mes_serie::date = dia_mes) OR "
                + "dia_semana = (extract(dow from dia_mes_serie::date) + 1) "
                + "WHERE id_" + nomeFiltro.toLowerCase() + " = ?";
        PreparedStatement pStmt = getCon().prepareStatement(sql);
        pStmt.setTimestamp(1, new Timestamp(dataInicial.getTime()));
        pStmt.setTimestamp(2, new Timestamp(dataFinal.getTime()));
        pStmt.setInt(3, idFiltro);
        ResultSet rs = pStmt.executeQuery();
        while (rs.next()) {
            IndisponibilidadeVO indisponibilidade = new IndisponibilidadeVO();
            indisponibilidade.setDiaMes(rs.getDate("dia_mes_serie"));
            indisponibilidade.setHoraInicial(rs.getTime("hora_inicial"));
            indisponibilidade.setHoraFinal(rs.getTime("hora_final"));
            listaIndisponibilidades.add(indisponibilidade);
        }
        return listaIndisponibilidades;
    }

    /**
     * Seleciona os feriados do banco de dados
     *
     * @param dataInicial
     * @param dataFinal
     * @return
     * @throws Exception
     */
    @Override
    public List<IndisponibilidadeVO> buscarFeriados(Date dataInicial, Date dataFinal) throws Exception {
        List<IndisponibilidadeVO> listaIndisponibilidades = new ArrayList<IndisponibilidadeVO>();
        String sql = "SELECT dia FROM feriado "
                + "WHERE dia BETWEEN ? AND ?";
        PreparedStatement pStmt = getCon().prepareStatement(sql);
        pStmt.setTimestamp(1, new Timestamp(dataInicial.getTime()));
        pStmt.setTimestamp(2, new Timestamp(dataFinal.getTime()));
        ResultSet rs = pStmt.executeQuery();
        while (rs.next()) {
            IndisponibilidadeVO indisponibilidade = new IndisponibilidadeVO();
            indisponibilidade.setDiaMes(rs.getDate("dia"));
            indisponibilidade.setHoraInicial(null);
            indisponibilidade.setHoraFinal(null);
            listaIndisponibilidades.add(indisponibilidade);
        }
        return listaIndisponibilidades;
    }

    public Disponibilidade() throws Exception {
        super();


    }

    public Disponibilidade(
            Connection con) throws Exception {
        super(con);

    }

    private TipoHorarioVO definirTipoHorario() throws Exception {
        TipoHorarioVO tipoHorarioVO = new TipoHorarioVO();
        tipoHorarioVO.setSigla("SS");
        tipoHorarioVO = getFacade().getTipoHorario().procurarPorSiglaAtivo(tipoHorarioVO);
        if(UteisValidacao.emptyNumber(tipoHorarioVO.getCodigo())){
            tipoHorarioVO = getFacade().getTipoHorario().consultarPrimeiroTipoCadastrado();
        }
        return tipoHorarioVO;
    }
}
