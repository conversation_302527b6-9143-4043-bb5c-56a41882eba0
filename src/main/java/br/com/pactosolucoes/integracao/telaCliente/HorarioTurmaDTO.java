package br.com.pactosolucoes.integracao.telaCliente;

public class HorarioTurmaDTO {
    private final String diaSemana;
    private Integer codigo;
    private String identificadorTurma;
    private Integer diaSemanaNumero;
    private String horaInicial;
    private String horaFinal;
    private ColaboradorDTO professor;

    public HorarioTurmaDTO(Integer codigo, String identificadorTurma, String diaSemana, Integer diaSemanaNumero, String horaInicial, String horaFinal, ColaboradorDTO professor) {
        this.codigo = codigo;
        this.identificadorTurma = identificadorTurma;
        this.diaSemanaNumero = diaSemanaNumero;
        this.horaInicial = horaInicial;
        this.horaFinal = horaFinal;
        this.professor = professor;
        this.diaSemana = diaSemana;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getIdentificadorTurma() {
        return identificadorTurma;
    }

    public Integer getDiaSemanaNumero() {
        return diaSemanaNumero;
    }

    public ColaboradorDTO getProfessor() {
        return professor;
    }

    public String getHoraInicial() {
        return horaInicial;
    }

    public String getHoraFinal() {
        return horaFinal;
    }

    public String getDiaSemana() {
        return diaSemana;
    }
}
