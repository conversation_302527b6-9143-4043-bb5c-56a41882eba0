package br.com.pactosolucoes.integracao.enotas.nfe;

import negocio.comuns.notaFiscal.NotaPagamentoTO;
import negocio.comuns.notaFiscal.NotaProdutoTO;
import negocio.comuns.notaFiscal.NotaTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

public class NFeEnotasJSON {

    public JSONObject obterJSONEnotas(NotaTO notaTO) throws JSONException {
        JSONObject json = new JSONObject();

        json.put("id", notaTO.getNotaIDReferencia());
        json.put("ambienteEmissao", notaTO.getConfiguracaoNotaFiscalVO().getAmbienteEmissao().getDescricaoSemAcentuacao());
        if (!Calendario.igual(Calendario.hoje(), notaTO.getNotaDtEmissao())) {
            json.put("dataCompetencia", (notaTO.getNotaDtEmissao() != null) ? notaTO.getNotaDtEmissao().toInstant().truncatedTo(ChronoUnit.SECONDS).toString() : null);
            json.put("dataEmissao", (notaTO.getNotaDtEmissao() != null) ? notaTO.getNotaDtEmissao().toInstant().truncatedTo(ChronoUnit.SECONDS).toString() : null);
        }
        json.put("naturezaOperacao", notaTO.getNotaNaturezaOperacao());
        json.put("finalidade", "Normal");
        json.put("consumidorFinal", true);
        json.put("indicadorPresencaConsumidor", "OperacaoPelaInternet");
        json.put("enviarPorEmail", notaTO.isNotaEnviarEmail());
        json.put("informacoesAdicionais", notaTO.getNotaObservacao().trim());

        JSONObject cliente = new JSONObject();
        cliente.put("tipoPessoa", notaTO.getCliTipo());
        cliente.put("indicadorContribuinteICMS", "NaoContribuinte");
        cliente.put("nome", notaTO.getCliRazaoSocial());
        cliente.put("cpfCnpj", Uteis.tirarCaracteres(notaTO.getCliCPFCNPJ(), true));
        cliente.put("inscricaoEstadual", notaTO.getCliInscEstadual().equals("") ? JSONObject.NULL : notaTO.getCliInscEstadual());
        cliente.put("inscricaoMunicipal", notaTO.getCliInscMunicipal().equals("") ? JSONObject.NULL : notaTO.getCliInscMunicipal());
        cliente.put("email", notaTO.getCliEmail());
        cliente.put("telefone", Uteis.tirarCaracteres(notaTO.getCliTelefone(), true));

        JSONObject cliEndereco = new JSONObject();
        cliEndereco.put("uf", notaTO.getCliEndUFEstado());
        cliEndereco.put("cidade", notaTO.getCliEndCidade());
        cliEndereco.put("logradouro", notaTO.getCliEndLogradouro());
        cliEndereco.put("numero", notaTO.getCliEndNumero());
        cliEndereco.put("complemento", notaTO.getCliEndComplemento());
        cliEndereco.put("bairro", notaTO.getCliEndBairro());
        cliEndereco.put("cep", Uteis.tirarCaracteres(notaTO.getCliEndCEP(), true));
        cliente.put("endereco", cliEndereco);
        json.put("cliente", cliente);

        JSONArray itens = new JSONArray();
        for (NotaProdutoTO notaProdutoTO : notaTO.getNotaProdutos()) {
            JSONObject prod = new JSONObject();
            prod.put("cfop", notaProdutoTO.getCfop());
            prod.put("codigo", notaProdutoTO.getProdutoVO().getCodigo().toString());

            String descricaoProduto = notaTO.getConfiguracaoNotaFiscalVO() != null && notaTO.getConfiguracaoNotaFiscalVO().getDescricaoServico() != null && !notaTO.getConfiguracaoNotaFiscalVO().getDescricaoServico().isEmpty() ?
                    notaTO.getConfiguracaoNotaFiscalVO().getDescricaoServico() : notaProdutoTO.getDescricao();
            prod.put("descricao", descricaoProduto);
            prod.put("ncm", notaProdutoTO.getNcm());
            prod.put("quantidade", notaProdutoTO.getQuantidade());
            prod.put("unidadeMedida", notaProdutoTO.getProdutoVO().getUnidadeMedida());
            prod.put("valorUnitario", notaProdutoTO.getValorUnitario());

            JSONObject impostos = new JSONObject();

            if (notaProdutoTO.getProdutoVO().isProdutoEstoque()) {
                JSONObject icms = new JSONObject();
                icms.put("situacaoTributaria", notaProdutoTO.getProdutoVO().getSituacaoTributariaICMS());

                if (notaProdutoTO.getProdutoVO().isEnviaAliquotaNFeICMS()) {
                    icms.put("aliquota", notaProdutoTO.getProdutoVO().getAliquotaICMS());
                }

                impostos.put("icms", icms);
            } else {
                JSONObject issqn = new JSONObject();
                issqn.put("itemListaServicoLC116", notaProdutoTO.getProdutoVO().getCodigoListaServico());
                issqn.put("aliquotaIss", notaProdutoTO.getProdutoVO().getAliquotaISSQN());
                impostos.put("issqn", issqn);
            }

            JSONObject pis = new JSONObject();
            pis.put("situacaoTributaria", notaProdutoTO.getProdutoVO().getSituacaoTributariaPIS());

            if (notaProdutoTO.getProdutoVO().isEnviaAliquotaNFePIS()) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("aliquota", notaProdutoTO.getProdutoVO().getAliquotaPIS());
                pis.put("porAliquota", jsonObject);
            }

            impostos.put("pis", pis);


            JSONObject cofins = new JSONObject();
            cofins.put("situacaoTributaria", notaProdutoTO.getProdutoVO().getSituacaoTributariaCOFINS());

            if (notaProdutoTO.getProdutoVO().isEnviaAliquotaNFeCOFINS()) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("aliquota", notaProdutoTO.getProdutoVO().getAliquotaCOFINS());
                cofins.put("porAliquota", jsonObject);
            }

            impostos.put("cofins", cofins);

            prod.put("impostos", impostos);

            itens.put(prod);
        }

        json.put("itens", itens);

        if (notaTO.isEmitirDuplicata()) {
            JSONObject cobranca = new JSONObject();
            JSONObject fatura = new JSONObject();

            fatura.put("numero", "FAT "+Calendario.hoje().getTime());
            fatura.put("desconto", 0.0);
            fatura.put("valorOriginal", notaTO.getNotaValor());

            List<JSONObject> listParcelas = new ArrayList<>();
            Ordenacao.ordenarLista(notaTO.getNotaPagamentos(), "numeroFormatado");
            for (NotaPagamentoTO fat : notaTO.getNotaPagamentos()) {
                JSONObject dup = new JSONObject();
                dup.put("numero", fat.getNumeroFormatado());
                dup.put("valor", fat.getValor());
                dup.put("vencimento", (fat.getDataVencimento() != null) ? fat.getDataVencimento().toInstant().truncatedTo(ChronoUnit.SECONDS).toString() : null);
                listParcelas.add(dup);
            }
            cobranca.put("fatura", fatura);
            JSONArray parcelas = new JSONArray(listParcelas);
            cobranca.put("parcelas", parcelas);
            json.put("cobranca", cobranca);
        }

        return json;
    }
}
