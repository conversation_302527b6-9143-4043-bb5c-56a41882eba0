package br.com.pactosolucoes.integracao.enotas.to;

import negocio.comuns.arquitetura.SuperTO;
import org.json.JSONObject;

public class InfoEmpresaEnotasTO extends SuperTO {

    private String id;
    private String status;
    private String uf;
    private String cidade;
    private Integer prazo;
    private boolean dadosObrigatoriosPreenchidos = false;
    private String json;

    public InfoEmpresaEnotasTO() {

    }

    public InfoEmpresaEnotasTO(JSONObject jsonObject) {
        this.json = jsonObject.toString();

        try {
            this.id = jsonObject.getString("id");
        } catch (Exception ignored) {
        }

        try {
            this.status = jsonObject.getString("status");
        } catch (Exception ignored) {
        }

        try {
            JSONObject jsonEnd = jsonObject.getJSONObject("endereco");
            this.uf = jsonEnd.getString("uf");
            this.cidade = jsonEnd.getString("cidade");
        } catch (Exception ignored) {
        }

        try {
            this.prazo = jsonObject.getInt("prazo");
        } catch (Exception ignored) {
        }

        try {
            this.dadosObrigatoriosPreenchidos = jsonObject.getBoolean("dadosObrigatoriosPreenchidos");
        } catch (Exception ignored) {
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getPrazo() {
        if (prazo == null) {
            prazo = 0;
        }
        return prazo;
    }

    public void setPrazo(Integer prazo) {
        this.prazo = prazo;
    }

    public String getDadosObrigatoriosPreenchidosApresentar() {
        return isDadosObrigatoriosPreenchidos() ? "SIM" : "NÃO";
    }

    public boolean isDadosObrigatoriosPreenchidos() {
        return dadosObrigatoriosPreenchidos;
    }

    public void setDadosObrigatoriosPreenchidos(boolean dadosObrigatoriosPreenchidos) {
        this.dadosObrigatoriosPreenchidos = dadosObrigatoriosPreenchidos;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getUf() {
        return uf;
    }

    public void setUf(String uf) {
        this.uf = uf;
    }
}
