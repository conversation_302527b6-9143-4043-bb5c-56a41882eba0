package br.com.pactosolucoes.integracao.enotas;

import br.com.pactosolucoes.integracao.enotas.to.NotaEnotasTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class CorrecaoTaxaPersonalRunner {
    private static void atualizarValorProdutoTaxaPersonal(Connection con, ResultSet resultSet) throws Exception {
        try {
            con.setAutoCommit(false);

            while (resultSet.next()) {

                String produtosPagos = resultSet.getString("produtospagos");
                String produtosPagosCorrigidos = "";
                String[] movprodutos = produtosPagos.split("\\|", -1);
                Integer codigoMovPgamento = resultSet.getInt("codigo");
                for(String movproduto: movprodutos){
                    if(movproduto.isEmpty()){
                        continue;
                    }
                    String[] atributosMovProduto = movproduto.split(",");
                    String movproduto_codigo = atributosMovProduto[0];
                    String movproduto_tipo = atributosMovProduto[1];
                    String codigo = atributosMovProduto[2];
                    String movproduto_valor = atributosMovProduto[3];

                    String movprodutoCorrigido = null;

                    Double valor = Double.parseDouble(movproduto_valor);
                    if(valor.equals(0.0)){
                        ResultSet resultSetMovProduto =  SuperFacadeJDBC.criarConsulta("SELECT * FROM movproduto where codigo = "+movproduto_codigo, con);
                        if(resultSetMovProduto.next()){
                            Double valorProduto = resultSetMovProduto.getDouble("totalfinal");
                            movproduto_valor = valorProduto.toString();

                            movprodutoCorrigido = movproduto_codigo+","+movproduto_tipo+","+codigo+","+movproduto_valor;
                        }
                    }

                    if(movprodutoCorrigido != null){
                        produtosPagosCorrigidos += "|"+movprodutoCorrigido;
                    }

                }

                if(!produtosPagosCorrigidos.isEmpty()){
                    String sql = "UPDATE movpagamento SET produtospagos = '"+produtosPagosCorrigidos+"'  WHERE codigo = "+codigoMovPgamento;
                    PreparedStatement update = con.prepareStatement(sql);
                    update.execute();
                    Uteis.logar("Corrigido o valor de produtos pago do movpagamento "+codigoMovPgamento);
                }
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public static void main(String... args) {
        try {
            Uteis.debug = true;
            for (String chave : args) {
                Uteis.logar(null, "Obter conexão para chave: " + chave);

                Connection con = null;
                try {
                    con = new DAO().obterConexaoEspecifica(chave);
                    String sql = "SELECT * FROM " +
                            "movpagamento " +
                            "where produtospagos like '%,TP,%' " +
                            "and produtospagos like '%,0.0%' order by codigo";
                    try (ResultSet resultSetMovPagamento = SuperFacadeJDBC.criarConsulta(sql, con)) {
                        atualizarValorProdutoTaxaPersonal(con, resultSetMovPagamento);
                    } catch (Exception ex) {
                        Logger.getLogger(CorrecaoTaxaPersonalRunner.class.getName()).log(Level.SEVERE, null, ex);
                    }
                } catch (Exception ex) {
                    Logger.getLogger(CorrecaoTaxaPersonalRunner.class.getName()).log(Level.SEVERE, null, ex);
                } finally {
                    try {
                        con.close();
                    } catch (SQLException e) {
                        e.printStackTrace();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
