package br.com.pactosolucoes.integracao.aragorn;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.apache.http.HttpStatus;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class ImportarBoletosAPIPjBank {

    public static void main(String[] args) {
        Connection con = null;
        try {
            Uteis.debug = true;
            Uteis.logar(true, null, "ImportarBoletosAPIPjBank | INICIO...");

            con = DriverManager.getConnection("****************************************************************", "postgres", "pactodb");
            String pjBank_Chave = "f57c570b4a6a53c54a96e248e3e104cd6f0db9d0";
            String pjBank_Credencial = "135a7cc5a1ebdc26bcdc77d54beae4de4cfcdb8b";
            Date dtInicio = Calendario.getDate("dd/MM/yyyy", "01/02/2023");
            Date dtFim = Calendario.getDate("dd/MM/yyyy", "28/02/2023");

            processar(pjBank_Chave, pjBank_Credencial, dtInicio, dtFim, con);
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(true, null, "ImportarBoletosAPIPjBank | ERRO: " + ex.getMessage());
        } finally {
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            Uteis.logar(true, null, "ImportarBoletosAPIPjBank | FIM...");
        }
    }

    public static void processar(String pjBank_Chave, String pjBank_Credencial,
                                 Date dtInicio, Date dtFim, Connection con) {
        try {
            criarTabela(con);

            Integer pagina = 0;
            boolean processar = true;
            while (processar) {
                try {
                    Uteis.logarDebug("Consultar pagina " + ++pagina);

                    String url = "https://api.pjbank.com.br/recebimentos/" + pjBank_Credencial + "/transacoes";
                    Map<String, String> params = new HashMap<>();
                    params.put("data_inicio", Calendario.getDataAplicandoFormatacao(dtInicio, "MM/dd/yyyy"));
                    params.put("data_fim", Calendario.getDataAplicandoFormatacao(dtFim, "MM/dd/yyyy"));
                    params.put("pagina", String.valueOf(pagina));
                    Map<String, String> header = new HashMap<>();
                    header.put("x-chave", pjBank_Chave);
                    header.put("Content-Type", "application/json");

                    RespostaHttpDTO respostaHttpDTO = executarRequest(url, header, params, pagina);
                    JSONArray array = new JSONArray(respostaHttpDTO.getResponse());

                    Uteis.logarDebug("Pagina: " + pagina + " | Registros: " + array.length());

                    if (array.length() == 0) {
                        processar = false;
                    }
                    for (int e = 0; e < array.length(); e++) {
                        try {
                            JSONObject obj = array.getJSONObject(e);
                            String id_unico = obj.optString("id_unico");
                            String pagador = obj.optString("pagador");
                            Double valor = getValor(obj.getString("valor"));
                            String pedido_numero = obj.optString("pedido_numero");
                            String vencimentoStr = obj.optString("data_vencimento");
                            Date vencimento = null;
                            try {
                                vencimento = Calendario.getDate("MM/dd/yyyy", vencimentoStr);
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                            incluir(id_unico, pedido_numero, pagador, valor, vencimento, obj.toString(), con);

                            if (id_unico.equals("207572724") ||
                                    pagador.toUpperCase().contains("RODRIGO VIEIRA RAMOS")) {
                                System.out.println("###########");
                                System.out.println(obj);
                                System.out.println("###########");
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }

                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static RespostaHttpDTO executarRequest(String url, Map<String, String> header, Map<String, String> params, Integer pagina) throws Exception {
        RequestHttpService service = new RequestHttpService();
        int tentativa = 0;
        while (++tentativa <= 10) {
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, header, params, null, MetodoHttpEnum.GET);
            if (respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
                return respostaHttpDTO;
            } else {
                Thread.sleep(2000);
                Uteis.logarDebug("Erro requisição | tentar novamente " + tentativa + " | Pagina  " + pagina + " | " + respostaHttpDTO.getHttpStatus() + " | " + respostaHttpDTO.getResponse());
            }
        }
        throw new Exception("Erro fazer requisição");
    }

    private static Double getValor(String valor) {
        if (UteisValidacao.emptyString(valor)) {
            return 0.0;
        } else {
            return Double.valueOf(valor);
        }
    }

    private static void criarTabela(Connection con) {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE importacaopjbank ( \n");
        sql.append("codigo serial PRIMARY KEY, \n");
        sql.append("dataregistro TIMESTAMP WITHOUT TIME ZONE, \n");
        sql.append("id_unico varchar, \n");
        sql.append("pedido_numero varchar, \n");
        sql.append("pagador varchar, \n");
        sql.append("valor double precision, \n");
        sql.append("vencimento date, \n");
        sql.append("dados text) \n");
        SuperFacadeJDBC.executarUpdateExecutarProcessos(sql.toString(), con);
        SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_importacaopjbank_id_unico ON importacaopjbank USING btree (id_unico);", con);
    }

    private static void incluir(String id_unico, String pedido_numero, String pagador, Double valor,
                                Date vencimento, String dados, Connection con) throws SQLException {
        String sql = "INSERT INTO importacaopjbank(dataregistro, id_unico, pedido_numero, pagador, valor, vencimento, dados) VALUES (?,?,?,?,?,?,?);";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            int i = 0;
            pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            pst.setString(++i, id_unico);
            pst.setString(++i, pedido_numero);
            pst.setString(++i, pagador);
            pst.setDouble(++i, valor);
            pst.setDate(++i, Uteis.getDataJDBC(vencimento));
            pst.setString(++i, dados);
            pst.execute();
        }
    }
}
