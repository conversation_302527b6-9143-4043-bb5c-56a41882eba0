/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.protheus;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class EnvioProtheusJSON extends SuperJSON implements Serializable {
    
    private Integer codigo;
    private String entidade;
    private String chaveEmpresa;
    private String msErro;
    private String codigoRetorno;
    private String dadosEnviados;
    private String chaveAutenticacao;
    private Date horaEnvio;
    private StatusEnvioEnum status;

    public String getChaveAutenticacao() {
        return chaveAutenticacao;
    }

    public void setChaveAutenticacao(String chaveAutenticacao) {
        this.chaveAutenticacao = chaveAutenticacao;
    }
    
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getEntidade() {
        return entidade;
    }

    public void setEntidade(String entidade) {
        this.entidade = entidade;
    }

    public String getChaveEmpresa() {
        return chaveEmpresa;
    }

    public void setChaveEmpresa(String chaveEmpresa) {
        this.chaveEmpresa = chaveEmpresa;
    }

    public String getDadosEnviados() {
        return dadosEnviados;
    }

    public void setDadosEnviados(String dadosEnviados) {
        this.dadosEnviados = dadosEnviados;
    }

    public Date getHoraEnvio() {
        return horaEnvio;
    }

    public void setHoraEnvio(Date horaEnvio) {
        this.horaEnvio = horaEnvio;
    }

    public String getMsErro() {
        return msErro;
    }

    public void setMsErro(String msErro) {
        this.msErro = msErro;
    }

    public String getCodigoRetorno() {
        return codigoRetorno;
    }

    public void setCodigoRetorno(String codigoRetorno) {
        this.codigoRetorno = codigoRetorno;
    }

    public StatusEnvioEnum getStatus() {
        return status;
    }

    public void setStatus(StatusEnvioEnum status) {
        this.status = status;
    }
    
    
    
}
