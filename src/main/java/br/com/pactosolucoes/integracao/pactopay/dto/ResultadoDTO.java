package br.com.pactosolucoes.integracao.pactopay.dto;


import java.util.List;

public class ResultadoDTO {

    private String chave;
    private Integer operacao;
    private RemessaResultadoDTO remessa;
    private ConvenioResultadoDTO convenio;
    private EmpresaResultadoDTO empresa;
    private TransacaoResultadoDTO transacaoResultado;
    private List<TransacaoResultadoDTO> remessaResultado;

    public ResultadoDTO() {

    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public RemessaResultadoDTO getRemessa() {
        return remessa;
    }

    public void setRemessa(RemessaResultadoDTO remessa) {
        this.remessa = remessa;
    }

    public ConvenioResultadoDTO getConvenio() {
        return convenio;
    }

    public void setConvenio(ConvenioResultadoDTO convenio) {
        this.convenio = convenio;
    }

    public EmpresaResultadoDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaResultadoDTO empresa) {
        this.empresa = empresa;
    }

    public Integer getOperacao() {
        return operacao;
    }

    public void setOperacao(Integer operacao) {
        this.operacao = operacao;
    }

    public TransacaoResultadoDTO getTransacaoResultado() {
        return transacaoResultado;
    }

    public void setTransacaoResultado(TransacaoResultadoDTO transacaoResultado) {
        this.transacaoResultado = transacaoResultado;
    }

    public List<TransacaoResultadoDTO> getRemessaResultado() {
        return remessaResultado;
    }

    public void setRemessaResultado(List<TransacaoResultadoDTO> remessaResultado) {
        this.remessaResultado = remessaResultado;
    }
}
