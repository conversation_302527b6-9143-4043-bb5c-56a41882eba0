package br.com.pactosolucoes.integracao.pactopay.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConvenioDTO extends SuperTO {

    private String id;
    private String chave;
    private Integer idReferencia;
    private String descricao;
    private String numeroContrato;
    private String numeroLogico;
    private SftpDTO sftpConfig;
    private String senhaDescriptografiaArquivo;
    private String chaveDescriptografiaArquivo;
    private String chaveCriptografiaArquivo;
    private Integer tipo;
    private String dataRegistro;
    private ContaCorrenteDTO contaCorrente;
    private Integer ambiente;
    private String sequencialArquivoCancelamento;

    //Cielo online
    private String merchantId;
    private String merchantKey;
    //Rede online
    private String numeroFiliacao;
    private String token;
    //Getnet online
    private String clientId;
    private String clientSecret;
    private String sellerId;
    //Stone online
    private String stoneCode;
    private String saleAffiliationKey;
    //Vindi online
    private String chavePrivada;
    private String chavePublica;
    //PagarMe
    private String chaveAPI;
    private String chaveCriptografia;
    //Pagbank
    private String tokenAPI;
    //Stripe
    private String secretKey;
    //Mundipagg
    //tbm utiliza chavePublica
    private String chaveSecreta;
    //PinBank
    private String userName;
    private String keyValue;
    private String grantType;
    private String requestOrigin;
    private String codigoCanal;
    private String codigoCliente;
    private String keyLoja;
    //One Payment
    private String privateSecretKey;

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    private String metadata;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getIdReferencia() {
        return idReferencia;
    }

    public void setIdReferencia(Integer idReferencia) {
        this.idReferencia = idReferencia;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public SftpDTO getSftpConfig() {
        return sftpConfig;
    }

    public void setSftpConfig(SftpDTO sftpConfig) {
        this.sftpConfig = sftpConfig;
    }

    public String getSenhaDescriptografiaArquivo() {
        return senhaDescriptografiaArquivo;
    }

    public void setSenhaDescriptografiaArquivo(String senhaDescriptografiaArquivo) {
        this.senhaDescriptografiaArquivo = senhaDescriptografiaArquivo;
    }

    public String getChaveDescriptografiaArquivo() {
        return chaveDescriptografiaArquivo;
    }

    public void setChaveDescriptografiaArquivo(String chaveDescriptografiaArquivo) {
        this.chaveDescriptografiaArquivo = chaveDescriptografiaArquivo;
    }

    public String getChaveCriptografiaArquivo() {
        return chaveCriptografiaArquivo;
    }

    public void setChaveCriptografiaArquivo(String chaveCriptografiaArquivo) {
        this.chaveCriptografiaArquivo = chaveCriptografiaArquivo;
    }

    public String getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(String dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getNumeroContrato() {
        return numeroContrato;
    }

    public void setNumeroContrato(String numeroContrato) {
        this.numeroContrato = numeroContrato;
    }

    public ContaCorrenteDTO getContaCorrente() {
        return contaCorrente;
    }

    public void setContaCorrente(ContaCorrenteDTO contaCorrente) {
        this.contaCorrente = contaCorrente;
    }

    public Integer getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Integer ambiente) {
        this.ambiente = ambiente;
    }

    public Integer getTipo() {
        return tipo;
    }

    public void setTipo(Integer tipo) {
        this.tipo = tipo;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantKey() {
        return merchantKey;
    }

    public void setMerchantKey(String merchantKey) {
        this.merchantKey = merchantKey;
    }

    public String getNumeroLogico() {
        return numeroLogico;
    }

    public void setNumeroLogico(String numeroLogico) {
        this.numeroLogico = numeroLogico;
    }

    public String getNumeroFiliacao() {
        return numeroFiliacao;
    }

    public void setNumeroFiliacao(String numeroFiliacao) {
        this.numeroFiliacao = numeroFiliacao;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getSequencialArquivoCancelamento() {
        return sequencialArquivoCancelamento;
    }

    public void setSequencialArquivoCancelamento(String sequencialArquivoCancelamento) {
        this.sequencialArquivoCancelamento = sequencialArquivoCancelamento;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public String getSaleAffiliationKey() {
        return saleAffiliationKey;
    }

    public void setSaleAffiliationKey(String saleAffiliationKey) {
        this.saleAffiliationKey = saleAffiliationKey;
    }

    public String getStoneCode() {
        return stoneCode;
    }

    public void setStoneCode(String stoneCode) {
        this.stoneCode = stoneCode;
    }

    public String getChavePrivada() {
        return chavePrivada;
    }

    public void setChavePrivada(String chavePrivada) {
        this.chavePrivada = chavePrivada;
    }

    public String getChavePublica() {
        return chavePublica;
    }

    public void setChavePublica(String chavePublica) {
        this.chavePublica = chavePublica;
    }

    public String getChaveAPI() {
        return chaveAPI;
    }

    public void setChaveAPI(String chaveAPI) {
        this.chaveAPI = chaveAPI;
    }

    public String getChaveCriptografia() {
        return chaveCriptografia;
    }

    public void setChaveCriptografia(String chaveCriptografia) {
        this.chaveCriptografia = chaveCriptografia;
    }

    public String getChaveSecreta() {
        return chaveSecreta;
    }

    public void setChaveSecreta(String chaveSecreta) {
        this.chaveSecreta = chaveSecreta;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getUserName() { return userName; }

    public void setUserName(String userName) { this.userName = userName; }

    public String getKeyValue() { return keyValue; }

    public void setKeyValue(String keyValue) { this.keyValue = keyValue; }

    public String getGrantType() { return grantType; }

    public void setGrantType(String grantType) { this.grantType = grantType; }

    public String getRequestOrigin() { return requestOrigin; }

    public void setRequestOrigin(String requestOrigin) { this.requestOrigin = requestOrigin; }

    public String getCodigoCanal() { return codigoCanal; }

    public void setCodigoCanal(String codigoCanal) { this.codigoCanal = codigoCanal; }

    public String getCodigoCliente() { return codigoCliente; }

    public void setCodigoCliente(String codigoCliente) { this.codigoCliente = codigoCliente; }

    public String getKeyLoja() { return keyLoja; }

    public void setKeyLoja(String keyLoja) { this.keyLoja = keyLoja; }

    public String getPrivateSecretKey() {
        return privateSecretKey;
    }

    public void setPrivateSecretKey(String privateSecretKey) {
        this.privateSecretKey = privateSecretKey;
    }

    public String getTokenAPI() {
        return tokenAPI;
    }

    public void setTokenAPI(String tokenAPI) {
        this.tokenAPI = tokenAPI;
    }
}
