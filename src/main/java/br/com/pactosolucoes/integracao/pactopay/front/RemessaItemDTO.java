package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.utilitarias.UteisValidacao;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class RemessaItemDTO extends SuperTO {

    private Integer codigo;
    private Integer remessa;
    private String identificador;
    private String nome;
    private Integer pessoa;
    private String cartao;
    private Integer status;
    private Integer statusCodigo;
    private String statusDescricao;
    private String bandeira;
    private String autorizacao;
    private String usuario;
    private String codigoRetorno;
    private String codigoRetornoDescricao;
    private Integer qtdParcelas;
    private Double valor;

    public RemessaItemDTO() {

    }

    public RemessaItemDTO(RemessaItemVO itemVO) {
        this.codigo = itemVO.getCodigo();
        this.remessa = itemVO.getRemessa().getCodigo();
        this.identificador = itemVO.getRemessa().getIdentificador();
        this.nome = itemVO.getPessoa().getNome();
        this.pessoa = itemVO.getPessoa().getCodigo();
        this.cartao = itemVO.getValorCartaoMascaradoOuAgenciaConta();
        this.autorizacao = itemVO.getAutorizacao();
        this.bandeira = itemVO.getBandeira();
        this.usuario = itemVO.getRemessa().getUsuario().getNome();
        this.codigoRetorno = itemVO.getCodigoStatus();
        this.codigoRetornoDescricao = itemVO.getDescricaoStatus();
        this.valor = itemVO.getValorItemRemessa();

        this.status = itemVO.getRemessa().getSituacaoRemessa().getId();
        this.statusCodigo = itemVO.getCodigoStatusPactoPay();
//        this.statusDescricao = itemVO.getRemessa().getSituacaoRemessa().getDescricao();

        if (itemVO.getMovPagamento() != null &&
                !UteisValidacao.emptyNumber(itemVO.getMovPagamento().getCodigo())) {
            this.statusDescricao = "PAGO";
        } else if (itemVO.getRemessa().getSituacaoRemessa() != null  &&
                itemVO.getRemessa().getSituacaoRemessa().equals(SituacaoRemessaEnum.REMESSA_ENVIADA) &&
                (this.codigoRetorno != null && this.codigoRetorno.toUpperCase().contains("NENHUM"))) {
            this.statusDescricao = "PENDENTE";
        } else {
            this.statusDescricao = "ERRO";
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getCartao() {
        return cartao;
    }

    public void setCartao(String cartao) {
        this.cartao = cartao;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusDescricao() {
        return statusDescricao;
    }

    public void setStatusDescricao(String statusDescricao) {
        this.statusDescricao = statusDescricao;
    }

    public Integer getQtdParcelas() {
        return qtdParcelas;
    }

    public void setQtdParcelas(Integer qtdParcelas) {
        this.qtdParcelas = qtdParcelas;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getCodigoRetorno() {
        return codigoRetorno;
    }

    public void setCodigoRetorno(String codigoRetorno) {
        this.codigoRetorno = codigoRetorno;
    }

    public String getCodigoRetornoDescricao() {
        return codigoRetornoDescricao;
    }

    public void setCodigoRetornoDescricao(String codigoRetornoDescricao) {
        this.codigoRetornoDescricao = codigoRetornoDescricao;
    }

    public String getAutorizacao() {
        return autorizacao;
    }

    public void setAutorizacao(String autorizacao) {
        this.autorizacao = autorizacao;
    }

    public String getBandeira() {
        return bandeira;
    }

    public void setBandeira(String bandeira) {
        this.bandeira = bandeira;
    }

    public Integer getRemessa() {
        return remessa;
    }

    public void setRemessa(Integer remessa) {
        this.remessa = remessa;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public Integer getStatusCodigo() {
        return statusCodigo;
    }

    public void setStatusCodigo(Integer statusCodigo) {
        this.statusCodigo = statusCodigo;
    }
}
