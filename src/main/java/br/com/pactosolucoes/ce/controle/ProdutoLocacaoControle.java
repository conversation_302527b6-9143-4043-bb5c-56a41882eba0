package br.com.pactosolucoes.ce.controle;

import java.util.ArrayList;
import java.util.List;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;

import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.utilitarias.ControleConsulta;
import br.com.pactosolucoes.ce.comuns.enumerador.TipoProdutoLocacao;
import br.com.pactosolucoes.ce.comuns.ex.NegocioException;
import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.ce.comuns.to.ProdutoLocacaoPatrimonioTO;
import br.com.pactosolucoes.ce.comuns.to.ProdutoLocacaoRastreamentoTO;
import br.com.pactosolucoes.ce.comuns.to.ProdutoLocacaoTO;
import br.com.pactosolucoes.comuns.util.JSFUtilities;

/**
 * Classe responsável por implementar a interação entre os componentes JSF da página produtoLocacao.jsp.
 * 
 * <AUTHOR>
 * 
 */
public class ProdutoLocacaoControle extends CEControle {

	private ProdutoLocacaoTO produto = new ProdutoLocacaoTO();
	private ProdutoLocacaoPatrimonioTO patrimonio = new ProdutoLocacaoPatrimonioTO();
	private ProdutoLocacaoPatrimonioTO patrimonioAAlterar = new ProdutoLocacaoPatrimonioTO();
	private List<String> patrimoniosAExcluir;
	private ProdutoLocacaoRastreamentoTO rastreamento = new ProdutoLocacaoRastreamentoTO();
	private Boolean visualizarRastreamento;
	private List<ProdutoLocacaoTO> listaProdutos;
	private boolean edicaoPatrimonio;
	private int codigoTipo = 0;
	
	/* -- OBJETO PARA LOG -- */
	private ProdutoLocacaoTO objetoLog;
	private String msgAlert;

	/**
	 * Construtor inicia a lista, o objeto e a mensagem
	 * @throws Exception 
	 */
	public ProdutoLocacaoControle() throws Exception {
		
		this.setControleConsulta(new ControleConsulta());
		this.listaConsulta = new ArrayList<ProdutoLocacaoTO>();
		this.setMensagemID("parametros.informar");

	}

        public Boolean getBrinquedo(){
            return codigoTipo == TipoProdutoLocacao.BRINQUEDOS.getCodigo();
        }

        public void mudarTipo(){
            if(!getBrinquedo()){
                produto.setRastreado(false);
            }
        }
	/**
	 * @param edicaoPatrimonio
	 *            the edicaoPatrimonio to set
	 */
	public void setEdicaoPatrimonio(final boolean edicaoPatrimonio) {
		this.edicaoPatrimonio = edicaoPatrimonio;
	}

	/**
	 * @return the edicaoPatrimonio
	 */
	public boolean isEdicaoPatrimonio() {
		return this.edicaoPatrimonio;
	}

	/**
	 * @param patrimonio
	 *            the patrimonio to set
	 */
	public void setPatrimonio(final ProdutoLocacaoPatrimonioTO patrimonio) {
		this.patrimonio = patrimonio;
	}

	/**
	 * @return the patrimonio
	 */
	public ProdutoLocacaoPatrimonioTO getPatrimonio() {
		if (this.patrimonio == null) {
			this.patrimonio = new ProdutoLocacaoPatrimonioTO();
		}
		return this.patrimonio;
	}

	/**
	 * @return O campo patrimonioAAlterar.
	 */
	public ProdutoLocacaoPatrimonioTO getPatrimonioAAlterar() {
		if (this.patrimonioAAlterar == null) {
			this.patrimonioAAlterar = new ProdutoLocacaoPatrimonioTO();
		}
		return this.patrimonioAAlterar;
	}

	/**
	 * @param patrimonioAAlterar
	 *            O novo valor de patrimonioAAlterar.
	 */
	public void setPatrimonioAAlterar(final ProdutoLocacaoPatrimonioTO patrimonioAAlterar) {
		this.patrimonioAAlterar = patrimonioAAlterar;
	}

	/**
	 * @return O campo patrimoniosAExcluir.
	 */
	public List<String> getPatrimoniosAExcluir() {
		if (this.patrimoniosAExcluir == null) {
			this.patrimoniosAExcluir = new ArrayList<String>();
		}
		return this.patrimoniosAExcluir;
	}

	/**
	 * @param patrimoniosAExcluir
	 *            O novo valor de patrimoniosAExcluir.
	 */
	public void setPatrimoniosAExcluir(final List<String> patrimoniosAExcluir) {
		this.patrimoniosAExcluir = patrimoniosAExcluir;
	}

	/**
	 * @param rastreamento
	 *            the rastreamento to set
	 */
	public void setRastreamento(final ProdutoLocacaoRastreamentoTO rastreamento) {
		this.rastreamento = rastreamento;
	}

	/**
	 * @return the rastreamento
	 */
	public ProdutoLocacaoRastreamentoTO getRastreamento() {
		return this.rastreamento;
	}

	/**
	 * @return O campo visualizarRastreamento.
	 */
	public Boolean getVisualizarRastreamento() {
		if (this.visualizarRastreamento == null) {
			this.visualizarRastreamento = Boolean.FALSE;
		}
		return this.visualizarRastreamento;
	}

	/**
	 * @param visualizarRastreamento
	 *            O novo valor de visualizarRastreamento.
	 */
	public void setVisualizarRastreamento(final Boolean visualizarRastreamento) {
		this.visualizarRastreamento = visualizarRastreamento;
	}

	/**
	 * @return the produto
	 */
	public ProdutoLocacaoTO getProduto() {
		return this.produto;
	}

	/**
	 * @return the codigoTipo
	 */
	public int getCodigoTipo() {
		return this.codigoTipo;
	}

	/**
	 * @param codigoTipo
	 *            the codigoTipo to set
	 */
	public void setCodigoTipo(final int codigoTipo) {
		this.codigoTipo = codigoTipo;
	}

	/**
	 * @param produto
	 *            the produto to set
	 */
	public void setProduto(final ProdutoLocacaoTO produto) {
		this.produto = produto;
	}

	/**
	 * @return the listaProdutos
	 */
	public List<ProdutoLocacaoTO> getListaProdutos() {
		return this.listaProdutos;
	}

	/**
	 * @param objetoLog the objetoLog to set
	 */
	public void setObjetoLog(ProdutoLocacaoTO objetoLog) {
		this.objetoLog = objetoLog;
	}

	/**
	 * @return the objetoLog
	 */
	public ProdutoLocacaoTO getObjetoLog() {
		if(objetoLog == null){
			objetoLog = new ProdutoLocacaoTO();
		}
		return objetoLog;
	}

	/**
	 * @param listaProdutos
	 *            the listaProdutos to set
	 */
	public void setListaProdutos(final List<ProdutoLocacaoTO> listaProdutos) {
		this.listaProdutos = listaProdutos;
	}

	/**
	 * Rotina responsável por verificar se a lista de produtos é nula
	 * 
	 * @return
	 */
	@Override
	public boolean getApresentarResultadoConsulta() {
		return !(this.getListaProdutos() == null);
	}

	/**
	 * Navega até a página inicial da datatable com os dados consultados
	 * 
	 * @throws Exception
	 */
	public void irPaginaInicial() throws Exception {
		this.controleConsulta.setPaginaAtual(1);
		this.consultar();
	}

	/**
	 * Navega até a página anterior da datatable com os dados consultados
	 * 
	 * @throws Exception
	 */
	public void irPaginaAnterior() throws Exception {
		this.controleConsulta.setPaginaAtual(this.controleConsulta.getPaginaAtual() - 1);
		this.consultar();
	}

	/**
	 * Navega até a página posterior da datatable com os dados consultados
	 * 
	 * @throws Exception
	 */
	public void irPaginaPosterior() throws Exception {
		this.controleConsulta.setPaginaAtual(this.controleConsulta.getPaginaAtual() + 1);
		this.consultar();
	}

	/**
	 * Navega até a página final da datatable com os dados consultados
	 * 
	 * @throws Exception
	 */
	public void irPaginaFinal() throws Exception {
		this.controleConsulta.setPaginaAtual(this.controleConsulta.getNrTotalPaginas());
		this.consultar();
	}

	/**
	 * Método que retorna uma lista de tipos de produtos de locação, para o selectOneMenu da jsp
	 * 
	 * @return
	 * @throws Exception
	 */
	public List<SelectItem> getListaTipos() throws Exception {
		final List<SelectItem> itens = new ArrayList<SelectItem>();

		for (TipoProdutoLocacao tipo : TipoProdutoLocacao.values()) {
			itens.add(new SelectItem(tipo.getCodigo(), tipo.getDescricao().toString()));
		}
		// retorna a lista
		return itens;
	}

	/**
	 * Lista os tipos de ambientes
	 */
	public void listar() {
		try {
			this.verificarAutorizacao();
			if(this.getCodigoTipo() != 0){
				this.getProduto().setTipo(TipoProdutoLocacao.getTipoProdutoLocacao(this.getCodigoTipo()));	
			}else{
				this.getProduto().setTipo(null);
			}
			
			final List<ProdutoLocacaoTO> objs = CEControle.getCEFacade().consultarProdutoLocacao(this.getProduto());
			// caso a lista tenha pelo menos um resultado
			if (objs.isEmpty()) {
				// mostrar as mensagens de nenhum resultado
				this.setMensagemID("operacoes.consulta.nenhumResultado");
				this.setSucesso(true);
				this.setErro(false);
				// limpar a lista
				this.setListaProdutos(new ArrayList<ProdutoLocacaoTO>());
			} else {
				// setar a lista com o resultado da consulta
				this.setListaProdutos(objs);
				// mostrar as mensagens de sucesso
				this.setMensagemID("operacoes.consulta.sucesso");
				this.setSucesso(true);
				this.setErro(false);
				// caso não tenham resultados
			}

			// se houverem erros na operação
		} catch (Exception e) {
			// mostrar as mensagens de erro
			Exception exTratada = this.tratarMensagemExcecao(e);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(exTratada.getMessage());
			this.setSucesso(false);
			this.setErro(true);
			this.setListaProdutos(new ArrayList<ProdutoLocacaoTO>());
		}
	}
	
	/*
	 * (non-Javadoc)
	 * 
	 * @see controle.arquitetura.SuperControle#consultar()
	 */
	@Override
	@SuppressWarnings("unchecked")
	public String consultar() throws Exception {
		//verifica autorização
		super.verificarAutorizacao();
		String result;
		try {
			super.consultar();
			// insere no objeto de ProdutoLocacaoTO o tipo selecionado na jsp
			this.getProduto().setTipo(TipoProdutoLocacao.getTipoProdutoLocacao(this.codigoTipo));
			// preenche uma lista com os resultados pesquisados
			List<ProdutoLocacaoTO> objs = CEControle.getCEFacade().consultarProdutoLocacao(this.getProduto());
			objs = ControleConsulta.obterSubListPaginaApresentar(objs, this.controleConsulta);
			// se a lista conter pelo menos um resultado, define os links de navegação, apresenta e seta as mensagens de sucesso
			if (objs.size() > 0) {
				this.definirVisibilidadeLinksNavegacao(this.controleConsulta.getPaginaAtual(), this.controleConsulta.getNrTotalPaginas());
				this.setListaProdutos(objs);
				this.setMensagemID("operacoes.consulta.sucesso");
				this.setSucesso(true);
				this.setErro(false);
				// senão, seta mensagens acusando nenhum resultado obtido
			} else {
				this.setMensagemID("operacoes.consulta.nenhumResultado");
				this.setSucesso(true);
				this.setErro(false);
				this.setListaProdutos(new ArrayList<ProdutoLocacaoTO>());
			}
			result = "consulta";
			// se houverem erros na operação
		} catch (Exception e) {
			this.setSucesso(true);
			this.setErro(false);
			this.setListaProdutos(new ArrayList<ProdutoLocacaoTO>());
			result = "consulta";

		}
		return result;
	}

	/**
	 * Método que envia para a tela de alterar/gravar o objeto escolhido na datatable
	 * 
	 * @return
	 * @throws Exception
	 */
	public String editar() throws Exception {
		Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
		final ProdutoLocacaoTO obj = CEControle.getCEFacade().carregarProdutoLocacao(codigoConsulta);
		this.setProduto(obj);
		this.setObjetoLog(obj.clone());
		this.setCodigoTipo(this.getProduto().getTipo().getCodigo());
		this.setSucesso(true);
		this.setErro(false);
		this.setMensagemID("msg_dados_editar");
		return "editar";
	}

	/**
	 * Responsável por redirecionar para a página de cadastro de um novo produto de locação
	 * 
	 * @return
	 */
	public String novo() {
		this.limpaMensagem();
		this.setProduto(new ProdutoLocacaoTO());
		return "novo";
	}

	/**
	 * Responsável por limpar o objeto local, a lista, e voltar para a página de consulta
	 * 
	 * @return
	 */
	public String voltar() {
		this.limpaMensagem();
		this.setProduto(new ProdutoLocacaoTO());
		this.setListaProdutos(new ArrayList<ProdutoLocacaoTO>());
		return "consulta";
	}

	/**
	 * Método responsável por persistir os dados de produtoLocacaoForm.jsp
	 * 
	 * @return
	 * @throws Exception 
	 */
	public String gravar() throws Exception {
		//verifica autorização
		super.verificarAutorizacao();
		String result;
		// insere o tipo
		this.getProduto().setTipo(TipoProdutoLocacao.getTipoProdutoLocacao(this.codigoTipo));
		try {
			if (this.getProduto().getRastreado()) {
				if (this.getProduto().getEstoque() != this.getProduto().getPatrimonios().size()) {
					throw new ValidacaoException("parametros.numeroPatrimoniosEstoque");
				}
			}
			// manda o objeto para o método no Facade que persiste os dados
			CEControle.getCEFacade().salvarProdutoLocacao(this.produto, this.getPatrimoniosAExcluir());
			
			if (this.produto.getCodigo() == null || this.produto.getCodigo() == 0) {
				//LOG
	            registrarLogCE(this.produto, new ProdutoLocacaoTO(), this.produto.getCodigo(),"PRODUTO LOCACAO",true);
	        }else{
	        	//LOG
                registrarLogCE(this.produto, this.getObjetoLog(), this.produto.getCodigo(),"PRODUTO LOCACAO",false);
            }
			
			
			this.setSucesso(true);
			this.setErro(false);
			this.setMensagemID("msg_dados_gravados");
			this.limpaLista();
			result = "consulta";
		} catch (Exception e) {
			// se houverem erros na gravação
			this.setSucesso(false);
			this.setErro(true);
//			this.setMensagemDetalhada("msg_erro", e.getMessage());
			Exception exTratada = this.tratarMensagemExcecao(e);
			this.setMensagemID("msg_erro");
			this.setMensagemDetalhada(exTratada.getMessage());
			result = "editar";
		}
		return result;
	}

	/**
	 * Método responsável por excluir os dados do registro escolhido na tela de consulta
	 * 
	 * @return
	 * @throws Exception 
	 */
	public String excluir() throws Exception {
		//verifica autorização
		super.verificarAutorizacao();
		try {
			// Executa o método do facade reponsável pela exclusão, passando como parametro o objeto desse bean
			limparMsg();
			CEControle.getCEFacade().excluirProdutoLocacao(this.produto);
			//registrar log
			registrarLogExclusaoObjetoCE(this.produto, this.produto.getCodigo(), "PRODUTO LOCACAO");
			// após a exclusão, limpa o objeto
			this.setProduto(new ProdutoLocacaoTO());
			// seta as mensagens de sucesso
			this.setSucesso(true);
			this.setErro(false);
			this.setMensagemID("msg_dados_excluidos");
			this.limpaLista();
			// retorna para a tela de consulta
			novo();
			montarSucessoGrowl("Dados excluídos com sucesso!");
			redirect("/faces/pages/ce/cadastros/produtoLocacao.jsp?modulo=centralEventos");
			return "consultar";
		} catch (Exception e) {
			this.setSucesso(false);
			this.setErro(true);
			this.setMensagemDetalhada("msg_erro",tratarMensagemErroDetalhadaReferenciaChaveEstrangeira(e.getMessage()));
			return "editar";
		}
	}

	/**
	 * Rotina para limpar atributos do bean
	 */
	public void limpaMensagem() {

		this.definirVisibilidadeLinksNavegacao(0, 0);
		this.setSucesso(false);
		this.setErro(false);
		this.setCodigoTipo(0);
		this.setMensagem("");
		this.setMensagemDetalhada("");
		this.setMensagemID("");
	}

	/**
	 * Rotina para limpar a lista, a visiblidade dos links de navegação e o objeto de ProdutoLocacaoTO.
	 */
	public void limpaLista() {
		this.definirVisibilidadeLinksNavegacao(0, 0);
		this.setProduto(new ProdutoLocacaoTO());
		this.setListaProdutos(new ArrayList<ProdutoLocacaoTO>());
		this.setCodigoTipo(0);
	}

	/**
	 * Adiciona um objeto a lista que será salva
	 */
	public void adicionarPatrimonio() {
		try {
			if(this.getProduto().getEstoque()<=this.getProduto().getPatrimonios().size()){
				throw new ValidacaoException("parametros.numeroPatrimoniosEstoque");
			}
			// Verificar se o patrimônio já foi adicionado
			for (ProdutoLocacaoPatrimonioTO patrimonio : this.produto.getPatrimonios()) {
				if (this.getPatrimonio().getCodigo().equals(patrimonio.getCodigo())) {
					throw new NegocioException("operacoes.adicao.erro.patrimonioJaRelacionado");
				}
			}

			// adicionar patrimonio a lista que será salva
			this.produto.getPatrimonios().add(this.getPatrimonio());
			this.setPatrimonio(null);
			this.setMensagemID("operacoes.adicao.sucesso");
			this.setAtencao(true);
			this.setSucesso(false);
			this.setErro(false);

		} catch (Exception e) {
			// Tratar mensagem para obter mensagem correta no bundle de mensagens segundo chave contida na exceção
			final Exception exTratada = this.tratarMensagemExcecao(e);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(exTratada.getMessage());
			this.setSucesso(false);
			this.setAtencao(false);
			this.setErro(true);
		}
	}

	/**
	 * Responsável pela edição de patrimonios relacionadas ao produto.
	 */
	public void editarPatrimonio() {
		// Marcar edição de patrimonio
		this.setEdicaoPatrimonio(true);

		// Obter patrimonio a alterar
		this.setPatrimonioAAlterar((ProdutoLocacaoPatrimonioTO) JSFUtilities.getRequestAttribute("patrimonio"));

		// Preencher campos de edição
		this.copiarProdutoLocacaoPatrimonioTO(this.getPatrimonioAAlterar(), this.getPatrimonio());

		this.setMensagemID("operacoes.edicao.dadosProntos");
		this.setSucesso(true);
		this.setAtencao(false);
		this.setErro(false);
	}

	/**
	 * Efetiva a alteração em algum patrimônio relacionado ao produto de locação.
	 */
	public void confirmarAlteracaoPatrimonio() {
		try {

			// Verificar se o patrimônio já foi adicionado
			for (ProdutoLocacaoPatrimonioTO patrimonio : this.produto.getPatrimonios()) {
				if (this.getPatrimonio().getCodigo().equals(patrimonio.getCodigo()) && !this.getPatrimonioAAlterar().equals(patrimonio)) {
					throw new NegocioException("operacoes.adicao.erro.patrimonioJaRelacionado");
				}
			}

			// Efetivar alteração
			this.copiarProdutoLocacaoPatrimonioTO(this.getPatrimonio(), this.getPatrimonioAAlterar());

			// Desmarcar edição de brinquedo
			this.setEdicaoPatrimonio(false);

			this.setPatrimonioAAlterar(null);
			this.setPatrimonio(null);

			this.setMensagemID("operacoes.edicao.sucesso");
			this.setAtencao(true);
			this.setSucesso(false);
			this.setErro(false);
		} catch (Exception e) {
			final Exception exTratada = this.tratarMensagemExcecao(e);
			this.setMensagemID("operacoes.erro");
			this.setMensagemDetalhada(exTratada.getMessage());
			this.setSucesso(false);
			this.setAtencao(false);
			this.setErro(true);
		}
	}

	/**
	 * Remover patrimonio dentre os relacionadas ao produto.
	 * 
	 * @throws Exception
	 */
	public void removerPatrimonio() {
		// Obter o patrimonio a ser excluído
		final ProdutoLocacaoPatrimonioTO obj = (ProdutoLocacaoPatrimonioTO) JSFUtilities.getRequestAttribute("patrimonio");
		// remover da lista

		this.produto.getPatrimonios().remove(obj);
		// verificar se o patrimonio já está persistido no banco

		// Caso o brinquedo seja persistido, marcá-lo para exclusão
		if ((obj.getProdutoLocacao() != null) && !obj.getProdutoLocacao().equals(0)) {
			this.getPatrimoniosAExcluir().add(obj.getCodigo());
		}

		// Exibir mensagem de sucesso
		this.setMensagemID("operacoes.exclusao.sucesso");
		this.setSucesso(true);
		this.setAtencao(false);
		this.setErro(false);
	}

	/**
	 * Copia um objeto <code>ProdutoLocacaoPatrimonioTO</code>.
	 * 
	 * @param original
	 *            Objeto cujas propriedades serão copiadas para um novo objeto.
	 * @param clone
	 *            Novo objeto com as propriedades do objeto <code>original</code>.
	 */
	private void copiarProdutoLocacaoPatrimonioTO(final ProdutoLocacaoPatrimonioTO original, final ProdutoLocacaoPatrimonioTO clone) {
		clone.setCodigo(original.getCodigo());
		clone.setDescricao(original.getDescricao());
		clone.setProdutoLocacao(original.getProdutoLocacao());
	}

	/**
	 * Abre a guia de visualização do rastreamento
	 * 
	 * @throws Exception
	 */
	public void visualizarRastreamento() throws Exception {
		ProdutoLocacaoPatrimonioTO obj = (ProdutoLocacaoPatrimonioTO) JSFUtilities.getRequestAttribute("patrimonio");

		if (obj.getRastreamentos().isEmpty()) {
			obj.setRastreamentos(CEControle.getCEFacade().carregarRastreamentosPatrimonio(obj));
		}
		this.setPatrimonio(obj);
		this.setVisualizarRastreamento(Boolean.TRUE);
	}
	public void exportar(ActionEvent evt) throws Exception {
		ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
		String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

		String[] split = paramsTableFiltrada.split(",");
		String campoOrdenacao = split[0].replace("[", "");
		String ordem = split[1];
		String filtro = split[2].replace("''", "");
		filtro = filtro.replace("]", "");
		List listaParaImpressao = getFacade().getCentralEventosFacade().getProdutoLocacao().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
		exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

	}
	/**
	 * Abre a guia de visualização dos patrimônios
	 */
	public void visualizarPatrimonios() {
		this.setPatrimonio(null);
		this.setVisualizarRastreamento(Boolean.FALSE);
	}

	public void setMsgAlert(String msgAlert) {
		this.msgAlert = msgAlert;
	}

	public String getMsgAlert() {
		if (msgAlert == null) {
			return "";
		}
		return msgAlert;
	}

	public void confirmarExcluir(){
		MensagemGenericaControle control = getControlador(MensagemGenericaControle.class);
		control.setMensagemDetalhada("", "");
		setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
		control.init("Exclusão de Produtos",
				"Deseja excluir o Produto?",
				this, "excluir", "", "", "", "grupoBtnExcluir,mensagens");
	}
}
