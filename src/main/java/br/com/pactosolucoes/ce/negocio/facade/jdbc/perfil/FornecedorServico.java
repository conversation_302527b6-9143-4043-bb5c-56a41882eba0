package br.com.pactosolucoes.ce.negocio.facade.jdbc.perfil;

import negocio.facade.jdbc.basico.Fornecedor;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

import br.com.pactosolucoes.ce.comuns.to.FornecedorServicoTO;
import br.com.pactosolucoes.ce.comuns.to.PerfilEventoAmbienteTO;
import br.com.pactosolucoes.ce.comuns.to.ServicoTO;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.arquitetura.CEDao;
import negocio.comuns.basico.FornecedorVO;
import br.com.pactosolucoes.comuns.util.Declaracao;

public class FornecedorServico extends CEDao {
	
	public FornecedorServico() throws Exception {
		super();
	}

	public List<FornecedorServicoTO> consultarFornecedorServico() throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT DISTINCT FS.codigo, FS.fornecedor, FS.servico, S.descricao AS descservico, pes.nome AS descfornecedor  \n");
		sql.append("FROM Pessoa pes, fornecedorservico FS \n");
		sql.append("INNER JOIN servico S on FS.servico = S.codigo \n");
		sql.append("INNER JOIN fornecedor F on FS.fornecedor = F.codigo \n");
		sql.append("where pes.codigo = f.codigo \n");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		ResultSet tabelaResultado = dc.executeQuery();
		return (this.montarDadosConsulta(tabelaResultado));
	}

	@Override
	public FornecedorServicoTO montarDados(ResultSet dadosSQL) throws Exception {
		FornecedorServicoTO obj = new FornecedorServicoTO();
		obj.setCodigo(dadosSQL.getInt("codigo"));
		obj.getFornecedor().setCodigo(dadosSQL.getInt("fornecedor"));
		obj.setCodServico(dadosSQL.getInt("servico"));
		obj.setDescServico(dadosSQL.getString("descservico"));
//		obj.getFornecedor().setDescricao(dadosSQL.getString("descfornecedor"));
		return obj;
	}
	
	public void incluir(Integer codServico, final Integer codFornecedor)throws Exception {
		super.incluirObj(this.getIdEntidade());

		StringBuilder sql = new StringBuilder();
		sql.append("INSERT INTO fornecedorservico (fornecedor, servico) \n");
		sql.append("VALUES (?, ?)");
		
		int i = 0;
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(++i, codFornecedor);
		dc.setInt(++i, codServico);

		dc.execute();
	}
	
	public List<FornecedorServicoTO> consultar(final FornecedorServicoTO filtro) throws Exception {
		super.consultarObj(this.getIdEntidade());
		StringBuilder sql = new StringBuilder();

		sql.append("SELECT DISTINCT FS.codigo, FS.fornecedor, FS.servico, S.descricao AS descservico, pes.nome AS descfornecedor  \n");
		sql.append("FROM Pessoa pes, fornecedorservico FS \n");
		sql.append("INNER JOIN servico S on FS.servico = S.codigo \n");
		sql.append("INNER JOIN fornecedor F on FS.fornecedor = F.codigo \n");

		if (filtro != null) {
			sql.append("WHERE pes.codigo = f.codigo \n");

			if ((filtro.getCodigo() != null) && !filtro.getCodigo().equals(0)) {
				sql.append("AND FS.codigo = ? \n");
			}
			if ((filtro.getFornecedor().getCodigo() != null) && !filtro.getFornecedor().getCodigo().equals(0)) {
				sql.append("AND F.codigo = ? \n");
			}
			if ((filtro.getCodServico() != null) && !filtro.getCodServico().equals(0)) {
				sql.append("AND S.codigo = ? \n");
			}
			if ((filtro.getDescServico() != null) && !filtro.getDescServico().equals("")) {
				sql.append("AND UPPER (S.descricao) LIKE ? \n");
			}
//			if ((filtro.getFornecedor().getDescricao() != null) && !filtro.getFornecedor().getDescricao().equals("")) {
//				sql.append("AND UPPER (descfornecedor) LIKE ? \n");
//			}
		}

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		int numParam = 0;

		if (filtro != null) {
			
			if ((filtro.getCodigo() != null) && !filtro.getCodigo().equals(0)) {
				dc.setInt(++numParam, filtro.getCodigo());
			}
			if ((filtro.getFornecedor().getCodigo() != null) && !filtro.getFornecedor().getCodigo().equals(0)) {
				dc.setInt(++numParam, filtro.getFornecedor().getCodigo());
			}
			if ((filtro.getCodServico() != null) && !filtro.getCodServico().equals(0)) {
				dc.setInt(++numParam, filtro.getCodServico());
			}
			if ((filtro.getDescServico() != null) && !filtro.getDescServico().equals("")) {
				dc.setString(++numParam, filtro.getDescServico().toUpperCase() + "%");
			}
			
//			if ((filtro.getFornecedor().getDescricao() != null) && !filtro.getFornecedor().getDescricao().equals("")) {
//				dc.setString(++numParam, filtro.getFornecedor().getDescricao().toUpperCase() + "%");
//			}
		}

		ResultSet tabelaResultado = dc.executeQuery();
		return (this.montarDadosConsulta(tabelaResultado));
	}
	
	public List<FornecedorServicoTO> consultarServicoSemParametro() throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT * \n");
		sql.append("FROM fornecedorservico ");

		Declaracao dc = new Declaracao(sql.toString(), this.con);

		ResultSet tabelaResultado = dc.executeQuery();
		return (this.montarDadosConsultaFornecedorServico(tabelaResultado));
	}
	
	public FornecedorServicoTO montarDadosFornecedorServico(ResultSet dadosSQL) throws Exception {
		FornecedorServicoTO obj = new FornecedorServicoTO();
		obj.setCodigo(dadosSQL.getInt("codigo"));
		obj.getFornecedor().setCodigo(dadosSQL.getInt("fornecedor"));
		obj.setFornecedor(new Fornecedor().obter(obj.getFornecedor().getCodigo()));
		obj.setCodServico(dadosSQL.getInt("servico"));
		return obj;
	}

	public List montarDadosConsultaFornecedorServico(final ResultSet tabelaResultado) throws Exception {
		final List vetResultado = new ArrayList();
		while (tabelaResultado.next()) {
			final Object obj = this.montarDadosFornecedorServico(tabelaResultado);
			vetResultado.add(obj);
		}
		return vetResultado;
	}
	

	/**
	 * Responsavel por consultar os fornecedores terceirizados de um servico
	 * @param servico
	 * @return 
	 * <AUTHOR>
	 * @throws Exception
	 */
	public List<FornecedorServicoTO> consultarTerceirizados(Integer servico) throws Exception{
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT p.nome AS descricao, fs.codigo AS codigo FROM fornecedor f ");
		sql.append("INNER JOIN fornecedorservico fs ON fs.fornecedor = f.codigo ");
		sql.append("INNER JOIN pessoa p ON p.codigo = f.pessoa ");
		sql.append("WHERE servico = ?");
		Declaracao dc = new Declaracao(sql.toString(), con);
		dc.setInt(1, servico);
		ResultSet rs = dc.executeQuery();
		return montarSimples(rs);
		
	}
	
	/**
	 * montar só com descricao do fornecedor e codigo
	 * @param result
	 * @return
	 * @throws Exception
	 */
	public List<FornecedorServicoTO> montarSimples(ResultSet result) throws Exception{
		List<FornecedorServicoTO> dados = new ArrayList<FornecedorServicoTO>();
		while(result.next()){
			FornecedorServicoTO fServico = new FornecedorServicoTO();
			fServico.getFornecedor().setDescricao(result.getString("descricao"));
			fServico.setCodigo(result.getInt("codigo"));
			dados.add(fServico);
		}
		return dados;
	}
	/**
	 * Excluir um relacionamento de fornecedor servico
	 * @param codigo
	 * @throws Exception 
	 */
	public void excluir(Integer codigo) throws Exception{
		this.excluirObj(this.getIdEntidade());
		String sql = "DELETE FROM fornecedorservico WHERE codigo = ?";
		Declaracao dc = new Declaracao(sql, con);
		dc.setInt(1,codigo);
		dc.execute();
	}

	
}
