package br.com.pactosolucoes.ce.comuns.to;

import java.io.Serializable;
import java.util.Date;

import negocio.comuns.arquitetura.UsuarioVO;

import br.com.pactosolucoes.ce.comuns.enumerador.Situacao;
import br.com.pactosolucoes.comuns.util.Formatador;

/**
 * Transfer Object para encapsulamento de dados relacionados à entidade Cadastro inicial.
 * 
 * <AUTHOR>
 */
public class CadastroInicialTO implements Serializable {

	

	/**
	 * 
	 */
	private static final long serialVersionUID = 1693372336441951689L;
	// Atributos
	private int codigo;
	private int codigoInteressado;
	private String nomeEvento;
	private String quemE;
	private int nrConvidados;
	private boolean associarQuiz;
	private String comoConheceu;
	private String formaContato;
	private String observacoes;
	private String quemIndicou;
	private Date dataInicial;
	private int codUserRespons;
	private int codAmbInteresse;
	private Situacao situacao;
	private Integer codigoPerfilEvento;
	private String nomePerfil;
	
	//Encerramento
	private UsuarioVO responsavelEncerramento;
	private Date dataEncerramento;
	private String obsEncerramento;
	
	private String msgDiasParaEvento;

	// Getters and Setters
	/**
	 * @return the codigo
	 */
	public int getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            the codigo to set
	 */
	public void setCodigo(final int codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return O campo codigoInteressado.
	 */
	public int getCodigoInteressado() {
		return this.codigoInteressado;
	}

	/**
	 * @param codigoInteressado
	 *            O novo valor de codigoInteressado.
	 */
	public void setCodigoInteressado(final int codigoInteressado) {
		this.codigoInteressado = codigoInteressado;
	}

	/**
	 * @return the nomeEvento
	 */
	public String getNomeEvento() {
		return this.nomeEvento;
	}

	/**
	 * @param nomeEvento
	 *            the nomeEvento to set
	 */
	public void setNomeEvento(final String nomeEvento) {
		this.nomeEvento = nomeEvento;
	}

	/**
	 * @return the quemE
	 */
	public String getQuemE() {
		return this.quemE;
	}

	/**
	 * @param quemE
	 *            the quemE to set
	 */
	public void setQuemE(final String quemE) {
		this.quemE = quemE;
	}

	/**
	 * @return the nrConvidados
	 */
	public int getNrConvidados() {
		return this.nrConvidados;
	}

	/**
	 * @param nrConvidados
	 *            the nrConvidados to set
	 */
	public void setNrConvidados(final int nrConvidados) {
		this.nrConvidados = nrConvidados;
	}

	/**
	 * @return the associarQuestionario
	 */
	public boolean isAssociarQuestionario() {
		return this.associarQuiz;
	}

	/**
	 * @param associarQuiz
	 *            the associarQuestionario to set
	 */
	public void setAssociarQuestionario(final boolean associarQuiz) {
		this.associarQuiz = associarQuiz;
	}

	/**
	 * @return the comoConheceu
	 */
	public String getComoConheceu() {
		return this.comoConheceu;
	}

	/**
	 * @param comoConheceu
	 *            the comoConheceu to set
	 */
	public void setComoConheceu(final String comoConheceu) {
		this.comoConheceu = comoConheceu;
	}

	/**
	 * @return the formaContato
	 */
	public String getFormaContato() {
		return this.formaContato;
	}

	/**
	 * @param formaContato
	 *            the formaContato to set
	 */
	public void setFormaContato(final String formaContato) {
		this.formaContato = formaContato;
	}

	/**
	 * @return the observacoes
	 */
	public String getObservacoes() {
		return this.observacoes;
	}

	/**
	 * @param observacoes
	 *            the observacoes to set
	 */
	public void setObservacoes(final String observacoes) {
		this.observacoes = observacoes;
	}

	/**
	 * @return the quemIndicou
	 */
	public String getQuemIndicou() {
		return this.quemIndicou;
	}

	/**
	 * @param quemIndicou
	 *            the quemIndicou to set
	 */
	public void setQuemIndicou(final String quemIndicou) {
		this.quemIndicou = quemIndicou;
	}

	/**
	 * @return the dataInicial
	 */
	public Date getDataInicial() {
		return this.dataInicial;
	}

	/**
	 * @param dataInicial
	 *            the dataInicial to set
	 */
	public void setDataInicial(final Date dataInicial) {
		this.dataInicial = dataInicial;
	}

	/**
	 * @return the codigoUsuarioResponsavel
	 */
	public int getCodigoUsuarioResponsavel() {
		return this.codUserRespons;
	}

	/**
	 * @param codUserRespons
	 *            the codigoUsuarioResponsavel to set
	 */
	public void setCodigoUsuarioResponsavel(final int codUserRespons) {
		this.codUserRespons = codUserRespons;
	}

	/**
	 * @return the codigoAmbienteInteresse
	 */
	public int getCodigoAmbienteInteresse() {
		return this.codAmbInteresse;
	}

	/**
	 * @param codAmbInteresse
	 *            the codigoAmbienteInteresse to set
	 */
	public void setCodigoAmbienteInteresse(final int codAmbInteresse) {
		this.codAmbInteresse = codAmbInteresse;
	}

	/**
	 * @return O campo situacao.
	 */
	public Situacao getSituacao() {
		return this.situacao;
	}

	/**
	 * @param situacao
	 *            O novo valor de situacao.
	 */
	public void setSituacao(final Situacao situacao) {
		this.situacao = situacao;
	}

	/**
	 * @return O campo dataInicio formatado.
	 */
	public String getDataFormatada() {
		return Formatador.formatarDataPadrao(this.dataInicial);
	}

	/**
	 * @return O campo responsavelEncerramento.
	 */
	public UsuarioVO getResponsavelEncerramento() {
		return this.responsavelEncerramento;
	}

	/**
	 * @param responsavelEncerramento O novo valor de responsavelEncerramento.
	 */
	public void setResponsavelEncerramento(UsuarioVO responsavelEncerramento) {
		this.responsavelEncerramento = responsavelEncerramento;
	}

	/**
	 * @return O campo dataEncerramento.
	 */
	public Date getDataEncerramento() {
		return this.dataEncerramento;
	}

	/**
	 * @param dataEncerramento O novo valor de dataEncerramento.
	 */
	public void setDataEncerramento(Date dataEncerramento) {
		this.dataEncerramento = dataEncerramento;
	}

	/**
	 * @return O campo dataInicio formatado.
	 */
	public String getDataEncerramentoFormatada() {
		return Formatador.formatarDataPadrao(this.dataEncerramento);
	}
	
	/**
	 * @return O campo obsEncerramento.
	 */
	public String getObsEncerramento() {
		return this.obsEncerramento;
	}

	/**
	 * @param obsEncerramento O novo valor de obsEncerramento.
	 */
	public void setObsEncerramento(String obsEncerramento) {
		this.obsEncerramento = obsEncerramento;
	}

	/**
	 * @return O campo msgDiasParaEvento.
	 */
	public String getMsgDiasParaEvento() {
		return this.msgDiasParaEvento;
	}

	/**
	 * @param msgDiasParaEvento O novo valor de msgDiasParaEvento.
	 */
	public void setMsgDiasParaEvento(String msgDiasParaEvento) {
		this.msgDiasParaEvento = msgDiasParaEvento;
	}

	/**
	 * @param codigoPerfilEvento the codigoPerfilEvento to set
	 */
	public void setCodigoPerfilEvento(Integer codigoPerfilEvento) {
		this.codigoPerfilEvento = codigoPerfilEvento;
	}

	/**
	 * @return the codigoPerfilEvento
	 */
	public Integer getCodigoPerfilEvento() {
		return codigoPerfilEvento;
	}

	/**
	 * @param nomePerfil the nomePerfil to set
	 */
	public void setNomePerfil(String nomePerfil) {
		this.nomePerfil = nomePerfil;
	}

	/**
	 * @return the nomePerfil
	 */
	public String getNomePerfil() {
		return nomePerfil;
	}
}