package br.com.pactosolucoes.controle.json.sesice;

public enum OperacoesIntegracaoSesiCeEnum {

    PROCESSAR_ALUNO,
    CANCELAR_CONTRATO;

    public static OperacoesIntegracaoSesiCeEnum obterOperacao(String operacao) {
        if (operacao == null) {
            return null;
        }
        for (OperacoesIntegracaoSesiCeEnum op : values()) {
            if (op.name().equalsIgnoreCase(operacao)) {
                return op;
            }
        }
        return null;
    }
}
