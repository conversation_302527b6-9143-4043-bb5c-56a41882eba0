package br.com.pactosolucoes.controle.json.nuvemshop;

public enum OperacoesIntegracaoNuvemshopEnum {


    SALVAR_DADOS_INTEGRACAO,
    BUSCAR_PEDIDOS,
    REGISTRAR_LOG;


    public static OperacoesIntegracaoNuvemshopEnum obterOperacao(String operacao) {
        if (operacao == null) {
            return null;
        }
        for (OperacoesIntegracaoNuvemshopEnum op : values()) {
            if (op.name().equalsIgnoreCase(operacao)) {
                return op;
            }
        }
        return null;
    }
}
