/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.controle.json.contratooperacao;

public enum ContratoOperacaoEnum {
    executarRobo,
    atualizarSintetico
    ;

    public static ContratoOperacaoEnum obterOperacao(String o) {
        if (o == null) {
            return null;
        }
        for (ContratoOperacaoEnum op : values()) {
            if (op.name().toLowerCase().equals(o.toLowerCase())) {
                return op;
            }
        }
        return null;
    }
}

