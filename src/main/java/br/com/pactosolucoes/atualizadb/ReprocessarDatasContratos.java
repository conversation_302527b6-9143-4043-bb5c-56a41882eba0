package br.com.pactosolucoes.atualizadb;

import controle.arquitetura.threads.ThreadMyWellnes;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class ReprocessarDatasContratos {

    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("*********************************************", "postgres", "pactodb");
            String chave = "";
            sincronizarDados(con, chave);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void  sincronizarDados(Connection con, String chave) throws SQLException {
        try {
            Conexao.guardarConexaoForJ2SE(con);
            Contrato contratoDAO = new Contrato(con);
            ContratoOperacao contratoOperacaoDAO = new ContratoOperacao(con);
            List<ContratoVO> contratos = contratoDAO.consultar("select * from contrato where codigo in (select distinct contrato from contratooperacao where tipooperacao in ('BC') ) order by codigo", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            List<String> operacoesExcluir = new ArrayList<String>();
            operacoesExcluir.add("BC");
            operacoesExcluir.add("BX");

            for(ContratoVO contratoVO: contratos){
                contratoOperacaoDAO.processarDatasContrato(contratoVO, operacoesExcluir);
            }


        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
