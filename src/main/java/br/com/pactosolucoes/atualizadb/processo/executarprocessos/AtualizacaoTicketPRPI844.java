package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Victor August<PERSON>",
        data = "04/04/2025",
        descricao = " Histórico de contatos no Sistema Pacto não está mostrando a conversa inteira",
        motivacao = "IA-844"
)
public class AtualizacaoTicketPRPI844 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {

        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "WITH consolidado AS (\n" +
                            "    SELECT\n" +
                            "        MIN(codigo) AS codigo_para_manter,\n" +
                            "        cliente,\n" +
                            "        DATE(dia) AS data_dia\n" +
                            "    FROM historicocontato\n" +
                            "    WHERE tipocontato = 'WA'\n" +
                            "    GROUP BY cliente, DATE(dia)\n" +
                            "    HAVING COUNT(*) > 1\n" +
                            "),\n" +
                            "     duplicados AS (\n" +
                            "         SELECT\n" +
                            "             h.codigo AS codigo_antigo,\n" +
                            "             c.codigo_para_manter\n" +
                            "         FROM historicocontato h\n" +
                            "         JOIN consolidado c\n" +
                            "              ON c.cliente = h.cliente AND DATE(h.dia) = c.data_dia\n" +
                            "         WHERE h.codigo <> c.codigo_para_manter\n" +
                            "     )\n" +
                            "UPDATE fecharmetadetalhado f\n" +
                            "SET historicocontato = d.codigo_para_manter\n" +
                            "FROM duplicados d\n" +
                            "WHERE f.historicocontato = d.codigo_antigo;\n",
                    c
            );

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "WITH consolidado AS (\n" +
                            "    SELECT\n" +
                            "        MIN(codigo) AS codigo_para_manter,\n" +
                            "        cliente,\n" +
                            "        DATE(dia) AS data_dia,\n" +
                            "        STRING_AGG(observacao, E'\\n' ORDER BY dia) AS observacoes_consolidadas,\n" +
                            "        MAX(dia) AS dia_mais_recente\n" +
                            "    FROM historicocontato\n" +
                            "    WHERE tipocontato = 'WA'\n" +
                            "    GROUP BY cliente, DATE(dia)\n" +
                            "    HAVING COUNT(*) > 1\n" +
                            ")\n" +
                            "UPDATE historicocontato h\n" +
                            "SET observacao = c.observacoes_consolidadas,\n" +
                            "    dia = c.dia_mais_recente\n" +
                            "FROM consolidado c\n" +
                            "WHERE h.codigo = c.codigo_para_manter;\n",
                    c
            );

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "WITH consolidado AS (\n" +
                            "    SELECT\n" +
                            "        MIN(codigo) AS codigo_para_manter,\n" +
                            "        cliente,\n" +
                            "        DATE(dia) AS data_dia\n" +
                            "    FROM historicocontato\n" +
                            "    WHERE tipocontato = 'WA'\n" +
                            "    GROUP BY cliente, DATE(dia)\n" +
                            "    HAVING COUNT(*) > 1\n" +
                            "),\n" +
                            "     duplicados AS (\n" +
                            "         SELECT\n" +
                            "             h.codigo AS codigo_antigo,\n" +
                            "             c.codigo_para_manter\n" +
                            "         FROM historicocontato h\n" +
                            "         JOIN consolidado c\n" +
                            "              ON c.cliente = h.cliente AND DATE(h.dia) = c.data_dia\n" +
                            "         WHERE h.codigo <> c.codigo_para_manter\n" +
                            "     )\n" +
                            "DELETE FROM historicocontato\n" +
                            "WHERE codigo IN (SELECT codigo_antigo FROM duplicados);\n",
                    c
            );
        }
    }
}