package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Vitor Junio",
        data = "26/02/2025",
        descricao = "Adicionar coluna para buscar ultimo json do produtoVO na tabela compra.",
        motivacao = "GCM-1")
public class GCM1AdicionarCampoUltimoProdutoVOJsonNaCompra implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE compra ADD COLUMN ultimoprodutoconfigjson TEXT;"
                    , c);
        }
    }
}
