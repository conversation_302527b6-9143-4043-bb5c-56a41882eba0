package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class PovoarAulasTreinoIndependente {


    public static void main(String[] args) throws Exception{
//        Connection con = DriverManager.getConnection("*************************************************************************", "postgres", "pactodb");
        Connection con = DriverManager.getConnection("*******************************************", "postgres", "pactodb");
//        povoarAulas(con);
        povoarAlunos(con,Calendario.hoje());
    }

    private static void povoarAlunos(Connection con, Date dia) throws Exception{
        String sql = "select capacidade,a.nome, ah.codigo as horario,a.professor_codigo, a.codigo as aula from aula a inner join aulahorario ah on ah.aula_codigo = a.codigo";
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        while(rs.next()){
            Integer aula = rs.getInt("aula");
            Integer horario = rs.getInt("horario");
            Integer capacidade = rs.getInt("capacidade");
            Integer professor = rs.getInt("professor_codigo");
            char letra = rs.getString("nome").charAt(0);
            String  sqlcli = "select codigo from clientesintetico  where nome like '"+letra+"%' LIMIT "+capacidade;
            ResultSet rscli = SuperFacadeJDBC.criarConsulta(sqlcli, con);
            String insert = "insert into aulaaluno (cliente_codigo, professor_codigo, dia, horario_codigo) values (?,?,?,?)  ";
            while(rscli.next()){
                PreparedStatement st = con.prepareStatement(insert);
                st.setInt(1, rscli.getInt("codigo"));
                st.setInt(2, professor);
                st.setDate(3, Uteis.getDataJDBC(dia));
                st.setInt(4, horario);
                st.execute();
            }

        }

    }

    private static Map<String, String> mapahora(String inicio, String fim){
        Map<String, String> m = new HashMap<String, String>();
        m.put("inicio", inicio);
        m.put("fim", fim);
        return m;
    }

    private static void povoarAulas(Connection con) throws Exception{

        Map<Integer, Map<String, String>> mapaHorarios = new HashMap<Integer, Map<String, String>>();
        mapaHorarios.put(1, mapahora("09:00", "10:00"));
        mapaHorarios.put(2, mapahora("10:00", "11:00"));
        mapaHorarios.put(3, mapahora("12:00", "13:00"));
        mapaHorarios.put(4, mapahora("13:00", "14:00"));
        mapaHorarios.put(5, mapahora("14:00", "15:00"));
        mapaHorarios.put(6, mapahora("15:00", "16:00"));
        mapaHorarios.put(7, mapahora("16:00", "17:00"));
        mapaHorarios.put(8, mapahora("17:00", "18:00"));
        mapaHorarios.put(9, mapahora("18:00", "19:00"));
        mapaHorarios.put(10, mapahora("19:00", "20:00"));
        mapaHorarios.put(11, mapahora("20:00", "21:00"));
        mapaHorarios.put(12, mapahora("21:00", "22:00"));
        mapaHorarios.put(13, mapahora("22:00", "23:00"));
        mapaHorarios.put(14, mapahora("23:00", "23:40"));

        int codiprof = 0;
        Map<Integer, Integer> mapaProfessores = new HashMap<Integer, Integer>();
        ResultSet rsprof = SuperFacadeJDBC.criarConsulta("select codigo from professorsintetico", con);
        while(rsprof.next()){
            mapaProfessores.put(codiprof++, rsprof.getInt("codigo"));
        }

        int codiamb = 0;
        Map<Integer, Integer> mapaAmbiente = new HashMap<Integer, Integer>();
        ResultSet rsambiente = SuperFacadeJDBC.criarConsulta("select codigo from ambiente", con);
        while(rsambiente.next()){
            mapaAmbiente.put(codiamb++, rsambiente.getInt("codigo"));
        }

        int professor = 0;
        int ambiente = 0;

        ResultSet rsaula = SuperFacadeJDBC.criarConsulta("select * from aula", con);
        if(!rsaula.next()){
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select * from modalidade", con);
            while(rs.next()){
                if(professor >= codiprof){
                    professor = 0;
                }

                if(ambiente >= codiamb){
                    ambiente = 0;
                }


                String nomeAula = rs.getString("nome");

                String sql = "insert into aula (bonificacao, capacidade, datafim, datainicio, diassemana, frequencia, meta, pontosbonus, ambiente_codigo, modalidade_codigo, professor_codigo, mensagem, nome, minutostolerancia)"+
                        " values (120,10,'2019-01-31 00:00:00','2018-10-01 00:00:00','DM,SG,TR,QA,QI,SX,SB',0,50,120,?,?,?,'Boa Aula!',?,12)";

                PreparedStatement stm = con.prepareStatement(sql);
                stm.setInt(1,mapaAmbiente.get(ambiente));
                stm.setInt(2, rs.getInt("codigo"));
                stm.setInt(3, mapaProfessores.get(professor));
                stm.setString(4, nomeAula);
                stm.execute();
                ambiente++;
                professor++;
            }
        }

        ResultSet rshorarios = SuperFacadeJDBC.criarConsulta("select * from aulahorario", con);
        if(!rshorarios.next()){
            int aula1 = 1;
            int aula2 = 7;

            rsaula = SuperFacadeJDBC.criarConsulta("select codigo from aula", con);
            while (rsaula.next()){
                int codigo = rsaula.getInt("codigo");
                String insert = "insert into aulahorario(inicio, fim, aula_codigo) values (?,?,?)";

                PreparedStatement stma = con.prepareStatement(insert);
                stma.setString(1, mapaHorarios.get(aula1).get("inicio"));
                stma.setString(2, mapaHorarios.get(aula1).get("fim"));
                stma.setInt(3, codigo);
                stma.execute();

                stma.setString(1, mapaHorarios.get(aula2).get("inicio"));
                stma.setString(2, mapaHorarios.get(aula2).get("fim"));
                stma.setInt(3, codigo);
                stma.execute();

                aula1++;
                aula2++;

                if(aula1 > 6){
                    aula1 = 1;
                }
                if(aula2 > 14){
                    aula2 = 7;
                }
            }
        }

    }
}
