/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class CorrigirClienteDuplicadosPorNome {

    public static void main(String[] args) {
        try {
            Connection con = new DAO().obterConexaoEspecifica("c44fa7a3d50f6830acc7a7647d110e33");
            excluirClientesDuplicados(con);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void excluirClientesDuplicados(Connection con) throws SQLException {
        con.setAutoCommit(false);

        List<String> nomesDuplicadosNaoRemovidos = new ArrayList<>();

        StringBuilder retorno = new StringBuilder();

        StringBuilder sqlDuplicados = new StringBuilder();
        sqlDuplicados.append("SELECT\n");
        sqlDuplicados.append("  pes.nome,\n");
        sqlDuplicados.append("  MAX(pes.codigo) AS cod_pessoa,\n");
        sqlDuplicados.append("  count(*) AS count\n");
        sqlDuplicados.append("FROM pessoa pes \n");
        sqlDuplicados.append("INNER JOIN cliente cli ON pes.codigo = cli.pessoa\n");
        sqlDuplicados.append("GROUP BY 1\n");
        sqlDuplicados.append("HAVING count(*) > 1");

        Statement stm = con.createStatement();
        ResultSet resultSet = stm.executeQuery(sqlDuplicados.toString());
        while (resultSet.next()) {
            String nomeDuplicado = resultSet.getString("nome");
            int qtdDuplicacoes = resultSet.getInt("count");

            List<Integer> codPessoasDeletar = new ArrayList<Integer>();
            List<Integer> codClienteDeletar = new ArrayList<Integer>();

            StringBuilder sqlCodigosDeletar = new StringBuilder();
            sqlCodigosDeletar.append("SELECT \n");
            sqlCodigosDeletar.append("   pes.codigo as pessoa, \n");
            sqlCodigosDeletar.append("   cli.codigo as cliente, \n");
            sqlCodigosDeletar.append("   coalesce(pes.cfp, '0') as cpf, \n");
            sqlCodigosDeletar.append("   cli.matriculaexterna, \n");
            sqlCodigosDeletar.append("   (SELECT count(codigo) FROM recibopagamento rp WHERE rp.pessoapagador = pes.codigo) as recibos,\n");
            sqlCodigosDeletar.append("   (SELECT count(codigo) FROM contrato con WHERE con.pessoa = pes.codigo) as contratos \n");
            sqlCodigosDeletar.append("FROM pessoa pes \n");
            sqlCodigosDeletar.append("LEFT JOIN cliente cli ON pes.codigo = cli.pessoa\n");
            sqlCodigosDeletar.append("WHERE pes.nome = '").append(nomeDuplicado.replace("'", "''")).append("'\n");
            sqlCodigosDeletar.append("ORDER BY datacadastro DESC, cli.codigo DESC, coalesce(pes.cfp, '') = '', cli.matriculaexterna, pes.codigo desc;");

            System.out.println("Ajustando: " + nomeDuplicado + " (Qtd: " + qtdDuplicacoes + ")");
            retorno.append("Ajustando: <b>").append(nomeDuplicado).append("</b> (Qtd: ").append(qtdDuplicacoes).append(")<br/>");

            stm = con.createStatement();
            ResultSet resultSetCodigosDeletar = stm.executeQuery(sqlCodigosDeletar.toString());
            Integer codPessoaNaoExcluir = null;
            Map<Integer, Boolean> matriculaExternaMap = new HashMap<>();
            Map<String, Boolean> cpfMap = new HashMap<>();
            boolean primeiroResultado = true;
            while (resultSetCodigosDeletar.next()) {
                Integer codPessoa = resultSetCodigosDeletar.getInt("pessoa");
                Integer codCliente = resultSetCodigosDeletar.getInt("cliente");
                String cpf = resultSetCodigosDeletar.getString("cpf");
                Integer matriculaExterna = resultSetCodigosDeletar.getInt("matriculaexterna");

                if (primeiroResultado) {
                    if (!UteisValidacao.emptyNumber(matriculaExterna)) {
                        matriculaExternaMap.put(matriculaExterna, true);
                    }
                    if (!cpf.equals("0")) {
                        cpfMap.put(cpf, true);
                    }
                    codPessoaNaoExcluir = codPessoa;
                    primeiroResultado = false;
                    continue;
                }

                boolean adicionar = true;

                if (!UteisValidacao.emptyNumber(matriculaExterna) && !matriculaExternaMap.containsKey(matriculaExterna)) {
                    matriculaExternaMap.put(matriculaExterna, true);
                    adicionar = false;
                }

                if (!cpf.equals("0") && !cpfMap.containsKey(cpf)) {
                    cpfMap.put(cpf, true);
                    adicionar = false;
                }

                if (adicionar) {
                    codPessoasDeletar.add(codPessoa);
                    if (!UteisValidacao.emptyNumber(codCliente)) {
                        codClienteDeletar.add(codCliente);
                    }
                }
            }

            if (codClienteDeletar.size() > 0 || codPessoasDeletar.size() > 0) {
                try {
                    Statement stmExclusao = con.createStatement();
                    con.setAutoCommit(false);

                    stmExclusao.execute("UPDATE cliente SET pessoaresponsavel = " + codPessoaNaoExcluir + " WHERE pessoaresponsavel IN (" + obterCodigos(codPessoasDeletar) + ");");
                    stmExclusao.execute("UPDATE agenda SET cliente = null WHERE codigo in (select agenda from historicocontato  where passivo in (select codigo from passivo where cliente in (" + obterCodigos(codClienteDeletar)+")) and agenda is not null)");
                    stmExclusao.execute("UPDATE agenda SET cliente = null WHERE codigo in (select agenda from historicocontato  where indicado in (select codigo from  indicado where cliente in (" + obterCodigos(codClienteDeletar)+")) and agenda is not null)");
                    stmExclusao.execute("UPDATE passivo SET cliente = null WHERE cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("UPDATE indicacao SET clientequeindicou = null WHERE clientequeindicou in (" + obterCodigos(codClienteDeletar) + ")");
                    stmExclusao.execute("update cliente set pessoaresponsavel = null where pessoaresponsavel in (" + obterCodigos(codPessoasDeletar) + ")");
                    stmExclusao.execute("delete from atestado  where arquivo in (select codigo from arquivo where pessoa in (" + obterCodigos(codPessoasDeletar) + "))");
                    stmExclusao.execute("DELETE FROM arquivo WHERE pessoa in (" + obterCodigos(codPessoasDeletar) + ")");
                    stmExclusao.execute("DELETE FROM liberacaoacesso WHERE pessoa in (" + obterCodigos(codPessoasDeletar) + ")");
                    stmExclusao.execute("DELETE FROM caixamovconta  WHERE movconta in (select codigo from movconta where pessoa in (" + obterCodigos(codPessoasDeletar) + "))");
                    stmExclusao.execute("DELETE FROM clientemensagem WHERE cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("DELETE FROM questionariocliente WHERE cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("DELETE FROM movcontacontabil WHERE movconta IN (SELECT codigo FROM movconta WHERE pessoa in (" + obterCodigos(codPessoasDeletar) + "))");
                    stmExclusao.execute("DELETE FROM movconta WHERE pessoa in (" + obterCodigos(codPessoasDeletar) + ")");
                    stmExclusao.execute("DELETE FROM maladiretaenviada WHERE cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from maladiretacrmextracliente where cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from fecharmetadetalhado where cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from historicoContato where cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from agenda where cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from reposicao where cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from situacaoclientesinteticodw where codigoCliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from situacaocontratoanaliticodw where cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from reciboclienteconsultor where cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from transacao where codigo in (select distinct t.codigo from transacao t inner join transacaomovparcela tm on tm.transacao = t.codigo inner join movparcela m on m.codigo = tm.movparcela left join vendaavulsa v on v.codigo = m.vendaavulsa where m.pessoa in ("+ obterCodigos(codPessoasDeletar) + ") and (m.contrato is not null or m.aulaavulsadiaria is not null or v.cliente is not null))");
                    stmExclusao.execute("delete from remessaitemmovparcela    where movparcela  in (select mp.codigo from movparcela mp left join vendaavulsa va on va.codigo = mp.vendaavulsa where mp.pessoa in ("+ obterCodigos(codPessoasDeletar) + ") and (mp.contrato is not null or mp.aulaavulsadiaria is not null or va.cliente is not null))");
                    stmExclusao.execute("delete from remessaitem  where movparcela  in (select mp.codigo from movparcela mp left join vendaavulsa va on va.codigo = mp.vendaavulsa where mp.pessoa in (" + obterCodigos(codPessoasDeletar) + ") and (mp.contrato is not null or mp.aulaavulsadiaria is not null or va.cliente is not null))");
                    stmExclusao.execute("delete from movparcelacupomdesconto WHERE movparcela IN (select mp.codigo from movparcela mp left join vendaavulsa va on va.codigo = mp.vendaavulsa where mp.pessoa in (" + obterCodigos(codPessoasDeletar) + ") and (mp.contrato is not null or mp.aulaavulsadiaria is not null or va.cliente is not null))");
                    stmExclusao.execute("delete from boleto b WHERE codigo in (select boleto from boletomovparcela  where movparcela  in (select mp.codigo from movparcela mp left join vendaavulsa va on va.codigo = mp.vendaavulsa where mp.pessoa in (" + obterCodigos(codPessoasDeletar) + ") and (mp.contrato is not null or mp.aulaavulsadiaria is not null or va.cliente is not null)))  or not exists(select codigo from boletomovparcela  where boleto  = b.codigo)");
                    stmExclusao.execute("delete from recibopagamento where codigo in (select recibopagamento from pagamentomovparcela where  movparcela in (select mp.codigo from movparcela mp left join vendaavulsa va on va.codigo = mp.vendaavulsa where mp.pessoa in (" + obterCodigos(codPessoasDeletar) + ") and (mp.contrato is not null or mp.aulaavulsadiaria is not null or va.cliente is not null)))");
                    stmExclusao.execute("delete from pagamentomovparcela  where movparcela  in (select mp.codigo from movparcela mp left join vendaavulsa va on va.codigo = mp.vendaavulsa where mp.pessoa in (" + obterCodigos(codPessoasDeletar) + ") and (mp.contrato is not null or mp.aulaavulsadiaria is not null or va.cliente is not null))");
                    stmExclusao.execute("delete from movparcela  WHERE codigo in (select mp.codigo from movparcela mp left join vendaavulsa va on va.codigo = mp.vendaavulsa where mp.pessoa in (" + obterCodigos(codPessoasDeletar) + ") and (mp.contrato is not null or mp.aulaavulsadiaria is not null or va.cliente is not null))");
                    stmExclusao.execute("UPDATE periodoacessocliente set contratobaseadorenovacao  = null where contratobaseadorenovacao in (select codigo from contrato where pessoa in (" + obterCodigos(codPessoasDeletar) + "))");
                    stmExclusao.execute("UPDATE contratooperacao set clienterecebedias = null where clienterecebedias in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from fecharmetadetalhado where contrato in (select codigo from contrato where pessoa in (" + obterCodigos(codPessoasDeletar) + "))");
                    stmExclusao.execute("delete from contratoassinaturadigital where contrato IN (select codigo from contrato where pessoa in (" + obterCodigos(codPessoasDeletar) + "))");
                    stmExclusao.execute("delete from contratoduracaocreditotreino where contratoduracao IN (select codigo from contratoduracao where contrato in (select codigo from contrato where pessoa in (" + obterCodigos(codPessoasDeletar) + ")))");
                    stmExclusao.execute("update contratooperacao set clienterecebedias  = null where clienterecebedias in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("update contratooperacao set clientetransferedias    = null where clientetransferedias in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from contrato where pessoa in (" + obterCodigos(codPessoasDeletar) + ")");
                    stmExclusao.execute("delete from historicoaluguelarmario where aluguelarmario in ( select codigo from aluguelarmario where cliente in (" + obterCodigos(codClienteDeletar)+"))");
                    stmExclusao.execute("delete from aluguelarmario where cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from movProduto where pessoa in (" + obterCodigos(codPessoasDeletar) + ")");
                    stmExclusao.execute("delete from exclusaomovprodutoestoque where pessoa in (" + obterCodigos(codPessoasDeletar) + ")");
                    stmExclusao.execute("delete from risco where cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from aulaavulsadiaria where cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from itemtaxapersonal where aluno in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from vinculo where cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from alunohorarioturma where cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from historicopontos where cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from aulaconfirmada where cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from demandahorarioturma where cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("update indicado set cliente  = null where cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from orcamento where cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from malingenviados where cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from conversaolead  where lead in (select codigo from lead  where cliente in (" + obterCodigos(codClienteDeletar)+"))");
                    stmExclusao.execute("delete from lead where cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from optin where cliente in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from cliente where codigo in (" + obterCodigos(codClienteDeletar)+")");
                    stmExclusao.execute("delete from movpagamento m where pessoa in (" + obterCodigos(codPessoasDeletar) + ") and recibopagamento is null");
                    stmExclusao.execute("delete from movpagamento m where pessoa in (" + obterCodigos(codPessoasDeletar) + ")");
                    stmExclusao.execute("delete from pessoaanexo where pessoa in (" + obterCodigos(codPessoasDeletar) + ")");
                    stmExclusao.execute("update socialmailpartener set pessoadestino = null  where pessoadestino in (" + obterCodigos(codPessoasDeletar) + ")");
                    stmExclusao.execute("update socialmailgrupoparticipante set participante  = null where participante in (" + obterCodigos(codPessoasDeletar) + ")");
                    stmExclusao.execute("update socialmailgrupo  set dono  = null where  dono  in (" + obterCodigos(codPessoasDeletar) + ")");
                    stmExclusao.execute("update socialmail  set pessoaorigem  = null where  pessoaorigem in (" + obterCodigos(codPessoasDeletar) + ")");
                    stmExclusao.execute("delete from pessoa where codigo in (" + obterCodigos(codPessoasDeletar) + ")");

                    con.commit();
                } catch (Exception e) {
                    con.rollback();
                    con.setAutoCommit(true);
                    e.printStackTrace();
                } finally {
                    con.setAutoCommit(true);
                }

            } else {
                nomesDuplicadosNaoRemovidos.add(nomeDuplicado);
            }
        }

        if(!nomesDuplicadosNaoRemovidos.isEmpty()) {
            System.out.println("Nomes duplicados não removidos:");
            System.out.println(obterCodigos(nomesDuplicadosNaoRemovidos));
        }
    }

    public static String obterCodigos(List obj) {
        return Arrays.toString(obj.toArray()).replace("[", "").replace("]", "");
    }
}
