package br.com.pactosolucoes.atualizadb.processo;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.nio.channels.FileChannel;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.SimpleTimeZone;
import java.util.TimeZone;
import negocio.comuns.utilitarias.Uteis;

import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.jdom.Element;
import org.postgresql.util.PSQLException;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import test.simulacao.LeitorXML;

public class CorrigirImportacao {

    public static Date getDateTime(Date data, int hora, int minuto, int segundo) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(data);
        cal.set(Calendar.HOUR_OF_DAY, hora);
        cal.set(Calendar.MINUTE, minuto);
        cal.set(Calendar.SECOND, segundo);

        return cal.getTime();
    }

    public static Date formataData(String data) throws Exception {
        TimeZone tzone = new SimpleTimeZone(-3 * 60 * 60 * 1000, "Brazil");
        TimeZone.setDefault(tzone);
        Date datamenor = new Date();
        Date datamaior = new Date();
        String dataSistema = "01/01/1900";
        String dataMaiorSistema = "01/01/2100";
        DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
        try {
            datamenor = df.parse(dataSistema);
            datamaior = df.parse(dataMaiorSistema);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        // verificar se data é nula
        if (data == null) {
            return null;
        } else {
            try {
                // converter data
                DateFormat formatter = new SimpleDateFormat("yyyyMMdd HH:mm");
                data = data.replaceAll("T", " ");
                if (data.length() > 14) {
                    data = data.substring(0, 14);
                }
                formatter.setLenient(false);
                Date date = (Date) formatter.parse(data);
                //verificar se a data é válida
                if (date.before(datamenor) || date.after(datamaior)) {
                    return null;
                }
                // retornar data
                if (data.length() > 8) {
                    date = getDateTime(date, Integer.parseInt(data.substring(9, 11)), Integer.parseInt(data.substring(12, 14)), 0);
                }
                return date;
            } catch (ParseException e) {
                try {
                    // converter data
                    DateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
                    formatter.setLenient(false);
                    Date date = (Date) formatter.parse(data);
                    //verificar se a data é válida
                    if (date.before(datamenor) || date.after(datamaior)) {
                        return null;
                    }
                    // retornar data
                    return date;
                    // caso existam erros na formatação, retornar null
                } catch (ParseException ex) {
                    try {
                        // converter data
                        DateFormat formatter = new SimpleDateFormat("yyyyMMdd");
                        formatter.setLenient(false);
                        Date date = (Date) formatter.parse(data);
                        //verificar se a data é válida
                        if (date.before(datamenor) || date.after(datamaior)) {
                            return null;
                        }
                        // retornar data
                        return date;
                    } catch (ParseException exc) {
                        return null;
                    }
                }
            }

        }
    }

    public static void inserirHorarios(Connection con) throws SQLException, Exception {
        ResultSet criarConsulta = SuperEntidade.criarConsulta("SELECT codigo FROM contrato", con);
        int i = 1;
        while (criarConsulta.next()) {
            System.out.println(i++);
            SuperEntidade.executarConsulta("INSERT INTO contratohorario(\n"
                    + "            valorespecifico, percentualdesconto, \n"
                    + "            contrato, horario)\n"
                    + "    VALUES (0, 0, " + criarConsulta.getInt("codigo") + ", 1);", con);
        }
    }

    public static void atualizarNrVezesSemana(Connection con) throws Exception {
        String sql = "UPDATE contratomodalidade cm set vezessemana = "
                + " ( SELECT count(m.codigo) from matriculaalunohorarioturma m"
                + " inner join horarioturma h on m.horarioturma = h.codigo "
                + " inner join turma t on t.codigo = h.turma "
                + " where contrato = cm.contrato and t.modalidade = cm.modalidade)"
                + " where exists(select mht.codigo from matriculaalunohorarioturma mht where mht.contrato = cm.contrato)";
        con.prepareStatement(sql).execute();
    }

    public static void retirarModalidade(Connection con, int modalidade) throws Exception {
        ResultSet queryCli = con.prepareStatement("select codigocliente, codigocontrato from "
                + "situacaoclientesinteticodw where codigocontrato is not null and idade < 15").executeQuery();
        while (queryCli.next()) {
            List<Integer> modalidades = new ArrayList<Integer>();
            ResultSet queryMod = con.prepareStatement("select modalidade, valormodalidade from "
                    + "contratomodalidade where contrato = " + queryCli.getInt("codigocontrato")).executeQuery();
            Double valorModalidade = 0.0;
            while (queryMod.next()) {
                if (queryMod.getInt("modalidade") == modalidade) {
                    valorModalidade = queryMod.getDouble("valormodalidade");
                } else {
                    modalidades.add(queryMod.getInt("modalidade"));
                }
            }
            if (!modalidades.isEmpty() && valorModalidade > 0.0) {
                Double valorPorModalidade = valorModalidade / modalidades.size();
                for (Integer mod : modalidades) {
                    con.prepareStatement("UPDATE contratomodalidade SET valormodalidade = " + valorPorModalidade
                            + ", valorfinalmodalidade = " + valorPorModalidade + " WHERE contrato = " + queryCli.getInt("codigocontrato")
                            + " and modalidade = " + mod).execute();
                }
                con.prepareStatement("DELETE FROM contratomodalidade WHERE contrato = " + queryCli.getInt("codigocontrato")
                        + " and modalidade = " + modalidade).execute();
            }

        }
    }

//	select distinct s.nomecliente from matriculaalunohorarioturma m
//	inner join horarioturma h on m.horarioturma = h.codigo
//	inner join turma t on t.codigo = h.turma
//	inner join situacaoclientesinteticodw s on s.codigocontrato = m.contrato
//	where s.idade < 15 and t.modalidade = 3
    public static void corrigirColaboradoresDuplicados(Connection con) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT p.nome FROM colaborador col ");
        sql.append(" INNER JOIN pessoa p ON p.codigo = col.pessoa ");
        sql.append(" GROUP by p.nome ");
        sql.append(" HAVING COUNT(col.codigo) > 1 ");
        sql.append(" ORDER BY p.nome ");
        ResultSet query = con.prepareStatement(sql.toString()).executeQuery();

        while (query.next()) {
            StringBuilder sqlCol = new StringBuilder();
            sqlCol.append("SELECT col.codigo FROM colaborador col ");
            sqlCol.append("INNER JOIN pessoa p ON p.codigo = col.pessoa ");
            sqlCol.append("WHERE p.nome LIKE '").append(query.getString("nome")).append("' ORDER BY codigo ");
            ResultSet queryCol = con.prepareStatement(sqlCol.toString()).executeQuery();
            boolean segundo = false;
            Integer col1 = null;
            Integer col2 = null;
            while (queryCol.next()) {
                if (segundo) {
                    col2 = queryCol.getInt("codigo");
                } else {
                    col1 = queryCol.getInt("codigo");
                    segundo = true;
                }
            }
            if (!UteisValidacao.emptyNumber(col1) && !UteisValidacao.emptyNumber(col2)) {
                StringBuilder sqlChange = new StringBuilder();
                sqlChange.append("UPDATE questionariocliente set consultor = ? where consultor = ?; ");
                sqlChange.append("UPDATE contrato set consultor = ? where consultor = ?; ");
                sqlChange.append("UPDATE grupocolaboradorparticipante set colaboradorparticipante = ? where colaboradorparticipante = ?; ");
                sqlChange.append("UPDATE historicovinculo set colaborador = ? where colaborador = ?; ");
                sqlChange.append("UPDATE risco set colaborador = ? where colaborador = ?; ");
                sqlChange.append("UPDATE usuario set colaborador = ? where colaborador = ?; ");
                sqlChange.append("UPDATE vinculo set colaborador = ? where colaborador = ?; ");
                sqlChange.append("UPDATE reciboclienteconsultor set consultor = ? where consultor = ?; ");
                sqlChange.append("UPDATE tipocolaborador set colaborador = ? where colaborador = ?; ");
                sqlChange.append("UPDATE historicovinculo set colaborador = ? where colaborador = ?; ");
                sqlChange.append("UPDATE horarioturma set professor = ? where professor = ?; ");
                sqlChange.append("delete from colaborador where codigo = ?; ");

                PreparedStatement stm = con.prepareStatement(sqlChange.toString());
                int j = 1;
                for (int i = 0; i < 11; i++) {
                    stm.setInt(j++, col1);
                    stm.setInt(j++, col2);
                }
                stm.setInt(j++, col2);
                stm.execute();
            }
        }
    }

    public static void deletesContrato(Connection con) throws Exception {
        ResultSet count = SuperFacadeJDBC.criarConsulta("SELECT COUNT(CODIGO) FROM CONTRATO WHERE codigo > 50000 and codigo < 100000", con);
        count.next();
        int total = count.getInt(1);
        ResultSet conts = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM CONTRATO WHERE codigo > 50000 and codigo < 100000", con);
        int i = 1;
        while (conts.next()) {
            System.out.println(i++ + "/" + total);
            try {
                SuperFacadeJDBC.executarConsulta("DELETE FROM contrato WHERE codigo = " + conts.getInt("codigo"), con);
            } catch (PSQLException e) {
                SuperFacadeJDBC.executarConsulta("DELETE FROM periodoacessocliente where contratobaseadorenovacao  =  " + conts.getInt("codigo"), con);
                SuperFacadeJDBC.executarConsulta("DELETE FROM contrato WHERE codigo = " + conts.getInt("codigo"), con);
            }

        }
    }

    public static void corrigirResponsaveisPagamento(Connection con) throws Exception {
        LeitorXML leitorXML = new LeitorXML();

        ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo, username from usuario", con);
        Map<String, Integer> usuarios = new HashMap<String, Integer>();
        while (rs.next()) {
            usuarios.put(rs.getString("username"), rs.getInt("codigo"));
        }
        Map<Integer, Integer> pagamentoResponsavel = new HashMap<Integer, Integer>();
        List<Element> listaCliente = leitorXML.lerXML("D:\\pagamentos.xml");
        for (Element cliente : listaCliente) {
//            System.out.println(retirarNulo(cliente, "usuario"));
            String nome = retirarNulo(cliente, "usuario");
            if (!UteisValidacao.emptyString(nome)) {
                Integer codigoZW = usuarios.get(nome);
                pagamentoResponsavel.put(Integer.valueOf(retirarNulo(cliente, "id_recebe")), codigoZW);
            }
        }
        int i = 1;
        int size = listaCliente.size();
        Set<Integer> keySet = pagamentoResponsavel.keySet();
        for (Integer key : keySet) {
            System.out.println(i++ + "/" + size);
            try {
                SuperFacadeJDBC.executarConsulta("UPDATE contrato SET responsavelcontrato = "
                        + pagamentoResponsavel.get(key) + " WHERE id_externo = " + key, con);

                SuperFacadeJDBC.executarConsulta("UPDATE recibopagamento SET responsavellancamento = "
                        + pagamentoResponsavel.get(key) + " WHERE codigo IN (SELECT recibopagamento FROM movpagamento "
                        + "WHERE id_recebe = " + key + ")", con);
            } catch (Exception e) {
                e.printStackTrace();
            }

        }

    }

    public static String retirarNulo(Element e, String campo) {
        try {
            return retirarNull(e.getAttributeValue(campo));
        } catch (Exception ie) {
            return null;
        }
    }

    public static String retirarNull(String valor) {
        if (valor.equals("null") || valor.equals("")) {
            return null;
        } else {
            return valor;
        }
    }

    public static void corrigirPagamentos(Connection con) {
        try {
            String sql1 = "SELECT codigo FROM movpagamento mp WHERE formapagamento = 3 AND valor in "
                    + "(select valor from movpagamento where recibopagamento  = mp.recibopagamento "
                    + "and codigo <> mp.codigo and formapagamento = 4)";
            ResultSet rs1 = SuperFacadeJDBC.criarConsulta(sql1, con);
            while (rs1.next()) {
                SuperFacadeJDBC.executarConsulta("DELETE FROM movpagamento WHERE codigo = " + rs1.getInt("codigo"), con);
            }

            String sql2 = "SELECT codigo FROM movpagamento mp WHERE formapagamento = 6 AND valor in "
                    + "(select valor from movpagamento where recibopagamento  = mp.recibopagamento "
                    + "and codigo <> mp.codigo and formapagamento = 3)";
            ResultSet rs2 = SuperFacadeJDBC.criarConsulta(sql2, con);
            while (rs2.next()) {
                SuperFacadeJDBC.executarConsulta("DELETE FROM movpagamento WHERE codigo = " + rs2.getInt("codigo"), con);
            }

            SuperFacadeJDBC.executarConsulta("DELETE FROM movpagamento WHERE codigo = 374992", con);
            SuperFacadeJDBC.executarConsulta("DELETE FROM movpagamento WHERE codigo = 375004", con);
            SuperFacadeJDBC.executarConsulta("DELETE FROM movpagamento WHERE codigo = 374998", con);
            SuperFacadeJDBC.executarConsulta("DELETE FROM movpagamento WHERE codigo = 375052", con);
            SuperFacadeJDBC.executarConsulta("DELETE FROM movpagamento WHERE codigo = 374904", con);

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public static void addAutorizacaoCartao(Connection con) throws Exception {
        LeitorXML leitorXML = new LeitorXML();
        String[] arqs = new String[]{"FormasAguasClaras",
            "FormasAsaNorte",
            "FormasLagoNorte",
            "FormasSudoeste"};
        for (String arq : arqs) {
            System.out.println("---------------------" + arq + "-----------------------------");
            List<Element> listaPagamentos = leitorXML.lerXML("D:\\" + arq + ".xml");
            Map<String, String> mapa = new HashMap<String, String>();
            for (Element forma : listaPagamentos) {
                String autorizacao = retirarNulo(forma, "NomeOuAuto");
                String id_recebe = retirarNulo(forma, "Id_Recebe");
                if (!UteisValidacao.emptyString(autorizacao)) {
                    mapa.put(id_recebe, autorizacao);
                }
            }
            Set<String> keySet = mapa.keySet();
            for (String key : keySet) {
                try {
                    SuperFacadeJDBC.executarConsulta("UPDATE movpagamento SET autorizacaocartao = '" + mapa.get(key)
                            + "' where formapagamento in (4,5,9) and id_recebe = " + key, con);
                    System.out.println("UPDATE movpagamento SET autorizacaocartao = '" + mapa.get(key)
                            + "' where id_recebe = " + key + ";");
                } catch (Exception e) {
                }
            }
        }
    }

    public static void corrigirFormaPagamentoCartaoDebito(Connection con) throws Exception {
        LeitorXML leitorXML = new LeitorXML();
        List<Element> listaPagamentos = leitorXML.lerXML("D:\\pagamentos.xml");
        Map<Integer, Integer> idRecebeidPessoa = new HashMap<Integer, Integer>();
        for (Element forma : listaPagamentos) {
            idRecebeidPessoa.put(Integer.valueOf(retirarNulo(forma, "id_recebe")), Integer.valueOf(retirarNulo(forma, "id_pessoa")));
        }

        Map<Integer, Integer> idPessoaPessoa = new HashMap<Integer, Integer>();
        ResultSet rspes = SuperFacadeJDBC.criarConsulta("SELECT idexterno, codigo FROM  pessoa WHERE idexterno is not null", con);
        while (rspes.next()) {
            idPessoaPessoa.put(rspes.getInt("idexterno"), rspes.getInt("codigo"));
        }

        ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM  formapagamento  WHERE tipoformapagamento LIKE 'CD' LIMIT 1;", con);
        int codigoFormaDebito = 0;
        if (rs.next()) {
            codigoFormaDebito = rs.getInt("codigo");
        } else {
            throw new Exception("NÃO EXISTE A FORMA DE PAGAMENTO EM CARTÃO DE DÉBITO NESTE BANCO");
        }
        List<Element> listaCartoes = leitorXML.lerXML("D:\\FormasCartaoDebito.xml");

        for (Element forma : listaCartoes) {
            Integer idRecebe = Integer.valueOf(retirarNulo(forma, "Id_Recebe"));
            Integer idPessoa = idRecebeidPessoa.get(idRecebe);
            Integer codigoPessoa = idPessoaPessoa.get(idPessoa);
            Date data = formataData(retirarNulo(forma, "Dt_Vencimento"));
            String valorFormatado = retirarNulo(forma, "Valor");
            valorFormatado = valorFormatado.replaceAll(",", ".");
            Double valor = Double.parseDouble(valorFormatado);

            String sql1 = "select codigo from movpagamento where pessoa = " + codigoPessoa
                    + " and dataquitacao = '\n" + Uteis.getDataJDBC(data)
                    + "' and cast(valor as integer) = cast(" + valor + " as integer)";
            ResultSet rs1 = SuperFacadeJDBC.criarConsulta(sql1, con);
            if (rs1.next()) {
                Integer codigoCartao = rs1.getInt("codigo");
                SuperFacadeJDBC.executarConsulta("UPDATE movpagamento SET formapagamento = " + codigoFormaDebito
                        + " WHERE codigo = " + codigoCartao, con);
                System.out.println("MOVPAGAMENTO " + codigoCartao + " ATUALIZADO!");
            }
        }
    }

    public static void copiarFotos(Connection con) throws Exception {
        File diretorio = new File("D:\\CarpeDiem\\fotos");
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select pessoa.codigo from pessoa \n"
                + "inner join cliente on cliente.pessoa = pessoa.codigo and cliente.empresa = 1"
                + " AND cliente.situacao = 'AT'", con);
        List<Integer> cods = new ArrayList<Integer>();
        while (rs.next()) {
            cods.add(rs.getInt("codigo"));
        }
        int i = 0;
        String[] arquivos = diretorio.list();

        for (String arquivo : arquivos) {
            File arquivoOrigem = new File("D:\\CarpeDiem\\fotos\\" + arquivo);
            String ident = genKey("sereia", MidiaEntidadeEnum.FOTO_PESSOA,
                    String.valueOf(cods.get(i)));
            File arquivoDestino = new File("D:\\opt\\zw-photos\\" + ident);
            SuperFacadeJDBC.executarConsulta(String.format("update pessoa set fotokey = '%s' where codigo = %s", ident, cods.get(i)), con);
            copyFile(arquivoOrigem, arquivoDestino);
            System.out.println("copiando fotos pessoa " + cods.get(i));
            i++;

        }
    }

    public static void copyFile(File source, File destination) throws Exception {
        if (destination.exists()) {
            destination.delete();
        }
        FileChannel sourceChannel = null;
        FileChannel destinationChannel = null;
        try {
            sourceChannel = new FileInputStream(source).getChannel();
            destinationChannel = new FileOutputStream(destination).getChannel();
            sourceChannel.transferTo(0, sourceChannel.size(),
                    destinationChannel);
        } finally {
            if (sourceChannel != null && sourceChannel.isOpen()) {
                sourceChannel.close();
            }
            if (destinationChannel != null && destinationChannel.isOpen()) {
                destinationChannel.close();
            }
        }
    }

    public static String genKey(final String chave, MidiaEntidadeEnum tipo, final String identifier) throws Exception {
        return String.format("%s/%s/%s%s",
                chave,
                Uteis.encriptarZWInternal(tipo.name().toLowerCase()),
                Uteis.encriptarZWInternal(identifier),
                tipo.getExtensao());
    }

    private static Map<String, String> getMapa() {
        Map<String, String> mapa = new HashMap<String, String>();
        mapa.put("8638", "ADEMILSON JOSE COSTA	");
        mapa.put("8555", "ADRIANA MENEGHINI REGO	");
        mapa.put("8664", "ALBERTO GINESTE NETTO	");
        mapa.put("8563", "ALEXANDRA B.CAMPOS	");
        mapa.put("8606", "ALEXANDRE VELOSO DE MATOS	");
        mapa.put("8602", "ALINE CENCI	");
        mapa.put("8550", "ALINE FABIOLA VERONA	");
        mapa.put("8600", "AMANDA DE NADAI COSTA	");
        mapa.put("8679", "AMANDA ELOY	");
        mapa.put("8565", "ANA CAROLINA DAPPER	");
        mapa.put("8691", "ANA CAROLINA MARTINS GAVRILOFF	");
        mapa.put("8566", "ANA CLARA RIBAS DA SILVEIRA	");
        mapa.put("8574", "ANA CLAUDIA FRANCO	");
        mapa.put("8573", "ANA PAULA LAURIANO CARDOSO	");
        mapa.put("8572", "ANA PAULA SEKULIC MEDEIROS	");
        mapa.put("8552", "ANDERSON BRAUN	");
        mapa.put("8544", "ANDREA MUGNAINI MARCONDES	");
        mapa.put("8562", "ANDREA YAMASAKI	");
        mapa.put("8696", "ANGELA TODESCO	");
        mapa.put("8676", "AUDREY TSUNODA	");
        mapa.put("8629", "BARBARA HECK KONIG	");
        mapa.put("8674", "BARBARA RIBAS	");
        mapa.put("8640", "BEATRIZ MACHNICK	");
        mapa.put("8454", "BERNARDO SALLES	");
        mapa.put("8618", "BIANCA UHDRE	");
        mapa.put("8700", "CARINE DAL PIZZOL	");
        mapa.put("8631", "CARLIANA SCHEFFER	");
        mapa.put("8665", "CARLOS ALBERTO DIAS DE OLIVEIR	");
        mapa.put("8621", "CARLOS ALBERTO TASSI	");
        mapa.put("8647", "CAROLINE CAMARGO REGIO	");
        mapa.put("8637", "CASSIANA CURI KALACHE	");
        mapa.put("8684", "CASSIO BECKER DA CRUZ	");
        mapa.put("8657", "CATERINE VIKTORIA BZYL SOUZA	");
        mapa.put("8698", "CELIO WILSON OLIVEIRA	");
        mapa.put("8582", "CHRISTHIANN INASARIS DE SOUZA	");
        mapa.put("8625", "CHRISTINE FREIBERGER	");
        mapa.put("8668", "CLAUDIA LAGUENTE ORTEGA PER	");
        mapa.put("8667", "CRISTIANE MEIRA ASSUN??O	");
        mapa.put("8579", "DALVINA APARECIDA COSTA SILVA	");
        mapa.put("8644", "DAMARIS CHIULLO	");
        mapa.put("8628", "DANIELLE PIE	");
        mapa.put("8682", "DANIELLE REQUIÃO	");
        mapa.put("8622", "DANILO PADILHA	");
        mapa.put("8547", "DANTE LUIZ MATTIOLI	");
        mapa.put("8607", "DAVID TILLE GAERTNER	");
        mapa.put("8632", "DENISE MARIA KARPEN	");
        mapa.put("8542", "DIEGO LEONARDO STAMM PAZA	");
        mapa.put("8685", "DURVAL RAMOS JUNIOR	");
        mapa.put("8670", "EDSON PAULUSSON LOPES FER	");
        mapa.put("8687", "EDUARDO PEREIRA	");
        mapa.put("8695", "ELIANE CRISTINA MAINARDES	");
        mapa.put("8599", "ELIZABETE GIRALDI	");
        mapa.put("8596", "EMERSON JANGADA	");
        mapa.put("8683", "ERICA CHIN LEE	");
        mapa.put("8545", "EVANDRO KRUGEL	");
        mapa.put("8609", "EVELISE REIS DE SOUZA	");
        mapa.put("8597", "FABIANO DE OLIVEIRA SOBRAL	");
        mapa.put("8626", "FATIMA CHRISTOFOLETTI	");
        mapa.put("8636", "FELIPE ABIL RUSS SEBBEN	");
        mapa.put("8641", "FELIPE RODRIGUES SANTOS	");
        mapa.put("8635", "FERNANDA LOPES BERNARDONI	");
        mapa.put("8660", "FERNANDO ALARCON	");
        mapa.put("8624", "FLAVIA CHRISTOFOLETTI DOS	");
        mapa.put("8560", "FLAVIA COSTA DE ALMEIDA	");
        mapa.put("8575", "GABRIELA KEIKO KAWAMURA	");
        mapa.put("8543", "GIAN CARLO PIEROZAN	");
        mapa.put("8593", "GILSON MATUSZEWSKI	");
        mapa.put("8589", "GIOVANA WORLICZECK PASSERIN	");
        mapa.put("8619", "GUILHERME GON?ALVES DE QUEIRO	");
        mapa.put("8680", "HELOISA CALDAS FERREIRA	");
        mapa.put("8643", "ISABELA FUENTES AVILA	");
        mapa.put("8620", "ISABELI DE BARROS	");
        mapa.put("8645", "ISABELLE BREMER	");
        mapa.put("8577", "JANE ALEXANDRA PROSPERO	");
        mapa.put("8686", "JANETE FLORIANO	");
        mapa.put("8594", "JHONATAN LUCAS HANZEN GRERN	");
        mapa.put("8578", "JOAO SOARES NUNES	");
        mapa.put("8614", "JOSE ROBERTO DUMKE	");
        mapa.put("8580", "JULIANA MOURA MIGUEL	");
        mapa.put("8652", "JULIO POLONSKI	");
        mapa.put("8611", "KAREN CRISTINA FARIA MARTINS	");
        mapa.put("8699", "KARINA DE BRITO	");
        mapa.put("8661", "KELLIN TOMAZ	");
        mapa.put("8584", "KHADER DE M. HAJJAR	");
        mapa.put("8605", "LAIS MELLO OLDEMBERGAS	");
        mapa.put("8567", "LARISSA DIAS MANCIO	");
        mapa.put("8554", "LAURA BEATRIZ KARAM	");
        mapa.put("8658", "LAURA PRATIS	");
        mapa.put("8653", "LEONARDO GABARDO AGUIAR	");
        mapa.put("8608", "LEX KOZLIK	");
        mapa.put("8587", "LIONEL HENRIQUE DE MATOS CESCH	");
        mapa.put("8586", "LUCIANO MARTIM DURSKI	");
        mapa.put("8649", "MANUELA TOURINHO ORUE	");
        mapa.put("8521", "MARCE REGINA COSTA	");
        mapa.put("8615", "MARCELI CAMARGO	");
        mapa.put("8603", "MARCELO CHAVES CENEI	");
        mapa.put("8650", "MARCELO FURTADO	");
        mapa.put("8656", "MARCIO AURELIO SILVERIO	");
        mapa.put("8677", "MARIA APARECIDA FATIGA	");
        mapa.put("8569", "MARIA CAROLINA POLYDORO VIEIRA	");
        mapa.put("8588", "MARIA CRISTINA PASSERINO	");
        mapa.put("8561", "MARIA ELENA COLOMER MALLEA	");
        mapa.put("8617", "MARIA ESTELA OSPEDAL	");
        mapa.put("8559", "MARIA HELENA PARCIDES	");
        mapa.put("8673", "MARIA HELENA WOREBIEN DOS	");
        mapa.put("8553", "MARIA ZILA COSTA DA SILVA	");
        mapa.put("8546", "MARIANA EMY HIRAI	");
        mapa.put("8669", "MARIANA TAMMENHAIN	");
        mapa.put("8604", "MARTA DIAS DE OLIVEIRA	");
        mapa.put("8690", "MICHELLE NOBRE MAIOLLI	");
        mapa.put("8540", "MIKHAELLA CAMARGO	");
        mapa.put("8634", "ONDINA GOMES ANTUNES	");
        mapa.put("8694", "PEDRO ALEXANDRE DE SALLES	");
        mapa.put("8633", "PEDRO EUGENIO DE LIMA	");
        mapa.put("8549", "POLYANA MATTOS MELLA	");
        mapa.put("8648", "PRISCILLA SHARON COMICHOLLI	");
        mapa.put("8692", "RAFAEL VACCARI	");
        mapa.put("8630", "RAFAELA WEIGERT	");
        mapa.put("8541", "RAFAELLA MOZENA	");
        mapa.put("8598", "RAIMUNDO SOUSA	");
        mapa.put("8639", "RAPHAEL KAYO HENRIQU DE ALMEID	");
        mapa.put("8689", "RENAN  ALVES RIBEIRO	");
        mapa.put("8571", "RENATA SETTI NOGUEIRA	");
        mapa.put("8558", "RINALDO SEMPREBON DE FIGUEI	");
        mapa.put("8548", "ROBERTO OLIVER LAGES	");
        mapa.put("8590", "ROBSON FONSECA	");
        mapa.put("8681", "ROSANY PEREIRA	");
        mapa.put("8666", "ROSELI DA SILVEIRA MEIRA	");
        mapa.put("8585", "RUBENS DAMASCENO	");
        mapa.put("8616", "SAMANTA LARA NAKAZATO	");
        mapa.put("8610", "SAMUEL JOSE SABINO PERIE	");
        mapa.put("8671", "SANDRA BZYL	");
        mapa.put("8627", "SANDRA REGINA HECK	");
        mapa.put("8662", "SERGIO MIGUEL DE SOUZA	");
        mapa.put("8655", "SILVANA MENDES DE OLIVEIRA	");
        mapa.put("8556", "SIMONE WOLFF FRANCO	");
        mapa.put("8613", "SOLANGE TRAVALAO FARIA	");
        mapa.put("8568", "STEL DIAS MANCIO	");
        mapa.put("8612", "STELLA TRAVALAO FARIA DUMK	");
        mapa.put("8675", "SYLVIA LYKA HOSHINA	");
        mapa.put("8592", "TANIA CONCEIÇÃO DE OLIVEIRA HE	");
        mapa.put("8678", "TANIA RITA TARDIVOS	");
        mapa.put("8570", "TAYANE HORSTMANN CABRAL	");
        mapa.put("8581", "THAIS DE VASCONCELLOS LOP	");
        mapa.put("8595", "THIAGO RICARDO SEELY	");
        mapa.put("8651", "ULISSES PEGAS LEAO SILVA	");
        mapa.put("8576", "VANESSA DOS SANTOS CORDEIRO	");
        mapa.put("8591", "VANIA G MATUSZEWSKI	");
        mapa.put("8688", "VANIA VIDAL TRINDADE	");
        mapa.put("8243", "VINICIUS LEMOS	");
        mapa.put("8601", "VINICIUS MALUF	");
        mapa.put("8557", "VIVIAN WOLFF	");
        mapa.put("8693", "VLADIMIR RISSARDI	");
        mapa.put("8551", "ZENILDA CHINISKI	");
        return mapa;
    }

    public static void atribuirMatriculas(Connection con) throws Exception {
        Map<String, String> mapa = getMapa();
        Integer cont = 0;
        for (String k : mapa.keySet()) {
            cont++;
            String nomeXLS = mapa.get(k).trim();
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select cliente.codigomatricula, cliente.matricula, pessoa.nome, cliente.matriculaexterna from pessoa \n"
                    + "inner join cliente on cliente.pessoa = pessoa.codigo"
                    + " AND pessoa.nome like '" + nomeXLS + "%'", con);
            if (rs.next()) {
                Integer codigoMatriculaXLS = rs.getInt("codigomatricula");
                String matriculaXLS = rs.getString("matricula");
                ResultSet rsZW = SuperFacadeJDBC.criarConsulta("select cliente.situacao, cliente.matricula,cliente.codigomatricula, pessoa.nome, cliente.matriculaexterna from pessoa \n"
                        + "inner join cliente on cliente.pessoa = pessoa.codigo"
                        + " AND cliente.codigomatricula = " + k, con);
                if (rsZW.next()) {
                    if (rsZW.getString("situacao").equals("AT")) {
                        System.out.println(cont.toString() + " - " + k + " - O aluno " + nomeXLS + " não pode ter a matricula alterada manualmente pois sua matricula está em uso por um aluno ativo.");
                    } else {
                        Integer codigoMatriculaZW = rsZW.getInt("codigomatricula");
                        String matriculaZW = rsZW.getString("matricula");
                        String nomeZW = rsZW.getString("nome");
                        ResultSet rsMat = SuperFacadeJDBC.criarConsulta("select coalesce(matricula) as m from numeromatricula", con);
                        if (rsMat.next()) {
                            Integer mat = rsMat.getInt("m") + 1;
                            String novaMat = mat.toString();
                            if (novaMat.length() < 6) {
                                for (int i = novaMat.length(); i < 6; i++) {
                                    novaMat = "0" + novaMat;
                                }
                            }
                            SuperFacadeJDBC.executarConsulta("UPDATE cliente SET codigomatricula = "
                                    + mat + ", matricula = '" + novaMat
                                    + "' where codigomatricula = "
                                    + codigoMatriculaZW, con);

                            SuperFacadeJDBC.executarConsulta("UPDATE numeromatricula SET matricula = "
                                    + (mat + 1), con);
                            SuperFacadeJDBC.executarConsulta("UPDATE cliente set codigomatricula = " + codigoMatriculaZW
                                    + ", matricula = " + matriculaZW + " where codigomatricula = " + codigoMatriculaXLS, con);
                            System.out.println(cont.toString() + " - " + k + " - O aluno " + nomeXLS + " trocou de matricula com o aluno " + nomeZW + ", que agora tem a matricula " + novaMat);

                        }

                    }
                } else {
                    String novaMat = k;
                    if (novaMat.length() < 6) {
                        for (int i = novaMat.length(); i < 6; i++) {
                            novaMat = "0" + novaMat;
                        }
                    }
                    SuperFacadeJDBC.executarConsulta("UPDATE cliente set codigomatricula = " + k
                            + ", matricula = " + novaMat + " where codigomatricula = " + codigoMatriculaXLS, con);
                    System.out.println(cont.toString() + " - " + k + " - O aluno " + nomeXLS + " agora tem a matricula " + novaMat);
                }

            } else {
                System.out.println(cont.toString() + " - " + "####NÃO ENCONTRADO: " + nomeXLS + " - MAT. ALTERAR: " + k);
            }
        }
    }

    public static void atualizarDatasEvo(Connection con) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select datalancamento, vigenciade, codigo from contrato where id_venda is not null", con);
        while (rs.next()) {
            Date datalancamento = rs.getDate("datalancamento");
            Date vigenciade = rs.getDate("vigenciade");
            Integer codigo = rs.getInt("codigo");
            SuperFacadeJDBC.executarConsulta("UPDATE contrato SET datalancamento = '"
                    + Uteis.getDataFormatoBD(vigenciade) + "', vigenciade = '"
                    + Uteis.getDataFormatoBD(datalancamento) + "' where codigo = " + codigo, con);

            SuperFacadeJDBC.executarConsulta("UPDATE historicocontrato SET dataregistro = '"
                    + Uteis.getDataFormatoBD(vigenciade) + "', datainiciosituacao = '"
                    + Uteis.getDataFormatoBD(datalancamento) + "' where tipohistorico in ('RN','MA','RE') AND contrato = " + codigo, con);

            SuperFacadeJDBC.executarConsulta("UPDATE situacaoclientesinteticodw SET datalancamentocontrato = '"
                    + Uteis.getDataFormatoBD(vigenciade) + "', datavigenciade = '"
                    + Uteis.getDataFormatoBD(datalancamento) + "' where codigocontrato = " + codigo, con);


            ResultSet rsRecibo = SuperFacadeJDBC.criarConsulta("select recibopagamento from pagamentomovparcela where movparcela "
                    + " in ( select codigo from movparcela where contrato  = " + codigo + ")", con);
            if (rsRecibo.next()) {
                SuperFacadeJDBC.executarConsulta("UPDATE recibopagamento SET data = '"
                        + Uteis.getDataFormatoBD(vigenciade) + "' where codigo = " + rsRecibo.getInt("recibopagamento"), con);
            }

            ResultSet rsMovPagamento = SuperFacadeJDBC.criarConsulta("select movpagamento from pagamentomovparcela where movparcela "
                    + " in ( select codigo from movparcela where contrato  = " + codigo + ")", con);
            if (rsMovPagamento.next()) {
                SuperFacadeJDBC.executarConsulta("UPDATE movpagamento SET dataLancamento = '"
                        + Uteis.getDataFormatoBD(vigenciade) + "' where codigo = " + rsMovPagamento.getInt("movpagamento"), con);
            }

            ResultSet rsMovParcela = SuperFacadeJDBC.criarConsulta("select codigo from movparcela where contrato = " + codigo, con);
            Date dataParcela = datalancamento;
            Date datalancamentoreal = (Date)vigenciade.clone();
            while (rsMovParcela.next()) {
                SuperFacadeJDBC.executarConsulta("UPDATE movparcela SET dataregistro = '"
                        + Uteis.getDataFormatoBD(datalancamentoreal) + "', datavencimento = '"
                        + Uteis.getDataFormatoBD(dataParcela) + "', datacobranca = '"
                        + Uteis.getDataFormatoBD(dataParcela) + "' where codigo = " + rsMovParcela.getInt("codigo"), con);
                dataParcela = Uteis.somarCampoData(dataParcela, Calendar.MONTH, 1);
            }
            atualizarProdutos(codigo, con);

        }

    }

    public static void atualizarProdutos( Connection con) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select datalancamento, vigenciade, codigo from contrato where id_venda is not null", con);
        while (rs.next()) {
            atualizarProdutos(rs.getInt("codigo"), con);
        }
    }
    public static void atualizarProdutos(Integer contrato, Connection con) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select datalancamento, vigenciade from contrato "
                + "where codigo = " + contrato, con);
        while (rs.next()) {
            System.out.println(contrato);
            Date dataLancamento = rs.getDate("datalancamento");
            Date vigenciade = rs.getDate("vigenciade");
            ResultSet rsMovProduto = SuperFacadeJDBC.criarConsulta("select codigo, descricao from movproduto where contrato = " + contrato
                    + " order by codigo ", con);
            Date dataProduto = rs.getDate("vigenciade");
            while (rsMovProduto.next()) {
                String mes = Uteis.getDataAplicandoFormatacao(dataProduto, "MM/yyyy");
                String descricao = rsMovProduto.getString("descricao");
                if (descricao.startsWith("PLANO IMPORTACAO")) {
                    SuperFacadeJDBC.executarConsulta("UPDATE movproduto SET datalancamento = '"
                            + Uteis.getDataFormatoBD(dataLancamento) + "', datainiciovigencia = '"
                            + Uteis.getDataFormatoBD(vigenciade) + "', mesreferencia = '"
                            + mes + "', descricao = 'PLANO IMPORTACAO - " + mes
                            + "' where codigo = " + rsMovProduto.getInt("codigo"), con);
                } else {
                    SuperFacadeJDBC.executarConsulta("UPDATE movproduto SET datalancamento = '"
                            + Uteis.getDataFormatoBD(dataLancamento) + "', datainiciovigencia = '"
                            + Uteis.getDataFormatoBD(vigenciade) + "', mesreferencia = '"
                            + mes + "' where codigo = " + rsMovProduto.getInt("codigo"), con);
                }
                dataProduto = Uteis.somarCampoData(dataProduto, Calendar.MONTH, 1);
                
            }
        }
    }

    public static void atualizarBancoTreino(Connection conZW) throws Exception {
        Connection conTR = DriverManager.getConnection("************************************************", "postgres", "pactodb");
        Map<String, String> mapa = getMapa();
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigocliente from clientesintetico", conTR);
        while (rs.next()) {
            ResultSet rsZ = SuperFacadeJDBC.criarConsulta("select codigomatricula from cliente where codigo = " + rs.getInt("codigocliente"), conZW);
            if (rsZ.next()) {
                SuperFacadeJDBC.executarConsulta("update clientesintetico set matricula = " + rsZ.getString("codigomatricula")
                        + " where codigocliente = " + rs.getInt("codigocliente"), conTR);
            }

        }
        System.out.println("#################### ATUALIZEI O TREINO ############################ ");
    }

    public static void main(String[] args) {
        try {
//            Connection con = DriverManager.getConnection("*************************************************", "postgres", "pactodb");

//            copiarFotos(con);
//            atribuirMatriculas(con);
//            atualizarBancoTreino(con);
//            atualizarDatasEvo(con);
//            atualizarProdutos(con);
            
            Connection conOamd2 = DriverManager.getConnection("*************************************", "postgres", "pactodb");
            ResultSet rs2 = SuperFacadeJDBC.criarConsulta("SELECT * FROM empresa ", conOamd2);
            System.out.println("empresa;aulas;horarios;alunos;");
             while (rs2.next()) {
                 try {
                 String hosttr = rs2.getString("hostBD");
                 String portatr = rs2.getString("porta");
                 String nomebdtr = rs2.getString("nomeBD");
                 String usertr = rs2.getString("userBD");
                 String senhatr = rs2.getString("passwordBD");
                 String urltr = "jdbc:postgresql://" + hosttr + ":" + portatr + "/" + nomebdtr;
                 Connection conTr = DriverManager.getConnection(urltr, usertr, senhatr);
                 ResultSet rsAula = SuperFacadeJDBC.criarConsulta("SELECT count(codigo) as c FROM aula", conTr);
                 rsAula.next();
                 if(rsAula.getInt("c") > 0){
                     String empresa = "";
                     ResultSet rEmpresa = SuperFacadeJDBC.criarConsulta("SELECT nome FROM empresa", conTr);
                     if(rEmpresa.next()){
                         empresa = rEmpresa.getString("nome");
                     }
                     int aulas = rsAula.getInt("c");
                     ResultSet rsAulaHorario = SuperFacadeJDBC.criarConsulta("SELECT count(codigo) as c FROM aulahorario", conTr);
                     rsAulaHorario.next();
                     ResultSet rsAulaAlunos = SuperFacadeJDBC.criarConsulta("SELECT count(codigo) as c FROM aulaaluno", conTr);
                     int hr = rsAulaHorario.getInt("c");
                     rsAulaAlunos.next();
                     int aluno = rsAulaAlunos.getInt("c");
                     System.out.println(empresa+";"+aulas+";"+hr+";"+aluno+";");
                 }    
                 } catch (Exception e) {
                 }
                 
             }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
