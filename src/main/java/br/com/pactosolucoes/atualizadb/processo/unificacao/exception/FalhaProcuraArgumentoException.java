package br.com.pactosolucoes.atualizadb.processo.unificacao.exception;

import br.com.pactosolucoes.atualizadb.processo.unificacao.parametrizacao.ParametroObrigatorioUnificacaoEnum;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 01/04/2019
 */
public class FalhaProcuraArgumentoException extends UnificadorGenericException {

    private static final String MESSAGE = "Se espera o argumento (%s) mas não foi possível encontrar nos argumentos passados: \n(%s)";

    public FalhaProcuraArgumentoException(ParametroObrigatorioUnificacaoEnum unificadorArgsMissing, String... args) {
        super(String.format(MESSAGE, unificadorArgsMissing, Arrays.toString(args)));
    }
}
