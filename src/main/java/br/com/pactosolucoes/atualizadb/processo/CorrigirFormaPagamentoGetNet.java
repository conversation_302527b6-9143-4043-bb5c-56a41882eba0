/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.CaixaContaVO;
import negocio.comuns.financeiro.CaixaVO;
import negocio.comuns.financeiro.CartaoCreditoTO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeTO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.EstornoReciboVO;
import negocio.comuns.financeiro.LoteVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RemessaService;
import servicos.impl.dcc.getnet.LayoutRemessaGetNetDCO;

/**
 *
 * <AUTHOR>
 */
public class CorrigirFormaPagamentoGetNet {

    public static final String sqlPagamentos = "select ri.codigo as item ,ri.movparcela, ri.pessoa,mp.codigo as pagamento,mp.recibopagamento,mp.datalancamento, nfe.movpagamento as mpemitido,"
            + " nfe.recibopagamento as reciboemitido, nfe.codigo as nfe, cf.codigo as cupom, cf.recibo as cfrecibo, cf.movpagamento as cfpagamento\n"
            + "from movpagamento mp inner join remessaitem ri on mp.codigo =ri.movpagamento inner join remessa re on re.codigo = ri.remessa\n"
            + " inner join conveniocobranca co on re.conveniocobranca = co.codigo \n"
            + "left join nfseemitida  nfe on mp.codigo = nfe.movpagamento or mp.recibopagamento = nfe.recibopagamento \n"
            + "left join cupomfiscal cf on mp.codigo = cf.movpagamento or mp.recibopagamento = cf.recibo\n"
            + " where mp.operadoracartao is null and co.tipoconvenio = 8 ";

    public static void main(String... args) {
        try {
            Connection c = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "green");
            Conexao.guardarConexaoForJ2SE(c);
            ajustarParcelas(c);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void processoAjustarParcelas(Connection con) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        ajustarParcelas(con);
    }

    public static void ajustarParcelas(Connection con) throws Exception {
        RemessaService remessaService = new RemessaService();
        SuperFacadeJDBC.executarConsultaUpdate("update formapagamento set somentefinanceiro  = 'f'  where codigo =  " + remessaService.getFormaPagamentoCartaoCredito().getCodigo(),con);
        Uteis.logar(null, "###### Finalizando CorrigirFormaPagamentoGetNet ########\n");
        ResultSet consulta = SuperFacadeJDBC.criarConsulta(sqlPagamentos, con);
        while (consulta.next()) {
            try {

                SuperFacadeJDBC.executarConsultaUpdate("update remessaitem set movpagamento = null where codigo =  " + consulta.getInt("item"), con);
                if (consulta.getInt("nfe") > 0) {
                    SuperFacadeJDBC.executarConsultaUpdate("update nfe set recibopagamento = null, movpagamento = null where codigo =  " + consulta.getInt("nfe"), con);
                }
                if (consulta.getInt("cupom") > 0) {
                    SuperFacadeJDBC.executarConsultaUpdate("update cupomfiscal set recibo = null, movpagamento = null where codigo =  " + consulta.getInt("cupom"), con);
                }

                estornarRecibo(consulta.getInt("recibopagamento"));

                RemessaItemVO item = FacadeManager.getFacade().getZWFacade().getRemessaItem().consultarPorChavePrimaria(consulta.getInt("item"), Uteis.NIVELMONTARDADOS_TODOS);
                int indexInicio = item.getRemessa().getHead().indexOf("DataDeposito=") + 13;
                int indexfim = indexInicio + 8;
                item.getRemessa().setDataPrevistaCredito(Uteis.somarDias(Calendario.getDate("ddMMyyyy", item.getRemessa().getHead().substring(indexInicio, indexfim)), 30));
                        
                remessaService.incluirPagamentoItem(item);
                FacadeManager.getFacade().getZWFacade().getRemessaItem().alterar(item);

                ResultSet consultaRecibo = SuperFacadeJDBC.criarConsulta("select * from pagamentomovparcela where movparcela =" + item.getMovParcela().getCodigo(), con);
                consultaRecibo.next();
                SuperFacadeJDBC.executarConsultaUpdate("update recibopagamento set data =  '" + Uteis.getDataJDBCTimestamp(consulta.getTimestamp("datalancamento")) + "' where codigo =  " + consultaRecibo.getInt("recibopagamento"), con);
                SuperFacadeJDBC.executarConsultaUpdate("update movpagamento set datalancamento =  '" + Uteis.getDataJDBCTimestamp(consulta.getTimestamp("datalancamento")) + "', "
                        + " datapagamento = '" + Uteis.getDataJDBCTimestamp(consulta.getTimestamp("datalancamento")) + "', dataquitacao = '" + Uteis.getDataJDBC(consulta.getDate("datalancamento")) + "' where codigo =  " + consultaRecibo.getInt("movpagamento"), con);
                if (consulta.getInt("nfe") > 0) {
                    if (consulta.getInt("reciboemitido") > 0) {
                        SuperFacadeJDBC.executarConsultaUpdate("update nfe set recibopagamento =  " + consultaRecibo.getInt("recibopagamento") + " where codigo =  " + consulta.getInt("nfe"), con);
                    }
                    if (consulta.getInt("mpemitido") > 0) {
                        SuperFacadeJDBC.executarConsultaUpdate("update nfe set movpagamento =  " + consultaRecibo.getInt("movpagamento") + " where codigo =  " + consulta.getInt("nfe"), con);
                    }
                }

                if (consulta.getInt("cupom") > 0) {
                    if (consulta.getInt("cfrecibo") > 0) {
                        SuperFacadeJDBC.executarConsultaUpdate("update cupomfiscal set recibo =  " + consultaRecibo.getInt("recibopagamento") + " where codigo =  " + consulta.getInt("cupom"), con);
                    }
                    if (consulta.getInt("cfpagamento") > 0) {
                        SuperFacadeJDBC.executarConsultaUpdate("update cupomfiscal set movpagamento =  " + consultaRecibo.getInt("movpagamento") + " where codigo =  " + consulta.getInt("cupom"), con);
                    }
                }

                System.out.println("Pagamento gerado para parcela nº " + item.getMovParcela().getCodigo());

            } catch (Exception e) {
                Uteis.logar(null, "Parcela: " + consulta.getInt("movparcela") + " teve problemas: " + e.getMessage() + "\n");
            }
        }

        Uteis.logar(null, "###### Finalizando CorrigirFormaPagamentoGetNet ########\n");

    }

    public static List<CaixaContaVO> preencherListaCaixaConta(Integer empresa) throws Exception {
        List<CaixaContaVO> listaContasAbrirCaixa = new ArrayList<CaixaContaVO>();
        List<ContaVO> lista = getFacade().getFinanceiro().getConta().consultarContasParaCaixa(empresa, Uteis.NIVELMONTARDADOS_TODOS);
        for (ContaVO obj : lista) {
            CaixaContaVO caixaContaVo = new CaixaContaVO();
            obj.setContaEscolhida(Boolean.TRUE);
            caixaContaVo.setContaVo(obj);
            caixaContaVo.setSaldoInicial(obj.getSaldoAtual());
            caixaContaVo.setSaldoFinal(obj.getSaldoAtual());
            listaContasAbrirCaixa.add(caixaContaVo);
        }
        return listaContasAbrirCaixa;
    }

    public static void estornarRecibo(Integer codigoRecibo) throws ConsistirException {

        try {
            ReciboPagamentoVO recibo = getFacade().getReciboPagamento().consultarPorChavePrimaria(codigoRecibo, Uteis.NIVELMONTARDADOS_TODOS);
            EstornoReciboVO estornoReciboVO = new EstornoReciboVO();
            estornoReciboVO.setReciboPagamentoVO(recibo);
            estornoReciboVO.setListaMovPagamento(getFacade().getMovPagamento().consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
            estornoReciboVO.setListaMovParcela(getFacade().getMovParcela().consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
            getFacade().getCupomFiscal().setCon(getFacade().getMovPagamento().getCon());
            //consulta as transações de cartão de crédito relacionadas aos contratos do recibo
            estornoReciboVO.montarListaTransacoes(estornoReciboVO.getListaMovParcela(), getFacade().getMovPagamento().getCon());

            getFacade().getReciboPagamento().estornarReciboPagamento(
                    estornoReciboVO,
                    getFacade().getMovPagamento(),
                    getFacade().getMovProdutoParcela(),
                    new CaixaVO(), "");

        } catch (Exception e) {
            throw new ConsistirException("Durante o estorno: " + e.getMessage());
        }
    }

}
