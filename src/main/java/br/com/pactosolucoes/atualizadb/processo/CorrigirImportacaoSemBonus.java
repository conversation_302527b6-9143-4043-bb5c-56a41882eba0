package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.swing.JOptionPane;

import negocio.comuns.utilitarias.Uteis;

public class CorrigirImportacaoSemBonus {
	
	public static void main(String[] args) {
		try {
			Connection con1 = DriverManager.getConnection("***************************************************", "postgres", "pactodb");
//			int mesesEntreDatas = mesesEntreDatas(new Date("2012/10/01"), new Date("2013/02/28"));
//			JOptionPane.showMessageDialog(null, mesesEntreDatas);
			ajustarDatas(con1);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	private static void ajustarDatas(Connection con) throws Exception{
		
		String sql = "SELECT * FROM contrato WHERE vigenciaate  <> vigenciaateajustada "+
					"AND codigo NOT IN (select contrato from contratooperacao)";
		ResultSet query = con.prepareStatement(sql).executeQuery();
		int i = 1;
		while(query.next()){
			System.out.println( i++ + " - Atualizados dados de contrato com codigo = "+query.getInt("codigo"));
			con.prepareStatement("UPDATE contrato SET vigenciaate = vigenciaateajustada where codigo = "+query.getInt("codigo")).execute();
			Date vigenciaDe = query.getDate("vigenciade");
			Date vigenciaAteAjustada = query.getDate("vigenciaateajustada");
			int duracao = mesesEntreDatas(vigenciaDe, vigenciaAteAjustada);
			con.prepareStatement("UPDATE contratoduracao SET numeromeses = "+duracao+" where contrato = "+query.getInt("codigo")).execute();
			
			con.prepareStatement("UPDATE periodoacessocliente SET datafinalacesso = '"+Uteis.getDataJDBC(vigenciaAteAjustada)
					+"' where tipoacesso like 'CA' AND contrato = "+query.getInt("codigo")).execute();
			
		}
		
	}
	
	
	private static int mesesEntreDatas(Date vigenciade, Date vigenciaAteAjustada) throws Exception{
		
		long i = Uteis.nrDiasEntreDatasSemHoraZerada(vigenciade, vigenciaAteAjustada);
		return (int) (i/30);
	}

}
