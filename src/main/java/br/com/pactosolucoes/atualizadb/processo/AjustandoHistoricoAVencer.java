/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import static br.com.pactosolucoes.atualizadb.processo.AjustarLinhaDoTempoDeContratos.ajustarContratos;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.contrato.HistoricoContratoVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.crm.HistoricoContato;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.bi.ValidadorBusinessIntelligence;

/**
 *
 * <AUTHOR>
 */
public class AjustandoHistoricoAVencer {
     public static void main(String... args) {
        try {
            Connection c = DriverManager.getConnection("**********************************************************************", "postgres", "pactodb");
            Conexao.guardarConexaoForJ2SE(c);
            Integer empresa = (args.length > 1 ? Integer.parseInt(args[1]) : 0);
            ajustarHistoricoAVencer(c);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
     public static void ajustarHistorico(Connection con) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        ajustarHistoricoAVencer(con);
    }
     
    public static void ajustarHistoricoAVencer(Connection con) throws Exception {
        String consultaHistoricos = "select h.codigo, h.datainiciosituacao,h.datafinalsituacao,h.contrato, cl.codigomatricula, c.vigenciaateajustada from historicocontrato h inner join contrato c on c.codigo = h.contrato inner join cliente cl on cl.pessoa = c.pessoa where h.tipohistorico = 'AV' and h.datafinalsituacao - h.datainiciosituacao > '20 days' and h.datafinalsituacao > now() and c.situacao = 'AT'";
        ResultSet consulta = SuperFacadeJDBC.criarConsulta(consultaHistoricos,con);
        String contratos = "";
        String matriculas = "";
         while (consulta.next()) {
             List<HistoricoContratoVO> historicos = getFacade().getHistoricoContrato().consultarPorContrato(consulta.getInt("contrato"), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
             HistoricoContratoVO aux = null;
             for(HistoricoContratoVO his: historicos){
                 if(his.getCodigo().intValue() != consulta.getInt("codigo")){
                     if(Calendario.igual(his.getDataFinalSituacao(), Uteis.obterDataAnterior(consulta.getDate("datainiciosituacao"), 1))){
                         aux = his;
                         aux.setDataFinalSituacao(consulta.getDate("datafinalsituacao"));
                     } else if(Calendario.maiorOuIgual(his.getDataFinalSituacao(), consulta.getDate("datainiciosituacao"))){
                         if(aux != null){
                             aux.setDataFinalSituacao(Uteis.obterDataAnterior(his.getDataInicioSituacao(), 1));
                             getFacade().getHistoricoContrato().alterarSemCommit(aux, Boolean.FALSE);
                             aux = null;
                             break;
                         }
                     }
                 } 
             }
             boolean ultimo = true;
             int cont = 1;
             while (ultimo){
                if (historicos.get(historicos.size() - cont).getCodigo() != consulta.getInt("codigo")){
                    if (!Calendario.igual(historicos.get(historicos.size() - cont).getDataFinalSituacao(), consulta.getDate("vigenciaateajustada"))){
                        historicos.get(historicos.size() - cont).setDataFinalSituacao(consulta.getDate("vigenciaateajustada"));
                        getFacade().getHistoricoContrato().alterarSemCommit(historicos.get(historicos.size() - cont), Boolean.FALSE);
                    }
                    ultimo = false;
                } else {
                    cont++;
                }
             }
              if(aux != null){
                  getFacade().getHistoricoContrato().alterarSemCommit(aux, Boolean.FALSE);
              }
              SuperFacadeJDBC.executarConsultaUpdate("delete from historicocontrato where codigo = "+consulta.getInt("codigo"), con);
              Integer contrato = new Integer(consulta.getInt("contrato"));
              Integer matricula =  new Integer(consulta.getInt("codigomatricula")); 
              contratos =  (contratos == "" ?  contrato.toString() : contratos + "," + contrato.toString());
              matriculas = (String) (matriculas == "" ?  matricula.toString() : matriculas + "," + matricula.toString());
                         
         }
         ValidadorBusinessIntelligence.processarRoboContratosProblematicos(contratos, matriculas);
    }
    
    
}
