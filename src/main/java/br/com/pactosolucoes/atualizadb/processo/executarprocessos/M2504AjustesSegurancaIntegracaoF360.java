package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.propriedades.PropsService;

import java.sql.Connection;

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "08/10/2024",
        descricao = "Remover uso de senha do ftp360 do banco de dados",
        motivacao = "M2-2504")
public class M2504AjustesSegurancaIntegracaoF360 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            String senhaAtual = Uteis.desencriptar(PropsService.getPropertyValue(PropsService.senhaCriptFTP360PasswordServer),PropsService.getPropertyValue(PropsService.chaveCriptFTP360PasswordServer));
            String sql = "update empresa set integracaof360password = null where integracaof360password = '" + senhaAtual + "'";
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
        }
    }
}
