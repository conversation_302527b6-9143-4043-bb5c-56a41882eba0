package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "29/10/2024",
        descricao = "Gerar periodo acesso para contratos sem periodo de acesso - Somentes contratos sem operações",
        motivacao = "M1-3169")
public class AtualizacaoTicketM13169 implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            StringBuilder sql = new StringBuilder();
            sql.append("INSERT INTO periodoacessocliente (tipoacesso, datafinalacesso, datainicioacesso, contrato, pessoa, datalancamento)\n");
            sql.append("(SELECT 'CA', c.vigenciaateajustada::date, c.vigenciade::date, c.codigo, c.pessoa, c.datalancamento::date\n");
            sql.append("\tFROM contrato c\n");
            sql.append("\tINNER JOIN cliente cl ON cl.pessoa = c.pessoa\n");
            sql.append("\tINNER JOIN pessoa pe ON pe.codigo = c.pessoa\n");
            sql.append("\tWHERE c.situacao = 'AT'\n");
            sql.append("\tAND current_date BETWEEN vigenciade AND vigenciaateajustada\n");
            sql.append("\tAND NOT EXISTS (SELECT codigo\n");
            sql.append("\t\tFROM periodoacessocliente p\n");
            sql.append("\t\tWHERE p.contrato = c.codigo\n");
            sql.append("\t\tAND current_date BETWEEN p.datainicioacesso AND p.datafinalacesso)\n");
            sql.append("\tAND NOT EXISTS (SELECT codigo\n");
            sql.append("\t\tFROM contratooperacao cop\n");
            sql.append("\t\tWHERE cop.contrato = c.codigo\n");
            sql.append("\t\tAND cop.tipooperacao = 'TR'\n");
            sql.append("\t\tAND cop.datafimefetivacaooperacao > current_date)\n");
            sql.append("\tAND NOT EXISTS (SELECT codigo\n");
            sql.append("\t\tFROM contratooperacao cop\n");
            sql.append("\t\tWHERE cop.contrato = c.codigo\n");
            sql.append("\t\tAND cop.tipooperacao IN ('RT', 'TR', 'AT', 'RA', 'BA', 'BR', 'CR', 'BC')))\n");
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql.toString(), c);
        }
    }
}
