package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Athos Feitosa",
        data = "07/11/2024",
        descricao = "Cria a coluna codigoOperacaoColetiva na tabela ClienteMensagem",
        motivacao = "GC-1116: Bloquear alunos na catraca - Operação coletiva (Necessidade de rastrear operações coletivas para mensagens de cliente)")
public class CriarColunaCodigoOperacaoColetivaTbClienteMensagem implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        String sql = "ALTER TABLE ClienteMensagem ADD COLUMN codigoOperacaoColetiva INTEGER;";
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
        }
    }
}
