/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.crm.AberturaMetaControle;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.crm.AberturaMetaVO;
import negocio.comuns.crm.FecharMetaDetalhadoVO;
import negocio.comuns.crm.FecharMetaVO;
import negocio.comuns.utilitarias.ThreadEnviarEmail;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.crm.AberturaMeta;
import negocio.facade.jdbc.crm.FecharMeta;
import negocio.facade.jdbc.crm.fecharMetaDetalhado.FecharMetaDetalhado;

/**
 *
 * <AUTHOR>
 */
public class AjustarAberturaMetaDuplicada {

    public static void main(String... args) {
        try {
            String nomeThread = args.length > 0 ? args[0] : "axis";
            DAO dao = new DAO();
            Connection con = dao.obterConexaoEspecifica(nomeThread);
            ajustarDuplicacoes(con);

        } catch (Exception ex) {
            Logger.getLogger(ProcessarMetasDoDia.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarDuplicacoes(Connection con) throws Exception {
        ajustarDuplicacoesMetas(con, null);
    }
    public static void ajustarDuplicacoes(Connection con, Date ProcessarAPartirDe) throws Exception {
        ajustarDuplicacoesMetas(con, ProcessarAPartirDe);
        ajustarDuplicacoesClientesSimplificado(con, ProcessarAPartirDe);
    }

    public static void ajustarDuplicacoesSimplificado(Connection con, Date ProcessarAPartirDe) throws Exception {
        ajustarDuplicacoesMetasSimplificado(con, ProcessarAPartirDe);
        ajustarDuplicacoesClientesSimplificado(con, ProcessarAPartirDe);
    }

    public static void ajustarDuplicacoesMetasSimplificado(Connection con, Date dataProcessar) throws Exception {
        AberturaMeta aberturaMetaDAO = new AberturaMeta(con);
        boolean terminou = false;
        try {
            // EXECUTA A SQL QUE BUSCA OS REPETIDOS
            while(!terminou){
                String consultaMetas = "select  colaboradorresponsavel ,dia,count(codigo), min(codigo) as primeira, max(codigo) as ultima  from  aberturameta where dia >='" +
                        (dataProcessar == null ? "2015-06-18" : Uteis.getDataFormatoBD(dataProcessar)) + "' group by colaboradorresponsavel ,dia, empresa having count(codigo)> 1" +
                        " order by count desc";
                ResultSet consulta = SuperFacadeJDBC.criarConsulta(consultaMetas, con);

                if(consulta.next()==false){
                    terminou = true;
                    break;
                }
                do {
                    // PREENCHE A ULTIMA ABERTURA
                    AberturaMetaVO ultima = aberturaMetaDAO.consultarPorChavePrimaria(consulta.getInt("ultima"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    SuperFacadeJDBC.executarConsultaUpdate("delete from aberturameta  where codigo = " + ultima.getCodigo(), con);
                }while (consulta.next());
            }

        }catch (Exception e){
            throw  e;
        }finally {
            aberturaMetaDAO = null;
        }
    }

    public static void ajustarDuplicacoesClientesSimplificado(Connection con, Date dataProcessar) throws Exception {
        FecharMeta fecharMetaDAO = new FecharMeta(con);
        FecharMetaDetalhado fecharMetaDetalhadoDAO = new FecharMetaDetalhado(con);
        boolean terminou = false;
        try {
            // EXECUTA A SQL QUE BUSCA OS CLIENTES REPETIDOS
            while(!terminou){
                String consultaMetas = "select " +
                        "a.codigo as a_codigo, a.colaboradorresponsavel, a.dia, a.empresa, f.identificadormeta, f.maladireta, f2.cliente, " +
                        "count(f.codigo), min(f.codigo) as primeira, max(f.codigo) as ultima, min(f2.codigo) as primeira2, max(f2.codigo) as ultima2 " +
                        "from  aberturameta a " +
                        "inner join fecharmeta f on f.aberturameta = a.codigo " +
                        "inner join fecharmetadetalhado f2 on f2.fecharmeta = f.codigo " +
                        "where 1=1 " +
                        "and f.identificadormeta not like 'AG' " +
                        "and dia >= '" + (dataProcessar == null ? "2015-06-18" : Uteis.getDataFormatoBD(dataProcessar)) + "'" +
                        "group by " +
                        "a.codigo, a.colaboradorresponsavel, a.dia, a.empresa, f.identificadormeta, f.maladireta, f2.cliente " +
                        "having count(f.codigo)> 1 " +
                        "order by count desc";
                ResultSet consulta = SuperFacadeJDBC.criarConsulta(consultaMetas, con);
                if(!consulta.next()){
                    terminou = true;
                    break;
                }
                do {
                    if (consulta.getInt("primeira") != consulta.getInt("ultima")){
                        // REMOVE OS CLIENTES REPETIDOS
                        FecharMetaVO ultima = fecharMetaDAO.consultarPorChavePrimaria(consulta.getInt("ultima"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        SuperFacadeJDBC.executarConsultaUpdate("delete from fecharmeta where codigo = " + ultima.getCodigo(), con);
                    } else {
                        FecharMetaDetalhadoVO ultima = fecharMetaDetalhadoDAO.consultarPorChavePrimaria(consulta.getInt("ultima2"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        SuperFacadeJDBC.executarConsultaUpdate("delete from fecharmetadetalhado where codigo = " + ultima.getCodigo(), con);
                    }
                }while (consulta.next());
            }

        }catch (Exception e){
            throw  e;
        }finally {
            fecharMetaDAO = null;
        }
    }

    public static void ajustarDuplicacoesMetas(Connection con, Date dataProcessar) throws Exception {
        AberturaMeta aberturaMetaDAO = new AberturaMeta(con);
        FecharMeta fecharMetaDAO = new FecharMeta(con);
        FecharMetaDetalhado fecharMetaDetalhadoDAO = new FecharMetaDetalhado(con);
        try {
            // EXECUTA A QUERY QUE BUSCA OS REPETIDOS
            String consultaMetas = "select  colaboradorresponsavel ,dia,count(codigo), min(codigo) as primeira, max(codigo) as ultima  from  aberturameta where dia >='" +
                    (dataProcessar == null ? "2015-06-18" : Uteis.getDataFormatoBD(dataProcessar)) + "' group by colaboradorresponsavel ,dia  having count(codigo)> 1";
            ResultSet consulta = SuperFacadeJDBC.criarConsulta(consultaMetas, con);
            List<Date> datas = new ArrayList<Date>();
            while (consulta.next()) {
                // PREENCHE A PRIMEIRA ABERTURA
                AberturaMetaVO primeira = aberturaMetaDAO.consultarPorChavePrimaria(consulta.getInt("primeira"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                primeira.setFecharMetaVosEstudio(fecharMetaDAO.consultarFecharMeta(primeira.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS, FasesCRMEnum.ULTIMAS_SESSOES.getSigla(), FasesCRMEnum.SEM_AGENDAMENTO.getSigla()));
                primeira.setFecharMetaVosRetencao(fecharMetaDAO.consultarFecharMetaRetencao(primeira.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                primeira.setFecharMetaVosVenda(fecharMetaDAO.consultarFecharMetaVenda(primeira.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                primeira.setFecharMetaVosLead(fecharMetaDAO.consultarFecharMetaLeads(primeira.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                if (!datas.contains(primeira.getDia())) {
                    datas.add(primeira.getDia());
                }
                // PREENCHE A ULTIMA ABERTURA
                AberturaMetaVO ultima = aberturaMetaDAO.consultarPorChavePrimaria(consulta.getInt("ultima"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ultima.setFecharMetaVosEstudio(fecharMetaDAO.consultarFecharMeta(ultima.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS, FasesCRMEnum.ULTIMAS_SESSOES.getSigla(), FasesCRMEnum.SEM_AGENDAMENTO.getSigla()));
                ultima.setFecharMetaVosRetencao(fecharMetaDAO.consultarFecharMetaRetencao(ultima.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                ultima.setFecharMetaVosVenda(fecharMetaDAO.consultarFecharMetaVenda(ultima.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                ultima.setFecharMetaVosLead(fecharMetaDAO.consultarFecharMetaLeads(ultima.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));

                // UTILIZADO PARA RECALCULAR
                Integer metaAtingida = 0;
                Double porcentagem = 0.0;
                boolean adicionado = false;
                Integer meta = 0;

                // INICIO: FECHAR-META-ESTUDIO
                for (FecharMetaVO fmPri : primeira.getFecharMetaVosEstudio()) {
                    metaAtingida = 0;
                    meta = 0;
                    for (FecharMetaVO fmUlt : ultima.getFecharMetaVosEstudio()) {
                        if (fmUlt.getIdentificadorMeta().equals(fmPri.getIdentificadorMeta())) {

                            // Verifica cada fecharMetaDetalhado do Primeiro
                            for (FecharMetaDetalhadoVO aluPri : fmPri.getFecharMetaDetalhadoVOs()) {
                                adicionado = false;
                                // Verifica cada fecharMetaDetalhado do Último
                                for (FecharMetaDetalhadoVO aluUlt : fmUlt.getFecharMetaDetalhadoVOs()) {
                                    // Se o Cliente do primeiro é igual ao Cliente do último, então:
                                    if (aluPri.getCliente().getCodigo().equals(aluUlt.getCliente().getCodigo())) {
                                        adicionado = true;
                                        // Remove o fecharMetaDetalhado da LISTA de FecharMeta do último.
                                        fmUlt.getFecharMetaDetalhadoVOs().remove(aluUlt);
                                        if (aluPri.getObteveSucesso() || aluUlt.getObteveSucesso()) {
                                            metaAtingida++;
                                        }
                                        // Se o último que bateu a meta, então passe os ados do último pro primeiro
                                        if (!aluPri.getObteveSucesso() && aluUlt.getObteveSucesso()) {
                                            aluUlt.setFecharMeta(fmPri);
                                            // Obs: O último com o código do primeiro, passa a se tornar o primeiro
                                            // no método de alterar do DAO
                                            aluUlt.setCodigo(aluPri.getCodigo());
                                            fecharMetaDetalhadoDAO.alterar(aluUlt);
                                        }
                                        break;
                                    }
                                }
                                // Se o cliente do primeiro é igual ao cliente do último, então:
                                if (adicionado) {
                                    meta++;
                                } else {
                                    fecharMetaDetalhadoDAO.excluir(aluPri);
                                }
                            }

                            // Se o FecharMeta do último não estiver vazio:
                            if (!fmUlt.getFecharMetaDetalhadoVOs().isEmpty()) {
                                for (FecharMetaDetalhadoVO aluUlt : fmUlt.getFecharMetaDetalhadoVOs()) {
                                    aluUlt.setFecharMeta(fmPri);
                                    meta++;
                                    if (aluUlt.getObteveSucesso()) {
                                        metaAtingida++;
                                    }
                                    fecharMetaDetalhadoDAO.alterar(aluUlt);
                                }
                            }
                            break;
                        }
                    }
                    porcentagem = meta == 0 ? 0 : Uteis.arredondarForcando2CasasDecimais((metaAtingida * 100) / meta);
                    SuperFacadeJDBC.executarConsultaUpdate("update fecharmeta set meta = " + meta + " , porcentagem = " + porcentagem + " , metaatingida =" + metaAtingida + "   where codigo = " + fmPri.getCodigo(), con);
                }
                // FIM: FECHAR-META-ESTUDIO

                // INICIO: FECHAR-META-RETENÇÃO
                for (FecharMetaVO fmPri : primeira.getFecharMetaVosRetencao()) {
                    metaAtingida = 0;
                    meta = 0;
                    for (FecharMetaVO fmUlt : ultima.getFecharMetaVosRetencao()) {
                        if (fmUlt.getIdentificadorMeta().equals(fmPri.getIdentificadorMeta())) {
                            for (FecharMetaDetalhadoVO aluPri : fmPri.getFecharMetaDetalhadoVOs()) {
                                adicionado = false;
                                for (FecharMetaDetalhadoVO aluUlt : fmUlt.getFecharMetaDetalhadoVOs()) {
                                    if (aluPri.getCliente().getCodigo().equals(aluUlt.getCliente().getCodigo())) {
                                        adicionado = true;
                                        fmUlt.getFecharMetaDetalhadoVOs().remove(aluUlt);
                                        if (aluPri.getObteveSucesso() || aluUlt.getObteveSucesso()) {
                                            metaAtingida++;
                                        }
                                        if (!aluPri.getObteveSucesso() && aluUlt.getObteveSucesso()) {
                                            aluUlt.setFecharMeta(fmPri);
                                            aluUlt.setCodigo(aluPri.getCodigo());
                                            fecharMetaDetalhadoDAO.alterar(aluUlt);
                                        }
                                        break;
                                    }
                                }
                                if (adicionado) {
                                    meta++;
                                } else {
                                    fecharMetaDetalhadoDAO.excluir(aluPri);
                                }

                            }
                            if (!fmUlt.getFecharMetaDetalhadoVOs().isEmpty()) {
                                for (FecharMetaDetalhadoVO aluUlt : fmUlt.getFecharMetaDetalhadoVOs()) {
                                    aluUlt.setFecharMeta(fmPri);
                                    meta++;
                                    if (aluUlt.getObteveSucesso()) {
                                        metaAtingida++;
                                    }
                                    fecharMetaDetalhadoDAO.alterar(aluUlt);
                                }
                            }
                            break;
                        }
                    }
                    porcentagem = meta == 0 ? 0 : Uteis.arredondarForcando2CasasDecimais((metaAtingida * 100) / meta);
                    SuperFacadeJDBC.executarConsultaUpdate("update fecharmeta set meta = " + meta + " , porcentagem = " + porcentagem + " , metaatingida =" + metaAtingida + "   where codigo = " + fmPri.getCodigo(), con);
                }
                // FIM: FECHAR-META-RETENÇÃO

                // INICIO: FECHAR-META-VENDAS
                for (FecharMetaVO fmPri : primeira.getFecharMetaVosVenda()) {
                    metaAtingida = 0;
                    meta = 0;
                    for (FecharMetaVO fmUlt : ultima.getFecharMetaVosVenda()) {
                        if (fmUlt.getIdentificadorMeta().equals(fmPri.getIdentificadorMeta())) {
                            for (FecharMetaDetalhadoVO aluPri : fmPri.getFecharMetaDetalhadoVOs()) {
                                adicionado = false;
                                for (FecharMetaDetalhadoVO aluUlt : fmUlt.getFecharMetaDetalhadoVOs()) {
                                    if (aluPri.getCliente().getCodigo().equals(aluUlt.getCliente().getCodigo())) {
                                        adicionado = true;
                                        fmUlt.getFecharMetaDetalhadoVOs().remove(aluUlt);
                                        if (aluPri.getObteveSucesso() || aluUlt.getObteveSucesso()) {
                                            metaAtingida++;
                                        }
                                        if (!aluPri.getObteveSucesso() && aluUlt.getObteveSucesso()) {
                                            aluUlt.setFecharMeta(fmPri);
                                            aluUlt.setCodigo(aluPri.getCodigo());
                                            fecharMetaDetalhadoDAO.alterar(aluUlt);
                                        }
                                        break;
                                    }
                                }
                                if (adicionado) {
                                    meta++;
                                } else {
                                    if (fmPri.getIdentificadorMeta().equals("AG")) {
                                        meta++;
                                        if (aluPri.getObteveSucesso()) {
                                            metaAtingida++;
                                        }
                                    } else {
                                        fecharMetaDetalhadoDAO.excluir(aluPri);
                                    }
                                }

                            }
                            if (!fmUlt.getFecharMetaDetalhadoVOs().isEmpty()) {
                                for (FecharMetaDetalhadoVO aluUlt : fmUlt.getFecharMetaDetalhadoVOs()) {
                                    aluUlt.setFecharMeta(fmPri);
                                    meta++;
                                    if (aluUlt.getObteveSucesso()) {
                                        metaAtingida++;
                                    }
                                    fecharMetaDetalhadoDAO.alterar(aluUlt);
                                }
                            }
                            break;
                        }
                    }
                    porcentagem = meta == 0 ? 0 : Uteis.arredondarForcando2CasasDecimais((metaAtingida * 100) / meta);
                    SuperFacadeJDBC.executarConsultaUpdate("update fecharmeta set meta = " + meta + " , porcentagem = " + porcentagem + " , metaatingida =" + metaAtingida + "   where codigo = " + fmPri.getCodigo(), con);
                }
                // FIM: FECHAR-META-VENDAS

                SuperFacadeJDBC.executarConsultaUpdate("delete from aberturameta  where codigo = " + ultima.getCodigo(), con);
                SuperFacadeJDBC.executarConsultaUpdate("update aberturameta set metaemaberto = 't'  where codigo = " + primeira.getCodigo(), con);
            }
            for (Date data : datas) {
                try {
                    new AberturaMetaControle().fecharMetas(data);
                } catch (Exception e) {

                } finally {
                    ThreadEnviarEmail.finalmente();
                }
            }

        }catch (Exception e){
            throw  e;
        }finally {
            aberturaMetaDAO = null;
            fecharMetaDAO = null;
            fecharMetaDetalhadoDAO = null;
        }
    }

}
