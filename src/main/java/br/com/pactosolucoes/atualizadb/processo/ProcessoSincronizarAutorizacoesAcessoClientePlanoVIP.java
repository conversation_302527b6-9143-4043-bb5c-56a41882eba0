package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.oamd.RedeEmpresaVO;
import org.json.JSONObject;
import servicos.impl.microsservice.acessosistema.AcessoSistemaMSService;
import servicos.oamd.RedeEmpresaService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class ProcessoSincronizarAutorizacoesAcessoClientePlanoVIP {


    private final Connection con;
    private final String chave;

    public ProcessoSincronizarAutorizacoesAcessoClientePlanoVIP(Connection con, String chave) {
        this.con = con;
        this.chave = chave;
    }

    public static void main(String[] args) throws Exception {
        Integer redeEmpresaId = 467;

        Connection conOAMD = DriverManager.getConnection("*************************************", "postgres", "pactodb");
        Connection conFranqueadora = DriverManager.getConnection("******************************************", "postgres", "pactodb");

        Map<String, String> mapaAutorizacoesFranqueadora = obterMapaAutorizacoesFranqueadora(conFranqueadora);

        String sql = "SELECT\n" +
                " r.chavefranqueadora,\n" +
                " r.codigounidadefranqueadora,\n" +
                " r.sincronizarclientesnafranqueadora,\n" +
                " ef.empresazw,\n" +
                " e.chave,\n" +
                " e.identificadorempresa,\n" +
                " e.\"hostBD\",\n" +
                " e.\"nomeBD\",\n" +
                " e.porta,\n" +
                " e.\"passwordBD\",\n" +
                " e.\"userBD\", \n" +
                " e.robocontrole, \n" +
                " efran.\"hostBD\" as hostBD_franqueadora,\n" +
                " efran.\"nomeBD\" as nomeBD_franqueadora,\n" +
                " efran.porta as porta_franqueadora,\n" +
                " efran.\"passwordBD\" as passwordBD_franqueadora,\n" +
                " efran.\"userBD\" as userBD_franqueadora \n" +
                "FROM redeempresa r \n" +
                " INNER JOIN empresafinanceiro ef ON ef.redeempresa_id = r.id \n" +
                " INNER JOIN empresa e ON e.chave = ef.chavezw \n" +
                " INNER JOIN empresa efran ON efran.chave = r.chavefranqueadora \n" +
                "WHERE 1 = 1\n" +
                "AND e.chave <> r.chavefranqueadora\n" +
                "AND e.usoteste IS FALSE \n" +
                "AND e.ativa IS TRUE \n" +
                "AND r.id = " + redeEmpresaId;

        ResultSet rs = conOAMD.createStatement().executeQuery(sql);

        int total = SuperFacadeJDBC.contar("select count(s.*) from (" + sql + ") as s", conOAMD);
        int atual = 0;
        StringBuilder log = new StringBuilder("chave;empresa;cliente;matricula;nome;datasincronizacao;plano;tipoaluno;resultado;url\n");

        while(rs.next()) {
            String chave = rs.getString("chave");
            String identificadorEmpresa = rs.getString("identificadorempresa");

            Uteis.logarDebug(String.format("%d\\%d - Processando empresa %s - %s", ++atual, total, chave, identificadorEmpresa));

            String urlCon = String.format("jdbc:postgresql://%s:%d/%s", rs.getString("hostBD"), rs.getInt("porta"), rs.getString("nomeBD"));
            Connection con = DriverManager.getConnection(urlCon, rs.getString("userBD"), rs.getString("passwordBD"));

            ProcessoSincronizarAutorizacoesAcessoClientePlanoVIP processo = new ProcessoSincronizarAutorizacoesAcessoClientePlanoVIP(con, chave);
            processo.corrigirClientesNaoSincronizados(mapaAutorizacoesFranqueadora.get(chave), rs.getString("robocontrole"), log);
        }

        Uteis.salvarArquivo("alunos-vip-sincronizacao-" + Calendario.hoje().getTime() + ".csv", log.toString(), "C:\\pactoj\\logs\\");
    }

    private void corrigirClientesNaoSincronizados(String codigosPessoasSincronizadosFranqueadora, String urlZW, StringBuilder log) throws Exception {

        String sqlAlunosVipSincronizar = "SELECT \n" +
                "\tcli_vip.*\n" +
                "FROM (\n" +
                "\tSELECT\n" +
                " cli.codigo as cliente,\n" +
                " cli.codigomatricula as matricula,\n" +
                " cli.pessoa,\n" +
                " pes.nome,\n" +
                " cli.sincronizadoredeempresa,\n" +
                " p.descricao as plano,\n" +
                " e.nome empresa,\n" +
                " 'TITULAR' AS tipoAluno\n" +
                "FROM contrato c\n" +
                " INNER JOIN plano p on c.plano = p.codigo\n" +
                " INNER JOIN cliente cli on c.pessoa = cli.pessoa\n" +
                " INNER JOIN pessoa pes on pes.codigo = cli.pessoa\n" +
                " INNER JOIN empresa e on e.codigo = cli.empresa\n" +
                "WHERE p.permitiracessoredeempresa\n" +
                "AND c.vigenciaateajustada > now()\n" +
                "UNION\n" +
                "SELECT\n" +
                " cli.codigo as cliente,\n" +
                " cli.codigomatricula as matricula,\n" +
                " cli.pessoa,\n" +
                " pes.nome,\n" +
                " cli.sincronizadoredeempresa,\n" +
                " p.descricao as plano,\n" +
                " e.nome as empresa,\n" +
                " 'DEPENDENTE' AS tipoAluno\n" +
                "FROM contratodependente cd\n" +
                "INNER JOIN contrato c on c.codigo = cd.contrato\n" +
                "INNER JOIN plano p on c.plano = p.codigo\n" +
                "INNER JOIN cliente cli on cd.cliente = cli.codigo\n" +
                "INNER JOIN pessoa pes on pes.codigo = cli.pessoa\n" +
                "INNER JOIN empresa e on e.codigo = cli.empresa\n" +
                "WHERE coalesce(cd.cliente,0) <> 0\n" +
                "AND p.permitiracessoredeempresa\n" +
                "AND cd.datafinalajustada > now()) as cli_vip\n";

        if (!UteisValidacao.emptyString(codigosPessoasSincronizadosFranqueadora)) {
            sqlAlunosVipSincronizar += "WHERE cli_vip.pessoa NOT IN (" + codigosPessoasSincronizadosFranqueadora + ") \n";
        }

        int total = SuperFacadeJDBC.contar("select count(s.*) from (" + sqlAlunosVipSincronizar + ") as s", con);
        Uteis.logarDebug(String.format("[%s] - Total de alunos VIP a sincronizar: %d", con.getCatalog(), total));

        ResultSet rsAlunosVipSincronizar = con.createStatement().executeQuery(sqlAlunosVipSincronizar);
        while (rsAlunosVipSincronizar.next()) {
            String resultado = "";
            try {
                String url = String.format("%s/prest/tela-cliente?key=%s&op=sincronizarAlunoPlanoVIP&codigoCliente=%d", urlZW, chave, rsAlunosVipSincronizar.getInt("cliente"));
                String response = ExecuteRequestHttpService.get(url, new HashMap<>());
                JSONObject jsonResponse = new JSONObject(response);
                if (jsonResponse.has("content")) {
                    resultado = jsonResponse.getString("content");
                } else {
                    if (jsonResponse.has("meta") && jsonResponse.getJSONObject("meta").has("error")) {
                        resultado = jsonResponse.getString("error");
                    } else {
                        resultado = "FALHA: " + resultado;
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                resultado = "FALHA: " + ex.getMessage();
            }
            log.append(String.format("%s;%s;%s;%s;%s;%s;%s;%s;%s;%s\n",
                    chave,
                    rsAlunosVipSincronizar.getString("empresa"),
                    rsAlunosVipSincronizar.getString("cliente"),
                    rsAlunosVipSincronizar.getString("matricula"),
                    rsAlunosVipSincronizar.getString("nome"),
                    Calendario.getDataAplicandoFormatacao(rsAlunosVipSincronizar.getTimestamp("sincronizadoredeempresa"), "dd/MM/yyyy HH:mm:ss"),
                    rsAlunosVipSincronizar.getString("plano"),
                    rsAlunosVipSincronizar.getString("tipoAluno"),
                    resultado,
                    urlZW
            ));
        }
    }

    private static Map<String, String> obterMapaAutorizacoesFranqueadora(Connection conFranqueadora) throws SQLException {
        Map<String, String> mapaAutorizacoesFranqueadora = new HashMap<>();

        String sql = "select \n" +
                "\tit.chave,\n" +
                "\tarray_to_string(array_agg(aut.codigopessoa),',') as codigospessoas\n" +
                "from autorizacaoacessogrupoempresarial aut\n" +
                "\tinner join integracaoacessogrupoempresarial it on it.codigo = aut.integracaoacessogrupoempresarial \n" +
                "where 1 = 1\n" +
                "group by it.chave";

        ResultSet rs = conFranqueadora.createStatement().executeQuery(sql);
        while (rs.next()) {
            mapaAutorizacoesFranqueadora.put(rs.getString("chave"), rs.getString("codigospessoas"));
        }
        return mapaAutorizacoesFranqueadora;
    }

    public void sincronizarAlunoVIPRedeEmpresa(Integer codigoCliente) throws Exception {
        RedeEmpresaService.limparMapaDeRedes();
        RedeEmpresaVO redeEmpresa = RedeEmpresaService.obterRedePorChave(chave);
        if (redeEmpresa == null || !redeEmpresa.getGestaoRedes()) {
            throw new Exception("Rede não encontrada ou não utiliza gestão de redes");
        }

        Cliente clienteDAO = new Cliente(con);
        ClienteVO cliente = clienteDAO.consultarPorChavePrimaria(codigoCliente, Uteis.NIVELMONTARDADOS_MINIMOS_COM_BIOMETRIAS);

        if(!possuiPlanoVIP(cliente.getCodigo())){
            throw new Exception("Cliente não possui plano VIP");
        }

        AutorizacaoAcessoGrupoEmpresarialVO autorizacao = AcessoSistemaMSService.findByCPF(cliente.getPessoa().getCfp(), null, redeEmpresa);
        if (UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
            autorizacao = new AutorizacaoAcessoGrupoEmpresarialVO(cliente);
            autorizacao = AcessoSistemaMSService.insertAccessAuthorization(autorizacao, chave, cliente.getEmpresa().getCodigo(), redeEmpresa);
        } else {
            AutorizacaoAcessoGrupoEmpresarialVO autorizacaoModelo = new AutorizacaoAcessoGrupoEmpresarialVO(cliente);
            autorizacao.setCodigoPessoa(autorizacaoModelo.getCodigoPessoa());
            autorizacao.setCodAcesso(autorizacaoModelo.getCodAcesso());
            autorizacao.setCodigoGenerico(autorizacaoModelo.getCodigoGenerico());
            autorizacao = AcessoSistemaMSService.updateAccessAuthorization(autorizacao, chave, cliente.getEmpresa().getCodigo(), redeEmpresa);
        }
        AcessoSistemaMSService.publish(autorizacao, false, redeEmpresa);

        if (!UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
            clienteDAO.alterarSincronizadoRedeEmpresa(cliente.getCodigo());
        } else {
            throw new Exception("Erro ao sincronizar aluno VIP na rede da empresa");
        }
        clienteDAO = null;
    }

    private boolean possuiPlanoVIP(Integer codigoCliente) throws Exception {
        String sql = "SELECT \n" +
                "\tcli_vip.*\n" +
                "FROM (\n" +
                "\tSELECT\n" +
                "\t\tcli.codigomatricula,\n" +
                "\t\tcli.codigo AS codigocliente,\n" +
                "\t\tcli.pessoa AS codigopessoa,\n" +
                "\t\tc.situacao\n" +
                "\tFROM contrato c\n" +
                "\tINNER JOIN plano p on c.plano = p.codigo\n" +
                "\tINNER JOIN cliente cli on c.pessoa = cli.pessoa\n" +
                "\tWHERE p.permitiracessoredeempresa\n" +
                "\tAND c.vigenciaateajustada > now()\n" +
                "\t\tUNION\n" +
                "\tSELECT\n" +
                "\t\tcli.codigomatricula,\n" +
                "\t\tcli.codigo AS codigocliente,\n" +
                "\t\tcli.pessoa AS codigopessoa,\n" +
                "\t\tc.situacao\n" +
                "\tFROM contratodependente cd\n" +
                "\tINNER JOIN contrato c on c.codigo = cd.contrato\n" +
                "\tINNER JOIN plano p on c.plano = p.codigo\n" +
                "\tINNER JOIN cliente cli on cd.cliente = cli.codigo\n" +
                "\tWHERE coalesce(cd.cliente,0) <> 0\n" +
                "\tAND p.permitiracessoredeempresa\n" +
                "\tAND cd.datafinalajustada > now()) as cli_vip\n" +
                "WHERE cli_vip.codigocliente = " + codigoCliente + " \n";
        return SuperFacadeJDBC.existe(sql, con);
    }

}
