package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.Date;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.basico.ConfiguracaoSistemaControle;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoModalidadeVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.contrato.MovProdutoModalidade;

public class GerarMovProdutoModalidade {

	public static void gravarDados(Connection con) throws Exception{
		MovProdutoModalidade movProdMod = new MovProdutoModalidade(con);
		Uteis.logarDebug( "GerarMovProdutoModalidade - início em : "+ new Date());
		StringBuilder sql = new StringBuilder();
		sql.append("select MP.codigo as movproduto, MP.contrato, mp.totalfinal, c.vigenciade, ");
		sql.append("c.vigenciaateajustada, cm.modalidade, cm.valormodalidade, (select sum(valormodalidade) from contratomodalidade where contrato = c.codigo) as valorModalidades");
		sql.append(" from movproduto mp ");
		sql.append(" inner join produto p on mp.produto = p.codigo and p.tipoproduto like 'PM' ");
		sql.append(" inner join contrato c on mp.contrato = c.codigo ");
		sql.append(" inner join contratomodalidade cm on cm.contrato = c.codigo ");
		sql.append(" left join movprodutomodalidade mpm on mpm.movproduto = mp.codigo   ");
		sql.append(" where mpm.codigo is null ");
		sql.append(" order by mp.codigo ");
		try (ResultSet dados = con.prepareStatement(sql.toString()).executeQuery()) {
			while (dados.next()) {
				double valorProduto = dados.getDouble("totalfinal");
				double valorTotalModalidade = dados.getDouble("valorModalidades");
				double valorModalidade = dados.getDouble("valormodalidade");

				double valorMovProdModalidade = valorTotalModalidade == 0.0 ? 0.0 : (valorProduto * (valorModalidade / valorTotalModalidade));

				MovProdutoModalidadeVO mpm = new MovProdutoModalidadeVO();
				mpm.getMovProdutoVO().setCodigo(dados.getInt("movproduto"));
				mpm.getModalidadeVO().setCodigo(dados.getInt("modalidade"));
				mpm.setDataInicio(dados.getDate("vigenciade"));
				mpm.setDataFim(dados.getDate("vigenciaateajustada"));
				mpm.setValor(valorMovProdModalidade);
				mpm.getContrato().setCodigo(dados.getInt("contrato"));

				movProdMod.incluir(mpm);
			}
		}
		Uteis.logarDebug( "GerarMovProdutoModalidade - fim em : "+ new Date());
	}

	public static void gerarMovProdutoModalidadeProdutoCancelado(Connection con) throws Exception{
		Uteis.logarDebug("Iniciando processo: GerarMovProdutoModalidade produtos cancelados");

		MovProdutoModalidade movProdMod = new MovProdutoModalidade(con);
		ConfiguracaoSistemaControle config = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession("ConfiguracaoSistemaControle");
		if (!JSFUtilities.isJSFContext()) {
			config = new ConfiguracaoSistemaControle();
			config.setRodandoGerarMOVProdutoModalidade(true);
		}

		String logInicio = String.format("GerarMovProdutoModalidade - Produtos Cancelados - início em : %s", new Date());
		Uteis.logarDebug(logInicio);
		config.setInformacaoGerarMOVProdutoModalidade(logInicio);
		//EXECUÇÃO DE TODOS OS PROCESSOS
		config.setInformacaoExecutarProcessos(logInicio);

		StringBuilder sql = new StringBuilder();
		sql.append("select mp.codigo as movproduto,\n" +
				"mp.totalfinal,\n" +
				"mp.produto,\n" +
				"c.codigo as contrato,\n" +
				"c.vigenciade,\n" +
				"c.vigenciaateajustada,\n" +
				"mp.descricaomovprodutomodalidade\n" +
				"from movproduto mp\n" +
				"left join movprodutomodalidade mpm on mpm.movproduto = mp.codigo\n" +
				"inner join produto p on mp.produto = p.codigo and p.tipoproduto = 'PM' and mp.situacao = 'CA'\n" +
				"inner join contrato c on c.codigo = mp.contrato\n" +
				"and mpm.codigo is null\n" +
				"order by mp.codigo");
		try (ResultSet dados = con.prepareStatement(sql.toString()).executeQuery()) {
			while (dados.next()) {
				if (config.isRodandoGerarMOVProdutoModalidade() && config.isRodandoExecutarProcessos()) {
					ZillyonWebFacade zillyonWebFacade = new ZillyonWebFacade(con);
					MovProduto movProduto = new MovProduto(con);
					ContratoVO contrato = zillyonWebFacade.getContrato().consultarPorCodigo(dados.getInt("contrato"), Uteis.NIVELMONTARDADOS_VALIDACAOACESSO);
					MovProdutoVO movProdutoVO = zillyonWebFacade.getMovProduto().consultarPorChavePrimaria(dados.getInt("movproduto"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
					movProdutoVO.setMovProdutoModalidades(zillyonWebFacade.gerarMovProdutoModalidade(MovProdutoModalidade.getValorSomaContratoModalidade(contrato.getContratoModalidadeVOs()),
							contrato.getVigenciaDe(), contrato.getVigenciaAte(), contrato.getContratoModalidadeVOs(), movProdutoVO.getTotalFinal()));
					movProduto.alterar(movProdutoVO);
					movProdMod.incluir(movProdutoVO);
				}
			}
		}
		String logFinal = String.format("GerarMovProdutoModalidade - Produtos Cancelados - fim em : %s", new Date());
		Uteis.logarDebug(logFinal);
		config.setInformacaoGerarMOVProdutoModalidade(logFinal);
		//EXECUÇÃO DE TODOS OS PROCESSOS
		config.setInformacaoExecutarProcessos(logFinal);
		config.setRodandoGerarMOVProdutoModalidade(false);
	}
	
	public static void main(String[] args) {
		try {
			Connection con1 = DriverManager.getConnection("*********************************************************************","postgres", "pactodb");
//          atualizarDescricaoMovProdutoModalidade(con1);
			gerarMovProdutoModalidadeProdutoCancelado(con1);
//			gravarDados(con1);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void atualizarDescricaoMovProdutoModalidade(Connection con) throws Exception {
		MovProduto movDAO = new MovProduto(con);
		movDAO.atualizarDescricaoMovProdutoModalidade(56923);
	}

	public static void preencherTabelaMovProdutoModalidade(Connection con) throws Exception{
		Uteis.logarDebug("Iniciando processo: GerarMovProdutoModalidade preencher tabela MovProdutoModalidade");

		MovProdutoModalidade movProdMod = new MovProdutoModalidade(con);
		ConfiguracaoSistemaControle config = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession("ConfiguracaoSistemaControle");
		config.setRodandoGerarMOVProdutoModalidade(true);

		String logInicio = String.format("GerarMovProdutoModalidade - Preencher tabela MovProdutoModalidade - início em : %s", new Date());
		Uteis.logarDebug(logInicio);
		config.setInformacaoGerarMOVProdutoModalidade(logInicio);
		//EXECUÇÃO DE TODOS OS PROCESSOS
		config.setInformacaoExecutarProcessos(logInicio);

		StringBuilder sql = new StringBuilder();
		sql.append("select mp.codigo as movproduto,\n" +
				"mp.totalfinal,\n" +
				"mp.produto,\n" +
				"c.codigo as contrato,\n" +
				"c.vigenciade,\n" +
				"c.vigenciaateajustada,\n" +
				"mp.descricaomovprodutomodalidade\n" +
				"from movproduto mp\n" +
				"left join movprodutomodalidade mpm on mpm.movproduto = mp.codigo\n" +
				"inner join produto p on mp.produto = p.codigo and p.tipoproduto = 'PM' \n" +
				"inner join contrato c on c.codigo = mp.contrato\n" +
				"and mpm.codigo is null\n" +
				"order by mp.codigo");
		try (ResultSet dados = con.prepareStatement(sql.toString()).executeQuery()) {
			while (dados.next()) {
				if (config.isRodandoGerarMOVProdutoModalidade() && config.isRodandoExecutarProcessos()) {
					ZillyonWebFacade zillyonWebFacade = new ZillyonWebFacade(con);
					MovProduto movProduto = new MovProduto(con);
					ContratoVO contrato = zillyonWebFacade.getContrato().consultarPorCodigo(dados.getInt("contrato"), Uteis.NIVELMONTARDADOS_VALIDACAOACESSO);
					MovProdutoVO movProdutoVO = zillyonWebFacade.getMovProduto().consultarPorChavePrimaria(dados.getInt("movproduto"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
					movProdutoVO.setMovProdutoModalidades(zillyonWebFacade.gerarMovProdutoModalidade(MovProdutoModalidade.getValorSomaContratoModalidade(contrato.getContratoModalidadeVOs()),
							contrato.getVigenciaDe(), contrato.getVigenciaAte(), contrato.getContratoModalidadeVOs(), movProdutoVO.getTotalFinal()));
					movProduto.alterar(movProdutoVO);
					movProdMod.incluir(movProdutoVO);
				}
			}
		}
		String logFinal = String.format("GerarMovProdutoModalidade - Preencher tabela MovProdutoModalidade - fim em : %s", new Date());
		Uteis.logarDebug(logFinal);
		config.setInformacaoGerarMOVProdutoModalidade(logFinal);
		//EXECUÇÃO DE TODOS OS PROCESSOS
		config.setInformacaoExecutarProcessos(logFinal);
		config.setRodandoGerarMOVProdutoModalidade(false);
	}
        

}
