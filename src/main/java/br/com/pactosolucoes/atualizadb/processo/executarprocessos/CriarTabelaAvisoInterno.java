package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON> Rodrigues",
        data = "14/08/2024",
        descricao = "Cria tabelas avisointerno, avisointerno_usuario e avisointerno_perfilacesso",
        motivacao = "Criar tabelas para armazenar avisos internos e suas associações com usuários e perfis")
public class CriarTabelaAvisoInterno implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.avisointerno (\n" +
                    "    codigo serial4 NOT NULL,\n" +
                    "    conteudo text NOT NULL,\n" +
                    "    datapublicacao timestamp NULL,\n" +
                    "    dataexpiracao timestamp NULL,\n" +
                    "    ativo boolean NOT NULL,\n" +
                    "    visivelparatodos boolean NULL,\n" +
                    "    empresa int4 NULL,\n" +
                    "    autor int4 NULL,\n" +
                    "    CONSTRAINT \"avisointerno_codigoPK\" PRIMARY KEY (codigo),\n" +
                    "    CONSTRAINT \"avisointerno_autorFK\" FOREIGN KEY (autor) REFERENCES public.usuario(codigo)\n" +
                    ");", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.avisointerno_usuario (\n" +
                    "    avisointerno_codigo int4 NOT NULL,\n" +
                    "    usuario_codigo int4 NOT NULL,\n" +
                    "    CONSTRAINT \"avisointerno_usuarioPK\" PRIMARY KEY (avisointerno_codigo, usuario_codigo),\n" +
                    "    CONSTRAINT \"avisointernoFK\" FOREIGN KEY (avisointerno_codigo) REFERENCES public.avisointerno(codigo) ON DELETE CASCADE,\n" +
                    "    CONSTRAINT \"usuarioFK\" FOREIGN KEY (usuario_codigo) REFERENCES public.usuario(codigo) ON DELETE CASCADE\n" +
                    ");", c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.avisointerno_perfilacesso (\n" +
                    "    avisointerno_codigo int4 NOT NULL,\n" +
                    "    perfilacesso_codigo int4 NOT NULL,\n" +
                    "    CONSTRAINT \"avisointerno_perfilacessoPK\" PRIMARY KEY (avisointerno_codigo, perfilacesso_codigo),\n" +
                    "    CONSTRAINT \"avisointernoFK\" FOREIGN KEY (avisointerno_codigo) REFERENCES public.avisointerno(codigo) ON DELETE CASCADE,\n" +
                    "    CONSTRAINT \"perfilacessoFK\" FOREIGN KEY (perfilacesso_codigo) REFERENCES public.perfilacesso(codigo) ON DELETE CASCADE\n" +
                    ");", c);
        }
    }
}
