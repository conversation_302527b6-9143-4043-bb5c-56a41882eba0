package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoAjustarDiariasDuplicadas {
    public static void main(String... args) {
        try {
            String chave = args.length > 0 ? args[0] : "korpusunidbessa";
            Connection c = new DAO().obterConexaoEspecifica(chave);
            ajustarDiariasDuplicadas(c);
        } catch (Exception ex) {
            Logger.getLogger(ProcessoAjustarDiariasDuplicadas.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarDiariasDuplicadas(Connection con) {
        try {
            Uteis.logarDebug("INÍCIO | ProcessoAjustarDiariasDuplicadas");

            StringBuilder sqlConsultaDiariaDuplicada = new StringBuilder();
            sqlConsultaDiariaDuplicada.append("SELECT\n");
            sqlConsultaDiariaDuplicada.append("\tcli.codigomatricula,\n");
            sqlConsultaDiariaDuplicada.append("\tavd.cliente,\n");
            sqlConsultaDiariaDuplicada.append("\tavd.datalancamento,\n");
            sqlConsultaDiariaDuplicada.append("\tstring_agg(DISTINCT mpv.codigo::varchar, ',') AS codParcelas\n");
            sqlConsultaDiariaDuplicada.append("FROM aulaavulsadiaria avd\n");
            sqlConsultaDiariaDuplicada.append("INNER JOIN cliente cli ON cli.codigo = avd.cliente\n");
            sqlConsultaDiariaDuplicada.append("INNER JOIN movparcela mpv ON mpv.aulaavulsadiaria = avd.codigo\n");
            sqlConsultaDiariaDuplicada.append("WHERE EXTRACT (HOUR FROM avd.datalancamento) <> '00'\n");
            sqlConsultaDiariaDuplicada.append("AND EXTRACT (MINUTE FROM avd.datalancamento) <> '00'\n");
            sqlConsultaDiariaDuplicada.append("GROUP BY cli.codigomatricula, avd.cliente, avd.datalancamento HAVING COUNT(avd.codigo) > 1\n");
            sqlConsultaDiariaDuplicada.append("ORDER BY avd.datalancamento DESC");

            try (java.sql.Statement stm = con.createStatement()) {
                try (java.sql.ResultSet rs = stm.executeQuery(sqlConsultaDiariaDuplicada.toString())) {
                    while(rs.next()){
                        StringBuilder sqlConsultaSituacaoParcelaDuplicada = new StringBuilder();
                        sqlConsultaSituacaoParcelaDuplicada.append("SELECT\n");
                        sqlConsultaSituacaoParcelaDuplicada.append("\tmpv.codigo,\n");
                        sqlConsultaSituacaoParcelaDuplicada.append("\tmpv.situacao,\n");
                        sqlConsultaSituacaoParcelaDuplicada.append("\tmpv.aulaavulsadiaria\n");
                        sqlConsultaSituacaoParcelaDuplicada.append("FROM movparcela mpv\n");
                        sqlConsultaSituacaoParcelaDuplicada.append("WHERE mpv.codigo IN (" + rs.getString("codParcelas") + ")");
                        try (java.sql.ResultSet rsParcelaDuplicada = stm.executeQuery(sqlConsultaSituacaoParcelaDuplicada.toString())) {
                            while (rsParcelaDuplicada.next()){
                                String situacaoMovParcela = rsParcelaDuplicada.getString("situacao");
                                int codigoMovParcela = rsParcelaDuplicada.getInt("codigo");
                                int codigoAulaAvulsaDiaria = rsParcelaDuplicada.getInt("aulaavulsadiaria");
                                if (!situacaoMovParcela.equalsIgnoreCase("EA")){
                                    continue;
                                }
                                try (java.sql.ResultSet rsMovprodutoParcela = stm.executeQuery("SELECT codigo, movproduto FROM movprodutoparcela WHERE movparcela = " + codigoMovParcela)) {
                                    while (rsMovprodutoParcela.next()) {
                                        StringBuilder deleteMovprodutoParcelaAndMovproduto = new StringBuilder();
                                        deleteMovprodutoParcelaAndMovproduto.append("DELETE FROM movprodutoparcela WHERE codigo = " + rsMovprodutoParcela.getInt("codigo")).append(";\n");
                                        deleteMovprodutoParcelaAndMovproduto.append("DELETE FROM movproduto WHERE codigo = " + rsMovprodutoParcela.getInt("movproduto"));
                                        try (PreparedStatement psDeleteMovprodutoParcelaAndMovproduto = con.prepareStatement(deleteMovprodutoParcelaAndMovproduto.toString())) {
                                            psDeleteMovprodutoParcelaAndMovproduto.execute();
                                        }
                                    }
                                }
                                try (PreparedStatement psDeleteMovparcela = con.prepareStatement("DELETE FROM movparcela WHERE codigo = " + codigoMovParcela)) {
                                    psDeleteMovparcela.execute();
                                }
                                try (PreparedStatement psDeletePeriodoAcessoVendaAvulsa = con.prepareStatement("DELETE FROM periodoacessocliente WHERE aulaavulsadiaria = " + codigoAulaAvulsaDiaria)) {
                                    psDeletePeriodoAcessoVendaAvulsa.execute();
                                }
                                try (PreparedStatement psDeleteVendaAvulsaDiaria = con.prepareStatement("DELETE FROM aulaavulsadiaria WHERE codigo = " + codigoAulaAvulsaDiaria)) {
                                    psDeleteVendaAvulsaDiaria.execute();
                                }
                                break;
                            }
                        }
                    }
                }
            }

            Uteis.logarDebug("FIM | ProcessoAjustarDiariasDuplicadas");
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO | ProcessoAjustarDiariasDuplicadas - " + ex.getMessage());
        }
    }

}
