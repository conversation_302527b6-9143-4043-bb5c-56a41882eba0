package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.crm.AberturaMetaVO;
import negocio.comuns.crm.FecharMetaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.crm.AberturaMeta;
import negocio.facade.jdbc.crm.FecharMeta;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by glauco on 05/12/2014
 */
public class GerarConversoesCRM {


    public static void main(String[] args) {
        try {
//            Connection con = DriverManager.getConnection("************************************************", "postgres", "pactodb");
            Connection con = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "corposaude");
            gerarConversoes(con);
        } catch (Exception ex) {
            Logger.getLogger(GerarConversoesCRM.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void gerarConversoes(Connection con) throws Exception {
        System.out.println("Obtendo lista de contratos");
        List<ContratoProcesso> contratos = obterContratosPeriodo(con);
        System.out.println("Lista de contratos: " + contratos.size());

        System.out.println("Obtendo lista de fases");
        List<FasesCRMEnum> fases = montarListaFases();
        List<FasesCRMEnum> conversoes = montarListaFasesConversao();
        System.out.println("Lista de fases: " + fases.size());

        for (int i = 0; i < fases.size(); i++) {
            FasesCRMEnum fase = fases.get(i);
            FasesCRMEnum faseConversao = conversoes.get(i);

            System.out.println("Processando fase: " + fase.getDescricao());
            for (ContratoProcesso contProc : contratos) {
                contProc.gerarConversao = null;
            }

            System.out.println("Verificando se existem contatos realizados");
            for (ContratoProcesso contProc : contratos) {
                contProc.gerarConversao = existeContatoRealizado(contProc, fase.getSigla(), con);
            }

            System.out.println("Desativando AutoCommit");
            con.setAutoCommit(false);
            try {
                System.out.println("Incrementando Metas");
                for (ContratoProcesso contProc : contratos) {
                    if (contProc.gerarConversao) {
                        incremetarFecharMeta(contProc, faseConversao, con);
                    }
                }
            } catch (Exception ex) {
                System.out.println("ROLLBACK: " + ex.getMessage());
                con.rollback();
            } finally {
                con.setAutoCommit(true);
            }
            System.out.println("Finalizando fase: " + fase.getDescricao());
        }
    }

    private static List<FasesCRMEnum> montarListaFasesConversao() {
        ArrayList<FasesCRMEnum> fases = new ArrayList<FasesCRMEnum>();
        fases.add(FasesCRMEnum.CONVERSAO_DESISTENTES);
        fases.add(FasesCRMEnum.CONVERSAO_EX_ALUNOS);
        fases.add(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS);
        return fases;
    }

    private static List<FasesCRMEnum> montarListaFases() {
        ArrayList<FasesCRMEnum> fases = new ArrayList<FasesCRMEnum>();
        fases.add(FasesCRMEnum.DESISTENTES);
        fases.add(FasesCRMEnum.EX_ALUNOS);
        fases.add(FasesCRMEnum.VISITANTES_ANTIGOS);
        fases.add(FasesCRMEnum.VISITA_RECORRENTE);
        return fases;
    }

    public static void incremetarFecharMeta(ContratoProcesso contrato, FasesCRMEnum fase, Connection con) throws Exception {
        FecharMeta fm = new FecharMeta(con);
        AberturaMeta am = new AberturaMeta(con);
        FecharMetaVO fecharMetaVO = new FecharMetaVO();
        AberturaMetaVO aberturaMetaVO;
        boolean consultarAberturaMeta = false;
        boolean existeAberturaMeta = false;
        try {
            fecharMetaVO = fm.consultarPorIdentificadorMetaPorDiaPorColaborador(
                    fase.getSigla(), contrato.lancamento,
                    contrato.colaborador, false, contrato.empresa,
                    Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        } catch (ConsistirException ignored) {
            consultarAberturaMeta = true;
            aberturaMetaVO = am.consultarAberturaPorCodigoUsuario(
                    contrato.colaborador, contrato.lancamento,
                    contrato.empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            existeAberturaMeta = aberturaMetaVO.getCodigo() != null;

            if (existeAberturaMeta) {
                fecharMetaVO.setAberturaMetaVO(aberturaMetaVO);
                fecharMetaVO.setIdentificadorMeta(fase.getSigla());
                fecharMetaVO.setFase(fase);
                fecharMetaVO.setDataRegistro(contrato.lancamento);
                fm.incluir(fecharMetaVO);
            }
        }

        if (consultarAberturaMeta && existeAberturaMeta || !consultarAberturaMeta) {
            System.out.println("Incrementando fase " + fase.getDescricao() +
                    " do dia: " + contrato.lancamento + " para empresa: " + contrato.empresa +
                    " contrato responsável: " + contrato.contrato);
            fecharMetaVO.setMetaAtingida(fecharMetaVO.getMetaAtingida() + 1.0);
            fm.alteraSemSubordinada(fecharMetaVO);
        }
    }

    public static boolean existeContatoRealizado(
            ContratoProcesso contrato, String identificadorMeta, Connection con) throws Exception {
        Date passado = Uteis.somarDias(contrato.lancamento, -20);
        String sqlContato = "SELECT EXISTS\n" +
                "(\n" +
                "    SELECT fmd.codigo\n" +
                "    FROM fecharmetadetalhado fmd\n" +
                "      INNER JOIN fecharmeta fm ON fm.codigo = fmd.fecharmeta\n" +
                "      INNER JOIN aberturameta am ON fm.aberturameta = am.codigo\n" +
                "    WHERE 1 = 1\n" +
                "          AND identificadormeta = '" + identificadorMeta + "'\n" +
                "          AND am.dia BETWEEN '" + Uteis.getDataJDBC(passado) + "' AND '" + Uteis.getDataJDBC(contrato.lancamento) + "'\n" +
                "          AND fmd.obtevesucesso = TRUE \n" +
                "          AND (UPPER(origem) = 'CONTRATO' AND codigoorigem = " + contrato.contrato + " OR\n" +
                "               UPPER(origem) = 'CLIENTE' AND codigoorigem = " + contrato.cliente + " OR\n" +
                "               UPPER(origem) = 'Ex-Aluno' AND codigoorigem = " + contrato.cliente + ")\n" +
                ");";

        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlContato);
        tabelaResultado.next();
        return tabelaResultado.getBoolean(1);
    }

    public static List<ContratoProcesso> obterContratosPeriodo(Connection con) throws Exception {
        Date hoje = Calendario.hoje();
        Date passado = Uteis.somarDias(hoje, -180);

        PreparedStatement ps = con.prepareStatement("SELECT\n" +
                "  con.codigo AS contrato,\n" +
                "  con.pessoa AS pessoa,\n" +
                "  cli.codigo AS cliente,\n" +
                "  usu.codigo AS colaborador,\n" +
                "  con.empresa AS empresa,\n" +
                "  datalancamento\n" +
                "FROM contrato con\n" +
                "  INNER JOIN cliente cli ON con.pessoa = cli.pessoa\n" +
                "  LEFT JOIN usuario usu ON con.consultor = usu.colaborador\n" +
                "WHERE datalancamento BETWEEN '" + Uteis.getDataJDBC(passado) + "' AND '" + Uteis.getDataJDBC(hoje) + "'\n" +
                "ORDER BY datalancamento;");
        ResultSet rs = ps.executeQuery();

        List<ContratoProcesso> contratos = new ArrayList<ContratoProcesso>();
        while (rs.next()) {
            ContratoProcesso contrato = new ContratoProcesso();
            contrato.contrato = rs.getInt("contrato");
            contrato.empresa = rs.getInt("empresa");
            contrato.cliente = rs.getInt("cliente");
            contrato.pessoa = rs.getInt("pessoa");
            contrato.lancamento = rs.getDate("datalancamento");
            contrato.colaborador = rs.getInt("colaborador");
            contratos.add(contrato);
        }
        return contratos;
    }

}

class ContratoProcesso {
    public Integer contrato = 0;
    public Integer empresa = 0;
    public Integer cliente = 0;
    public Integer pessoa = 0;
    public Integer colaborador = 0;
    public Date lancamento = Calendario.hoje();
    public Boolean gerarConversao = null;
}
