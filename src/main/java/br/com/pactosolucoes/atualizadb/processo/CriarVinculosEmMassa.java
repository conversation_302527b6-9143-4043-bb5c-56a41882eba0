package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

public class CriarVinculosEmMassa {

	
	public static void criarVinculos(Connection con, List<ColaboradorVO> colaboradores, 
			String tipoVinculo, String situacao, boolean situacaoContrato) throws Exception{
		
		ResultSet count = SuperFacadeJDBC.criarConsulta("SELECT count(*) as total FROM situacaoclientesinteticodw "+  
				  "WHERE codigocliente not in (" +
				  "select cliente from vinculo where tipovinculo like '"+tipoVinculo+"') and "+ (situacaoContrato ? " situacaocontrato " : " situacao ") +" like '"+situacao+"'", con);
		
		ResultSet clientes = SuperFacadeJDBC.criarConsulta("SELECT codigocliente FROM situacaoclientesinteticodw s "+
									  " inner join cliente c on s.codigocliente = c.codigo "+
									  "WHERE codigocliente not in (" +
									  "select cliente from vinculo where tipovinculo like '"+tipoVinculo+"') and "+ (situacaoContrato ? " s.situacaocontrato " : " s.situacao ") +" like '"+situacao+"' ORDER BY codigocliente", con);
		count.next();
		int total = count.getInt(1);
		System.out.println("-------------------------"+situacao +" ===== "+ total+"-------------------------\n\n\n");
		List<Integer> codigosClientes = new ArrayList<Integer>();
		List<Integer> codigosClientesUsados = new ArrayList<Integer>();
		while(clientes.next()){
			codigosClientes.add(clientes.getInt("codigocliente"));
		}
		int numeroClientesPorColaborador = total/colaboradores.size();
		
		int contCol = 0;
		for(ColaboradorVO col : colaboradores){
			contCol++;
			for(int cont = ((contCol*numeroClientesPorColaborador)-numeroClientesPorColaborador); 
					cont < ((contCol*numeroClientesPorColaborador)); cont++){
				try{
				gravar(col, tipoVinculo, codigosClientes.get(cont), con);
				codigosClientesUsados.add(codigosClientes.get(cont));
				}catch (IndexOutOfBoundsException e) {
					System.out.println("------------"); 
				}
			}
			
		}
		codigosClientes.removeAll(codigosClientesUsados);
		if(!codigosClientes.isEmpty()){
			for(Integer codigoCliente : codigosClientes){
				gravar(colaboradores.get(contCol-1), tipoVinculo, codigoCliente, con);
			}
		}
		
	}
	
	
	public static void gravar(ColaboradorVO col, String tipoVinculo, int codigoCliente, Connection con) throws Exception{
		SuperFacadeJDBC.executarConsulta("INSERT INTO VINCULO (colaborador, tipovinculo, cliente) VALUES" +
				" ("+col.getCodigo()+", '"+tipoVinculo+"', "+codigoCliente+" )", con);
		
		PreparedStatement stm = con.prepareStatement("INSERT INTO historicovinculo (dataregistro, tipohistoricovinculo, tipocolaborador, cliente, colaborador, origem) "+
				" values (?,?,?,?,?, ?)");
		int i = 1;
		stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
		stm.setString(i++, "EN");
		stm.setString(i++, tipoVinculo);
		stm.setInt(i++, codigoCliente);
		stm.setInt(i++, col.getCodigo());
		stm.setString(i++, "PROCESSO EM MASSA");
		
		stm.execute();
		
		System.out.println(col.getPessoa().getNome() + " - "+ codigoCliente);
	}

    public void executarProcesso(Connection con, List<ColaboradorVO> colaboradores) throws Exception {
        criarVinculos (con, colaboradores, TipoColaboradorEnum.CONSULTOR.getSigla(), "AT", false);
        criarVinculos (con, colaboradores, TipoColaboradorEnum.CONSULTOR.getSigla(), "TR", true);
        criarVinculos (con, colaboradores, TipoColaboradorEnum.CONSULTOR.getSigla(), "TR", false);
        criarVinculos (con, colaboradores, TipoColaboradorEnum.CONSULTOR.getSigla(), "AE", true);
        criarVinculos (con, colaboradores, TipoColaboradorEnum.CONSULTOR.getSigla(), "CR", true);
        criarVinculos (con, colaboradores, TipoColaboradorEnum.CONSULTOR.getSigla(), "NO", true);
        criarVinculos (con, colaboradores, TipoColaboradorEnum.CONSULTOR.getSigla(), "CA", true);
        criarVinculos (con, colaboradores, TipoColaboradorEnum.CONSULTOR.getSigla(), "DE", true);
        criarVinculos (con, colaboradores, TipoColaboradorEnum.CONSULTOR.getSigla(), "VI", false);
        criarVinculos (con, colaboradores, TipoColaboradorEnum.CONSULTOR.getSigla(), "VE", true);
    }

	public static void main(String[] args) {
//		"TAINA CAMILO DE ARAUJO";23
//		"AMANDA BENTO TEIXEIRA";34
//		"BIANCA SALGADO SCHROEDER ALVES";38
//		"ANEDIA SOUSA FONSECA";31
		ColaboradorVO col1 = new ColaboradorVO();
		ColaboradorVO col2 = new ColaboradorVO();
		ColaboradorVO col3 = new ColaboradorVO();
		ColaboradorVO col4 = new ColaboradorVO();
		List<ColaboradorVO> colaboradores = new ArrayList<ColaboradorVO>();
		col1.getPessoa().setNome("TAINA CAMILO DE ARAUJO");
		col1.setCodigo(23);
		colaboradores.add(col1);
		col2.getPessoa().setNome("AMANDA BENTO TEIXEIRA");
		col2.setCodigo(34);
		colaboradores.add(col2);
		col3.getPessoa().setNome("BIANCA SALGADO SCHROEDER ALVES");
		col3.setCodigo(38);
		colaboradores.add(col3);
		col4.getPessoa().setNome("ANEDIA SOUSA FONSECA");
		col4.setCodigo(31);
		colaboradores.add(col4);
		
		try{
			Connection con1 = DriverManager.getConnection("***********************************************************************", "postgres", "pactodb");
            new CriarVinculosEmMassa().executarProcesso(con1, colaboradores);
		}catch (Exception e) {
			e.printStackTrace();
		}
	}
	
}
