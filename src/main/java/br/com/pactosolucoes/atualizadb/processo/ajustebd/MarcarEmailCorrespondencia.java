package br.com.pactosolucoes.atualizadb.processo.ajustebd;

import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AmostraClienteTO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Empresa;

import javax.faces.model.SelectItem;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by GlaucoT on 10/04/2017
 */
public class MarcarEmailCorrespondencia extends SuperEntidade {

    private List<SelectItem> listaEmpresas;
    private int codEmpresa;
    private List<AmostraClienteTO> clientesParaMarcar;
    private String msgResultado;
    private UsuarioVO usuarioVO;

    public MarcarEmailCorrespondencia() throws Exception {
        super();
        inicializarListas();
    }

    public MarcarEmailCorrespondencia(Connection conexao) throws Exception {
        super(conexao);
        inicializarListas();
    }

    public void inicializarListas() throws Exception {
        consultarListaEmpresa();
    }

    public List<SelectItem> consultarListaEmpresa() throws Exception {
        listaEmpresas = new ArrayList<SelectItem>();
        listaEmpresas.add(new SelectItem(0, ""));
        Empresa empresaDAO = new Empresa(con);
        List<EmpresaVO> emps = empresaDAO.consultarPorNome("", true, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        for (EmpresaVO empresa : emps) {
            listaEmpresas.add(new SelectItem(empresa.getCodigo(), empresa.getNome()));
        }
        return listaEmpresas;
    }

    public void obterClientesParaMarcar() throws SQLException {
        String sbSql = "SELECT cli.codigo as cliente, cli.matricula, cli.pessoa, pes.nome, e.email FROM email e\n" +
                "LEFT JOIN pessoa pes ON pes.codigo = e.pessoa\n" +
                "LEFT JOIN cliente cli ON cli.pessoa = pes.codigo\n" +
                "WHERE empresa = ?\n" +
                "AND e.emailcorrespondencia = ?";

        PreparedStatement ps = con.prepareStatement(sbSql);
        ps.setInt(1, codEmpresa);
        ps.setBoolean(2, false);
        ResultSet rs = ps.executeQuery();
        List<AmostraClienteTO> amostraClienteTOS = new ArrayList<AmostraClienteTO>();
        while (rs.next()) {
            AmostraClienteTO amostra = new AmostraClienteTO();
            amostra.setMatricula(rs.getString("matricula"));
            amostra.setNome(rs.getString("nome"));
            amostra.setEmails(rs.getString("email"));
            amostra.setCodigoPessoa(rs.getInt("pessoa"));
            amostra.setCodigoCliente(rs.getInt("cliente"));
            amostraClienteTOS.add(amostra);
        }
        setClientesParaMarcar(amostraClienteTOS);
        if (getClientesParaMarcar().size() == 0) {
            setMsgResultado("Não há e-mails para serem marcados.");
        } else if (getClientesParaMarcar().size() == 1) {
            setMsgResultado("Há apenas 1 e-mail para ser marcado.");
        } else {
            setMsgResultado("Existem " + getClientesParaMarcar().size() + " e-mails para serem marcados.");
        }
    }

    public void marcarEmails() throws SQLException {
        setMsgResultado("");
        try {
            con.setAutoCommit(false);
            List<Integer> pessoas = new ArrayList<Integer>();
            for (AmostraClienteTO amostraClienteTO : getClientesParaMarcar()) {
                if (!pessoas.contains(amostraClienteTO.getCodigoPessoa())) {
                    pessoas.add(amostraClienteTO.getCodigoPessoa());

                    String sql = "UPDATE email SET emailcorrespondencia = TRUE WHERE pessoa = " + amostraClienteTO.getCodigoPessoa();
                    PreparedStatement ps = con.prepareStatement(sql);
                    ps.execute();

                    incluirLog(amostraClienteTO, getUsuarioVO());
                }
            }

            con.commit();
            setMsgResultado("E-mails marcados com sucesso;");
        } catch (Exception e) {
            con.rollback();
            setMsgResultado(e.getMessage());
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void incluirLog(AmostraClienteTO amostraClienteTO, UsuarioVO usuarioVO) throws Exception {
        LogVO obj = new LogVO();
        obj.setChavePrimaria(amostraClienteTO.getCodigoCliente().toString());
        obj.setPessoa(amostraClienteTO.getCodigoPessoa());
        obj.setNomeEntidade("PESSOA");
        obj.setNomeEntidadeDescricao("Configurações");
        obj.setOperacao("Marcar E-mail Correspondência");
        obj.setResponsavelAlteracao(usuarioVO.getNome());
        obj.setUserOAMD(usuarioVO.getUserOamd());
        obj.setNomeCampo("emailCorrespondencia");
        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        obj.setValorCampoAnterior("Desmarcado");
        obj.setValorCampoAlterado("Marcado");

        getFacade().getLog().incluir(obj);
    }

    public List<SelectItem> getListaEmpresas() {
        return listaEmpresas;
    }

    public void setListaEmpresas(List<SelectItem> listaEmpresas) {
        this.listaEmpresas = listaEmpresas;
    }

    public int getCodEmpresa() {
        return codEmpresa;
    }

    public void setCodEmpresa(int codEmpresa) {
        this.codEmpresa = codEmpresa;
    }

    public List<AmostraClienteTO> getClientesParaMarcar() {
        if (clientesParaMarcar == null) {
            clientesParaMarcar = new ArrayList<AmostraClienteTO>();
        }
        return clientesParaMarcar;
    }

    public void setClientesParaMarcar(List<AmostraClienteTO> clientesParaMarcar) {
        this.clientesParaMarcar = clientesParaMarcar;
    }

    public String getMsgResultado() {
        return msgResultado;
    }

    public void setMsgResultado(String msgResultado) {
        this.msgResultado = msgResultado;
    }

    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null) {
            usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }
}
