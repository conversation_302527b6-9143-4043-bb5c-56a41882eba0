package test.simulacao;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.funcionais.Function;
import servicos.operacoes.midias.awss3.MigracaoFotosZWParaS3Service;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.function.Predicate;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

public class ExtractTagsMenu {

    public static void main(String[] args) {
        if (args.length == 0)
            args = new String[]{"D:/PactoJ-Novo/zw/src/main/webapp"};
        try {
            final List<Map<String, List<String>>> tags = Files.find(Paths.get(args[0]),
                    Integer.MAX_VALUE,
                    (filePath, fileAttr) -> fileAttr.isRegularFile() && filePath.toString().toLowerCase().endsWith(".jsp"))
                    .map(ExtractTagsMenu::fillTags).collect(Collectors.toList());

            for (Map<String, List<String>> m : tags) {
                m.keySet().stream().filter(e -> !m.get(e).isEmpty()).forEach(p -> {
                    final String v = m.get(p).toString();
                    final String page = p.substring(p.indexOf("webapp")).replace("\\", "/").replace("webapp", "");
                    final String t = v.replace("[<jsp:param name=\"menu\" value=", "").replace("\"", "").replace("/>]", "").replaceAll(" ", "");

                    if (!(page.endsWith("include_box_menulateral.jsp") && t.equals("BI,INICIO"))) {
                        System.out.println(String.format("%s=%s", t, page));
                    }

                });
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static Map<String, List<String>> fillTags(Path path) {
        Map<String, List<String>> m = new HashMap<>();
        try {
            final List<String> input;
            try {
                input = Files.readAllLines(path, StandardCharsets.ISO_8859_1);

                if (input != null && !input.isEmpty()) {
                    Predicate<String> pred = str -> str.contains("<jsp:param name=\"menu\"");
                    int contextLength = 0;

                    m.put(path.toString(), IntStream.range(0, input.size()) // line numbers
                            // filter them leaving only numbers of lines satisfying the predicate
                            .filter(idx -> pred.test(input.get(idx)))
                            // add nearby numbers
                            .flatMap(idx -> IntStream.rangeClosed(idx - contextLength, idx + contextLength))
                            // remove numbers which are out of the input range
                            .filter(idx -> idx >= 0 && idx < input.size())
                            // sort numbers and remove duplicates
                            .distinct().sorted()
                            // map to the lines themselves
                            .mapToObj(input::get)
                            .map(String::trim)
                            // output
                            .collect(Collectors.toList()));
                }
            } catch (Exception e) {
                System.out.println(String.format("Ignoring %s for error %s", path.toString(), e.getMessage()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return m;
    }
}
