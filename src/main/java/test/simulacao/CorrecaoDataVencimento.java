package test.simulacao;


import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class CorrecaoDataVencimento extends FacadeManager {
    
    public CorrecaoDataVencimento() {
    }

    public static void main(String[] args) {
        System.out.println("Inicio das correções de vencimento das parcelas");
        if (args.length == 0) {
            args = new String[]{"mauricio"};
        }
        if (args.length >= 1) {
            String nomeThread = args[0];
            try {
                DAO dao = new DAO();
                Connection con = dao.obterConexaoEspecifica(nomeThread);
                Conexao.guardarConexaoForJ2SE(con);
                new CorrecaoDataVencimento().correcao(con);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        System.out.println("Fim das correções de vencimento das parcelas");
    }

    public void correcao(Connection con) throws Exception {
        List<MovParcelaVO> parcelas = new ArrayList<MovParcelaVO>();
        try {
            // consulta as parcelas que podem ter o problema
            Statement st = con.createStatement();
            ResultSet resultados = st.executeQuery("select * from movparcela where contrato in "
                    + "(select contrato from contratocondicaopagamento where condicaopagamento = 12) order by codigo");
            // prepara a lista de parcelas para serem processadas
            while (resultados.next()) {
                MovParcelaVO aux = new MovParcelaVO();
                aux.setContrato(new ContratoVO());
                aux.setCodigo(resultados.getInt("codigo"));
                aux.getContrato().setCodigo(resultados.getInt("contrato"));
                aux.setDataVencimento(resultados.getDate("datavencimento"));
                parcelas.add(aux);
            }
            String chave = (String) JSFUtilities.getFromSession("key");
            if (chave == null) {
                System.out.println("Nao foi possivel identificar a chave da empresa. \n Iniciando a correção de vencimento das parcelas.");
            } else {
                System.out.println("Iniciando correção de vencimento das parcelas na empresa: " + chave);
            }
            int contratoAtual = 0;
            int mes = 1;
            Date dataCorrigida = null;
            final String aux = "UPDATE MovParcela set dataVencimento=? WHERE codigo = ?;";
            // processa as parcelas corrigindo os vencimentos se necessário
            for (MovParcelaVO mp : parcelas) {
                // se é um novo contrato
                if (contratoAtual == 0 || contratoAtual != mp.getContrato().getCodigo()) {
                    contratoAtual = mp.getContrato().getCodigo();
                    dataCorrigida = mp.getDataVencimento();
                    mes = 1;
                } else {
                    // altera o vencimento das parcelas seguintes
                    Date dataAuxiliar = Uteis.obterDataFuturaParcela(dataCorrigida, mes++);
                    PreparedStatement sqlAlterar = con.prepareStatement(aux);
                    sqlAlterar.setDate(1, Uteis.getDataJDBC(dataAuxiliar));
                    sqlAlterar.setInt(2, mp.getCodigo());
                    sqlAlterar.execute();
                    System.out.println("Alterada a data de vencimento (" + mp.getDataVencimento_Apresentar() + ") da parcela " + mp.getCodigo() + " para (" + dataAuxiliar + ")");
                }
            }
            System.out.println("Finalizada a correção de vencimento das parcelas");
        } finally {
            if (con != null) {
                con.close();
            }
        }
    }
}
