package test.simulacao;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;

public class ObterArquivosRetorno {

    public static void main(String[] args) {
        try {
            String nomeBD = "bdzillyonhabitusvaz-2017-09-23";
            Connection con = DriverManager.getConnection("********************************/" + nomeBD, "postgres", "pactodb");
            obterArquivos(con, nomeBD);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void obterArquivos(Connection con, String nomeBD) throws Exception {
        String path = "/opt/ZW_Retornos/" + nomeBD + "/";
        File file = new File(path);
        boolean criado = file.exists();
        if (!criado) {
            criado = file.mkdir();
        }
        if (!criado) {
            throw new Exception("Não foi criado o diretório");
        }

        ResultSet rsEscolherArquivos = SuperFacadeJDBC.criarConsulta("SELECT distinct replace(('SANTANDER      '||ri.codigosretorno), '|', '') numretorno\n" +
                "FROM remessa r\n" +
                "  INNER JOIN remessaitem ri ON r.codigo = ri.remessa\n" +
                "WHERE dataregistro >= '2017-08-05' AND dataregistro <= '2017-08-23'\n" +
                "      AND r.conveniocobranca = (SELECT codigo FROM conveniocobranca WHERE tipoconvenio = 1)\n" +
                "      AND movpagamento IS NOT NULL", con);
        while (rsEscolherArquivos.next()) {
            String arquivo = rsEscolherArquivos.getString("numretorno");
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT * FROM retornoremessa WHERE arquivoretorno like '%" + arquivo + "%';", con);
            while (rs.next()) {
                String nome = rs.getString("nomearquivo");
                StringBuilder sb = new StringBuilder(rs.getString("arquivoretorno"));
                String pathCriar = path + nome;
                StringUtilities.saveToFile(sb, pathCriar, "UTF-8");
            }
        }
    }
}
