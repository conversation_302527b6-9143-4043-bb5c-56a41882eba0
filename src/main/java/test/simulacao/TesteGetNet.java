package test.simulacao;

import acesso.webservice.AcessoControle;
import br.com.pactosolucoes.atualizadb.negocio.AtualizadorBD;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import org.json.JSONException;
import org.json.JSONObject;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.contrato.*;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeFactory;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.financeiro.TransacaoMovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.basico.ClienteInterfaceFacade;
import org.apache.commons.codec.binary.Base64;
import servicos.impl.apf.APF;
import servicos.impl.apf.RecorrenciaService;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RemessaService;
import servicos.impl.dcc.cielo.LayoutRemessaCieloDCC;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static servicos.impl.dcc.base.LayoutRemessaBase.preencherAtributosTransientes;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 *
 * <AUTHOR>
 */
public class TesteGetNet {

    private static String urlAPI = "https://api-sandbox.getnet.com.br";
    private static String seller_id = "a4a5f080-49c9-4bd1-8aba-7cfcc2314ae7";
    private static String token = "c279e65f-b68b-4828-980c-d6d14da6cbbc";
    private static String clientID = "486a9922-2394-4df7-95a5-1ad22292e330";
    private static String clientSecret = "90fe0ff1-4221-4da5-bb9e-810a9022e303";

    public static void main(String args[]) throws Exception {

        executarRequestGetNetPagamento();
//        executarRequestGetNetTokenCartao();
    }

    private static void executarRequestGetNetTokenCartao() throws IOException, JSONException {

        String token = obterToken();

        String path = urlAPI + "/v1/tokens/card";
        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("Authorization", "Bearer " + token);
        header.put("seller_id", seller_id);

        JSONObject json = new JSONObject();
        json.put("card_number", "****************");

        String teste1 = ExecuteRequestHttpService.executeHttpRequest(path, json.toString(), header, ExecuteRequestHttpService.METODO_POST, "UTF-8");
        System.out.println(teste1);
    }


    private static void executarRequestGetNetPagamento() throws IOException, JSONException {
        String path = urlAPI + "/v1/payments/credit";
        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("Authorization", "Bearer " + obterToken());
        header.put("seller_id", seller_id);

        String dados = "{\"amount\":1000,\"order\":{\"sales_tax\":0,\"order_id\":\"20181002183335\"},\"seller_id\":\"a4a5f080-49c9-4bd1-8aba-7cfcc2314ae7\",\"credit\":{\"number_installments\":1,\"delayed\":false,\"transaction_type\":\"INSTALL_NO_INTEREST\",\"card\":{\"expiration_month\":\"11\",\"number_token\":\"d09550e429be849678332446fd49e1071c8df5348fde4f25754980ab6d028afa6225bd7bb1c0cad26db3a231bb94e4ad7edf1f4cce5844519e42da3582ccfbc6\",\"expiration_year\":\"20\",\"security_code\":\"789\",\"cardholder_name\":\"LUIIZ NAO APROVA\"},\"soft_descriptor\":\"JUST FIT PARTICIPACOES\",\"save_card_data\":false},\"customer\":{\"customer_id\":\"customer_21081826\",\"billing_address\":{}},\"customer_id\":\"customer_21081826\",\"currency\":\"BRL\"}";


        String retorno = ExecuteRequestHttpService.executeHttpRequest(path, dados, header, ExecuteRequestHttpService.METODO_POST, "UTF-8");
        System.out.println(retorno);
    }

    private static String obterToken() throws IOException, JSONException {
        String path = urlAPI + "/auth/oauth/v2/token";
        Map<String, String> headers = new HashMap<String, String>();
        String auto = clientID + ":" + clientSecret;
        headers.put("Authorization", "Basic " + new String(new Base64().encode(auto.getBytes())));
        headers.put("Content-type", "application/x-www-form-urlencoded");

        String retorno = ExecuteRequestHttpService.executeHttpRequest(path, "scope=oob&grant_type=client_credentials", headers, ExecuteRequestHttpService.METODO_POST, "UTF-8");
        JSONObject jsonObject = new JSONObject(retorno);
        return jsonObject.getString("access_token");
    }
}
