package importador.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public class AcessosImportacaoJSON extends SuperJSON {

    private Integer idExterno;
    private Integer idExternoCliente;
    private Integer idExternoColaborador;
    private Date dataEntrada;
    private Date dataSaida;

    public Integer getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(Integer idExterno) {
        this.idExterno = idExterno;
    }

    public Integer getIdExternoCliente() {
        return idExternoCliente;
    }

    public void setIdExternoCliente(Integer idExternoCliente) {
        this.idExternoCliente = idExternoCliente;
    }

    public Date getDataEntrada() {
        return dataEntrada;
    }

    public void setDataEntrada(Date dataEntrada) {
        this.dataEntrada = dataEntrada;
    }

    public Date getDataSaida() {
        return dataSaida;
    }

    public void setDataSaida(Date dataSaida) {
        this.dataSaida = dataSaida;
    }

    public Integer getIdExternoColaborador() {
        return idExternoColaborador;
    }

    public void setIdExternoColaborador(Integer idExternoColaborador) {
        this.idExternoColaborador = idExternoColaborador;
    }
}
