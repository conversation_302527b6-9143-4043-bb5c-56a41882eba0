package importador.alunosTurma;


import br.com.pactosolucoes.oamd.controle.basico.DAO;
import importador.ImportacaoCache;
import importador.ImportacaoConfigTO;
import importador.LeitorExcel2010;
import importador.UteisImportacaoExcel;
import importador.json.AlunoTurmaImportacaoJSON;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfigExcelImportacaoTO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.ProcessoImportacaoItemVO;
import negocio.comuns.basico.ProcessoImportacaoLogVO;
import negocio.comuns.basico.ProcessoImportacaoVO;
import negocio.comuns.basico.enumerador.TipoImportacaoEnum;
import negocio.comuns.basico.enumerador.TipoLogEnum;
import negocio.comuns.contrato.ContratoModalidadeHorarioTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoModalidadeVezesSemanaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoModalidadeVO;
import negocio.comuns.plano.PlanoModalidadeVezesSemanaVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.ProcessoImportacao;
import negocio.facade.jdbc.basico.ProcessoImportacaoItem;
import negocio.facade.jdbc.basico.ProcessoImportacaoLog;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoModalidade;
import negocio.facade.jdbc.contrato.ContratoModalidadeVezesSemana;
import negocio.facade.jdbc.plano.HorarioTurma;
import negocio.facade.jdbc.plano.Modalidade;
import negocio.facade.jdbc.plano.PlanoModalidade;
import negocio.facade.jdbc.plano.PlanoModalidadeVezesSemana;
import negocio.facade.jdbc.plano.Turma;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.json.JSONArray;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

public class ImportarAlunosTurma {

    private Connection con;
    private ImportacaoConfigTO configTO;
    private Contrato contratoDAO;
    private ContratoModalidade contratoModalidadeDAO;
    private ContratoModalidadeVezesSemana contratoModalidadeVezesSemanaDAO;
    private Modalidade modalidadeDAO;
    private Turma turmaDAO;
    private HorarioTurma horarioTurmaDAO;
    private Usuario usuarioDAO;
    private Log logDAO;
    ImportacaoCache importacaoCache;

    public ImportarAlunosTurma(Connection con, ImportacaoConfigTO configTO) throws Exception {
        this.con = con;
        this.configTO = configTO;
        this.contratoDAO = new Contrato(con);
        this.turmaDAO = new Turma(con);
        this.horarioTurmaDAO = new HorarioTurma(con);
        this.contratoModalidadeDAO = new ContratoModalidade(con);
        this.contratoModalidadeVezesSemanaDAO = new ContratoModalidadeVezesSemana(con);
        this.modalidadeDAO = new Modalidade(con);
        this.logDAO = new Log(con);
        this.usuarioDAO = new Usuario(con);
        if (UteisValidacao.emptyNumber(configTO.getUsuarioResponsavelImportacao())) {
            configTO.setUsuarioResponsavelImportacao(usuarioDAO.consultarPorUsername("pactobr", Uteis.NIVELMONTARDADOS_DADOSBASICOS).getCodigo());
        }
        this.importacaoCache = new ImportacaoCache(con, configTO.getUsuarioResponsavelImportacao());
    }

    private PlanoModalidadeVezesSemanaVO forcarModalidadeNoPlano(ModalidadeVO modalidadeVO, final Integer vezesSemana, final Integer plano) throws Exception {
        Set<Integer> listaCodigo = new HashSet<Integer>();
        PlanoModalidade planoModalidadeDAO = new PlanoModalidade(con);
        PlanoModalidadeVezesSemana planoVezesSemanaDAO = new PlanoModalidadeVezesSemana(con);

        PlanoModalidadeVO planoModalidadeVO = planoModalidadeDAO.consultar(plano, modalidadeVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!UtilReflection.objetoMaiorQueZero(planoModalidadeVO, "getCodigo()")) {
            planoModalidadeVO = new PlanoModalidadeVO();
            planoModalidadeVO.setValidarDados(false);
            planoModalidadeVO.setModalidade(new ModalidadeVO());
            planoModalidadeVO.getModalidade().setCodigo(modalidadeVO.getCodigo());
            planoModalidadeVO.setPlano(plano);
            planoModalidadeVO.setPlanoModalidadeVezesSemanaVOs(new ArrayList());
            planoModalidadeDAO.incluir(planoModalidadeVO);
        }
        listaCodigo.add(planoModalidadeVO.getCodigo());

        PlanoModalidadeVezesSemanaVO planoModalidadeVezesSemanaVO = planoVezesSemanaDAO.consultar(planoModalidadeVO.getCodigo(), vezesSemana, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (planoModalidadeVezesSemanaVO == null) {
            planoModalidadeVezesSemanaVO = new PlanoModalidadeVezesSemanaVO();
            planoModalidadeVezesSemanaVO.setValidarDados(false);
            planoModalidadeVezesSemanaVO.setPlanoModalidade(planoModalidadeVO.getCodigo());
            planoModalidadeVezesSemanaVO.setNrVezes(vezesSemana);
            planoModalidadeVezesSemanaVO.setPercentualDesconto(0.0);
            planoModalidadeVezesSemanaVO.setValorEspecifico(0.0);
            planoModalidadeVezesSemanaVO.setTipoValor("");
            planoModalidadeVezesSemanaVO.setReferencia(false);
            planoVezesSemanaDAO.incluir(planoModalidadeVezesSemanaVO);
        }

        for (Integer codigo : listaCodigo) {
            PlanoModalidadeVO planoModalidadeDescVO = planoModalidadeDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_TODOS);
            StringBuilder descVzSemana = new StringBuilder();
            for (Object obj : planoModalidadeDescVO.getPlanoModalidadeVezesSemanaVOs()) {
                PlanoModalidadeVezesSemanaVO planoModalidadeVezesSemanaDescVO = (PlanoModalidadeVezesSemanaVO) obj;
                String vP;
                if (planoModalidadeVezesSemanaDescVO.getNrVezes() == 1) {
                    vP = " Vez";
                } else {
                    vP = " Vezes";
                }
                DecimalFormat decimalFormat = new DecimalFormat("R$ #,##0.00");
                if (descVzSemana.length() <= 0) {
                    descVzSemana.append(planoModalidadeVezesSemanaVO.getNrVezes()).append(vP).append(" - " + decimalFormat.format(0));
                } else {
                    descVzSemana.append(", ").append(planoModalidadeVezesSemanaVO.getNrVezes()).append(vP).append(" - " + decimalFormat.format(0));
                }
            }
            Statement st = planoModalidadeDAO.getCon().createStatement();
            st.execute("update planoModalidade set listaVezesSemana = '" + descVzSemana.toString() + "' where codigo =" + codigo);
        }
        return planoModalidadeVezesSemanaVO;
    }

    public String alterContratoIncluindoTurma(Integer contrato, Integer modalidade, JSONArray horariosTurma,
                                              boolean forcarPlanoModalidade, boolean adicionarContratoModalidade) throws Exception {
        ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_TODOS);
        contratoVO.setIncluindoTurma(true);
        AtomicBoolean temModalidade = new AtomicBoolean(false);
        for (ContratoModalidadeVO cM : contratoVO.getContratoModalidadeVOs()) {
            if (!temModalidade.get() && cM.getModalidade().getCodigo().equals(modalidade)) {
                if (cM.getModalidade().getUtilizarTurma()) {
                    temModalidade.set(true);
                    if (!UteisValidacao.emptyList(cM.getContratoModalidadeTurmaVOs())) {
                        throw new ConsistirException("Esse contrato já possui turmas para a modalidade informada!");
                    }
                } else {
                    throw new ConsistirException("Modalidade não permite turmas");
                }
            }
        }

        if (!temModalidade.get() && adicionarContratoModalidade) {
            ModalidadeVO modalidadeVO = this.modalidadeDAO.consultarPorChavePrimaria(modalidade, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (modalidadeVO == null) {
                throw new ConsistirException(String.format("Modalidade não encontrada: %d", modalidade));
            }
            UsuarioVO usuarioResponsaveVO = null;
            if (configTO != null) {
                usuarioResponsaveVO = importacaoCache.obterUsuarioVO(configTO.getUsuarioResponsavelImportacao());
            }
            adicionarModalidadeContrato(contratoVO, modalidadeVO, usuarioResponsaveVO);
        } else if (!temModalidade.get()) {
            throw new ConsistirException("Modalidade não encontrada no contrato");
        }

        List<Integer> codigosHorariosTurmas = obterCodigosHorariosTurmas(contratoVO);
        List<TurmaVO> turmasHorariosSelecionados = new ArrayList<>();
        List<Integer> horarios = new ArrayList<>();
        for (int i = 0; i < horariosTurma.length(); i++) {
            boolean horarioTurmaExiste = SuperFacadeJDBC.existe("select ht.codigo from horarioTurma ht\n" +
                    "inner join turma t on t.codigo = ht.turma where t.empresa = "+contratoVO.getEmpresa().getCodigo()+" and ht.codigo = " + horariosTurma.getInt(i), con);
            if (!horarioTurmaExiste) {
                throw new Exception(String.format("Horário turma não encontrado: %d para a empresa: %s", horariosTurma.getInt(i), contratoVO.getEmpresa().getNome()));
            }
            if (!codigosHorariosTurmas.contains(horariosTurma.getInt(i))) {
                horarios.add(horariosTurma.getInt(i));
            }
        }

        if (horarios.isEmpty()) {
            return "Nenhum para adicionar!";
        }

        montarHorariosTurmas(contratoVO, horarios, turmasHorariosSelecionados);
        if (turmasHorariosSelecionados.isEmpty()) {
            throw new Exception(String.format("Nenhuma turma encontrada para modalidade informada: %d", modalidade));
        }
        contratoVO.getContratoModalidadeVOs().forEach(cm -> {
            if (cm.getModalidade().getCodigo().equals(modalidade)) {
                cm.setNrVezesSemana(horarios.size());
                cm.getModalidade().setNrVezes(horarios.size());
            }
        });
        processarTurmasModalidades(contratoVO, turmasHorariosSelecionados);

        if (forcarPlanoModalidade) {
            ContratoModalidadeVO cmVO = contratoVO.getContratoModalidadeVOs().stream().filter(cm -> cm.getModalidade().getCodigo().equals(modalidade)).findFirst().get();
            if (cmVO != null) {
                PlanoModalidadeVezesSemanaVO pmvs = forcarModalidadeNoPlano(cmVO.getModalidade(), cmVO.getModalidade().getNrVezes(), contratoVO.getPlano().getCodigo());
                if (pmvs != null) {
                    cmVO.setPlanoVezesSemanaVO(pmvs);
                }
            }
        }

        contratoDAO.alterar(contratoVO);
        contratoDAO.gerarMatriculaAlunoTurma(contratoVO);
        return "Turmas incluída com sucesso!";
    }

    private List<Integer> obterCodigosHorariosTurmas(ContratoVO contratoVO) {
        List<Integer> horariosTurmas = new ArrayList<>();
        for (ContratoModalidadeVO cmVO : contratoVO.getContratoModalidadeVOs()) {
            for (Object cmtVOObj : cmVO.getContratoModalidadeTurmaVOs()) {
                for (Object cmhtVOObj : ((ContratoModalidadeTurmaVO) cmtVOObj).getContratoModalidadeHorarioTurmaVOs()) {
                    ContratoModalidadeHorarioTurmaVO cmhtVO = (ContratoModalidadeHorarioTurmaVO) cmhtVOObj;
                    if (!horariosTurmas.contains(cmhtVO.getHorarioTurma().getCodigo())) {
                        horariosTurmas.add(cmhtVO.getHorarioTurma().getCodigo());
                    }
                }
            }
        }
        return horariosTurmas;
    }

    private void montarHorariosTurmas(ContratoVO contratoVO, List<Integer> horarios, List<TurmaVO> turmasHorariosSelecionados) throws Exception {
        for (Integer codigoHorario : horarios) {
            HorarioTurmaVO horario = horarioTurmaDAO.consultarPorCodigo(codigoHorario, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            if (horario != null) {
                for (ContratoModalidadeVO cModalidade : contratoVO.getContratoModalidadeVOs()) {
                    TurmaVO turma = turmaDAO.consultarPorChavePrimaria(horario.getTurma(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (turma.getModalidade().getCodigo().equals(cModalidade.getModalidade().getCodigo())) {
                        if (contratoVO.getPessoa().getDataNasc() != null) {
                            Integer idadeAluno = Uteis.calcularIdadePessoa(Calendario.hoje(), contratoVO.getPessoa().getDataNasc());
                            if (idadeAluno >= turma.getIdadeMinima() && idadeAluno <= turma.getIdadeMaxima()) {
                                if (!turmasHorariosSelecionados.isEmpty()) {
                                    boolean achouTurma = false;
                                    for (TurmaVO turmaHorario : turmasHorariosSelecionados) {
                                        if (turmaHorario.getCodigo().equals(turma.getCodigo())) {
                                            achouTurma = true;
                                            turmaHorario.getHorarioTurmaVOs().add(horario);
                                        }
                                    }
                                    if (!achouTurma) {
                                        turma.setHorarioTurmaVOs(new ArrayList());
                                        turma.getHorarioTurmaVOs().add(horario);
                                        turmasHorariosSelecionados.add(turma);
                                    }
                                } else {
                                    turma.setHorarioTurmaVOs(new ArrayList());
                                    turma.getHorarioTurmaVOs().add(horario);
                                    turmasHorariosSelecionados.add(turma);
                                }
                            } else {
                                throw new Exception("O horario " + horario.getDiaSemana_Apresentar() + " - " + horario.getHoraInicial() + " às " + horario.getHoraFinal() + " não é compatível com a idade do aluno");
                            }
                        } else {
                            throw new Exception("Data de nascimento do aluno inválida");
                        }
                    }
                }
            }

        }
    }

    private void processarTurmasModalidades(ContratoVO contratoVO, List<TurmaVO> turmasHorariosSelecionados) {
        try {
            for (ContratoModalidadeVO contratoModalidadeVO : contratoVO.getContratoModalidadeVOs()) {
                for (TurmaVO turma : turmasHorariosSelecionados) {
                    if (contratoModalidadeVO.getModalidade().getCodigo().equals(turma.getModalidade().getCodigo())) {
                        ContratoModalidadeTurmaVO contratoModalidadeTurmaVO = new ContratoModalidadeTurmaVO();
                        List<ContratoModalidadeHorarioTurmaVO> contratoModalidadeHorariosTurmaVO = new ArrayList<>();
                        contratoModalidadeTurmaVO.setContratoModalidade(contratoModalidadeVO.getCodigo());
                        turma.setTurmaEscolhida(true);
                        contratoModalidadeTurmaVO.setTurma(turma);
                        for (HorarioTurmaVO horario : turma.getHorarioTurmaVOs()) {
                            ContratoModalidadeHorarioTurmaVO contratoModalidadeHorarioTurmaVO = new ContratoModalidadeHorarioTurmaVO();
                            horario.setHorarioTurmaEscolhida(true);
                            contratoModalidadeHorarioTurmaVO.setHorarioTurma(horario);
                            contratoModalidadeHorariosTurmaVO.add(contratoModalidadeHorarioTurmaVO);
                        }
                        contratoModalidadeTurmaVO.setContratoModalidadeHorarioTurmaVOs(contratoModalidadeHorariosTurmaVO);
                        if (contratoModalidadeVO.getContratoModalidadeTurmaVOs() == null) {
                            contratoModalidadeVO.setContratoModalidadeTurmaVOs(new ArrayList<>());
                        }
                        contratoModalidadeVO.getContratoModalidadeTurmaVOs().add(contratoModalidadeTurmaVO);


                        ContratoModalidadeVezesSemanaVO contratoModalidadeVezesSemanaVO = new ContratoModalidadeVezesSemanaVO();
                        contratoModalidadeVezesSemanaVO.setContratoModalidade(contratoModalidadeVO.getCodigo());
                        contratoModalidadeVezesSemanaVO.setNrVezes(contratoModalidadeVO.getModalidade().getNrVezes());
                        contratoModalidadeVezesSemanaVO.setVezeSemanaEscolhida(true);
                        contratoModalidadeVO.setContratoModalidadeVezesSemanaVO(contratoModalidadeVezesSemanaVO);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void importarAlunoTurma(AlunoTurmaImportacaoJSON alunoTurmaImportacao) throws Exception {
        try {
            JSONArray horarios = new JSONArray();
            alunoTurmaImportacao.getHorarios().forEach(h -> horarios.put(h));

            if (UteisValidacao.emptyNumber(alunoTurmaImportacao.getCodigoDeContrato()) && !UteisValidacao.emptyNumber(alunoTurmaImportacao.getIdExterno())) {
                ResultSet rs = SuperFacadeJDBC.criarConsulta("select con.codigo from contrato con \n" +
                        " where con.empresa = " + alunoTurmaImportacao.getEmpresa() + " \n" +
                        " and con.id_externo = " + alunoTurmaImportacao.getIdExterno(), con);
                if (rs.next()) {
                    alunoTurmaImportacao.setCodigoDeContrato(rs.getLong("codigo"));
                } else {
                    throw new Exception(String.format("Nenhum contrato encontrado para o id externo %d", alunoTurmaImportacao.getIdExterno()));
                }
            } else {
                throw new Exception("Código de contrato não informado");
            }

            alterContratoIncluindoTurma(alunoTurmaImportacao.getCodigoDeContrato().intValue(), alunoTurmaImportacao.getCodigoDaModalidade().intValue(), horarios, true, true);

        } catch (Exception ex) {
            alunoTurmaImportacao.setSucesso(false);
            alunoTurmaImportacao.setMsgRetorno(ex.getMessage());
            throw ex;
        }
    }

    public void adicionarModalidadeContrato(ContratoVO contratoVO, ModalidadeVO modalidadeVO, UsuarioVO usuarioVO) throws Exception {
        ContratoModalidadeVO contratoModalidadeVO = new ContratoModalidadeVO();
        contratoModalidadeVO.setNrVezesSemana(modalidadeVO.getNrVezes());
        contratoModalidadeVO.setValorModalidade(0.0);
        contratoModalidadeVO.setValorFinalModalidade(0.0);
        contratoModalidadeVO.setModalidade(modalidadeVO);
        contratoModalidadeVO.setContrato(contratoVO.getCodigo());
        contratoModalidadeDAO.incluirContratoModalidade(contratoModalidadeVO);

        ContratoModalidadeVezesSemanaVO contratoModalidadeVezesSemanaVO = new ContratoModalidadeVezesSemanaVO();
        contratoModalidadeVezesSemanaVO.setNrVezes(modalidadeVO.getNrVezes());
        contratoModalidadeVO.setContratoModalidadeVezesSemanaVO(contratoModalidadeVezesSemanaVO);
        contratoModalidadeVezesSemanaDAO.incluirContratoModalidadeVezesSemana(contratoModalidadeVO.getCodigo(), contratoModalidadeVezesSemanaVO);

        LogVO obj = new LogVO();
        obj.setChavePrimaria(contratoVO.getCodigo().toString());
        obj.setPessoa(contratoVO.getPessoa().getCodigo());
        obj.setNomeEntidade("CONTRATOMODALIDADE");
        obj.setNomeEntidadeDescricao("Contrato Modalidade (Importação Turma)");
        obj.setOperacao("Adicionar Modalidade (Importação Turma)");
        obj.setResponsavelAlteracao(usuarioVO.getNome());
        obj.setUserOAMD(usuarioVO.getUserOamd());
        obj.setNomeCampo("Contrato Modalidade");
        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        obj.setValorCampoAnterior("");
        obj.setValorCampoAlterado(modalidadeVO.getNome());
        logDAO.incluir(obj);
    }


    public static void main(String[] args) throws Exception {

        Integer codigoEmpresa = 1;
        String chave = "wellupunidbrotas";
        String caminhoPlanilha = "C:\\pacto\\backups\\well\\Modelo inclusão de turma no contrato.xlsx";

        DAO dao = new DAO();
        Connection con = dao.obterConexaoEspecifica(chave);
        Conexao.guardarConexaoForJ2SE(con);
        dao = null;

        Empresa empresaDAO = new Empresa(con);
        EmpresaVO empresaVO = empresaDAO.consultarPorCodigo(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        empresaDAO = null;

        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO usuarioVO = usuarioDAO.consultarPorUsername("pactobr", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        usuarioDAO = null;

        ImportacaoConfigTO configTO = new ImportacaoConfigTO();
        configTO.setChave(chave);
        configTO.setListaEmails(new ArrayList<>());
        configTO.setUsuarioResponsavelImportacao(usuarioVO.getCodigo());
        configTO.setTipoImportacaoEnum(TipoImportacaoEnum.ALUNOS_TURMAS);

        ConfigExcelImportacaoTO configExcelImportacaoTO = new ConfigExcelImportacaoTO();
        configExcelImportacaoTO.setEmpresaVO(empresaVO);

        List<XSSFRow> linhas = LeitorExcel2010.lerLinhas(caminhoPlanilha);
        UteisImportacaoExcel uteisImportacaoExcel = new UteisImportacaoExcel();
        uteisImportacaoExcel.obterListaAlunoTurmaJSONExcel(linhas, configExcelImportacaoTO);

        int total = uteisImportacaoExcel.getListaAlunoTurma().size();
        int atual = 0;
        Integer sucesso = 0;
        Integer falha = 0;

        ProcessoImportacao processoDAO = new ProcessoImportacao(con);
        ProcessoImportacaoLog processoLogDAO = new ProcessoImportacaoLog(con);
        ProcessoImportacaoItem processoItemDAO = new ProcessoImportacaoItem(con);

        ProcessoImportacaoVO processoVO = processoDAO.iniciarProcessoImportacaoVO(configTO, total);
        ImportarAlunosTurma importarAlunosTurmaDao = new ImportarAlunosTurma(con, configTO);

        for (AlunoTurmaImportacaoJSON json : uteisImportacaoExcel.getListaAlunoTurma()) {
            Integer idContrato = UteisValidacao.emptyNumber(json.getCodigoDeContrato()) ? json.getIdExterno() : json.getCodigoDeContrato().intValue();
            try {
                System.out.printf("%d\\%d Importando turma contrato %s \n", ++atual, total, idContrato);

                importarAlunosTurmaDao.importarAlunoTurma(json);

                processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, String.format("%d - Importado com sucesso.", idContrato)));
                ++sucesso;
            } catch (Exception ex) {
                ex.printStackTrace();
                ++falha;
                processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.ERRO, String.format("%d - %s", idContrato, ex.getMessage())));
            } finally {
                processoVO.atualizarJSONStatus(atual, sucesso, falha, total);
                processoDAO.atualizarStatus(processoVO);
                processoItemDAO.incluir(new ProcessoImportacaoItemVO(processoVO, new JSONObject(json).toString()));
            }
        }
        processoDAO.finalizarProcessoImportacaoVO(processoVO, configTO);
    }
}
