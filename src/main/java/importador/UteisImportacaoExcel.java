package importador;

import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import controle.crm.MetaExtraImportadoDTO;
import importador.json.*;
import negocio.comuns.basico.ConfigExcelImportacaoTO;
import negocio.comuns.basico.enumerador.MascaraDataEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class UteisImportacaoExcel {

    private List<ClienteImportacaoJSON> listaClientes;
    private List<ParcelaPagamentoJSON> listaParcelasPagamentos;
    private List<AlunoTurmaImportacaoJSON> listaAlunoTurma;
    private List<TurmaImportacaoJSON> listaTurma;
    private List<ContratoImportacaoJSON> listaContratos;
    private List<FamiliarImportacaoJSON> listaFamiliares;
    private List<TreinoAtividadeJSON> listaTreinoAtividades;
    private List<ProgramaFichaJSON> listaTreinoProgramas;
    private List<AtividadeFichaJSON> listaTreinoAtividadeFicha;

    public void obterListaParcelasPagamentosJSONExcel(List<XSSFRow> linhas,
                                                      ConfigExcelImportacaoTO configuracaoTO) throws Exception {
        MascaraDataEnum mascaraDataEnum = MascaraDataEnum.obterPorCodigo(configuracaoTO.getMascaraDataEnum());
        if (mascaraDataEnum == null) {
            throw new Exception("Não foi possível obter a máscara da data");
        }
        String mascaraData = mascaraDataEnum.getMascara();

        setListaParcelasPagamentos(new ArrayList<>());

        for (XSSFRow linha : linhas) {
            try {
                ParcelaPagamentoJSON parcelaPagamentoJSON = new ParcelaPagamentoJSON();

                int i = 0;

                // Campo 'matricula'
                long matriculaExterna = parseLongSafe(obterStringExcel(linha, i));
                parcelaPagamentoJSON.setMatriculaExterna(matriculaExterna);

                // Campo 'contrato'
                long contratoExterno = parseLongSafe(obterStringExcel(linha, ++i));
                parcelaPagamentoJSON.setIdExternoContrato(contratoExterno);

                // Campo 'id_externo_parcela'
                long idExternoParcela = parseLongSafe(obterStringExcel(linha, ++i));
                parcelaPagamentoJSON.setIdExternoParcela(idExternoParcela);

                // Outros campos
                parcelaPagamentoJSON.setNumeroParcela(parseIntSafe(obterStringExcel(linha, ++i))); // num_parcela
                parcelaPagamentoJSON.setValorParcela(parseDoubleSafe(obterStringExcel(linha, ++i))); // valor_parcela
                parcelaPagamentoJSON.setSituacaoParcela(obterStringExcel(linha, ++i).trim()); // situacao
                parcelaPagamentoJSON.setDtVencimento(obterDataExcel(linha, ++i, mascaraData)); // dt_vencimento
                parcelaPagamentoJSON.setDtPagamento(obterDataExcel(linha, ++i, mascaraData)); // dt_pagamento
                parcelaPagamentoJSON.setFormaPagamento(obterStringExcel(linha, ++i).trim()); // forma_pagamento
                parcelaPagamentoJSON.setCodigoFormaPagamento(parseIntSafe(obterStringExcel(linha, ++i))); // cod_forma_pagamento
                parcelaPagamentoJSON.setQtdParcelasCartao(parseIntSafe(obterStringExcel(linha, ++i))); // num_parcelas_cartao

                // Campo 'nsu'
                long numeroNsu = parseLongSafe(obterStringExcel(linha, ++i));
                parcelaPagamentoJSON.setNumeroNsu(numeroNsu);

                getListaParcelasPagamentos().add(parcelaPagamentoJSON);

            } catch (Exception ex) {
                Uteis.logar(ex, this.getClass());
                ex.printStackTrace();
            }
        }
    }

    public void obterListaTreinoAtividadesJSONExcel(List<XSSFRow> linhas) throws Exception {
        setListaTreinoAtividades(new ArrayList<>());
        String retorno = "Erro ao tentar obter dado";
        for (XSSFRow linha : linhas) {
            try {
                TreinoAtividadeJSON treinoAtividadeJSON = new TreinoAtividadeJSON();

                int i = 0;
                Integer codigoAtividade = (int)Math.round(obterDoubleExcel(linha, i));
                treinoAtividadeJSON.setCodigo(codigoAtividade);

                String nomeAtividade = obterStringExcel(linha, ++i);
                treinoAtividadeJSON.setNome(nomeAtividade.replace("'", ""));

                String nomeAparelho = obterStringExcel(linha, ++i);
                treinoAtividadeJSON.setNomeAparelho(nomeAparelho);

                Integer grupoMuscular = (int)Math.round(obterDoubleExcel(linha, ++i));
                treinoAtividadeJSON.setGrupoMuscular(grupoMuscular);

                String tipoAtividade = obterStringExcel(linha, ++i);
                treinoAtividadeJSON.setTipoAtividade(tipoAtividade);

                getListaTreinoAtividades().add(treinoAtividadeJSON);

            } catch (Exception ex) {
                if(!isLinhaVazia(linha)) {
                    retorno += "\nna linha: " + linha.getRowNum()+",";
                }
                Uteis.logar(ex, this.getClass());
                ex.printStackTrace();
            }
        }
        if (retorno.contains("linha")) {
            throw new Exception(retorno);
        }
    }

    public void obterListaTreinoProgramasJSONExcel(List<XSSFRow> linhas) throws Exception {
        setListaTreinoProgramas(new ArrayList<>());

        String retorno = "Erro ao tentar obter dado";
        for (XSSFRow linha : linhas) {
            try {
                ProgramaFichaJSON programaFichaJSON = new ProgramaFichaJSON();

                int i = 0;
                Integer codigoFicha = (int)Math.round(obterDoubleExcel(linha, i));
                programaFichaJSON.setCodigoFicha(codigoFicha);

                ++i;
                programaFichaJSON.setIdCliente(null);

                String nomeFicha = obterStringExcel(linha, ++i);
                programaFichaJSON.setNomeFicha(nomeFicha.replace("'", ""));

                ++i;
                programaFichaJSON.setDataLancamento(null);

                ++i;
                programaFichaJSON.setDataInicio(null);

                ++i;
                programaFichaJSON.setDataFim(null);

                Integer vezesSemana = (int)Math.round(obterDoubleExcel(linha, ++i));
                programaFichaJSON.setDiasPorSemana(vezesSemana);

                Integer treinosPrevistos = (int)Math.round(obterDoubleExcel(linha, ++i));
                programaFichaJSON.setTotalAulasPrevistas(treinosPrevistos);

                String nomePrograma = obterStringExcel(linha, ++i);
                programaFichaJSON.setNomePrograma(nomePrograma.replace("'", ""));

                getListaTreinoProgramas().add(programaFichaJSON);

            } catch (Exception ex) {
                if(!isLinhaVazia(linha)) {
                    retorno += "\nna linha: " + linha.getRowNum()+",";
                }
                Uteis.logar(ex, this.getClass());
                ex.printStackTrace();
            }
        }
        if (retorno.contains("linha")) {
            throw new Exception(retorno);
        }
    }

    public void obterListaTreinoAtividadeFichaJSONExcel(List<XSSFRow> linhas) throws Exception {
        setListaTreinoAtividadeFicha(new ArrayList<>());
        String retorno = "Erro ao tentar obter dado";
        for (XSSFRow linha : linhas) {
            try {
                AtividadeFichaJSON atividadeFichaJSON = new AtividadeFichaJSON();

                int i = 0;
                Integer codigoFicha = (int)Math.round(obterDoubleExcel(linha, i));
                atividadeFichaJSON.setCodigoFicha(codigoFicha);

                Integer codigoAtividade = (int)Math.round(obterDoubleExcel(linha, ++i));
                atividadeFichaJSON.setCodigoAtividade(codigoAtividade);

                String nomeAtividade = obterStringExcel(linha, ++i);
                atividadeFichaJSON.setNomeAtividade(nomeAtividade.replace("'", ""));

                Integer sequenciaOrdem = (int)Math.round(obterDoubleExcel(linha, ++i));
                atividadeFichaJSON.setSequenciaOrdem(sequenciaOrdem);

                try{
                    Integer repeticoes = (int)Math.round(obterDoubleExcel(linha, ++i));
                    atividadeFichaJSON.setRepeticoes(String.valueOf(repeticoes));
                }catch (Exception ignore){
                    String repeticoes = obterStringExcel(linha, i);
                    atividadeFichaJSON.setRepeticoes(repeticoes.replace("'", ""));
                }

                ++i;
                atividadeFichaJSON.setCarga(null);

                ++i;
                atividadeFichaJSON.setCadencia(null);

                ++i;
                atividadeFichaJSON.setDuracao(null);

                ++i;
                atividadeFichaJSON.setVelocidade(null);

                ++i;
                atividadeFichaJSON.setDistancia(null);

                String descancoIntervalo = obterStringExcel(linha, ++i);
                if(!UteisValidacao.emptyString(descancoIntervalo) && !descancoIntervalo.equals("0.0")){
                    descancoIntervalo = descancoIntervalo.replace("\"", "");
                    descancoIntervalo = descancoIntervalo.replace("MIN", "");
                    descancoIntervalo = descancoIntervalo.replace("'", "");
                    descancoIntervalo = descancoIntervalo.replace(" ", "");
                    atividadeFichaJSON.setDescancoIntervalo(Integer.parseInt(descancoIntervalo));
                }else{
                    atividadeFichaJSON.setDescancoIntervalo(null);
                }

                Integer qtdSeries = !UteisValidacao.emptyString(obterStringExcel(linha, ++i)) ? (int)Math.round(obterDoubleExcel(linha, i)) : 0;
                atividadeFichaJSON.setQtdSeries(qtdSeries);

                String obsComplemento = obterStringExcel(linha, ++i);
                atividadeFichaJSON.setObsComplemento(obsComplemento.replace("'", ""));

                getListaTreinoAtividadeFicha().add(atividadeFichaJSON);

            } catch (Exception ex) {
                if(!isLinhaVazia(linha)) {
                    retorno += "\nna linha: " + linha.getRowNum()+",";
                }
                Uteis.logar(ex, this.getClass());
                ex.printStackTrace();
            }
        }
        if (retorno.contains("linha")) {
            throw new Exception(retorno);
        }
    }

    private boolean isLinhaVazia(XSSFRow row) {
        if (row == null) return true;

        for (Cell cell : row) {
            if (cell != null && cell.getCellType() != 3) {
                return false;
            }
        }
        return true;
    }


    // metodos auxiliares
    private long parseLongSafe(String value) {
        try {
            return value != null && !value.trim().isEmpty() ? Long.parseLong(value.trim()) : 0L;
        } catch (NumberFormatException e) {
            return 0L;
        }
    }

    private int parseIntSafe(String value) {
        try {
            return value != null && !value.trim().isEmpty() ? Integer.parseInt(value.trim()) : 0;
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    private double parseDoubleSafe(String value) {
        try {
            return value != null && !value.trim().isEmpty() ? Double.parseDouble(value.trim()) : 0.0;
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }


    public void obterListaClienteJSONExcel(List<XSSFRow> linhas,
                                           ConfigExcelImportacaoTO configuracaoTO) throws Exception {

        //Dados de planilha modelo em excel que se encontra dentro da pasta modelos no projeto.
        MascaraDataEnum mascaraDataEnum = MascaraDataEnum.obterPorCodigo(configuracaoTO.getMascaraDataEnum());
        if (mascaraDataEnum == null) {
            throw new Exception("Não foi possível obter a máscara da data");
        }
        String mascaraData = mascaraDataEnum.getMascara();

        setListaClientes(new ArrayList<>());
        setListaContratos(new ArrayList<>());
        for (XSSFRow linha : linhas) {
            try {
                ClienteImportacaoJSON clienteJSON = new ClienteImportacaoJSON();

                int i = 0;

                clienteJSON.setEmpresa(obterNumeroDaStringExcel(linha, i++));
                if (UteisValidacao.emptyNumber(clienteJSON.getEmpresa())) {
                    clienteJSON.setEmpresa(configuracaoTO.getEmpresaVO().getCodigo());
                }
                long idExterno;
                try {
                    String stringID = obterStringExcel(linha, i); //idExterno
                    if (UteisValidacao.emptyString(stringID)) {
                        stringID = obterStringExcel(linha, i);
                    }
                    idExterno = Long.parseLong(stringID);
                } catch (Exception ex) {
                    idExterno = 0L;
                }

                if (UteisValidacao.emptyNumber(idExterno)) {
                    continue;
                }

                long matriculaExterna;
                try {
                    Integer coluna = ++i;
                    String stringID = obterStringExcel(linha, coluna); //matriculaExterna
                    matriculaExterna = Long.parseLong(stringID.trim());
                } catch (Exception ex) {
                    matriculaExterna = 0L;
                }

                String nome = obterStringExcel(linha, ++i); //nome

                if (UteisValidacao.emptyString(nome)) {
                    continue;
                }

                clienteJSON.setIdExterno(idExterno);
                clienteJSON.setMatriculaExterna(matriculaExterna);
                clienteJSON.setNome(nome);
                clienteJSON.setUsuario(configuracaoTO.getUsuarioResponsavelImportacao().getCodigo());
                clienteJSON.setEmpresa(configuracaoTO.getEmpresaVO().getCodigo());
                clienteJSON.setCpf(obterStringExcelCorrigindoNotacoesCientificas(linha, ++i)); //cpf
                clienteJSON.setRg(obterStringExcelCorrigindoNotacoesCientificas(linha, ++i)); //rg
                clienteJSON.setRgOrgao(obterStringExcel(linha, ++i)); //orgao rg
                clienteJSON.setDataNascimento(obterDataExcel(linha, ++i, mascaraData)); //data nascimento
                clienteJSON.setDataCadastro(obterDataExcel(linha, ++i, mascaraData)); //data cadastro
                clienteJSON.setSexo(obterSexo(obterStringExcel(linha, ++i))); // sexo
                clienteJSON.setEstadoCivil(obterEstadoCivil(obterStringExcel(linha, ++i))); //estado civil
                String profissao = obterStringExcel(linha, ++i);
                clienteJSON.setProfissao(!UteisValidacao.emptyString(profissao) && profissao.length() > 45 ? profissao.substring(0, 45) : profissao); //profissao
                clienteJSON.setCategoria(obterStringExcel(linha, ++i)); //categoria
                clienteJSON.setGrauInstrucao(obterStringExcel(linha, ++i)); //grau instrução
                clienteJSON.setNacionalidade(obterStringExcel(linha, ++i)); //nacionalidade
                clienteJSON.setNaturalidade(obterStringExcel(linha, ++i)); //naturalidade
                clienteJSON.setNomePai(obterStringExcel(linha, ++i)); // nome pai
                clienteJSON.setCpfPai(obterStringExcel(linha, ++i)); // cpf pai
                String nomeMae = obterStringExcel(linha, ++i);
                clienteJSON.setNomeMae(!UteisValidacao.emptyString(nomeMae) && nomeMae.length() > 50 ? nomeMae.substring(0, 50) : nomeMae); //nome mae
                clienteJSON.setCpfMae(obterStringExcel(linha, ++i)); // cpf mae
                clienteJSON.setNecessidadeEspecial(obterStringExcel(linha, ++i)); // deficiencia (necessidades especiais)
                clienteJSON.setEmpresaFornecedor(obterStringExcel(linha, ++i)); //codigo id empresa fornecedor

                //endereço
                String logradouro = obterStringExcel(linha, ++i); //logradouro
                String numero = obterStringExcel(linha, ++i); //numero
                String complemento = obterStringExcel(linha, ++i); //complemento
                String bairro = obterStringExcel(linha, ++i); //bairro
                String cep = obterStringExcel(linha, ++i); //cep
                adicionarEnderecoExcel(clienteJSON.getEnderecos(), cep, bairro, logradouro, numero, complemento);

                clienteJSON.setCidade(obterStringExcel(linha, ++i)); //cidade
                clienteJSON.setUf(obterStringExcel(linha, ++i)); // uf

                adicionarEmailExcel(clienteJSON.getEmails(), obterStringExcel(linha, ++i)); //email
                adicionarTelefoneExcel(clienteJSON.getTelefones(), TipoTelefoneEnum.RESIDENCIAL, obterStringExcel(linha, ++i)); //telefone residencial 1
                adicionarTelefoneExcel(clienteJSON.getTelefones(), TipoTelefoneEnum.RESIDENCIAL, obterStringExcel(linha, ++i)); //telefone residencial 2
                adicionarTelefoneExcel(clienteJSON.getTelefones(), TipoTelefoneEnum.CELULAR, obterStringExcel(linha, ++i)); //telefone celular 1
                adicionarTelefoneExcel(clienteJSON.getTelefones(), TipoTelefoneEnum.CELULAR, obterStringExcel(linha, ++i)); //telefone celular 2

                clienteJSON.setObservacao(obterStringExcel(linha, ++i)); //observação

                //contrato
                Date dataLancamentoContrato = obterDataExcel(linha, ++i, mascaraData);
                Date dataInicioContrato = obterDataExcel(linha, ++i, mascaraData);
                Date dataFimContrato = obterDataExcel(linha, ++i, mascaraData);
                Double valorContrato = obterDoubleExcel(linha, ++i);
                String descricaoPlano = obterStringExcel(linha, ++i);
                obterContratoExcel(clienteJSON, configuracaoTO, dataLancamentoContrato, dataInicioContrato, dataFimContrato, valorContrato, descricaoPlano);

                //consultor
                VinculoImportacaoJSON vinculoJSON = new VinculoImportacaoJSON();
                vinculoJSON.setColaborador(configuracaoTO.getConsultor());
                vinculoJSON.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());
                clienteJSON.getVinculos().add(vinculoJSON);

                //professor TreinoWeb
                if (!UteisValidacao.emptyNumber(configuracaoTO.getProfessorTreinoWeb())) {
                    vinculoJSON = new VinculoImportacaoJSON();
                    vinculoJSON.setColaborador(configuracaoTO.getProfessorTreinoWeb());
                    vinculoJSON.setTipoVinculo(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla());
                    clienteJSON.getVinculos().add(vinculoJSON);
                }

                getListaClientes().add(clienteJSON);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    public void obterListaVinculosFamiliaresJSONExcel(List<XSSFRow> linhas,
                                                      ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception {
        setListaFamiliares(new ArrayList<>());

        for (XSSFRow linha : linhas) {
            FamiliarImportacaoJSON familiarImportacaoJSON = new FamiliarImportacaoJSON();
            int i = 0;

            String matriculaExternaAluno = obterStringExcel(linha, i);
            String matriculaExternaFamiliar = obterStringExcel(linha, ++i);
            String parentesco = obterStringExcel(linha, ++i);

            if(UteisValidacao.emptyString(matriculaExternaAluno) || !matriculaExternaAluno.matches("\\d+")) {
                throw new Exception("Informe um valor válido para o campo MATRICULA_EXTERNA_ALUNO");
            }
            if(UteisValidacao.emptyString(matriculaExternaFamiliar)  || !matriculaExternaFamiliar.matches("\\d+")) {
                throw new Exception("Informe um valor válido para o campo MATRICULA_EXTERNA_FAMILIAR");
            }
            if(UteisValidacao.emptyString(parentesco)) {
                throw new Exception("Informe o parentesco (código ou descrição)");
            }

            familiarImportacaoJSON.setMatriculaExternaAluno(matriculaExternaAluno);
            familiarImportacaoJSON.setMatriculaExternaFamiliar(matriculaExternaFamiliar);
            familiarImportacaoJSON.setParentesco(parentesco);

            getListaFamiliares().add(familiarImportacaoJSON);

        }


    }

    public void obterListaContratoJSONExcel(List<XSSFRow> linhas,
                                           ConfigExcelImportacaoTO configuracaoTO) throws Exception {

        //Dados de planilha modelo em excel que se encontra dentro da pasta modelos no projeto.
        MascaraDataEnum mascaraDataEnum = MascaraDataEnum.obterPorCodigo(configuracaoTO.getMascaraDataEnum());
        if (mascaraDataEnum == null) {
            throw new Exception("Não foi possível obter a máscara da data");
        }
        String mascaraData = mascaraDataEnum.getMascara();

        setListaContratos(new ArrayList<>());
        for (XSSFRow linha : linhas) {
            try {
                ContratoImportacaoJSON contratoJSON = new ContratoImportacaoJSON();

                int i = 0;

                contratoJSON.setEmpresa(obterIntegerConvertendoStringExcel(linha, i++)); //codigo_empresa

                int idExterno;
                try {
                    String stringID = obterStringExcel(linha, i); //idExterno
                    if (UteisValidacao.emptyString(stringID)) {
                        stringID = obterStringExcel(linha, i);
                    }
                    idExterno = Integer.parseInt(stringID);
                } catch (Exception ex) {
                    idExterno = 0;
                }

                if (UteisValidacao.emptyNumber(idExterno)) {
                    continue;
                }

                //////// lendo arquivo contrato excel
                contratoJSON.setIdExterno(idExterno);

                contratoJSON.setIdExternoCliente(obterIntegerConvertendoStringExcel(linha, ++i).longValue()); //matricula_externa

                Integer idExternoTurma = obterIntegerConvertendoStringExcel(linha, ++i);
                if(idExternoTurma != null) {
                    contratoJSON.setIdExternoTurma(idExternoTurma.longValue()); //id_externo_turma
                } else {
                    contratoJSON.setIdExternoTurma(null);
                }


                contratoJSON.setHorarioTurma(obterStringExcel(linha, ++i)); //horario_turma

                contratoJSON.setDiasTurma(obterListaDiasSemana(obterStringExcel(linha, ++i))); //dias_turma

                contratoJSON.setDataLancamento(obterDataExcel(linha, ++i, mascaraData)); //dt_lancamento_contrato

                contratoJSON.setDataInicio(obterDataExcel(linha, ++i, mascaraData)); //dt_inicio_contrato

                contratoJSON.setDataFim(obterDataExcel(linha, ++i, mascaraData)); //dt_final_contrato

                Integer modalidade = getCampoExcelOuConfigPadrao("MODALIDADE", obterIntegerConvertendoStringExcel(linha, ++i), configuracaoTO);
                contratoJSON.setModalidade(modalidade); //codigo_modalidade

                Integer plano = getCampoExcelOuConfigPadrao("PLANO", obterIntegerConvertendoStringExcel(linha, ++i), configuracaoTO);
                contratoJSON.setPlano(plano); //codigo_plano

                contratoJSON.setNrParcelas(obterIntegerConvertendoStringExcel(linha, ++i)); //qtd_parcelas

                contratoJSON.setValorTotal(obterDoubleExcel(linha, ++i));

                contratoJSON.setImportacaoParcelasSituacao(configuracaoTO.getImportacaoParcelasSituacaoEnum().getCodigo()); //config padrao selecionada para situacao parcelas

                //obterContratoExcel(clienteJSON, configuracaoTO, dataLancamentoContrato, dataInicioContrato, dataFimContrato, valorContrato, descricaoPlano);

                if(UteisValidacao.emptyNumber(contratoJSON.getIdExternoTurma())) {
                    contratoJSON.setHorario(configuracaoTO.getHorario());
                }

                getListaContratos().add(contratoJSON);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    public Integer getCampoExcelOuConfigPadrao(String descricaoCampo, Integer valorCampoExcel, ConfigExcelImportacaoTO configExcelImportacaoTO) {
        if (UteisValidacao.emptyNumber(valorCampoExcel)) {
            try {
                switch (descricaoCampo) {
                    case "MODALIDADE":
                        return configExcelImportacaoTO.getModalidade();
                    case "PLANO":
                        return configExcelImportacaoTO.getPlano();
                    default:
                        throw new IllegalArgumentException("Campo Excel inválido: " + descricaoCampo);
                }
            } catch (IllegalArgumentException e) {
                Uteis.logar(e, this.getClass());
                return null;
            }
        }

        return valorCampoExcel;
    }


    public void obterListaAlunoTurmaJSONExcel(List<XSSFRow> linhas, ConfigExcelImportacaoTO configExcelImportacaoTO) throws Exception {
        for (XSSFRow linha : linhas) {
            int contador = -1;
            try {
                AlunoTurmaImportacaoJSON alunoTurmaImportacaoJSON = new AlunoTurmaImportacaoJSON();

                alunoTurmaImportacaoJSON.setIdExterno(Integer.parseInt(obterStringExcel(linha, ++contador)));
                alunoTurmaImportacaoJSON.setCodigoDeContrato(Long.parseLong(obterStringExcel(linha, ++contador)));
                alunoTurmaImportacaoJSON.setCodigoDaModalidade(Long.parseLong(obterStringExcel(linha, ++contador)));
                String horarios = obterStringExcel(linha, ++contador);
                for(String h: horarios.split(",")){
                    alunoTurmaImportacaoJSON.getHorarios().add(Integer.parseInt(h.trim()));

                }
                alunoTurmaImportacaoJSON.setEmpresa(configExcelImportacaoTO.getEmpresaVO().getCodigo());
                alunoTurmaImportacaoJSON.validarDados();
                getListaAlunoTurma().add(alunoTurmaImportacaoJSON);
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new Exception("Erro na linha: " + linha.getRowNum() + " - " + ex.getMessage());
            }
        }
    }

    public void obterListaTurmaJSONExcel(List<XSSFRow> linhas, Integer codEmpresa) throws Exception {
        for (XSSFRow linha : linhas) {
            int count = 0;
            try {
                TurmaImportacaoJSON turmaImportacaoJSON = new TurmaImportacaoJSON();
                turmaImportacaoJSON.setCodigoEmpresa(obterNumeroDaStringExcel(linha, count++));
                if (UteisValidacao.emptyNumber(turmaImportacaoJSON.getCodigoEmpresa())) {
                    turmaImportacaoJSON.setCodigoEmpresa(codEmpresa);
                }
                turmaImportacaoJSON.setIdExterno(obterNumeroDaStringExcel(linha, count++));
                turmaImportacaoJSON.setTurma(obterStringExcel(linha, count++));
                turmaImportacaoJSON.setModalidade(obterStringExcel(linha, count++));
                turmaImportacaoJSON.setVigenciaDe(obterDataExcel(linha, count++, MascaraDataEnum.DATA_DDMMYYYY_1.getMascara()));
                turmaImportacaoJSON.setVigenciaAte(obterDataExcel(linha, count++, MascaraDataEnum.DATA_DDMMYYYY_1.getMascara()));
                turmaImportacaoJSON.setIdadeMinima(obterNumeroDaStringExcel(linha, count++).intValue());
                turmaImportacaoJSON.setIdadeMinimaMeses(obterNumeroDaStringExcel(linha, count++).intValue());
                turmaImportacaoJSON.setIdadeMaxima(obterNumeroDaStringExcel(linha, count++).intValue());
                turmaImportacaoJSON.setIdadeMaximaMeses(obterNumeroDaStringExcel(linha, count++).intValue());
                turmaImportacaoJSON.setDiasSemana(obterListaDiasSemana(obterStringExcel(linha, count++)));

                turmaImportacaoJSON.setHoraInicio(obterStringExcel(linha, count++));
                turmaImportacaoJSON.setHoraFim(obterStringExcel(linha, count++));
                turmaImportacaoJSON.setCapacidade(obterNumeroDaStringExcel(linha, count++).intValue());
                turmaImportacaoJSON.setCodigoProfessor(obterNumeroDaStringExcel(linha, count++).intValue());
                turmaImportacaoJSON.setAmbiente(obterStringExcel(linha, count++));
                turmaImportacaoJSON.setNivel(obterStringExcel(linha, count++));
                turmaImportacaoJSON.setTolerancia(obterNumeroDaStringExcel(linha, count++).intValue());

                turmaImportacaoJSON.validarDados();

                getListaTurma().add(turmaImportacaoJSON);
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new Exception("Erro na linha: " + linha.getRowNum() + " - " + ex.getMessage());
            }
        }

    }

    private List<DiaSemana> obterListaDiasSemana(String diasSemana) {
        List<DiaSemana> dias = new ArrayList<>();
        if (UteisValidacao.emptyString(diasSemana)) {
            return dias;
        }
        String separator = diasSemana.contains(",") ? "," : " ";
        for (String dia: diasSemana.split(separator)) {
            dia = dia.replace(".","").toUpperCase().trim();
            if (dia.isEmpty()) {
                continue;
            }
            if (dia.equals("DOM") || dia.equals("DM")) {
                dias.add(DiaSemana.DOMINGO);
            } else if (dia.equals("SEG") || dia.equals("SG")) {
                dias.add(DiaSemana.SEGUNDA_FEIRA);
            } else if (dia.equals("TER") || dia.equals("TR")) {
                dias.add(DiaSemana.TERCA_FEIRA);
            } else if (dia.equals("QUA") || dia.equals("QA")) {
                dias.add(DiaSemana.QUARTA_FEIRA);
            } else if (dia.equals("QUI") || dia.equals("QI")) {
                dias.add(DiaSemana.QUINTA_FEIRA);
            } else if (dia.equals("SEX") || dia.equals("SX")) {
                dias.add(DiaSemana.SEXTA_FEIRA);
            } else if (dia.equals("SAB") || dia.equals("SÁB") || dia.equals("SB")) {
                dias.add(DiaSemana.SABADO);
            }
        }
        return dias;
    }

    private Integer obterNumeroDaStringExcel(XSSFRow linha, int index) {
        String str = obterStringExcel(linha, index);
        return str.matches("\\d+") ? Integer.parseInt(str.trim()) : 0;
    }

    public List<ColaboradorImportacaoJSON> obterListaColaboradorExcel(List<XSSFRow> linhas,
                                                                      ConfigExcelImportacaoTO configuracaoTO) throws Exception {

        //Dados de planilha modelo em excel que se encontra dentro da pasta modelos no projeto.
        MascaraDataEnum mascaraDataEnum = MascaraDataEnum.obterPorCodigo(configuracaoTO.getMascaraDataEnum());
        if (mascaraDataEnum == null) {
            throw new Exception("Não foi possível obter a máscara da data");
        }
        String mascaraData = mascaraDataEnum.getMascara();

        List<ColaboradorImportacaoJSON> lista = new ArrayList<>();
        for (XSSFRow linha : linhas) {
            try {
                ColaboradorImportacaoJSON json = new ColaboradorImportacaoJSON();

                int i = 0;

                Integer idExterno = 0;
                try {
                    String stringID = obterStringExcel(linha, i); //idExterno
                    idExterno = Integer.parseInt(stringID.trim());
                } catch (Exception ex) {
                    idExterno = 0;
                }

                if (UteisValidacao.emptyNumber(idExterno)) {
                    continue;
                }

                String nome = obterStringExcel(linha, ++i); //nome

                if (UteisValidacao.emptyString(nome)) {
                    continue;
                }

                json.setIdExterno(idExterno);
                json.setNome(nome);
                json.setEmpresa(configuracaoTO.getEmpresaVO().getCodigo());
                json.setCpf(obterStringExcel(linha, ++i)); //cpf
                json.setRg(obterStringExcel(linha, ++i)); //rg
                json.setRgOrgao(obterStringExcel(linha, ++i)); //orgao rg
                json.setCref(obterStringExcel(linha, ++i)); //cref
                adicionarTipoColaborador(json.getListaTipoColaborador(), obterStringExcel(linha, ++i)); //tipo colaborador
                json.setAtivo(obterBooleanExcel(linha, ++i, true)); //ativo
                json.setDataNascimento(obterDataExcel(linha, ++i, mascaraData)); //data nascimento
                json.setDataCadastro(obterDataExcel(linha, ++i, mascaraData)); //data cadastro
                json.setSexo(obterSexo(obterStringExcel(linha, ++i))); // sexo
                json.setEstadoCivil(obterEstadoCivil(obterStringExcel(linha, ++i))); //estado civil
                json.setProfissao(obterStringExcel(linha, ++i)); //profissao
                json.setGrauInstrucao(obterStringExcel(linha, ++i)); //grau instrucao
                json.setNacionalidade(obterStringExcel(linha, ++i)); //nacionalidade
                json.setNaturalidade(obterStringExcel(linha, ++i)); //naturalidade

                //endereço
                String logradouro = obterStringExcel(linha, ++i); //logradouro
                String numero = obterStringExcel(linha, ++i); //numero
                String complemento = obterStringExcel(linha, ++i); //complemento
                String bairro = obterStringExcel(linha, ++i); //bairro
                String cep = obterStringExcel(linha, ++i); //cep
                adicionarEnderecoExcel(json.getEnderecos(), cep, bairro, logradouro, numero, complemento);

                json.setCidade(obterStringExcel(linha, ++i)); //cidade
                json.setUf(obterStringExcel(linha, ++i)); // uf

                adicionarEmailExcel(json.getEmails(), obterStringExcel(linha, ++i)); //email
                adicionarTelefoneExcel(json.getTelefones(), TipoTelefoneEnum.RESIDENCIAL, obterStringExcel(linha, ++i)); //telefone residencial 1
                adicionarTelefoneExcel(json.getTelefones(), TipoTelefoneEnum.RESIDENCIAL, obterStringExcel(linha, ++i)); //telefone residencial 2
                adicionarTelefoneExcel(json.getTelefones(), TipoTelefoneEnum.CELULAR, obterStringExcel(linha, ++i)); //telefone celular 1
                adicionarTelefoneExcel(json.getTelefones(), TipoTelefoneEnum.CELULAR, obterStringExcel(linha, ++i)); //telefone celular 2

                if (configuracaoTO.isCriarUsuario()) {
                    if (UteisValidacao.emptyList(json.getEmails())) {
                        continue;
                    }
                    if (json.getDataNascimento() == null) {
                        continue;
                    }
                    if (UteisValidacao.emptyList(json.getListaTipoColaborador())) {
                        continue;
                    }
                }

                lista.add(json);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return lista;
    }

    public List<FornecedorImportacaoJSON> obterListaFornecedorExcel(List<XSSFRow> linhas,
                                                                    ConfigExcelImportacaoTO configuracaoTO) throws Exception {
        MascaraDataEnum mascaraDataEnum = MascaraDataEnum.obterPorCodigo(configuracaoTO.getMascaraDataEnum());
        List<FornecedorImportacaoJSON> lista = new ArrayList<>();
        for (XSSFRow linha : linhas) {
            FornecedorImportacaoJSON json = new FornecedorImportacaoJSON();
            json.setEmpresa(configuracaoTO.getEmpresaVO().getCodigo());

            int i = -1;

            Integer idExterno = obterNumeroDaStringExcel(linha, ++i);

            if (UteisValidacao.emptyNumber(idExterno)) {
                continue;
            }

            String nome = obterStringExcel(linha, ++i); //nome
            if (UteisValidacao.emptyString(nome)) {
                continue;
            }

            json.setIdExterno(idExterno);
            json.setNome(nome);
            json.setCpf_cnpj(obterStringExcel(linha, ++i)); //cpf cnpj
            json.setNomeContato(obterStringExcel(linha, ++i)); //nome contato
            json.setIncricaoEstadual(obterStringExcel(linha, ++i)); //estadual
            json.setIncricaoMunicipal(obterStringExcel(linha, ++i)); //municipal

            //endereço
            String logradouro = obterStringExcel(linha, ++i); //logradouro
            String numero = obterStringExcel(linha, ++i); //numero
            String complemento = obterStringExcel(linha, ++i); //complemento
            String bairro = obterStringExcel(linha, ++i); //bairro
            String cep = obterStringExcel(linha, ++i); //cep
            adicionarEnderecoExcel(json.getEnderecos(), cep, bairro, logradouro, numero, complemento);

            json.setCidade(obterStringExcel(linha, ++i)); //cidade
            json.setUf(obterStringExcel(linha, ++i)); // uf

            adicionarEmailExcel(json.getEmails(), obterStringExcel(linha, ++i)); //email
            adicionarTelefoneExcel(json.getTelefones(), TipoTelefoneEnum.COMERCIAL, obterStringExcel(linha, ++i)); //telefone 1
            adicionarTelefoneExcel(json.getTelefones(), TipoTelefoneEnum.COMERCIAL, obterStringExcel(linha, ++i)); //telefone 2
            adicionarTelefoneExcel(json.getTelefones(), TipoTelefoneEnum.CELULAR, obterStringExcel(linha, ++i)); //telefone celular 1
            adicionarTelefoneExcel(json.getTelefones(), TipoTelefoneEnum.CELULAR, obterStringExcel(linha, ++i)); //telefone celular 2

            json.setObservacao(obterStringExcel(linha, ++i)); //observacao

            //em anapolise
            json.setRazaoSocial(obterStringExcel(linha, ++i)); //observacao
            json.setDataDoCadastro(obterDataExcel(linha, ++i, mascaraDataEnum.toString())); //data cadastro
            json.setDataDeValidade(obterDataExcel(linha, ++i, mascaraDataEnum.toString())); //data de validade
            json.setCnae(obterStringExcel(linha, ++i)); //CNAE
            json.setCodFpas(obterStringExcel(linha, ++i)); //codFpas
            json.setGrauDeRiscoNr4(obterNumeroDaStringExcel(linha, ++i)); //NR4
            json.setGrauDeRiscoInss(obterNumeroDaStringExcel(linha, ++i)); //Inss
            json.setSindicato(obterStringExcel(linha, ++i)); //sindicato
            json.setNumeroTotalDeFuncionarios(obterNumeroDaStringExcel(linha, ++i)); //total de funcionarios
            String porteEmpresa = obterStringExcel(linha, ++i); //porte da empresa
            if (!UteisValidacao.emptyString(porteEmpresa)) {
                if (porteEmpresa.matches("\\d+")) {
                    json.setPorteDaEmpresa(Integer.parseInt(porteEmpresa));
                } else {
                    throw new Exception("Porte da empresa deve conter somente numero: 1 - Pequena, 2 - Média, 3 - Grande" + porteEmpresa);
                }
            }
            json.setPorteDaEmpresa(obterNumeroDaStringExcel(linha, ++i)); //porte

            lista.add(json);
        }
        return lista;
    }

    public List<ContaImportacaoJSON> obterListaContaExcel(List<XSSFRow> linhas,
                                                                ConfigExcelImportacaoTO configuracaoTO) throws Exception {

        //Dados de planilha modelo em excel que se encontra dentro da pasta modelos no projeto.
        MascaraDataEnum mascaraDataEnum = MascaraDataEnum.obterPorCodigo(configuracaoTO.getMascaraDataEnum());
        if (mascaraDataEnum == null) {
            throw new Exception("Não foi possível obter a máscara da data");
        }
        String mascaraData = mascaraDataEnum.getMascara();

        List<ContaImportacaoJSON> lista = new ArrayList<>();
        for (XSSFRow linha : linhas) {
            try {
                ContaImportacaoJSON json = new ContaImportacaoJSON();

                int i = 0;

                Integer idExterno = 0;
                try {
                    String stringID = obterStringExcel(linha, i); //idExterno
                    idExterno = Integer.parseInt(stringID.trim());
                } catch (Exception ex) {
                    idExterno = 0;
                }

                if (UteisValidacao.emptyNumber(idExterno)) {
                    continue;
                }

                json.setEmpresa(configuracaoTO.getEmpresaVO().getCodigo());
                json.setIdExterno(idExterno);
                json.setDescricao(obterStringExcel(linha, ++i));
                json.setPessoa(obterStringExcel(linha, ++i)); //nome pessoa
                json.setCpf_cnpj(obterStringExcel(linha, ++i)); //cpf cnpj
                json.setValor(obterDoubleExcel(linha, ++i)); //valou
                json.setTipoConta(obterTipoConta(obterStringExcel(linha, ++i))); //tipo conta
                json.setDataLancamento(obterDataExcel(linha, ++i, mascaraData));
                json.setDataVencimento(obterDataExcel(linha, ++i, mascaraData));
                json.setDataCompetencia(obterDataExcel(linha, ++i, mascaraData));
                json.setDataQuitacao(obterDataExcel(linha, ++i, mascaraData));
                json.setCentroCusto(obterStringExcel(linha, ++i));
                json.setPlanoConta(obterStringExcel(linha, ++i));
                json.setObservacao(obterStringExcel(linha, ++i));

                //validar se o json esta com os dados mínimos para importar
                if (UteisValidacao.emptyString(json.getDescricao().trim()) ||
                        UteisValidacao.emptyString(json.getPessoa().trim()) ||
                        UteisValidacao.emptyNumber(json.getValor()) ||
                        UteisValidacao.emptyString(json.getTipoConta().trim()) ||
                        json.getDataLancamento() == null) {
                    continue;
                }

                lista.add(json);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return lista;
    }

    private String obterTipoConta(String valorExcel) {
        try {

            if (UteisValidacao.emptyString(valorExcel.trim())) {
                return "";
            }

            valorExcel = Uteis.retirarAcentuacaoRegex(valorExcel.toUpperCase());

            if (valorExcel.contains("ENTRADA") || valorExcel.contains("RECEBIMENTO") ||
                    valorExcel.equalsIgnoreCase("E")) {
                return "E";
            } else if (valorExcel.contains("SAIDA") || valorExcel.contains("SAÍDA") ||
                    valorExcel.contains("PAGAMENTO") || valorExcel.equalsIgnoreCase("S")) {
                return "s";
            } else {
                return "";
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    private String obterEstadoCivil(String valorExcel) {
        try {

            if (UteisValidacao.emptyString(valorExcel.trim())) {
                return "";
            }

            valorExcel = Uteis.retirarAcentuacaoRegex(valorExcel.toUpperCase());

            if (valorExcel.contains("SOLTEI") || valorExcel.equalsIgnoreCase("S")) {
                return "S";
            } else if (valorExcel.contains("CASADO") || valorExcel.contains("CASADA") ||
                    valorExcel.equalsIgnoreCase("C")) {
                return "C";
            } else if (valorExcel.contains("AMASIAD") || valorExcel.equalsIgnoreCase("A")) {
                return "A";
            } else if (valorExcel.contains("VIUVA") || valorExcel.contains("VIUVO") ||
                    valorExcel.equalsIgnoreCase("V")) {
                return "V";
            } else if (valorExcel.contains("DIVORCIA") || valorExcel.equalsIgnoreCase("D")) {
                return "D";
            } else if (valorExcel.contains("SEPARAD") || valorExcel.equalsIgnoreCase("P")) {
                return "P";
            }
            return "";
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    private String obterSexo(String valorExcel) {
        try {

            if (UteisValidacao.emptyString(valorExcel.trim())) {
                return "";
            }

            valorExcel = Uteis.retirarAcentuacaoRegex(valorExcel.toUpperCase());

            if (valorExcel.contains("MASC") || valorExcel.equalsIgnoreCase("M")) {
                return "M";
            } else if (valorExcel.contains("FEMI") || valorExcel.equalsIgnoreCase("F")) {
                return "F";
            }
            return "";
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    private void adicionarTipoColaborador(List<TipoColaboradorImportacaoJSON> lista, String tipoColaboradorExcel) {
        try {
            if (UteisValidacao.emptyString(tipoColaboradorExcel.trim())) {
                return;
            }
            tipoColaboradorExcel = tipoColaboradorExcel.toUpperCase();

            TipoColaboradorEnum tipoColaboradorEnum = null;
            if (tipoColaboradorExcel.contains("PROFESSOR")) {
                tipoColaboradorEnum = TipoColaboradorEnum.PROFESSOR_TREINO;
            } else if (tipoColaboradorExcel.contains("RECEP") || tipoColaboradorExcel.contains("CONSULTOR")) {
                tipoColaboradorEnum = TipoColaboradorEnum.CONSULTOR;
            } else if (tipoColaboradorExcel.contains("COORDENADOR") || tipoColaboradorExcel.contains("GERENTE")) {
                tipoColaboradorEnum = TipoColaboradorEnum.COORDENADOR;
            } else if (tipoColaboradorExcel.contains("ADMINISTRADOR")) {
                tipoColaboradorEnum = TipoColaboradorEnum.ADMINISTRADOR;
            } else if (tipoColaboradorExcel.contains("PERSONAL")) {
                tipoColaboradorEnum = TipoColaboradorEnum.PERSONAL_TRAINER;
            }

            if (tipoColaboradorEnum != null) {
                TipoColaboradorImportacaoJSON json = new TipoColaboradorImportacaoJSON();
                json.setSigla(tipoColaboradorEnum.getSigla());
                json.setDescricao(tipoColaboradorEnum.getDescricao());
                lista.add(json);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void adicionarEnderecoExcel(List<EnderecoImportacaoJSON> lista, String cep, String bairro,
                                        String logradouro, String numero, String complemento) {
        try {
            EnderecoImportacaoJSON enderecoJSON = new EnderecoImportacaoJSON();
            enderecoJSON.setCep(cep);
            enderecoJSON.setBairro(bairro);
            enderecoJSON.setEndereco(logradouro);
            enderecoJSON.setNumero(numero);
            enderecoJSON.setComplemento(complemento);
            lista.add(enderecoJSON);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void obterContratoExcel(ClienteImportacaoJSON clienteJSON, ConfigExcelImportacaoTO configuracaoTO,
                                    Date dataLancamentoContrato, Date dataInicioContrato,
                                    Date dataFimContrato, Double valorContrato, String descricaoPlano) {
        try {
            if (dataInicioContrato != null && dataFimContrato != null && configuracaoTO.isImportarContratos()) {
                ContratoImportacaoJSON contratoJSON = new ContratoImportacaoJSON();
                contratoJSON.setIdExternoCliente(clienteJSON.getIdExterno());
                contratoJSON.setIdExterno(getListaContratos().size() + 1);
                contratoJSON.setDataInicio(dataInicioContrato);
                contratoJSON.setDataFim(dataFimContrato);
                if (dataLancamentoContrato == null) {
                    dataLancamentoContrato = dataInicioContrato;
                }

                Integer numeroMeses;
                Long dias = Uteis.nrDiasEntreDatas(contratoJSON.getDataInicio(), contratoJSON.getDataFim());
                if (dias < 30) {
                    numeroMeses = 1;
                } else {
                    numeroMeses = (new Long(dias / 30).intValue());
                }

                contratoJSON.setDuracao(numeroMeses);

                Integer nrParcelas = 1;
                if (configuracaoTO.isGerarParcelasDeAcordoDuracao()) {
                    nrParcelas = contratoJSON.getDuracao();
                }

                contratoJSON.setNrParcelas(nrParcelas);
                contratoJSON.setDataLancamento(dataLancamentoContrato);
                contratoJSON.setValorTotal(valorContrato);
                contratoJSON.setDescricaoPlano(descricaoPlano);
                contratoJSON.setPlano(configuracaoTO.getPlano());
                contratoJSON.setModalidade(configuracaoTO.getModalidade());
                contratoJSON.setUsuario(configuracaoTO.getUsuarioResponsavelImportacao().getCodigo());
                contratoJSON.setConsultor(configuracaoTO.getConsultor());
                contratoJSON.setDiasCarencia(configuracaoTO.getDiasCarencia());
                contratoJSON.setHorario(configuracaoTO.getHorario());
                contratoJSON.setImportacaoParcelasSituacao(configuracaoTO.getImportacaoParcelasSituacaoEnum().getCodigo());
                contratoJSON.setEmpresa(configuracaoTO.getEmpresaVO().getCodigo());
                getListaContratos().add(contratoJSON);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void adicionarEmailExcel(List<EmailImportacaoJSON> lista, String email) {
        try {
            lista.add(new EmailImportacaoJSON(email));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void adicionarCodigoHorarios(List<Integer> lista, String codigoHorarios) {
        try {
            lista.add(new Integer(codigoHorarios));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void adicionarTelefoneExcel(List<TelefoneImportacaoJSON> lista, TipoTelefoneEnum tipoTelefoneEnum, String numero) {
        try {
            TelefoneImportacaoJSON tel = new TelefoneImportacaoJSON();
            tel.setNumero(numero);
            tel.setTipoTelefone(tipoTelefoneEnum.getCodigo());
            lista.add(tel);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void obterVinculoExcel(ClienteImportacaoJSON clienteJSON, TipoColaboradorEnum tipoColaboradorEnum, String nomeColaborador) {
        try {
            VinculoImportacaoJSON vinculo = new VinculoImportacaoJSON();
            vinculo.setTipoVinculo(tipoColaboradorEnum.getSigla());
            vinculo.setNomeColaborador(nomeColaborador);
            clienteJSON.getVinculos().add(vinculo);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public Date obterDataExcel(XSSFRow row, int coluna, String mascaraData) {
        return LeitorExcel2010.obterData(row, coluna, mascaraData);
    }

    public Double obterDoubleExcel(XSSFRow row, int coluna) {
        return LeitorExcel2010.obterDouble(row, coluna);
    }

    public String obterStringExcel(XSSFRow row, int coluna) {
        return LeitorExcel2010.obterString(row, coluna);
    }

    public String obterStringExcelCorrigindoNotacoesCientificas(XSSFRow row, int coluna) {
        return LeitorExcel2010.obterStringCorrigindoNotacoesCientificas(row, coluna);
    }

    public Integer obterIntegerConvertendoStringExcel(XSSFRow row, int coluna) {
        String valorString = LeitorExcel2010.obterString(row, coluna);
        if (!UteisValidacao.emptyString(valorString)) {
            try {
                return Integer.parseInt(valorString);
            } catch (NumberFormatException e) {
                try {
                    Double valorDouble = Double.parseDouble(valorString);
                    if (valorDouble % 1 == 0) {
                        return valorDouble.intValue();
                    }
                } catch (NumberFormatException ignored) {
                }
            }
        }
        return null;
    }


    public Boolean obterBooleanExcel(XSSFRow row, int coluna, boolean retornoVazio) {
        return LeitorExcel2010.obterBooleanImportacao(row, coluna, retornoVazio);
    }

    public List<ParcelaPagamentoJSON> getListaParcelasPagamentos() {
        return listaParcelasPagamentos;
    }

    public void setListaParcelasPagamentos(List<ParcelaPagamentoJSON> listaParcelasPagamentos) {
        this.listaParcelasPagamentos = listaParcelasPagamentos;
    }

    public List<ClienteImportacaoJSON> getListaClientes() {
        if (listaClientes == null) {
            listaClientes = new ArrayList<>();
        }
        return listaClientes;
    }
    public List<AlunoTurmaImportacaoJSON> getListaAlunoTurma() {
        if (listaAlunoTurma == null) {
            listaAlunoTurma = new ArrayList<>();
        }
        return listaAlunoTurma;
    }

    public List<TurmaImportacaoJSON> getListaTurma() {
        if (listaTurma == null) {
            listaTurma = new ArrayList<>();
        }
        return listaTurma;
    }

    public void setListaTurma(List<TurmaImportacaoJSON> listaTurma) {
        this.listaTurma = listaTurma;
    }

    public void setListaAlunoTurma(List<AlunoTurmaImportacaoJSON> listaAlunoTurma) {
        this.listaAlunoTurma = listaAlunoTurma;
    }

    public void setListaClientes(List<ClienteImportacaoJSON> listaClientes) {
        this.listaClientes = listaClientes;
    }

    public List<ContratoImportacaoJSON> getListaContratos() {
        if (listaContratos == null) {
            listaContratos = new ArrayList<>();
        }
        return listaContratos;
    }

    public void setListaContratos(List<ContratoImportacaoJSON> listaContratos) {
        this.listaContratos = listaContratos;
    }

    public List<FamiliarImportacaoJSON> getListaFamiliares() {
        return listaFamiliares;
    }

    public void setListaFamiliares(List<FamiliarImportacaoJSON> listaFamiliares) {
        this.listaFamiliares = listaFamiliares;
    }

    public List<TreinoAtividadeJSON> getListaTreinoAtividades() {
        if (listaTreinoAtividades == null) {
            listaTreinoAtividades = new ArrayList<>();
        }
        return listaTreinoAtividades;
    }

    public void setListaTreinoAtividades(List<TreinoAtividadeJSON> listaTreinoAtividades) {
        this.listaTreinoAtividades = listaTreinoAtividades;
    }

    public List<ProgramaFichaJSON> getListaTreinoProgramas() {
        return listaTreinoProgramas;
    }

    public void setListaTreinoProgramas(List<ProgramaFichaJSON> listaTreinoProgramas) {
        this.listaTreinoProgramas = listaTreinoProgramas;
    }

    public List<AtividadeFichaJSON> getListaTreinoAtividadeFicha() {
        return listaTreinoAtividadeFicha;
    }

    public void setListaTreinoAtividadeFicha(List<AtividadeFichaJSON> listaTreinoAtividadeFicha) {
        this.listaTreinoAtividadeFicha = listaTreinoAtividadeFicha;
    }

    public List<ProdutoImportacaoJSON> obterListaProdutosJSONExcel(List<XSSFRow> linhas,
                                                                   ConfigExcelImportacaoTO configuracaoTO) throws Exception {

        //Dados de planilha modelo em excel que se encontra dentro da pasta modelos no projeto.
        MascaraDataEnum mascaraDataEnum = MascaraDataEnum.obterPorCodigo(configuracaoTO.getMascaraDataEnum());
        if (mascaraDataEnum == null) {
            throw new Exception("Máscara não encontrada");
        }
        String mascaraData = mascaraDataEnum.getMascara();

        List<ProdutoImportacaoJSON> lista = new ArrayList<>();
        for (XSSFRow linha : linhas) {
            try {
                ProdutoImportacaoJSON produtoJSON = new ProdutoImportacaoJSON();

                int i = 0;
                String idExternoString = obterStringExcel(linha, i); //idExterno
                String descricao = obterStringExcel(linha, ++i); //descricao

                if (UteisValidacao.emptyString(descricao)) {
                    continue;
                }

                Integer idExterno = 0;
                try {
                    idExterno = Integer.parseInt(idExternoString.trim());
                } catch (Exception ex) {
                    idExterno = 0;
                }

                if (UteisValidacao.emptyNumber(idExterno)) {
                    continue;
                }

                produtoJSON.setIdExterno(idExterno);
                produtoJSON.setDescricao(descricao);
                produtoJSON.setValor(obterDoubleExcel(linha, ++i)); //valor
                produtoJSON.setTipo(obterStringExcel(linha, ++i)); //tipo
                produtoJSON.setCategoria(obterStringExcel(linha, ++i)); //categoria
                produtoJSON.setCodigoBarras(obterStringExcel(linha, ++i)); //codigo barras
                produtoJSON.setAtivo(obterBooleanExcel(linha, ++i, true)); //situacao

                Integer ndDias = null;
                try {
                    String dias = obterStringExcel(linha, ++i); //nrDias
                    ndDias = Integer.parseInt(dias);
                } catch (Exception ex) {
                    ndDias = null;
                }
                produtoJSON.setVigenciaNrDias(ndDias);

                Date vigenciaInicio = obterDataExcel(linha, ++i, mascaraData); //vigenciaInicio
                Date vigenciaFinal = obterDataExcel(linha, ++i, mascaraData); //vigenciaFinal
                produtoJSON.setVigenciaInicio(vigenciaInicio);
                produtoJSON.setVigenciaFinal(vigenciaFinal);

                if (produtoJSON.getVigenciaInicio() != null && produtoJSON.getVigenciaFinal() != null) {
                    produtoJSON.setVigenciaNrDias(null);
                }

                // Se a coluna estiver vazia, vai para o catch que seta false.
                try {
                    produtoJSON.setBloquearAposVigencia(obterBooleanExcel(linha, ++i, true));
                }catch (Exception e){
                    produtoJSON.setBloquearAposVigencia(false);
                }

                produtoJSON.setObservacao(obterStringExcel(linha, ++i)); //observacao

                lista.add(produtoJSON);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return lista;
    }



    public List<MetaExtraImportadoDTO> obterListaMetaExtraExcel(List<XSSFRow> linhas) throws Exception {
        List<MetaExtraImportadoDTO> dtos = new ArrayList<>();
        for (XSSFRow linha : linhas) {
            try {
                MetaExtraImportadoDTO dto = new MetaExtraImportadoDTO();
                int i = 0;
                dto.setMatricula(obterStringExcel(linha, i).replace(".0", "")); //matricula
                dto.setNome(obterStringExcel(linha, ++i)); //nome
                dto.setEmail(obterStringExcel(linha, ++i)); //email
                dto.setTelefone(obterStringExcel(linha, ++i)); //telefone
                dto.setCampoLivre(obterStringExcel(linha, ++i)); //campo livre
                if(UteisValidacao.emptyString(dto.getNome())){
                    continue;
                }
                dtos.add(dto);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return dtos;
    }
}
