package importador.colaborador;


import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import controle.arquitetura.security.UsuarioControle;
import importador.ImportacaoCache;
import importador.ImportacaoConfigTO;
import importador.UteisImportacao;
import importador.json.ColaboradorImportacaoJSON;
import negocio.comuns.arquitetura.HorarioAcessoSistemaVO;
import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class ImportarColaborador {

    private Connection con;
    private ImportacaoConfigTO configTO;

    public ImportarColaborador(ImportacaoConfigTO configTO, Connection con) {
        this.con = con;
        this.configTO = configTO;
    }

    public ImportarColaborador(Connection con) {
        this.con = con;
    }

    public ColaboradorVO importarColaborador(ColaboradorImportacaoJSON json, ConfigExcelImportacaoTO configExcelImportacaoTO, ImportacaoCache cache) throws Exception {
        try {
            con.setAutoCommit(false);

            Uteis.logar(true, null, "INICIA IMPORTAÇÃO | COLABORADOR... " + json.getIdExterno() + " | " + json.getNome().toUpperCase());

            if (UteisValidacao.emptyNumber(json.getIdExterno())) {
                throw new Exception("Necessário que o colaborador tenha um IdExterno");
            }

            ResultSet rsExiste = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM colaborador WHERE idExterno = '" + json.getIdExterno()
                    + "' and empresa = " + json.getEmpresa(), con);
            if (rsExiste.next()) {
                throw new Exception("Já existe um COLABORADOR importado com o idExterno " + json.getIdExterno() + " na empresa " + json.getEmpresa());
            }

            UteisImportacao uteisImportacao = new UteisImportacao();

            if (configExcelImportacaoTO.isValidarCpfCnpjJaCadastrado() && !json.getCpf().equals("")) {
                StringBuilder sqlPessoa = new StringBuilder();
                sqlPessoa.append("select \n");
                sqlPessoa.append("p.nome,\n");
                sqlPessoa.append("p.cfp\n");
                sqlPessoa.append("from colaborador c\n");
                sqlPessoa.append("inner join pessoa p on p.codigo = c.pessoa\n");
                sqlPessoa.append("where (p.cfp = '").append(json.getCpf_Formatado()).append("' or p.cfp = '").append(json.getCpf_SomenteNumeros()).append("') ");
                ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlPessoa.toString(), con);
                if (rs.next()) {
                    String nome = rs.getString("nome");
                    throw new Exception("O CPF (" + json.getCpf_Formatado() + ") já está cadastrado para um colaborador: \"" + nome + "\".");
                }
            }

            EstadoVO estadoVO = new EstadoVO();
            CidadeVO cidadeVO = new CidadeVO();
            PaisVO paisVO = new PaisVO();

            String ufCliente = json.getUf();
            if (!UteisValidacao.emptyString(ufCliente)) {
                estadoVO = cache.obterEstadoVO(ufCliente);
                if (estadoVO != null && !UteisValidacao.emptyNumber(estadoVO.getCodigo())) {
                    if (!UteisValidacao.emptyNumber(estadoVO.getPais())) {
                        paisVO.setCodigo(estadoVO.getPais());
                    }
                    cidadeVO = uteisImportacao.obterCidade(json.getCidade(), estadoVO, con);
                }
            }

            if (cidadeVO != null && !UteisValidacao.emptyNumber(cidadeVO.getCodigo())) {
                estadoVO = cidadeVO.getEstado();
                paisVO = cidadeVO.getPais();
            }

            PessoaVO pessoaVO = new PessoaVO();
//        pessoaVO.setIdExterno(json.getIdExterno());
            pessoaVO.setNome(json.getNome().toUpperCase());
            pessoaVO.setCfp(json.getCpf_Formatado());
            pessoaVO.setDataCadastro(json.getDataCadastro());
            pessoaVO.setDataNasc(uteisImportacao.obterDataNascimento(json.getDataNascimento()));
            pessoaVO.setSexo(json.getSexo());
            pessoaVO.setTipoPessoa(TipoPessoaEnum.COLABORADOR.getTipo());
            pessoaVO.setNacionalidade(json.getNacionalidade());
            pessoaVO.setNaturalidade(json.getNaturalidade());
            pessoaVO.setRg(json.getRg());
            pessoaVO.setRgOrgao(json.getRgOrgao());
            pessoaVO.setPais(paisVO);
            pessoaVO.setEstadoVO(estadoVO);
            pessoaVO.setCidade(cidadeVO);
            pessoaVO.setEstadoCivil(json.getEstadoCivil());
            pessoaVO.setGrauInstrucao(uteisImportacao.obterGrauInstrucao(json.getGrauInstrucao(), con));
            pessoaVO.setProfissao(uteisImportacao.obterProfissao(json.getProfissao(), con));
            pessoaVO.setEnderecoVOs(uteisImportacao.obterListaEnderecoVO(json.getEnderecos()));
            pessoaVO.setTelefoneVOs(uteisImportacao.obterListaTelefonesVO(json.getTelefones(), configExcelImportacaoTO.getPadraoDDD()));
            pessoaVO.setEmailVOs(uteisImportacao.obterListaEmailVO(json.getEmails()));

            //incluir pessoa
            Pessoa pessoaDAO = new Pessoa(con);
            pessoaDAO.incluirPessoaImportacao(pessoaVO);
            pessoaDAO = null;
            if (UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                throw new Exception("Erro ao incluir pessoa.");
            }

            ColaboradorVO colaboradorVO = new ColaboradorVO();
            colaboradorVO.setPessoa(pessoaVO);
            colaboradorVO.setCref(json.getCref());
            colaboradorVO.setEmpresa(cache.obterEmpresaVO(json.getEmpresa()));
            if (json.isAtivo()) {
                colaboradorVO.setSituacao("AT");
            } else {
                colaboradorVO.setSituacao("NA");
            }
            colaboradorVO.setListaTipoColaboradorVOs(uteisImportacao.obterTipoColaboradorVO(json.getListaTipoColaborador()));
            uteisImportacao = null;

            //incluir colaborador
            Colaborador colaboradorDAO = new Colaborador(con);
            colaboradorDAO.incluirSemCommit(colaboradorVO, false);
            colaboradorDAO = null;

            if (UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
                throw new Exception("Erro ao criar colaborador.");
            }

            SuperFacadeJDBC.executarUpdate("update colaborador set idExterno = '" + json.getIdExterno() + "' where codigo = " + colaboradorVO.getCodigo(), con);

            //incluir usuario
            UsuarioVO usuarioVO = new UsuarioVO();
            if (configExcelImportacaoTO.isCriarUsuario()) {

                if (UteisValidacao.emptyList(pessoaVO.getEmailVOs())) {
                    throw new Exception("É necessário que o colaborador tenha pelo menos um email para criar o usuário.");
                }

                Integer senhaAleatoria = UteisValidacao.gerarNumeroRandomico(100000, 999999);
                usuarioVO = new UsuarioVO();
                usuarioVO.setDataUltimaAlteracaoSenha(Calendario.hoje());
                usuarioVO.setTipoUsuario("CE");
                usuarioVO.setColaboradorVO(colaboradorVO);
                usuarioVO.setNome(json.getNome());
                usuarioVO.setUsername(obterUsername(json.getNome()));
                usuarioVO.setSenha(senhaAleatoria.toString());
                usuarioVO.setSenhaNaoCriptografada(senhaAleatoria.toString());
                usuarioVO.setEmailVO(pessoaVO.getEmailVOs().get(0));

                //perfil de acesso
                usuarioVO.setUsuarioPerfilAcessoVOs(obterPerfilAcessoVO(colaboradorVO, cache));

                //horarios de acesso
                usuarioVO.setUsuarioHorarioAcessoSistemaVOs(obterHorarioAcessoSistemaVO("06:00", "23:00"));

                Usuario usuarioDAO = new Usuario(con);
                usuarioDAO.incluirSemCommit(usuarioVO, false);
                usuarioDAO = null;

                if (UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                    throw new Exception("Erro ao criar usuário.");
                }

                Log logDao = new Log(con);
                logDao.incluirLogItemImportacao("USUARIO", usuarioVO.getCodigo(), cache.getUsuarioVOImportacao(), 0);
                logDao = null;
            }

            Log logDao = new Log(con);
            logDao.incluirLogItemImportacao("COLABORADOR", colaboradorVO.getCodigo(), cache.getUsuarioVOImportacao(), pessoaVO.getCodigo());
            logDao = null;

            //enviar email
            String msgEmail = "";
            if (!UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                Usuario usuarioDAO = new Usuario(con);
                msgEmail = usuarioDAO.enviarEmailSenha(getConfigTO().getChave(), usuarioVO);
                usuarioDAO = null;
            }

            if (!UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
                json.setSucesso(true);
                json.setCodigo(colaboradorVO.getCodigo());
                json.setCodigoUsuario(usuarioVO.getCodigo());
                json.setMsgEnvioEmail(msgEmail);
                json.setMsgRetorno("Colaborador importado com sucesso.");
            } else {
                throw new Exception("Colaborador não importado.");
            }

            con.commit();
            return colaboradorVO;
        } catch (Exception ex) {
            json.setSucesso(false);
            json.setCodigo(null);
            json.setMsgRetorno(ex.getMessage());
            ex.printStackTrace();
            con.rollback();
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public UsuarioVO consultarOuCriarUsuarioImportadorTreino(String chave, Integer codigoEmpresa) throws Exception {
        try {
            con.setAutoCommit(false);

            String nome = "Importador Treino";
            String email = "<EMAIL>";
            String idExterno = "IMPORTADORTW";
            String dtNascimento = "01/01/1999";
            String senha = PropsService.getPassWordImportadorTreino();

            Empresa empresaDao = new Empresa(con);
            EmpresaVO empresaVO = empresaDao.consultarPorCodigo(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            Usuario usuarioDAO = new Usuario(con);

            ResultSet rsUsuario = SuperFacadeJDBC.criarConsulta("SELECT u.codigo FROM usuario u \n" +
                "WHERE upper(trim(u.username)) = '" + email.toUpperCase() + "'", con);
            if (rsUsuario.next()) {
                return usuarioDAO.consultarPorCodigo(rsUsuario.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            if (UteisValidacao.emptyString(senha)) {
                throw new Exception("Falha ao criar usuário padrão importador treino");
            }

            ImportacaoCache importacaoCache = new ImportacaoCache(con, usuarioDAO.consultarPorUsername("PACTOBR", Uteis.NIVELMONTARDADOS_DADOSBASICOS).getCodigo());

            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setNome(nome);
            pessoaVO.setDataCadastro(Calendario.hoje());
            pessoaVO.setDataNasc(Calendario.getDate("dd/MM/yyyy", dtNascimento));
            pessoaVO.setTipoPessoa(TipoPessoaEnum.COLABORADOR.getTipo());
            pessoaVO.setPais(empresaVO.getPais());
            pessoaVO.setEstadoVO(empresaVO.getEstado());
            pessoaVO.setCidade(empresaVO.getCidade());
            EmailVO emailVO = new EmailVO();
            emailVO.setEmail(email);
            emailVO.setEmailCorrespondencia(false);
            pessoaVO.getEmailVOs().add(emailVO);
            pessoaVO.setValidarDados(false);

            //incluir pessoa
            Pessoa pessoaDAO = new Pessoa(con);
            pessoaDAO.incluirPessoaImportacao(pessoaVO);
            pessoaDAO = null;
            if (UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                throw new Exception("Erro ao incluir pessoa.");
            }

            ColaboradorVO colaboradorVO = new ColaboradorVO();
            colaboradorVO.setPessoa(pessoaVO);
            colaboradorVO.setEmpresa(empresaVO);
            colaboradorVO.setSituacao("AT");
            TipoColaboradorVO tipoColaboradorVO = new TipoColaboradorVO();
            tipoColaboradorVO.setDescricao(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla());
            colaboradorVO.getListaTipoColaboradorVOs().add(tipoColaboradorVO);

            //incluir colaborador
            Colaborador colaboradorDAO = new Colaborador(con);
            colaboradorDAO.incluirSemCommit(colaboradorVO, false);
            colaboradorDAO = null;

            if (UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
                throw new Exception("Erro ao criar colaborador.");
            }

            SuperFacadeJDBC.executarUpdate("update colaborador set idExterno = '" + idExterno + "' where codigo = " + colaboradorVO.getCodigo(), con);

            //incluir usuario
            if (UteisValidacao.emptyList(pessoaVO.getEmailVOs())) {
                throw new Exception("É necessário que o colaborador tenha pelo menos um email para criar o usuário.");
            }

            UsuarioVO usuarioVO = new UsuarioVO();
            usuarioVO.setDataUltimaAlteracaoSenha(Calendario.hoje());
            usuarioVO.setTipoUsuario("CE");
            usuarioVO.setColaboradorVO(colaboradorVO);
            usuarioVO.setNome(pessoaVO.getNome());
            usuarioVO.setUsername(email);
            usuarioVO.setSenha(senha);
            usuarioVO.setSenhaNaoCriptografada(senha);
            usuarioVO.setEmailVO(pessoaVO.getEmailVOs().get(0));
            usuarioVO.setStatusTw(0);

            //perfil de acesso
            usuarioVO.setUsuarioPerfilAcessoVOs(obterPerfilAcessoVO(colaboradorVO, importacaoCache));

            //horarios de acesso
            usuarioVO.setUsuarioHorarioAcessoSistemaVOs(obterHorarioAcessoSistemaVO("06:00", "23:00"));

            usuarioDAO.incluirSemCommit(usuarioVO, false);
            usuarioDAO = null;

            if (UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Erro ao criar usuário.");
            }

            Log logDao = new Log(con);
            logDao.incluirLogItemImportacao("USUARIO", usuarioVO.getCodigo(), importacaoCache.getUsuarioVOImportacao(), 0);
            logDao.incluirLogItemImportacao("COLABORADOR", colaboradorVO.getCodigo(), importacaoCache.getUsuarioVOImportacao(), pessoaVO.getCodigo());
            logDao = null;

            empresaDao = null;

            con.commit();

            try {
                UsuarioControle usuarioControle = new UsuarioControle();
                usuarioControle.setKey(chave);
                usuarioControle.setUsuarioVO(null);
                usuarioControle.editarParametro(usuarioVO);
                usuarioControle.preencherUsuarioEmail();
                usuarioControle.sincronizarUsuarioMovel(true, chave);
            } catch (Exception e) {
                e.printStackTrace();
            }

            return usuarioVO;
        } catch (Exception ex) {
            ex.printStackTrace();
            con.rollback();
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private List<UsuarioPerfilAcessoVO> obterPerfilAcessoVO(ColaboradorVO colaboradorVO, ImportacaoCache cache) throws Exception {
        List<UsuarioPerfilAcessoVO> lista = new ArrayList<>();
        UsuarioPerfilAcessoVO usuarioPerfilAcessoVO = new UsuarioPerfilAcessoVO();
        usuarioPerfilAcessoVO.setEmpresa(colaboradorVO.getEmpresa());
        usuarioPerfilAcessoVO.setPerfilAcesso(obterPerfilAcesso(colaboradorVO, cache));
        lista.add(usuarioPerfilAcessoVO);
        return lista;
    }

    private PerfilAcessoVO obterPerfilAcesso(ColaboradorVO colaboradorVO, ImportacaoCache cache) throws Exception {
        List<PerfilAcessoVO> listaPerfis = cache.obterListaPerfilAcesso();
        for (PerfilAcessoVO obj : listaPerfis) {
            for (TipoColaboradorVO tipoVO : colaboradorVO.getListaTipoColaboradorVOs()) {
                if (UteisValidacao.emptyString(tipoVO.getDescricao())) {
                    continue;
                }

                if (obj.getTipo() != null) {
                    PerfilUsuarioEnum perfilUsuarioEnum = PerfilUsuarioEnum.getFromOrdinal(obj.getTipo().getId());
                    if (perfilUsuarioEnum != null && tipoVO.getDescricao_Apresentar().toUpperCase().contains(perfilUsuarioEnum.getNome().toUpperCase())) {
                        return obj;
                    }
                }
                if (obj.getNome().toUpperCase().contains(tipoVO.getDescricao_Apresentar().toUpperCase())) {
                    return obj;
                }
            }
        }
        throw new Exception("Não foi encontrado perfil de acesso (" + colaboradorVO.getListaTipoColaboradorVOs().get(0).getDescricao_Apresentar().toUpperCase() + ")");
    }

    private List<HorarioAcessoSistemaVO> obterHorarioAcessoSistemaVO(String horaInicial, String horaFinal) {
        List<HorarioAcessoSistemaVO> lista = new ArrayList<>();

        HorarioAcessoSistemaVO hor = new HorarioAcessoSistemaVO();
        hor.setDiaSemana(DiaSemana.DOMINGO);
        hor.setHoraInicial(horaInicial);
        hor.setHoraFinal(horaFinal);
        lista.add(hor);

        HorarioAcessoSistemaVO hor2 = new HorarioAcessoSistemaVO();
        hor2.setDiaSemana(DiaSemana.SEGUNDA_FEIRA);
        hor2.setHoraInicial(horaInicial);
        hor2.setHoraFinal(horaFinal);
        lista.add(hor2);

        HorarioAcessoSistemaVO hor3 = new HorarioAcessoSistemaVO();
        hor3.setDiaSemana(DiaSemana.TERCA_FEIRA);
        hor3.setHoraInicial(horaInicial);
        hor3.setHoraFinal(horaFinal);
        lista.add(hor3);

        HorarioAcessoSistemaVO hor4 = new HorarioAcessoSistemaVO();
        hor4.setDiaSemana(DiaSemana.QUARTA_FEIRA);
        hor4.setHoraInicial(horaInicial);
        hor4.setHoraFinal(horaFinal);
        lista.add(hor4);

        HorarioAcessoSistemaVO hor5 = new HorarioAcessoSistemaVO();
        hor5.setDiaSemana(DiaSemana.QUINTA_FEIRA);
        hor5.setHoraInicial(horaInicial);
        hor5.setHoraFinal(horaFinal);
        lista.add(hor5);

        HorarioAcessoSistemaVO hor6 = new HorarioAcessoSistemaVO();
        hor6.setDiaSemana(DiaSemana.SEXTA_FEIRA);
        hor6.setHoraInicial(horaInicial);
        hor6.setHoraFinal(horaFinal);
        lista.add(hor6);

        HorarioAcessoSistemaVO hor7 = new HorarioAcessoSistemaVO();
        hor7.setDiaSemana(DiaSemana.SABADO);
        hor7.setHoraInicial(horaInicial);
        hor7.setHoraFinal(horaFinal);
        lista.add(hor7);

        return lista;
    }

    private String obterUsername(String nome) throws Exception {
        String username = Uteis.getPrimeiroNome(nome);

        Usuario usuarioDAO = new Usuario(con);
        boolean existe = usuarioDAO.consultarPorUsernameEDiferenteDoUsuario(username, 0);
        if (existe) {
            username = Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(nome).replaceAll("\\.", "");
            existe = usuarioDAO.consultarPorUsernameEDiferenteDoUsuario(username, 0);
            if (existe) {
                username = gerarUsername(Uteis.getPrimeiroNome(nome));
            }
        }
        usuarioDAO = null;
        return username;
    }

    private String gerarUsername(String primeiroNome) throws Exception {
        String retorno = primeiroNome + UteisValidacao.gerarNumeroRandomico(100000, 999999);

        Usuario usuarioDAO = new Usuario(con);
        boolean existe = usuarioDAO.consultarPorUsernameEDiferenteDoUsuario(retorno, 0);
        usuarioDAO = null;
        if (existe) {
            return gerarUsername(primeiroNome);
        }
        return retorno;
    }

    public Connection getCon() {
        return con;
    }

    public void setCon(Connection con) {
        this.con = con;
    }

    public ImportacaoConfigTO getConfigTO() {
        return configTO;
    }

    public void setConfigTO(ImportacaoConfigTO configTO) {
        this.configTO = configTO;
    }
}
