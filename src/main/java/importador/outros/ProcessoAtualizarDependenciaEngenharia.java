package importador.outros;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import importador.UteisImportacao;
import importador.enumerador.OperacoesProcessoDependenciaEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ContratoDependenteVO;
import negocio.comuns.basico.FamiliarVO;
import negocio.comuns.basico.ParentescoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.financeiro.EstornoReciboVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ContratoDependente;
import negocio.facade.jdbc.basico.Familiar;
import negocio.facade.jdbc.basico.Parentesco;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.propriedades.PropsService;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class ProcessoAtualizarDependenciaEngenharia {

    private final String key;
    private final boolean reabrirContratos;

    private Connection conZW;

    private ReciboPagamento reciboPagamentoDAO;
    private MovPagamento movPagamentoDAO;
    private MovParcela movParcelaDAO;
    private Cliente clienteDAO;
    private Contrato contratoDAO;
    private ContratoDependente contratoDependenteDAO;
    private ZillyonWebFacade zwFacadeDAO;
    private Familiar familiarDAO;
    private Parentesco parentescoDAO;
    private Transacao transacaoDAO;
    private Usuario usuarioDAO;

    public ProcessoAtualizarDependenciaEngenharia(final String key, boolean reabrirContratos) {
        this.key = key;
        this.reabrirContratos = reabrirContratos;
    }

    public ProcessoAtualizarDependenciaEngenharia(Connection conZW) {
        this.conZW = conZW;
        this.key = "";
        this.reabrirContratos = false;
    }

    public static void main(String[] args) {
        try {
            String keyParam = "saofrancisco";
            Integer codigoEmpresa = 1;
            String matriculas = "";
            boolean reabrirContrato = false;
            String operacao = "dependencia_importacao";
            if (args.length > 0) {
                keyParam = args[0];
                if (args.length > 1) {
                    reabrirContrato = Boolean.parseBoolean(args[1]);
                }
                if (args.length > 2) {
                    codigoEmpresa = Integer.parseInt(args[2]);
                }
                if (args.length > 3) {
                    operacao = args[3];
                }
            }

            ProcessoAtualizarDependenciaEngenharia processo = new ProcessoAtualizarDependenciaEngenharia(keyParam, reabrirContrato);
            processo.inicializar();

            if (processo.reabrirContratos) {
                processo.reabrirContratosTransferidosCancelados();
            }

            OperacoesProcessoDependenciaEnum operacaoEnum = OperacoesProcessoDependenciaEnum.valueOf(operacao.toUpperCase());
            switch (operacaoEnum) {
                case DEPENDENCIA_IMPORTACAO:
                    processo.validarCorrespondenciaPlanos();
                    List<ContratoParaCessao> contratosParaDependencia = processo.consultarContratosParaDependencia(matriculas);
                    processo.processarDependentesZW(contratosParaDependencia, codigoEmpresa);
                    break;
                case DEPENDENCIA_CONTRATOS_RENOVADOS_ADM:
                    processo.processoIncluirContratoDependentes(codigoEmpresa, matriculas);
                    break;
                case CORRIGIR_COMPARTILHAMENTOS_IMPORTACAO_EVO:
                    processo.corrigirCompartilhamentosImportacaoEvo();
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void validarCorrespondenciaPlanos() throws Exception {
        String sql = "select  \n" +
                " case when c.observacao like '%;%' then substring(c.observacao, 0, position(';' in c.observacao))\n" +
                "      else c.observacao end as obs," +
                " count(c.codigo) as qtd_contratos," +
                " array_agg(c.codigo) as cod_contratos" +
                " from contrato c\n" +
                "inner join plano pl on c.plano = pl.codigo\n" +
                "inner join cliente cli on cli.pessoa = c.pessoa\n" +
                "where coalesce(c.id_externo,0) > 0 \n" +
                "   and (case when c.observacao like '%;%' then substring(c.observacao, 0, position(';' in c.observacao)) else c.observacao end ilike '%plano evo: % - %dobro%' ) \n" +
                "   and coalesce(pl.quantidadecompartilhamentos, 0) = 0 \n" +
                "   and c.valorfinal > 0\n" +
                "  group by 1 \n" +
                " order by count(c.codigo) desc";
        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql", conZW);
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conZW);
        if (total > 0) {
            System.out.println("[Atenção] ->  Foram encontrados " + total + " planos para verificar a correspondencia:");
        }
        while (rs.next()) {
            System.out.println(rs.getString("obs").replaceFirst("plano evo: ", "").replaceFirst(" - ", " | "));
        }
        if (total > 0) {
            throw new Exception("Os contratos dependentes não foram atualizados, pois existem contratos sem correspondencia ou com incosistencia.");
        }
    }

    private void processoIncluirContratoDependentes(Integer codEmpresa, String matriculaDependentes) throws Exception {
        ParentescoVO parentescoTitular = parentescoDAO.obterParentescoCriandoSeNaoExiste("TITULAR");

        String sql = "select \n" +
                " distinct clit.codigo as clientetitular,\n" +
                " clid.codigomatricula as matricula_dependente,\n" +
                " pd.nome as nome_dependente,\n" +
                " cd.cliente as clientedependente,\n" +
                " cd.contrato as contratotitular,\n" +
                " cd.posicaodependente,\n" +
                " ct.situacao  as situacao_contrato_titular,\n" +
                " ct_atual.codigo as contratotitual_atual,\n" +
                " pla_atual.descricao,\n" +
                " (select codigo from contratodependente where contrato = ct_atual.codigo and coalesce(cliente,0) = 0 order by codigo limit 1) as contratodependente_atual,\n" +
                " f.codigo as vinculo_familiar\n" +
                "from contratodependente cd\n" +
                " inner join contrato ct on ct.codigo = cd.contrato\n" +
                " inner join cliente clit on clit.pessoa = ct.pessoa \n" +
                " inner join contrato ct_atual on ct_atual.codigo = (\n" +
                "  select max(con.codigo) from contrato con \n" +
                "   inner join plano pla on pla.codigo = con.plano \n" +
                "  where con.situacao = 'AT' \n" +
                "  and pla.quantidadecompartilhamentos > 0\n" +
                "  and con.pessoa = ct.pessoa)\n" +
                " inner join plano pla_atual on pla_atual.codigo = ct_atual.plano\n" +
                " inner join cliente clid on clid.codigo = cd.cliente \n" +
                " left join familiar f on f.cliente = clit.codigo and f.familiar = clid.codigo \n" +
                " inner join pessoa pd on pd.codigo = clid.pessoa \n" +
                "where 1 = 1\n" +
                "and coalesce(ct.id_externo,0) <> 0\n" +
                "and ct.situacao = 'IN'\n" +
                "and not exists (select codigo from contratodependente where contrato = ct_atual.codigo and cliente = clid.codigo)\n" +
                "and not exists (select codigo from contratodependente where cliente = clid.codigo and datafinalajustada > current_date)\n\n" +
                "and not exists (select codigo from contrato where pessoa = clid.pessoa and situacao = 'AT')\n";

        if (!UteisValidacao.emptyString(matriculaDependentes)) {
            sql += "and clid.codigomatricula in (" + matriculaDependentes + ") \n";
        }

        sql +=  "order by clit.codigo, cd.contrato, cd.posicaodependente \n";


        Integer total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql", conZW);
        int count = 0;
        Integer sucesso = 0;
        Integer falha = 0;

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), conZW);
        while (rs.next()) {
            try {
                conZW.setAutoCommit(false);

                Integer codigoContratoAtualTitular = rs.getInt("contratotitual_atual");
                Integer codigoFamiliar = rs.getInt("vinculo_familiar");

                ClienteVO clienteDependente = clienteDAO.consultarPorChavePrimaria(rs.getInt("clientedependente"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                System.out.println(++count + "\\" + total + " - Incluindo contrato dependente para matricula: " + rs.getInt("matricula_dependente"));

                ResultSet rsContratoDepenete = SuperFacadeJDBC.criarConsulta("select codigo from contratodependente where contrato = " + codigoContratoAtualTitular + " and coalesce(cliente,0) = 0 order by codigo limit 1", conZW);

                Integer codigoContratoDepententeVincular;
                if (rsContratoDepenete.next()) {
                    codigoContratoDepententeVincular = rsContratoDepenete.getInt("codigo");
                } else {
                    throw new Exception("Cliente titular não possui contrato depente disponivel para compartilhar");
                }

                if (UteisValidacao.emptyNumber(codigoFamiliar)) {
                    FamiliarVO familiarVO = new FamiliarVO();
                    familiarVO.setCliente(rs.getInt("clientetitular"));

                    familiarVO.setFamiliar(clienteDependente.getCodigo());
                    familiarVO.setNome(clienteDependente.getNome_Apresentar());
                    familiarVO.setCodAcesso(clienteDependente.getCodAcesso());
                    familiarVO.setParentesco(parentescoTitular);

                    familiarVO.setCompartilharPlano(true);
                    familiarVO.setContratoCompartilhado(codigoContratoAtualTitular);
                    familiarVO.setContratoDependente(new ContratoDependenteVO());
                    familiarVO.getContratoDependente().setCodigo(codigoContratoDepententeVincular);
                    familiarVO.getContratoDependente().setCliente(clienteDependente);
                    familiarDAO.incluir(familiarVO, true);
                } else {
                    FamiliarVO familiarVO = familiarDAO.consultarPorChavePrimaria(codigoFamiliar, Uteis.NIVELMONTARDADOS_TODOS);

                    familiarVO.setCompartilharPlano(true);
                    familiarVO.setContratoCompartilhado(codigoContratoAtualTitular);
                    familiarVO.setContratoDependente(new ContratoDependenteVO());
                    familiarVO.getContratoDependente().setCodigo(codigoContratoDepententeVincular);
                    familiarVO.getContratoDependente().setCliente(clienteDependente);
                    familiarDAO.alterar(familiarVO);
                }

                conZW.commit();
                sucesso++;
            } catch (Exception ex) {
                falha++;
                conZW.rollback();
                conZW.setAutoCommit(true);
                Uteis.logarDebug("ERRO: " + ex.getMessage());
            } finally {
                conZW.setAutoCommit(true);
            }
        }
        Uteis.logarDebug("--- TOTAL | " + total);
        Uteis.logarDebug("--- TOTAL | SUCESSO | " + sucesso);
        Uteis.logarDebug("--- TOTAL | FALHA   | " + falha);
    }

    private void processarDependentesZW(List<ContratoParaCessao> contratosParaDependencia, Integer codigoEmpresa) throws Exception {
        List<String> listSucesso = new ArrayList<>();
        List<String> listSemSucesso = new ArrayList<>();
        List<String> listAvaliar = new ArrayList<>();
        List<String> listErros = new ArrayList<>();

        criarColunasIdsExternos();

        UsuarioVO usuarioAdmin = usuarioDAO.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);
        JustificativaOperacaoVO justificativaFerias = UteisImportacao.obterJustificativaOperacao(codigoEmpresa, "IMPORTAÇÃO AFASTAMENTO - FÉRIAS", "CR", conZW);
        JustificativaOperacaoVO justificativaAtestado = UteisImportacao.obterJustificativaOperacao(codigoEmpresa, "IMPORTAÇÃO AFASTAMENTO - ATESTADO", "AT", conZW);

        ParentescoVO parentescoTitular = parentescoDAO.obterParentescoCriandoSeNaoExiste("TITULAR");

        int qtdTotal = contratosParaDependencia.size();
        for (ContratoParaCessao contratoParaCessao : contratosParaDependencia) {
            try {
                ContratoVO contratoTitular = contratoDAO.consultarPorChavePrimaria(contratoParaCessao.getCodigoContrato(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                Integer codigoPessoaTitular = contratoTitular.getPessoaOriginal() != null && !UteisValidacao.emptyNumber(contratoTitular.getPessoaOriginal().getCodigo())
                        ? contratoTitular.getPessoaOriginal().getCodigo() : contratoTitular.getPessoa().getCodigo();
                ClienteVO clienteTitular = clienteDAO.consultarPorCodigoPessoa(codigoPessoaTitular, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                ClienteVO clienteDependente;
                if (contratoParaCessao.getMatriculaCederContrato() != null) {
                    clienteDependente = clienteDAO.consultarPorCodigoMatriculaExterna(Integer.parseInt(contratoParaCessao.getMatriculaCederContrato()), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                } else {
                    clienteDependente = clienteDAO.consultarPorNomeCliente(contratoParaCessao.getNomeCederContrato(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }

                if (UteisValidacao.emptyNumber(clienteDependente.getCodigo())) {
                    String msg = "Não realizou dependência do contrato %s. Dependente não encontrado. Observação importada: %s";
                    String msgFormatada = String.format(msg, contratoParaCessao.getCodigoContrato(), contratoParaCessao.getObservacaoImportada());
                    listSemSucesso.add(msgFormatada);
                    continue;
                }

                ContratoVO contratoVODependente = contratoDAO.consultarContratoTransferido(clienteDependente.getPessoa().getCodigo(), clienteTitular.getMatricula(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                boolean ehDependentePlanoCompartilhado = false;
                if (!UteisValidacao.emptyNumber(clienteDependente.getTitularPlanoCompartilhado())) {
                    String msg = "Dependente plano em dobro: %s %s com %s %s";
                    String msgFormatada = String.format(msg, clienteDependente.getMatricula(), clienteDependente.getNome_Apresentar(), clienteTitular.getMatricula(), clienteTitular.getNome_Apresentar());
                    listSucesso.add(msgFormatada);

                    ehDependentePlanoCompartilhado = true;
                }

                if (contratoVODependente.getValorFinal() > 0.0) {
                    String msg = "Não alterar dependência do cliente (Valor do contrato > R$ 0): %s - Titular: %s";
                    listSemSucesso.add(String.format(msg, clienteDependente.getNome_Apresentar(), clienteTitular.getNome_Apresentar()));
                    continue;
                }

                List<TransacaoVO> transacoesContrato = transacaoDAO.consultarPorPessoa(clienteDependente.getPessoa().getCodigo());
                if (!transacoesContrato.isEmpty()) {
                    String msg = "Não alterar dependência do cliente (dependente possui transações): %s - Titular: %s";
                    listSemSucesso.add(String.format(msg, clienteDependente.getNome_Apresentar(), clienteTitular.getNome_Apresentar()));
                    continue;
                }

                boolean permiteCompartilhamento = contratoTitular.getPlano().getCodigo() != 0 && contratoTitular.getPlano().getQuantidadeCompartilhamentos() >= 1;
                if (!permiteCompartilhamento) {
                    String msg = "Não alterar dependência do cliente (Plano atual %s): %s - Titular: %s";
                    listSemSucesso.add(String.format(msg, contratoVODependente.getPlano().getDescricao(), clienteDependente.getNome_Apresentar(), clienteTitular.getNome_Apresentar()));
                    continue;
                }

                JSONArray suspensoesImportacao = consultarAfastamentosImportacao(contratoVODependente.getCodigo());

                if (!UteisValidacao.emptyNumber(contratoVODependente.getCodigo())) {
                    estornarContrato(contratoVODependente, usuarioAdmin, conZW);
                }

                if (!ehDependentePlanoCompartilhado) {
                    ContratoVO contratoOficial = obterContratoOficialTitular(clienteTitular.getPessoa().getCodigo());
                    if (contratoOficial != null) {
                        gerarContratosDependentes(contratoOficial);
                        Optional<ContratoDependenteVO> contratoDependenteVOOptional = contratoDependenteDAO.consultarProximoContratoDependenteDisponivelPorContrato(contratoOficial.getCodigo());
                        if (!contratoDependenteVOOptional.isPresent()) {
                            String msg = "O contrato dependente não foi encontrato para o contrato titular %d ao tentar vincular os clientes %s %s e %s %s";
                            listAvaliar.add(String.format(msg, contratoParaCessao.getCodigoContrato(), clienteTitular.getMatricula(), clienteTitular.getNome_Apresentar(), clienteDependente.getMatricula(), clienteDependente.getNome_Apresentar()));
                            continue;
                        }

                        ContratoDependenteVO contratoDependenteVO = contratoDependenteVOOptional.get();

                        if (suspensoesImportacao.length() > 0) {
                            for (int i = 0; i < suspensoesImportacao.length(); i++) {
                                JSONObject jsonSuspensao = suspensoesImportacao.getJSONObject(i);
                                ProcessoIncluirAfastamentoContratoDependenteEngenharia.incluirAfastamento(conZW, jsonSuspensao, contratoVODependente.getCodigo(), usuarioAdmin, justificativaAtestado, justificativaFerias);
                            }
                            contratoDependenteVO.setDataInicio(contratoVODependente.getVigenciaDe());
                            contratoDependenteVO.setDataFinal(contratoVODependente.getVigenciaAteAjustada());
                            contratoDependenteVO.setDataFinalAjustada(contratoVODependente.getVigenciaAteAjustada());
                            contratoDependenteDAO.alterar(contratoDependenteVO);
                        }

                        System.out.printf("\nIncluir dependencia entre: titular: %s %s e dependente: %s %s ", clienteTitular.getMatricula(), clienteTitular.getNome_Apresentar(), clienteDependente.getMatricula(), clienteDependente.getNome_Apresentar());

                        FamiliarVO familiarVO = familiarDAO.consultarPorDependentePlanoCompartilhado(clienteDependente.getCodigo());
                        if (familiarVO.getCodigo() == 0) {
                            familiarVO.setCliente(clienteTitular.getCodigo());

                            familiarVO.setFamiliar(clienteDependente.getCodigo());
                            familiarVO.setNome(clienteDependente.getNome_Apresentar());
                            familiarVO.setCodAcesso(clienteDependente.getCodAcesso());
                            familiarVO.setParentesco(parentescoTitular);

                            familiarVO.setCompartilharPlano(true);
                            familiarVO.setContratoCompartilhado(contratoOficial.getCodigo());
                            familiarVO.setContratoDependente(contratoDependenteVO);
                            familiarVO.getContratoDependente().setCliente(clienteDependente);
                            familiarDAO.incluir(familiarVO, true);
                            String msg = "Foi incluída a dependência do contrato %d entre os clientes %s %s e %s %s";
                            listSucesso.add(String.format(msg, contratoParaCessao.getCodigoContrato(), clienteTitular.getMatricula(), clienteTitular.getNome_Apresentar(), clienteDependente.getMatricula(), clienteDependente.getNome_Apresentar()));
                        }

                    } else {
                        String msg = "Não foi incluída a dependência do contrato %d entre os clientes %s %s e %s %s";
                        listAvaliar.add(String.format(msg, contratoParaCessao.getCodigoContrato(), clienteTitular.getMatricula(), clienteTitular.getNome_Apresentar(), clienteDependente.getMatricula(), clienteDependente.getNome_Apresentar()));
                        continue;
                    }
                }

                if (contratoTitular.getValorFinal() > 0.0) {
                    continue;
                }
                estornarContrato(contratoTitular, usuarioAdmin, conZW);
            } catch (Exception ex) {
                listErros.add(ex.getMessage());
                ex.printStackTrace();
            }
        }


        Uteis.logarDebug("### SUCESSO ###");
        listSucesso.forEach(Uteis::logarDebug);

        Uteis.logarDebug("### NÃO VINCULADOS ###");
        listSemSucesso.forEach(Uteis::logarDebug);

        Uteis.logarDebug("### AVALIAR ###");
        listAvaliar.forEach(Uteis::logarDebug);

        Uteis.logarDebug(String.format("Total de dependentes analisados: %s\n" +
                        "Vinculados com sucesso: %s\n" +
                        "Vinculados ignorados: %s\n" +
                        "Vinculos para avaliar: %s\n",
                qtdTotal, listSucesso.size(), listSemSucesso.size(), listAvaliar.size()));

    }

    private ContratoVO obterContratoOficialTitular(Integer codigoPessoaTitular) throws Exception {
        List<ContratoVO> contratosTitular = contratoDAO.consultarContratosVigentesPorPessoaOuPessoaOriginal(codigoPessoaTitular, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        ContratoVO contratoTitular = contratosTitular.stream().filter(it -> it.getPlano().getQuantidadeCompartilhamentos() > 0 && it.getValorFinal() > 0.0).findAny().orElse(null);
        ContratoVO contratoDependente = contratosTitular.stream().filter(it -> it.getObservacao().contains("Transferido para") && it.getValorFinal().equals(0.0)).findAny().orElse(null);

        if (contratoTitular == null && contratoDependente != null && contratosTitular.size() == 1) {
            // tentar identificar possivel contrato titullar cancelado
            String sql = "select con.codigo from contrato con \n" +
                    "inner join plano pla on pla.codigo = con.plano \n" +
                    "where con.pessoa = " + codigoPessoaTitular + " \n" +
                    "and con.observacao ilike '%dobro%' \n" +
                    "and coalesce(pla.quantidadecompartilhamentos, 0) > 0 \n" +
                    "and con.situacao = 'CA' \n" +
                    "and '" + Uteis.getDataJDBC(contratoDependente.getVigenciaDe()) + "' BETWEEN con.vigenciade AND con.vigenciaateajustada \n";
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conZW);
            if (rs.next()) {
                contratoTitular = contratoDAO.consultarPorChavePrimaria(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
        }

        return contratoTitular;
    }

    private JSONArray consultarAfastamentosImportacao(Integer codigo) throws Exception {
        String sql = "select importacao_suspensoes from contrato where coalesce(importacao_suspensoes, '') <> '' and codigo = " + codigo;
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conZW);
        if (rs.next()) {
            return new JSONArray(rs.getString("importacao_suspensoes"));
        } else {
            return new JSONArray();
        }
    }

    private void gerarContratosDependentes(ContratoVO contratoVO) throws Exception {
        Integer quantidadeContratoDependente = contratoDependenteDAO.quantidadeContratoDependente(contratoVO.getCodigo(), false);
        if (quantidadeContratoDependente.equals(0)) {
            contratoDependenteDAO.incluir(contratoVO);
        }
    }

    public void criarColunasIdsExternos() {
        try {
            SuperFacadeJDBC.executarConsulta("alter table contratodependente add column idexterno integer", conZW);
            SuperFacadeJDBC.executarConsulta("alter table afastamentocontratodependente add column idexterno integer", conZW);
        } catch (Exception ignore) {}
    }

    private void estornarContrato(ContratoVO contratoVO, UsuarioVO usuarioAdmin, Connection conZW) throws Exception {
        ClienteVO cliente = null;
        contratoVO.setUsuarioVO(usuarioAdmin);

        List<ReciboPagamentoVO> listaReciboPagamento = reciboPagamentoDAO.consularReciboPagamParcelaPorCodigoContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!listaReciboPagamento.isEmpty()) {
            contratoVO.setMovParcelaVOs(new ArrayList<>());
            contratoVO.setListaEstornoRecibo(new ArrayList<>());
            for (ReciboPagamentoVO recibo : listaReciboPagamento) {
                EstornoReciboVO estornoRecibo = new EstornoReciboVO();
                estornoRecibo.setReciboPagamentoVO(recibo);
                estornoRecibo.setListaMovPagamento(movPagamentoDAO.consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR));
                estornoRecibo.setListaMovParcela(movParcelaDAO.consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS));

                //transações de cartão de crédito
                contratoVO.montarListaTransacoes(estornoRecibo.getListaMovParcela(), conZW);
                contratoVO.getListaEstornoRecibo().add(estornoRecibo);
            }

        } else {
            contratoVO.setMovParcelaVOs(movParcelaDAO.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

            //transações de cartão de crédito
            contratoVO.montarListaTransacoes(contratoVO.getMovParcelaVOs(), conZW);
        }

        contratoVO.setPrecisaEstornarTransacoes(false);

        contratoVO.montarListaItensRemessa(movParcelaDAO.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA), conZW);


        try {
            cliente = clienteDAO.consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            contratoDAO.estornoContrato(contratoVO, cliente, null, null);
            zwFacadeDAO.atualizarSintetico(cliente, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
            Uteis.logar(null, "Estornado com sucesso o contrato -> " + contratoVO.getCodigo() + " do cliente -> " + cliente.getCodigo());
        } catch (Exception e) {
            Uteis.logar(null, "Erro ao estornar contrato -> " + contratoVO.getCodigo() + " do cliente -> " + cliente.getCodigo());
            Uteis.logar(null, "Exceção -> " + e.getMessage());
        }

    }

    private void inicializar() throws Exception {
        DAO dao = new DAO();
        conZW = dao.obterConexaoEspecifica(key);
        Conexao.guardarConexaoForJ2SE(conZW);

        reciboPagamentoDAO = new ReciboPagamento(conZW);
        movPagamentoDAO = new MovPagamento(conZW);
        movParcelaDAO = new MovParcela(conZW);
        clienteDAO = new Cliente(conZW);
        contratoDAO = new Contrato(conZW);
        contratoDependenteDAO = new ContratoDependente(conZW);
        zwFacadeDAO = new ZillyonWebFacade(conZW);
        familiarDAO = new Familiar(conZW);
        parentescoDAO = new Parentesco(conZW);
        transacaoDAO = new Transacao(conZW);
        usuarioDAO = new Usuario(conZW);
    }

    public List<ContratoParaCessao> consultarContratosParaDependencia(String matriculas) throws SQLException {
        String sql = "select importacao_observacao,\n" +
                "   c.codigo,\n" +
                "   c.pessoa,\n" +
                "   c.pessoaoriginal\n" +
                "from contrato c\n" +
                "inner join plano pl on c.plano = pl.codigo\n" +
                "inner join cliente cli on cli.pessoa = c.pessoa\n" +
                "where c.importacao_observacao ilike 'Transferido para%'\n" +
                "   and c.observacao ilike '%DOBRO%' \n" +
                "   and coalesce(pl.quantidadecompartilhamentos, 0) > 0 \n" +
                "   and c.valorfinal > 0\n" +
                "   and c.vigenciaateajustada > now()\n";
        if (!UteisValidacao.emptyString(matriculas)) {
            sql += "  and cli.codigomatricula in (" + matriculas + ") \n";
        }
        List<ContratoParaCessao> listaContratos = new ArrayList<>();
        try (PreparedStatement ps = conZW.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    ContratoParaCessao contratoCeder = new ContratoParaCessao();

                    String observacoes = rs.getString("importacao_observacao");

                    String replaceObs = observacoes.replace("Transferido para: ", "");
                    String[] partObs = replaceObs.split(" - ");
                    String matriculaExternaClienteCessao = null;
                    String nomeClienteCessao = null;
                    if (partObs.length > 1) {
                        matriculaExternaClienteCessao = partObs[0].replace(" ", "");
                    } else {
                        nomeClienteCessao = partObs[0];
                    }

                    contratoCeder.setCodigoContrato(rs.getInt("codigo"));
                    contratoCeder.setMatriculaCederContrato(matriculaExternaClienteCessao);
                    contratoCeder.setNomeCederContrato(nomeClienteCessao);
                    contratoCeder.setObservacaoImportada(observacoes);

                    listaContratos.add(contratoCeder);
                }
            }
        }
        return listaContratos;
    }

    private void reabrirContratosTransferidosCancelados() throws Exception {
        String sqlContratosCedidosCancelados = "select * from contrato c\n" +
                "   left join contratooperacao co on c.codigo = co.contrato and co.tipooperacao = 'CA' and co.dataoperacao > '2023-04-15'\n" +
                "where importacao_dt_fim_original > now()\n" +
                "and situacao = 'CA'\n" +
                "and importacao_observacao like 'Transferido para%'\n" +
                "and co.codigo is null;";

        List<ContratoVO> contratosCedidos;
        try (PreparedStatement ps = conZW.prepareStatement(sqlContratosCedidosCancelados)) {
            try (ResultSet rs = ps.executeQuery()) {
                contratosCedidos = Contrato.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, conZW);
            }
        }

        String updateContrato = "UPDATE contrato\n" +
                "SET vigenciaateajustada = vigenciaate, situacao = 'AT'\n" +
                "WHERE codigo = %d";

        String updatePeriodoAcesso = "UPDATE periodoacessocliente p\n" +
                "SET datafinalacesso = '%s', pessoa = %d\n" +
                "WHERE contrato = %d";

        String deleteHistoricoCancelado = "DELETE FROM historicocontrato h\n" +
                "WHERE contrato = %d and tipohistorico = 'CA';";

        String updateHistoricoMatricula = "UPDATE historicocontrato h\n" +
                "SET datafinalsituacao = '%s'\n" +
                "where contrato = %d and tipohistorico = 'MA';";

        String deleteContratoOperacao = "DELETE FROM contratooperacao c\n" +
                "WHERE contrato = %d AND tipooperacao = 'CA';";

        String infoContratoReaberto = "Contrato %d reaberto;";

        for (ContratoVO contratoCedido : contratosCedidos) {
            SuperFacadeJDBC.executarConsultaUpdate(String.format(updateContrato,
                    contratoCedido.getCodigo()), conZW);

            SuperFacadeJDBC.executarConsultaUpdate(String.format(updatePeriodoAcesso,
                    Uteis.getDataJDBC(contratoCedido.getVigenciaAte()),
                    contratoCedido.getPessoa().getCodigo(),
                    contratoCedido.getCodigo()), conZW);

            SuperFacadeJDBC.executarConsultaUpdate(String.format(deleteHistoricoCancelado,
                    contratoCedido.getCodigo()), conZW);

            SuperFacadeJDBC.executarConsultaUpdate(String.format(updateHistoricoMatricula,
                    Uteis.getDataJDBC(contratoCedido.getVigenciaAte()),
                    contratoCedido.getCodigo()), conZW);

            SuperFacadeJDBC.executarConsultaUpdate(String.format(deleteContratoOperacao,
                    contratoCedido.getCodigo()), conZW);

            Uteis.logarDebug(String.format(infoContratoReaberto,
                    contratoCedido.getCodigo()));

            ClienteVO clienteVO = new ClienteVO();
            clienteVO.setPessoa(contratoCedido.getPessoa());
            zwFacadeDAO.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
        }

    }



    public void corrigirVinculoFamiliarTitularDependente() throws Exception {
        parentescoDAO = new Parentesco(conZW);
        clienteDAO = new Cliente(conZW);
        familiarDAO = new Familiar(conZW);

        ParentescoVO parentescoTitular = parentescoDAO.obterParentescoCriandoSeNaoExiste("DEPENDENTE");

        String sql = "select \n" +
                "\tclit.codigo as clientetitular,\n" +
                "\tpt.nome as nometitular,\n" +
                "\tct.codigo as contratotitular,\n" +
                "\tcd.codigo as contratodependente,\n" +
                "\tcd.datainicio,\n" +
                "\tcd.datafinalajustada,\n" +
                "\tclid.codigo  as clientedependente,\n" +
                "\tpd.nome as nomedependente \n" +
                "from contratodependente cd\n" +
                "\tinner join contrato ct on ct.codigo = cd.contrato \n" +
                "\tinner join pessoa pt on pt.codigo = ct.pessoa \n" +
                "\tinner join cliente clit on clit.pessoa = pt.codigo \n" +
                "\tleft join cliente clid on clid.codigo = cd.cliente \n" +
                "\tleft join pessoa pd on pd.codigo = clid.pessoa \n" +
                "where 1 = 1\n" +
                "and ct.situacao = 'AT'\n" +
                "and coalesce(cd.cliente,0) <> 0\n" +
                "and (not exists (select f.codigo from familiar f where f.cliente = clit.codigo and f.familiar = clid.codigo and f.compartilharplano is true))\n";

        sql +=  "order by clit.codigo, ct.codigo \n";

        Integer total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql", conZW);
        int count = 0;
        Integer sucesso = 0;
        Integer falha = 0;

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), conZW);
        while (rs.next()) {
            try {
                conZW.setAutoCommit(false);

                Integer contratoTitular = rs.getInt("contratotitular");
                Integer codigoClienteTitular = rs.getInt("clientetitular");

                ClienteVO clienteDependente = clienteDAO.consultarPorChavePrimaria(rs.getInt("clientedependente"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                System.out.println(++count + "\\" + total + " - Ajustando vinculo familar aluno titular: " + rs.getString("nometitular") + " e dependente: " + rs.getString("nomedependente"));

                SuperFacadeJDBC.executarUpdate("delete from familiar where cliente = " + codigoClienteTitular + " and familiar = " + clienteDependente.getCodigo(), conZW);
                SuperFacadeJDBC.executarUpdate("delete from familiar where cliente = " + clienteDependente.getCodigo() + " and familiar = " + codigoClienteTitular, conZW);

                FamiliarVO familiarVO = new FamiliarVO();
                familiarVO.setCliente(codigoClienteTitular);

                familiarVO.setFamiliar(clienteDependente.getCodigo());
                familiarVO.setNome(clienteDependente.getNome_Apresentar());
                familiarVO.setCodAcesso(clienteDependente.getCodAcesso());
                familiarVO.setParentesco(parentescoTitular);

                familiarVO.setCompartilharPlano(true);
                familiarVO.setContratoCompartilhado(contratoTitular);
                familiarVO.setContratoDependente(new ContratoDependenteVO());
                familiarVO.getContratoDependente().setCodigo(rs.getInt("contratodependente"));
                familiarVO.getContratoDependente().setCliente(clienteDependente);
                familiarDAO.incluir(familiarVO, true);

                conZW.commit();
                sucesso++;
            } catch (Exception ex) {
                falha++;
                conZW.rollback();
                conZW.setAutoCommit(true);
                Uteis.logarDebug("ERRO: " + ex.getMessage());
            } finally {
                conZW.setAutoCommit(true);
            }
        }
        Uteis.logarDebug("--- TOTAL | " + total);
        Uteis.logarDebug("--- TOTAL | SUCESSO | " + sucesso);
        Uteis.logarDebug("--- TOTAL | FALHA   | " + falha);
    }

    public void corrigirDependenteSemTitularVinculado() throws Exception {
        familiarDAO = new Familiar(conZW);
        contratoDependenteDAO = new ContratoDependente(conZW);
        clienteDAO = new Cliente(conZW);

        String sql = "select \n" +
                "\tcli_d.codigo as codigoclientedependente,\n" +
                "\tcli_d.codigomatricula,\n" +
                "\tpes_d.nome as nomedependente,\n" +
                "\tcli_t.codigo as codigoclientetitular,\n" +
                "\tpes_t.nome as nometitular,\n" +
                "\tcd.codigo as contratodependente,\n" +
                "\tcd.contrato as contratotitular,\n" +
                "\tcd.datafinalajustada,\n" +
                "\tf.codigo as familiar\n" +
                "from contratodependente cd\n" +
                "inner join contrato con on con.codigo = cd.contrato\n" +
                "inner join cliente cli_d on cli_d.codigo = cd.cliente \n" +
                "inner join pessoa pes_d on pes_d.codigo = cli_d.pessoa \n" +
                "inner join pessoa pes_t on pes_t.codigo = con.pessoa \n" +
                "inner join cliente cli_t on cli_t.pessoa = con.pessoa \n" +
                "inner join familiar f on f.cliente = cli_t.codigo and f.familiar = cli_d.codigo and f.compartilharplano is true\n" +
                "where cd.cliente is not null\n" +
                "and cd.datafinalajustada >= current_date\n" +
                "and coalesce(cli_d.titularplanocompartilhado,0) = 0\n";

        Integer total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql", conZW);
        int count = 0;
        Integer sucesso = 0;
        Integer falha = 0;

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), conZW);
        while (rs.next()) {
            try {
                conZW.setAutoCommit(false);

                System.out.println(++count + "\\" + total + " - Ajustando vinculo cliente dependente: " + rs.getString("nomedependente") + " e titular: " + rs.getString("nometitular"));

                clienteDAO.preencherTitularPlanoCompartilhado(rs.getInt("codigoclientetitular"), rs.getInt("codigoclientedependente"));

                ClienteVO clienteDependente = clienteDAO.consultarPorChavePrimaria(rs.getInt("codigoclientedependente"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ZillyonWebFacade zillyonWebFacadeDAO = new ZillyonWebFacade(conZW);
                zillyonWebFacadeDAO.atualizarSintetico(clienteDependente, Calendario.hoje(),
                        SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                zillyonWebFacadeDAO = null;

                conZW.commit();
                sucesso++;
            } catch (Exception ex) {
                falha++;
                conZW.rollback();
                conZW.setAutoCommit(true);
                Uteis.logarDebug("ERRO: " + ex.getMessage());
            } finally {
                conZW.setAutoCommit(true);
            }
        }
        Uteis.logarDebug("--- TOTAL | " + total);
        Uteis.logarDebug("--- TOTAL | SUCESSO | " + sucesso);
        Uteis.logarDebug("--- TOTAL | FALHA   | " + falha);
    }

    public void finalizarDependenciasErradasImportacao() throws Exception {
        Cliente clienteDAO = new Cliente(conZW);
        String sql = "select \n" +
                " con.codigo,\n" +
                " con.importacao_observacao,\n" +
                " cli.codigo as clientetitular,\n" +
                " pes.nome as nometitular,\n" +
                " cd.cliente as clientedependente,\n" +
                " pes_d.nome as nomedependente\n" +
                "from contrato con\n" +
                "\tinner join pessoa pes on pes.codigo = con.pessoa \n" +
                "\tinner join cliente cli on cli.pessoa = pes.codigo\n" +
                "\tinner join plano pla on pla.codigo = con.plano\n" +
                "\tinner join contratodependente cd on cd.contrato = con.codigo\n" +
                "\tleft join cliente cli_d on cli_d.codigo = cd.cliente\n" +
                "\tleft join pessoa pes_d on pes_d.codigo = cli_d.pessoa\n" +
                "where 1 = 1\n" +
                "and con.vigenciaateajustada > current_date\n" +
                "and con.valorfinal > 0\n" +
                "and pla.quantidadecompartilhamentos > 0\n" +
                "and con.observacao  ilike '%DOBRO%'\n" +
                "and con.importacao_observacao ilike '%transferido para:%'\n" +
                "and coalesce(cd.cliente) > 0 ";

        int count = 0;
        int total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql", conZW);

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conZW);
        while(rs.next()) {
            Uteis.logarDebug(++count + "\\" + total + " - Verificando contrato: " + rs.getInt("codigo"));

            if (isContratoTransferidoParaDependenteAtual(rs.getInt("clientedependente"), rs.getString("importacao_observacao"))) {
                String msg = String.format("\tFinalizando dependencia entre o titular: %s e dependente %s pois o contrato deve ser cedido, obs_importacao: %s", rs.getString("nometitular"), rs.getString("nomedependente"), rs.getString("importacao_observacao"));
                Uteis.logarDebug(msg);
                clienteDAO.finalizarDependenciaPlanoCompartilhado(rs.getInt("clientedependente"), true);

                Integer codClienteTitular = rs.getInt("clientetitular");
                Integer codClienteDependente = rs.getInt("clientedependente");
                SuperFacadeJDBC.executarConsulta("delete from familiar where cliente = " + codClienteTitular + " and familiar = " + codClienteDependente, conZW);
                SuperFacadeJDBC.executarConsulta("delete from familiar where cliente = " + codClienteDependente + " and familiar = " + codClienteTitular, conZW);
            }
        }
    }

    public void corrigirCompartilhamentosImportacaoEvo() throws Exception {
        finalizarDependenciasErradasImportacao();
        gerarContratoDependentePlanoEmDobroImportado();

        String sqlFamiliares = "select array_to_string(array(\n" +
                "select f.codigo from cliente ct\n" +
                "\tinner join familiar f on f.codigo = (select max(f2.codigo) from familiar f2 where f2.cliente = ct.codigo)\n" +
                "\tinner join cliente cli_d on cli_d.codigo = f.familiar \n" +
                "\tinner join pessoa pes_d on pes_d.codigo = cli_d.pessoa\n" +
                "\tinner join pessoa pes on pes.codigo = ct.pessoa \n" +
                "\tinner join contrato con on coalesce(con.pessoaoriginal,con.pessoa) = ct.pessoa \n" +
                "\tinner join plano pla on pla.codigo = con.plano \n" +
                "\tinner join contratodependente cd_disponivel on cd_disponivel.codigo = (select cd_sub.codigo from contratodependente cd_sub where cd_sub.contrato = con.codigo and coalesce(cd_sub.cliente,0) = 0 order by cd_sub.posicaodependente limit 1)\n" +
                "left join contratodependente cd on cd.contrato = con.codigo and cd.cliente = f.familiar \n" +
                "where 1 = 1 \n" +
                "and coalesce(con.id_externo,0) > 0 \n" +
                "and con.vigenciaateajustada > current_date\n" +
                "and pla.quantidadecompartilhamentos > 0\n" +
                "and f.compartilharplano is false),',') as codigos;";
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlFamiliares, conZW);
        if (rs.next()) {
            if (!UteisValidacao.emptyString(rs.getString("codigos"))) {
                SuperFacadeJDBC.executarUpdate("update familiar set compartilharplano = true where codigo in (" + rs.getString("codigos") + ");", conZW);
            }
        }

        corrigirDependentesInativosComCompartilhamentoAtivo();
    }

    public void gerarContratoDependentePlanoEmDobroImportado() throws Exception {
        Contrato contratoDAO = new Contrato(conZW);
        String sql = "select distinct c.codigo from contrato c \n" +
                "inner join plano pl on c.plano = pl.codigo  \n" +
                "left join contratodependente cd on cd.contrato = c.codigo \n" +
                "where pl.quantidadecompartilhamentos > 0 \n" +
                "and c.vigenciaateajustada > now() \n" +
                "and cd.codigo is null";

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conZW);
        System.out.println("Verificando e corrigindo contratos titular sem contratos dependentes...");
        while (rs.next()) {
            ContratoVO contratoVO = contratoDAO.consultarPorCodigo(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            gerarContratosDependentes(contratoVO);
        }
    }

    public void corrigirDependentesInativosComCompartilhamentoAtivo() throws Exception {
        familiarDAO = new Familiar(conZW);
        contratoDependenteDAO = new ContratoDependente(conZW);
        UsuarioVO usuarioAdmin = usuarioDAO.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);

        String sql = "select \n" +
                "\tct.codigomatricula,\n" +
                "\tpes.nome as nometitular,\n" +
                "\tpes_d.nome as nomedependente,\n" +
                "\tf.familiar,\n" +
                "\tcon.codigo as contratotitular,\n" +
                "\tcon.importacao_observacao,\n" +
                "\tcd_disponivel.codigo as contratodependente_disponivel\n" +
                "from cliente ct\n" +
                "\tinner join familiar f on f.cliente = ct.codigo\n" +
                "\tinner join cliente cli_d on cli_d.codigo = f.familiar \n" +
                "\tinner join pessoa pes_d on pes_d.codigo = cli_d.pessoa\n" +
                "\tinner join pessoa pes on pes.codigo = ct.pessoa \n" +
                "\tinner join contrato con on coalesce(con.pessoaoriginal,con.pessoa) = ct.pessoa \n" +
                "\tinner join plano pla on pla.codigo = con.plano \n" +
                "\tinner join contratodependente cd_disponivel on cd_disponivel.codigo = (select cd_sub.codigo from contratodependente cd_sub where cd_sub.contrato = con.codigo and coalesce(cd_sub.cliente,0) = 0 order by cd_sub.posicaodependente limit 1)\n" +
                "\tleft join contratodependente cd on cd.contrato = con.codigo and cd.cliente = f.familiar \n" +
                "where 1 = 1 \n" +
                "and con.vigenciaateajustada > current_date\n" +
                "and pla.quantidadecompartilhamentos > 0\n" +
                "and f.compartilharplano is true\n" +
                "and cd.codigo is null\n";

        Integer total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql", conZW);
        int count = 0;
        Integer sucesso = 0;
        Integer falha = 0;

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), conZW);
        while (rs.next()) {
            if (isContratoTransferidoParaDependenteAtual(rs.getInt("familiar"), rs.getString("importacao_observacao"))
                    && contratoDependenteDAO.existeDependenciaVigente(rs.getInt("familiar"))) {
                ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(rs.getInt("contratotitular"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                Uteis.logarDebug("\tEstorndo contrato: " + contratoVO.getCodigo());
                estornarContrato(contratoVO, usuarioAdmin, conZW);
            } else {
                try {
                    conZW.setAutoCommit(false);
                    Uteis.logarDebug(++count + "\\" + total + " - Verificando dependencia entre o titular: " + rs.getString("nometitular") + " e dependente: " + rs.getString("nomedependente"));

                    Uteis.logarDebug(++count + "\\" + total + " - Ajustando dependencia entre o titular: " + rs.getString("nometitular") + " e dependente: " + rs.getString("nomedependente"));
                    FamiliarVO familiarVO = familiarDAO.consultarPorDependentePlanoCompartilhado(rs.getInt("familiar"));
                    familiarVO.setContratoCompartilhado(rs.getInt("contratotitular"));
                    ContratoDependenteVO contratoDependenteVO = contratoDependenteDAO.findByCodigo(rs.getInt("contratodependente_disponivel")).get();
                    contratoDependenteVO.getCliente().setCodigo(rs.getInt("familiar"));
                    familiarVO.setContratoDependente(contratoDependenteVO);
                    familiarDAO.alterar(familiarVO);

                    conZW.commit();
                    sucesso++;
                } catch (Exception ex) {
                    falha++;
                    ex.printStackTrace();
                    Uteis.logarDebug("ERRO: " + ex.getMessage());
                    conZW.rollback();
                    conZW.setAutoCommit(true);
                } finally {
                    conZW.setAutoCommit(true);
                }
            }
        }
        Uteis.logarDebug("--- TOTAL | " + total);
        Uteis.logarDebug("--- TOTAL | SUCESSO | " + sucesso);
        Uteis.logarDebug("--- TOTAL | FALHA   | " + falha);
    }

    private boolean isContratoTransferidoParaDependenteAtual(Integer codigoClienteDependenteAtual, String contratoImportacaoObservacao) throws Exception {
        if (UteisValidacao.emptyString(contratoImportacaoObservacao)) {
            return false;
        }
        String replaceObs = contratoImportacaoObservacao.replace("Transferido para: ", "");
        String[] partObs = replaceObs.split(" - ");
        Integer matriculaExternaClienteCessao = null;
        String nomeClienteCessao = null;
        if (partObs.length > 1) {
            String m = partObs[0].replace(" ", "");
            matriculaExternaClienteCessao = m.matches("\\d+") ? Integer.parseInt(m) : null;
        } else {
            nomeClienteCessao = partObs[0];
        }

        if (matriculaExternaClienteCessao == null && nomeClienteCessao == null) {
            return false;
        }

        ClienteVO clienteVODependenteAtual = clienteDAO.consultarPorChavePrimaria(codigoClienteDependenteAtual, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        ClienteVO clienteVOObsContratoTransferencia;
        if (matriculaExternaClienteCessao != null) {
            clienteVOObsContratoTransferencia = clienteDAO.consultarPorCodigoMatriculaExterna(matriculaExternaClienteCessao, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        } else {
            clienteVOObsContratoTransferencia = clienteDAO.consultarPorNomeCliente(nomeClienteCessao, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        return !UteisValidacao.emptyNumber(clienteVOObsContratoTransferencia.getCodigo())
                && clienteVODependenteAtual.getCodigo().equals(clienteVOObsContratoTransferencia.getCodigo());
    }

    public void ajustarVigenciaContratoDependente() throws Exception {
        clienteDAO = new Cliente(conZW);

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("\tpes.nome,\n");
        sql.append("\tcd.codigo,\n");
        sql.append("\tcd.contrato,\n");
        sql.append("\tcd.cliente,\n");
        sql.append("\tcd.datainicio,\n");
        sql.append("\tcd.datafinalajustada\n");
        sql.append("from contratodependente cd\n");
        sql.append("\tinner join contrato con on con.codigo = cd.contrato \n");
        sql.append("\tinner join pessoa pes on pes.codigo = con.pessoa \n");
        sql.append("where 1=1 \n");
        sql.append("and cd.datainicio::date != con.vigenciade::date \n");
        sql.append("and cd.datainicio > current_date \n");

        Integer total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql", conZW);
        int count = 0;
        Integer sucesso = 0;
        Integer falha = 0;

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), conZW);
        while (rs.next()) {
            try {
                conZW.setAutoCommit(false);
                Uteis.logarDebug(++count + "\\" + total + " - Ajustando vigência do contrato dependente: " + rs.getString("codigo") + " contrato titular: " + rs.getString("contrato"));

                SuperFacadeJDBC.executarConsulta("update contratodependente cd set datainicio = con.vigenciade, datafinal = con.vigenciaate, datafinalajustada = con.vigenciaateajustada from contrato con \n" +
                        "where cd.contrato = con.codigo \n" +
                        "and cd.codigo = " + rs.getInt("codigo"), conZW);

                if (!UteisValidacao.emptyNumber(rs.getInt("cliente"))) {
                    ClienteVO clienteDependente = clienteDAO.consultarPorChavePrimaria(rs.getInt("cliente"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                    ZillyonWebFacade zillyonWebFacadeDAO = new ZillyonWebFacade(conZW);
                    zillyonWebFacadeDAO.atualizarSintetico(clienteDependente, Calendario.hoje(),
                            SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                }

                conZW.commit();
                sucesso++;
            } catch (Exception ex) {
                falha++;
                conZW.rollback();
                conZW.setAutoCommit(true);
                Uteis.logarDebug("ERRO: " + ex.getMessage());
            } finally {
                conZW.setAutoCommit(true);
            }
        }
        Uteis.logarDebug("--- TOTAL | " + total);
        Uteis.logarDebug("--- TOTAL | SUCESSO | " + sucesso);
        Uteis.logarDebug("--- TOTAL | FALHA   | " + falha);
    }

    public void ajustarVigenciaFinalContratoDependente() throws Exception {
        clienteDAO = new Cliente(conZW);

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("\tpes.nome,\n");
        sql.append("\tcd.codigo,\n");
        sql.append("\tcd.contrato,\n");
        sql.append("\tcd.cliente,\n");
        sql.append("\tcd.datainicio,\n");
        sql.append("\tcd.datafinalajustada\n");
        sql.append("from contratodependente cd\n");
        sql.append("\tinner join contrato con on con.codigo = cd.contrato \n");
        sql.append("\tinner join pessoa pes on pes.codigo = con.pessoa \n");
        sql.append("where 1=1 \n");
        sql.append("and (cd.datafinal::date != con.vigenciaate::date or cd.datafinalajustada::date != con.vigenciaateajustada::date)\n");
        sql.append("and con.vigenciaateajustada > current_date \n");
        sql.append("and not exists(select 1 from afastamentocontratodependente acd where acd.contratodependente = cd.codigo) \n");

        Integer total = SuperFacadeJDBC.contar("select count(sql.*) from (" + sql + ") as sql", conZW);
        int count = 0;
        Integer sucesso = 0;
        Integer falha = 0;

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), conZW);
        while (rs.next()) {
            try {
                conZW.setAutoCommit(false);
                Uteis.logarDebug(++count + "\\" + total + " - Ajustando vigência final do contrato dependente: " + rs.getString("codigo") + " contrato titular: " + rs.getString("contrato"));

                SuperFacadeJDBC.executarConsulta("update contratodependente cd set datafinal = con.vigenciaate, datafinalajustada = con.vigenciaateajustada from contrato con \n" +
                        "where cd.contrato = con.codigo \n" +
                        "and cd.codigo = " + rs.getInt("codigo"), conZW);

                if (!UteisValidacao.emptyNumber(rs.getInt("cliente"))) {
                    ClienteVO clienteDependente = clienteDAO.consultarPorChavePrimaria(rs.getInt("cliente"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                    ZillyonWebFacade zillyonWebFacadeDAO = new ZillyonWebFacade(conZW);
                    zillyonWebFacadeDAO.atualizarSintetico(clienteDependente, Calendario.hoje(),
                            SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                }

                conZW.commit();
                sucesso++;
            } catch (Exception ex) {
                falha++;
                conZW.rollback();
                conZW.setAutoCommit(true);
                Uteis.logarDebug("ERRO: " + ex.getMessage());
            } finally {
                conZW.setAutoCommit(true);
            }
        }
        Uteis.logarDebug("--- TOTAL | " + total);
        Uteis.logarDebug("--- TOTAL | SUCESSO | " + sucesso);
        Uteis.logarDebug("--- TOTAL | FALHA   | " + falha);
    }

}

class ContratoParaCessao {

    private Integer codigoContrato;
    private String matriculaCederContrato;
    private String nomeCederContrato;
    private String observacaoImportada;

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public String getMatriculaCederContrato() {
        return matriculaCederContrato;
    }

    public void setMatriculaCederContrato(String matriculaCederContrato) {
        this.matriculaCederContrato = matriculaCederContrato;
    }

    public String getNomeCederContrato() {
        return nomeCederContrato;
    }

    public void setNomeCederContrato(String nomeCederContrato) {
        this.nomeCederContrato = nomeCederContrato;
    }

    public String getObservacaoImportada() {
        return observacaoImportada;
    }

    public void setObservacaoImportada(String observacaoImportada) {
        this.observacaoImportada = observacaoImportada;
    }
}
