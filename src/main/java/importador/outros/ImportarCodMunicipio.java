/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package importador.outros;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import importador.LeitorExcel;
import org.apache.poi.hssf.usermodel.HSSFRow;

/**
 *
 * <AUTHOR>
 */
public class ImportarCodMunicipio {
    
    public static Connection con;
    

    public static void lerArquivo() throws Exception {
        List<HSSFRow> linhas = LeitorExcel.lerLinhas("D:\\munic.xls");
        for (HSSFRow linha : linhas) {
            PreparedStatement ps = con.prepareStatement("UPDATE cidade SET codigomunicipio = ? where nome like ?");
            ps.setString(1, String.valueOf(LeitorExcel.obterNumero(linha, 1).intValue()).substring(2));
//            System.out.println(String.valueOf(LeitorExcel.obterNumero(linha, 1).intValue()).substring(2));
            ps.setString(2, LeitorExcel.obterString(linha, 2).toUpperCase());
            ps.execute();
        }
    }

    public static void main(String... args) {
        try {
            List<HSSFRow> linhas = LeitorExcel.lerLinhas("D:\\munic.xls");
        for (HSSFRow linha : linhas) {
            System.out.println(String.valueOf(LeitorExcel.obterNumero(linha, 1).intValue()).substring(2));
        }
        } catch (Exception ex) {
            Logger.getLogger(ImportarCodMunicipio.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
}
