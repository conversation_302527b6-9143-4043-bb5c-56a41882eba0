package importador.cliente;


import importador.ImportacaoCache;
import importador.ImportacaoConfigTO;
import importador.UteisImportacao;
import importador.json.ClienteImportacaoJSON;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ClienteObservacao;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.basico.Vinculo;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.Connection;
import java.sql.ResultSet;

public class ImportarCliente {

    private Connection con;
    private ImportacaoConfigTO configTO;

    public ImportarCliente(ImportacaoConfigTO configTO, Connection con) {
        this.con = con;
        this.configTO = configTO;
    }

    public ClienteVO importarCliente(ClienteImportacaoJSON json, ConfigExcelImportacaoTO configExcelImportacaoTO, ImportacaoCache cache) throws Exception {
        try {
            con.setAutoCommit(false);

            Uteis.logar(true, null, "INICIA IMPORTAÇÃO | CLIENTE... " + json.getIdExterno() + " | " + json.getNome().toUpperCase());

            if (UteisValidacao.emptyNumber(json.getIdExterno())) {
                throw new Exception("Necessário que o cliente tenha um IdExterno");
            }

            ResultSet rsExiste = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM cliente WHERE coalesce(idExterno,matriculaexterna) = " + json.getIdExterno()
                    + " and empresa = " + json.getEmpresa(), con);
            if (rsExiste.next()) {
                throw new Exception("Já existe um CLIENTE importado com o idExterno " + json.getIdExterno() + " na empresa " + json.getEmpresa());
            }

            UteisImportacao uteisImportacao = new UteisImportacao();

            if (configExcelImportacaoTO.isValidarCpfCnpjJaCadastrado() && !json.getCpf().equals("")) {
                ResultSet rs = SuperFacadeJDBC.criarConsulta("select nome, cfp from pessoa where (cfp = '" + json.getCpf_Formatado() + "' or cfp = '" + json.getCpf_SomenteNumeros() + "') ", con);
                if (rs.next()) {
                    String nome = rs.getString("nome");
                    throw new Exception("CPF (" + json.getCpf_Formatado() + " já está cadastrado para o cliente \"" + nome + "\".");
                }
            }

            EstadoVO estadoVO = new EstadoVO();
            CidadeVO cidadeVO = new CidadeVO();
            PaisVO paisVO = new PaisVO();

            String ufCliente = json.getUf();
            if (!UteisValidacao.emptyString(ufCliente)) {
                estadoVO = cache.obterEstadoVO(ufCliente);
                if (estadoVO != null && !UteisValidacao.emptyNumber(estadoVO.getCodigo())) {
                    if (!UteisValidacao.emptyNumber(estadoVO.getPais())) {
                        paisVO.setCodigo(estadoVO.getPais());
                    }
                    cidadeVO = uteisImportacao.obterCidade(json.getCidade(), estadoVO, con);
                }
            }

            if (cidadeVO != null && !UteisValidacao.emptyNumber(cidadeVO.getCodigo())) {
                estadoVO = cidadeVO.getEstado();
                paisVO = cidadeVO.getPais();
            }

            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setIdExterno(json.getIdExterno());
            pessoaVO.setNome(json.getNome().toUpperCase());
            pessoaVO.setCfp(json.getCpf_Formatado());
            pessoaVO.setDataCadastro(json.getDataCadastro());
            pessoaVO.setDataNasc(uteisImportacao.obterDataNascimento(json.getDataNascimento()));
            pessoaVO.setSexo(json.getSexo());
            pessoaVO.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());
            pessoaVO.setNacionalidade(json.getNacionalidade());
            pessoaVO.setNaturalidade(json.getNaturalidade());
            pessoaVO.setRg(json.getRg());
            pessoaVO.setRgOrgao(json.getRgOrgao());
            pessoaVO.setPais(paisVO);
            pessoaVO.setEstadoVO(estadoVO);
            pessoaVO.setCidade(cidadeVO);
            pessoaVO.setNomeMae(json.getNomeMae());
            pessoaVO.setCpfMae(json.getCpfMae());
            pessoaVO.setNomePai(json.getNomePai());
            pessoaVO.setCpfPai(json.getCpfPai());
            pessoaVO.setEstadoCivil(json.getEstadoCivil());
            pessoaVO.setGrauInstrucao(uteisImportacao.obterGrauInstrucao(json.getGrauInstrucao(), con));
            pessoaVO.setProfissao(uteisImportacao.obterProfissao(json.getProfissao(), con));
            pessoaVO.setEnderecoVOs(uteisImportacao.obterListaEnderecoVO(json.getEnderecos()));
            pessoaVO.setTelefoneVOs(uteisImportacao.obterListaTelefonesVO(json.getTelefones(), configExcelImportacaoTO.getPadraoDDD()));
            pessoaVO.setEmailVOs(uteisImportacao.obterListaEmailVO(json.getEmails()));

            //incluir pessoa
            Pessoa pessoaDAO = new Pessoa(con);
            pessoaDAO.incluirPessoaImportacao(pessoaVO);
            pessoaDAO = null;
            if (UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                throw new Exception("Pessoa não foi incluida!");
            }

            ClienteVO clienteVO = new ClienteVO();
            clienteVO.setIdExterno(json.getIdExterno());
            clienteVO.setPessoa(pessoaVO);
            clienteVO.setEmpresa(cache.obterEmpresaVO(json.getEmpresa()));
            clienteVO.setEmpresaFornecedor(uteisImportacao.obterEmpresaFornecedor(json.getEmpresaFornecedor(), con));
            clienteVO.setCategoria(uteisImportacao.obterCategoria(json.getCategoria(), con));
            clienteVO.setNecessidadesEspeciaisSesiCe(uteisImportacao.obterNecessidadesEspeciais(json.getNecessidadeEspecial(), con));
            clienteVO.setVinculoVOs(uteisImportacao.obterVinculosVO(json, cache, con));
            clienteVO.setMatriculaExterna(json.getMatriculaExterna());
            uteisImportacao = null;

            //incluir cliente
            Cliente clienteDAO = new Cliente(con);
            clienteDAO.gerarNumeroMatricula(clienteVO, clienteVO.getEmpresa(), null);
            clienteDAO.incluirClienteImportacao(clienteVO);
            clienteDAO = null;

            //incluir vinculos
            if (!UteisValidacao.emptyList(clienteVO.getVinculoVOs())) {
                Vinculo vinculoDAO = new Vinculo(con);
                vinculoDAO.incluirVinculo(clienteVO.getCodigo(), clienteVO.getVinculoVOs(), "CLIENTE", null, false);
                vinculoDAO = null;
            }

            //observação
            if (!UteisValidacao.emptyString(json.getObservacao())) {
                UsuarioVO usuarioVO = cache.obterUsuarioVO(json.getUsuario());

                ClienteObservacaoVO observacaoVO = new ClienteObservacaoVO();
                observacaoVO.setClienteVO(clienteVO);
                observacaoVO.setDataCadastro(Calendario.hoje());
                observacaoVO.setUsuarioVO(usuarioVO);
                observacaoVO.setObservacao(json.getObservacao());
                ClienteObservacao clienteObservacaoDAO = new ClienteObservacao(con);
                clienteObservacaoDAO.incluir(observacaoVO);
                clienteObservacaoDAO = null;
            }

            Log logDao = new Log(con);
            logDao.incluirLogItemImportacao("CLIENTE", clienteVO.getCodigo(), cache.getUsuarioVOImportacao(), clienteVO.getPessoa().getCodigo());
            logDao = null;

            ZillyonWebFacade zwFacadeDAO = new ZillyonWebFacade(con);
            zwFacadeDAO.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CLIENTE, false);
            zwFacadeDAO = null;

            if (!UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                json.setSucesso(true);
                json.setCodigo(clienteVO.getCodigo());
                json.setMsgRetorno("Cliente importado com sucesso.");
            } else {
                throw new Exception("Cliente não importado.");
            }
            con.commit();
            return clienteVO;
        } catch (Exception ex) {
            json.setSucesso(false);
            json.setCodigo(null);
            json.setMsgRetorno(ex.getMessage());
            ex.printStackTrace();
            con.rollback();
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }
    public Connection getCon() {
        return con;
    }

    public void setCon(Connection con) {
        this.con = con;
    }

    public ImportacaoConfigTO getConfigTO() {
        return configTO;
    }

    public void setConfigTO(ImportacaoConfigTO configTO) {
        this.configTO = configTO;
    }
}
