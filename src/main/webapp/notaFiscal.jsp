<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<link href="./beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/pure-forms.css" type="text/css" rel="stylesheet"/>
<script src="script/packJQueryPlugins.min.js" type="text/javascript"></script>
<script src="script/time_1.3.js" type="text/javascript"></script>
<script src="script/tooltipster/jquery.tooltipster.min.js" type="text/javascript"></script>
<link href="./bootstrap/bootplus.css" rel="stylesheet">
<link href="./css/otimize.css" rel="stylesheet">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>

<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>

<script>
    jQuery.noConflict();
</script>
<body class="paginaFontResponsiva">
<f:view>
    <c:if test="${SuperControle.menuZwUi}">
        <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    </c:if>
    <script>
        function carregarTooltipsterNotaFiscal() {
            carregarTooltipNota(jQuery('.tooltipster'));
        }

        function carregarTooltipNota(el) {
            el.tooltipster({
                theme: 'tooltipster-light',
                position: 'bottom',
                animation: 'grow',
                contentAsHTML: true
            });
        }
        function copiar(elcopy,titulo) {
            var el = document.createElement('textarea');
            el.value = elcopy;
            document.body.appendChild(el);
            el.select();
            document.execCommand('copy');
            document.body.removeChild(el);
            Notifier.info(titulo + ' copiada para a área de transferência.');
        }
    </script>

    <style>
        <%--  Correções de compatibilidade de css do menu novo e menu antigo. Estes problemas são causados pelo pure-form que é utilizado na tela de nota fiscal --%>
        .campo-busca-zw-ui {
            background-color: #fff !important;
            width: 100% !important;
            color: #51555a !important;
            outline: unset !important;
            padding: unset !important;
            border: none !important;
            font-size: 14px !important;
            font-family: 'Nunito Sans', sans-serif;
            font-weight: 400 !important;
            min-height: 26px !important;
            background-image: none !important;
            border-bottom: none !important;
            border: none !important;
            box-shadow: none !important;
            width: 100% !important;
        }
        .panelInferiorNotaFiscal {
            border-top: 1px solid #ccc;
            padding-top: 20px;
            width: 100%;
            display: inline-flex;
            margin-top: 10px;
        }

        .botaoFecharFiltro {
            font-size: 14px;
            color: #094771 !important;
            font-family: Arial;
            font-weight: bold;
            text-decoration: none;
            writing-mode: vertical-rl;
            height: 100%;
            padding: 10px;
            padding-bottom: 35px;
            transform: rotate(180deg);
        }

        .panelFiltroNotaFiscal {
            text-align: center;
        }

        .panelFiltroTexto {
            background-color: #ccc;
            line-height: 0;
            height: 100%;
            text-align: right;
        }

        .pageContentZwUi {
            margin: 30px 30px 30px 30px !important;
            width: calc(100% - 50px) !important
        }

        .botoes.nvoBt {

        }
    </style>

    <h:form id="form" styleClass="pure-form">
        <a4j:keepAlive beanName="ExportadorListaControle"/>
        <jsp:include page="include_head.jsp" flush="true"/>

        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <c:if test="${!SuperControle.menuZwUi}">
                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="include_topo_novo.jsp" flush="true"/>
                    <jsp:include page="include_menu_notaFiscal_flat.jsp" flush="true"/>
                    <rich:jQuery query="addClass('menuItemAtual')" selector=".item7"/>
                </h:panelGroup>
            </c:if>

            <c:if test="${SuperControle.menuZwUi}">
                <h:panelGroup layout="block" styleClass="bgtop topoZW tudo" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="include_topo_novo.jsp" flush="true"/>
                    <rich:jQuery selector=".item7" query="addClass('menuItemAtual')"/>
                </h:panelGroup>
            </c:if>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel" style="display: inline-flex;">
                        <c:if test="${SuperControle.menuZwUi}">
                            <jsp:include page="include_box_menulateral.jsp" flush="true">
                                <jsp:param name="menu" value="NOTAS-INICIO"/>
                            </jsp:include>
                        </c:if>
                        <jsp:include page="include_box_menuLateral_notaFiscal.jsp"/>
                        <c:if test="${!SuperControle.menuZwUi}">
                            <h:panelGroup layout="block" id="panelFiltroNotaFiscal"
                                          styleClass="panelFiltroNotaFiscal">
                                <h:panelGroup layout="block" id="panelFiltroTexto" styleClass="panelFiltroTexto">
                                    <a4j:commandLink
                                            id="botaoFecharFiltro"
                                            styleClass="botaoFecharFiltro"
                                            status="false"
                                            ajaxSingle="true"
                                            oncomplete="abrirFecharFiltro()">
                                        <i class="fa-icon-filter" style="transform: rotate(90deg);"></i> Filtros
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>
                        </c:if>
                        <div style="display: inline-block;height: 100%; width: 100%"
                             class="container-imagem container-conteudo-central divCentralNotaFiscal">
                            <h:panelGroup layout="block" styleClass="container-conteudo-central">
                                <h:panelGroup layout="block" styleClass="container-box form-flat"
                                              style="margin: 30px 30px 30px 30px !important; width: calc(100% - 50px) !important;">
                                    <h:panelGroup styleClass="container-box-header" layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box" id="panelBtnSuperior">
                                            <h:outputText value="Notas Fiscais" styleClass="container-header-titulo"/>
                                            <a4j:commandLink id="consultarSolicitacoes"
                                                             action="#{NotaFiscalControle.exibirSolicitacoes}"
                                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                             oncomplete="#{NotaFiscalControle.onComplete}#{NotaFiscalControle.mensagemNotificar}"
                                                             reRender="modalSolicitacoes"
                                                             value="Solicitações Download"
                                                             styleClass="botoes nvoBt"
                                                             style="float: right; line-height: 1; margin-top: 15px;"/>
                                            <a4j:commandLink id="consultarEnotasSuperior"
                                                             onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                                             action="#{NotaFiscalControle.consultarNotasFiscais}"
                                                             oncomplete="#{NotaFiscalControle.mensagemNotificar}"
                                                             reRender="form:panelENotas, form:grupoMenuLateralNotaFiscal, form:panelBtnSuperior"
                                                             style="float: right; line-height: 1; margin-top: 15px;"
                                                             styleClass="botoes nvoBt btSec">
                                                <i class="fa-icon-refresh"></i> Atualizar
                                            </a4j:commandLink>
                                            <a4j:commandLink
                                                    id="sincronizarTodasNotasComEnotasTop"
                                                             style="float: right; line-height: 1; margin-top: 15px; margin-right: 15px;"
                                                             styleClass="botoes nvoBt btSec tooltipster"
                                                             action="#{NotaFiscalControle.sincronizarTodasNotasComEnotas}"
                                                             oncomplete="#{NotaFiscalControle.mensagemNotificar};#{NotaFiscalControle.onComplete};addPlaceHolderNotaFiscal()"
                                                             value="Sincronizar Todas Notas"
                                                             title="Consulta e sincroniza todas as notas filtradas com o eNotas"/>
                                            <h:outputText value="#{NotaFiscalControle.dataCertificado_Apresentar}"
                                                          style="float: right; line-height: 1; margin-top: 22px;"
                                                          styleClass="container-header-titulo"/>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <h:panelGroup id="panelENotas" layout="block" styleClass="margin-box"
                                                  style="margin-top: 0px; margin-left: 0px; margin-right: 0px; width: 100%; min-height: calc(100vh - 220px);">
                                        <jsp:include page="include_grid_notaFiscal.jsp"/>
                                        <h:panelGroup layout="block"
                                                      id="panelInferiorNotaFiscal"
                                                      styleClass="panelInferiorNotaFiscal"
                                                      rendered="#{!empty NotaFiscalControle.listaNotas}">
                                            <jsp:include page="include_totalizadores_notaFiscal.jsp"/>
                                            <jsp:include page="include_btnAcoesInferior_notaFiscal.jsp"/>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </h:panelGroup>
                        </div>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>
        </body>
        </html>
    </h:form>
    <jsp:include page="includes/include_expiracao_alerta.jsp" flush="true">
        <jsp:param name="apresentarModal" value="${SuporteControle.apresentarMensagemNFe}"/>
    </jsp:include>
    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
    <%@include file="includes/include_modal_notaFiscal.jsp" %>
    <rich:modalPanel id="modalSolicitacoes" autosized="true" shadowOpacity="true" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText id="lblModalSolicitacoes" value="Solicitações de download"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelinkModalSolicitacoes"/>
                <rich:componentControl for="modalSolicitacoes" attachTo="hidelinkModalSolicitacoes" operation="hide"  event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formSolicitacoes">

            <h:panelGroup>
                <rich:dataTable value="#{NotaFiscalControle.listaSolicitacaoVOS}"
                                rows="10"
                                id="tblSolicitacoes"
                                rowKeyVar="status"
                                var="item">

                    <%@include file="pages/ce/includes/include_contador_richtable.jsp" %>
                    <rich:column sortBy="#{item.codigo}" >
                        <f:facet name="header">
                            <h:outputText value="Código"/>
                        </f:facet>
                        <h:outputText value="#{item.codigo}"/>
                    </rich:column>

                    <rich:column sortBy="#{item.dataLancamento}" >
                        <f:facet name="header">
                            <h:outputText value="Dt.Lanc."/>
                        </f:facet>
                        <div  style="width:88px;">
                            <h:outputText value="#{item.dataLancamento}">
                                <f:convertDateTime pattern="dd/MM/yyyy HH:mm"/>
                            </h:outputText>
                        </div>
                    </rich:column>

                    <rich:column sortBy="#{item.usuarioSolicitante.nomeAbreviado}" >
                        <f:facet name="header">
                            <h:outputText value="Usuário"/>
                        </f:facet>
                        <h:outputText value="#{item.usuarioSolicitante.nomeAbreviado}"/>
                    </rich:column>

                    <rich:column sortBy="#{item.tipo.descricao}">
                        <f:facet name="header">
                            <h:outputText value="Tipo"/>
                        </f:facet>
                        <div  style="width:300px;">
                            <h:outputText value="#{item.tipo.descricao}"/>
                        </div>
                    </rich:column>

                    <rich:column sortBy="#{item.status.descricao}">
                        <f:facet name="header">
                            <h:outputText value="Status"/>
                        </f:facet>
                        <div  style="width:300px;">
                            <h:outputText value="#{item.status.descricao}"/>
                            <h:outputText rendered="#{item.apresentarErro}" styleClass="tooltipster"
                                          title="#{item.mensagemErroProcessamento}"
                                          value="">
                                <i class="fa-icon-info-circle"></i>
                            </h:outputText>
                        </div>
                    </rich:column>

                    <rich:column sortBy="#{item.dataProcessamento}" >
                        <f:facet name="header">
                            <h:outputText value="Dt. processamento"/>
                        </f:facet>
                        <h:outputText rendered="#{item.dataProcessamento != null}" value="#{item.dataProcessamento}">
                            <f:convertDateTime pattern="dd/MM/yyyy HH:mm"/>
                        </h:outputText>
                        <h:outputText rendered="#{item.dataProcessamento == null}" value=""/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Opções"/>
                        </f:facet>

                        <h:outputLink styleClass="fa-icon-download texto-size-18 linkAzul tooltipster"
                                      rendered="#{item.apresentarLink}"
                                      title="Download ZIP das notas"
                                      value="#{item.linkArquivoNotas}"
                                      target="_blank"/>

                    </rich:column>
                    <f:facet name="footer">
                        <rich:datascroller for="tblSolicitacoes"/>
                    </f:facet>
                </rich:dataTable>
                <h:panelGroup layout="block" styleClass="container-botoes" style="margin-top: 25px;">
                        <a4j:commandLink id="atualizarSolicitacoes"
                                     action="#{NotaFiscalControle.exibirSolicitacoes}"
                                     onclick="fireElementFromAnyParent('form:btnAtualizaTempo')"
                                     oncomplete="#{NotaFiscalControle.onComplete}#{NotaFiscalControle.mensagemNotificar}"
                                     reRender="modalSolicitacoes"
                                     value="Atualizar Solicitações"
                                     styleClass="botaoPrimario texto-size-16"/>
                </h:panelGroup>

            </h:panelGroup>

        </a4j:form>
    </rich:modalPanel>
</f:view>

<script type="text/javascript">

    carregarTooltipsterNotaFiscal();
    addPlaceHolderNotaFiscal();

    function abrirFecharFiltro() {
        jQuery('.grupoMenuLateralNotaFiscal').hide();
        jQuery('.menuLateralNotaFiscal').toggle("slide");
        jQuery('.grupoMenuLateralNotaFiscal').show();
    }

    function addPlaceHolderNotaFiscal() {
        try {
            document.getElementById("formCance:justificativaCancelarSel").setAttribute("placeholder", "Justificativa");
        } catch (err) {
        }

        try {
            document.getElementById("formInu:justificativaInutilizarSel").setAttribute("placeholder", "Justificativa");
        } catch (err) {
        }

        try {
            document.getElementById("formInu:justificativaInutilizarSelFaixa").setAttribute("placeholder", "Justificativa");
        } catch (err) {
        }

        try {
            document.getElementById("formInu:numInicialFaixa").setAttribute("placeholder", "Num. Inicial");
        } catch (err) {
        }

        try {
            document.getElementById("formInu:numFinalFaixa").setAttribute("placeholder", "Num. Final");
        } catch (err) {
        }

        try {
            document.getElementById("formInuti:justificativaInutilizar").setAttribute("placeholder", "Justificativa");
        } catch (err) {
        }

        try {
            document.getElementById("formCan:justificativa").setAttribute("placeholder", "Justificativa");
        } catch (err) {
        }

        try {
            document.getElementById("formEmail:emailEnviar").setAttribute("placeholder", "E-mail");
        } catch (err) {
        }
    }
</script>
