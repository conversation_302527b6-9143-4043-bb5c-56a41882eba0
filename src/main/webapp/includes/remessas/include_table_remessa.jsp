<%-- 
    Document   : include_table_remessa
    Created on : 22/11/2012, 18:39:12
    Author     : Waller
--%>
<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../imports.jsp" %>
<a4j:outputPanel id="outputPnlRemessas">
    <style>
        .rich-fileupload-list-decor{
            border:none;
            padding: 0 0 0 0;
            margin: 0 0 0 0;
        }

        .rich-fileupload-toolbar-decor{
            background-color: transparent;
            border:none;
        }

        .rich-fileupload-table-td{
            border: none;
        }

        .rich-panel{
            background-color: transparent;
            border:none;
            padding: 0 0 0 0;
            margin: 0 0 0 0;
        }

        .rich-panel-body {
            padding: 0 0 0 0;
            margin: 0 0 0 0;
        }

        .rich-fileupload-ico-add {
            background-image: url(images/drive-upload.png);
        }

        .rich-fileupload-button{
            background-color: transparent;
            background-image: none;
        }

        .rich-fileupload-button-border{
            border: none;
        }

        .rich-fileupload-button-light, .rich-fileupload-button-press{
            background-image: none;
            background-color: transparent;
        }

        .remessa-padrao {
            background: #AADFB9;
        }

        .remessa-cancelamento {
            background: #F4C9D1;
        }
    </style>
    <h:panelGrid width="100%" columns="2">
        <h:panelGrid columns="6" style="text-align: left;">
            <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-configurar-retentativa-de-cobranca-para-retornos-especificos-de-remessas/"
                          title="Clique e saiba mais: Códigos de Erros de Remessas" target="_blank">
                <h:graphicImage styleClass="linkWiki" style="margin-left:5px;margin-right:2px;" url="/imagens/wiki_link2.gif"/>
                <h:outputText value="Lista de Códigos de Erros de Remessas"/>
            </h:outputLink>
            <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-configurar-retentativa-de-cobranca-para-retornos-especificos-de-remessas/"
                          style="margin-left: 20px;"
                          title="Clique e saiba mais: Códigos de Erros de Remessas" target="_blank">
                <h:graphicImage styleClass="linkWiki" style="margin-left:5px;margin-right:2px;" url="/imagens/wiki_link2.gif"/>
                <h:outputText value="Lista de Códigos de Erros de GetNet"/>
            </h:outputLink>
            <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-configurar-retentativa-de-cobranca-para-retornos-especificos-de-remessas/"
                          style="margin-left: 20px;"
                          title="Clique e saiba mais: Códigos de Erros de Remessas" target="_blank">
                <h:graphicImage styleClass="linkWiki" style="margin-left:5px;margin-right:2px;" url="/imagens/wiki_link2.gif"/>
                <h:outputText value="Lista de Códigos de Erros de BIN"/>
            </h:outputLink>

            <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-acompanho-as-cobrancas-em-cartao-de-credito-enviadas-por-remessa-dos-meus-alunos-dcc/"
                          style="margin-left: 20px;"
                          title="Clique e saiba mais: Contato com as adquirentes" target="_blank">
                <h:graphicImage styleClass="linkWiki" style="margin-left:5px;margin-right:2px;" url="/imagens/wiki_link2.gif"/>
                <h:outputText value="Lista de Contatos"/>
            </h:outputLink>

            <h:outputLink
                    value="#{SuperControle.urlWiki}Operacional:Remessas#Gerencial"
                    title="Clique e saiba mais: Códigos de Erros de Remessas" target="_blank"
                    rendered="#{!empty GestaoRemessasControle.remessaVO && GestaoRemessasControle.remessaVO.codigo != 0 && !GestaoRemessasControle.itau}">
                <h:graphicImage styleClass="linkWiki"
                                style="margin-left: 20px;"
                                url="/imagens/wiki_link2.gif"/>
                <h:outputText value="Gestão do Débito Cartão de Credito (DCC)"/>
            </h:outputLink>
            <h:panelGroup layout="block" rendered="#{GestaoRemessasControle.convenio.boleto}">
                <rich:spacer height="20"/>
                <a4j:commandLink action="#{GestaoRemessasControle.agruparRemessas}" rendered="#{!GestaoRemessasControle.agruparRemessas}" value="Agrupar Remessas por Dia" reRender="outputPnlRemessas"/>
                <a4j:commandLink action="#{GestaoRemessasControle.desagruparRemessas}" rendered="#{GestaoRemessasControle.agruparRemessas}" value="Desagrupar Remessas por Dia" reRender="outputPnlRemessas"/>
            </h:panelGroup>
        </h:panelGrid>
        <h:panelGrid style="text-align: right;" width="100%">
            <h:panelGroup>
                <a4j:commandLink id="exportarExcel"
                                 style="margin-left: 8px;"
                                 actionListener="#{ExportadorListaControle.exportar}"
                                 rendered="#{not empty GestaoRemessasControle.listaRemessas}"
                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                 accesskey="2" styleClass="linkPadrao">
                    <f:attribute name="lista" value="#{GestaoRemessasControle.listaRemessas}"/>
                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="atributos" value="codigo=C.,sequencialArquivoExibicao=Seq,dataRegistro=Data de Registro,situacaoRemessa.descricao=Status Remessa,qtdRegistros=Qtd,valorBruto=R$ Br,valorAceito=R$ Ac,valorLiquido=R$ Liq,dataPrevistaCredito=Dt.Cred.,dataRetorno=Dt.Caixa,usuario.nomeAbreviado=U.Rem.,usuarioRetorno=U.Ret."/>
                    <f:attribute name="prefixo" value="Remessas"/>
                    <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGrid>
    </h:panelGrid>

    <h:panelGroup rendered="#{GestaoRemessasControle.agruparRemessas}">
        <a4j:repeat value="#{GestaoRemessasControle.agrupamentoRemessas}" var="agrupamento">
            <rich:panel>
                <f:facet name="header">
                    <h:panelGrid columns="3" columnClasses="esquerda, direita, direita" width="100%">
                        <h:outputText value="#{agrupamento.diaAgrupado}" style="font-weight:bold" styleClass="text">
                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                        </h:outputText>
                        <h:panelGroup>
                            <h:outputText value=" Total Itens: " style="font-weight:bold" styleClass="text"/>
                            <h:outputText value="#{agrupamento.qtdItens}" styleClass="text"/>
                            <h:outputText value=" Total Bruto: " style="font-weight:bold" styleClass="text"/>
                            <h:outputText value="#{agrupamento.valorBruto}" styleClass="text">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <h:outputText value=" Total Aceito: " style="font-weight:bold" styleClass="text"/>
                            <h:outputText value="#{agrupamento.valorAceito}" styleClass="text">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                            <h:outputText value=" Total Líquido: " style="font-weight:bold" styleClass="text"/>
                            <h:outputText value="#{agrupamento.valorLiquido}" styleClass="text">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:panelGroup>
                        <h:panelGroup>
                            <a4j:commandButton actionListener="#{GestaoRemessasControle.downloadAgrupadorRemessa}"
                                               image="images/download_file.png"
                                               styleClass="rich-fileupload-button rich-fileupload-font"
                                               reRender="panelDadosRemessa"
                                               onmouseover="this.className='rich-fileupload-button-light'"
                                               onmouseout="this.className='rich-fileupload-font'"
                                               oncomplete="#{GestaoRemessasControle.msgAlertAuxiliar}"
                                               title="Download Arquivo Remessa">
                                <f:attribute name="agrupamento" value="#{agrupamento}"/>
                            </a4j:commandButton>
                        </h:panelGroup>
                    </h:panelGrid>
                </f:facet>
                <rich:dataTable rowClasses="linhaPar,linhaImpar" rowKeyVar="status" id="tblDetalheAgrupamento"
                                width="100%"
                                columnClasses="colunaCentralizada" value="#{agrupamento.remessas}" var="remessa">
                    <f:facet name="header">
                        <h:outputText value="Remessas de Cobrança"/>
                    </f:facet>

                    <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>

                    <rich:column sortBy="#{remessa.codigo}">
                        <f:facet name="header">
                            <h:outputText title="Código Remessa" value="C."/>
                        </f:facet>
                        <h:outputText value="#{remessa.codigo}"/>
                    </rich:column>
                    <rich:column sortBy="#{remessa.sequencialArquivoExibicao}">
                        <f:facet name="header">
                            <h:outputText title="Sequencial do Arquivo" value="Seq"/>
                        </f:facet>
                        <h:outputText value="#{remessa.sequencialArquivoExibicao}"/>
                    </rich:column>

                    <rich:column sortBy="#{remessa.dataRegistro}">
                        <f:facet name="header">
                            <h:outputText value="Data de Registro"/>
                        </f:facet>
                        <h:outputText style="font-size: 9px;" title="Data Geração Remessa"
                                      value="#{remessa.dataRegistro}">
                            <f:convertDateTime timeStyle="medium" pattern="dd/MM/yyyy HH:mm:ss"/>
                        </h:outputText>
                    </rich:column>

                    <rich:column sortBy="#{remessa.situacaoRemessa.descricao}" styleClass="#{remessa.classRemessa}">
                        <f:facet name="header">
                            <h:outputText title="Status Remessa" value="St."/>
                        </f:facet>
                        <h:panelGrid columnClasses="colunaCentralizada" width="100%">
                            <h:column>
                                <rich:spacer style="cursor:pointer;vertical-align:middle;
                                 width: 8px; height: 8px;
                                 background-color:#{remessa.situacaoRemessa.cor}"
                                             width="8" height="8"
                                             title="#{remessa.situacaoRemessa.hint}">
                                </rich:spacer>
                            </h:column>
                        </h:panelGrid>
                    </rich:column>

                    <rich:column sortBy="#{remessa.qtdRegistros}">
                        <f:facet name="header">
                            <h:outputText title="Número de Itens da Remessa" value="Qtd"/>
                        </f:facet>
                        <h:outputText value="#{remessa.qtdRegistros}"/>
                    </rich:column>

                    <rich:column sortBy="#{remessa.valorBruto}">
                        <f:facet name="header">
                            <h:outputText title="Valor Bruto da Remessa" value="R$ Br"/>
                        </f:facet>
                        <h:outputText value="#{remessa.valorBruto_Apresentar}"/>
                    </rich:column>

                    <rich:column sortBy="#{remessa.valorAceito}">
                        <f:facet name="header">
                            <h:outputText title="Valor Aceito (Aprovado)" value="R$ Ac"/>
                        </f:facet>
                        <h:outputText
                                style="color:#{remessa.valorAceito < remessa.valorBruto && remessa.situacaoRemessa.id == 2 ? '#FF5555' : ''};"
                                value="#{remessa.valorAceito_Apresentar}"/>
                    </rich:column>

                    <rich:column sortBy="#{remessa.valorLiquido}">
                        <f:facet name="header">
                            <h:outputText title="Valor Líquido" value="R$ Liq"/>
                        </f:facet>
                        <h:outputText value="#{remessa.valorLiquido_Apresentar}"/>
                    </rich:column>

                    <rich:column rendered="#{SuperControle.widthScreenClient > 1024}"
                                 sortBy="#{remessa.dataPrevistaCredito}">
                        <f:facet name="header">
                            <h:outputText title="Data Prevista para Crédito no Banco" value="Dt.Cred."/>
                        </f:facet>
                        <h:outputText style="font-size: 9px;" value="#{remessa.dataPrevistaCredito}">
                            <f:convertDateTime timeStyle="medium" pattern="dd/MM/yyyy"/>
                        </h:outputText>
                    </rich:column>

                    <rich:column rendered="#{SuperControle.widthScreenClient > 1024}" sortBy="#{remessa.dataRetorno}">
                        <f:facet name="header">
                            <h:outputText title="Data no fechamento de Caixa" value="Dt.Caixa"/>
                        </f:facet>
                        <h:outputText style="font-size: 9px;" value="#{remessa.dataRetorno}">
                            <f:convertDateTime timeStyle="medium" pattern="dd/MM/yyyy"/>
                        </h:outputText>
                    </rich:column>

                    <rich:column rendered="#{SuperControle.widthScreenClient > 1024}"
                                 sortBy="#{remessa.usuario.nomeAbreviado}">
                        <f:facet name="header">
                            <h:outputText title="Usuário criou Remessa" value="U.Rem."/>
                        </f:facet>
                        <h:outputText value="#{remessa.usuario.nomeAbreviado}"/>
                    </rich:column>

                    <rich:column rendered="#{SuperControle.widthScreenClient > 1024}"
                                 sortBy="#{remessa.usuarioRetorno}">
                        <f:facet name="header">
                            <h:outputText title="Usuário processou o Retorno" value="U.Ret."/>
                        </f:facet>
                        <h:outputText value="#{remessa.usuarioRetorno}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText title="Registro Header e Trailer" value="?"/>
                        </f:facet>
                        <a4j:commandLink reRender="panelDadosParametros" title="Visualizar Header e Trailer"
                                         actionListener="#{GestaoRemessasControle.exibirParams}" value="?">
                            <f:attribute name="params" value="header"/>
                            <f:attribute name="remessa" value="#{remessa}"/>
                        </a4j:commandLink>
                    </rich:column>

                    <rich:column rendered="#{GestaoRemessasControle.exibirControles}">
                        <f:facet name="header">
                            <h:outputText title="Arquivo de Remessa" value="Rem."/>
                        </f:facet>

                        <a4j:commandButton actionListener="#{GestaoRemessasControle.downloadRemessa}"
                                           rendered="#{((!GestaoRemessasControle.envioAutomatico || (remessa.convenioCobranca.tipo.codigo != 8 && remessa.convenioCobranca.tipo.codigo != 2)) && remessa.situacaoRemessa.id != 4) || GestaoRemessasControle.usuarioLogado.username == 'admin'}"
                                           image="images/download_file.png"
                                           styleClass="rich-fileupload-button rich-fileupload-font"
                                           reRender="panelDadosRemessa"
                                           onmouseover="this.className='rich-fileupload-button-light'"
                                           onmouseout="this.className='rich-fileupload-font'"
                                           oncomplete="#{GestaoRemessasControle.msgAlertAuxiliar}"
                                           title="Download Arquivo Remessa">
                            <f:attribute name="remessa" value="#{remessa}"/>
                        </a4j:commandButton>

                        <a4j:commandButton actionListener="#{GestaoRemessasControle.downloadRemessa}"
                                           rendered="#{remessa.getnet}"
                                           image="images/lock.gif"
                                           styleClass="rich-fileupload-button rich-fileupload-font"
                                           reRender="panelDadosRemessa"
                                           onmouseover="this.className='rich-fileupload-button-light'"
                                           onmouseout="this.className='rich-fileupload-font'"
                                           oncomplete="#{GestaoRemessasControle.msgAlertAuxiliar}"
                                           title="Download Arquivo Criptografado Remessa">
                            <f:attribute name="remessa" value="#{remessa}"/>
                            <f:attribute name="criptografado" value="true"/>
                        </a4j:commandButton>
                    </rich:column>

                    <rich:column
                            rendered="#{GestaoRemessasControle.exibirControles && GestaoRemessasControle.usuarioLogado.username == 'admin'}">
                        <f:facet name="header">
                            <h:outputText title="Arquivo de Retorno" value="Ret."/>
                        </f:facet>
                        <h:panelGrid cellpadding="0" cellspacing="0" columnClasses="text,text" columns="2">
                            <rich:panel>
                                <a4j:commandButton actionListener="#{GestaoRemessasControle.downloadRetorno}"
                                                   rendered="#{remessa.temRetorno}"
                                                   image="images/download_file.png"
                                                   reRender="panelDadosRemessa"
                                                   styleClass="rich-fileupload-button rich-fileupload-font"
                                                   onmouseover="this.className='rich-fileupload-button-light'"
                                                   onmouseout="this.className='rich-fileupload-button rich-fileupload-font'"
                                                   oncomplete="#{GestaoRemessasControle.msgAlertAuxiliar}"
                                                   title="Download Arquivo Retorno">
                                    <f:attribute name="retorno" value="#{remessa}"/>
                                </a4j:commandButton>
                            </rich:panel>
                        </h:panelGrid>
                    </rich:column>

                    <rich:column rendered="#{GestaoRemessasControle.exibirControles}">
                        <f:facet name="header">
                            <h:outputText title="Resultado Remessa" value="Result."/>
                        </f:facet>
                        <a4j:commandButton id="itemremessa"
                                           actionListener="#{GestaoRemessasControle.exibirItensRemessa}"
                                           image="images/view-details.png"
                                           styleClass="rich-fileupload-button rich-fileupload-font"
                                           rendered="#{remessa.qtdRegistros <= 1500}"
                                           reRender="panelDadosRemessa, panelDadosRemessaNovo, panelDadosRemessaBoleto"
                                           onmouseover="this.className='rich-fileupload-button-light'"
                                           onmouseout="this.className='rich-fileupload-font'"
                                           title="Exibir Itens da Remessa">
                            <f:attribute name="remessa" value="#{remessa}"/>
                        </a4j:commandButton>

                        <a4j:commandButton id="naoapresentaritemremessa"
                                           image="images/view-details.png"
                                           styleClass="rich-fileupload-button rich-fileupload-font"
                                           rendered="#{remessa.qtdRegistros > 1500}"
                                           reRender="panelDadosRemessa, panelDadosRemessaNovo, panelDadosRemessaBoleto"
                                           onmouseover="this.className='rich-fileupload-button-light'"
                                           onmouseout="this.className='rich-fileupload-font'"
                                           onclick="alert('Para visualizar os itens da remessa, acesse o Gerador de Consultas');"
                                           title="Exibir Itens da Remessa"/>
                    </rich:column>

                    <rich:column rendered="#{GestaoRemessasControle.exibirControles}">
                        <f:facet name="header">
                            <h:outputText title="Imprimir Boleto" value="Imp. Boleto"/>
                        </f:facet>
                        <a4j:commandButton id="imprimirBoletos"
                                           actionListener="#{GestaoRemessasControle.imprimirBoletosRemessa}"
                                           rendered="#{remessa.boleto}"
                                           image="images/imprimir.png"
                                           oncomplete="#{GestaoRemessasControle.msgAlert} #{BoletoBancarioControle.mensagemNotificar} #{GestaoRemessasControle.mensagemNotificar}"
                                           styleClass="rich-fileupload-button rich-fileupload-font"
                                           reRender="panelDadosRemessa, panelDadosRemessaNovo, panelDadosRemessaBoleto"
                                           onmouseover="this.className='rich-fileupload-button-light'"
                                           onmouseout="this.className='rich-fileupload-font'"
                                           title="Imprimir boletos da Remessa">
                            <f:attribute name="remessa" value="#{remessa}"/>
                        </a4j:commandButton>
                    </rich:column>

                </rich:dataTable>
                <rich:datascroller for="tblDetalheAgrupamento" status="false" renderIfSinglePage="false"/>
            </rich:panel>
            <br/>
        </a4j:repeat>
    </h:panelGroup>

    <rich:dataTable rowClasses="linhaPar,linhaImpar" rowKeyVar="status" id="tblRemessas" width="100%" rendered="#{!GestaoRemessasControle.agruparRemessas}"
                    columnClasses="colunaCentralizada" value="#{GestaoRemessasControle.listaRemessas}" var="remessa">
        <f:facet name="header">
            <h:outputText value="Remessas de Cobrança"/>
        </f:facet>

        <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>

        <rich:column sortBy="#{remessa.codigo}">
            <f:facet name="header">
                <h:outputText title="Código Remessa" value="C."/>
            </f:facet>
            <h:outputText id="codigoRemessa"
                          styleClass="tooltipster"
                          value="#{remessa.codigo}"
                          title="#{remessa.informacoesTitle}"/>
        </rich:column>

        <rich:column sortBy="#{remessa.sequencialArquivoExibicao}">
            <f:facet name="header">
                <h:outputText title="Sequencial do Arquivo" value="Seq"/>
            </f:facet>
            <h:outputText value="#{remessa.sequencialArquivoExibicao}"/>
        </rich:column>

        <rich:column sortBy="#{remessa.dataRegistro}">
            <f:facet name="header">
                <h:outputText value="Data Registro"/>
            </f:facet>
            <h:outputText style="font-size: 9px;"
                          styleClass="tooltipster"
                          title="#{remessa.dataRegistroTitle}"
                          value="#{remessa.dataRegistro}">
                <f:convertDateTime timeStyle="medium" pattern="dd/MM/yyyy HH:mm:ss"/>
            </h:outputText>
        </rich:column>

        <rich:column sortBy="#{remessa.situacaoRemessa.descricao}" styleClass="#{remessa.classRemessa}">
            <f:facet name="header">
                <h:outputText title="Status Remessa" value="St."/>
            </f:facet>
            <h:panelGrid columnClasses="colunaCentralizada" width="100%">
                <h:column>
                    <rich:spacer style="cursor:pointer;vertical-align:middle;
                                 width: 10px; height: 10px;
                                 background-color:#{remessa.situacaoRemessa.cor}"
                                 id="situacaoremessa"
                                 width="10"
                                 height="10"
                                 title="#{remessa.situacaoRemessa.hint}">
                    </rich:spacer>
                </h:column>
            </h:panelGrid>
        </rich:column>

        <rich:column sortBy="#{remessa.qtdRegistrosTela}">
            <f:facet name="header">
                <h:outputText id="quantidade" title="Número de Itens da Remessa" value="Qtd"/>
            </f:facet>
            <h:outputText value="#{remessa.qtdRegistrosTela}"/>
        </rich:column>

        <rich:column sortBy="#{remessa.valorBruto}">
            <f:facet name="header">
                <h:outputText title="Valor Bruto da Remessa" value="R$ Br"/>
            </f:facet>
            <h:outputText id="valorRemessa" value="#{remessa.valorBruto_Apresentar}"/>
        </rich:column>

        <rich:column sortBy="#{remessa.valorAceito}">
            <f:facet name="header">
                <h:outputText title="Valor Aceito (Aprovado)" value="R$ Ac"/>
            </f:facet>
            <h:outputText style="color:#{remessa.valorAceito < remessa.valorBruto && remessa.situacaoRemessa.id == 2 ? '#FF5555' : ''};" value="#{remessa.valorAceito_Apresentar}"/>
        </rich:column>

        <rich:column sortBy="#{remessa.valorLiquido}">
            <f:facet name="header">
                <h:outputText title="Valor Líquido" value="R$ Liq"/>
            </f:facet>
            <h:outputText value="#{remessa.valorLiquido_Apresentar}"/>
        </rich:column>

        <rich:column rendered="#{SuperControle.widthScreenClient > 1024}" sortBy="#{remessa.dataPrevistaCredito}">
            <f:facet name="header">
                <h:outputText title="Data Prevista para Crédito no Banco" value="Dt.Cred."/>
            </f:facet>
            <h:outputText style="font-size: 9px;" value="#{remessa.dataPrevistaCredito}">
                <f:convertDateTime timeStyle="medium" pattern="dd/MM/yyyy"/>
            </h:outputText>
        </rich:column>

        <rich:column rendered="#{SuperControle.widthScreenClient > 1024}" sortBy="#{remessa.dataRetorno}">
            <f:facet name="header">
                <h:outputText title="Data no fechamento de Caixa" value="Dt.Caixa"/>
            </f:facet>
            <h:outputText style="font-size: 9px;" value="#{remessa.dataRetorno}">
                <f:convertDateTime timeStyle="medium" pattern="dd/MM/yyyy"/>
            </h:outputText>
        </rich:column>

        <rich:column rendered="#{SuperControle.widthScreenClient > 1024}"  sortBy="#{remessa.usuario.nomeAbreviado}">
            <f:facet name="header">
                <h:outputText title="Usuário criou Remessa" value="U.Rem."/>
            </f:facet>
            <h:outputText value="#{remessa.usuario.nomeAbreviado}"/>
        </rich:column>

        <rich:column rendered="#{SuperControle.widthScreenClient > 1024}" sortBy="#{remessa.usuarioRetorno}">
            <f:facet name="header">
                <h:outputText title="Usuário processou o Retorno" value="U.Ret."/>
            </f:facet>
            <h:outputText value="#{remessa.usuarioRetorno}"
                          styleClass="tooltipster"
                          title="#{remessa.usuarioRetornoTitle}"/>
        </rich:column>

        <rich:column>
            <f:facet name="header">
                <h:outputText title="Registro Header e Trailer" value="?"/>
            </f:facet>
            <a4j:commandLink reRender="panelDadosParametros" title="Visualizar Header e Trailer"
                             actionListener="#{GestaoRemessasControle.exibirParams}" value="?">
                <f:attribute name="params" value="header"/>
                <f:attribute name="remessa" value="#{remessa}"/>
            </a4j:commandLink>
        </rich:column>

        <rich:column rendered="#{GestaoRemessasControle.exibirControles}">
            <f:facet name="header">
                <h:outputText title="Arquivo de Remessa" value="Rem."/>
            </f:facet>

            <h:panelGroup rendered="#{remessa.boleto}">
                <a4j:commandLink actionListener="#{GestaoRemessasControle.confirmarFecharRemessa}"
                                 rendered="#{remessa.dataFechamento == null}"
                                 id="fecharRemessa"
                                 reRender="mdlMensagemGenerica"
                                 oncomplete="#{GestaoRemessasControle.msgAlert}"
                                 title="Fechar remessa de boleto"
                                 value="Fechar">
                    <f:attribute name="remessa" value="#{remessa}"/>
                </a4j:commandLink>

            <a4j:commandButton actionListener="#{GestaoRemessasControle.downloadRemessa}"
                                   rendered="#{remessa.dataFechamento != null}"
                                   image="images/download_file.png"
                                   styleClass="rich-fileupload-button rich-fileupload-font"
                                   reRender="panelDadosRemessa"
                                   onmouseover="this.className='rich-fileupload-button-light'"
                                   onmouseout="this.className='rich-fileupload-font'"
                                   oncomplete="#{GestaoRemessasControle.msgAlertAuxiliar}"
                                   title="Download Arquivo Remessa">
                    <f:attribute name="remessa" value="#{remessa}"/>
                </a4j:commandButton>
            </h:panelGroup>

            <h:panelGroup rendered="#{!remessa.boleto}">
            <a4j:commandButton actionListener="#{GestaoRemessasControle.downloadRemessa}"
                               id="downloadRemessa"
                               rendered="#{((!GestaoRemessasControle.envioAutomatico || (remessa.convenioCobranca.tipo.codigo != 8 && remessa.convenioCobranca.tipo.codigo != 2)) && remessa.situacaoRemessa.id != 4) || GestaoRemessasControle.usuarioLogado.username == 'admin' || LoginControle.permissaoAcessoMenuVO.permiteDownloadRemessa}"
                               image="images/download_file.png"
                               styleClass="rich-fileupload-button rich-fileupload-font"
                               reRender="panelDadosRemessa"
                               onmouseover="this.className='rich-fileupload-button-light'"
                               onmouseout="this.className='rich-fileupload-font'"
                               oncomplete="#{GestaoRemessasControle.msgAlertAuxiliar}"
                               title="Download Arquivo Remessa">
                <f:attribute name="remessa" value="#{remessa}"/>
            </a4j:commandButton>
            
            <a4j:commandButton actionListener="#{GestaoRemessasControle.downloadRemessa}"
                               rendered="#{remessa.getnet}"
                               image="images/lock.gif"
                               styleClass="rich-fileupload-button rich-fileupload-font"
                               reRender="panelDadosRemessa"
                               onmouseover="this.className='rich-fileupload-button-light'"
                               onmouseout="this.className='rich-fileupload-font'"
                               oncomplete="#{GestaoRemessasControle.msgAlertAuxiliar}"
                               title="Download Arquivo Criptografado Remessa">
                <f:attribute name="remessa" value="#{remessa}"/>
                <f:attribute name="criptografado" value="true"/>
            </a4j:commandButton>
            </h:panelGroup>
        </rich:column>

        <rich:column rendered="#{GestaoRemessasControle.exibirControles && GestaoRemessasControle.usuarioLogado.usuarioAdminPACTO}">
            <f:facet name="header">
                <h:outputText title="Arquivo de Retorno" value="Ret."/>
            </f:facet>
            <h:panelGrid cellpadding="0" cellspacing="0" columnClasses="text,text" columns="2">
                <rich:panel>
                    <a4j:commandButton actionListener="#{GestaoRemessasControle.downloadRetorno}"
                                       rendered="#{remessa.temRetorno}"
                                       image="images/download_file.png"                                       
                                       reRender="panelDadosRemessa"
                                       styleClass="rich-fileupload-button rich-fileupload-font"
                                       onmouseover="this.className='rich-fileupload-button-light'"
                                       onmouseout="this.className='rich-fileupload-button rich-fileupload-font'"
                                       oncomplete="#{GestaoRemessasControle.msgAlertAuxiliar}"
                                       title="Download Arquivo Retorno">
                        <f:attribute name="retorno" value="#{remessa}"/>
                    </a4j:commandButton>
                </rich:panel>
            </h:panelGrid>
        </rich:column>

        <rich:column rendered="#{GestaoRemessasControle.exibirControles}">
            <f:facet name="header">
                <h:outputText title="Resultado Remessa" value="Result."/>
            </f:facet>
            <a4j:commandButton id="itemremessa" actionListener="#{GestaoRemessasControle.exibirItensRemessa}"
                               image="images/view-details.png"
                               styleClass="rich-fileupload-button rich-fileupload-font"
                               rendered="#{remessa.qtdRegistros <= 1500}"
                               reRender="panelDadosRemessa, panelDadosRemessaNovo, panelDadosRemessaBoleto"
                               onmouseover="this.className='rich-fileupload-button-light'"
                               onmouseout="this.className='rich-fileupload-font'"
                               title="Exibir Itens da Remessa">
                <f:attribute name="remessa" value="#{remessa}"/>
            </a4j:commandButton>

            <a4j:commandButton id="naoapresentaritemremessa"
                               image="images/view-details.png"
                               styleClass="rich-fileupload-button rich-fileupload-font"
                               rendered="#{remessa.qtdRegistros > 1500}"
                               reRender="panelDadosRemessa, panelDadosRemessaNovo, panelDadosRemessaBoleto"
                               onmouseover="this.className='rich-fileupload-button-light'"
                               onmouseout="this.className='rich-fileupload-font'"
                               onclick="alert('Para visualizar os itens da remessa, acesse o Gerador de Consultas');"
                               title="Exibir Itens da Remessa"/>
        </rich:column>

        <rich:column rendered="#{GestaoRemessasControle.exibirControles}">
            <f:facet name="header">
                <h:outputText title="Imprimir Boleto" value="Imp. Boleto"/>
            </f:facet>
            <a4j:commandButton id="imprimirBoletos"
                               actionListener="#{GestaoRemessasControle.imprimirBoletosRemessa}"
                               rendered="#{remessa.boleto}"
                               image="images/imprimir.png"
                               oncomplete="#{GestaoRemessasControle.msgAlert} #{BoletoBancarioControle.mensagemNotificar} #{GestaoRemessasControle.mensagemNotificar}"
                               styleClass="rich-fileupload-button rich-fileupload-font"
                               reRender="panelDadosRemessa, panelDadosRemessaNovo, panelDadosRemessaBoleto"
                               onmouseover="this.className='rich-fileupload-button-light'"
                               onmouseout="this.className='rich-fileupload-font'"
                               title="Imprimir boletos da Remessa">
                <f:attribute name="remessa" value="#{remessa}"/>
            </a4j:commandButton>
        </rich:column>

        <rich:column rendered="#{GestaoRemessasControle.buscarTodasEmpresas}" sortBy="#{remessa.convenioCobranca.descricao}">
            <f:facet name="header">
                <h:outputText title="Convênio Cobrança" value="Convênio Cobrança"/>
            </f:facet>
            <h:outputText value="#{remessa.convenioCobranca.descricao}" id="descricaoConvenio"/>
        </rich:column>

        <rich:column rendered="#{GestaoRemessasControle.buscarTodasEmpresas}" sortBy="#{remessa.empresaVO.nome}">
            <f:facet name="header">
                <h:outputText title="Empresa" value="Empresa"/>
            </f:facet>
            <h:outputText value="#{remessa.empresa_Apresentar}" id="nomeEmpresa"/>
        </rich:column>

    </rich:dataTable>

    <h:panelGrid rendered="true" columns="2" columnClasses="colunaTopCentralizada, colunaTopCentralizada"
                 style="margin-top: 5px" cellpadding="0" cellspacing="0">
        <rich:dataTable value="#{GestaoRemessasControle.situacoesRemessa}" var="situacao">
            <f:facet name="header">
                <h:outputText value="Situações da Remessa"/>
            </f:facet>

            <rich:column styleClass="#{situacao.classe}">
                <h:panelGroup layout="block" style="border:none; width: 10px; height: 10px; background-color:#{situacao.cor}"/>
            </rich:column>

            <rich:column>
                <h:outputText value="#{situacao.descricao}"/>
            </rich:column>

            <rich:column>
                <h:outputText value="#{situacao.hint}"/>
            </rich:column>
        </rich:dataTable>
    </h:panelGrid>
</a4j:outputPanel>
