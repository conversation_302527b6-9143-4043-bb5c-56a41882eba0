<%-- 
    Document   : include_table_dados_acesso_cliente
    Created on : 01/04/2015, 18:31:11
    Author     : Waller
--%>
<%@include file="../imports.jsp" %>

<rich:dataTable
    id="dadosUltimoAcesso" width="100%" border="0"
    cellspacing="0" cellpadding="10" styleClass="textsmall"
    columnClasses="centralizado, centralizado, centralizado, centralizado, centralizado, colunaDireita"
    rendered="#{!empty ClienteControle.clienteVO.situacaoClienteSinteticoVO}"
    value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO}"
    var="situacao">
    <rich:column rendered="#{!empty ClienteControle.clienteVO.situacaoClienteSinteticoVO.dataUltimoAcesso}">
        <f:facet name="header">
            <h:outputText style="font-weight: bold" value="�ltimo Acesso" />
        </f:facet>
        <f:verbatim>
            <h:outputText style="font-weight: bold" styleClass="blue"
                          value="#{situacao.dataUltimoAcesso}">
                <f:convertDateTime type="date" dateStyle="short"
                                   locale="pt" pattern="dd/MM/yyyy HH:mm" />
            </h:outputText>
            -
            <h:outputText
                style="font-weight: bold" styleClass="blue"
                value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.nomeDiaSemanaDataUltimoAcesso}" />
        </f:verbatim>
    </rich:column>

    <rich:column rendered="#{!empty ClienteControle.clienteVO.situacaoClienteSinteticoVO.diasAcessoSemanaPassada}">
        <f:facet name="header">
            <h:outputText style="font-weight: bold" value="Q.A 1" />
        </f:facet>
        <f:verbatim>
            <h:outputText style="font-weight: bold" styleClass="blue"
                          value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.diasAcessoSemanaPassada != -1 ? ClienteControle.clienteVO.situacaoClienteSinteticoVO.diasAcessoSemanaPassada : '-'}"/>
        </f:verbatim>
    </rich:column>
    <rich:column rendered="#{!empty ClienteControle.clienteVO.situacaoClienteSinteticoVO.diasAcessoSemana2}">
        <f:facet name="header">
            <h:outputText style="font-weight: bold" value="Q.A 2" />
        </f:facet>
        <h:outputText style="font-weight: bold" styleClass="blue"
                      value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.diasAcessoSemana2 != -1 ? ClienteControle.clienteVO.situacaoClienteSinteticoVO.diasAcessoSemana2 : '-'}"/>
    </rich:column>
    <rich:column rendered="#{!empty ClienteControle.clienteVO.situacaoClienteSinteticoVO.diasAcessoSemana3}">
        <f:facet name="header">
            <h:outputText style="font-weight: bold" value="Q.A 3" />
        </f:facet>
        <h:outputText style="font-weight: bold" styleClass="blue"
                      value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.diasAcessoSemana3 != -1 ? ClienteControle.clienteVO.situacaoClienteSinteticoVO.diasAcessoSemana3 : '-' }"/>
    </rich:column>
    <rich:column rendered="#{!empty ClienteControle.clienteVO.situacaoClienteSinteticoVO.diasAcessoSemana4}">
        <f:facet name="header">
            <h:outputText style="font-weight: bold" value="Q.A 4" />
        </f:facet>
        <h:outputText style="font-weight: bold" styleClass="blue"
                      value="#{ClienteControle.clienteVO.situacaoClienteSinteticoVO.diasAcessoSemana4 != -1 ? ClienteControle.clienteVO.situacaoClienteSinteticoVO.diasAcessoSemana4 : '-' }"/>
    </rich:column>
</rich:dataTable>
<h:outputLink value="#{SuperControle.urlWiki}Zillyon:Siglas#QA"
              title="Clique e saiba mais: Quantidade Acessos" target="_blank">
    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
    <h:outputText value="Q.A = Quantidade Acessos: 1�, 2�, 3� e 4� �ltimas semanas"/>
</h:outputLink>