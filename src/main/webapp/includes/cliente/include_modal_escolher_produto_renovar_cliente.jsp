<%@include file="/includes/imports.jsp" %>

<a4j:keepAlive beanName="RenovarProdutoControle"/>

<a4j:outputPanel>
    <rich:modalPanel id="modalEscolherProdutoRenovar" width="400" autosized="true" shadowOpacity="true"
                     styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Escolha o produto para renovar"/>
            </h:panelGroup>
        </f:facet>

        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                    id="hidelink1modalEscolherProdutoRenovar"/>
                <rich:componentControl for="modalEscolherProdutoRenovar"
                                       attachTo="hidelink1modalEscolherProdutoRenovar" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>

        <h:panelGroup id="panelConteudoMdlRenovar">
            <a4j:form id="formRenovar">
                    <h:panelGrid columns="2" columnClasses="text,text">
                        <h:outputText value="Produto:"/>
                        <h:selectOneMenu id="produto" value="#{RenovarProdutoControle.produtoEscolhido.codigo}">
                            <f:selectItems value="#{RenovarProdutoControle.produtosParaRenovar}"/>
                        </h:selectOneMenu>
                    </h:panelGrid>
                <h:panelGroup id="botoes">
                    <a4j:commandLink id="btnGravar" title="Grava as altera��es realizadas"
                                       style="float: right; margin-top: 10px;"
                                       styleClass="pure-button pure-button-primary tooltipster"
                                       action="#{RenovarProdutoControle.acaoRenovarProduto}"
                                       reRender="mensagemDetalhada, form:panelProdutosValidadeCliente, form:avisosCliente"
                                       oncomplete="#{RenovarProdutoControle.acaoAjaxRenovarProduto};">
                        <h:outputText value="Renovar Produto" style="font-size: 14px"/>
                    </a4j:commandLink>
                </h:panelGroup>

                <h:panelGroup id="mensagemDetalhada" layout="block">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens" rendered="#{RenovarProdutoControle.erro}">
                        <h:commandButton image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgRenovarProdDet" styleClass="mensagemDetalhada" value="#{RenovarProdutoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGroup>
            </a4j:form>
        </h:panelGroup>

    </rich:modalPanel>

    <a4j:status forceId="true" id="statusRenovarProduto"
                onstart="document.getElementById('formRenovar:imageLoading').style.visibility = '';"
                onstop="document.getElementById('formRenovar:imageLoading').style.visibility = 'hidden';"/>

</a4j:outputPanel>