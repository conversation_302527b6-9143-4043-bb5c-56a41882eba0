<%-- 
    Document   : include_modal_reposicoes
    Created on : 27/02/2013, 13:58:18
    Author     : waller
--%>

<%@include file="../imports.jsp" %>

<rich:modalPanel id="modalReposicoes" autosized="true" shadowOpacity="true" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="lblModalReposicoes" value="Reposi��es Solicitadas na Turma"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkModalReposicoes"/>
            <rich:componentControl for="modalReposicoes" attachTo="hidelinkModalReposicoes" operation="hide"  event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formReposicoes">

        <h:panelGroup>
            <rich:dataTable value="#{ReposicaoControle.reposicoes}"
                            rows="10"
                            id="tblReposicoes"
                            rowKeyVar="status"
                            var="item">

                <%@include file="../../pages/ce/includes/include_contador_richtable.jsp" %>

                <rich:column rendered="#{fn:contains(pagina, 'consultarTurmaForm')}"
                             sortBy="#{item.cliente.matricula}">
                    <f:facet name="header">
                        <h:outputText value="Mat."/>
                    </f:facet>
                    <h:outputText value="#{item.cliente.matricula}"/>
                </rich:column>

                <rich:column rendered="#{fn:contains(pagina, 'consultarTurmaForm')}" 
                             sortBy="#{item.cliente.pessoa.nome}">
                    <f:facet name="header">
                        <h:outputText value="Cliente"/>
                    </f:facet>
                    <div  style="width:220px;">
                        <h:outputText style="font-weight:bold;font-size:9px;" value="#{item.cliente.pessoa.nome}"/>
                    </div>
                </rich:column>

                <rich:column sortBy="#{item.dataLancamento}" >
                    <f:facet name="header">
                        <h:outputText value="Dt.Lanc."/>
                    </f:facet>
                    <div  style="width:88px;">
                        <h:outputText value="#{item.dataLancamento}">
                            <f:convertDateTime pattern="dd/MM/yyyy HH:mm"/>
                        </h:outputText>
                    </div>
                </rich:column>

                <rich:column sortBy="#{item.dataOrigem}">
                    <f:facet name="header">
                        <h:outputText value="Aula Desmarcada"/>
                    </f:facet>
                    <div  style="width:300px;">
                        <h:outputText value="#{item.descricaoOrigem}"/>
                    </div>
                </rich:column>

                <rich:column sortBy="#{item.dataReposicao}">
                    <f:facet name="header">
                        <h:outputText value="Aula Reposi��o"/>
                    </f:facet>
                    <div  style="width:300px;">
                        <h:outputText value="#{item.descricaoDestino}"/>
                    </div>
                </rich:column>                

                <rich:column sortBy="#{item.dataPresenca}" >
                    <f:facet name="header">
                        <h:outputText value="Presen�a"/>
                    </f:facet>
                    <h:outputText rendered="#{item.dataPresenca != null}" value="#{item.dataPresenca}">
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:outputText>
                    <h:outputText rendered="#{item.dataPresenca == null}" value="Falta"/>
                </rich:column>

                <rich:column sortBy="#{item.usuario.username}" >
                    <f:facet name="header">
                        <h:outputText value="Usu�rio"/>
                    </f:facet>
                    <h:outputText value="#{item.usuario.username}"/>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Op��es"/>
                    </f:facet>                    

                    <a4j:commandButton onclick="if (!confirm('Deseja enviar Email e/ou SMS com os dados dessa reposi��o?')){return false;}"
                                       rendered="#{item.codigo != null && item.codigo > 0}"
                                       image="/imagens/send_email.png"
                                       style="vertical-align:middle;padding: 1 1 1 1;"
                                       title="Envia os dados da Reposi��o por e-mail e/ou SMS, se poss�vel"
                                       reRender="formReposicoes"
                                       oncomplete="#{ReposicaoControle.msgAlert}"
                                       actionListener="#{ReposicaoControle.enviarEmailESMS}">
                        <f:attribute name="reposicao" value="#{item}"/>
                    </a4j:commandButton>

                    <a4j:commandButton onclick="if (!confirm('Confirma exclus�o desta Reposi��o?')){return false;}"
                                       rendered="#{item.dataPresenca == null && item.codigo != null && item.codigo > 0}"
                                       image="/imagens/delete.png"
                                       style="vertical-align:middle;padding: 1 1 1 1;"
                                       title="Excluir esta Reposi��o"
                                       reRender="formReposicoes,form:panelTurmas"
                                       oncomplete="#{fn:length(ReposicaoControle.reposicoes) <= 0 ? 'Richfaces.hideModalPanel(\"modalReposicoes\");' : ''}
                                       #{ReposicaoControle.msgAlert}"
                                       actionListener="#{ReposicaoControle.deleteListener}">
                        <f:attribute name="reposicao" value="#{item}"/>
                    </a4j:commandButton>
                </rich:column>
                <f:facet name="footer">
                    <rich:datascroller for="tblReposicoes"/>
                </f:facet>
            </rich:dataTable>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>


