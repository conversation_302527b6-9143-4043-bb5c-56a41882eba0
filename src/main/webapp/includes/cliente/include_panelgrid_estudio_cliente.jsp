<%-- 
    Document   : include_panelgrid_estudio_cliente
    Created on : 02/05/2012, 09:35:11
    Author     : Waller
--%>
<%@include file="../imports.jsp" %>
<link href="${root}/css/estudio.css" rel="stylesheet" type="text/css">
<h:panelGroup layout="block" id="dadosStudioCliente">
    <a4j:commandButton id="btnAtualizaDadosStudioCliente"
                       style="display: none;"
                       reRender="dadosStudioCliente"
                       action="#{ClienteControle.atualizarInfoStudioCliente}"/>
    <h:panelGrid width="100%" cellspacing="1" columns="3" bgcolor="#e6e6e6">
        <rich:column colspan="3">
            <h:panelGrid columns="1" width="100%">

                <h:panelGroup layout="block" style="padding-bottom: 10px;">

                    <div class="sep" style="margin: 4px 0 5px 0;">
                        <img src="images/shim.gif">
                    </div>
                    <h:graphicImage
                            value="/images/arrow2.gif" width="16" height="16"
                            style="vertical-align: middle; margin-right: 6px; float:left;"/>
                    <a4j:commandLink action="#{clienteEstudioControle.fecharModalAgendaAluno}"
                                     oncomplete="abrirPopup('popup_panelgrid_estudio_cliente.jsp', 'HistoricoAgendamento', 920, 640);">
                        <h:outputText style="font-size:14px; font-weight: bold;" value="Hist�rico de Agendamento"/>
                    </a4j:commandLink>
                    <h:graphicImage
                            value="/images/arrow3.gif" width="16" height="16"
                            style="vertical-align: middle; margin-left: 6px;  float:right;"/>
                    <a4j:commandLink oncomplete="#{rich:component('modalAgendaExcecao')}.show()"
                                     action="#{clienteEstudioControle.acaoListarAgendaExcecao}"
                                     reRender="mainPanelModalAgendaExcecao">
                        <h:outputText id="outLista"
                                      style="#{fn:length(clienteEstudioControle.listaAgendaExcecao) > 0 ? 'color:red;' : ''} font-size:14px; font-weight: bold; float:right;"
                                      value="#{fn:length(clienteEstudioControle.listaAgendaExcecao)} Falta#{fn:length(clienteEstudioControle.listaAgendaExcecao) > 1 ? 's' : ''} Justificada#{fn:length(clienteEstudioControle.listaAgendaExcecao) > 1 ? 's' : ''}"/>
                    </a4j:commandLink>
                </h:panelGroup>

            </h:panelGrid>

        </rich:column>
    </h:panelGrid>


    <h:panelGrid width="100%" cellspacing="1" columns="3" bgcolor="#e6e6e6">

        <rich:column width="33%" style="vertical-align:top;">
            <h:graphicImage
                    value="/images/Money_1.png" width="16" height="16"
                    style="vertical-align: middle; margin-right: 6px;"/>
            <h:outputText style="font-weight: bold" value="Vendidos"/>
            <rich:dataTable
                    id="clienteEstudioControle-listaItemVenda"
                    rows="#{clienteEstudioControle.nmrPaginaItemVenda}"
                    width="100%"
                    rowClasses="textsmall"
                    styleClass="textsmall"
                    columnClasses="centralizado, centralizado, centralizado"
                    value="#{clienteEstudioControle.listaItemVenda}"
                    var="item">

                <rich:column
                        id="item-codigo"
                        sortBy="#{item.vendaAvulsa}"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Id"/>
                    </f:facet>
                    <h:outputText value="#{item.vendaAvulsa}"/>
                </rich:column>

                <rich:column
                        id="item-dataVenda"
                        sortBy="#{item.dataVenda}"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Data Venda"/>
                    </f:facet>
                    <h:outputText value="#{item.dataVenda}">
                        <f:convertDateTime pattern="dd/MM/yyyy HH:mm:ss"/>
                    </h:outputText>
                </rich:column>
                <rich:column
                        id="item-produto-venda"
                        width="200px"
                        sortBy="#{item.produto.descricao}"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Produto"/>
                    </f:facet>
                    <h:outputText value="#{item.produto.descricao}"/>
                </rich:column>
                <rich:column
                        id="item-quantidade"
                        width="10px"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Quantidade"/>
                    </f:facet>
                    <h:outputText value="#{item.quantidade}"/>
                </rich:column>
                <rich:column
                        id="item-valorParcial"
                        width="10px"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Valor Parcial"/>
                    </f:facet>
                    <h:outputText value="#{item.valorParcial}">
                        <f:convertNumber maxFractionDigits="2" groupingUsed="true" maxIntegerDigits="14" type="currency"
                                         currencySymbol="#{clienteEstudioControle.empresa.moeda} "/>
                    </h:outputText>
                </rich:column>
                <rich:column
                        id="item-pacote"
                        width="10px"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Pacote"/>
                    </f:facet>
                    <h:outputText value="#{item.pacoteVO.titulo}">
                    </h:outputText>
                </rich:column>
                <rich:column
                        id="item-agendamentos"
                        width="10px"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Agend."/>
                    </f:facet>
                    <a4j:commandButton action="#{clienteEstudioControle.consultarAgendamentos}"
                                       image="imagens/botalVisualizarLog.png"
                                       oncomplete="abrirPopup('pages/estudio/includes/popup_dados_agendamento.jsp', 'Agendamentos', 905, 660);"
                                       reRender="modalAgenda"/>
                </rich:column>
            </rich:dataTable>

            <a4j:outputPanel id="painelPaginacao">
                <h:panelGroup id="painelPaginacaoManual">
                    <a4j:commandLink id="pagiInicial" styleClass="tituloCampos" value="  <<  "
                                     reRender="clienteEstudioControle-listaItemVenda, paginaAtual,paginaAtualTop,painelPaginacaoTop, painelPaginacao"
                                     rendered="#{clienteEstudioControle.confPaginacao.apresentarPrimeiro}"
                                     actionListener="#{clienteEstudioControle.consultarPaginadoListener}">
                        <f:attribute name="pagNavegacao" value="pagInicial"/>
                        <f:attribute name="codigoCliente" value="#{clienteEstudioControle.codigoCliente}"/>
                    </a4j:commandLink>
                    <a4j:commandLink id="pagiAnterior" styleClass="tituloCampos" value="  <  "
                                     reRender="clienteEstudioControle-listaItemVenda, paginaAtual,paginaAtualTop,painelPaginacaoTop, painelPaginacao"
                                     rendered="#{clienteEstudioControle.confPaginacao.apresentarAnterior}"
                                     actionListener="#{clienteEstudioControle.consultarPaginadoListener}">
                        <f:attribute name="pagNavegacao" value="pagAnterior"/>
                        <f:attribute name="codigoCliente" value="#{clienteEstudioControle.codigoCliente}"/>
                    </a4j:commandLink>
                    <h:outputText id="paginaAtual" styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_msg_pagina} #{clienteEstudioControle.confPaginacao.paginaAtualDeTodas}"
                                  rendered="true"/>
                    <a4j:commandLink id="pagiPosterior" styleClass="tituloCampos" value="  >  "
                                     reRender="clienteEstudioControle-listaItemVenda, paginaAtual,paginaAtualTop,painelPaginacaoTop, painelPaginacao"
                                     rendered="#{clienteEstudioControle.confPaginacao.apresentarPosterior}"
                                     actionListener="#{clienteEstudioControle.consultarPaginadoListener}">
                        <f:attribute name="pagNavegacao" value="pagPosterior"/>
                        <f:attribute name="codigoCliente" value="#{clienteEstudioControle.codigoCliente}"/>
                    </a4j:commandLink>
                    <a4j:commandLink id="pagiFinal" styleClass="tituloCampos" value="  >>  "
                                     reRender="clienteEstudioControle-listaItemVenda, paginaAtual,paginaAtualTop,painelPaginacaoTop, painelPaginacao"
                                     rendered="#{clienteEstudioControle.confPaginacao.apresentarUltimo}"
                                     actionListener="#{clienteEstudioControle.consultarPaginadoListener}">
                        <f:attribute name="pagNavegacao" value="pagFinal"/>
                        <f:attribute name="codigoCliente" value="#{clienteEstudioControle.codigoCliente}"/>
                    </a4j:commandLink>

                    <h:outputText id="totalItens" styleClass="tituloCampos"
                                  value=" [#{msg_aplic.prt_msg_itens} #{clienteEstudioControle.confPaginacao.numeroTotalItens}]"
                                  rendered="true"/>

                </h:panelGroup>
            </a4j:outputPanel>
        </rich:column>

        <rich:column width="33%" style="vertical-align:top;">
            <h:graphicImage
                    value="/images/schedule.png" width="16" height="16"
                    style="vertical-align: middle; margin-right: 6px;"/>
            <h:outputText style="font-weight: bold" value="A Agendar"/>
            <rich:dataTable
                    id="clienteEstudioControle-listaAAgendar"
                    rows="#{clienteEstudioControle.nmrPaginaAgendar}"
                    width="100%"
                    rowClasses="textsmall"
                    styleClass="textsmall"
                    columnClasses="centralizado, centralizado, centralizado"
                    value="#{clienteEstudioControle.listaAAgendar}"
                    var="item">

                <rich:column
                        id="item-saldo"
                        width="50px"
                        sortBy="#{item.saldoProduto}"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Saldo"/>
                    </f:facet>
                    <h:outputText value="#{item.saldoProduto} #{item.saldoProduto == 1 ? ' sess�o' :' sess�es'}"/>
                </rich:column>

                <rich:column
                        id="item-produto-agendar"
                        sortBy="#{item.produtoVO.descricao}"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Produto"/>
                    </f:facet>
                    <h:outputText value="#{item.produtoVO.descricao}"/>
                </rich:column>

                <rich:column
                        id="item-data"
                        width="50px"
                        sortBy="#{item.dataFimAgendar_Apresentar}"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Vencimento"/>
                    </f:facet>
                    <h:outputText value="#{item.dataFimAgendar_Apresentar}"/>
                </rich:column>

                <rich:column
                        width="10px"
                        id="item-agendar">
                    <f:facet name="header">
                        <h:selectBooleanCheckbox
                                id="selecionarTodosAgendar"
                                value="#{clienteEstudioControle.selecionarTodosAgendar}">
                            <a4j:support
                                    action="#{clienteEstudioControle.acaoSelecionarTodosAgendar}"
                                    event="onclick"
                                    reRender="itemSolicitacao-selecionado-agendar">
                            </a4j:support>
                        </h:selectBooleanCheckbox>
                    </f:facet>
                    <h:selectBooleanCheckbox
                            id="itemSolicitacao-selecionado-agendar"
                            value="#{item.selecionarAgendar}">
                        <a4j:support
                                status="statusHora"
                                action="#{clienteEstudioControle.acaoSelecionarUmAgendar}"
                                event="onclick"
                                reRender="selecionarTodosAgendar">
                        </a4j:support>
                    </h:selectBooleanCheckbox>
                </rich:column>

            </rich:dataTable>
            <h:panelGroup>
                <h:panelGrid columns="2">
                    <rich:datascroller align="center"
                                       for="clienteEstudioControle-listaAAgendar" maxPages="100"
                                       id="scResultadoListaAAgendar"/>
                    <rich:inputNumberSpinner inputSize="4"
                                             styleClass="form" enableManualInput="true"
                                             minValue="1" maxValue="100"
                                             value="#{clienteEstudioControle.nmrPaginaAgendar}">
                        <a4j:support event="onchange"
                                     focus="scResultadoListaAAgendar"
                                     reRender="clienteEstudioControle-listaAAgendar,scResultadoListaAAgendar"/>
                    </rich:inputNumberSpinner>
                </h:panelGrid>
                <a4j:commandButton image="../imagens/estudio/agendar_selecionados.png" value="Agendar Selecionados"
                                   action="#{clienteEstudioControle.acaoDisponibilidade}"/>
            </h:panelGroup>
        </rich:column>

        <rich:column width="33%" style="vertical-align:top;">
            <h:graphicImage
                    value="/images/dollar_green.png" width="16" height="16"
                    style="vertical-align: middle; margin-right: 6px;"/>
            <h:outputText style="font-weight: bold" value="A Faturar"/>

            <rich:dataTable
                    id="clienteEstudioControle-listaAFaturar"
                    width="100%"
                    rows="#{clienteEstudioControle.nmrPaginaFaturar}"
                    rowClasses="textsmall"
                    styleClass="textsmall"
                    columnClasses="centralizado, centralizado, centralizado"
                    value="#{clienteEstudioControle.listaAFaturar}"
                    var="item">

                <rich:column
                        id="item-produto-faturar"
                        sortBy="#{item.produtoVO.descricao}"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Produto"/>
                    </f:facet>
                    <h:outputText value="#{item.produtoVO.descricao}"/>
                </rich:column>

                <rich:column

                        id="item-data-faturar"
                        sortBy="#{item.dataAula}"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Data Aula"/>
                    </f:facet>
                    <h:outputText value="#{item.dataAula}" converter="dataConverter"/>
                </rich:column>

                <rich:column
                        width="10px"
                        id="item-faturar">
                    <f:facet name="header">
                        <h:selectBooleanCheckbox
                                id="selecionarTodosFaturar"
                                value="#{clienteEstudioControle.selecionarTodosFaturar}">
                            <a4j:support
                                    action="#{clienteEstudioControle.acaoSelecionarTodosFaturar}"
                                    event="onclick"
                                    reRender="itemSolicitacao-selecionado-faturar">
                            </a4j:support>
                        </h:selectBooleanCheckbox>
                    </f:facet>
                    <h:selectBooleanCheckbox
                            id="itemSolicitacao-selecionado-faturar"
                            value="#{item.selecionarPagarFatura}">
                        <a4j:support
                                action="#{clienteEstudioControle.acaoSelecionarUmFaturar}"
                                event="onclick"
                                reRender="selecionarTodosFaturar"/>
                    </h:selectBooleanCheckbox>
                </rich:column>

            </rich:dataTable>

            <h:panelGroup>
                <h:panelGrid columns="2">
                    <rich:datascroller align="center"
                                       for="clienteEstudioControle-listaAFaturar" maxPages="100"
                                       id="scResultadoListaAFaturar"/>
                    <rich:inputNumberSpinner inputSize="4"
                                             styleClass="form" enableManualInput="true"
                                             minValue="1" maxValue="100"
                                             value="#{clienteEstudioControle.nmrPaginaFaturar}">
                        <a4j:support event="onchange"
                                     focus="scResultadoListaAFaturar"
                                     reRender="clienteEstudioControle-listaAFaturar,scResultadoListaAFaturar"/>
                    </rich:inputNumberSpinner>
                </h:panelGrid>
            </h:panelGroup>
            <a4j:commandButton image="../imagens/estudio/pagar_selecionados.png" value="Pagar Selecionados"
                               action="#{clienteEstudioControle.acaoPagarFaturar}"/>
        </rich:column>
    </h:panelGrid>
</h:panelGroup>
<rich:modalPanel id="modalAgendaExcecao" autosized="true" shadowOpacity="true" width="800"
                 height="350" onshow="focusAt('okButton');">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Hist�rico de Sess�es Justificadas"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="../imagens/close.png" style="cursor:pointer" id="hidelinkmodalAgendaExcecao"/>
            <rich:componentControl for="modalAgendaExcecao" attachTo="hidelinkmodalAgendaExcecao"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>


    <h:panelGrid id="mainPanelModalAgendaExcecao" columns="1" width="100%" columnClasses="colunaCentralizada">

        <h:panelGrid columns="2">
            <h:outputText style="font-weight: bold;" value="Ver todos" styleClass="text"/>
            <h:selectBooleanCheckbox
                    id="selecionado-verTodosModalAgenda"
                    style="margin-left: 14px;"
                    value="#{clienteEstudioControle.verTodosModalAgenda}">
                <a4j:support event="onclick" action="#{clienteEstudioControle.acaoListarAgendaExcecao}"
                             ajaxSingle="true"
                             reRender="clienteEstudioControle-listaAgendaExcecao-modal"/>
            </h:selectBooleanCheckbox>
        </h:panelGrid>
        <h:panelGroup layout="block" style="height:300px;">
            <rich:dataTable
                    id="clienteEstudioControle-listaAgendaExcecao-modal"
                    width="100%"
                    rowClasses="textsmall"
                    styleClass="textsmall"
                    columnClasses="centralizado, centralizado, centralizado"
                    value="#{clienteEstudioControle.listaAgendaExcecao}"
                    var="item">

                <rich:column
                        id="item-dataLancamento"
                        sortable="true"
                        width="110px"
                        selfSorted="true"
                        sortBy="#{item.dataLancamento}"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Data Justificada"/>
                    </f:facet>
                    <h:outputText value="#{item.dataLancamento}" converter="dataConverter"/>
                    -
                    <h:outputText value="#{item.dataLancamento}" converter="timeConverter"/>
                </rich:column>

                <rich:column
                        id="item-dataAula"
                        sortable="true"
                        width="150px"
                        selfSorted="true"
                        sortBy="#{item.dataAula}"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Data Aula"/>
                    </f:facet>
                    <h:outputText value="#{item.dataAula}" converter="dataConverter" escape="false"/>
                    <h:outputText value="- #{item.diaSemana_Apresentar}"/>
                </rich:column>

                <rich:column
                        id="item-horario"
                        sortable="true"
                        width="20px"
                        selfSorted="true"
                        sortBy="#{item.horaInicio}"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Hor�rio"/>
                    </f:facet>
                    <h:outputText value="#{item.horaInicio_Apresentar}"/>
                </rich:column>
                <rich:column
                        id="item-produto-venda-excecao"
                        width="105px"
                        sortable="true"
                        selfSorted="true"
                        sortBy="#{item.produtoVO.descricao}"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Produto"/>
                    </f:facet>
                    <h:outputText value="#{item.produtoVO.descricao}"/>
                </rich:column>
                <rich:column
                        id="item-professor-excecao"
                        width="200px"
                        sortable="true"
                        selfSorted="true"
                        sortBy="#{item.colaboradorVO.pessoa.nome}"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Colaborador"/>
                    </f:facet>
                    <h:outputText value="#{item.colaboradorVO.pessoa.nome}"/>
                </rich:column>
                <rich:column
                        id="item-ambiente-excecao"
                        width="120px"
                        sortable="true"
                        selfSorted="true"
                        sortBy="#{item.ambienteVO.descricao}"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Ambiente"/>
                    </f:facet>
                    <h:outputText value="#{item.ambienteVO.descricao}"/>
                </rich:column>
                <rich:column
                        id="item-status-excecao"
                        width="90px"
                        sortable="true"
                        selfSorted="true"
                        sortBy="#{item.resolvidoComo}"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Status"/>
                    </f:facet>
                    <h:outputText
                            value="#{item.resolvidoComo == '0' ? 'Pendente' : item.resolvidoComo == '1' ? 'Remarca��o' : 'Prescreveu Remarca��o'}"/>
                </rich:column>
                <rich:column
                        id="item-dataHoraNovoAgendamento"
                        sortable="true"
                        selfSorted="true"
                        sortBy="#{item.dataNovoAgendamento}"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Data Remarca��o"/>
                    </f:facet>
                    <h:outputText value="#{item.dataNovoAgendamento}" converter="dataConverter" escape="false"/> -
                    <h:outputText value="#{item.horaNovoAgendamento}" converter="timeConverter"/>
                </rich:column>
                <rich:column
                        id="item-tipoHorarioVO-excecao"
                        sortable="true"
                        selfSorted="true"
                        sortBy="#{item.tipoHorarioVO.descricao}"
                        filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Tipo Hor�rio"/>
                    </f:facet>
                    <h:outputText value="#{item.tipoHorarioVO.descricao}"/>
                </rich:column>

                <rich:column
                        width="30px"
                        id="item-pendente">
                    <h:selectBooleanCheckbox
                            id="itemSolicitacao-selecionado-pendente"
                            value="#{item.selecionarPendente}">
                        <a4j:support
                                action="#{clienteEstudioControle.acaoSelecionarUmPendente}"
                                event="onclick"
                                status="statusHora"
                                ajaxSingle="true"
                                reRender="clienteEstudioControle-listaAgendaExcecao-modal">
                            <f:setPropertyActionListener
                                    value="#{item}"
                                    target="#{clienteEstudioControle.agendaEstudio}"/>
                        </a4j:support>
                    </h:selectBooleanCheckbox>
                </rich:column>
            </rich:dataTable>
        </h:panelGroup>
    </h:panelGrid>


    <rich:spacer height="05px"/>
    <h:panelGrid style="position: relative; float:left; ">
        <a4j:commandButton
                image="/imagens/estudio/fechar.png"
                value="Fechar"
                oncomplete="#{rich:component('modalAgendaExcecao')}.hide();"
                title="Fechar" status="statusHora"/>
    </h:panelGrid>

    <h:panelGrid style="position: relative; float:right; ">
        <a4j:commandButton
                id="btnRemarcar2"
                image="/imagens/estudio/remarcar.png"
                value="Remarcar"
                reRender="panelAgendaAluno,modalPanelErro"
                ajaxSingle="true"
                action="#{clienteEstudioControle.acaoPanelAgendaAluno}"
                oncomplete="#{rich:component('modalAgendaExcecao')}.hide(); "
                title="Remarcar" status="statusHora">
        </a4j:commandButton>
    </h:panelGrid>
</rich:modalPanel>


<rich:modalPanel id="modalPanelErro" autosized="true" shadowOpacity="true"
                 showWhenRendered="#{clienteEstudioControle.apresentarRichModalErro}" width="450"
                 height="80" onshow="focusAt('fecharButton');">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Aten��o!"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">

        <h:panelGrid columns="1" width="100%">
            <h:outputText styleClass="mensagemDetalhada" value="#{clienteEstudioControle.mensagemDetalhada}"/>
        </h:panelGrid>
    </h:panelGrid>
    <rich:spacer height="05px"/>
    <h:panelGrid style="position: relative; float:right; ">
        <a4j:commandButton
                image="/imagens/estudio/fechar.png"
                id="fecharButton"
                value="Fechar"
                ajaxSingle="true"
                title="Fechar" status="statusHora"
                action="#{clienteEstudioControle.acaoFecharModalErro}" reRender="modalPanelErro"/>
    </h:panelGrid>
</rich:modalPanel>