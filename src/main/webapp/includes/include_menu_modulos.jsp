<%-- 
    Document   : include_menu_modulos
    Created on : 06/06/2012, 15:07:07
    Author     : <PERSON>
    Changed on : 30/08/2013
    By         : <PERSON>
--%>
<%@include file="/include_imports.jsp" %>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<style>
    #menuModulos a {
        color: black;
    }
    #menuModulos a:hover {
        color: white;
    }
</style>


<rich:hotKey/>


<c:if test="${LoginControle.apresentarMenuModulos}">
    <h:panelGroup layout="block">
        <a4j:commandLink id="modZillyonWeb" action="#{LoginControle.abrirZillyonWeb}"
                         value="" styleClass="zwicon">
                <h:graphicImage url="/faces/beta/imagens/oldbutnew-topo-zw.png"/>
        </a4j:commandLink>

        <a4j:commandLink id="modCRMWeb" value="" actionListener="#{SkinControle.definirSkinCrm}"
                         rendered="#{LoginControle.apresentarLinkParaModuloCRM}"
                         action="#{LoginControle.abrirModuloCRM}" styleClass="crmicon">
            <h:graphicImage url="/faces/beta/imagens/oldbutnew-topo-crm.png"/>
        </a4j:commandLink>

        <a4j:commandLink id="modEnotas" value="eNotas"
                         rendered="#{LoginControle.apresentarNotaFiscal}"
                         action="#{LoginControle.abrirModuloNotaFiscal}" styleClass="notasicon">
            <h:graphicImage url="/faces/beta/imagens/oldbutnew-topo-nfe.png"/>
        </a4j:commandLink>

        <a4j:commandLink id="modFinanceiroWeb" rendered="#{LoginControle.apresentarLinkFinanceiro}"
                         action="#{LoginControle.abrirModuloFinanceiro}"
                         value=""
                         actionListener="#{SkinControle.definirSkinFinanceiro}" styleClass="finanicon">
            <h:graphicImage url="/faces/beta/imagens/oldbutnew-topo-finan.png"/>
        </a4j:commandLink>
        <a4j:commandLink id="pannerFinan"
                         onclick="abrirPopup('https://app.pactosolucoes.com.br/banner/popup_financeiro.jpg', 'Novo Módulo Financeiro', 760, 590);"
                         rendered="#{!LoginControle.apresentarLinkFinanceiro}" styleClass="finanicond">
            <h:graphicImage url="/faces/beta/imagens/oldbutnew-topo-finan-desat.png"/>
        </a4j:commandLink>

        <a4j:commandLink id="modCE" rendered="#{LoginControle.apresentarLinkCE}"
                         value=""
                         oncomplete="#{LoginControle.msgAlert}"
                         action="#{LoginControle.abrirCE}" styleClass="ceicon">
            <h:graphicImage url="/faces/beta/imagens/oldbutnew-topo-ce.png"/>
        </a4j:commandLink>


        <a4j:commandLink id="pannerCE"
                         onclick="/*abrirPopup('/faces/beta/imagens/oldbutnew-topo-ce.png', 'Módulo Central de Eventos', 760, 590);*/console.log('ce')"
                         rendered="#{!LoginControle.apresentarLinkCE}" styleClass="ceicond">
            <h:graphicImage url="/faces/beta/imagens/oldbutnew-topo-ce-desat.png"/>
        </a4j:commandLink>
        <h:outputLink value="#{LoginControle.abrirTreino}" rendered="#{LoginControle.apresentarLinkTREINO && LoginControle.validarTreinoLoginApp}" styleClass="finanicond">
            <h:graphicImage url="/faces/beta/imagens/logos_treino.png" width="28" height="24"/>
        </h:outputLink>
        <h:outputLink value="#{LoginControle.abrirAulaCheia}" rendered="#{LoginControle.apresentarLinkAulaCheia && LoginControle.validarTreinoLoginApp}" styleClass="finanicond">
            <h:graphicImage url="/faces/beta/imagens/logos_turmacheia.png" width="28" height="24"/>
        </h:outputLink>
    </h:panelGroup>
</c:if>
