<%@page pageEncoding="ISO-8859-1" %>

<%--
    Created on : 16/11/2023, 16:05:03
    Author     : Estulano
--%>

<%@include file="/includes/imports.jsp" %>

<%--  MODAL SOLICITAÇÃO DE TOKEN PARA GRAVAR OPERAÇÕES--%>
<rich:modalPanel id="modalTokenOperacaoExplicacao" domElementAttachment="parent"
                 autosized="true" shadowOpacity="false" width="650"
                 styleClass="novaModal">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Passo a passo para autenticação"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formModalTokenOperacaoExplicacao" ajaxSubmit="true">

        <h:panelGrid columns="1">
            <h:graphicImage value="/imagens/explicacao-token-operacao.jpg" style="width: 87%;"
                            id="hideListaClientes"/>
        </h:panelGrid>

        <h:panelGroup layout="block">
            <h:outputText style="margin-left: 52px; font-family: Arial; font-size: 15px; margin-top: 10px" value="Ainda com dúvidas? "/>
            <h:outputLink value="#{SuperControle.urlBaseConhecimento}token-de-autenticacao-no-pacto-app/"
                          style="font-family: Arial; font-size: 15px; color: #0090FF; margin-top: 10px"
                          target="_blank">Clique aqui e conheça o recurso</h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" id="divBtnValidar" style="padding: 8px; margin-top: 15px;">
            <a4j:commandLink style="margin-left: 42px; font-size: 14px!important; padding: 8px 25px;!important;"
                             styleClass="botoes nvoBt btSec"
                             value="Voltar"
                             oncomplete="Richfaces.hideModalPanel('modalTokenOperacaoExplicacao')">
            </a4j:commandLink>
        </h:panelGroup>

    </a4j:form>
</rich:modalPanel>
