<%-- 
    Document   : include_bi_indiceconversaofiltrotipo
    Created on : 09/08/2012, 13:54:51
    Author     : carla
--%>
<%@include file="imports.jsp" %>


<rich:modalPanel id="panelFiltro" width="350"   styleClass="novaModal" autosized="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Filtro"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelink"/>
            <rich:componentControl for="panelFiltro" attachTo="hidelink" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <h:form id="formPanelFiltroICVTipo">

        <h:panelGroup layout="block" style="width: 100%" styleClass="paginaFontResponsiva">


            <h:panelGroup layout="block" style="display: inline-block; padding-top: 5px;width: 130px; width: 48%;">
                <h:panelGroup layout="block" style=";margin: 0 auto;margin-bottom:5px;">
                    <h:outputText styleClass="rotuloCampos" value="BV's"/>
                </h:panelGroup>
                <h:panelGroup layout="block" style="display: block;" styleClass="chk-fa-container">
                    <h:selectBooleanCheckbox id="matriculas" value="#{IndiceConversaoVendaRelControle.filtroMatriculas}"/>
                    <span></span>
                    <h:outputText styleClass="texto-size-16 texto-cor-cinza" style="vertical-align: super" value="Matr�culas"/>
                </h:panelGroup>
                <h:panelGroup layout="block" style="display: block;" styleClass="chk-fa-container">
                    <h:selectBooleanCheckbox id="rematriculas" value="#{IndiceConversaoVendaRelControle.filtroRematriculas}"/>
                    <span></span>
                    <h:outputText styleClass="texto-size-16 texto-cor-cinza" style="vertical-align: super" value="Rematr�culas"/>
                </h:panelGroup>
                <h:panelGroup layout="block" style="display: block;" styleClass="chk-fa-container">
                    <h:selectBooleanCheckbox id="retornos" value="#{IndiceConversaoVendaRelControle.filtroRetornos}"/>
                    <span></span>
                    <h:outputText styleClass="texto-size-16 texto-cor-cinza" style="vertical-align: super" value="Retornos"/>
                </h:panelGroup>

            </h:panelGroup>

            <h:panelGroup layout="block" style="text-align: left; display: inline-block;vertical-align: top;padding-top: 5px; width: 48%;">
                <h:panelGroup layout="block" style="margin: 0 auto;margin-bottom:5px;">
                    <h:outputText styleClass="rotuloCampos" style="text-align: left" value="Tipos de Contrato"/>
                </h:panelGroup>
                <h:panelGroup layout="block" style="display: block;" styleClass="chk-fa-container">
                    <h:selectBooleanCheckbox id="espontaneos"  value="#{IndiceConversaoVendaRelControle.filtroEspontaneo}"/>

                    <span></span>
                    <h:outputText styleClass="texto-size-16 texto-cor-cinza" style="vertical-align: super" value="Espont�neos"/>
                </h:panelGroup>
                <h:panelGroup layout="block" style="display: block;" styleClass="chk-fa-container">
                    <h:selectBooleanCheckbox id="agendados" value="#{IndiceConversaoVendaRelControle.filtroAgendado}"/>
                    <span></span>
                    <h:outputText styleClass="texto-size-16 texto-cor-cinza" style="vertical-align: super" value="Agendados"/>
                </h:panelGroup>
            </h:panelGroup>


            <h:panelGroup layout="block" style="display: inline-block; padding-top: 5px;width: 130px; width: 48%;">
                <h:panelGroup layout="block" style=";margin: 0 auto;margin-bottom:5px;">
                    <h:outputText styleClass="rotuloCampos" style="padding-top: 5px;" value="Plano Bolsa"/>
                </h:panelGroup>
                <h:panelGroup layout="block" style="display: block;" styleClass="chk-fa-container">
                    <h:selectBooleanCheckbox id="bolsistas" value="#{IndiceConversaoVendaRelControle.incluirBolsistas}"/>
                    <span></span>
                    <h:outputText styleClass="texto-size-16 texto-cor-cinza" style="vertical-align: super" value="Considerar plano bolsa"/>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" style="text-align: left; display: inline-block;vertical-align: top;padding-top: 5px; width: 48%;">
                <h:panelGroup layout="block" style="margin: 0 auto;margin-bottom:5px;">
                    <h:outputText styleClass="rotuloCampos" style="text-align: left" value="Origem"/>
                </h:panelGroup>
                <h:panelGroup layout="block" style="display: block;" styleClass="chk-fa-container">
                    <h:selectBooleanCheckbox value="#{IndiceConversaoVendaRelControle.filtroOrigemSistema}"/>
                    <span></span>
                    <h:outputText styleClass="texto-size-16 texto-cor-cinza" style="vertical-align: super" value="Origem sistema"/>
                </h:panelGroup>
                <h:panelGroup layout="block" style="display: block;" styleClass="chk-fa-container">
                    <h:selectBooleanCheckbox  value="#{IndiceConversaoVendaRelControle.filtroOrigemSite}"/>
                    <span></span>
                    <h:outputText styleClass="texto-size-16 texto-cor-cinza" style="vertical-align: super" value="Origem site"/>
                </h:panelGroup>
            </h:panelGroup>


            <h:panelGroup layout="block" style="display: inline-block;vertical-align: top;padding-top: 5px; width: 98%;">
                <h:panelGroup layout="block" style="margin: 0 auto;margin-bottom:5px;">
                    <h:outputText styleClass="rotuloCampos" value="Gympass"/>
                </h:panelGroup>
                <h:panelGroup layout="block" style="display: block;" styleClass="chk-fa-container">
                    <h:selectBooleanCheckbox id="desconsiderarGympass" value="#{IndiceConversaoVendaRelControle.filtroDesconsiderarGympass}"/>
                    <span></span>
                    <h:outputText styleClass="texto-size-16 texto-cor-cinza" style="vertical-align: super" value="Desconsiderar alunos gympass"/>
                </h:panelGroup>
            </h:panelGroup>


            <h:panelGroup layout="block" style="display: inline-block;vertical-align: top;padding-top: 5px; width: 98%;">
                <h:panelGroup layout="block" style="margin: 0 auto;margin-bottom:5px;">
                    <h:outputText styleClass="rotuloCampos" value="Evento"/>
                </h:panelGroup>
                <h:panelGroup  layout="block" style="margin-bottom:5px; max-height: 150px; overflow:auto;  border-radius: 5px;" styleClass="paginaFontResponsiva">
                    <c:forEach var="objEvento" items="#{IndiceConversaoVendaRelControle.listaEvento}">
                        <h:panelGroup layout="block" style="display: block;" styleClass="chk-fa-container">
                            <h:selectBooleanCheckbox  value="#{objEvento.marcado}">
                            </h:selectBooleanCheckbox>
                            <span></span>
                            <h:outputText styleClass="texto-size-16 texto-cor-cinza" style="vertical-align: super" value="#{objEvento.descricao}"/>
                        </h:panelGroup>
                    </c:forEach>
                </h:panelGroup>
            </h:panelGroup>



        </h:panelGroup>
        <br>

        <h:panelGroup layout="block" styleClass="container-botoes">
                <a4j:commandLink
                               reRender="containerConversao"
                               oncomplete="carregarGragicoICV(); Richfaces.hideModalPanel('panelFiltro');"
                               title="Consultar Dados Indice de Convers�o de Vendas"
                               action="#{IndiceConversaoVendaRelControle.atualizar}"
                               style="vertical-align:middle;line-height: 50px"
                               styleClass="botaoPrimario texto-size-16">
                    <span class="texto-size-16 texto-font">Atualizar </span>
                    <i style="vertical-align: inherit" class="fa-icon-refresh texto-size-20 texto-cor-branco">
                </a4j:commandLink>
        </h:panelGroup>

    </h:form>
</rich:modalPanel>
<script type="text/javascript">

    function atualizarTable(){
        //alert('atualizar table');
        //alert($('.tableChekBox td'));
        /*$('.tableChekBox td').each(function () {
            $('.divCheck')
                .clone()
                .appendTo(this)
                .show();
        });*/
    }


</script>



