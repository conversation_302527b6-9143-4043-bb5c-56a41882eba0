<h:panelGroup id="painelCappta">
    <script>

    </script>

    <h:panelGroup>


        <script>
            //Fun��o de mock para teste local e com QA.
            //Se precisar usar, altera os dados abaixo conforme a necessidade e descomenta a chamada dessa fun��o no arquivo MovPagamentoControle - abrirPinpadCappta
            function mockDadosTesteLinx(numeroPagamento) {
                console.log("ENTROU FUNCAO MOCK VALOR: " + numeroPagamento);

                let param1 = '';
                let param2 = "";
                let param3 = '';

                if (numeroPagamento === 1) {
                    param1 = '\n\n              REDE GETNET\n\nCALI FIT ACADEMIA CONS\nCNPJ: 26.519.120/0002-74\n25/03/25 10:01:57 AUT:794991 DOC:000147\nEC:000000005170393 TERM: TF106963    S\nCV:001000030  CAIXA:00000001 L:04343673\nARQC: C628AEF2C3A1068F\nAID: A0000000031010                  \nVISA                ************7418\n\n                                \n            CREDITO A VISTA\nVALOR: R$        169,90\n\n\n         \n \n\n&\n\n        (CUPOM FISCAL: 000000)\n        (NSU D-TEF   : 000147)\n\n\n\n\n\n\n\"';
                    param2 = "001000030";
                    param3 = '{\"administrativeCode\":\"000147\",\"acquirerAffiliationKey\":\"\",\"acquirerAuthorizationCode\":\"794991\",\"acquirerAuthorizationDateTime\":\"2025-03-25T09:03:25\",\"acquirerName\":\"GetNet LAC\",\"acquirerUniqueSequentialNumber\":\"001000030\",\"uniqueSequentialNumber\":\"001000030\",\"paymentProductName\":\"Transa��o de Pagamento com Cart�o de Cr�dito\",\"cardBrandCode\":250,\"cardBrandCodeLinx\":1,\"cardBrandName\":\"Visa\",\"cardExpirationDate\":\"2029-10-01T00:00:00\",\"cardHolderName\":\"PAYWAVE/VISA\",\"cardTokenDetails\":\"\",\"customerCardLastFourDigits\":\"7418\",\"customerCardPan\":\"498401******7418\",\"installments\":\"00\",\"paymentAmount\":169.9,\"cardOperator\":\"0233\",\"acquirerCode\":\"0026\",\"receipt\":{\"merchantReceipt\":\"\\n\\n              REDE GETNET\\n\\nCALI FIT ACADEMIA CONS\\nCNPJ: 26.519.120/0002-74\\n25/03/25 10:01:57 AUT:794991 DOC:000147\\nEC:000000005170393 TERM: TF106963    S\\nCV:001000030  CAIXA:00000001 L:04343673\\nARQC: C628AEF2C3A1068F\\nAID: A0000000031010                  \\nVISA                ************7418\\n\\n                                \\n            CREDITO A VISTA\\nVALOR: R$        169,90\\n\\n\\n         \\n \\n\\n&\\n\\n        (CUPOM FISCAL: 000000)\\n        (NSU D-TEF   : 000147)\\n\\n\\n\\n\\n\\n\\n\",\"customerReceipt\":\"\\n\\n              REDE GETNET\\n\\nCALI FIT ACADEMIA CONS\\nCNPJ: 26.519.120/0002-74\\n25/03/25 10:01:57 AUT:794991 DOC:000147\\nEC:000000005170393 TERM: TF106963    S\\nCV:001000030  CAIXA:00000001 L:04343673\\nARQC: C628AEF2C3A1068F\\nAID: A0000000031010                  \\nVISA                ************7418\\n\\n                                \\n            CREDITO A VISTA\\nVALOR: R$        169,90\\n\\n\\n         \\n \\n\\n&\\n\\n        (CUPOM FISCAL: 000000)\\n        (NSU D-TEF   : 000147)\\n\\n\\n\\n\\n\\n\\n\",\"reducedReceipt\":\"\"}}';
                }
                if (numeroPagamento === 2) {
                    param1 = '\n              REDE GETNET\n\nCALI FIT ACADEMIA CONS\nCNPJ: 26.519.120/0002-74\n25/03/25 10:16:03 AUT:M96422 DOC:000149\nEC:000000005170393 TERM: TF106963    C\nCV:001000032  CAIXA:00000001 L:04343673\nARQC: EB6B9D9EEB3574B0\nAID: A0000000043060                  \nMASTERCARD          ************8144\n\n                                \n             DEBITO A VISTA\nVALOR: R$        139,90\n\n\n          \n \n APROVADA MEDIANTE USO DE SENHA PESSOAL\n\n\n        (CUPOM FISCAL: 000000)\n        (NSU D-TEF   : 000149)\n\n\n\n\"';
                    param2 = "001000032";
                    param3 = '{\"administrativeCode\":\"000149\",\"acquirerAffiliationKey\":\"\",\"acquirerAuthorizationCode\":\"M96422\",\"acquirerAuthorizationDateTime\":\"2025-03-25T09:03:41\",\"acquirerName\":\"GetNet LAC\",\"acquirerUniqueSequentialNumber\":\"001000032\",\"uniqueSequentialNumber\":\"001000032\",\"paymentProductName\":\"Transa��o de Pagamento com Cart�o de D�bito\",\"cardBrandCode\":125,\"cardBrandCodeLinx\":2,\"cardBrandName\":\"Mastercard\",\"cardExpirationDate\":\"2026-05-01T00:00:00\",\"cardHolderName\":\"SOUZA/GISELLY FALCAO\",\"cardTokenDetails\":\"\",\"customerCardLastFourDigits\":\"8144\",\"customerCardPan\":\"589916******8144\",\"installments\":\"00\",\"paymentAmount\":139.9,\"cardOperator\":\"0111\",\"acquirerCode\":\"0026\",\"receipt\":{\"merchantReceipt\":\"\\n              REDE GETNET\\n\\nCALI FIT ACADEMIA CONS\\nCNPJ: 26.519.120/0002-74\\n25/03/25 10:16:03 AUT:M96422 DOC:000149\\nEC:000000005170393 TERM: TF106963    C\\nCV:001000032  CAIXA:00000001 L:04343673\\nARQC: EB6B9D9EEB3574B0\\nAID: A0000000043060                  \\nMASTERCARD          ************8144\\n\\n                                \\n             DEBITO A VISTA\\nVALOR: R$        139,90\\n\\n\\n          \\n \\n APROVADA MEDIANTE USO DE SENHA PESSOAL\\n\\n&\\n\\n        (CUPOM FISCAL: 000000)\\n        (NSU D-TEF   : 000149)\\n\\n\\n\\n\",\"customerReceipt\":\"\\n              REDE GETNET\\n\\nCALI FIT ACADEMIA CONS\\nCNPJ: 26.519.120/0002-74\\n25/03/25 10:16:03 AUT:M96422 DOC:000149\\nEC:000000005170393 TERM: TF106963    C\\nCV:001000032  CAIXA:00000001 L:04343673\\nARQC: EB6B9D9EEB3574B0\\nAID: A0000000043060                  \\nMASTERCARD          ************8144\\n\\n                                \\n             DEBITO A VISTA\\nVALOR: R$        139,90\\n\\n\\n          \\n \\n APROVADA MEDIANTE USO DE SENHA PESSOAL\\n\\n\\n        (CUPOM FISCAL: 000000)\\n        (NSU D-TEF   : 000149)\\n\\n\\n\\n\",\"reducedReceipt\":\"\"}}';
                }

                concluirPinpad(param1, param2, param3);
            }
        </script>
        <!-- https://linxpaykitapi.linx.com.br/LinxPaykitApi/paykit-checkout.js -->
        <!-- A lib abaixo � referente ao endere�o acima e foi adiciona local no dia 09/12/2024. -->
        <!-- Tendo uma nova vers�o, precisa criar ou trocar o arquilo local. -->
        <script type="text/javascript" src="script/linx-checkout.js"></script>

        <style>
            #cappta-checkout-iframe {
                z-index: 999999;
                display: none;
            }
            .BrandCappta{
                display: none !important;
            }

        </style>

        <script>
            var authenticationRequest = {
                authenticationKey: '${MovPagamentoControle.authenticationKeyCappta}'
            };
            var onAuthenticationSuccess = function (response) {
                console.log(response);
                document.getElementById('resposta').innerHTML = 'Pinpad Autenticado com sucesso' + '<br>' + 'Checkout GUID: ' + response.merchantCheckoutGuid;
            };
            var onAuthenticationError = function (response) {
                console.log(response);
                document.getElementById('resposta').innerHTML = 'C�digo: ' + response.reasonCode + '<br>' + response.reason;
            };
            var onPendingPayments = function (response) {
                console.log(response);
            };
            var checkout;
            function fazerCheckout(){
                if (checkout == null) {
                    checkout = CapptaCheckout.authenticate(authenticationRequest, onAuthenticationSuccess, onAuthenticationError, onPendingPayments);
                }
            }
            var multiplePaymentsSessionInProgress = false;

            function canStartMultiplePaymentsSession() {
                return multiplePaymentsSessionInProgress === false && $('input[name="rbMultiplePayments"]:checked').val() === 'true';
            }

            function startMultiplePayments() {
                try {
                    var numberOfPayments = parseInt(document.getElementById('txtNumberOfPayments').value);

                    checkout.startMultiplePayments(numberOfPayments, function () {
                        alert('Sess�o multiplos pagamentos encerrada!');
                        document.getElementById('txtNumberOfPayments').value = 0;
                        handlerMultiplePaymentsElements(false);

                    });

                    multiplePaymentsSessionInProgress = true;
                    handlerMultiplePaymentsElements(true);
                } catch (ex) {
                    alert(ex);
                }
            }

            function handlerMultiplePaymentsElements(disabled) {
                document.getElementById('txtNumberOfPayments').disabled = disabled;
                document.getElementById('rbUseMultiplePayments').disabled = disabled;
                document.getElementById('rbNotUseMultiplePayments').disabled = disabled;
            }


            var onPaymentError = function (response) {
                console.log(response);
                document.getElementById('resposta').innerHTML = 'C�digo: ' + response.reasonCode + '<br>' + response.reason;
                erroPagamentoPinpad(JSON.stringify(response));
                jQuery('#cappta-checkout-iframe').hide();
            };


            function splittedDebitPayment() {
                if (canStartMultiplePaymentsSession()) {
                    startMultiplePayments();
                }

                var splittedDebitRequest = {
                    amount: parseFloat(document.getElementById('txtSplittedDebitAmount').value.replace(',', '')),
                    installments: document.getElementById('txtSplittedDebitInstallments').value
                };

                checkout.splittedDebitPayment(splittedDebitRequest, onPaymentSuccess, onPaymentError);
            }

            function selectInstallmenteType(value) {
                if (value) {
                    document.getElementById('installmentDetails').classList.add('show');
                    return;
                }
                document.getElementById('installmentDetails').classList.remove('show');
            }

            function paymentReversal() {

                var paymentReversalRequest = {
                    administrativePassword: document.getElementById('administrativePassword').value,
                    administrativeCode: document.getElementById('administrativeCode').value
                };

                CapptaCheckout.paymentReversal(paymentReversalRequest, onPaymentSuccess, onPaymentError);
            }

            function pinpadInput() {
                var elInputType = document.getElementById("pinpadInputType");
                var inputType = elInputType.options[elInputType.selectedIndex].value;

                var success = function (response) {
                    updateResult(response.pinpadValue);
                };

                var error = function (response) {
                    updateResult(response.reason);
                };

                checkout.getPinpadInformation({inputType: inputType}, success, error);
            }

            function confirmPayments() {
                multiplePaymentsSessionInProgress = false;

                checkout.confirmPayments();

                alert('Pagamentos confirmados com sucesso!');
            }

            function undoPayments() {
                multiplePaymentsSessionInProgress = false;

                checkout.undoPayments();

                alert('Pagamentos desfeitos com sucesso!');
            }

            function updateResult(message) {
//    document.getElementById('resposta').innerHTML = message;
            }

            $(function () {

                $('#rbUseMultiplePayments').prop('checked', false);
                $('#rbNotUseMultiplePayments').prop('checked', true);

                $('#txtDebitAmount').maskMoney();
                $('#txtCreditAmount').maskMoney();
                $('#txtSplittedDebitAmount').maskMoney();

                $('input[name=rbMultiplePayments]').change(function () {
                    var isMultiplePayments = this.value === 'true' ? true : false;

                    if (isMultiplePayments) {
                        document.getElementById('txtNumberOfPayments').classList.remove('hide');
                        document.getElementById('multiplePaymentsButtons').classList.remove('hide');
                        return;
                    }

                    document.getElementById('txtNumberOfPayments').classList.add('hide');
                    document.getElementById('multiplePaymentsButtons').classList.add('hide');
                    multiplePaymentsSessionInProgress = false;
                });
            });


            function start() {
                // document.getElementById('resposta').innerHTML = 'Aguardando Pinpad...';
            }
            start();

            var onPaymentSuccess = function (response) {
                // console.log(response);
                var bandeira = document.getElementById('form:bandeiraCappta');
                if (bandeira != null) {
                    bandeira.value = response.cardBrandCode;
                }
                concluirPinpad(response.receipt.customerReceipt, response.uniqueSequentialNumber, JSON.stringify(response));
                jQuery('#cappta-checkout-iframe').hide();
            };

            function openWin(conteudo) {
                var divText = '<div style="text-align: center;">'+conteudo+'</div>';
                var myWindow = window.open('', '', 'width=600,height=800');
                var doc = myWindow.document;
                doc.open();
                doc.write(divText);
                doc.close();
                myWindow.onfocus= myWindow.print();
            }

            function matarConexaoWs() {
                if(document.getElementById('cappta-checkout-iframe')){
                    document.getElementById('cappta-checkout-iframe').remove();
                }
                document.getElementById('resposta').innerHTML = 'C�digo: 9' + '<br>' + 'Conex�o iniciada.' + '<br>' + 'Escolha um PDV!';
                if (ws) {
                    ws.close();
                }
            }

        </script>

    </h:panelGroup>
    <h:panelGroup id="painelCapptaPayment" rendered="#{MovPagamentoControle.pinpad.pinpadCappta}">
        <script>

            function debitPayment() {
                var amount = parseFloat(${MovPagamentoControle.pinpad.valorPinpad});
                checkout.debitPayment({amount: amount}, onPaymentSuccess, onPaymentError);
                jQuery('#cappta-checkout-iframe').show();
            }

            function creditPayment() {
                var installmentType = ${MovPagamentoControle.pinpad.tipo};
                var installments = '${MovPagamentoControle.pinpad.nrParcelas}';
                var creditRequest = {
                    amount: parseFloat(${MovPagamentoControle.pinpad.valorPinpad}),
                    installments: installments === '' ? 0 : installments,
                    installmentType: installmentType
                };

                checkout.creditPayment(creditRequest, onPaymentSuccess, onPaymentError);
                jQuery('#cappta-checkout-iframe').show();
            }
        </script>

    </h:panelGroup>
</h:panelGroup>
