<%--
    Document   : include_bi_indicerenovacao
    Created on : 03/02/2011, 16:32:31
    Author     : Waller
--%>
<%@include file="../imports.jsp" %>
<style>
    .special tr[id] {
        display: none;
    }

    .special tooltipster-grow-show {
        display: none;
    }
</style>
<h:panelGroup layout="block" id="biIndiceRenovacao"
              rendered="#{LoginControle.permissaoAcessoMenuVO.indiceRenovacao && !BIControle.configuracaoBI.indiceRenovacao.naLixeira}"
              styleClass="container-bi">
    <c:if test="${(LoginControle.permissaoAcessoMenuVO.indiceRenovacao || LoginControle.usuarioLogado.administrador) && (!BIControle.configuracaoBI.indiceRenovacao.naLixeira)}">
        <a4j:commandLink action="#{BIControle.carregarRenovacao}" reRender="containerRenovacao"
                         onclick="abrirCarregandoPoBI(this)" oncomplete="hideCarregandoPorBI(this)"
                         status="nenhumStatus"
                         styleClass="btn-atualizar-bi"/>


        <h:panelGroup layout="block" id="containerRenovacao">
            <h:panelGroup layout="block"
                          rendered="#{(BIControle.configuracaoBI.indiceRenovacao.apresentarBoxCarregar) && !BIControle.biCarregado}"
                          styleClass="bi-unloaded">
                <h:panelGroup styleClass="bi-header" layout="block">
                    <h:panelGroup layout="block" styleClass="bi-panel">
                        <h:outputText value="�ndice Renova��o" styleClass="bi-titulo pull-left"/>
                    </h:panelGroup>
                </h:panelGroup>

                <div class="ghost">
                    <table>
                        <tr>
                            <td style="width: 50%"><h3 class="card-title loading" style="height: 60px"></h3></td>
                            <td><h3 class="card-title loading" style="height: 60px"></h3></td>
                        </tr>
                        <tr>
                            <td style="width: 50%;"><h3 class="card-title loading" style="height: 60px"></h3></td>
                            <td><h3 class="card-title loading" style="height: 60px"></h3></td>
                        </tr>
                    </table>
                </div>
            </h:panelGroup>
            <h:panelGroup layout="block"
                          rendered="#{!BIControle.configuracaoBI.indiceRenovacao.apresentarBoxCarregar || BIControle.biCarregado}">
                <h:panelGroup styleClass="bi-header" layout="block">
                    <h:panelGroup layout="block" styleClass="bi-panel">
                        <h:outputText id="labelIndiceMes" value="�ndice Renova��o M�s "
                                      styleClass="bi-titulo pull-left"/>
                        <h:outputText id="labelMesIndice" value="#{RenovacaoSinteticoControle.mesApresentaDataBase}"
                                      styleClass="bi-titulo pull-left"/>

                        <h:outputLink styleClass="tooltipster linkWiki bi-cor-cinza bi-left-align"
                                      style="float: left;margin-top: 4.4%;margin-left: 2%"
                                      value="#{SuperControle.urlBaseConhecimento}bi-indice-renovacao-mes-adm/"
                                      title="Clique e saiba mais: �ndice de Renova��o do M�s" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px; margin-top: -0.4em !important"></i>
                        </h:outputLink>

                        <a4j:commandLink id="consultarRenovacao"
                                         reRender="containerRenovacao"
                                         oncomplete="montarTips();"
                                         action="#{RenovacaoSinteticoControle.carregarTela}">
                            <i title="Consultar Dados �ndice de Renova�ao"
                               class="tooltipster fa-icon-refresh bi-btn-refresh bi-link pull-right lineHeight-3em"></i>
                        </a4j:commandLink>

                        <h:panelGroup layout="block" styleClass="pull-right calendarSemInputBI col-text-align-right">
                            <div title="${RenovacaoSinteticoControle.dataBase_ApresentarMesAno}"
                                 id="divDataInicioRenovacao"
                                 style="display: inline-flex;margin-top: 1em;vertical-align: top;"
                                 class="tooltipster dateTimeCustom alignToRight">
                                <rich:calendar id="dataInicioRenovacao"
                                               value="#{RenovacaoSinteticoControle.dataBaseFiltro}"
                                               inputSize="8"
                                               showInput="false"
                                               inputClass="forcarSemBorda"
                                               buttonIcon="#{RenovacaoSinteticoControle.dataAlterada ? '/imagens_flat/icon-calendar-red.png' : '/imagens_flat/icon-calendar-check.png'}"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="false"
                                               todayControlMode="hidden"
                                               showFooter="false"
                                               styleClass="special"
                                               zindex="2">
                                    <a4j:support event="oncurrentdateselected"
                                                 action="#{RenovacaoSinteticoControle.acaoMudarMes}"
                                                 reRender="containerRenovacao"/>
                                </rich:calendar>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="pull-right calendarSemInputBI"
                                      style="margin-right: 12px;">
                            <a4j:commandLink oncomplete="Richfaces.showModalPanel('filtroConversaoColaborador')"
                                             action="#{BIControle.biAtualizarParam}"
                                             reRender="formPanelFiltroCol, tituloFiltro">
                                <i title="Filtrar Por Colaborador"
                                   class="tooltipster fa-icon-user bi-btn-refresh bi-link pull-right lineHeight-3em">
                                    <span class="badgeItem2Icon" data-bagde="${BIControle.qtdColIndiceRenov}"></span>
                                </i>
                                <a4j:actionparam name="biAtualizar" value="INDICE_RENOVACAO"></a4j:actionparam>
                                <a4j:actionparam name="biAtualizarAbrirConsulta"
                                                 value="biAtualizarAbrirConsulta"></a4j:actionparam>
                                <a4j:actionparam name="reRenderBi" value="containerRenovacao"></a4j:actionparam>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup styleClass="ir-container" layout="block">
                    <h:panelGroup styleClass="bi-panel col-text-align-left" style="margin-left: 2.5%;width: 94%"
                                  layout="block">
                        <h:outputText styleClass="bi-titulo icv-periodo-text" value="PREVIS�O"/>
                        <h:outputText style="margin-left: 4px" styleClass="bi-font-family bi-table-text"
                                      value=" do in�cio ao fim do m�s"/>
                        <h:outputText styleClass="bi-font-family bi-table-text pull-right"
                                      value="#{RenovacaoSinteticoControle.primeiroTitulo}"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block">
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{RenovacaoSinteticoControle.mensagemDetalhada}"/>
                    </h:panelGroup>
                    <h:panelGrid id="panelPrevisaoRenovacao" columns="2" width="100%" bgcolor="white"
                                 columnClasses="colunaEsquerda">
                        <h:panelGrid columns="1" width="100%">
                            <h:panelGrid width="94%" style="margin-left: 3.5%;" columns="3" styleClass="panel-grid-icv"
                                         columnClasses="colunaEsquerda,col-text-align-center,colunaDireita">
                                <h:panelGroup>
                                    <h:outputText
                                            styleClass="bi-table-text tooltipster"
                                            value="Previs�o"
                                            title="#{RenovacaoSinteticoControle.title}"/>
                                    <h:outputText
                                            styleClass="bi-table-text bi-font-bold tooltipster #{RenovacaoSinteticoControle.alertaRenovadoAtivoPercentual ? 'texto-cor-verde' :  'texto-cor-vermelho'}"
                                            value=" #{RenovacaoSinteticoControle.previstoVencidoAtivoPercentual}"
                                            title="#{RenovacaoSinteticoControle.title}"/>
                                </h:panelGroup>
                                <h:panelGroup layout="block">
                                    <a4j:commandLink id="linkNumeroPrevisaoMes"
                                                     styleClass="linkPadrao texto-size-16 texto-cor-azul"
                                                     actionListener="#{RenovacaoSinteticoControle.apresentarAlunosPrevisaoMes}"
                                                     oncomplete="#{RenovacaoSinteticoControle.mensagemNotificar}#{RenovacaoSinteticoControle.msgAlert}"
                                                     value="#{RenovacaoSinteticoControle.qtdePrevista}"
                                                     rendered="#{RenovacaoSinteticoControle.qtdePrevista > 0}">
                                        <h:outputText styleClass="tooltipster" title="Ver Detalhes"/>
                                        <f:attribute name="tipo" value="previsao"/>
                                    </a4j:commandLink>
                                    <h:outputText value="0" id="linkNumeroPrevisaoMesZerado"
                                                  styleClass="linkPadrao texto-size-16 texto-cor-azul"
                                                  rendered="#{RenovacaoSinteticoControle.qtdePrevista <= 0}"/>
                                </h:panelGroup>
                                <h:outputText styleClass="bi-table-text" value="100,00%"/>
                                <%--previsao do mes--%>
                                <%--inicio renovados ate o dia ##/##--%>
                                <h:panelGroup>
                                    <h:outputText styleClass="bi-table-text tooltipster"
                                                  value="#{RenovacaoSinteticoControle.renovadosAteUltimoDia}"
                                                  title="#{RenovacaoSinteticoControle.title}"/>
                                </h:panelGroup>
                                <a4j:commandLink id="linkNumeroRenovados"
                                                 styleClass="linkPadrao texto-size-16 texto-cor-azul"
                                                 action="#{RenovacaoSinteticoControle.mostrarListaRenovadosPrevisaoMes}"
                                                 oncomplete="abrirPopup('./includes/bi/lista_indice_renovacao.jsp', 'ResumoPessoa', 850, 595);"
                                                 value="#{RenovacaoSinteticoControle.qtdeRenovada}">
                                    <h:outputText styleClass="tooltipster" title="Ver Detalhes"/>
                                </a4j:commandLink>

                                <h:outputText rendered="#{RenovacaoSinteticoControle.alertaRenovado}"
                                              styleClass="tituloCamposAzulGrandeNegrito"
                                              value="#{RenovacaoSinteticoControle.previstoRenovadoPercentual}"/>
                                <h:outputText rendered="#{!RenovacaoSinteticoControle.alertaRenovado}"
                                              styleClass="gr-text-warning bi-font-bold"
                                              value="#{RenovacaoSinteticoControle.previstoRenovadoPercentual}"/>
                                <%--renovados ate o dia ##/##--%>
                                <%--inicio nao renovados da previsao--%>
                                <h:panelGroup>
                                    <h:outputText styleClass="bi-table-text tooltipster"
                                                  value=" N�o Renovados da Previs�o"
                                                  title="#{RenovacaoSinteticoControle.title}"/>
                                </h:panelGroup>
                                <a4j:commandLink id="linkNumeroNaoRenovados"
                                                 styleClass="linkPadrao texto-size-16 texto-cor-azulMes"
                                                 action="#{RenovacaoSinteticoControle.mostrarListaNaoRenovadosPrevisaoMes}"
                                                 oncomplete="abrirPopup('./includes/bi/lista_indice_renovacao.jsp', 'ResumoPessoa', 850, 595);"
                                                 value="#{RenovacaoSinteticoControle.qtdeNaoRenovada}">
                                    <h:outputText styleClass="tooltipster" title="Ver Detalhes"/>
                                </a4j:commandLink>

                                <h:outputText styleClass="bi-table-text"
                                              value="#{RenovacaoSinteticoControle.previstoNaoRenovadoPercentual}"/>
                                <%--nao renovados da previsao--%>
                            </h:panelGrid>
                            <h:panelGrid width="94%" style="margin-left: 3.5%;" columns="3" styleClass="panel-grid-icv"
                                         columnClasses="colunaEsquerda,col-text-align-center,colunaDireita"
                                         rendered="#{RenovacaoSinteticoControle.mostrarSegundoTitulo}">
                                <%--inicio sub Titulo 2--%>
                                <h:panelGroup layout="block">
                                    <c:if test="${!empty RenovacaoSinteticoControle.dataUltimaRenovacao}">
                                        <h:outputText styleClass="bi-titulo icv-periodo-text texto-upper"
                                                      value="#{RenovacaoSinteticoControle.segundoTitulo}"/>
                                    </c:if>
                                </h:panelGroup>
                                <rich:spacer width="40"/>
                                <rich:spacer width="40"/>
                                <%--sub Titulo 2--%>
                                <%--inicio previsao do mes--%>
                                <h:panelGroup>
                                    <h:outputText styleClass="bi-table-text tooltipster" value=" Previs�o do M�s"
                                                  title="#{RenovacaoSinteticoControle.title}"/>
                                </h:panelGroup>
                                <a4j:commandLink styleClass="linkPadrao texto-size-16 texto-cor-azul"
                                                 actionListener="#{RenovacaoSinteticoControle.inicializarPrevisaoVindoDoBI}"
                                                 oncomplete="abrirPopup('renovacaoAnaliticoForm.jsp', 'Renova��o', 780, 595);"
                                                 value="#{RenovacaoSinteticoControle.qtdePrevista}">
                                    <f:attribute name="tipo" value="previsao"/>
                                    <h:outputText title="Ver Detalhes" styleClass="tooltipster"/>
                                </a4j:commandLink>
                                <h:outputText styleClass="bi-table-text" value="100,00%"/>
                                <%--previsao do mes--%>
                                <%--inicio renovados da previsao--%>
                                <h:panelGroup>
                                    <h:outputText styleClass="bi-table-text tooltipster" value=" Renovados da Previs�o"
                                                  title="#{RenovacaoSinteticoControle.title}"/>
                                </h:panelGroup>
                                <a4j:commandLink title="Ver Detalhes"
                                                 styleClass="linkPadrao texto-size-16 texto-cor-azul tooltipster"
                                                 actionListener="#{RenovacaoSinteticoControle.inicializarPrevisaoVindoDoBI}"
                                                 oncomplete="abrirPopup('renovacaoAnaliticoForm.jsp', 'Renova��o', 780, 595);"
                                                 value="#{RenovacaoSinteticoControle.qtdeRenovadaComTolerancia}">
                                    <f:attribute name="tipo" value="renovados"/>
                                </a4j:commandLink>

                                <h:outputText rendered="#{RenovacaoSinteticoControle.alertaRenovadoPercentual}"
                                              styleClass="gr-text-warning bi-font-bold"
                                              value="#{RenovacaoSinteticoControle.previstoRenovadoPercentualComTolerancia}"/>
                                <h:outputText rendered="#{!RenovacaoSinteticoControle.alertaRenovadoPercentual}"
                                              styleClass="gr-text-warning bi-font-bold"
                                              value="#{RenovacaoSinteticoControle.previstoRenovadoPercentualComTolerancia}"/>
                                <%--renovados da previsao--%>
                                <%--inicio nao renovados da previsao--%>
                                <h:panelGroup>
                                    <h:outputText styleClass="bi-table-text tooltipster"
                                                  value=" N�o Renovados da Previs�o"
                                                  title="#{RenovacaoSinteticoControle.title}"/>
                                </h:panelGroup>
                                <a4j:commandLink styleClass="linkPadrao texto-size-16 texto-cor-azulMes tooltipster"
                                                 title="Ver Detalhes"
                                                 actionListener="#{RenovacaoSinteticoControle.inicializarPrevisaoVindoDoBI}"
                                                 oncomplete="abrirPopup('renovacaoAnaliticoForm.jsp', 'Renova��o', 780, 595);"
                                                 value="#{RenovacaoSinteticoControle.qtdeNaoRenovadaComTolerancia}">
                                    <f:attribute name="tipo" value="naorenovados"/>
                                </a4j:commandLink>
                                <h:outputText styleClass="bi-table-text"
                                              value="#{RenovacaoSinteticoControle.previstoNaoRenovadoPercentualComTolerancia}"/>
                                <%--nao renovados da previsao--%>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGroup layout="block" styleClass="bi-separador"/>

                    <h:panelGroup styleClass="bi-panel col-text-align-left" style="margin-left: 2.5%;width: 94%"
                                  layout="block">
                        <h:outputText styleClass="bi-titulo icv-periodo-text" value="RENOVADOS"/>
                    </h:panelGroup>
                    <h:panelGrid id="panelPrevisaoResumo" styleClass="panel-grid-icv" columns="2" width="100%"
                                 bgcolor="white"
                                 columnClasses="colunaEsquerda, colunaEsquerda">
                        <h:panelGrid columns="1" width="100%">
                            <h:panelGrid width="94%" columns="3" style="margin-left: 3.5%;" styleClass="panel-grid-icv"
                                         columnClasses="colunaEsquerda,colunaDireita,colunaDireita">
                                <%--do mes atual--%>
                                <h:outputText styleClass="bi-table-text tooltipster"
                                              value="Do M�s"
                                              title="#{RenovacaoSinteticoControle.title}"/>
                                <a4j:commandLink id="linkRenovacoesDoMes"
                                                 styleClass="texto-font texto-size-16 texto-cor-azul tooltipster"
                                                 title="Ver Detalhes"
                                                 action="#{RenovacaoSinteticoControle.mostrarListaTotal}"
                                                 oncomplete="abrirPopup('./includes/bi/lista_indice_renovacao.jsp', 'ResumoPessoa', 850, 595);"
                                                 value="#{RenovacaoSinteticoControle.qtdeRenovadaTotal}"/>
                                <h:outputText styleClass="bi-table-text" value="100,00%"/>
                                <%--do mes atual--%>
                                <%--do mes passado--%>
                                <h:panelGroup>
                                    <h:outputText styleClass="bi-table-text tooltipster"
                                                  value="#{RenovacaoSinteticoControle.renovadosDoMesPassado}"
                                                  title="#{RenovacaoSinteticoControle.title}"/>
                                </h:panelGroup>
                                <a4j:commandLink id="linkRenovacoesMesPassado"
                                                 styleClass="texto-font texto-size-16 texto-cor-azul tooltipster"
                                                 title="Ver Detalhes"
                                                 action="#{RenovacaoSinteticoControle.mostrarListaMesPassado}"
                                                 oncomplete="abrirPopup('./includes/bi/lista_indice_renovacao.jsp', 'ResumoPessoa', 850, 595);"
                                                 value="#{RenovacaoSinteticoControle.qtdeRenovadaMesesPassados}"/>
                                <h:outputText styleClass="bi-table-text"
                                              value="#{RenovacaoSinteticoControle.renovadoMesPassadoPercentual}"/>
                                <%--do mes passado--%>
                                <%--da previsao do mes--%>
                                <h:panelGroup>
                                    <h:outputText styleClass="bi-table-text tooltipster" value=" Da Previs�o do M�s"
                                                  title="#{RenovacaoSinteticoControle.title}"/>
                                </h:panelGroup>
                                <a4j:commandLink id="linkRenovacoesDaPrevisao"
                                                 styleClass="texto-font texto-size-16 texto-cor-azul tooltipster"
                                                 title="Ver Detalhes"
                                                 action="#{RenovacaoSinteticoControle.mostrarListaDentroMes}"
                                                 oncomplete="abrirPopup('./includes/bi/lista_indice_renovacao.jsp', 'ResumoPessoa', 850, 595);"
                                                 value="#{RenovacaoSinteticoControle.qtdeRenovadaDentroMes}"/>
                                <h:outputText styleClass="bi-table-text"
                                              value="#{RenovacaoSinteticoControle.renovadoDentroMesPercentual}"/>
                                <%--da previsao do mes--%>
                                <%--antecipadas--%>
                                <h:panelGroup>
                                    <h:outputText styleClass="bi-table-text tooltipster" value="De Meses Futuros"
                                                  title="#{RenovacaoSinteticoControle.title}"/>
                                </h:panelGroup>
                                <a4j:commandLink id="linkRenovacoesMesFuturo"
                                                 styleClass="texto-font texto-size-16 texto-cor-azul tooltipster"
                                                 title="Ver Detalhes"
                                                 action="#{RenovacaoSinteticoControle.mostrarListaMesesFuturos}"
                                                 oncomplete="abrirPopup('./includes/bi/lista_indice_renovacao.jsp', 'ResumoPessoa', 850, 595);"
                                                 value="#{RenovacaoSinteticoControle.qtdeRenovadaMesesFuturos}"/>
                                <h:outputText styleClass="bi-table-text"
                                              value="#{RenovacaoSinteticoControle.renovadoMesesFuturosPercentual}"/>
                                <%--da previsao do mes--%>


                                <h:panelGroup>
                                    <h:outputText styleClass="bi-table-text tooltipster"
                                                  value="Alunos com mais de um professor"
                                                  title="#{RenovacaoSinteticoControle.title}"/>
                                </h:panelGroup>
                                <a4j:commandLink styleClass="texto-font texto-size-16 texto-cor-azul tooltipster"
                                                 title="Ver Detalhes"
                                                 action="#{RenovacaoSinteticoControle.mostrarListaClientesComMuitosProfessores}"
                                                 oncomplete="abrirPopup('./includes/bi/lista_indice_renovacao.jsp', 'ResumoPessoa', 850, 595);"
                                                 value="#{RenovacaoSinteticoControle.qtdeClientesComMuitosProfessores}"/>
                                <h:outputText styleClass="bi-table-text" value=""/>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </c:if>
</h:panelGroup>
