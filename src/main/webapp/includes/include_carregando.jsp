<%-- 
    Document   : include_carregando
    Created on : 12/04/2011, 08:48:32
    Author     : <PERSON> include do "carregando..." deve ser utilizado apenas em p�ginas de janelas
    tipo PopUp, para n�o conflitar com outro a4j:Status de outras p�ginas.
--%>
<%@include file="imports.jsp" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<rich:modalPanel id="panelCarregando" autosized="true">
    <h:panelGrid columns="3">
        <h:graphicImage url="/imagens/carregando.gif" style="border:none"/>
        <h:outputText style="font-family:Arial, Helvetica, sans-serif;
                      font-size:11px;
                      text-decoration:none;
                      color:#8a8989;
                      line-height:100%;
                      font-weight:normal;"
                      value="Carregando..."/>
    </h:panelGrid>
</rich:modalPanel>
<a4j:status
    id="statusCarencia" onstart="Richfaces.showModalPanel('panelCarregando');resetTime(tempoEmMillis);"
    onstop="#{rich:component('panelCarregando')}.hide();">
</a4j:status>

<a4j:status forceId="true" 
            onstart="document.getElementById('form:imageLoading').style.visibility = '';"
            onstop="document.getElementById('form:imageLoading').style.visibility = 'hidden';"/>

<a4j:status forceId="true" id="statusHora" onstart=""
            onstop="" />

<a4j:status forceId="true" id="statusInComponent"
            onstart="document.getElementById('form:imageLoading').style.visibility = '';"
            onstop="document.getElementById('form:imageLoading').style.visibility = 'hidden';"/>