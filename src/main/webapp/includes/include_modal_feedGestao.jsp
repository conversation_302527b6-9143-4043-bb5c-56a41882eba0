<%@include file="imports.jsp" %>

<!-- Script paginar modal assistente -->
<script>
    indiceAtual = 1;

    function proximaDica() {
        jQuery('.blocoDica').removeClass('ativo');
        indiceAtual++;
        var seletor = ".blocoDica:nth-child(" + indiceAtual + ")";
        jQuery(seletor).addClass('ativo');
    }

    function voltaDica() {
        jQuery('.blocoDica').removeClass('ativo');
        indiceAtual--;
        var seletor = ".blocoDica:nth-child(" + indiceAtual + ")";
        jQuery(seletor).addClass('ativo');
    }

    function mudarCorBack() {
        var cor = '#FFFFFF';
        alert(cor);
    }
</script>
<style>
    .botoesIndicadores{
        padding-top: 3px;
        width: 81px;
        height: 81px;
    }
    .caixaTexto{
        background-color: white !important;
        border-radius: 10px !important;
        width: 420px;
        padding: 10px;
    }
    .like{
        position:relative;
        float:right; 
        padding-right: 8px;
    }
    .dislike{
        float:right;
    }
    .tituloDica{
        font-weight: bold;
        color: #1A4267;
        font-family: 'Trebuchet MS', Helvetica, sans-serif !important;
        font-size: 29.25px;
        font-style: italic;
    }
    .contador{
        font-weight: bold;
        color: #1A4267;
        font-family: 'Trebuchet MS', Helvetica, sans-serif !important;
        font-size: 20px;
    }
    .textoDica{
        font-size: 11pt;
        font-family: 'Trebuchet MS', Helvetica, sans-serif !important; 
        color: #1A4267;
        text-align: justify;
        line-height:150%;
    }
    .textoCapa{
        font-size: 14pt; 
        color: #1A4267; 
        font-family: 'Trebuchet MS', Helvetica, sans-serif; 
        line-height:150%;text-align: justify; 
    }
</style>
<rich:modalPanel autosized="false" id="feedGestao" width="1110" showWhenRendered="#{FeedGestaoControle.exibirFeed}"
                 styleClass="modalDicNovos assistente" minHeight="550"
                 top="100">
    <f:facet name="controls">
        <h:panelGroup>
            <i class="fa-icon-remove fecharModal" style="cursor:pointer" id="hideFeed"> </i>
            <rich:componentControl for="feedGestao"
                                   attachTo="hideFeed" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <h:panelGroup layout="block" id="feedPainelModal"  styleClass="default margin-v-10">

        <h:panelGroup layout="block" style="float: left">
            <!-- CAPA -->
            <h:panelGroup>
                <a4j:commandLink action="#{FeedGestaoControle.voltarCapa}" reRender="feedPainelModal" status="false"
                                 onclick="indiceAtual = 1;">
                    <h:graphicImage url="/feed_gestao/assistente/carinha-icon.png" width="81px" height="81px"/>

                </a4j:commandLink><br/>
            </h:panelGroup>
            <!-- FINANCEIRO -->
            <a4j:repeat value="#{FeedGestaoControle.indicadores}" var="ind" rowKeyVar="indexInd">
                <h:panelGroup layout="block" style="position:absolute; left: 70px; #{FeedGestaoControle.positions[indexInd]}" 
                              rendered="#{FeedGestaoControle.naoLidas[ind] > 0}">
                    <div class="notificacaoAtividades">
                        <h:outputText  value="#{FeedGestaoControle.naoLidas[ind]}"></h:outputText>
                        </div>
                </h:panelGroup>
                <a4j:commandLink  actionListener="#{FeedGestaoControle.mudarIndicador}" 
                                  reRender="feedPainelModal" status="false"
                                  onclick="indiceAtual = 1;">
                    <h:graphicImage url="/feed_gestao/assistente/#{ind.icone}" styleClass="botoesIndicadores" />   
                    <f:attribute name="selec" value="#{ind}"/>
                    <rich:toolTip  style="background-color: white; border-color: black;" showDelay="500">
                        <h:outputText style="color: black; font-weight: bold; " value="#{ind.descricao}"></h:outputText><br/>  
                        <h:outputText style="color: black;" value="#{ind.hint}"></h:outputText>
                    </rich:toolTip>
                </a4j:commandLink ><br/>
            </a4j:repeat>
        </h:panelGroup>
        <h:panelGroup layout="block" style="position:relative; float: left">
            <h:panelGroup layout="block" styleClass="blocoFeed ativo" rendered="#{FeedGestaoControle.capa}"
                          style="background-color: #FFFFFF; border-color: #1A4267;border-width: 10px;border-style: solid;">
                <div style="position: absolute; left: 530px; top: 215px; margin-right: 180px;">
                    <h:outputText value="O PMG revisou seus indicadores e tem <span style='color: red;'>#{fn:length(FeedGestaoControle.feeds)}</span> dica(s) para voc�. Vem ver!"
                                  style="font-size: 12.5pt; color: black;font-family: 'Trebuchet MS', Helvetica, sans-serif; padding-top: 8px;"
                                  escape="false"/>
                </div>

                <h:panelGroup layout="block" style="width: 450px; padding-left: 10px; "
                              styleClass="blocoDica ativo" >


                    <h:panelGroup layout="block"
                                  id="painelFeed1">
                        <h:panelGroup layout="block">
                            <h2><h:outputText styleClass="tituloDica" value="#{FeedGestaoControle.inicial.tituloGrande}"></h:outputText>     </h2>
                            <h:panelGroup layout="block" styleClass="textoDica">
                                <h:outputText value="#{FeedGestaoControle.inicial.mensagemCapa}"></h:outputText>
                            </h:panelGroup>
                            <br/>
                            <h:panelGroup rendered="#{not empty FeedGestaoControle.inicial.linkVideo && empty FeedGestaoControle.inicial.linkImagem}">
                                <iframe src="//${FeedGestaoControle.inicial.linkVideo}" width="480" height="280" 
                                        frameborder="0" 
                                        webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe> 
                            </h:panelGroup>
                            <h:panelGroup rendered="#{not empty FeedGestaoControle.inicial.linkImagem}">
                                <h:graphicImage url="#{FeedGestaoControle.inicial.linkImagem}"
                                                width="480" height="280"/>
                            </h:panelGroup>

                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>



                <h:panelGroup layout="block" style="padding-left: 540px;padding-top: 60px;padding-right: 50px;">
                    <h4 style="font-size: 14pt; color: black;  font-family: 'Trebuchet MS', Helvetica, sans-serif;">Ol� Parceiro!</h4>  

                    <h4 style="font-size: 12.5pt; color: black;font-family: 'Trebuchet MS', Helvetica, sans-serif; padding-top: 8px;">
                        <h:outputText value="#{FeedGestaoControle.inicial.textoBalaoGrande}"/></h4>  
                    </h:panelGroup>

            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="blocoFeed ativo" 
                          id="feedsApresentar"
                          rendered="#{!FeedGestaoControle.capa}"
                          style="background-image: url('./feed_gestao/assistente/balao-menor.png'),url('./feed_gestao/assistente/balaocinza.png'),url('./feed_gestao/assistente/#{FeedGestaoControle.indicadorSelecionado.image}');
                          background-color: #FFFFFF; 
                          position: absolute; border-color: #{FeedGestaoControle.indicadorSelecionado.cor};border-width: 10px;border-style: solid;">


                <!-- -------------------------------------------------------------------------- -->  

                <a4j:repeat value="#{FeedGestaoControle.feedsApresentar}"
                            var="tela">
                    <h:panelGroup layout="block" style="width: 450px; padding-left: 10px;"
                                  styleClass="blocoDica #{tela.primeira ? 'ativo' : ''}" >
                        <div layout="block" style="position:absolute; right: 200px; top:423px; ">
                            <h2 class="contador"><h:outputText value="#{tela.index}/#{fn:length(FeedGestaoControle.feedsApresentar)}"/></h2>  
                        </div>

                        <h:panelGroup layout="block" rendered="#{!tela.primeira}">
                            <a4j:commandLink styleClass="btn-prev"
                                             status="false" onclick="voltaDica()"
                                             style="color: #{FeedGestaoControle.indicadorSelecionado.cor};">
                                <span class="fa fa-icon-angle-left fa-icon-4x"/>
                            </a4j:commandLink>
                        </h:panelGroup>


                        <h:panelGroup layout="block">

                            <h:panelGroup layout="block" style="height: 210px; padding-top:20px;"
                                          id="painelFeed1">
                                <h2><h:outputText escape="false" styleClass="tituloDica" value="#{tela.feed1.nome}"></h:outputText>     </h2>
                                <h:panelGroup layout="block" styleClass="textoDica" >
                                    <h:inputTextarea style="border: none;color: #1A4267;"
                                                styleClass="camposSomenteLeitura" readonly="true" rows="7"
                                                cols="65"
                                            value="#{tela.feed1.mensagem}" />
                                </h:panelGroup>     

                                <a4j:commandLink reRender="painelFeed1" status="false" styleClass="dislike"
                                                 title="Esta dica n�o � relevante!"
                                                 actionListener="#{FeedGestaoControle.dislike}">
                                    <h:graphicImage url="/feed_gestao/assistente/dislike#{tela.feed1.disliked ? 'd':''}.png"/>
                                    <f:attribute name="feedSelecionado" value="#{tela.feed1}"/>
                                </a4j:commandLink>
                                <a4j:commandLink reRender="painelFeed1" status="false" styleClass="like"
                                                 title="Curti esta dica!"
                                                 actionListener="#{FeedGestaoControle.like}">
                                    <h:graphicImage url="/feed_gestao/assistente/like#{tela.feed1.liked ? 'd':''}.png"/>
                                    <f:attribute name="feedSelecionado" value="#{tela.feed1}"/>
                                </a4j:commandLink>
                                
                            </h:panelGroup>    
                                <br/>
                                <div style="opacity: 0.4;border-style:  solid;border-width: 0 0 1px 0; border-color: ${FeedGestaoControle.indicadorSelecionado.cor}; width: 100%;"></div>    
                            
                            <h:panelGroup layout="block" style="height: 210px;" rendered="#{tela.feed2 != nome}"
                                          id="painelFeed2">
                                <h2><h:outputText escape="false" styleClass="tituloDica" value="#{tela.feed2.nome}"></h:outputText>      </h2>
                                <h:panelGroup layout="block" styleClass="textoDica">
                                      <h:inputTextarea style="border: none;color: #1A4267;"
                                                styleClass="camposSomenteLeitura" readonly="true" rows="7"
                                                cols="65"
                                            value="#{tela.feed2.mensagem}"/>
                                </h:panelGroup>  
                                <a4j:commandLink reRender="painelFeed2" status="false" styleClass="dislike"
                                                 title="Esta dica n�o � relevante!"
                                                 actionListener="#{FeedGestaoControle.dislike}">
                                    <h:graphicImage url="/feed_gestao/assistente/dislike#{tela.feed2.disliked ? 'd':''}.png"/>
                                    <f:attribute name="feedSelecionado" value="#{tela.feed2}"/>
                                </a4j:commandLink>

                                <a4j:commandLink reRender="painelFeed2" status="false" styleClass="like"
                                                 actionListener="#{FeedGestaoControle.like}">
                                    <h:graphicImage url="/feed_gestao/assistente/like#{tela.feed2.liked ? 'd':''}.png"
                                                    title="Curti esta dica!"/>
                                    <f:attribute name="feedSelecionado" value="#{tela.feed2}"/>
                                </a4j:commandLink>


                            </h:panelGroup>  

                        </h:panelGroup> 


                        <a4j:commandLink styleClass="btn-prox" rendered="#{!tela.ultima}"
                                         status="false" onclick="proximaDica()" 
                                         actionListener="#{FeedGestaoControle.proxima}"
                                         style="color: #{FeedGestaoControle.indicadorSelecionado.cor};">
                            <span class="fa fa-icon-angle-right fa-icon-4x"/>
                            <f:attribute name="index" value="#{tela.index}"/>
                        </a4j:commandLink>
                    </h:panelGroup>
                </a4j:repeat>
                <div style="position: absolute; left: 530px; top: 215px; margin-right: 180px;">
                    <h:outputText value="O PMG revisou seus indicadores e tem <span style='color: red;'>#{FeedGestaoControle.totais[FeedGestaoControle.indicadorSelecionado]}</span> dica(s) para voc�. Vem ver!"
                                  style="font-size: 12.5pt; color: black;font-family: 'Trebuchet MS', Helvetica, sans-serif; padding-top: 8px;"
                                  escape="false"/>
                </div>  
                <!-- -------------------------------------------------------------------------- -->    

                <!-- -------------------------------------------------------------------------- -->    
                <h:panelGroup layout="block" style="padding-left: 540px;padding-top: 60px;padding-right: 50px; position:absolute;">
                    <h4 style="font-size: 14pt; color: black;  font-family: 'Trebuchet MS', Helvetica, sans-serif;">Ol� Parceiro!</h4>  

                    <h4 style="font-size: 12.5pt; color: black;font-family: 'Trebuchet MS', Helvetica, sans-serif; padding-top: 8px;">
                        <h:outputText value="Vamos rever seus indicadores #{FeedGestaoControle.indicadorSelecionado.texto} e encontrar meios de aprimorar seus resultados?"/></h4>  


                </h:panelGroup>



            </h:panelGroup>


        </h:panelGroup>
        <span class="finish" style="display:none;"></span>
    </h:panelGroup>

</rich:modalPanel>
