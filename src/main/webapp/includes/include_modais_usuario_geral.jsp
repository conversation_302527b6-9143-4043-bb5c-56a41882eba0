<%@include file="imports.jsp" %>

<style>
    .userdisponivel {
        color: darkgreen;
        font-size: 14px;
    }
    .userindisponivel {
        color: darkred;
        font-size: 14px;
    }
</style>

<rich:modalPanel id="modalAlterarTelefone" autosized="true" styleClass="novaModal" shadowOpacity="true"
                 width="400" height="250">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Alterar celular"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                    id="hidelinkAlterTelefoneLogin"/>
            <rich:componentControl for="modalAlterarTelefone" attachTo="hidelinkAlterTelefoneLogin"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formTelefoneLogin" style="padding-top: 20px;">
        <h:panelGrid id="panelTrocaTelefoneLogin"
                     columns="1" width="100%" columnClasses="colunaCentralizada">
            <h:inputText id="telefoneUsuarioNovo"
                         rendered="#{empty UsuarioControle.tokenDTO.token}"
                         size="13"
                         maxlength="13"
                         onblur="blurinput(this);"
                         onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                         onfocus="focusinput(this);"
                         styleClass="form"
                         value="#{UsuarioControle.usuarioTelefoneAlterarVO.numero}"/>

            <a4j:commandButton id="btnSolicitarNovoTelefone"
                               rendered="#{empty UsuarioControle.tokenDTO.token}"
                               style="margin-top: 10px;"
                               action="#{UsuarioControle.solicitarNovoTelefone}"
                               value="Enviar c�digo via SMS"
                               reRender="panelTrocaTelefoneLogin"
                               oncomplete="#{UsuarioControle.onComplete}#{UsuarioControle.mensagemNotificar}"
                               styleClass="botoes nvoBt"/>

            <h:panelGroup layout="block" style="padding-bottom: 10px">
                <h:outputText styleClass="form"
                              rendered="#{not empty UsuarioControle.tokenDTO.token}"
                              value="Informe o c�digo recebido por SMS"/>
            </h:panelGroup>

            <h:inputText id="usuarioTokenSMSLogin"
                         rendered="#{not empty UsuarioControle.tokenDTO.token}"
                         size="6" maxlength="6" styleClass="form"
                         value="#{UsuarioControle.tokenDTO.codigoVerificacao}"/>

            <a4j:commandButton id="btnValidarTokenSMSLogin"
                               rendered="#{not empty UsuarioControle.tokenDTO.token}"
                               style="margin-top: 10px;"
                               action="#{UsuarioControle.validarTokenTelefone}"
                               value="Confirmar"
                               reRender="#{UsuarioControle.msgAlert}"
                               oncomplete="#{UsuarioControle.onComplete}#{UsuarioControle.mensagemNotificar}"
                               styleClass="botoes nvoBt"/>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>
<rich:modalPanel id="modalAlterarEmail" autosized="true" styleClass="novaModal" shadowOpacity="true"
                 width="500" height="250">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Alterar e-mail"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                    id="hidelinkAlterEmailLogin"/>
            <rich:componentControl for="modalAlterarEmail" attachTo="hidelinkAlterEmailLogin"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formEmailLogin" style="padding-top: 20px;">
        <h:panelGrid id="panelTrocaEmailLogin"
                     columns="1" width="100%" columnClasses="colunaCentralizada">

            <h:inputText id="emailUsuarioNovo"
                         rendered="#{empty UsuarioControle.tokenDTO.token}"
                         size="40" maxlength="60"
                         onblur="blurinput(this);" onfocus="focusinput(this);"
                         styleClass="form"
                         value="#{UsuarioControle.usuarioEmailAlterarVO.email}"/>

            <a4j:commandButton id="btnSolicitarNovoEmail"
                               rendered="#{empty UsuarioControle.tokenDTO.token}"
                               style="margin-top: 10px;"
                               action="#{UsuarioControle.solicitarNovoEmail}"
                               value="Enviar c�digo"
                               reRender="panelTrocaEmailLogin"
                               oncomplete="#{UsuarioControle.onComplete}#{UsuarioControle.mensagemNotificar}"
                               styleClass="botoes nvoBt"/>

            <h:panelGroup layout="block" style="padding-bottom: 10px">
                <h:outputText styleClass="form"
                              rendered="#{not empty UsuarioControle.tokenDTO.token}"
                              value="Informe o c�digo recebido por e-mail"/>
            </h:panelGroup>

            <h:inputText id="usuarioTokenEmailLogin"
                         rendered="#{not empty UsuarioControle.tokenDTO.token}"
                         size="6" maxlength="6" styleClass="form"
                         value="#{UsuarioControle.tokenDTO.codigoVerificacao}"/>

            <a4j:commandButton id="btnValidarTokenEmailLogin"
                               rendered="#{not empty UsuarioControle.tokenDTO.token}"
                               style="margin-top: 10px;"
                               action="#{UsuarioControle.validarTokenEmail}"
                               value="Confirmar"
                               reRender="#{UsuarioControle.msgAlert}"
                               oncomplete="#{UsuarioControle.onComplete}#{UsuarioControle.mensagemNotificar}"
                               styleClass="botoes nvoBt"/>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>
<rich:modalPanel id="modalValidarToken" autosized="true" styleClass="novaModal" shadowOpacity="true"
                 width="400" height="250">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="C�digo de verifica��o"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                    id="hidelinkvalidartokenLogin"/>
            <rich:componentControl for="modalValidarToken" attachTo="hidelinkvalidartokenLogin"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formValidarToken" style="padding-top: 20px;">
        <h:panelGrid id="panelValidarToken"
                     columns="1" width="100%" columnClasses="colunaCentralizada">

            <h:panelGroup layout="block" style="padding-bottom: 10px">
                <h:outputText styleClass="form"
                              rendered="#{not empty UsuarioControle.tokenDTO.token}"
                              value="Informe o c�digo recebido por e-mail"/>
            </h:panelGroup>

            <h:inputText id="codigoVerificacaoTokenLogin"
                         rendered="#{not empty UsuarioControle.tokenDTO.token}"
                         size="6" maxlength="6" styleClass="form"
                         value="#{UsuarioControle.tokenDTO.codigoVerificacao}"/>

            <a4j:commandButton id="btnValidarToken"
                               rendered="#{not empty UsuarioControle.tokenDTO.token}"
                               style="margin-top: 10px;"
                               action="#{UsuarioControle.validarCodigoVerificacaoEmail}"
                               value="Confirmar"
                               reRender="#{UsuarioControle.msgAlert}"
                               oncomplete="#{UsuarioControle.onComplete}#{UsuarioControle.mensagemNotificar}"
                               styleClass="botoes nvoBt"/>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>
<rich:modalPanel id="modalConfirmarDesvinculoUsuario" autosized="true" styleClass="novaModal" shadowOpacity="true"
                 width="400" height="250">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Remover acesso do usu�rio � academia"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                    id="hidelinkconfirmarDesvinculoUsuario"/>
            <rich:componentControl for="modalConfirmarDesvinculoUsuario" attachTo="hidelinkconfirmarDesvinculoUsuario"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formConfirmarDesvinculoUsuario" style="padding-top: 20px;">
        <h:panelGrid id="panelConfirmarDesvinculoUsuario"
                     columns="1" width="100%" columnClasses="colunaCentralizada">
            <h:outputText value="Confirmar remo��o de acesso do usu�rio #{UsuarioControle.usuarioVO.nome} a academia." />
            <h:outputText style="color: red" value="Esta a��o far� com que o usu�rio selecionado perca o acesso a todas as unidades da academia e ir� remover os dados de acesso de e-mail e senha!" />
            <a4j:commandButton id="btnDesvincularUsuario"
                               style="margin-top: 10px;"
                               action="#{UsuarioControle.desvincularUsuario}"
                               value="Confirmar"
                               reRender="#{UsuarioControle.msgAlert}"
                               oncomplete="#{UsuarioControle.onComplete}#{UsuarioControle.mensagemNotificar}"
                               styleClass="botoes nvoBt"/>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>
<rich:modalPanel id="mdlDadosAcessosEmailInformado" autosized="true" styleClass="novaModal" shadowOpacity="true"
                 width="800" height="300">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Usu�rios com o mesmo e-mail"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="texto-size-20 texto-cor-branco fa-icon-remove-sign"
                    id="hidelinkDadosAcessosEmailInformado"/>
            <rich:componentControl for="mdlDadosAcessosEmailInformado" attachTo="hidelinkDadosAcessosEmailInformado"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formDadosAcessosEmailInformado" style="padding-top: 20px;">
        <h:panelGroup>
            <div style="display: block; margin-botom: 10px">
                <h:outputText
                        styleClass="texto-size-25 texto-cor-brancon" value="Existem usu�rios com o e-mail "/>
                <strong><h:outputText styleClass="texto-size-20 texto-cor-brancon" value="#{UsuarioControle.usuarioEmailAlterarVO.email}."/></strong>
            </div>
            <h:outputText style="display: block; margin-botom: 10px" styleClass="texto-size-20 texto-cor-brancon" value="Caso voc� decida prosseguir, os dados de acesso destes outros usu�rios ser�o adicionados ao seu usu�rio, ocorrendo a exclus�o dos outros."/>
            <h:outputText style="display: block; margin-botom: 10px" styleClass="texto-size-20 texto-cor-brancon" value="Al�m disso, ser� encaminhado um link no e-mail informado para valida��o e redefini��o de senha."/>
            <h:outputText style="display: block; margin-botom: 10px" styleClass="texto-size-20 texto-cor-brancon" value="Caso contr�rio voc� dever� escolher outro e-mail para realizar a altera��o."/>
        </h:panelGroup>
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" style="margin-top: 10px">
            <h:outputText styleClass="texto-size-16 texto-cor-brancon" value="Deseja prosseguir?"/>
            <h:panelGroup>
                <a4j:commandButton id="btnNaoVincularDadosUsuarios"
                                   style="margin-top: 10px; margin-right: 10px"
                                   action="#{UsuarioControle.apresentarModalEscolhaOutroEmail}"
                                   value="Cancelar"
                                   reRender="#{UsuarioControle.msgAlert}"
                                   oncomplete="#{UsuarioControle.onComplete}#{UsuarioControle.mensagemNotificar}"
                                   styleClass="botoes nvoBt btSec"/>
                <a4j:commandButton id="btnVincularDadosUsuarios"
                                   style="margin-top: 10px;"
                                   action="#{UsuarioControle.trocarEmailRedefinindoSenhaEVinculandoDados}"
                                   value="Prosseguir"
                                   reRender="#{UsuarioControle.msgAlert}"
                                   oncomplete="#{UsuarioControle.onComplete}#{UsuarioControle.mensagemNotificar}"
                                   styleClass="botoes nvoBt"/>
            </h:panelGroup>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>
