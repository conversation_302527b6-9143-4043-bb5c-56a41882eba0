<%--
O recurso de notificação, irá continuar na forma de código, porém não estará acessível por nenhuma parte do sistema.
Este jsp era chamado pelo "include_headCRM.jsp"
Ticket: IN-520
--%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<script src="${root}/script/notificacaoHorarioAgendadoCRM.js" type="text/javascript" ></script>
<a4j:jsFunction name="carregarNotificacoes"
                action="#{NotificacaoHorarioAgendadoControle.carregarNotificacoes}"
                data="#{NotificacaoHorarioAgendadoControle.notificacoesJSON}"
                oncomplete="NotificacoesCRM.exibirNotificacoes(data)"
                status="none"/>

<a4j:jsFunction name="irParaAgendamento" action="#{NotificacaoHorarioAgendadoControle.irParaAgendamento}">
    <a4j:actionparam name="param1" assignTo="#{NotificacaoHorarioAgendadoControle.notificacaoJSON}" />
</a4j:jsFunction>
