<%@include file="includes/imports.jsp" %>
<rich:modalPanel id="panelCheque" autosized="true" shadowOpacity="true"
                 width="800" height="200"
                 onshow="document.getElementById('formCheque:codigoBanco').focus()">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Informa��es do Cheque"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                            id="hidelinkPanelCheque" />
            <rich:componentControl for="panelCheque" attachTo="hidelinkPanelCheque"
                                   operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formCheque">
        <input type="hidden" value="${modulo}" name="modulo"/>
        <h:panelGrid columns="1" width="100%" columnClasses="colunaEsquerda">
            <h:panelGrid rendered="#{!ChequeControle.financeiro}" columns="1"
                         style="height:25px; background-image:url('imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                         columnClasses="colunaEsquerda" width="100%">
                <h:outputText styleClass="tituloFormulario"
                              value="Informa��es do Cheque" />
            </h:panelGrid>

            <h:panelGrid rendered="#{ChequeControle.financeiro}" columns="1"
                         style="height:25px; background-image:url('./../../imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                         columnClasses="colunaEsquerda" width="100%">
                <h:outputText styleClass="tituloFormulario"
                              value="Informa��es do Cheque" />
            </h:panelGrid>

            <h:panelGrid id="panelChequePreenchido" columns="1" width="100%"
                         styleClass="tabForm">
                <h:panelGrid columns="2" width="100%" styleClass="tabForm">
                    <h:dataTable id="listaCheque" rowClasses="tablelistras"
                                 width="100%" columnClasses="colunaEsquerda"
                                 value="#{ChequeControle.cheque}" var="cheque">
                        <f:facet name="header">
                            <rich:columnGroup>
                                <h:column>
                                    <h:outputText value="N� do Banco" styleClass="text" />
                                </h:column>
                                <h:column>
                                    <h:outputText value="Banco" styleClass="text" />
                                </h:column>
                                <h:column>
                                    <h:outputText value="Ag�ncia" styleClass="text" />
                                </h:column>
                                <h:column>
                                    <h:outputText value="Conta" styleClass="text" />
                                </h:column>
                                <h:column>
                                    <h:outputText value="N� do Cheque" styleClass="text" />
                                </h:column>
                            </rich:columnGroup>
                        </f:facet>
                        <h:column>
                            <h:inputText id="codigoBanco"
                                         value="#{ChequeControle.banco}" onblur="blurinput(this);"
                                         size="10" maxlength="10" onfocus="focusinput(this);" onkeypress="if (event.keyCode == 13) { document.getElementById('formCheque:listaCheque:0:linkcodigoBancohidden').click();return false;}; "
                                         styleClass="form">
                                <a4j:support event="onchange" focus="bancoPreenchido"
                                             action="#{ChequeControle.consultarBancoPorCodigoBanco}"
                                             reRender="panelChequePreenchido" />
                            </h:inputText>
                            <a4j:commandLink style="visibility: hidden;" id="linkcodigoBancohidden"
                                             focus="bancoPreenchido"
                                             action="#{ChequeControle.consultarBancoPorCodigoBanco}"
                                             reRender="panelChequePreenchido" />
                        </h:column>
                        <h:column>
                            <h:selectOneMenu id="bancoPreenchido"
                                             converter="simpleIndexConverter"
                                             value="#{ChequeControle.bancoSelected}" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="form">
                                <f:selectItems
                                        value="#{ChequeControle.listaSelectItemBanco}" />
                                <a4j:support event="onchange" focus="bancoPreenchido"
                                             action="#{ChequeControle.consultarBancoPorCodigo}"
                                             reRender="panelChequePreenchido" />
                            </h:selectOneMenu>

                        </h:column>
                        <h:column>
                            <h:inputText id="agencia" value="#{cheque.agencia}"
                                         onblur="blurinput(this);" size="7" maxlength="5"
                                         onfocus="focusinput(this);" styleClass="form" onkeypress="if (event.keyCode == 13) {return false}; " />
                        </h:column>
                        <h:column>
                            <h:inputText id="conta" value="#{cheque.conta}"
                                         onblur="blurinput(this);" size="15" maxlength="20"
                                         onfocus="focusinput(this);" styleClass="form" onkeypress="if (event.keyCode == 13) {return false}; "/>
                        </h:column>
                        <h:column>
                            <h:inputText id="nDoc" value="#{cheque.numero}"
                                         onblur="blurinput(this);" size="15" maxlength="15"
                                         onfocus="focusinput(this);" styleClass="form" onkeypress="if (event.keyCode == 13) {return false}; " />
                        </h:column>
                    </h:dataTable>
                </h:panelGrid>
                <h:panelGrid id="identificacao" columns="2" width="100%"
                             columnClasses="colunaEsquerda" styleClass="tabForm">
                    <h:panelGrid id="panelcpfcnpjCheque" columns="3" width="100%">
                        <h:outputText rendered="#{!ChequeControle.financeiro}"
                                      value="Tipo de Identifica��o" styleClass="text" />
                        <h:selectOneMenu id="cpfCnpjCheque" rendered="#{!ChequeControle.financeiro}"
                                         value="#{ChequeControle.variavelCpfCnpj}"
                                         onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="form">
                            <f:selectItem itemValue="" itemLabel="#{msg_aplic.prt_Agenda_Selecione}" />
                            <f:selectItems
                                    value="#{ChequeControle.listaSelectItemCpfCnpj}" />
                            <a4j:support event="onchange" focus="cpfCnpjCheque"
                                         action="#{ChequeControle.mostrarCampoCPF}"
                                         reRender="formCheque:identificacao" />
                        </h:selectOneMenu>
                        <h:inputText id="cpfCheque2"
                                     rendered="#{ChequeControle.apresentarCampoCPF && !ChequeControle.financeiro}"
                                     value="#{ChequeControle.cheque.cpf}"
                                     onblur="blurinput(this);" onfocus="focusinput(this);" size="14"
                                     maxlength="14"
                                     onkeypress="return mascara(this.form, 'formCheque:cpfCheque2', '999.999.999-99', event);"
                                     styleClass="form" />
                        <h:inputText id="CnpjCheque2"
                                     rendered="#{ChequeControle.apresentarCampoCNPJ && !ChequeControle.financeiro}"
                                     value="#{ChequeControle.cheque.cnpj}"
                                     onblur="blurinput(this);" onfocus="focusinput(this);" size="18"
                                     maxlength="18"
                                     onkeypress="return mascara(this.form, 'formCheque:CnpjCheque2', '99.999.999/9999-99', event);"
                                     styleClass="form" />
                    </h:panelGrid>
                    <h:panelGrid width="100%">
                        <h:panelGroup>
                            <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nomeTerceiro}" styleClass="text" />
                            <h:inputText id="nomeTerceiro"
                                         value="#{ChequeControle.cheque.nomeNoCheque}"
                                         onblur="blurinput(this);" onfocus="focusinput(this);" size="18"
                                         maxlength="120"
                                         styleClass="form" />
                            <h:outputText value="Qtd. de Cheques:" styleClass="text" />
                            <h:inputText id="qtdeCheques" readonly="#{ChequeControle.financeiro}"
                                         value="#{ChequeControle.qtdeCheques}"
                                         onblur="blurinput(this);"
                                         size="5" maxlength="5" onfocus="focusinput(this);"
                                         styleClass="form">
                            </h:inputText>
                            <rich:toolTip for="qtdeCheques" rendered="#{ChequeControle.financeiro}"
                                          followMouse="true" direction="top-right"  style="width:200px;height: 90px;">
                                <h:outputText styleClass="tituloCampos"
                                              value="#{msg_aplic.prt_Finan_msg_qtdChequesFinanceiro}"></h:outputText>
                            </rich:toolTip>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGrid id="mensagem1">
                    <h:outputText styleClass="mensagem"
                                  value="#{MovPagamentoControle.mensagem}" />
                    <h:outputText styleClass="mensagemDetalhada"
                                  value="#{MovPagamentoControle.mensagemDetalhada}" />
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%"
                             columnClasses="colunaCentralizada">
                    <a4j:commandButton id="linkGravarInformacoesCheque"
                                       value="gravarCheque" rendered="#{!ChequeControle.financeiro}"
                                       action="#{MovPagamentoControle.adicionarListaCheque}"
                                       reRender="escolhaFormaPagamento,MovPagamentoCheque,
                                       escolhaFormaPagamentoCC,totalLancado,
                                       residuo,mensagem1, form:panelCheque"
                                       oncomplete="#{MovPagamentoControle.msgAlert}"
                                       image="/images/icon_mais.gif" alt="Gravar Cheque" />


                    <a4j:commandButton id="linkGravarInformacoesChequeFinanceiro"
                                       value="Gerar" rendered="#{ChequeControle.financeiro}"
                                       action="#{MovContaControle.gerarCheques}"
                                       reRender="formLancarPagamento"
                                       oncomplete="Richfaces.hideModalPanel('panelCheque')" />
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>