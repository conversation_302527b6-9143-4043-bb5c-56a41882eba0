<%@page import="controle.arquitetura.SuperControle"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<meta name="robots" content="noindex,nofollow"/>
<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<link href="css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="script/script2.js"></script>
<link href="./beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="./css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>
<link href="./beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/pure-forms.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="script/ce_script.js"></script>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" src="script/jquery.js"></script>
<script type="text/javascript" src="script/jquery.maskedinput-1.2.2.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<f:loadBundle var="Mensagens" basename="propriedades.Mensagens"/>

<link href="css_pacto.css" rel="stylesheet" type="text/css">

<f:view>

    <%@include file="includes/include_getip.jsp" %>
    

    
    
    <script type="text/javascript">
        var $j = jQuery.noConflict();
        function load() {
            enviarBrowser(navigator.userAgent, screen.availWidth,
            screen.availHeight, document.location.protocol, document.location.origin);
            submitIp();
        }
        function submitIp() {
            urlGetIp = "https://app.pactosolucoes.com.br/ip/v2.php";
            rtype = "text";            
            var protocolo = document.location.protocol.toString();            
            if (protocolo.indexOf("https", 0) != -1){
                urlGetIp = "https://app.pactosolucoes.com.br/ip/v2.php";
            }           
            jQuery.ajax({
                type: "GET",
                url: urlGetIp,
                dataType: rtype,
                success: function (valor) {
                    if (rtype == "jsonp") {
                        enviarIp(valor.ip);
                    } else {
                        enviarIp(valor);
                    }
                }
            });
        }
    </script>
    
    <%@include file="includes/include_redirect_login.jsp" %>

    <html>
        <head>
            <style type="text/css">
                html, body {
                    width: 100%;
                    height: 100%;
                    margin: 0 0 0 0;
                    padding: 0 0 0 0;
                    text-align: center;
                }

                #table {
                    width: 100%;
                    height: 100%;
                    display: table;
                }

                #cell {
                    vertical-align: middle;
                    display: table-cell;
                    _position: absolute;
                    _top: 50%;
                }

                #conteudo {
                    width: 500px;
                    margin: 0 auto;
                    _left: -50%;
                    padding: 0 0 0 0;
                    _position: relative;
                    _top: -50%;
                    text-align: left;
                }

                <c:if test="${InicioControle.NFe}">
                    html, body {
                        background: url("images/nfe/bg.png");
                        text-align: left;
                    }

                    #table {
                        position: absolute;
                        width: 1006px;
                        height: 501px;
                        top: 50%;
                        left: 50%;
                        margin-top: -250px;
                        margin-left: -503px;
                    }

                    #cell {
                        vertical-align: middle;
                        display: table-cell;
                        _position: inherit;
                        _top: 0%;
                    }

                    .login-esquerda {
                        background: url("./images/nfe/suporte-esq.png");
                        height: 501px;
                        width: 437px;
                        float: left;
                    }

                    .login-direita {
                        background: url("./images/nfe/suporte-dir.png");
                        height: 501px;
                        width: 569px;
                        float: left;
                    }

                    .logoNFeLogin {
                        margin-top: 81px;
                        margin-left: 124px;
                    }

                    #formLogin {
                        margin-left: 125px;
                        margin-top: 81px;
                    }

                    .tituloNFe {
                        font-family: helvetica, arial, sans-serif;
                        color: #abffff;
                        font-size: 20px;
                        text-align: left
                    }

                    .enfaseTitulo {
                        font-weight: bold;
                        font-size: 20px;
                    }

                    .inputsLoginNFe {
                        margin-top: 24px;
                        width: 320px;
                        height: 162px
                    }

                    .inputLoginNFe {
                        margin-bottom: 18px;
                    }

                    #botaoLogar {
                        background: url("./images/nfe/login-button-3px-entre-estados.png") no-repeat 0 0;
                        height: 42px;
                        width: 131px;
                    }

                    #botaoLogar:hover {
                        background: url("./images/nfe/login-button-3px-entre-estados.png") no-repeat 0 -45;
                        height: 42px;
                        width: 131px;
                    }

                    #botaoLogar:active {
                        background: url("./images/nfe/login-button-3px-entre-estados.png") no-repeat 0 -90;
                        height: 42px;
                        width: 131px;
                    }

                </c:if>
            </style>
        </head>

        <body onload="load();">

        <rich:modalPanel id="panelEmpresas" styleClass="novaModal" autosized="true"
                         showWhenRendered="#{InicioControle.apresentarPanelEscolhaEmpresa}"
                         shadowOpacity="true" width="220" height="90">
        <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkPanelEmpresas"/>
            <rich:componentControl for="panelEmpresas" attachTo="hidelinkPanelEmpresas" operation="hide" event="onclick"/>
        </h:panelGroup>
        </f:facet>

        <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="#{msg_aplic.prt_escolha_empresa_logar}"></h:outputText>
        </h:panelGroup>
        </f:facet>

        <h:form id="formEmpresas" styleClass="pure-form">

        <h:panelGrid columns="2" width="100%" columnClasses="colunaEsquerda, colunaCentralizada">

        <h:selectOneMenu onblur="blurinput(this);"
                         id="menuEmpresa"
                         onfocus="focusinput(this);" styleClass="form" style="font-size: 12px; height: 30px;width: 100%;line-height:30px;"
                         value="#{InicioControle.empresaVO.codigo}">
            <f:selectItems value="#{InicioControle.listaEmpresasPermitidas}"/>
        </h:selectOneMenu>

            <a4j:commandButton  id="unidade" image="images/tick.png" action="#{InicioControle.confirmarEmpresa}" value="#{msg_bt.btn_confirmar}"/>
        </h:panelGrid>

        </h:form>

        </rich:modalPanel>

            <h:form id="formDescobridor">
                <jsp:include page="include_head.jsp" flush="true"/>
                <div id="table">
                    <div id="cell">
                        <c:choose>
                            <c:when test="${InicioControle.NFe}">

                                <div id="container-nfe">
                                    <div class="login-esquerda">
                                        <div class="logoNFeLogin">
                                            <img src="./beta/imagens/pacto-nfe-topo.png" alt="logoNFe"/>
                                        </div>
                                    </div>
                                    <div class="login-direita">
                                        <div id="formLogin" style="position:absolute;">
                                            <div class="tituloNFe">
                                                Busque e Administre
                                            </div>
                                            <div class="tituloNFe">
                                                suas <span class="enfaseTitulo">Notas Fiscais</span>
                                            </div>
                                            <div class="tituloNFe">
                                                com facilidade
                                            </div>

                                            <h:inputHidden value="#{InicioControle.resetParam}"/>
                                            <h:inputHidden value="#{InicioControle.logout}"/>

                                            <div class="inputsLoginNFe">
                                                <div class="inputLoginNFe">
                                                    <h:inputText id="userKey"
                                                                 onkeydown="if (event.keyCode == 13) document.getElementById('botaoLogar').click()"
                                                                 size="30" maxlength="100"
                                                                 style="background: white;
                                                                 height:36px;
                                                                 width: 320px;
                                                                 color: black;
                                                                 vertical-align: middle;
                                                                 padding-left: 6px"
                                                                 value="#{InicioControle.userKey}"/>
                                                </div>
                                                <div class="inputLoginNFe">
                                                    <h:inputText id="userName"
                                                                 onkeydown="if (event.keyCode == 13) document.getElementById('botaoLogar').click()"
                                                                 size="20" maxlength="100"
                                                                 style="background: white;
                                                                 height:36px;
                                                                 width: 320px;
                                                                 color: black;
                                                                 vertical-align: middle;
                                                                 padding-left: 6px"
                                                                 value="#{InicioControle.userName}"/>
                                                </div>
                                                <div class="inputLoginNFe">
                                                    <h:inputSecret id="userPassword"
                                                                   onkeydown="if (event.keyCode == 13) document.getElementById('botaoLogar').click()"
                                                                   size="20" maxlength="64"
                                                                   style="background: white;
                                                                   height:36px;
                                                                   width: 320px;
                                                                   color: black;
                                                                   vertical-align: middle;
                                                                   padding-left: 6px"
                                                                   value="#{InicioControle.userPassword}"/>
                                                </div>
                                                <div style="float: right;">
                                                    <div class="botaoLoginNFe">

                                                        <h:commandLink id="descobrir"
                                                                       style="margin:5px;vertical-align:middle;"
                                                                       action="#{InicioControle.descobrir}">
                                                            <div id="botaoLogar">
                                                            </div>
                                                        </h:commandLink>
                                                    </div>
                                                </div>
                                                <c:if test="${!empty InicioControle.mensagemDetalhada}">
                                                    <div style="width: 189px">
                                                        <table border="0" cellpadding="0" cellspacing="0" width="100%"
                                                               style="margin-top: -4px; margin-left: -10px">
                                                            <tr style="background: url('./images/nfe/warning-10.png') no-repeat; height: 12px">
                                                                <td></td>
                                                            </tr>
                                                            <tr style="background: url('./images/nfe/warning-11.png') repeat-y; height: 2px">
                                                                <td>
                                                                    <div style="margin-left: 15px; margin-right: 15px">
                                                                        <h:outputText id="mensagem" styleClass="mensagem"
                                                                                      value="#{InicioControle.mensagem}"/>
                                                                        <h:outputText id="mensagemDetalhada" styleClass="mensagemDetalhada"
                                                                                      value="#{InicioControle.mensagemDetalhada}"/>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr style="background: url('./images/nfe/warning-12.png') no-repeat; height: 18px">
                                                                <td></td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </c:if>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <div id="conteudo">
                                    <div id="login">
                                        <div class="top"></div>
                                        <div class="middle">

                                            <div style="text-align:center; margin-bottom: 8px">
                                                <img src="${InicioControle.imagemLogo}" alt="ZillyonWeb">
                                            </div>
                                            <table width="477" border="0" cellpadding="0" cellspacing="0" class="tabletext text">
                                                <tr>
                                                    <td width="32" align="left" valign="top">
                                                        <img style="margin-top:3px;" src="images/tick.png" alt="" width="16" height="13">
                                                    </td>
                                                    <td align="left" valign="top">Para utilização do Pacto Zillyon Web, tenha em mãos a sua chave de acesso
                                                        fornecida pela Pacto Soluções. Ela será necessária algumas vezes durante o dia para utilização desse
                                                        software.
                                                    </td>
                                                </tr>
                                            </table>
                                            <table width="477" border="0" cellpadding="0" cellspacing="0" class="tabletext text">
                                                <tr>
                                                    <td width="32" align="left" valign="top">
                                                        <img style="margin-top:3px;" src="images/tick.png" alt="" width="16" height="13">
                                                    </td>
                                                    <td align="left" valign="top">Recomendamos que uma vez conhecida sua chave, você poderá adicionar ao
                                                        endereço (URL) a sua chave de acesso e, posteriormente gravá-lo nos favoritos de seu navegador.
                                                    </td>
                                                </tr>
                                            </table>

                                            <div class="sep" style="margin-bottom:15px;"><img src="images/shim.gif"></div>

                                            <h:inputHidden value="#{InicioControle.resetParam}"/>
                                            <h:inputHidden value="#{InicioControle.logout}"/>
                                            <!-- Entrada de Dados -->
                                            <table class="text">
                                                <!-- Chave do usuário -->
                                                <c:if test="${!InicioControle.desabilitarChave}">
                                                    <c:choose>
                                                        <c:when test="${(empty InicioControle.userKey && !InicioControle.jaExisteUmaConexaoIniciada)}">
                                                            <tr>
                                                                <td width="166" align="right" valign="middle">Chave:</td>
                                                                <td align="left" valign="middle" style="display: inline">
                                                                    <h:inputText id="userKey"
                                                                                 size="30" maxlength="100"
                                                                                 style="background: white;
                                                                                 border: 1px solid #CCC;
                                                                                 border-bottom-color: #999;
                                                                                 border-right-color: #999;
                                                                                 height:26px;
                                                                                 color: black;
                                                                                 margin: 5px;
                                                                                 padding: 5px 8px 0 6px;
                                                                                 padding-right: 5px;
                                                                                 vertical-align: middle;"
                                                                                 value="#{InicioControle.userKey}"/>
                                                                    <h:inputHidden value="#{InicioControle.userKeyParam}"/>
                                                                </td>
                                                                <td style="display: contents">
                                                                    <a4j:commandLink action="#{InicioControle.limparChave}"
                                                                                     tabindex="10"
                                                                                     id="btnLimparChaveInicio"
                                                                                     title="Limpar Chave"
                                                                                     styleClass="linkPadrao texto-size-18"
                                                                                     reRender="form">
                                                                        <i class="fa-icon-eraser"></i>
                                                                    </a4j:commandLink>
                                                                </td>
                                                            </tr>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <tr>
                                                                <td width="166" align="right" valign="middle">Chave:</td>
                                                                <td align="left" valign="middle" style="display: inline">
                                                                    <h:inputText id="userKey"
                                                                                 size="30" maxlength="100"
                                                                                 disabled="true"
                                                                                 style="background: white;
                                                                                 border: 1px solid #CCC;
                                                                                 border-bottom-color: #999;
                                                                                 border-right-color: #999;
                                                                                 height:26px;
                                                                                 color: black;
                                                                                 margin: 5px;
                                                                                 padding: 5px 8px 0 6px;
                                                                                 padding-right: 5px;
                                                                                 vertical-align: middle;"
                                                                                 value="#{InicioControle.userKey}"/>
                                                                </td>
                                                                <td align="left" valign="middle" style="display: contents">
                                                                    <a4j:commandLink action="#{InicioControle.limparChave}"
                                                                                     styleClass="linkPadrao texto-size-18"
                                                                                     id="btnLimparChaveDeslogado"
                                                                                     title="Limpar chave"
                                                                                     reRender="form">
                                                                        <i class="fa-icon-eraser"></i>
                                                                    </a4j:commandLink>
                                                                </td>
                                                            </tr>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </c:if>
                                                <!-- Campo para entrada de data no sistema -->
                                                <%
                                                            if ((((String) request.getParameter("desv")) != null
                                                                    && ((String) request.getParameter("desv")).equalsIgnoreCase("true"))
                                                                    || ((((String) session.getAttribute("desv")) != null
                                                                    && ((String) session.getAttribute("desv")).equalsIgnoreCase("true")))) {
                                                                session.setAttribute("desv", "true");
                                                            }
                                                %>
                                                <c:if test="${SuperControle.permitirAlterarDataSistema}">
                                                    
                                                
                                                <tr>
                                                    <td width="166" align="right" valign="middle">Data sistema:</td>
                                                    <td align="left" valign="middle">
                                                        <a4j:form id="formData" style="vertical-align:middle;">
                                                            <rich:calendar
                                                                id="dataSistema" styleClass="form"
                                                                value="#{InicioControle.dataSistema}" datePattern="dd/MM/yyyy"
                                                                inputSize="10" inputClass="form"
                                                                style="background: white;
                                                                border: 1px solid #CCC;
                                                                border-bottom-color: #999;
                                                                border-right-color: #999;
                                                                height:26px;
                                                                color: black;
                                                                margin: 5px;
                                                                padding: 5px 8px 0 6px;
                                                                padding-right: 5px;
                                                                vertical-align: middle;"
                                                                oninputblur="blurinput(this); limparMsgObrig('form:dataSistemaInputDate', 'dataSistema');"
                                                                oninputfocus="focusinput(this);"
                                                                oninputkeypress="limparMsgObrig('form:dataSistemaInputDate', 'dataSistema');"
                                                                enableManualInput="true" zindex="2" showWeeksBar="false"
                                                                tabindex="1">
                                                                
                                                            </rich:calendar>
                                                            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                                        </a4j:form>
                                                    </td>
                                                </tr>
                                                </c:if>
                                                <!-- Nome do usuário -->
                                                <tr>
                                                    <td width="166" align="right" valign="middle">Usuário:</td>
                                                    <td align="left" valign="middle">
                                                        <h:inputText id="userName"
                                                                     size="20" maxlength="100"
                                                                     style="background: white;
                                                                     border: 1px solid #CCC;
                                                                     border-bottom-color: #999;
                                                                     border-right-color: #999;
                                                                     height:26px;
                                                                     color: black;
                                                                     margin: 5px;
                                                                     padding: 5px 8px 0 6px;
                                                                     padding-right: 5px;
                                                                     vertical-align: middle;"
                                                                     value="#{InicioControle.userName}"/>
                                                    </td>
                                                </tr>
                                                <!-- Senha do usuário e botão de login-->
                                                <tr>
                                                    <td width="166" align="right" valign="middle">Senha:</td>
                                                    <td align="left" valign="middle">
                                                        <h:inputSecret style="background: white;
                                                                       border: 1px solid #CCC;
                                                                       border-bottom-color: #999;
                                                                       border-right-color: #999;
                                                                       height:26px;
                                                                       color: black;
                                                                       margin: 5px;
                                                                       padding: 5px 8px 0 6px;
                                                                       padding-right: 5px;
                                                                       vertical-align: middle;"
                                                                       id="userPassword" size="20" maxlength="64"
                                                                       value="#{InicioControle.userPassword}"/>
                                                        <h:commandButton
                                                            id="descobrir"
                                                            image="./imagens/botaoLogin1.png"
                                                            style="margin:5px;vertical-align:middle;"
                                                            action="#{InicioControle.descobrir}">
                                                        </h:commandButton>
                                                        <a4j:commandButton action="#{RecuperacaoSenhaControle.abrirModalRecuperacaoSenha}"
                                                                           style="margin:5px;vertical-align:middle;"
                                                                           image="images/change_password.png"
                                                                           title="Clique aqui para recuperar sua senha"
                                                                           reRender="panelRecuperacaoSenha, mensagem, mensagemDetalhada"/>
                                                    </td>
                                                </tr>
                                                <c:if test="${InicioControle.numDeTentativas >= CaptchaControle.numTentativasInvalidas}">
                                                    <tr>
                                                        <td align="right" valign="middle">
                                                            <a4j:commandLink value="Trocar imagem"
                                                                             rendered="#{InicioControle.numDeTentativas >= CaptchaControle.numTentativasInvalidas}"
                                                                             reRender="formDescobridor:captcha"/>
                                                        </td>
                                                        <td valign="middle" style="padding-left: 6px">
                                                            <a4j:mediaOutput id="captcha"
                                                                             rendered="#{InicioControle.numDeTentativas >= CaptchaControle.numTentativasInvalidas}"
                                                                             element="img" cacheable="false" session="true"
                                                                             createContent="#{CaptchaControle.paintCaptcha}"
                                                                             value="#{CaptchaControle.mediaData}"
                                                                             mimeType="image/jpeg"/>
                                                        </td>
                                                    </tr>

                                                    <tr>
                                                        <td align="right" valign="middle">
                                                            <h:outputText value="Inforque o código de verificação "/>
                                                        </td>
                                                        <td valign="middle" style="padding-left: 3px; padding-top: 3px">
                                                            <h:inputText size="20" maxlength="20" value="#{CaptchaControle.valor}" style="background: white;
                                                                         border: 1px solid #CCC;
                                                                         border-bottom-color: #999;
                                                                         border-right-color: #999;
                                                                         height:26px;
                                                                         color: black;
                                                                         margin: 3px;
                                                                         padding: 5px 8px 0 6px;
                                                                         padding-right: 5px;
                                                                         vertical-align: middle;"/>
                                                        </td>
                                                    </tr>
                                                </c:if>

                                                <tr>
                                                    <td align="right" valign="middle">&nbsp;</td>
                                                    <td align="left" valign="middle">

                                                        <rich:hotKey selector="#userPassword" key="return"
                                                                     handler="#{rich:element('login')}.onclick();return false;"/>

                                                        <rich:hotKey selector="#userName" key="return"
                                                                     handler="#{rich:element('login')}.onclick();return false;"/>

                                                        <rich:hotKey selector="#userKey" key="return"
                                                                     handler="#{rich:element('login')}.onclick();return false;"/>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <td align="center"><h:outputText id="mensagem" styleClass="mensagem"
                                                                  value="#{InicioControle.mensagem}"/></td>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <td align="center"><h:outputText id="mensagemDetalhada" styleClass="mensagemDetalhada"
                                                                  value="#{InicioControle.mensagemDetalhada}"/></td>
                                                </tr>
                                            </table>
                                            <div class="sep" style="padding-bottom:15px;margin-top:10px"><img src="images/shim.gif"></div>

                                            <div class="textsmall" style="padding:10px 5px 7px 0;text-align:right;width: 350px;">
                                                <img border="0" style="margin-right:4px;" src="images/arrow.gif" alt="" width="4" height="7">
                                                One Application many databases -
                                                <h:outputText styleClass="tituloCamposReduzidos" value="#{SuperControle.versaoSistema} #{SuperControle.instance}"
                                                              title="#{SuperControle.dataVersaoComoString}"/>
                                                <h:outputText styleClass="tituloCamposReduzidos"
                                                              title="#{SuperControle.commitTime}"
                                                              value="#{SuperControle.shortCommitHash}"/>

                                            </div>

                                        </div>
                                        <div class="bottom"></div>
                                    </div>
                                </div>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
            </h:form>

            <script type="text/javascript" language="javascript">
                document.getElementById("formDescobridor:userName").focus();
            </script>
            <c:if test="${InicioControle.NFe}">
                <script type="text/javascript" language="javascript">
                    document.getElementById("formDescobridor:userKey").setAttribute("placeholder","Chave da Empresa");
                    document.getElementById("formDescobridor:userName").setAttribute("placeholder","Username");
                    document.getElementById("formDescobridor:userPassword").setAttribute("placeholder","Senha");
                </script>
            </c:if>
    </html>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true">
        <jsp:param name="naoIncluirPrint" value="true"/>
    </jsp:include>
    <%@include file="includes/include_recuperacao_senha.jsp" %>
</f:view>
