<%--
  Created by IntelliJ IDEA.
  User: <PERSON><PERSON>
  Date: 17/01/2017
  Time: 16:03
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" %>
<%@include file="/includes/imports.jsp" %>

<script type="text/javascript" language="javascript" src="${root}/script/solicitacao.js"></script>

<style>

    .panelConteudoUCP {
        border: 1px solid #aebbbc;
        text-align: left !important;
        background: #29AAE2;
    }

    .panelDireitaUCP {
        line-height: 1;
    }

    .panelInfoUCP {
        height: 10% !important;
        display: flex;
        width: 45%;
        font-size: 12px;
        color: #FFF;
        padding: 15px 10px 15px 15px;
    }

    .panelInfoUCPInterno {
        text-align: right !important;
        padding-right: 10px;
    }

    .tituloOpcoesMenuUCP {
        text-align: left !important;
        font-family: Arial;
        font-weight: bold;
        font-size: 14px;
        color: #2b496f;
    }

    .panelConhecimentoMenuUCP {
        padding: 10px 0 5px 10px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: left !important;
        color: #29AAE2;
    }

    .panelListaNotificacoesUCP {
        padding: 10px 10px 2px 10px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: left !important;
        color: #29AAE2;
    }

    .notificacaoUCP {
        background-color: #FF6E1E !important;
        color: #fff !important;
        font-family: 'Oxygen', sans-serif;
        font-size: 10px !important;
        text-align: center;
        border-radius: 100%;
        padding: 2px 5px 3px 5px;
        position: relative;
        top: -3px;
        margin-left: 5px;
    }

    .notificacaoGeralUCP {
        -webkit-background-clip: padding-box;
        -webkit-box-shadow: rgba(0, 0, 0, 0.701961) 0 1px 1px 0;
        -webkit-font-smoothing: subpixel-antialiased;
        background-color: #F06D29;
        box-shadow: rgba(0, 0, 0, 0.701961) 0 1px 1px 0;
        color: rgb(255, 255, 255) !important;
        cursor: pointer;
        font-family: 'Helvetica Neue', Helvetica, sans-serif;
        font-size: 10px !important;
        line-height: 0 !important;
        text-align: center;
        text-shadow: rgba(0, 0, 0, 0.4) 0 -1px 0;
        border-radius: 10px;
        padding: 2px 5px 2px 5px;
        position: relative;
        top: -15px;
        margin-left: 15px;
    }

    .panelPesquisaUCP {
        background: #E3E3E3;
        text-align: left;
        display: inline-flex;
        padding: 15px 30px 15px 0;
    }

    .panelGeralSugestaoSuperior {
        text-align: left;
    }

    .panelGeralSugestaoInferior {
        width: 55%;
        text-align: left;
        padding: 0 15px 0 15px;
        border-top: 1px solid #aebbbc;
        background: #E3E3E3;
    }

    .panelGeralResultado {
        width: 600px;
        text-align: left;
        padding: 0 15px 15px 15px;
        background: #E3E3E3;
    }

    .panelScrollUCP {
        overflow-y: auto;
        max-height: 350px;
        width: 100%;
    }

    .panelMsgInfoUCP {
        text-align: left !important;
        padding-top: 15px;
        padding-bottom: 10px;
    }

    .textoTituloConhecimentoUCP {
        text-decoration: none;
        text-align: left;
        font-size: 14px;
        color: #29AAE2;
        font-weight: bold;
    }

    .textoDescricaoConhecimentoUCP {
        text-decoration: none;
        font-size: 12px;
        color: #29AAE2;
    }

    .textoAbrirSolicitacaoUCP {
        text-decoration: none;
        font-weight: bold;
        text-align: left;
        font-size: 14px;
        color: #29AAE2;
    }

    .panelTextoAbrirSolicitacaoUCP {
        padding: 5px 15px 15px 15px;
        text-align: left !important;
        background: #E3E3E3;
    }

    .panelRelacionamentoUCP {
        padding: 15px;
        width: 55%;
        background: #eeeeee;
    }

    .panelTipoNotificacaoGeralUCP {
        display: inline-flex;
        width: 100%;
        min-width: 775px !important;
    }

    .panelTipoNotificacaoUCP {
        padding: 15px 15px 5px 15px;
        width: 55%;
        background: #eeeeee;
    }

    .panelConhecimentoFundamentaisGeralUCP {
        display: inline-flex;
        width: 100%;
        min-width: 775px !important;
    }

    .panelRelacionamentoGeralUCP {
        display: inline-flex;
        width: 100%;
        min-width: 775px !important;
    }

    .panelPesquisaGeralUCP {
        display: inline-flex;
        width: 100%;
    }

    .panelConhecimentoMenuResultadoUCP {
        padding: 5px 0 5px 0;
        text-align: left !important;
        color: #29AAE2;
    }

    .inputCarregando:before {
        content: "\f110";
        position: absolute;
        top: 6px;
        right: 12px;
        font-family: FontAwesome;
        font-size: 21px;
        color: #333;
        -webkit-animation-name: spin;
        -webkit-animation-duration: 1000ms;
        -webkit-animation-iteration-count: infinite;
        -webkit-animation-timing-function: linear;
        -moz-animation-name: spin;
        -moz-animation-duration: 1000ms;
        -moz-animation-iteration-count: infinite;
        -moz-animation-timing-function: linear;
        -ms-animation-name: spin;
        -ms-animation-duration: 1000ms;
        -ms-animation-iteration-count: infinite;
        -ms-animation-timing-function: linear;

        animation-name: spin;
        animation-duration: 1000ms;
        animation-iteration-count: infinite;
        animation-timing-function: linear;
    }

    @-moz-keyframes spin {
        from {
            -moz-transform: rotate(0deg);
        }
        to {
            -moz-transform: rotate(360deg);
        }
    }

    @-webkit-keyframes spin {
        from {
            -webkit-transform: rotate(0deg);
        }
        to {
            -webkit-transform: rotate(360deg);
        }
    }

    @keyframes spin {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }

    .inputTextCleanUCP {
        background-image: none !important;
        padding: 8px 10px !important;
        border: 1px solid #ddd !important;
        background-color: #fff !important;
        border-radius: 3px;
        color: #666666;
        font-size: 14px !important;
        /*color: #b4b4b4 !important;*/
        background-image: none;
        -webkit-transition: all .6s;
        -moz-transition: all .6s;
        -ms-transition: all .6s;
        -o-transition: all .6s;
        transition: all .6s;
    }
</style>

<h:panelGroup layout="block" id="panelUCPConhecimentos" styleClass="panelConteudoUCP">

    <%--PANEL DIREITA--%>
    <h:panelGroup layout="block" id="panelDireitaUCP"
                  styleClass="panelDireitaUCP">

        <h:panelGroup layout="block" id="panelGeralSugestaoSuperior"
                      styleClass="panelGeralSugestaoSuperior"
                      rendered="#{empty UCPUsuarioControle.resultadoConhecimentosUCP && not empty UsuarioControle.usuario.notificacoesUsuarioUCP}">

            <%--NOVO PADRAO--%>
            <a4j:repeat id="listaNotificacoesUCP" var="tipoNotificacao"
                        value="#{UsuarioControle.usuario.notificacoesUsuarioUCP}">

                <h:panelGroup layout="block"
                              styleClass="panelTipoNotificacaoGeralUCP"
                              id="panelTipoNotificacaoGeralUCP${tipoNotificacao.tipoNotificacaoUCPEnum.codigo}">

                    <h:panelGroup layout="block"
                                  styleClass="panelInfoUCP">
                        <h:panelGroup layout="block" styleClass="panelInfoUCPInterno">
                            <h:outputText
                                    rendered="#{tipoNotificacao.tipoNotificacaoUCPEnum.codigo == 1}"
                                    value="Novos encontros realizados pela Pacto, pr�ximos � voc�"/>
                            <h:outputText
                                    rendered="#{tipoNotificacao.tipoNotificacaoUCPEnum.codigo == 2}"
                                    value="Novos conte�dos da Universidade Corporativa Pacto compartilhados para voc�"/>
                            <h:outputText
                                    rendered="#{tipoNotificacao.tipoNotificacaoUCPEnum.codigo == 3}"
                                    value="Veja as respostas das suas d�vidas realizadas na Universidade Corporativa Pacto"/>
                        </h:panelGroup>
                        <h:panelGroup layout="block" id="setaTipo${tipoNotificacao.tipoNotificacaoUCPEnum.codigo}">
                            <h:graphicImage value="imagens_flat/icon_seta_direita_UCP.svg" width="55"/>
                        </h:panelGroup>
                    </h:panelGroup>


                    <h:panelGroup layout="block"
                                  styleClass="panelTipoNotificacaoUCP"
                                  id="tipoNotificacao${tipoNotificacao.tipoNotificacaoUCPEnum.codigo}">

                        <h:panelGroup layout="block"
                                      styleClass="tituloOpcoesMenuUCP">
                            <h:panelGroup rendered="#{!tipoNotificacao.tipoNotificacaoUCPEnum.SVG}"
                                          styleClass="#{tipoNotificacao.tipoNotificacaoUCPEnum.icone}"/>
                            <h:graphicImage rendered="#{tipoNotificacao.tipoNotificacaoUCPEnum.SVG}"
                                            value="#{tipoNotificacao.tipoNotificacaoUCPEnum.icone}"
                                            width="15"/>
                            <h:outputText value="#{tipoNotificacao.tipoNotificacaoUCPEnum.descricao}"
                                          style="padding-left: 5px"/>
                            <h:outputText value="#{tipoNotificacao.totalNotificacao}"
                                          styleClass="notificacaoUCP"
                                          style="padding-left: 5px"/>
                        </h:panelGroup>

                        <%--sem agrupar--%>
                        <a4j:repeat var="notificacao" value="#{tipoNotificacao.listaNotificacoes}"
                                    rendered="#{tipoNotificacao.totalNotificacao <= 3}">
                            <h:panelGroup layout="block" styleClass="panelListaNotificacoesUCP">
                                <h:outputLink target="_blank"
                                              title="#{notificacao.mensagem}"
                                              style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"
                                              value="#{notificacao.linkMontadaUCP}">
                                    <i class="fa-icon-info-circle"></i>
                                    <h:outputText value="#{notificacao.mensagem}"/>
                                    <a4j:support event="onclick"
                                                 action="#{UsuarioControle.excluirNotificacaoUsuario}"
                                                 reRender="panelNotificacoesUCP, panelGeralSugestaoSuperior"/>
                                </h:outputLink>
                            </h:panelGroup>
                        </a4j:repeat>

                        <%--agrupando--%>
                        <h:panelGroup layout="block" styleClass="panelListaNotificacoesUCP"
                                      rendered="#{tipoNotificacao.totalNotificacao > 3}">
                            <h:outputLink target="_blank"
                                          title="Clique para visualizar #{tipoNotificacao.totalNotificacao} #{tipoNotificacao.tipoNotificacaoUCPEnum.descricaoAgrupada}"
                                          style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"
                                          value="#{tipoNotificacao.linkAgrupado}">
                                <i class="fa-icon-info-circle"></i>
                                <h:outputText
                                        value="#{tipoNotificacao.totalNotificacao} #{tipoNotificacao.tipoNotificacaoUCPEnum.descricaoAgrupada}"/>
                                <a4j:support event="onclick"
                                             action="#{UsuarioControle.excluirTodasNotificacaoTipoUsuario}"
                                             reRender="panelNotificacoesUCP, panelGeralSugestaoSuperior"/>
                            </h:outputLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </a4j:repeat>
        </h:panelGroup>

        <h:panelGroup layout="block" id="panelRelacionamentoGeralUCP"
                      styleClass="panelRelacionamentoGeralUCP"
                      rendered="#{empty UCPUsuarioControle.resultadoConhecimentosUCP && (UsuarioControle.usuario.existeConversaSolicitacao || UCPUsuarioControle.apresentarAcompanharSolicitacoes)}">

            <h:panelGroup layout="block"
                          styleClass="panelInfoUCP">
                <h:panelGroup layout="block" styleClass="panelInfoUCPInterno">
                    <h:outputText
                            value="Veja as respostas �s solicita��es feitas atrav�s do canal de relacionamento"/>
                </h:panelGroup>
                <h:panelGroup layout="block" id="setaRelaci">
                    <h:graphicImage value="imagens_flat/icon_seta_direita_UCP.svg" width="55"/>
                </h:panelGroup>
            </h:panelGroup>

            <%--Relacionamento--%>
            <h:panelGroup layout="block" id="panelRelacionamentoUCP"
                          styleClass="panelRelacionamentoUCP">
                <h:panelGroup layout="block"
                              styleClass="tituloOpcoesMenuUCP">
                    <i class="fa-icon-comments-o"></i>
                    <h:outputText value="Relacionamento"
                                  style="padding-left: 5px"/>
                    <h:outputText value="#{UsuarioControle.usuario.totalGruposSolicitacao}"
                                  rendered="#{UsuarioControle.usuario.totalGruposSolicitacao > 0}"
                                  styleClass="notificacaoUCP"
                                  style="padding-left: 5px"/>
                </h:panelGroup>

                <%--sem agrupar--%>
                <a4j:repeat var="grupo" value="#{UsuarioControle.usuario.listaGrupoSolicitacao}"
                            rendered="#{UsuarioControle.usuario.totalGruposSolicitacao <= 3}">
                    <h:panelGroup layout="block" styleClass="panelListaNotificacoesUCP" style="height: 15px;">
                        <a4j:commandLink action="#{LoginControle.abrirSocialMailingSolicitacao}"
                                         title="Clique para visualizar a mensagem completa."
                                         reRender="panelNotificacoesUCP"
                                         style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"
                                         oncomplete="#{LoginControle.msgAlert}">
                            <i class="fa-icon-youtube-play"></i>
                            <h:outputText
                                    escape="false"
                                    value="#{grupo.nome}"/>
                        </a4j:commandLink>
                    </h:panelGroup>
                </a4j:repeat>

                <%--agrupando--%>
                <h:panelGroup layout="block" styleClass="panelListaNotificacoesUCP" style="height: 15px;"
                              rendered="#{UsuarioControle.usuario.totalGruposSolicitacao > 3}">
                    <a4j:commandLink action="#{LoginControle.abrirSocialMailingSolicitacao}"
                                     title="Clique para visualizar a mensagem completa."
                                     reRender="panelNotificacoesUCP"
                                     style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"
                                     oncomplete="#{LoginControle.msgAlert}">
                        <i class="fa-icon-youtube-play"></i>
                        <h:outputText
                                value="#{UsuarioControle.usuario.totalGruposSolicitacao} respostas de solicita��es"/>
                    </a4j:commandLink>
                </h:panelGroup>

                <%-- PARA CLIENTE VERIFICAR AS MENSAGEM ANTIGAS JA FINALIZADAS QUANDO NAO TIVER NENHUMA EM ABERTO --%>
                <h:panelGroup layout="block" styleClass="panelListaNotificacoesUCP" style="height: 15px;"
                              rendered="#{UsuarioControle.usuario.totalGruposSolicitacao == 0 && UsuarioControle.usuario.existeConversaSolicitacao}">
                    <a4j:commandLink action="#{LoginControle.abrirSocialMailingSolicitacao}"
                                     title="Clique para visualizar o hist�rico das solicita��es j� finalizadas."
                                     reRender="panelNotificacoesUCP"
                                     style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"
                                     oncomplete="#{LoginControle.msgAlert}">
                        <i class="fa-icon-info-circle"></i>
                        <h:outputText value="Hist�rico de respostas"/>
                    </a4j:commandLink>
                </h:panelGroup>

                <%--ACOMPANHAR SOLICITACOES SEMPRE FIXO
                <h:panelGroup layout="block"
                              rendered="#{UCPUsuarioControle.apresentarAcompanharSolicitacoes}"
                              styleClass="panelConhecimentoMenuUCP">
                    <a href="#"
                       onclick="abrirUrlMinhaPaginaService('${UCPUsuarioControle.serviceUsuario}','${UCPUsuarioControle.serviceSenha}')"
                       style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;">
                        <i class="fa-icon-question-sign"></i>
                        <h:outputText value="Acompanhe suas solicita��es"/>
                    </a>
                </h:panelGroup> --%>
                <h:panelGroup layout="block" styleClass="panelListaNotificacoesUCP" style="height: 15px;">
                    <a4j:commandLink oncomplete="#{CanalPactoControle.mensagemNotificar}"
                                     action="#{LoginControle.abrirModuloCanalCliente}"
                                     style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;">
                        <i class="fa-icon-question-sign"></i>
                        <h:outputText value="Acompanhe suas solicita��es"/>
                    </a4j:commandLink>
                </h:panelGroup>

            </h:panelGroup>


        </h:panelGroup>


        <h:panelGroup layout="block" id="panelConhecimentoFundamentaisGeralUCP"
                      styleClass="panelConhecimentoFundamentaisGeralUCP"
                      rendered="#{empty UCPUsuarioControle.resultadoConhecimentosUCP && not empty UCPControle.listaConhecimentosFundamentais}">

            <h:panelGroup layout="block"
                          styleClass="panelInfoUCP">
                <h:panelGroup layout="block" styleClass="panelInfoUCPInterno">
                    <h:outputText
                            value="Veja os conhecimentos fundamentais para a gest�o da sua academia"/>
                </h:panelGroup>
                <h:panelGroup layout="block" id="setaConhe">
                    <h:graphicImage value="imagens_flat/icon_seta_direita_UCP.svg" width="55"/>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" id="panelGeralSugestaoInferior"
                          styleClass="panelGeralSugestaoInferior">

                <%--Conhecimentos fundamentais--%>
                <h:panelGroup layout="block" id="panelConhecimentos"
                              style="padding-top: 15px;">
                    <h:panelGroup layout="block"
                                  styleClass="tituloOpcoesMenuUCP">
                        <i class="fa-icon-flag"></i> Conhecimentos fundamentais
                    </h:panelGroup>

                    <h:panelGroup layout="block" id="panelListaConhecimentosFunda"
                                  style="padding-top: 10px;">
                        <a4j:repeat id="listaConhecimentosFunda" var="conhe"
                                    value="#{UCPControle.listaConhecimentosFundamentais}">
                            <h:panelGroup layout="block"
                                          styleClass="panelConhecimentoMenuUCP">
                                <h:outputLink
                                        value="#{conhe.linkPergunta}"
                                        style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"
                                        target="_blank">
                                    <i class="fa-icon-youtube-play"></i>
                                    <h:outputText value="#{conhe.titulo}"/>
                                </h:outputLink>
                            </h:panelGroup>
                        </a4j:repeat>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" id="panelPesquisaGeralUCP"
                      styleClass="panelPesquisaGeralUCP"
                      style="#{(empty UCPUsuarioControle.resultadoConhecimentosUCP && empty UsuarioControle.usuario.listaGrupoSolicitacao && empty UsuarioControle.usuario.notificacoesUsuarioUCP && empty UCPControle.listaConhecimentosFundamentais) ? 'min-width: 775px !important;' : ''}">

            <h:panelGroup layout="block"
                          rendered="#{empty UCPUsuarioControle.resultadoConhecimentosUCP}"
                          style="padding-top: 23px"
                          styleClass="panelInfoUCP">
                <h:panelGroup layout="block" styleClass="panelInfoUCPInterno">
                    <h:outputText
                            value="N�o encontrou o que queria? Utilize nossa busca inteligente"/>
                </h:panelGroup>
                <h:panelGroup layout="block" id="setassE">
                    <h:graphicImage value="imagens_flat/icon_seta_direita_UCP.svg" width="55"/>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" id="panelPesquisaUCP"
                          style="#{empty UCPUsuarioControle.resultadoConhecimentosUCP ? 'width: 55%' : 'width: 100%'}"
                          styleClass="panelPesquisaUCP">

                <h:panelGroup layout="block" id="panelBtnVideo">
                    <a4j:commandLink id="mostrarVideoUCP"
                                     title="Veja uma explica��o desse novo recurso"
                                     reRender="modalVideoUCP"
                                     oncomplete="Richfaces.showModalPanel('modalVideoUCP');">
                        <h:graphicImage value="imagens_flat/tip-icon.svg" width="30" height="32"
                                        style="padding-top: 2px;"/>
                    </a4j:commandLink>
                </h:panelGroup>

                <h:panelGroup layout="block" id="panelInputPesquisaUCP" styleClass="panelInputPesquisaUCP"
                              style="width: 95%">
                    <h:inputText id="searchInputUCP"
                                 style="width: 100%"
                                 onclick="removeEvent(event);"
                                 styleClass="inputTextCleanUCP"
                                 title="Digite o nome para buscar..."
                                 autocomplete="off"
                                 value="#{UCPUsuarioControle.filtroUCP}"
                                 onkeypress="bloquearEnter();">

                        <a4j:queue name="queueFiltro" ignoreDupResponses="true" requestDelay="1000"
                                   onsubmit="iniciaCarregando()"
                                   timeout="60000"/>

                        <a4j:support status="statusInComponent" eventsQueue="queueFiltro"
                                     event="onkeyup"
                                     oncomplete="focarPlaceHolderUCP();finalizaCarregando()"
                                     action="#{UCPUsuarioControle.filtrarConhecimentosUCP}"
                                     reRender="panelUCPConhecimentos"/>
                    </h:inputText>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>


        <h:panelGroup layout="block" id="panelGeralResultado" styleClass="panelGeralResultado"
                      rendered="#{not empty UCPUsuarioControle.resultadoConhecimentosUCP}">

            <h:panelGroup layout="block" id="panelMsgInfoUCP"
                          styleClass="panelMsgInfoUCP">
                <h:outputText value="#{UCPUsuarioControle.msgApresentarResultado}"
                              styleClass="tituloOpcoesMenuUCP"/>
                <h:outputText value="#{UCPUsuarioControle.msgTotalPesquisa}"
                              style="float: right"
                              styleClass="tituloOpcoesMenuUCP"/>
            </h:panelGroup>

            <h:panelGroup layout="block"
                          id="panelScrollUCP"
                          styleClass="panelScrollUCP">

                <a4j:repeat id="listaConhecimentos" var="conhe"
                            value="#{UCPUsuarioControle.resultadoConhecimentosUCP}">
                    <h:panelGroup layout="block"
                                  styleClass="panelConhecimentoMenuResultadoUCP">
                        <h:panelGroup layout="block" id="panelTituloConhecimento${conhe.codigo}"
                                      style="text-align: left; padding-left: 5px; padding-top: 5px; padding-right: 10px; max-width: 600px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                            <h:outputLink value="#{conhe.linkPerguntaSolicitacao}&duvida=#{UCPUsuarioControle.filtroCripto}"
                                          title="#{conhe.descricao}"
                                          target="_blank">
                                <h:outputText value="#{conhe.posicao}. #{conhe.titulo}"
                                              styleClass="textoTituloConhecimentoUCP"/>
                            </h:outputLink>
                        </h:panelGroup>
                        <h:panelGroup layout="block" id="panelDescricaoConhecimento${conhe.codigo}"
                                      style="text-align: left; padding-left: 20px; padding-top: 5px; padding-right: 20px;">
                            <h:outputLink
                                    value="#{conhe.linkPerguntaSolicitacao}&duvida=#{UCPUsuarioControle.filtroCripto}"
                                    target="_blank"
                                    title="#{conhe.descricao}">
                                <h:outputText value="#{conhe.descricaoApresentar}"
                                              styleClass="textoDescricaoConhecimentoUCP"/>
                            </h:outputLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </a4j:repeat>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" id="panelTextoAbrirSolicitacaoGeralUCP" style="display: inline-flex; width: 100%;"
                      rendered="#{UCPUsuarioControle.apresentarBtnSuporte}">

            <h:panelGroup layout="block"
                          rendered="#{empty UCPUsuarioControle.resultadoConhecimentosUCP}"
                          styleClass="panelInfoUCP">
                <%--<h:panelGroup layout="block" styleClass="panelInfoUCPInterno">--%>
                <%--<h:outputText--%>
                <%--value="Abrir solicita��o de atendimento"/>--%>
                <%--</h:panelGroup>--%>
                <%--<h:panelGroup layout="block" id="setaSuporte">--%>
                <%--<h:graphicImage value="imagens_flat/icon_seta_direita_UCP.svg" width="55"/>--%>
                <%--</h:panelGroup>--%>
            </h:panelGroup>

            <h:panelGroup layout="block"
                          id="panelTextoAbrirSolicitacaoUCP"
                          style="#{empty UCPUsuarioControle.resultadoConhecimentosUCP ? 'width: 55%' : 'width: 100%'}"
                          styleClass="panelTextoAbrirSolicitacaoUCP">
                <h:outputLink
                        value='#{SuperControle.contextPath}/redir?up&solicitacao=true&duvida=#{UCPUsuarioControle.filtroCripto}'
                        styleClass="textoAbrirSolicitacaoUCP"
                        target="_blank">
                    <h:outputText value="N�o encontrou sua resposta ? Clique Aqui."
                                  styleClass="textoAbrirSolicitacaoUCP"/>
                </h:outputLink>
            </h:panelGroup>
        </h:panelGroup>

    </h:panelGroup>

    <a4j:commandLink id="btnLimparBuscaUCP"
                     style="visibility: hidden;"
                     styleClass="btnLimparBuscaUCP"
                     action="#{UCPUsuarioControle.inicializaBuscaUCP}"
                     status="false"
                     oncomplete="focarPlaceHolderUCP()"
                     reRender="panelUCPConhecimentos"/>
</h:panelGroup>


<rich:modalPanel id="modalVideoUCP" styleClass="novaModal" autosized="true" shadowOpacity="true" width="380"
                 height="150">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Busca F�cil"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelink1"/>
            <rich:componentControl for="modalVideoUCP" attachTo="hidelink1" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGroup layout="block" styleClass="paginaFontResponsiva">
        <object width="640" height="351">
            <param name="allowfullscreen" value="true"/>
            <param name="allowscriptaccess" value="always"/>
            <param name="movie"
                   value="https://vimeo.com/moogaloop.swf?clip_id=202201765&amp;force_embed=1&amp;server=vimeo.com&amp;show_title=1&amp;show_byline=1&amp;show_portrait=1&amp;color=00adef&amp;fullscreen=1&amp;autoplay=1&amp;loop=0"/>
            <embed src="https://vimeo.com/moogaloop.swf?clip_id=202201765&amp;force_embed=1&amp;server=vimeo.com&amp;show_title=1&amp;show_byline=1&amp;show_portrait=1&amp;color=00adef&amp;fullscreen=1&amp;autoplay=1&amp;loop=0"
                   type="application/x-shockwave-flash" allowfullscreen="true" allowscriptaccess="always" width="640"
                   height="351"></embed>
        </object>
    </h:panelGroup>
</rich:modalPanel>

<script type="text/javascript">

    function limparBuscaUCP() {
        /*if (document.getElementsByClassName('inputTextCleanUCP')[0] != null) {
            document.getElementsByClassName('inputTextCleanUCP')[0].focus();
            document.getElementsByClassName('inputTextCleanUCP')[0].value = '';
            document.getElementsByClassName('inputTextCleanUCP')[0].setAttribute("placeholder", "Qual a sua d�vida?");
        }*/

        var inputTextCleanUCP = jQuery('.inputTextCleanUCP');
        inputTextCleanUCP.focus();
        inputTextCleanUCP.val("");
        inputTextCleanUCP.attr('placeholder', "Qual a sua d�vida?");


        //document.getElementsByClassName('btnLimparBuscaUCP')[0].click();
        jQuery('.btnLimparBuscaUCP').click();
    }

    function removeEvent(e) {
        e.stopPropagation();
    }

    function focarPlaceHolderUCP() {

        var inputTextCleanUCP = jQuery('.inputTextCleanUCP');
        var tmp = inputTextCleanUCP.val();

        inputTextCleanUCP.val("");
        inputTextCleanUCP.val(tmp);
        inputTextCleanUCP.focus();
        inputTextCleanUCP.attr('placeholder', "Qual a sua d�vida?");
        /*if (document.getElementsByClassName('inputTextCleanUCP')[0] != null) {
            temp = document.getElementsByClassName('inputTextCleanUCP')[0].value;
            document.getElementsByClassName('inputTextCleanUCP')[0].value = '';
            document.getElementsByClassName('inputTextCleanUCP')[0].value = temp;
            document.getElementsByClassName('inputTextCleanUCP')[0].focus();
            document.getElementsByClassName('inputTextCleanUCP')[0].setAttribute("placeholder", "Qual a sua d�vida?");
        }*/
    }

    function iniciaCarregando() {
        /*var d = document.getElementsByClassName("panelInputPesquisaUCP")[0];
        d.className += " inputCarregando";*/
        jQuery('.panelInputPesquisaUCP').addClass( "inputCarregando" );
    }

    function finalizaCarregando() {
        /*var d = document.getElementsByClassName("panelInputPesquisaUCP")[0];
        d.classList.remove("inputCarregando");*/
        jQuery('.panelInputPesquisaUCP').removeClass( "inputCarregando" );
    }

</script>

