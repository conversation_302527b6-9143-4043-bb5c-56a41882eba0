<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>


<script type="text/javascript"
        src="${pageContext.request.contextPath}/script/tooltipster/jquery.tooltipster.min.js"></script>

<style>
    .div-geral-experimente {
        display: grid;
        padding: 1.5% 0 0 1.5%;
        width: 96%;
        grid-template-columns: 2fr 0fr;
    }

    .div-experimente {
        border-radius: 5px;
        font-family: "Nunito Sans", sans-serif !important;
        padding: 10px;
        align-items: center;
        display: flex;
        background-color: #FEE6E6 !important;
        color: #E10505 !important;
    }

    .switch {
        position: relative;
        display: inline-block;
        width: 30px;
        height: 17px;
    }

    /* Hide default HTML checkbox */
    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    /* The slider */
    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        -webkit-transition: .4s;
        transition: .4s;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 13px;
        width: 13px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        -webkit-transition: .4s;
        transition: .4s;
    }

    input:checked + .slider {
        background-color: #2196F3;
    }

    input:focus + .slider {
        box-shadow: 0 0 1px #2196F3;
    }

    input:checked + .slider:before {
        -webkit-transform: translateX(13px);
        -ms-transform: translateX(13px);
        transform: translateX(13px);
    }

    /* Rounded sliders */
    .slider.round {
        border-radius: 17px;
    }

    .slider.round:before {
        border-radius: 50%;
    }

    .padrao-conf {
        display: flex;
        color: #797D86;
        font-size: 16px;
        font-weight: 400;
        line-height: 16px;
        /*padding: 30px 0px 0 40px;*/
        /*width: calc(100% - 80px);*/
        justify-content: flex-end;
    }

    .padrao-conf label {
        margin-left: 8px;
    }

    .padrao-conf .clicavel {
        cursor: pointer;
    }

    .bottom-container-msg-configuracao-titulo {
        width: 100%;
        font-family: 'Nunito Sans', sans-serif !important;
        font-style: normal;
        font-weight: 600;
        font-size: 14px;
        color: #55585E;
        align-items: center;
        text-align: center;
        padding-top: 20px;
    }

    .bottom-container-msg-configuracao-info {
        width: 100%;
        font-family: 'Nunito Sans', sans-serif !important;
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        color: #55585E;
        align-items: center;
        text-align: center;
        padding-top: 20px;
    }
</style>


<h:panelGroup layout="block" styleClass="div-geral-experimente"
              rendered="#{LoginControle.usuarioLogado.usuarioPactoSolucoes}">
    <h:panelGroup layout="block"
                  styleClass="div-experimente">
        <h:graphicImage value="images/pct-alert-triangle.svg"
                        style="width: 16px; padding-right: 5px; padding-bottom: 2px;"/>
        <h:outputText
                value="Nos próximos meses essa tela será descontinuada. Experimente a nova versão e teste as melhorias. Você poderá nos enviar feedbacks e ajudar a construir um sistema melhor para o seu dia a dia."
                styleClass="texto-size-14"/>
    </h:panelGroup>
</h:panelGroup>

<h:panelGroup layout="block" id="divSuperiorConfig"
              style="display: flex; align-items: center; padding: 1.5% 0 0 1.5%; width: 96%;">
    <h:panelGroup layout="block" id="divTituloConfiguracao"
                  style="width: 60%;">
        <h:outputText value="Configurações" styleClass="container-header-titulo"/>
        <%--        <h:outputLink styleClass="linkWiki"--%>
        <%--                      value="#{SuperControle.urlBaseConhecimento}configuracoes"--%>
        <%--                      title="Clique e saiba mais: Configurações" target="_blank">--%>
        <%--            <i class="fa-icon-question-sign" style="font-size: 18px"></i>--%>
        <%--        </h:outputLink>--%>
    </h:panelGroup>
    <h:panelGroup layout="block" style="width: 40%;">
        <span class="padrao-conf"
              id="div-switch-nova-versao-config">
            <span class="clicavel" onclick="abrirNovaTelaConfiguracao()">Usar nova versão</span>
            <label class="switch clicavel" onclick="abrirNovaTelaConfiguracao()">
                <input type="checkbox"
                       id="switch-nova-versao-config">
                <span class="slider round"></span>
            </label>
        </span>
    </h:panelGroup>
</h:panelGroup>

<div class="container-msg-configuracao" style="height: 330px;">

    <div class="center-container-msg-configuracao">
        <h:graphicImage style="width: 112px; height: 112px;" url="/imagens/img_lampada_configuracao.png"/>
    </div>

    <div class="bottom-container-msg-configuracao-titulo">
        <i class="pct  pct-arrow-up-left" style="font-size: 60px; color: #D7D8DB"></i>
        <h:outputText value="Acesse as configurações de cada módulo usando o menu lateral."/>
    </div>

    <div class="bottom-container-msg-configuracao-titulo">
        <h:outputText
                value="A nova versão da tela de configuração já está disponível, ative o \"Usar nova versão\" e experimente!"/>
    </div>
    <div class="bottom-container-msg-configuracao-info">
        Mas se preferir, você pode usar a versão atual acessando as configurações de cada módulo usando o menu
        lateral.
    </div>

    <div class="beta-container">
        <a4j:jsFunction name="abrirNovaTelaConfiguracao"
                        oncomplete="#{ConfiguracaoSistemaControle.msgAlert}"
                        action="#{ConfiguracaoSistemaControle.abrirNovaConfiguracao}">
        </a4j:jsFunction>
    </div>
</div>
<style>
    .beta-container {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn-config-beta {
        background-color: #6c757d;
        margin-top: 20px;
        height: 42.5px;
        width: 291px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        margin-bottom: 20px;
    }

    .btn-config-beta > a, .btn-config-beta > a:hover {
        text-decoration: none;
        color: #FFFFFF;
        font-size: 18px;
        font-family: "Nunito Sans", sans-serif;
        text-decoration: none;
        font-weight: 400;
    }

</style>
