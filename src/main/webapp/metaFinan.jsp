<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<head><%@include file="/includes/include_import_minifiles.jsp" %></head>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
        <html>
        <jsp:include page="include_head.jsp" flush="true" />
        <body>
        <table width="100%" align="center" height="100%" border="0" cellpadding="0" cellspacing="0">
            <tr>
                <td height="77" align="left" valign="top" class="bgtop"><jsp:include page="include_top.jsp" flush="true" />	</td>
            </tr>
            <tr>
                <td height="48" align="left" valign="top" class="bgmenu"><jsp:include page="include_menu.jsp" flush="true" /></td>
            </tr>
            <tr>
                <td align="left" valign="top" class="bglateral">
                    <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td width="206" align="center" valign="top" class="bglateraltop" style="padding-top:6px;">
                                <jsp:include page="menuFinanceiro.jsp" flush="true"/>
                                <jsp:include page="include_box_descricao.jsp" flush="true" />
                            </td>
                            <td align="top" valign="top" height="100%" style="padding:7px 15px 0 20px;">
                                <%@include file="includes/bi/include_bi_metasFinanceiras.jsp" %>
                                <h:panelGroup>
                                    <h:outputText rendered="#{!LoginControle.permissaoAcessoMenuVO.visualizaRMetaFinanceiraEmpresaBI
                                                            						&& !LoginControle.permissaoAcessoMenuVO.visualizarMetasFinanceirasTodasEmpresas
                                                            						&& !MetaFinanceiroBIControle.usuarioTemMeta}"
                                                  style="color:red; font-size:16px"
                                                  value="#{msg.msg_usuarioNaoPossuiPermissaoMetasFinanceiras}">
                                    </h:outputText>
                                </h:panelGroup>

                            </td>
                        </tr>
                        <tr>
                            <td height="93" align="left" colspan="2" valign="top" class="bgrodape">
                                <jsp:include page="include_rodape.jsp" flush="true" />
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </h:form>

    <%@include file="pages/finan/includes/include_modal_consultaMetaFinanceiro.jsp" %>
</f:view>