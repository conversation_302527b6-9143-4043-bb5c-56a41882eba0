<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@include file="includes/include_imports.jsp" %>
<link href="../../css_pacto.css" rel="stylesheet" type="text/css">
<link href="../../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../../css/jquery.treeTable.css" rel="stylesheet" type="text/css">
<head><script type="text/javascript" language="javascript" src="../../script/script.js"></script></head>

<script type="text/javascript" src="../../script/jquery.js"></script>
<script type="text/javascript" src="../../script/jquery.treeTable.js"></script>
<script type="text/javascript" src="../../script/demonstrativoFinan.js"></script>


<c:set var="moduloSession" value="1" scope="session" />
<script type="text/javascript">
    $.noConflict();
</script>

<style>
    .codigoCentro{
        font-family: Arial, Helvetica, sans-serif;
        font-size: 11px;
        text-decoration: none;
        line-height: normal;
        font-weight: bold;
        text-transform: none;
        color: gray;
    }


</style>
<f:view>
    <jsp:include page="../../includes/include_carregando_ripple.jsp"/>
    <html>
        <!-- Inclui o elemento HEAD da página -->
        <head>
            <%@include file="includes/include_head_finan.jsp" %>
            <script type="text/javascript" language="javascript" src="${contextoFinan}/script/telaInicial.js"></script>
        </head>
        <body onload="naoMostrarBotaoEditar();">
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:form id="formTopo" style="overflow-x: visible !important;">
                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                    <jsp:include page="../../include_menu_fin_flat.jsp" flush="true"/>
                </h:panelGroup>
            </h:form>
            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText styleClass="container-header-titulo"
                                                      value="#{msg_aplic.prt_Finan_ConsultaCentroCustos_tituloForm} "/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlBaseConhecimento}como-cadastrar-um-centro-de-custos/"
                                                      title="Clique e saiba mais: Centro de Custos"
                                                      target="_blank" >
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>

                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:form id="formBotoes">
                                        <%-- Começo da Página  --%>
                                        <a4j:commandButton id="adicionar" reRender="painelEdicaoCentroCustos"
                                                           action="#{CentroCustosControle.incluir}" value="#{msg_bt.btn_adicionar}"
                                                           oncomplete="Richfaces.showModalPanel('painelEdicaoCentroCustos')"
                                                           image="/imagens/botaoNovo.png" styleClass="botoes"/>
                                        <rich:spacer width="17px;"></rich:spacer>
                                        <a4j:commandButton oncomplete="abrirPopup('visualizarImpressaoCentroCustos.jsp', 'RelatórioCentroCustos', 780, 595);"
                                                           image="../../imagens/btn_VisualizarImpressao.png" value="Visualizar Impressão" >
                                        </a4j:commandButton>
                                        <br/>
                                    </h:form>
                                    <%@include file="include_centroCustosCons.jsp" %>
                                    </td>
                                </h:panelGroup>
                            </h:panelGroup>=
                        </h:panelGroup>
                        <h:form id="formMenu">
                            <jsp:include page="cadastros/includes/include_box_menulatcadastrosfinan.jsp" flush="true"/>
                        </h:form>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../include_rodape_flat.jsp" flush="true" />
        </h:panelGroup>
        </body>
    </html>

    <%@include file="/pages/finan/includes/include_modalSelecaoCentroCusto.jsp" %>


    <!-- ---------------------------------------------- INICIO MODAL DE EDIÇÃO / ADIÇÃO ------------------------------------------------- -->
    <rich:modalPanel id="painelEdicaoCentroCustos" autosized="true"
                     shadowOpacity="true" width="500" height="350">
        <f:facet name="header">
            <jsp:include page="cadastros/includes/topoReduzido.jsp" />
        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Centro de Custos"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidelink1" />
                <rich:componentControl for="painelEdicaoCentroCustos"
                                       attachTo="hidelink1" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formEdicaoCentroCustos" style="margin-top: 10px" ajaxSubmit="true">
            <h:panelGrid id="formCentro" columns="2" rowClasses="linhaImpar, linhaPar"
                         columnClasses="classEsquerda, classDireita" width="100%">

                <h:outputText rendered="#{CentroCustosControle.centro.codigo gt 0}" styleClass="tituloCampos" value="Código Interno:" />
                <h:outputText rendered="#{CentroCustosControle.centro.codigo gt 0}" styleClass="tituloCampos" value="#{CentroCustosControle.centro.codigo}" />

                <h:outputText rendered="#{CentroCustosControle.centro.codigo gt 0}" styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_CadastroCentroCustos_codigoCentro}" />
                <h:panelGroup rendered="#{CentroCustosControle.centro.codigo gt 0}" >
                    <h:inputText disabled="#{!CentroCustosControle.alterarCodigo}" id="alterarCodigoCentro"  size="40" maxlength="255" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form" value="#{CentroCustosControle.centro.codigoCentro}" />
                    <h:panelGroup>
                        <h:selectBooleanCheckbox value="#{CentroCustosControle.alterarCodigo}">
                            <a4j:support event="onclick" reRender="alterarCodigoCentro"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_CadastroPlanoConta_alterarCodigo}" />
                    </h:panelGroup>
                </h:panelGroup>

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_CadastroCentroCustos_centroPai}" />
                <h:panelGroup id="centroCusto">
                    <h:inputText disabled="#{CentroCustosControle.disabledCampoCentroCustoPai}" id="nomeCentroSelecionado"
                                 size="50"
                                 maxlength="50"
                                 onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{CentroCustosControle.centroNome}" >
                        <a4j:support event="onchange" action="#{CentroCustosControle.setarCentroVazio}" reRender="panelMensagem, codigoCentroCusto"/>
                    </h:inputText>
                    <rich:suggestionbox   height="200" width="400"
                                          for="nomeCentroSelecionado"
                                          status="statusInComponent"
                                          immediate="true"
                                          suggestionAction="#{CentroCustosControle.executarAutocompletePesqCentroCusto}"
                                          minChars="1"
                                          rowClasses="linhaImpar, linhaPar"
                                          var="result"  id="suggestionCentroCusto">
                        <a4j:support event="onselect"
                                     reRender="nomeCentroSelecionado, panelMensagem"
                                     action="#{CentroCustosControle.selecionarCentroCusto}" oncomplete="#{rich:element('descricao')}.focus();">
                        </a4j:support>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Nome"  styleClass="textverysmall"/>
                            </f:facet>
                            <h:outputText styleClass="textverysmall" value="#{result.descricaoCurta}" />
                        </h:column>
                    </rich:suggestionbox>

                    <h:panelGroup id="panelTrocarPai">
                        <a4j:commandButton reRender="panelMensagem" rendered="#{!CentroCustosControle.disabledCampoCentroCustoPai}"
                                           id="btAddCentro" value="Consultar"
                                           oncomplete="Richfaces.showModalPanel('modalCentros')" />
                    </h:panelGroup>
                    <c:if test="${CentroCustosControle.btTrocarCentroPai}">
                        <h:selectBooleanCheckbox value="#{CentroCustosControle.btTrocarCentroPaiMarcado}">
                            <a4j:support event="onclick" action="#{CentroCustosControle.habilitarCampoCentroPai}"
                                         reRender="nomeCentroSelecionado,panelTrocarPai,suggestionCentroCusto"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_CadastroCentroCustos_trocarCentroPai}" />
                    </c:if>
                </h:panelGroup>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_CadastroCentroCustos_descricao}" />
                <h:panelGroup>
                    <h:inputText  id="descricao"  size="40" maxlength="255" onblur="blurinput(this);"
                                  onfocus="focusinput(this);" styleClass="form" value="#{CentroCustosControle.centro.descricao}" />
                </h:panelGroup>
            </h:panelGrid>
            <br/>
            <center>
                <a4j:commandButton reRender="formEdicaoCentroCustos"
                                   action="#{CentroCustosControle.incluir}" value="#{msg_bt.btn_adicionar}"
                                   image="/imagens/botaoNovo.png" styleClass="botoes"/>
                <rich:spacer  width="10"/>
                <a4j:commandButton id="ok"
                                   action="#{CentroCustosControle.salvar}"
                                   reRender="formEdicaoCentroCustos,form:dados"
                                   oncomplete="atualizarTreeViewCentroCustos('#{CentroCustosControle.codigoCentroPaiSelecionado}');"
                                   image="/imagens/botaoGravar.png"  styleClass="botoes"/>
                <rich:spacer rendered="#{CentroCustosControle.btExcluir}" width="10"/>
                <a4j:commandButton rendered="#{CentroCustosControle.btExcluir}" image="/imagens/botaoExcluir.png"
                                   reRender="formEdicaoCentroCustos,panelMensagem,form:dados, formTrocarPlano"
                                   oncomplete="#{CentroCustosControle.fecharModalExclusao}"
                                   onclick="if(!confirm('Deseja realmente excluir este centro de custos?')) {return false;}"
                                   action="#{CentroCustosControle.excluir}"/>
                <rich:spacer width="10"/>
                <a4j:commandButton reRender="painelEdicaoCentroCustos" value="#{msg_bt.btn_fechar}"
                                   oncomplete="Richfaces.hideModalPanel('painelEdicaoCentroCustos');"
                                   image="/imagens/botaoFechar.png" styleClass="botoes"/>
            </center>
            <br/>
            <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText value=" "/>
                    </h:panelGrid>
                    <h:commandButton  rendered="#{CentroCustosControle.sucesso}" image="/imagens/sucesso.png"/>
                    <h:commandButton rendered="#{CentroCustosControle.erro}" image="/imagens/erro.png"/>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{CentroCustosControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{CentroCustosControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
            <br/>
        </a4j:form>
    </rich:modalPanel>

    <!-- ---------------------------------------------- FIM MODAL DE EDIÇÃO / ADIÇÃO ------------------------------------------------- -->

    <a4j:outputPanel>
<!-- Modal de Exclusão -->
    <rich:modalPanel id="modalTrocarCentro" autosized="true"
                     shadowOpacity="true" width="500" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Trocar Centro para Exclusão"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidelinktrocar" />
                <rich:componentControl for="modalTrocarCentro"
                                       attachTo="hidelinktrocar" operation="hide" event="onclick" />
            </h:panelGroup>
        </f:facet>


        <a4j:form id="formTrocarPlano">
            <center>
                <h:outputText styleClass="tituloBold" value="Informe para qual Centro de Custos irão os lançamentos do centro a ser excluído"/>
            </center>
            <br/>
            <h:panelGrid columns="2" width="100%">
	            <h:outputText styleClass="tituloCampos" value="Centro de Custos"/>
				<h:panelGroup>
				    <%@include file="includes/include_SuggestionCentroCusto.jsp" %>
				</h:panelGroup>

            </h:panelGrid>
            <br/>
            <center>
            	<a4j:commandButton value="Confirmar" image="/images/finan/confirmar.png" action="#{CentroCustosControle.excluirTrocando}"
            					   reRender="formEdicaoPlanoContas,panelMensagem,form:dados"
            					   oncomplete="#{CentroCustosControle.fecharModalExclusao}">

            	</a4j:commandButton>
            	&nbsp;
            	<a4j:commandButton value="Cancelar" image="/imagens/Cancelar_Modal.png"
            					   oncomplete="Richfaces.hideModalPanel('modalTrocarCentro');"
            					   status="false">
            	</a4j:commandButton>

            </center>

        </a4j:form>

    </rich:modalPanel>
</a4j:outputPanel>
<!-- FIM: Modal de Exclusão -->
</f:view>

