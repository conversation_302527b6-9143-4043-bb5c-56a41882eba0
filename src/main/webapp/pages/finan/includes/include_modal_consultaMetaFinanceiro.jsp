<%@include file="include_imports.jsp" %>

<rich:modalPanel id="modalPanelConsultarMeta" autosized="true"
                 styleClass="novaModal vw90"
                 shadowOpacity="true" height="550">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Consulta Meta"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelink1"/>
            <rich:componentControl for="modalPanelConsultarMeta"
                                   attachTo="hidelink1" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>
    <a4j:form id="formFiltrosConsultarMeta" ajaxSubmit="true" styleClass="font-size-Em">


        <!-- ---------------------- FILTROS CONSULTA ------------------------------------ -->
        <h:panelGrid columns="1" style="padding: 20px 0px 20px 0px">
            <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_empresa_maiusculo}"/>
            <h:panelGroup layout="block" styleClass="pull-left">

            <h:panelGroup layout="block" styleClass="cb-container texto-cor-cinza">
                <h:selectOneMenu id="empresa" styleClass="form"
                                 rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarMetasFinanceirasTodasEmpresas}"
                                 value="#{MetaFinanceiroBIControle.empresa.codigo}" >
                    <f:selectItems value="#{MetaFinanceiroBIControle.listaSelectItemEmpresa}" />
                </h:selectOneMenu>
            </h:panelGroup>
            <h:outputText rendered="#{!LoginControle.permissaoAcessoMenuVO.visualizarMetasFinanceirasTodasEmpresas}"
                          value="#{MetaFinanceiroBIControle.empresa.nome}"
                          id="empresaNome"
                          styleClass="rotuloCampos"></h:outputText>
            </h:panelGroup>
        </h:panelGrid>
        <h:panelGroup layout="block" styleClass="pull-left colunaEsquerda" style="display: inline-block">
             <h:outputText style="margin-left: 3px;" styleClass="rotuloCampos" value="PER�ODO"/>
            <h:panelGrid columns="10" columnClasses="centralizado">

                <h:panelGroup layout="block"  styleClass="dateTimeCustom" style="display: inline-flex">
                    <h:inputText size="12" readonly="true"
                                 maxlength="15" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="inputTextClean noBorderRight"
                                 id="dataIni"
                                 value="#{MetaFinanceiroBIControle.apresentarPeriodoDe}" />
                    <rich:calendar value="#{MetaFinanceiroBIControle.periodoDe}"
                                   styleClass="inputTextClean"
                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                   showInput="false" zindex="2" showWeeksBar="false">
                        <a4j:support event="onchanged" reRender="dataIni"/>
                    </rich:calendar>
                </h:panelGroup>
                <h:outputText styleClass="rotuloCampos" value=" � "/>

                <h:panelGroup layout="block" styleClass="dateTimeCustom" style="display: inline-flex">
                    <h:inputText size="12" readonly="true"
                                 maxlength="15" onblur="blurinput(this);" onfocus="focusinput(this);"  styleClass="inputTextClean noBorderRight"
                                 id="dataFim"
                                 value="#{MetaFinanceiroBIControle.apresentarPeriodoAte}" />
                    <rich:calendar value="#{MetaFinanceiroBIControle.periodoAte}"
                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                   styleClass="inputTextClean"
                                   showInput="false" zindex="2" showWeeksBar="false">
                        <a4j:support event="onchanged" reRender="dataFim"/>
                    </rich:calendar>
                </h:panelGroup>

            </h:panelGrid>
        </h:panelGroup>
        <h:panelGroup layout="block" styleClass="colunaEsquerda" style="display: inline-block">
            <h:outputText style="margin-left: 3px;" styleClass="rotuloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_descricao_maiusculo}"/>
            <h:panelGrid columns="10" columnClasses="centralizado">
                <!-- DESCRICAO -->
                <h:inputText id="descricao" styleClass="inputTextClean" value="#{MetaFinanceiroBIControle.descricao}" />
                <!-- BOTAO CONSULTA -->
                <a4j:commandLink id="consultar" styleClass="botaoPrimario texto-size-16" value="#{msg_bt.btn_consultar}"
                                 action="#{MetaFinanceiroBIControle.consultarMetas}" title="#{msg.msg_consultar_dados}"
                                 reRender="formFiltrosConsultarMeta"/>
            </h:panelGrid>
        </h:panelGroup>



        <h:panelGrid width="100%" style="text-align: right">
            <h:panelGroup layout="block">
                    <a4j:commandLink id="exportarExcel"
                                   style="margin-left: 8px;"
                                   actionListener="#{ExportadorListaControle.exportar}"
                                   rendered="#{not empty MetaFinanceiroBIControle.metas}"
                                   oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                   accesskey="2" styleClass="botoes">
                    <f:attribute name="lista" value="#{MetaFinanceiroBIControle.metas}"/>
                    <f:attribute name="tipo" value="xls"/>
                    <f:attribute name="atributos" value="empresa_Apresentar=Empresa,mes_Apresentar=M�s,ano=Ano,descricao=Descri��o,valor0=Meta 1,valor1=Meta 2
                                             ,valor2=Meta 3,valor3=Meta 4,valor4=Meta 5"/>
                    <f:attribute name="prefixo" value="MetasFinanceiras"/>
                    <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                </a4j:commandLink>
                <%--BOT�O PDF--%>
                <a4j:commandLink id="exportarPdf"
                                   style="margin-left: 8px;"
                                   actionListener="#{ExportadorListaControle.exportar}"
                                   rendered="#{not empty MetaFinanceiroBIControle.metas}"
                                   oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                   accesskey="2" styleClass="botoes">
                    <f:attribute name="lista" value="#{MetaFinanceiroBIControle.metas}"/>
                    <f:attribute name="tipo" value="pdf"/>
                    <f:attribute name="atributos" value="empresa_Apresentar=Empresa,mes_Apresentar=M�s,ano=Ano,descricao=Descri��o,valor0=Meta 1,valor1=Meta 2
                                 ,valor2=Meta 3,valor3=Meta 4,valor4=Meta 5"/>
                    <f:attribute name="prefixo" value="MetasFinanceiras"/>
                    <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGrid>
        <rich:spacer height="10"/>

        <!-- RESULTADO DA CONSULTA -->
        <rich:dataTable id="listaOutros" width="100%"
                        styleClass="tabelaSimplesCustom"
                        columnClasses="colunaEsquerda, colunaEsquerda, colunaEsquerda, colunaCentralizada, colunaCentralizada, colunaCentralizada, colunaCentralizada, colunaCentralizada,colunaCentralizada"
                        value="#{MetaFinanceiroBIControle.metas}" var="meta"
                        rendered="#{not empty MetaFinanceiroBIControle.metas}"
                        rows="6">
            <!-- EMPRESA -->
            <h:column>
                <f:facet name="header">
                    <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_empresa_maiusculo}" />
                </f:facet>
                <h:outputText value="#{meta.empresa.nome}" styleClass="texto-font texto-size-16 texto-cor-cinza" />
            </h:column>

            <!-- MES -->
            <h:column headerClass="colunaEsquerda">
                <f:facet name="header">
                    <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_mes_maiusculo}" />
                </f:facet>
                <h:outputText value="#{meta.mes.descricao}/#{meta.ano}" styleClass="texto-font texto-size-16 texto-cor-cinza" />
            </h:column>

            <!-- DESCRICAO -->
            <h:column headerClass="colunaEsquerda">
                <f:facet name="header">
                    <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_descricao_maiusculo}" />
                </f:facet>
                <h:outputText value="#{meta.descricao}" styleClass="texto-font texto-size-16 texto-cor-cinza"/>
            </h:column>

            <!-- META 1 -->
            <rich:column style="background-color: #{meta.valores[0].cor}; ">
                <f:facet name="header">
                    <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_meta1_maiusculo}" />
                </f:facet>
                <h:outputText value="#{meta.valores[0].valor}" styleClass="texto-font texto-size-16 #{empty meta.valores[0].cor ? '' : 'texto-cor-branco'}">
                    <f:converter converterId="FormatadorNumerico" />
                </h:outputText>
            </rich:column>

            <!-- META 2 -->
            <rich:column style="background-color: #{meta.valores[1].cor}; ">
                <f:facet name="header">
                    <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_meta2_maiusculo}"/>
                </f:facet>
                <h:outputText value="#{meta.valores[1].valor}" styleClass="texto-font texto-size-16 #{empty meta.valores[1].cor ? '' : 'texto-cor-branco'}">
                    <f:converter converterId="FormatadorNumerico" />
                </h:outputText>
            </rich:column>

            <!-- META 3 -->
            <rich:column style="background-color: #{meta.valores[2].cor}; ">
                <f:facet name="header">
                    <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_meta3_maiusculo}"/>
                </f:facet>
                <h:outputText value="#{meta.valores[2].valor}" styleClass="texto-font texto-size-16 #{empty meta.valores[2].cor ? '' : 'texto-cor-branco'}">
                    <f:converter converterId="FormatadorNumerico" />
                </h:outputText>
            </rich:column>

            <!-- META 4 -->
            <rich:column style="background-color: #{meta.valores[3].cor}; ">
                <f:facet name="header">
                    <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_meta4_maiusculo}"/>
                </f:facet>
                <h:outputText value="#{meta.valores[3].valor}" styleClass="texto-font texto-size-16 #{empty meta.valores[3].cor ? '' : 'texto-cor-branco'}">
                    <f:converter converterId="FormatadorNumerico" />
                </h:outputText>
            </rich:column>

            <!-- META 5 -->
            <rich:column style="background-color: #{meta.valores[4].cor}; ">
                <f:facet name="header">
                    <h:outputText styleClass="rotuloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_meta5_maiusculo}"/>
                </f:facet>
                <h:outputText value="#{meta.valores[4].valor}" styleClass="texto-font texto-size-16 #{empty meta.valores[4].cor ? '' : 'texto-cor-branco'}">
                    <f:converter converterId="FormatadorNumerico" />
                </h:outputText>
            </rich:column>

            <!-- OPCOES -->
            <h:column>
                <a4j:commandLink styleClass="linkPadrao texto-size-16 tooltipster" title="Selecionar"
                                   oncomplete="Richfaces.hideModalPanel('modalPanelConsultarMeta');"
                                   action="#{MetaFinanceiroBIControle.selecionarMeta}"
                                   reRender="containerMetasFinan">
                    Selecionar <i class="fa-icon-arrow-right"></i>
                </a4j:commandLink>
            </h:column>
        </rich:dataTable>
        <rich:datascroller for="listaOutros" id="scrollMetas"  styleClass="scrollPureCustom" renderIfSinglePage="false"/>
    </a4j:form>
</rich:modalPanel>


