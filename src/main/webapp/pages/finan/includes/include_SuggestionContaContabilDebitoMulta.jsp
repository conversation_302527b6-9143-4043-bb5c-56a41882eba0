<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%-- CONTA DEBITO --%>
<h:panelGroup id="labelDebitoMulta">
    <h:outputText rendered="#{MovContaControle.integracaoContabilAlterData && MovContaControle.informarMulta}"
                  value="Conta cont�bil devedora multa:"
                  style="vertical-align: middle"
                  styleClass="tituloCampos">
    </h:outputText>
</h:panelGroup>


<h:panelGroup id="sugestionDebitoMulta">
    <h:inputText  id="nomeDebitoMulta"
                  size="50"
                  style="vertical-align: middle"
                  maxlength="50"
                  onblur="blurinput(this);"
                  rendered="#{MovContaControle.integracaoContabilAlterData && MovContaControle.informarMulta}"
                  onfocus="focusinput(this);"
                  styleClass="form"
                  value="#{MovContaControle.movContaContabilVO.contaContabilDebitoMulta.descricao}" >
        <a4j:support event="onchange" action="#{MovContaControle.setarContaDebitoMultaVazio}" reRender="form"/>
    </h:inputText>

    <rich:suggestionbox   height="200" width="400"
                          rendered="#{MovContaControle.integracaoContabilAlterData && MovContaControle.informarMulta}"
                          status="true"
                          for="nomeDebitoMulta"
                          fetchValue="#{resultCDevedoraMulta.descricao}"
                          nothingLabel="Nenhum registro  encontrado!"
                          style="vertical-align: middle"
                          suggestionAction="#{MovContaControle.executarAutocompleteConsultarContaContabil}"
                          minChars="1"
                          rowClasses="linhaImpar, linhaPar"
                          var="resultCDevedoraMulta"
                          id="suggestionDebitoMulta">
        <a4j:support event="onselect"
                     reRender="form"
                     ignoreDupResponses="true"
                     action="#{MovContaControle.selecionarContaDevedoraMultaSuggestionBox}">
        </a4j:support>
        <h:column>
            <f:facet name="header">
                <h:outputText value="Descri��o"  styleClass="textverysmall"/>
            </f:facet>
            <h:outputText styleClass="textverysmall" value="#{resultCDevedoraMulta.descricao}" />
        </h:column>
    </rich:suggestionbox>
</h:panelGroup>