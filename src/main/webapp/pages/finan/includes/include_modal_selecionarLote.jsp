<%@include file="include_imports.jsp" %>
<link href="../../../css/financeiro.css" rel="stylesheet" type="text/css">

 <a4j:outputPanel>
        <rich:modalPanel id="modalPanelSelecionarCheques" autosized="true" 
        				minWidth="400" height="190" top="100"  shadowOpacity="true">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Selecionar Lote"></h:outputText>
                </h:panelGroup>
            </f:facet>
                <f:facet name="controls">
			        <h:panelGroup>
			            <h:graphicImage value="../../imagens/close.png" style="cursor:pointer" id="hidelink4"/>
			            <rich:componentControl for="modalPanelSelecionarCheques" attachTo="hidelink4" operation="hide" event="onclick"/>
			        </h:panelGroup>
			    </f:facet>
            <h:form id="formSelecionarCheques">


				<h:outputText value="Lote: "></h:outputText>
            	<h:inputText id="lote" size="50" maxlength="50" 
                 			onfocus="focusinput(this);" styleClass="form" value="#{MovContaControle.consultaLote}">
   				 </h:inputText>
    
			    <rich:suggestionbox height="200" width="500"
			                        for="lote"
			                        fetchValue="#{result}"
			                        suggestionAction="#{MovContaControle.executarAutocompleteConsultaLote}"
			                        minChars="1" rowClasses="20"
			                        status="statusInComponent"
			                        nothingLabel="Nenhum lote encontrado!"
			                        var="result" id="suggestionLote">
			        <a4j:support event="onselect" 
			                     action="#{MovContaControle.selecionarLoteSuggestionBox}"
			                     reRender="formSelecionarCheques"/>
			
			        <h:column>
			            <h:outputText value="#{result.descricao}"/>
			        </h:column>
			        
			        <h:column>
			            <h:outputText value="#{result.dataLancamento_Apresentar}"/>
			        </h:column>
			        
			        <h:column>
			            <h:outputText value="#{result.valor_Apresentar}"/>
			        </h:column>
			    
			    </rich:suggestionbox>
            	
                       
            
               
                 <script>
	                var modal = document.getElementById('modalPanelSelecionarChequesCDiv');
	                var tamanho = modal.offsetWidth;
	                var altura = modal.offsetHeight;
	                if(altura > 600){
			            modal.style.top = (screen.availHeight - altura) / 4;
	                }
	                modal.style.left = (screen.availWidth - tamanho) / 2;
	            </script>
            </h:form>
        </rich:modalPanel>
    </a4j:outputPanel>
    