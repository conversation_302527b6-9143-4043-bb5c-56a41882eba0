<%@include file="imports.jsp" %>

        <style>
            .borderTr th{
                border-bottom: #E5E5E5 1px solid; 
            }
            .tabelaDados{
                width: calc(100% - 20px);
                margin: 10px;
                border: none!important;
            }
        </style>
        <rich:modalPanel id="modalHistoricoCheque" autosized="true" width="800" height="220"  shadowOpacity="true" styleClass="novaModal" >
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="#{msg_aplic.prt_HistoricoCheque} - #{ChequeControle.chequeTO.numero}"></h:outputText>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:outputText
                            styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                            id="hidelinkHistoricoCheque"/>
                    <rich:componentControl for="modalHistoricoCheque" attachTo="hidelinkHistoricoCheque"
                                           operation="hide" event="onclick">
                    </rich:componentControl>
                </h:panelGroup>
            </f:facet>
            <h:form id="formHistoricoCheque">
            	<h:outputText value="#{msg_aplic.prt_Cheque_data}: #{ChequeControle.chequeTO.dataCompensacaoApresentar}"
                              styleClass="label cinza"
                              style="margin-left: 15px;"></h:outputText>
                
                <h:outputText value=" - Data original: #{ChequeControle.chequeTO.dataOriginalApresentar}" styleClass="label cinza"
                              rendered="#{ChequeControle.chequeTO.dataOriginal != null}"></h:outputText>
            	<br/><br/>
                <h:outputText value="N�o existem movimenta��es para este cheque!"
                              rendered="#{fn:length(ChequeControle.historicoCheque) == 0}"
                              style="font-weight: bold;font-size: larger;"/>
            	<h:dataTable var="historico" value="#{ChequeControle.historicoCheque}"
            		     styleClass="tabelaDados semZebra borderTr" rendered="#{fn:length(ChequeControle.historicoCheque) > 0}">
            		<h:column>
            			<f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataInicio}" />
                        </f:facet>
            			<h:outputText value="#{historico.dataInicioApresentar}"></h:outputText>
            		</h:column>
            		<h:column>
            			<f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataFim}" />
                        </f:facet>
            			<h:outputText value="#{historico.dataFimApresentar}"></h:outputText>
            		</h:column>
            		<h:column>
            			<f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_status}" />
                        </f:facet>
            			<h:outputText value="#{historico.status.descricao}"></h:outputText>
            		</h:column>
            		<h:column>
            			<f:facet name="header">
                            <h:outputText style="padding-left: 10px; padding-right: 10px" value="#{msg_aplic.prt_Finan_GestaoRecebiveis_Lote}" />
                        </f:facet>
            			<h:outputText style="padding-left: 10px; padding-right: 10px"  value="#{historico.lote.codigo}"></h:outputText>
            		</h:column>
            		<h:column>
            		    <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_CustodiaDe}" />
                        </f:facet>
            			<h:outputText value="#{historico.custodiaDe}"></h:outputText>
            		</h:column>
            		<h:column>
            			<a4j:commandLink value="Lan�amento" 
                                                 title="Ver o lan�amento dessa opera��o"
                                                 styleClass="tooltipster texto-cor-azul linkPadrao inline" 
                                                 action="#{ChequeControle.visualizarOperacao}"
                                                 rendered="#{historico.movConta.codigo > 0}"
            					 oncomplete="abrirPopup('includes/include_LancamentosDF.jsp', 'TelaLan�amento', 1200, 700);"></a4j:commandLink>
            		</h:column>
            	</h:dataTable>
            </h:form>
       </rich:modalPanel>
            
            
            
            