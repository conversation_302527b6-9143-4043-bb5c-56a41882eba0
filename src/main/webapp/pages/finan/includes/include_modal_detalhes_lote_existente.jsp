<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="imports.jsp" %>
<a4j:outputPanel>
    <rich:modalPanel id="modalLotePagamentoExistente" autosized="true" styleClass="novaModal"
                     minWidth="1300" width="1300" minHeight="580" height="580" shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText
                        value="Detalhes do Lote: #{MovContaControle.infoAdicionalHeaderModalExibirLancLote}"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hideLotePagamentoExistente"/>
                <rich:componentControl for="modalLotePagamentoExistente" attachTo="hideLotePagamentoExistente"
                                       operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:form id="formLotePagamentoExistente" style="display: grid; grid-template-rows: 0fr 0fr 0fr;">

            <rich:dataTable id="listaItemsLote" width="100%" headerClass="consulta" rowClasses="linhaPar"
                            style="margin-top: 10px;"
                            reRender="paginaAtual, paginaAtualTop,painelPaginacaoTop,painelPaginacao"
                            columnClasses="centralizado, centralizado, centralizado, centralizado, direita, centralizado"
                            value="#{MovContaControle.listaLancamentosLoteExistentePaginada}" rows="10" var="item">

                <!-- codigo -->
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Cód. Item"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText
                                value="#{item.codigo}"
                                title="Uid do Item: #{item.uid}"
                                styleClass="tooltipster"/>
                    </h:panelGroup>
                </rich:column>

                <!-- Cód. Conta -->
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Cód. Conta"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText
                                value="#{item.movcontaVO.codigo}"
                                title="Código da Conta a Pagar que relaciona esse item do lote."
                                styleClass="tooltipster"/>
                    </h:panelGroup>
                </rich:column>

                <!-- descricao -->
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Descrição"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{item.movcontaVO.descricao}"/>
                    </h:panelGroup>
                </rich:column>

                <!-- favorecido -->
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Favorecido"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{item.movcontaVO.pessoaVO.nome}"/>
                    </h:panelGroup>
                </rich:column>

                <!-- vencimento -->
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Vencimento"/>
                    </f:facet>
                    <h:outputText value="#{item.movcontaVO.dataVencimento_Apresentar}">
                    </h:outputText>
                </rich:column>

                <!-- valor -->
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Valor"/>
                    </f:facet>
                    <h:outputText value="#{item.movcontaVO.valor_Apresentar}"
                                  styleClass="#{item.movcontaVO.mudarCorLinkValor}">
                    </h:outputText>
                </rich:column>

                <!-- status -->
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Status"/>
                    </f:facet>
                    <h:panelGroup id="statusItemLote" style="text-align-last: center;; display: block;">
                        <a4j:commandLink style="#{item.styleCssStatus}; border-radius: 20px!important; cursor: default; display: block"
                                         value="#{item.status_Apresentar}"
                                         title="#{item.motivoRejeicao_Apresentar}"
                                         styleClass="botaoPrimario texto-size-10-real tooltipster"/>
                    </h:panelGroup>
                </rich:column>

                <!-- status registro -->
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Status Registro"/>
                    </f:facet>
                    <h:outputText value="#{item.registration_Status_Apresentar}">
                    </h:outputText>
                </rich:column>

                <!-- últ. atualização -->
                <rich:column>
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno"
                                      value="Dt. Últ. Atualização"/>
                    </f:facet>
                    <h:outputText
                            value="#{item.updated_at_Apresentar}"
                            title="#{item.titleUltAtualizacaoPagamentoExplicacao}"
                            styleClass="tooltipster">
                    </h:outputText>
                </rich:column>

                <!-- QRCode -->
                <rich:column rendered="#{MovContaControle.exibirColunaQRCodeModalDetalhesLoteExistente}"
                             style="text-align: justify !important; white-space: break-spaces; word-break: break-all; width: 40%;">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="QRCode"/>
                    </f:facet>
                    <h:panelGroup id="panelQrCodeItem">
                        <h:outputText value="#{item.qrcode}">
                        </h:outputText>
                    </h:panelGroup>
                </rich:column>

                <!-- Dados bancários -->
                <rich:column rendered="#{MovContaControle.exibirColunaDadosBancariosModalDetalhesLoteExistente}"
                             style="text-align: justify !important; white-space: break-spaces; word-break: break-all; width: 40%;">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Dados Bancários"/>
                    </f:facet>
                    <h:panelGroup id="panelDadosBancariosItem">
                        <h:outputText value="#{item.movcontaVO.contaBancariaFornecedorVO.dadosBancariosApresentar}">
                        </h:outputText>
                    </h:panelGroup>
                </rich:column>

                <!-- Cód. Barras -->
                <rich:column rendered="#{MovContaControle.exibirColunaCodBarrasModalDetalhesLoteExistente}">
                    <f:facet name="header">
                        <h:outputText styleClass="topoPequeno" value="Cód. Barras"/>
                    </f:facet>
                    <h:outputText value="#{item.codigobarras}">
                    </h:outputText>
                </rich:column>

                <%--AÇÕES--%>
                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Ações"/>
                    </f:facet>
                    <a4j:commandLink id="copiarUidDoLote"
                                     rendered="#{item.uid != '' && item.uid != null}"
                                     onclick="copyToClipboard('#{item.uid}')"
                                     title="#{item.uidPagamentoCopiar}"
                                     style="text-decoration: none;"
                                     styleClass="linkAzul texto-size-14 tooltipster"
                                     reRender="mdlMensagemGenerica">
                        <i class="fa-icon-file-alt" style="font-size: 17px; margin-top: 2px; margin-left: 5px"></i>
                    </a4j:commandLink>
                </rich:column>


            </rich:dataTable>

            <%--PAGINAÇÃO--%>
            <h:panelGrid columns="1"
                         style="margin-top: 12px;"
                         width="100%"
                         columnClasses="colunaCentralizada">
                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td align="center" valign="middle">
                            <h:panelGroup layout="block"
                                          styleClass="paginador-container">
                                <h:panelGroup styleClass="pull-left"
                                              layout="block">
                                    <h:outputText
                                            styleClass="texto-size-14 cinza"
                                            value="Total #{MovContaControle.labelTotalItensDetalheLote} itens"></h:outputText>
                                </h:panelGroup>
                                <h:panelGroup layout="block"
                                              style="align-items: center;position: absolute;margin-left: 34%;">
                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                     reRender="formLotePagamentoExistente"
                                                     action="#{MovContaControle.primeiraPaginaDetalheLote}">
                                        <i class="fa-icon-double-angle-left"></i>
                                    </a4j:commandLink>

                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                     reRender="formLotePagamentoExistente"
                                                     action="#{MovContaControle.paginaAnteriorDetalheLote}">
                                        <i class="fa-icon-angle-left"></i>
                                    </a4j:commandLink>

                                    <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                                  value="Página #{MovContaControle.labelPaginaAtualExibirDetalheLote} / #{MovContaControle.totalPaginasDetalheLote}"
                                                  rendered="true"/>
                                    <a4j:commandLink
                                            styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real"
                                            reRender="formLotePagamentoExistente"
                                            action="#{MovContaControle.proximaPaginaDetalheLote}">
                                        <i class="fa-icon-angle-right"></i>
                                    </a4j:commandLink>

                                    <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                                     reRender="formLotePagamentoExistente"
                                                     action="#{MovContaControle.ultimaPaginaDetalheLote}">
                                        <i class="fa-icon-double-angle-right"></i>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>
                        </td>
                    </tr>
                </table>
            </h:panelGrid>
        </h:form>
    </rich:modalPanel>
</a4j:outputPanel>

<script>
    function copyToClipboard(elcopy) {
        console.log(elcopy);
        // Cria um campo de texto temporário para copiar o valor
        var el = document.createElement('textarea');
        el.value = elcopy;  // Usa o valor diretamente, sem necessidade de querySelector
        document.body.appendChild(el);
        el.select();
        document.execCommand('copy');
        document.body.removeChild(el);

        Notifier.info('Uid: (' + el.value + ') copiado para a área de transferência.');
    }
</script>

<style>
    .botaoConta:hover {
        /*background: white;*/
        background-color: #bdd6ff;
        color: #094771 !important;
        text-decoration: none;
    }

    .svg-color {
        fill: red;
    }
</style>
