<%@include file="imports.jsp" %>
<rich:modalPanel id="panelTransferenciaMesmoBanco" autosized="true" shadowOpacity="true" width="550" height="120"
                 styleClass="novaModal"
                 onshow="document.getElementById('formPanelTransferenciaMesmoBanco:descricaoDeposito').focus();">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText styleClass="texto-upper" value="Transfer�ncia entre contas #{MovContaControle.contaOrigem.nomeBanco_Apresentar}"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formPanelTransferenciaMesmoBanco" ajaxSubmit="true">

        <div style="margin:10px 0 0 0;"></div>

        <rich:spacer width="63"/>
        <h:panelGrid columns="2" width="100%" columnClasses="direita, esquerda" style="font-size: 12px">

            <h:outputText styleClass="tituloCampos" style="font-weight: normal;"
                          value="Data Transfer�ncia:"/>
            <h:panelGroup styleClass="dateTimeCustom"
                          layout="block"
                          style="margin-top: 8px;">
                <rich:calendar id="dataLancamento"
                               value="#{OperacaoContaControle.dataDeposito}"
                               inputSize="10"
                               inputClass="form"
                               oninputblur="blurinput(this);"
                               oninputfocus="focusinput(this);"
                               oninputchange="return validar_Data(this.id);"
                               datePattern="dd/MM/yyyy"
                               enableManualInput="true"
                               zindex="2"
                               buttonIcon="/imagens_flat/calendar-button.svg"
                               showWeeksBar="false"/>

            </h:panelGroup>

            <!-- codigo banco -->
            <h:panelGroup>
                <h:outputText styleClass="tituloCampos" style="font-weight: normal;"
                              value="C�digo banco:"/>
            </h:panelGroup>
            <h:panelGroup>
                <h:inputText id="codBanco" size="50" maxlength="100" onblur="blurinput(this);"
                             onfocus="focusinput(this);"
                             styleClass="form" value="#{MovContaControle.movContaVO.contaDestinoOpenBank.banco.codigoBanco}"/>
            </h:panelGroup>

            <!-- agencia -->
            <h:panelGroup>
                <h:outputText styleClass="tituloCampos" style="font-weight: normal;"
                              value="Agencia:"/>
            </h:panelGroup>
            <h:panelGroup>
                <h:inputText id="agencia" size="50" maxlength="100" onblur="blurinput(this);"
                             onfocus="focusinput(this);"
                             styleClass="form" value="#{MovContaControle.movContaVO.contaDestinoOpenBank.agencia}"/>
            </h:panelGroup>

            <!-- agencia dv -->
            <h:panelGroup>
                <h:outputText styleClass="tituloCampos" style="font-weight: normal;"
                              value="Agencia DV:"/>
            </h:panelGroup>
            <h:panelGroup>
                <h:inputText id="agenciaDv" size="50" maxlength="100" onblur="blurinput(this);"
                             onfocus="focusinput(this);"
                             styleClass="form" value="#{MovContaControle.movContaVO.contaDestinoOpenBank.agenciaDV}"/>
            </h:panelGroup>

            <!-- conta -->
            <h:panelGroup>
                <h:outputText styleClass="tituloCampos" style="font-weight: normal;"
                              value="Conta:"/>
            </h:panelGroup>
            <h:panelGroup>
                <h:inputText id="conta" size="50" maxlength="100" onblur="blurinput(this);"
                             onfocus="focusinput(this);"
                             styleClass="form" value="#{MovContaControle.movContaVO.contaDestinoOpenBank.numero}"/>
            </h:panelGroup>

            <!-- conta dv -->
            <h:panelGroup>
                <h:outputText styleClass="tituloCampos" style="font-weight: normal;"
                              value="Conta DV:"/>
            </h:panelGroup>
            <h:panelGroup>
                <h:inputText id="contaDv" size="50" maxlength="100" onblur="blurinput(this);"
                             onfocus="focusinput(this);"
                             styleClass="form" value="#{MovContaControle.movContaVO.contaDestinoOpenBank.numeroDV}"/>
            </h:panelGroup>

            <!-- valor -->
            <h:panelGroup>
                <h:outputText styleClass="tituloCampos" style="font-weight: normal;"
                              value="#{msg_aplic.prt_Finan_Lancamento_valor}"/>
            </h:panelGroup>
            <h:panelGroup>
                <h:inputText id="valor" style="width: 90px;" maxlength="40" onfocus="focusinput(this);"
                             styleClass="form" value="#{MovContaControle.movContaVO.valor}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:inputText>
            </h:panelGroup>

            <h:outputText
                    styleClass="tituloCampos" style="font-weight: normal;" value="Conta:"/>
            <h:panelGroup>
                <h:panelGroup layout="block" styleClass="cb-container"
                              style="margin-bottom: 5px;margin-top: 5px;">
                    <h:selectOneMenu value="#{MovContaControle.movContaVO.contaDestinoOpenBank.banco.codigo}"
                                     id="contaSelectitem"
                                     styleClass="form">
                        <f:selectItems value="#{MovContaControle.listaComboBanco}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGroup>

        </h:panelGrid>
        <h:panelGrid columns="1" width="100%" columnClasses="centralizado">
            <h:panelGroup>
                <a4j:commandLink action="#{OperacaoContaControle.gravarOperacaoContaOpenBankMesmoBanco}"
                                 reRender="formPanelTransferenciaMesmoBanco"
                                 id="btnConfirmaOperacao"
                                 oncomplete="#{OperacaoContaControle.onCompleteModalOperacao}"
                                 styleClass="pure-button pure-button-primary">
                    OK
                </a4j:commandLink>
                <rich:spacer width="10"/>
                <a4j:commandLink reRender="panelMensagemOp, formPanelTransferenciaMesmoBanco"
                                 styleClass="pure-button"
                                 oncomplete="Richfaces.hideModalPanel('panelTransferenciaMesmoBanco')">
                    Fechar
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGrid>
        <h:panelGrid id="panelMensagemOp" columns="1" width="100%">
            <h:panelGrid columns="2" width="100%">
                <h:panelGrid columns="1" width="100%">

                    <h:outputText value=" "/>

                </h:panelGrid>
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem" value="#{OperacaoContaControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada" value="#{OperacaoContaControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>