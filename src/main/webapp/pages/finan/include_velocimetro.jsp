<%@page pageEncoding="ISO-8859-1"%>
<rich:panel style="height: 350px;"
            id="painelVeloc" headerClass="headerSanfona">
                                                   <f:facet name="header">
                                                       <h:panelGrid columns="2" width="100%" columnClasses="esquerda, direita" cellpadding="0" cellspacing="0">

                                                           <h:panelGroup>
                                                               <h:outputLink styleClass="linkWiki"
                                                                              value="#{SuperControle.urlBaseConhecimento}bi-velocimetro-meta-geral-financeiro/"
                                                                              title="Clique e saiba mais: Velocímetro"
                                                                              target="_blank">
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                               <h:outputText value="Velocímetro - Meta geral" styleClass="text" style="font-weight:bold;color:#0f4c6b;font-size:10pt;"/>
                                                           </h:panelGroup>

                                                         <h:panelGroup>
                                                             <h:selectOneMenu value="#{BIFinanceiroControle.velocimetro.mes}" styleClass="form">
                                                                 <f:selectItems value="#{BIFinanceiroControle.meses}"/>
                                                             </h:selectOneMenu>
                                                                    <a4j:commandButton id="btnAtualizarVelocimentor" reRender="painelVeloc"
                                                                                       title="Atualizar Resumo de Receitas"
                                                                                       style="vertical-align:middle; width:16px;height:16px;"
                                                                                       status="statusInComponent"
                                                                                       action="#{BIFinanceiroControle.configurarVelocimetro}"
                                                                                       image="/images/update.png"/>
                                                          </h:panelGroup>
                                                       </h:panelGrid>
                                                   </f:facet>

                                                    <%@include file="include_bi_velocimetro.jsp" %>


                                            </rich:panel>