<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>


<!-- inicio box -->
<h:panelGroup layout="block" styleClass="menuLateral">
    <!-- Menu Lateral -->
    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-list"></i> Rel. de clientes
        </h:panelGroup>


        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink value="#{CElabels['menu.consulta.relatorios.clientesAniversariantes']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="ANIVERSARIANTES_CE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-gerar-uma-lista-de-aniversariantes/"
                          title="Clique e saiba mais: Clientes Aniversariantes"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-list"></i> Rel. Financeiros
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink value="#{CElabels['menu.consulta.relatorios.receitaPeriodo']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="RECEITA_PERIODO_CE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-verificar-a-receita-por-periodo-da-empresa/"
                          title="Clique e saiba mais: Receita por Per�odo"
                          target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink value="#{CElabels['menu.consulta.relatorios.fechamentoCaixa']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="FECHAMENTO_CAIXA_CE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-verificar-o-relatorio-de-fechamento-de-caixa-por-operador/"
                          title="Clique e saiba mais: Fechamento de Caixa Por Operador"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink value="#{CElabels['menu.consulta.relatorios.parcelasAberto']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="RECEITA_PERIODO_CE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}o-sistema-possui-alguma-ferramenta-que-permita-visualizar-as-inadimplencias-da-minha-empresa/"
                          title="Clique e saiba mais: Parcelas Aberto"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">
            <a4j:commandLink value="Saldo CC Cliente"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="SALDO_CONTA_CORRENT" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-tirar-um-relatorio-dos-alunos-que-possuem-saldo-na-conta-corrente/"
                          title="Clique e saiba mais: Saldo de Conta Corrente Cliente"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

    </h:panelGroup>
</h:panelGroup>