<%@include file="../includes/include_imports.jsp" %>

<f:view>
	<jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
	<html>
       	<!-- Inclui o elemento HEAD da p�gina -->
        <%@include file="../includes/include_head_form.jsp" %>
		<link href="${contexto}/beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
		
   		
   		<script type="text/javascript">
			function validar() {
				limparMensagem(['descricao', 'valor', 'estoque', 'minEstoque', 'tipo']);
	
				var descricao = document.getElementById('form:descricao');
				var valor = document.getElementById('form:valor');
				var estoque = document.getElementsByName('form:estoque')[0].value;
				var tipo = document.getElementById('form:tipo');
				var validade = true;
	
				if (descricao == null || descricao.value == null || descricao.value == "") {
					exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.descricao\']}"/>', 'descricao');
					validade = false;
				}
				if (valor == null || valor.value == null || valor.value == "") {
					exibirMensagem(montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.valor\']}"/>'), 'valor');
					validade = false;
				}
				if (tipo == 0 || tipo.value == 0) {
					exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.tipo\']}"/>', 'tipo');
					validade = false;
				}
				if (estoque == 0 || estoque.value == 0) {
					exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.estoque\']}"/>', 'estoque');
					validade = false;
				}
				
				return validade;
			}

			function validarPatrimonio() {
				limparMensagem('codigoPatrimonio');
   				limparMensagem('descricaoPatrimonio');

   				var codigoPatrimonio = document.getElementById('form:codigoPatrimonio');
   				var descricaoPatrimonio = document.getElementById('form:descricaoPatrimonioTextArea_ifr').contentWindow.document.body.firstChild.innerHTML;
   				

   				var validade = true;

   				if (codigoPatrimonio == null || codigoPatrimonio.value == null || codigoPatrimonio.value == "") {
					exibirMensagem(montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.codigo\']}"/>'), 'codigoPatrimonio');
					validade = false;
				}
   				if (descricaoPatrimonio == null || descricaoPatrimonio == '<br mce_bogus="1">') {
					exibirMensagem(montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.descricao\']}"/>'), 'descricaoPatrimonio');
					validade = false;
				}

				return validade;
   			}

			function hideOrShow(show) {
				 
				 var obj = document.getElementById("form:panelPatrimonio"); // Get the panel using its ID
				 var obj1 = document.getElementById("form:panelRastreamento");
				 if (show) {
					  obj.style.display = "block";
					  obj1.style.display = "block";  
				} 
				 else { 
					 obj.style.display = "none";
					 obj1.style.display = "none";  
				}
			}
		</script>
   		
   		<title>
       		<h:outputText value="#{CElabels['menu.cadastros.produtos.produtosLocacao']}"/>
   		</title>

		<c:set var="titulo" scope="session" value="Produtos"/>
		<c:set var="urlWiki" scope="session" value="${SuperControle.urlWikiCE}Cadastros:PP:Produto"/>
		<f:facet name="header">

				<jsp:include page="../../../topoReduzido_material.jsp"/>
		</f:facet>
		<h:form id="form">
			    
	    	<a4j:keepAlive beanName="ProdutoLocacaoControle" />
			<h:panelGrid columns="1" width="100%" >
                <h:panelGrid columns="1" style="height:25px; background-image:url('../../../imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                     <h:outputText styleClass="tituloFormulario"
                                      value="#{CElabels['menu.cadastros.produtoLocacao']}">
                            <h:outputLink value="#{SuperControle.urlWikiCE}Cadastros:PP:Produto"
                                          title="Clique e saiba mais: Produto" target="_blank">
                                <h:graphicImage styleClass="linkWiki" url="/imagens/wiki_bco.gif"/>
                            </h:outputLink>
                        </h:outputText>
                </h:panelGrid>
            </h:panelGrid>
		                
			<!-- *********** PRODUTO DE LOCACAO ************** -->		                
			<h:panelGrid id="panelProdutoLocacao" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
				<h:panelGrid rowClasses="linhaImpar, linhaPar"
					columnClasses="classEsquerda, classDireita"
					style="background-image:url('./imagens/backGroundTelaBasico.png'); background-repeat: no-repeat;padding: 8px;"
					width="100%" columns="2">
					<h:panelGroup>
						<%@include file="../includes/include_obrigatorio.jsp" %>
						<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.produto']}:" />
					</h:panelGroup>
					<h:panelGroup>
						<h:inputText onblur="blurinput(this);" onfocus="focusinput(this);"
							styleClass="form" size="30" maxlength="25"
							value="#{ProdutoLocacaoControle.produto.descricao}" id="descricao"></h:inputText>
						<div id="divObg-descricao" class="mensagemObrigatorio"></div>
					</h:panelGroup>
					<h:panelGroup>
						<%@include file="../includes/include_obrigatorio.jsp" %>
						<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.valor']}:" />
					</h:panelGroup>
					<h:panelGroup>
						<h:inputText id="valor" onblur="blurinput(this); limparMsgObrig('form:valor', 'valor');"
								onfocus="focusinput(this);"	onkeypress="return(currencyFormat(this,'.',',',event));"
								styleClass="form" maxlength="14" value="#{ProdutoLocacaoControle.produto.valorFormatado}" />
						<div id="divObg-valor" class="mensagemObrigatorio"></div>
					</h:panelGroup>
					<h:panelGroup>
						<%@include file="../includes/include_obrigatorio.jsp" %>
						<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.produto.estoque']}:" />
					</h:panelGroup>
					<h:panelGroup>
					<rich:inputNumberSpinner value="#{ProdutoLocacaoControle.produto.estoque}"
											cycled="false" minValue="0" step="50" maxValue="99999"
											id="estoque" />
						<div id="divObg-estoque" class="mensagemObrigatorio"></div>
					</h:panelGroup>
					<h:panelGroup>
						<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.produtoLocacao.minimoEstoque']}:" />
					</h:panelGroup>
					<h:panelGroup>
					<rich:inputNumberSpinner value="#{ProdutoLocacaoControle.produto.minimoEstoque}"
											cycled="false" minValue="0" step="50" maxValue="99999"
											id="minEstoque" />
						<div id="divObg-minEstoque" class="mensagemObrigatorio"></div>
					</h:panelGroup>
					<h:panelGroup>
						<%@include file="../includes/include_obrigatorio.jsp" %>
						<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.tipo']}:" />
					</h:panelGroup>
					<h:panelGroup>
                                            <h:selectOneMenu styleClass="form" onblur="blurinput(this);" onfocus="focusinput(this);" id="tipo" value="#{ProdutoLocacaoControle.codigoTipo}">
                                                <f:selectItem itemValue="0" itemLabel="--Selecione--"/>
                                                <f:selectItems value="#{ProdutoLocacaoControle.listaTipos}" />
                                                <a4j:support event="onchange" reRender="form" action="#{ProdutoLocacaoControle.mudarTipo}"/>
                                            </h:selectOneMenu>
						<div id="divObg-tipo" class="mensagemObrigatorio"></div>
					</h:panelGroup>
                                        <h:panelGroup rendered="#{ProdutoLocacaoControle.brinquedo}">
						<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.produtoLocacao.rastreado']}:" />
					</h:panelGroup>
					<h:panelGroup rendered="#{ProdutoLocacaoControle.brinquedo}">
						<h:selectBooleanCheckbox
							value="#{ProdutoLocacaoControle.produto.rastreado}" id="questionario" tabindex="11">
							<a4j:support event="onclick" reRender="tablePatrimonioRastreamento"/>
							</h:selectBooleanCheckbox>
						
						<div id="divObg-questionario" class="mensagemObrigatorio"></div>
					</h:panelGroup>
				</h:panelGrid>
			</h:panelGrid>
			
			<br />
			
			<h:panelGrid id="tablePatrimonioRastreamento"  columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
			
				<!-- *********** PAINEL DE PATRIM�NIO ************** -->
				<rich:simpleTogglePanel id="panelPatrimonio" rendered="#{ProdutoLocacaoControle.produto.rastreado}" switchType="client" opened="#{!ProdutoLocacaoControle.visualizarRastreamento}"
						onexpand="return false;" oncollapse="return false;">
					<f:facet name="header">
						<h:outputText value="#{CElabels['entidade.produtoLocacaoPatrimonio']}" />
					</f:facet>
					<h:panelGrid rowClasses="linhaImpar, linhaPar"
							columnClasses="classEsquerda, classDireita"
							style="background-image:url('./imagens/backGroundTelaBasico.png'); background-repeat: no-repeat;padding: 8px;"
							width="100%" columns="2">
						<h:panelGroup>
							<%@include file="../includes/include_obrigatorio.jsp" %>
							<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.codigo']}:" />
						</h:panelGroup>
						
						<h:panelGroup>
							<h:inputText onblur="blurinput(this); limparMsgObrig('form:codigoPatrimonio', 'codigoPatrimonio');"
									styleClass="form" size="30" maxlength="30"
									style="border: 1px solid #8eb3c3" onfocus="focusinput(this);"
									value="#{ProdutoLocacaoControle.patrimonio.codigo}"
									id="codigoPatrimonio"/>
							<div id="divObg-codigoPatrimonio" class="mensagemObrigatorio"></div>
						</h:panelGroup>
						
						<h:panelGroup>
							<%@include file="../includes/include_obrigatorio.jsp" %>
							<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.descricao']}:" />
						</h:panelGroup>
						
						<h:panelGroup>
							<rich:editor id="descricaoPatrimonio" useSeamText="false" viewMode="visual"
									width="400" height="200" value="#{ProdutoLocacaoControle.patrimonio.descricao}"
									/>
							<div id="divObg-descricaoPatrimonio" class="mensagemObrigatorio"></div>
						</h:panelGroup>
					</h:panelGrid>
					<center>
						<a4j:commandButton rendered="#{!ProdutoLocacaoControle.edicaoPatrimonio}" action="#{ProdutoLocacaoControle.adicionarPatrimonio}" reRender="tablePatrimonioRastreamento,mensagens, panelRastreamento"
								value="#{CElabels['operacoes.adicionar']}" image= "/imagens/botoesCE/incluir.png" styleClass="botoes"
								onclick="hideToolTip(); if (!validarPatrimonio()) {return false;}" 
								onmouseover="toolTip('Adicionar Patrim�nio' , 120 , 'gray')"
										onmouseout="hideToolTip();"/>
					 	<a4j:commandButton rendered="#{ProdutoLocacaoControle.edicaoPatrimonio}" action="#{ProdutoLocacaoControle.confirmarAlteracaoPatrimonio}" reRender="tablePatrimonioRastreamento,mensagens, panelRastreamento"
								value="#{CElabels['operacoes.editar.confirmar']}" image= "/imagens/bt_editar.png" styleClass="botoes"
								onclick="hideToolTip(); if (!validarPatrimonio()) {return false;};" 
								onmouseover="toolTip('Confirmar Edi��o' , 120 , 'gray')"
										onmouseout="hideToolTip();"/>
					</center>
					
					<h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
						<h:dataTable id="tablePatrimonio" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
		                                       rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
		                                       value="#{ProdutoLocacaoControle.produto.patrimonios}" var="patrimonio">
							<h:column>
								<f:facet name="header">
									<h:outputText value="#{CElabels['entidade.codigo']}" />
								</f:facet>
								<h:outputText value="#{patrimonio.codigo}" />
							</h:column>
							<h:column>
								<f:facet name="header">
									<h:outputText  value="#{CElabels['entidade.descricao']}" />
								</f:facet>
								<h:outputText escape="false" value="#{patrimonio.descricao}" />
							</h:column>
							<h:column>
								<f:facet name="header">
									<h:outputText value="#{CElabels['operacoes.opcoes']}" />
								</f:facet>
								<h:panelGroup>
									<a4j:commandButton id="editarPatrimonio" reRender="form" ajaxSingle="true" immediate="true"
										action="#{ProdutoLocacaoControle.editarPatrimonio}" value="#{CElabels['operacoes.editar']}"
										image="/imagens/bt_editar.png" styleClass="botoes"
										onmouseover="toolTip('Editar Patrimonio' , 120 , 'gray')"
										onmouseout="hideToolTip();"
										onclick="hideToolTip();"/>&nbsp;&nbsp;
									<a4j:commandButton id="removerPatrimonio" reRender="form" ajaxSingle="true" immediate="true"
										action="#{ProdutoLocacaoControle.removerPatrimonio}" value="#{CElabels['operacoes.excluir']}"
										image="/imagens/bt_excluir.png" styleClass="botoes"
										onmouseover="toolTip('Remover Patrimonio' , 120 , 'gray')"
										onmouseout="hideToolTip();"
										onclick="hideToolTip();"/>&nbsp;&nbsp;
									<a4j:commandButton value="Exibir Rastreamento" id="visualizarRastreamento" reRender="tablePatrimonioRastreamento" ajaxSingle="true" immediate="true"
										action="#{ProdutoLocacaoControle.visualizarRastreamento}"
										onmouseover="toolTip('Exibir Rastreamento' , 120 , 'gray')"
										onmouseout="hideToolTip();"
										onclick="hideToolTip();"
										image="/imagens/botoesCE/Exibir_Rastreamento.png">
	         						</a4j:commandButton>	
									
									
								</h:panelGroup>
							</h:column>
						</h:dataTable>
					</h:panelGrid>
				</rich:simpleTogglePanel>
				
				<!-- *********** PAINEL DE RASTREAMENTO ************** -->
				<rich:simpleTogglePanel id="panelRastreamento" rendered="#{ProdutoLocacaoControle.produto.rastreado}" switchType="client" opened="#{ProdutoLocacaoControle.visualizarRastreamento}"
						onexpand="return false;" oncollapse="return false;">
					<f:facet name="header">
						<h:outputText value="#{CElabels['entidade.produtoLocacaoRastreamento']}" />
					</f:facet>
					
					<h:panelGrid rowClasses="linhaImpar, linhaPar"
							columnClasses="classEsquerda, classDireita"
							style="background-image:url('/imagens/backGroundTelaBasico.png'); background-repeat: no-repeat;padding: 8px;"
							width="100%" columns="2">
						<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.codigo']}:" />
						<h:outputText styleClass="text" escape="false" value="#{ProdutoLocacaoControle.patrimonio.codigo}" />
						
						<h:outputText styleClass="tituloCampos" value="#{CElabels['entidade.descricao']}:" />
						<h:outputText styleClass="text" escape="false" value="#{ProdutoLocacaoControle.patrimonio.descricao}"/>
					</h:panelGrid>
					
					<h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
						<h:dataTable width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
		                        rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
		                        value="#{ProdutoLocacaoControle.patrimonio.rastreamentos}" var="rastreamento">
							<h:column>
								<f:facet name="header">
									<h:outputText value="#{CElabels['entidade.evento.data']}" />
								</f:facet>
								<h:outputText value="#{rastreamento.dataEventoFormatada}" />
							</h:column>
							<h:column>
								<f:facet name="header">
									<h:outputText value="#{CElabels['entidade.horario.inicial']}" />
								</f:facet>
								<h:outputText value="#{rastreamento.horarioInicialFormatado}" />
							</h:column>
							<h:column>
								<f:facet name="header">
									<h:outputText value="#{CElabels['entidade.horario.final']}" />
								</f:facet>
								<h:outputText value="#{rastreamento.horarioFinalFormatado}" />
							</h:column>
							<h:column>
								<f:facet name="header">
									<h:outputText value="#{CElabels['entidade.ambiente']}" />
								</f:facet>
								<h:outputText value="#{rastreamento.descricaoAmbiente}" />
							</h:column>
							<h:column>
								<f:facet name="header">
									<h:outputText value="#{CElabels['entidade.evento.nome']}" />
								</f:facet>
								<h:outputText value="#{rastreamento.nomeEvento}" />
							</h:column>
							<h:column>
								<f:facet name="header">
									<h:outputText value="#{CElabels['entidade.interessado.nome']}" />
								</f:facet>
								<h:outputText value="#{rastreamento.nomeInteressado}" />
							</h:column>
						</h:dataTable>
					</h:panelGrid>
					<center>
						<a4j:commandButton action="#{ProdutoLocacaoControle.visualizarPatrimonios}"
						        reRender="tablePatrimonioRastreamento"
								value="Visualizar Patrim�nios" styleClass="botoes"
								image="/imagens/botoesCE/visualizar_patrimonios.png"/>
					</center>
				</rich:simpleTogglePanel>
			</h:panelGrid>
		
		<h:panelGrid id="mensagens" columns="1" width="100%" styleClass="tabMensagens">
							<h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
								<h:panelGrid columns="1" width="100%">
							    	
							        	<h:outputText value=" "/>
							    	
							    </h:panelGrid>
							    <h:commandButton rendered="#{ProdutoLocacaoControle.sucesso}" image="/imagens/bt_sucesso.png"/>
							    <h:commandButton rendered="#{ProdutoLocacaoControle.erro}" image="/imagens/erro.png"/>
							    <h:panelGrid columns="1" width="100%">
							    	<h:outputText styleClass="mensagem" value="#{ProdutoLocacaoControle.mensagem}"/>
							    	<h:outputText styleClass="mensagemDetalhada"
								value="#{ProdutoLocacaoControle.mensagemDetalhada}" />
							    	</h:panelGrid>
							</h:panelGrid>
							<h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
								<h:panelGroup>
									<h:panelGrid columns="1" width="100%"
												 columnClasses="colunaCentralizada">
										<h:panelGroup>

											<h:commandButton immediate="true"
															 action="#{ProdutoLocacaoControle.novo}"
															 onclick="if(!confirm('#{CElabels['operacoes.novo']}')) {return false;}"
															 value="#{msg_bt.btn_novo}"
															 styleClass="botoes nvoBt btSec"
															 title="#{msg.msg_novo_dados}"/>

											<a4j:commandButton id="salvar" action="#{ProdutoLocacaoControle.gravar}"
															   value="Gravar"
															   onclick="if(!validar()){return false;};"
															   reRender="panelMensagem"
															   actionListener="#{ProdutoLocacaoControle.autorizacao}"
															   oncomplete="#{TipoContaControle.msgAlert}"
															   alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt">
												<!-- Entidade.PRODUTO_LOCACAO -->
												<f:attribute name="entidade" value="102" />
												<!-- Operacao.GRAVAR -->
												<f:attribute name="operacao" value="G" />
													</a4j:commandButton>

												<a4j:commandButton id="excluir"
																   rendered="#{ProdutoLocacaoControle.produto.codigo gt 0}"
																   action="#{ProdutoLocacaoControle.confirmarExcluir}"
																   oncomplete="#{ProdutoLocacaoControle.msgAlert}"
																   styleClass="botoes nvoBt btSec btPerigo" reRender="form"
																   actionListener="#{ProdutoLocacaoControle.autorizacao}"
																   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3">
													<h:outputText value="    "/>
													<!-- Entidade.fornecedor -->
													<f:attribute name="entidade" value="102"/>
													<!-- Operacao.consultar -->
													<f:attribute name="operacao" value="E"/>
												</a4j:commandButton>

											<h:commandButton id="consultar" immediate="true"
															 action="#{ProdutoLocacaoControle.voltar}"
															 value="#{msg_bt.btn_consultar}"
															 title="#{msg.msg_consultar_dados}" accesskey="4"
															 styleClass="botoes nvoBt btSec"
															 actionListener="#{ProdutoLocacaoControle.autorizacao}">
												<!-- Entidade.PRODUTO_LOCACAO -->
												<f:attribute name="entidade" value="102" />
												<!-- Operacao.EXCLUIR -->
												<f:attribute name="operacao" value="C" />
											</h:commandButton>

										</h:panelGroup>
									</h:panelGrid>
								</h:panelGroup>
							</h:panelGrid>	
						</h:panelGrid>
		</h:form>
				<%@include file="/pages/ce/includes/include_focus.jsp" %>

   				</body>
   		
	</html>
	<%@include file="../../../includes/include_modal_mensagem_generica.jsp"%>
</f:view>