<%@include file="../includes/include_imports.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <html>
       	<!-- Inclui o elemento HEAD da p�gina -->
        <%@include file="../includes/include_head_form.jsp" %>

        <title><h:outputText value="#{CElabels['menu.cadastros.provaConceito']}"/></title>

        <script>
            // SCRIPT DE VALIDA��O DO FORMUL�RIO
            function validar() {
		   		
                // LIMPA AS MENSAGENS CONTIDAS NAS DIVs DETERMINADAS
                limparMensagem('data');
                limparMensagem('nome');
                limparMensagem('valor');
                limparMensagem('descricao');

                // OBTEM OS CAMPOS QUE SER�O VALIDADOS
                var descricao = document.getElementById('form:descricao');
                var data = document.getElementById('form:dataInputDate');
                var valor = document.getElementById('form:valor');
                var nome = document.getElementById('form:nome');

                // VERIFICA SE EST�O PREENCHIDOS
                var validade = true;
	
                if (descricao == null || descricao.value == null || descricao.value == "") {
                    exibirMensagem('<h:outputText value="O campo Descri��o deve ser preenchido"/>', 'descricao');
                    validade = false;
                }
                if (nome == null || nome.value == null || nome.value == "") {
                    exibirMensagem('<h:outputText value="O campo Nome deve ser preenchido"/>', 'nome');
                    validade = false;
                }
                if (data == null || data.value == null || data.value == "") {
                    exibirMensagem('<h:outputText value="O campo Data deve ser preenchido"/>', 'data');
                    validade = false;
                }
                if (valor == null || valor.value == null || valor.value == "") {
                    exibirMensagem('<h:outputText value="O campo Valor deve ser preenchido"/>', 'valor');
                    validade = false;
                }
			
                return validade;
            }
        </script>

        <body>

            <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
                <f:facet name="header">
                    <!-- INCLUS�O DO TOPO DA P�GINA -->

                    <jsp:include page="../includes/topoReduzido.jsp"/>

                </f:facet>
            </h:panelGrid>

            <h:form id="form">
                <!-- MANTEM O CONTROLLER NA �RVORE DE COMPONENTES -->
                <a4j:keepAlive beanName="ProvaConceitoControle" />

                <h:panelGrid columns="1" width="100%" >
                    <h:panelGrid columns="1" style="height:25px; background-image:url('../../../imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                                 columnClasses="colunaCentralizada" width="100%">
                        <h:outputText styleClass="tituloFormulario" value="#{CElabels['menu.cadastros.provaConceito']}"/>
                    </h:panelGrid>
                </h:panelGrid>

                <!-- CAMPOS PARA PREENCHIMENTO -->
                <h:panelGrid rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                             style="background-image:url('./imagens/backGroundTelaBasico.png'); background-repeat: no-repeat;padding: 8px;"
                             width="100%" columns="2">
                    <h:panelGroup>
                        <!-- MARCA��O DE OBRIGATORIEDADE DE PREENCHIMENTO DO CAMPO -->
                        <%@include file="../includes/include_obrigatorio.jsp" %>
                        <h:outputText styleClass="tituloCampos" value="Data" />
                    </h:panelGroup>

                    <h:panelGroup>
                        <rich:calendar id="data" styleClass="form" value="#{ProvaConceitoControle.provaConceito.data}"
                                       datePattern="dd/MM/yyyy" inputSize="10" inputClass="form" enableManualInput="true"
                                       zindex="2" showWeeksBar="false" />
                        <!-- DIV USADA PARA EXIBIR MENSAGEM DE OBRIGATORIEDADE DE PREENCHIMENTO (O ID DESTE CAMPO DEVE SER PRECEDIDO POR 'divObg-') -->
                        <div id="divObg-data" class="mensagemObrigatorio"></div>
                    </h:panelGroup>

                    <h:panelGroup>
                        <%@include file="../includes/include_obrigatorio.jsp" %>
                        <h:outputText styleClass="tituloCampos" value="Nome" />
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:inputText onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form" size="30" maxlength="25"
                                     value="#{ProvaConceitoControle.provaConceito.nome}" id="nome"></h:inputText>
                        <div id="divObg-nome" class="mensagemObrigatorio"></div>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="Descri��o" />
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:inputTextarea rows="3" cols="30" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" id="descricao"
                                         value="#{ProvaConceitoControle.provaConceito.descricao}">
                        </h:inputTextarea>
                        <div id="divObg-descricao" class="mensagemObrigatorio"></div>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="Valor" />
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:inputText onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form" size="10" maxlength="5" id="valor"
                                     value="#{ProvaConceitoControle.provaConceito.valor}"></h:inputText>
                        <div id="divObg-valor" class="mensagemObrigatorio"></div>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos" value="Verdadeiro?" />
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:selectBooleanCheckbox value="#{ProvaConceitoControle.provaConceito.verdadeiro}" id="verdadeiro" />
                        <div id="divObg-verdadeiro" class="mensagemObrigatorio"></div>
                    </h:panelGroup>
                </h:panelGrid>

                <!-- PAINEL DE EXIBI��O DE MENSAGENS E A��ES -->
                <h:panelGrid id="mensagens" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>

                        <!-- MENSAGENS -->
                        <h:commandButton rendered="#{PerfilEventoControle.sucesso}" image="/imagens/bt_sucesso.png"/>
                        <h:commandButton rendered="#{PerfilEventoControle.erro}" image="/imagens/erro.png"/>

                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{ProvaConceitoControle.mensagem}"/>
                        </h:panelGrid>
                    </h:panelGrid>

                    <!-- A��ES -->
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{ProvaConceitoControle.novo}"
                                             value="#{CElabels['operacoes.consulta.consultar']}" image="/imagens/botaoNovo.png"
                                             alt="#{CElabels['operacoes.consulta.consultarDados']}" styleClass="botoes" />

                            <h:outputText value="    " />

                            <h:commandButton id="salvar" action="#{ProvaConceitoControle.salvar}" value="#{CElabels['operacoes.gravar']}"
                                             onclick="if(!validar()){return false;};" image="/imagens/botaoGravar.png"
                                             alt="#{CElabels['operacoes.gravar.dados']}" styleClass="botoes" />

                            <h:outputText value="    " />

                            <h:commandButton id="consultar" immediate="true" action="#{NavegacaoControle.abrirTelaProvaConceito}"
                                             value="#{CElabels['operacoes.consulta.consultar']}" image="/imagens/botaoConsultar.png"
                                             alt="#{CElabels['operacoes.consulta.consultarDados']}" styleClass="botoes" />
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>

            </h:form>
        </body>
    </html>
</f:view>