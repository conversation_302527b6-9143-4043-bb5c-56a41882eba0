<%@include file="../includes/include_imports.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
    	<html>
    			
		<!-- Inclui o elemento HEAD da p�gina -->
		<%@include file="/pages/ce/includes/include_head.jsp" %>
		
		<body>
        <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
        
            <%@include file="../includes/include_topo.jsp"%>
            
            <a4j:keepAlive beanName="OrcamentoDetalhadoControle" />
            
            <tr>
                <td align="left" valign="top" class="bglateral">
                    <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td width="206" align="center" valign="top" class="bglateraltop" style="padding-top:6px;">
								<%@include file="../includes/include_box_menulateral.jsp" %>
                            </td>
                            <td align="left" valign="top" style="padding:7px 15px 0 20px;">
                                <div style="clear:both;">
                                    <table width="98%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-right:30px;margin-bottom:20px;">
                                        <tr>
                                            <td width="19" height="50" align="left" valign="top">
                                            	<img src="${contexto}/images/box_centro_top_left.gif" width="19" height="50">
                                            </td>
                                            <td align="left" valign="top" background="${contexto}/images/box_centro_top.gif"
                                            		class="tituloboxcentro" style="padding:11px 0 0 0;">
                                            	<h:outputText value="#{CElabels['menu.operacoesCE.operacoes.preReserva']}"/>
                                            </td>
                                            <td width="19" align="left" valign="top"><img src="${contexto}/images/box_centro_top_right.gif" width="19" height="50"></td>
                                        </tr>
                                        <tr>
                                            <td align="left" valign="top" background="${contexto}/images/box_centro_left.gif"><img src="${contexto}/images/shim.gif"></td>
                                            <td align="left" valign="top" bgcolor="#ffffff" style="padding:15px 15px 5px 15px;">
												<h:commandButton id="fechaNegociacao" action="#{OrcamentoDetalhadoControle.abrirTelaExibeOrcamento}"
														rendered="#{!OrcamentoDetalhadoControle.orcamentoFicticio}" value="#{CElabels['operacoes.negociacao.exibir']}"/>
											</td>
											<td align="left" valign="top" background="${contexto}/images/box_centro_right.gif"><img src="${contexto}/images/shim.gif"></td>
										</tr>
										<tr>
											<td height="20" align="left" valign="top"><img src="${contexto}/images/box_centro_bottom_left.gif" width="19" height="20"></td>
											<td align="left" valign="top" background="${contexto}/images/box_centro_bottom.gif"><img src="${contexto}/images/shim.gif"></td>
											<td align="left" valign="top"><img src="${contexto}/images/box_centro_bottom_right.gif" width="19" height="20"></td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td height="93" align="left" valign="top" class="bgrodape"><jsp:include page="/include_rodape.jsp" flush="true" /></td>
            </tr>
        </table>
        </body>
        </html>
    </h:form>
        
</f:view>