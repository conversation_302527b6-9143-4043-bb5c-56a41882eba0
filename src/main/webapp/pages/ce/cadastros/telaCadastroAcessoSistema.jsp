<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../includes/include_imports.jsp" %>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
        <html>

            <!-- <PERSON><PERSON><PERSON> o elemento HEAD da página -->
            <head>
                <%@include file="../includes/include_head_ce.jsp" %>
            </head>

            <body class="ce">
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">

                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <jsp:include page="../../../include_topo_2.0.jsp" flush="true"/>
                    <jsp:include page="../../../include_menu_ce_flat.jsp" flush="true"/>
                    <rich:jQuery selector=".item2" query="addClass('menuItemAtual')"/>
                </h:panelGroup>
                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                            <h:panelGroup layout="block" styleClass="container-imagem">
                                <div style="clear:both;">
                                    <table width="98%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-right:30px;margin-bottom:20px;">
                                        <tr>
                                            <td width="19" height="50" align="left" valign="top"><img src="${contexto}/images/box_centro_top_left.gif" width="19" height="50"></td>
                                            <td align="left" valign="top" background="${contexto}/images/box_centro_top.gif" class="tituloboxcentro" style="padding:11px 0 0 0;">Acesso ao Sistema</td>
                                            <td width="19" align="left" valign="top"><img src="${contexto}/images/box_centro_top_right.gif" width="19" height="50"></td>
                                        </tr>
                                        <tr>
                                            <td align="left" valign="top" background="${contexto}/images/box_centro_left.gif"><img src="${contexto}/images/shim.gif"></td>
                                            <td align="left" valign="top" bgcolor="#ffffff" style="padding:15px 15px 5px 15px;" class="headerIndex">
                                                <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
                                                    <h:panelGrid columnClasses="w33,w33,w33" columns="3" width="100%" cellpadding="0" cellspacing="0">


                                                    <h:panelGrid columns="1" style="height:100%;" width="100%" cellpadding="5" cellspacing="5" >
                                                        <h:panelGroup>
                                                            <div>
                                                                <h:outputLink styleClass="linkWiki"
                                                                              value="#{SuperControle.urlBaseConhecimento}configurando-os-dados-da-empresa-para-emissao-de-notas-fiscais/"
                                                                              title="Clique e saiba mais: Empresa"
                                                                              target="_blank" >
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a class="titulo3" id="categoria" onclick="abrirPopup('${contexto}/faces/empresaCons.jsp?modulo=centralEventos', 'Empresa', 800, 595);" href="#">
                                                                    <h:outputText value="#{CElabels['menu.cadastros.acessoSistema.empresa']}"/>
                                                                </a>
                                                            </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Os sistemas Web da Pacto Soluções trabalham com o conceito de multiempresa, onde através de apenas um sistema será possível controlar várias filiais de uma mesma empresa. Nessa tela é possível cadastrar as filiais que utilizarão o sistema e acompanhar de perto o rendimento de cada uma delas."/>
                                                        </h:panelGroup>
                                                        <h:panelGroup>
                                                            <rich:spacer height="25px"/>
                                                            <div>
                                                                <h:outputLink styleClass="linkWiki"
                                                                              value="#{SuperControle.urlBaseConhecimento}quais-sao-as-permissoes-do-perfil-de-acesso-do-modulo-adm/"
                                                                              title="Clique e saiba mais: Perfis de Acesso"
                                                                              target="_blank" >
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a class="titulo3" id="produto" onclick="abrirPopup('${contexto}/faces/perfilAcessoCons.jsp?modulo=centralEventos', 'perfilAcessoCons', 800, 595);" href="#">
                                                                    <h:outputText value="#{CElabels['menu.cadastros.acessoSistema.perfisAcesso']}"/>
                                                                </a>
                                                            </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="O controle do que os colaboradores podem acessar ou não dentro de um sistema é muito importante e aqui você pode definir os perfis de acesso, limitando e permitindo que as pessoas tenham acessos diferentes ao sistema. Este é importante para a segurança do sistema, cadastre os perfis de acordo com a função do colaborador."/>
                                                            <br />
                                                        </h:panelGroup>
                                                    </h:panelGrid>
                                                    <h:panelGrid style="height:100%;" columns="1" width="100%" cellpadding="5" cellspacing="5" >
                                                        <h:panelGroup>
                                                            <div>
                                                                <h:outputLink styleClass="linkWiki"
                                                                              value="#{SuperControle.urlBaseConhecimento}como-cadastro-um-novo-usuario-no-sistema/"
                                                                              title="Clique e saiba mais: Usuário"
                                                                              target="_blank" >
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a class="titulo3" id="produtosLocacao" onclick="abrirPopup('${contexto}/faces/usuarioCons.jsp?modulo=centralEventos', 'Usuário', 820, 595);" href="#">
                                                                    <h:outputText  value="#{CElabels['menu.cadastros.acessoSistema.usuario']}"/>
                                                                </a>
                                                            </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Cadastre aqui os usuários do sistema. Para acessar é necessário que o usuário seja devidamente cadastrado e possua Login (usuário) e Senha de acesso. Lembre-se que senhas são únicas e não devem ser compartilhadas."/>
                                                        </h:panelGroup>
                                                        <h:panelGroup>
                                                            <rich:spacer height="25px"/>
                                                            <div>
                                                                <h:outputLink styleClass="linkWiki"
                                                                              value="#{SuperControle.urlBaseConhecimento}como-utilizar-o-controle-de-log/"
                                                                              title="Clique e saiba mais: Log do Sistema"
                                                                              target="_blank" >
                                                                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                </h:outputLink>
                                                                <a class="titulo3" id="tabelaPrecos" onclick="abrirPopup('${contexto}/faces/controleLogCons.jsp?modulo=centralEventos', 'controleLogCons', 800, 595);" href="#">
                                                                    <h:outputText value="#{CElabels['menu.cadastros.acessoSistema.controleLog']}"/>
                                                                </a>
                                                            </div>
                                                            <rich:spacer height="25px"/>
                                                            <rich:spacer width="10px"/>
                                                            <h:outputText styleClass="text" value="Aqui temos os registros das operações que são realizadas no sistema, com as informações de qual usuário realizou a operação, em qual data e horário, dentre outras informações. Por exemplo é possível rastrear alguma operação que foi executada de forma indevida."/>
                                                        </h:panelGroup>

                                                    </h:panelGrid>
                                                    <h:panelGrid columns="1" width="100%" style="height:100%;" cellpadding="5" cellspacing="5" >
                                                        <h:panelGroup>
                                                            <div><img src="${contexto}/imagens/cadastros.png"></div>
                                                        </h:panelGroup>

                                                    </h:panelGrid>

                                                </h:panelGrid>
                                            </h:panelGrid>
                                        </td>
                                        <td align="left" valign="top" background="${contexto}/images/box_centro_right.gif"><img src="${contexto}/images/shim.gif"></td>
                                        </tr>
                                        <tr>
                                            <td height="20" align="left" valign="top"><img src="${contexto}/images/box_centro_bottom_left.gif" width="19" height="20"></td>
                                            <td align="left" valign="top" background="${contexto}/images/box_centro_bottom.gif"><img src="${contexto}/images/shim.gif"></td>
                                            <td align="left" valign="top"><img src="${contexto}/images/box_centro_bottom_right.gif" width="19" height="20"></td>
                                        </tr>
                                    </table>
                                </div>
                            </h:panelGroup>
                            <%@include file="../includes/include_box_menulatcadastrosacesso.jsp" %>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <jsp:include page="../../../include_rodape_flat.jsp" flush="true"/>
                <%@include file="../includes/include_focus.jsp" %>
            </h:panelGroup>
            </body>
        </html>
    </h:form>
</f:view>
