<%@include file="include_imports.jsp" %>

<style>
    .rich-calendar-tool-btn{
        font-size: 12px;
        text-align: center;
        width: 100%
    }
</style>

<table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
    <tr>
        <td>
            <rich:calendar value="#{menuLateralControle.data}"
                           preloadDateRangeBegin="#{menuLateralControle.data}" preloadDateRangeEnd="#{menuLateralControle.data}"
                           id="menuCalendar"
                           boundaryDatesMode="select"
                           showWeeksBar="false" popup="false" datePattern="dd/MM/yyyy HH:mm" showApplyButton="false"
                           style="width:50px; float:left;">
                <f:facet name="header">
                    <h:panelGrid columns="2">
                        <h:panelGrid columns="3" >
                            <h:outputText value="{previousMonthControl}"/>
                            <h:outputText value="{currentMonthControl}"/>
                            <h:outputText value="{nextMonthControl}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </f:facet>
                <f:facet name="weekDay">
                    <h:panelGroup layout="block">
                        <h:outputText value="{weekDayLabelShort}"/>
                    </h:panelGroup>
                </f:facet>
                <h:outputText value="{day}" />
                <a4j:support event="onchanged" action="#{AgendaAmbienteColaboradorControle.verPreferencias}" reRender="agendaGeral" >
                    <f:setPropertyActionListener target="#{AgendaAmbienteColaboradorControle.dataSelecionada}" value="#{menuLateralControle.data}"/>
                </a4j:support>

            </rich:calendar>
            <rich:spacer height="10px"/>
        </td>

    </tr>
    <rich:spacer height="10px"/>

</table>
<table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
    <tr>
        <td colspan="2" align="left" valign="top"><img style="vertical-align:middle;margin-right:9px;" src="${contexto}/imagens/estudio/m_comprar.png" width="16" height="16"><a class="titulo2">Servi�os</a></td>
    </tr>
</table>

<table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:10px;">
    <tr>
        <td width="20" align="left" valign="top"><img src="${contexto}/images/shim.gif"></td>
        <td align="left" valign="top">
            <div>
                <h:outputLink styleClass="linkWiki"
                              value="Consulta Disponibilidade"
                              title="Consulta Disponibilidade"
                              target="_blank" >
                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                </h:outputLink>
                <a4j:commandLink styleClass="titulo3" id="linkConsultaDisponibilidade" value="Disponibilidade" action="#{disponibilidadeControle.acaoEntrar}"/>
            </div>
            <div class="sepmenu"><img src="${contexto}/images/shim.gif"></div>

            <div>
                <h:outputLink styleClass="linkWiki"
                              value="Realizar Compras"
                              title="Realizar Compras"
                              target="_blank" >
                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                </h:outputLink>
                <a4j:commandLink styleClass="titulo3"
                                 actionListener="#{VendaAvulsaControle.prepare}"
                                 action="#{VendaAvulsaControle.novo}" id="linkRealizarCompras">
                    Venda Avulsa
                    <f:attribute name="modulo" value="4bf2add2267962ea87f029fef8f75a2f"/>
                </a4j:commandLink>
            </div>
            <div class="sepmenu"><img src="${contexto}/images/shim.gif"></div>

            <div>
                <h:outputLink styleClass="linkWiki"
                              value="Pacote"
                              title="Pacote"
                              target="_blank" >
                    <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                </h:outputLink>
                <a4j:commandLink styleClass="titulo3" id="linkPacote" value="Pacote" action="#{pacoteControle.acaoEntrar}"/>
            </div>
            <div class="sepmenu"><img src="${contexto}/images/shim.gif"></div>
        </td>
    </tr>
</table>
<rich:spacer height="3px"/>
<c:if test="${LoginControle.permissaoAcessoMenuVO.agendaMensal or LoginControle.permissaoAcessoMenuVO.agendaAmbiente or
        LoginControle.permissaoAcessoMenuVO.agendaProfissional or LoginControle.permissaoAcessoMenuVO.agendaIndividual}">
    <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:6px;">
        <tr>
            <td colspan="2" align="left" valign="top"><img style="vertical-align:middle;margin-right:9px;" src="${contexto}/imagens/estudio/m_agenda.png" width="16" height="16"><a class="titulo2">Agendas</a></td>
        </tr>
    </table>
    <table width="146" border="0" cellspacing="0" cellpadding="0" style="padding-bottom:10px;">
        <tr>
            <td width="20" align="left" valign="top"><img src="${contexto}/images/shim.gif"></td>
            <td align="left" valign="top">
                <c:if test="${LoginControle.permissaoAcessoMenuVO.agendaMensal}">
                    <div>
                        <h:outputLink styleClass="linkWiki"
                                      value="Mensal"
                                      title="Mensal"
                                      target="_blank" >
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <a4j:commandLink styleClass="titulo3" id="linkMensal" action="#{agendaMensalController.acaoEntrar}">Mensal
                        </a4j:commandLink>
                    </div>
                    <div class="sepmenu"><img src="${contexto}/images/shim.gif"></div>
                </c:if>

                <c:if test="${LoginControle.permissaoAcessoMenuVO.agendaAmbiente}">
                    <div>
                        <h:outputLink styleClass="linkWiki"
                                      value="Ambiente"
                                      title="Ambiente"
                                      target="_blank" >
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <a4j:commandLink styleClass="titulo3" id="linkAmbiente" action="#{AgendaAmbienteColaboradorControle.verPreferencias}"
                                         reRender="agendaGeral">Ambiente
                            <f:setPropertyActionListener value="ambiente" target="#{AgendaAmbienteColaboradorControle.parTipoFiltro}"/>
                        </a4j:commandLink>
                    </div>
                    <div class="sepmenu"><img src="${contexto}/images/shim.gif"></div>
                </c:if>

                <c:if test="${LoginControle.permissaoAcessoMenuVO.agendaProfissional}">
                    <div>
                        <h:outputLink styleClass="linkWiki"
                                      value="Profissional"
                                      title="Profissional"
                                      target="_blank" >
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <a4j:commandLink styleClass="titulo3" id="linkAgeProfissional" action="#{AgendaAmbienteColaboradorControle.verPreferencias}"
                                         reRender="agendaGeral">Profissional
                            <f:setPropertyActionListener value="colaborador" target="#{AgendaAmbienteColaboradorControle.parTipoFiltro}"/>
                        </a4j:commandLink>
                    </div>
                    <div class="sepmenu"><img src="${contexto}/images/shim.gif"></div>
                </c:if>


                <c:if test="${LoginControle.permissaoAcessoMenuVO.agendaIndividual}">
                    <div>
                        <h:outputLink styleClass="linkWiki"
                                      value="Profissional"
                                      title="Profissional"
                                      target="_blank" >
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <a4j:commandLink styleClass="titulo3" id="linkProfissional" action="#{agendaIndividualControle.acaoEntrar}">Individual
                            <f:setPropertyActionListener value="true" target="#{agendaIndividualControle.recarregarDados}"/>
                        </a4j:commandLink>
                    </div>
                    <div class="sepmenu"><img src="${contexto}/images/shim.gif"></div>
                </c:if>
            </td>
        </tr>
    </table>
</c:if>