<%@include file="imports.jsp" %>
<%@include file="/includes/include_import_minifiles.jsp" %>
<rich:modalPanel id="toolTipAgenda" autosized="true" shadowOpacity="false" moveable="true"
                 showWhenRendered="#{AgendaAmbienteColaboradorControle.apresentarToolTip}" width="450"
                 height="80" >
    <f:facet name="header" >
        <h:outputText value="Dados"/>
    </f:facet>

    <h:form id="formToolTip">
        <h:panelGrid columns="2" border="0" styleClass="texto_agenda tabela_agenda" style="text-align: left;" >
            <h:outputText value="Aluno:"/>
            <h:outputText value="#{AgendaAmbienteColaboradorControle.itemAgenda.clienteVO.pessoa.nome}"/>
            <h:outputText value="Colaborador:"/>
            <h:outputText value="#{AgendaAmbienteColaboradorControle.itemAgenda.colaboradorVO.pessoa.nome}" />
            <h:outputText value="Agendamento:"/>
            <h:outputText value="#{AgendaAmbienteColaboradorControle.itemAgenda.tipoHorarioVO.descricao}" />
            <h:outputText value="Status: " />
            <h:outputText value="#{AgendaAmbienteColaboradorControle.itemAgenda.status.descricao}" />
        </h:panelGrid>
        <h:panelGrid columns="4" border="0" style="float: right; border-style: none; border-width: 0px;">

            <rich:column>
                <a4j:commandLink id="historicoBVCliente"
                                 style="font-size: 23px;color:rgba(56, 53, 53, 0.65);"
                                 action="#{AgendaAmbienteColaboradorControle.montarHistoricoBVClienteEditar}"
                                 title="Visualizar Boletim Visita"
                                 oncomplete="setDocumentCookie('popupsImportante', '',1);abrirPopup('../../questionarioClienteCRMForm.jsp', 'HistoricoBVCliente', 1000, 650);">
                    <i class="fa-icon-time"></i>
                </a4j:commandLink>
            </rich:column>

            <rich:column>
                <a4j:commandButton
                    image="#{context}/imagens/estudio/editar.png"
                    value="Editar" action="#{AgendaAmbienteColaboradorControle.buscarDetalhesAgenda}"
                    reRender="toolTipAgenda, panelAgendaAluno, modalPanelErro, panelAluno-servico-generico" style="cursor: pointer;" oncomplete="Richfaces.showModalPanel('panelAgendaAluno');fireElementFromParent('form:btnAtualizaDadosStudioCliente');">
                    <f:setPropertyActionListener target="#{AgendaAmbienteColaboradorControle.agendaDetalhada}" value="#{AgendaAmbienteColaboradorControle.itemAgenda}" />
                </a4j:commandButton>
            </rich:column>
            <rich:column>
                <a4j:commandButton image="#{context}/imagens/estudio/excluir.png"
                        value="Excluir" title="Excluir"
                        style="cursor: pointer" status="statusHora"
                        action="#{AgendaAmbienteColaboradorControle.verificarUsuarioSenhaResponsavel}"
                        oncomplete="#{AgendaAmbienteColaboradorControle.mensagemNotificar}"
                        reRender="toolTipAgenda,panelAutorizacaoFuncionalidade">
                    <f:setPropertyActionListener value="#{AgendaAmbienteColaboradorControle.itemAgenda}"
                                                 target="#{AgendaAmbienteColaboradorControle.agendaSelecionada}"/>
                </a4j:commandButton>
            </rich:column>
            <rich:column>
                <a4j:commandButton
                    image="#{context}/imagens/estudio/fechar.png"
                    title="Fechar" style="cursor: pointer"
                    action="#{AgendaAmbienteColaboradorControle.acaoFecharToolTip}"
                    reRender="toolTipAgenda" >
                </a4j:commandButton>
            </rich:column>
        </h:panelGrid>
    </h:form>
</rich:modalPanel>
