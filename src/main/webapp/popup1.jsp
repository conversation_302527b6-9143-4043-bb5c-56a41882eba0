<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <rich:modalPanel id="panelProfissao" autosized="true" shadowOpacity="true" width="550" height="300">        
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Cadastro de Profissão"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink1"/>
                <rich:componentControl for="panelProfissao" attachTo="hidelink1" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formProfissao" ajaxSubmit="true">
            <h:panelGrid columns="1"  width="100%">               
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Profissao_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2"  rowClasses="linhaImpar, linhaPar"  columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText   value="#{msg_aplic.prt_Profissao_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  value="#{ClienteControle.profissaoVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText   value="#{msg_aplic.prt_Profissao_descricao}" />
                    <h:panelGroup>
                        <h:inputText  id="nomeProfissao" required="true" size="45" maxlength="45"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  value="#{ClienteControle.profissaoVO.descricao}" />
                        <h:message for="nome_Profissao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                </h:panelGrid>

                <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{ClienteControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ClienteControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>  
                            <a4j:commandButton id="salvar" reRender="profissao" action="#{ClienteControle.gravarProfissao}" oncomplete="Richfaces.hideModalPanel('panelProfissao')" value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>

        </a4j:form>
    </rich:modalPanel>
    <rich:modalPanel id="panelCategoria" autosized="true" shadowOpacity="true" width="550" height="300">        
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Cadastro de Categoria"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink"/>
                <rich:componentControl for="panelCategoria" attachTo="hidelink" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formCategoria" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Categoria_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Categoria_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ClienteControle.categoriaVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Categoria_nome}" />
                    <h:panelGroup>
                        <h:inputText  id="nome" size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ClienteControle.categoriaVO.nome}" />
                        <h:message for="nome" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Categoria_tipoCategoria}" />
                    <h:selectOneMenu  id="tipoCategoria" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ClienteControle.categoriaVO.tipoCategoria}" >
                        <f:selectItems  value="#{CategoriaControle.listaSelectItemTipoCategoriaCategoria}" />
                    </h:selectOneMenu>
                    <%--Foi solicitado pela GQS Bruna que fosse retirado, pois não é utilizado em nenhum local do ZW--%>
                    <%--<h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Categoria_nrConvitePermitido}" />--%>
                    <%--<h:inputText  id="nrConvitePermitido" size="10" maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ClienteControle.categoriaVO.nrConvitePermitido}" />--%>

                </h:panelGrid>

                <h:panelGrid id="panelMensagem" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{ClienteControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{ClienteControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>  
                            <a4j:commandButton id="salvar" reRender="categoria" action="#{ClienteControle.gravarCategoria}" oncomplete="Richfaces.hideModalPanel('panelCategoria')" value="#{msg_bt.btn_gravar}" image="./imagens/botaoGravar.png" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>

        </a4j:form>
    </rich:modalPanel>


    <h:form id="form">  
        <html>
            <jsp:include page="include_head.jsp" flush="true" />
            <body>
                <script type="text/javascript" language="javascript" src="hoverform.js"></script>               
                <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
                    <tr>                                    
                        <td align="center" valign="top" bgcolor="#ffffff">
                            <table width="500" border="0" cellpadding="0" cellspacing="0" style="padding:10px;">
                                <tr>                                          

                                    <td align="left" valign="top">
                                        <!-- inicio botões -->
                                        <h:commandButton id="salvar1" action="#{ClienteControle.gravarSimples}"  value="Confirmar" onclick="location='javascript:window.close();'"  alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
                                        <input onClick="location='javascript:window.close();'" type="button" name="Submit" value="Cancelar">

                                        <!-- fim botões -->
                                        <!-- inicio item -->

                                        <div style="clear:both;" class="text">
                                            <p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Informações Pessoais</p>
                                        </div>
                                        <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div>
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablelistras textsmall" style="margin-bottom:25px;">

                                            <tr>
                                                <td width="25%" height="27" align="right" valign="middle" class="par"><span class="text" style="font-weight: bold">C&oacute;digo:</span></td>
                                                <td align="left" valign="middle" class="par"> <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true" title="file" value="#{ClienteControle.clienteVO.codigo}" /></td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle"><span class="text" style="font-weight: bold">Foto:</span></td>
                                                <td align="left" valign="middle"><img src="images/foto_exemplo.jpg" width="81" height="81" class="foto"><br><br>

                                                    <input onBlur="blurinput(this);" onFocus="focusinput(this);" size="30" class="form" type="file"/></td>
                                            </tr>

                                            <tr>
                                                <td height="27" align="right" valign="middle" class="par"><span class="text" style="font-weight: bold">* Nome:</span> </td>
                                                <td align="left" valign="middle" class="par"> <h:inputText   id="nome"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" size="50" maxlength="50"  value="#{ClienteControle.pessoaVO.nome}" /></td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle" ><span class="text" style="font-weight: bold">* Data de Nascimento:</span></td>
                                                <td align="left" valign="middle" >
                                                    <h:inputText  id="dataNasc"
                                                                  onblur="blurinput(this);"
                                                                  onfocus="focusinput(this);"
                                                                  styleClass="form"
                                                                  onkeypress="return mascara(this.form, 'form:dataNasc', '99/99/9999', event);"
                                                                  size="10"
                                                                  maxlength="10"
                                                                  value="#{ClienteControle.pessoaVO.dataNasc}" >
                                                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                                                        <h:message for="dataNasc"  styleClass="mensagemDetalhada"/>
                                                    </h:inputText>
                                                    00/00/0000</td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle" class="par"><span class="text" style="font-weight: bold">* Categoria:</span></td>
                                                <td align="left" valign="middle" class="par"><h:panelGroup>
                                                        <h:selectOneMenu  id="categoria"   onblur="blurinput(this);"  onfocus="focusinput(this);"styleClass="form"  value="#{ClienteControle.clienteVO.categoria.codigo}" >
                                                            <f:selectItems  value="#{ClienteControle.listaSelectItemCategoria}" />
                                                        </h:selectOneMenu>
                                                        <a4j:commandButton id="atualizar_categoria" action="#{ClienteControle.montarListaSelectItemCategoria}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:categoria"/>
                                                        <a4j:commandButton id="consultaDadosCategoria" action="#{ProfissaoControle.inicializarProfisaoControle}"  focus="nomeCategoria" alt="Cadastrar Categoria" reRender="formCategoria" oncomplete="Richfaces.showModalPanel('panelCategoria')" image="./images/icon_add.gif" />
                                                        <h:message for="categoria" styleClass="mensagemDetalhada"/>
                                                    </h:panelGroup></td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle"><span class="text" style="font-weight: bold">Nome da m&atilde;e:</span></td>
                                                <td align="left" valign="middle" ><h:inputText  id="nomeMae" onblur="blurinput(this);"  styleClass="form" onfocus="focusinput(this);" size="50" maxlength="50" value="#{ClienteControle.pessoaVO.nomeMae}" /></td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle"class="par" ><span class="text" style="font-weight: bold">Nome do pai:</span></td>
                                                <td align="left" valign="middle" class="par">  <h:inputText  id="nomePai"  onblur="blurinput(this);" styleClass="form" onfocus="focusinput(this);" size="50" maxlength="50"  value="#{ClienteControle.pessoaVO.nomePai}" /></td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle"><span class="text" style="font-weight: bold">Sexo biol&oacute;gico:</span></td>
                                                <td align="left" valign="middle"><h:selectOneRadio  id="sexo"  onblur="blurinput(this);" styleClass="form" onfocus="focusinput(this);" value="#{ClienteControle.pessoaVO.sexo}" >
                                                        <f:selectItems  value="#{ClienteControle.listaSelectItemSexoPessoa}" />
                                                    </h:selectOneRadio>   </td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle"class="par" ><span class="text" style="font-weight: bold">Nome da profiss&atilde;o:</span></td>
                                                <td align="left" valign="middle" class="par"><h:selectOneMenu  id="profissao"  onblur="blurinput(this);"  styleClass="form"  onfocus="focusinput(this);"  value="#{ClienteControle.pessoaVO.profissao.codigo}" >
                                                        <f:selectItems  value="#{ClienteControle.listaSelectItemProfissao}" />
                                                    </h:selectOneMenu>
                                                    <a4j:commandButton id="atualizar_profissao" action="#{ClienteControle.montarListaSelectItemProfissao}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:profissao"/>
                                                    <a4j:commandButton id="consultaDadosProfissao" action="#{ProfissaoControle.inicializarProfisaoControle}"  focus="nomeProfissao" alt="Cadastrar Profissão" reRender="formProfissao" oncomplete="Richfaces.showModalPanel('panelProfissao')" image="./images/icon_add.gif" />

                                                </td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle"><span class="text" style="font-weight: bold">Grau de instru&ccedil;&atilde;o:</span></td>
                                                <td align="left" valign="middle">
                                                    <h:selectOneMenu  id="grauIntrucao"   onblur="blurinput(this);"  onfocus="focusinput(this);"  styleClass="form" value="#{ClienteControle.pessoaVO.grauInstrucao}" >
                                                        <f:selectItems  value="#{ClienteControle.listaSelectItemGrauInstrucaoPessoa}" />
                                                    </h:selectOneMenu>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle" class="par"><span class="text" style="font-weight: bold">Estado civil:</span></td>
                                                <td align="left" valign="middle" class="par"> <h:selectOneMenu  id="estadoCivil"  onblur="blurinput(this);"  styleClass="form"  onfocus="focusinput(this);"  value="#{ClienteControle.pessoaVO.estadoCivil}" >
                                                        <f:selectItems  value="#{ClienteControle.listaSelectItemEstadoCivilPessoa}" />
                                                    </h:selectOneMenu></td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle"><span class="text" style="font-weight: bold"><span class="text" style="font-weight: bold">CPF:</span></span></td>
                                                <td align="left" valign="middle"><span>
                                                        <h:inputText  id="cpf" size="14" maxlength="14"  styleClass="form"  onblur="blurinput(this);"  onfocus="focusinput(this);" onkeypress="return mascara(this.form, 'form:cpf', '999.999.999-99', event);"  value="#{ClienteControle.pessoaVO.cfp}" />
                                                    </span>Digitar somente n&uacute;meros </td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle"class="par" ><span class="text" style="font-weight: bold">Identidade:</span></td>
                                                <td align="left" valign="middle" class="par"><h:inputText  id="rg" size="20" onblur="blurinput(this);"   styleClass="form" onfocus="focusinput(this);" maxlength="20"  value="#{ClienteControle.pessoaVO.rg}" />
                                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="text" style="font-weight: bold">&Oacute;rg&atilde;o Expedidor:</span><h:inputText  id="rgOrgao"  onblur="blurinput(this);"   styleClass="form" onfocus="focusinput(this);" size="10" maxlength="10" value="#{ClienteControle.pessoaVO.rgOrgao}" />
                                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;      <span class="text" style="font-weight: bold">UF:</span>
                                                    <h:selectOneMenu  id="rgUf" onblur="blurinput(this);"  styleClass="form" onfocus="focusinput(this);"  value="#{ClienteControle.pessoaVO.rgUf}" >
                                                        <f:selectItems  value="#{ClienteControle.listaSelectItemRgUfPessoa}" />
                                                    </h:selectOneMenu></td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle"><span class="text" style="font-weight: bold">Data do cadastro:</span></td>
                                                <td align="left" valign="middle" >
                                                    <h:inputText  id="dataCadastro" onblur="blurinput(this);"  styleClass="form" onfocus="focusinput(this);"  onkeypress="return mascara(this.form, 'form:dataCadastro', '99/99/9999', event);" size="10" maxlength="10"  value="#{ClienteControle.pessoaVO.dataCadastro}" >
                                                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                                                        <h:message for="dataCadastro"  styleClass="mensagemDetalhada"/>
                                                    </h:inputText><a href="javascript:;"></td>
                                            </tr>
                                        </table>
                                        <!-- fim item -->
                                        <!-- inicio item -->
                                        <div style="clear:both;" class="text">
                                            <p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Contato</p>
                                        </div>
                                        <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div>
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablelistras textsmall" style="margin-bottom:25px;">

                                            <tr>
                                                <td width="25%" height="27" align="right" valign="middle" class="par"><span class="text" style="font-weight: bold">Tel. residencial:</span></td>
                                                <td align="left" valign="middle" class="par"> <h:inputText  id="residencial"  size="13"
                                                                                                            maxlength="13"
                                                                                                            onchange="return validar_Telefone(this.id);"
                                                                                                            onblur="blurinput(this);"
                                                                                                            onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                                                                                            onfocus="focusinput(this);"
                                                                                                            styleClass="form"  value="#{ClienteControle.telefoneResidencialVO.numero}" />
                                                </td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle"><span class="text" style="font-weight: bold">Tel. comercial:</span> </td>
                                                <td align="left" valign="middle"> <h:inputText id="comercial"   size="13"
                                                                                               maxlength="13"
                                                                                               onchange="return validar_Telefone(this.id);"
                                                                                               onblur="blurinput(this);"
                                                                                               onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                                                                               onfocus="focusinput(this);"
                                                                                               styleClass="form"  value="#{ClienteControle.telefoneComercialVO.numero}"/>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle" class="par"><span class="text" style="font-weight: bold">Tel. celular:</span></td>
                                                <td align="left" valign="middle" class="par"> <h:inputText  id="celular"  size="13"
                                                                                                            maxlength="13"
                                                                                                            onchange="return validar_Telefone(this.id);"
                                                                                                            onblur="blurinput(this);"
                                                                                                            onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                                                                                                            onfocus="focusinput(this);"
                                                                                                            styleClass="form" value="#{ClienteControle.telefoneCelularVO.numero}"/>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle"><span class="text" style="font-weight: bold">E-mail:</span></td>
                                                <td align="left" valign="middle"><h:inputText  id="email" size="50" onblur="blurinput(this);"  onfocus="focusinput(this);"  styleClass="form" maxlength="50"  value="#{ClienteControle.emailVO.email}" /></td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle" class="par"><span class="text" style="font-weight: bold">Web page:</span></td>
                                                <td align="left" valign="middle" class="par"><h:inputText  id="webPage" size="50" onblur="blurinput(this);"  onfocus="focusinput(this);"  styleClass="form" maxlength="50"  value="#{ClienteControle.pessoaVO.webPage}" /></td>
                                            </tr>
                                        </table>
                                        <!-- fim item -->
                                        <!-- inicio item -->
                                        <div style="clear:both;" class="text">
                                            <p style="margin-bottom:6px;"><img src="images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">Endere&ccedil;o Residencial</p>
                                        </div>
                                        <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div>
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablelistras textsmall" style="margin-bottom:25px;">

                                            <tr>
                                                <td width="25%" height="27" align="right" valign="middle" class="par"><span class="text" style="font-weight: bold">CEP:</span></td>
                                                <td align="left" valign="middle" class="par"> <h:inputText  id="CEP"  onblur="blurinput(this);"  styleClass="form" onfocus="focusinput(this);" size="10" maxlength="10" onkeypress="return mascara(this.form, 'form:CEP', '99.999-999', event);"  value="#{ClienteControle.enderecoResidencialVO.cep}" /><a class="textsmall" href="javascript:;"><img border="0" style="margin-left:4px;margin-right:4px;vertical-align:middle;" src="images/icon_lupa.png" title="Consulte o CEP" width="16" height="16">Consulte o CEP</a></td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle"><span class="text" style="font-weight: bold">Endere&ccedil;o:</span> </td>
                                                <td align="left" valign="middle"> <h:inputText  size="40" maxlength="40"   onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ClienteControle.enderecoResidencialVO.endereco}"/>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle" class="par"><span class="text" style="font-weight: bold">Complemento:</span></td>
                                                <td align="left" valign="middle" class="par"> <h:inputText  size="40"  onblur="blurinput(this);"  onfocus="focusinput(this);" maxlength="40"  styleClass="form" value="#{ClienteControle.enderecoResidencialVO.complemento}" />
                                                </td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle"><span class="text" style="font-weight: bold">Bairro:</span></td>
                                                <td align="left" valign="middle"> <h:inputText  size="35" maxlength="35"  onblur="blurinput(this);"  onfocus="focusinput(this);"  styleClass="form" value="#{ClienteControle.enderecoResidencialVO.bairro}" />
                                                    <span class="text" style="font-weight: bold">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;N&ordm;:</span>
                                                    <h:inputText  size="10" maxlength="10"  onblur="blurinput(this);"  onfocus="focusinput(this);"  styleClass="form" value="#{ClienteControle.enderecoResidencialVO.numero}"/>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle" class="par"><span class="text" style="font-weight: bold">País:</span></td>
                                                <td align="left" valign="middle" class="par"><h:selectOneMenu  id="pais"  onblur="blurinput(this);"  onfocus="focusinput(this);"  styleClass="form" value="#{ClienteControle.pessoaVO.pais.codigo}" >
                                                        <a4j:support  event="onchange" ajaxSingle="true"  reRender="form:cidade" action="#{ClienteControle.montarListaSelectItemCidade}"/>
                                                        <f:selectItems  value="#{ClienteControle.listaSelectItemPais}" />
                                                    </h:selectOneMenu></td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle" ><span class="text" style="font-weight: bold">Cidade:</span></td>
                                                <td align="left" valign="middle" ><h:selectOneMenu  id="cidade"  onblur="blurinput(this);"  onfocus="focusinput(this);"  styleClass="form" value="#{ClienteControle.pessoaVO.cidade.codigo}" >
                                                        <a4j:support  event="onchange" ajaxSingle="true"  reRender="form:estado" action="#{ClienteControle.consultarEstadoPorCidade}"/>
                                                        <f:selectItems  value="#{ClienteControle.listaSelectItemCidade}" />
                                                    </h:selectOneMenu></td>
                                            </tr>
                                            <tr>
                                                <td height="27" align="right" valign="middle"class="par"><span class="text" style="font-weight: bold">UF:</span></td>
                                                <td align="left" valign="middle"class="par"> <h:inputText id="estado"  readonly="true" size="3" maxlength="3"  onblur="blurinput(this);"  onfocus="focusinput(this);"  styleClass="form" value="#{ClienteControle.pessoaVO.cidade.estado}" /></td>
                                            </tr>
                                        </table>
                                        <!-- fim item -->
                                        <!-- inicio botões -->
                                        <h:commandButton id="salvar" action="#{ClienteControle.gravarSimples}" onclick="location='javascript:window.close();'"  value="Confirmar"  alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>

                                        <input onClick="location='javascript:window.close();'" type="button" name="Submit2" value="Cancelar">

                                        <!-- fim botões -->

                            </table>	
                        </td>
                    </tr>
                </table>

            </body>
        </html>
    </h:form>
</f:view>