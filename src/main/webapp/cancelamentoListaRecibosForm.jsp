<%-- 
    Document   : cancelamentoListaRecibosForm
    Created on : 31/10/2011, 10:51:21
    Author     : <PERSON><PERSON><PERSON>
--%>

<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Cancelamento" />
    </title>
    <c:set var="titulo" scope="session" value="Cancelamento"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>
    </h:panelGrid>

    <h:form id="form">
        <h:panelGrid columns="2" width="100%" styleClass="tabMensagens">
            <h:panelGrid columns="1" >
                <img src="./imagens/lateralWizardCancelamentoMaior.png" >
            </h:panelGrid>
            <h:panelGrid columns="1" width="100%" cellpadding="2" >
                <p id="tituloDetalhes" style="font-weight: bold;margin-top: 15px;margin-bottom: 15px;" class="fonteTrebuchet">
                    <img src="images/arrow2.gif" style="vertical-align:middle;margin-right:6px;">Pagamentos Conjuntos</p>
                    <h:outputText styleClass="tituloCampos" value="Foram encontrados neste contrato recibos conjuntos, ou seja, um mesmo recibo pagando vários contratos. Confira na lista a seguir: "/>
                    <rich:dataTable id="listaRecibos" width="100%" border="0" cellspacing="0" cellpadding="2" styleClass="textsmall"
                                    columnClasses="centralizado, colunaEsquerda, centralizado, colunaDireita" value="#{CancelamentoContratoControle.recibos}" var="recibo">
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold" value="Recibo"/>
                            </f:facet>
                            <h:outputText value="#{recibo.codigo}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold" value="Nome Pagador"/>
                            </f:facet>
                            <h:outputText value="#{recibo.nomePagador}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold" value="Data Término"/>
                            </f:facet>
                            <h:outputText value="#{recibo.dataRecebimento}">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText style="font-weight: bold" value="Valor"/>
                            </f:facet>
                            <h:outputText value="#{MovPagamentoControle.empresaLogado.moeda}  "/>
                            <h:outputText value="#{recibo.valor}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                        </rich:column>
                        <rich:column colspan="4" breakBefore="true" styleClass="colunaEsquerda">
                            <h:outputText style="font-weight: bold" value="Contratos:"/>
                        <p style="margin-left: 20px;margin-top: 0px;margin-bottom: 0px;">
                            <rich:dataTable id="listaContratos" width="60%" border="0" cellspacing="0" cellpadding="0" styleClass="textsmall"
                                            columnClasses="centralizado, colunaEsquerda, centralizado" rowClasses="linhaImpar"
                                            value="#{recibo.contratos}" var="contrato" style="border-color: white;">
                                <rich:column style="border-color: white;">
                                    <h:outputText value="#{contrato.codigo}"/>
                                </rich:column>
                                <rich:column style="border-color: white;">
                                    <h:outputText value="#{contrato.nomePessoa}"/>
                                </rich:column>
                                <rich:column style="border-color: white;">
                                    <h:outputText value="#{contrato.situacaoContrato}"/>
                                </rich:column>
                            </rich:dataTable>
                        </p>
                    </rich:column>
                </rich:dataTable>
                <h:outputText value="Se houverem PAGAMENTOS COM CHEQUE em que seja necessário devolvê-los você pode estornar
                              os recibos e receber dos alunos separado. Mas se escolher não estornar, não será possível
                              DEVOLVER OS CHEQUES, pois eles quitam contrato(s) de outro(s) aluno(s)." styleClass="tituloCampos"/>
                <h:panelGrid width="565px" columns="2" style="position:relative; top:10px;"  >
                    <h:panelGrid width="350px"/>
                    <h:panelGrid width="50px">
                        <h:panelGroup>
                            <h:commandButton id="voltar" title="voltar" action="#{CancelamentoContratoControle.voltarTelaCancelamento}" image="./imagens/botaoVoltar.png" />
                            <rich:spacer width="7"/>
                            <h:commandButton id="proximo" action="#{CancelamentoContratoControle.existePagamentoComCheque}" 
                                             image="./imagens/botaoProximo.png" title="continuar">
                                <a4j:support event="onclick" reRender="panel"/>
                            </h:commandButton>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </h:form>
</f:view>