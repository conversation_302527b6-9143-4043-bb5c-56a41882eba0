<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <link href="./css/otimize.css" rel="stylesheet" type="text/css">
    <script type="text/javascript" language="javascript" src="script/script.js"></script>


</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<link href="./css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/font-awesome.css" type="text/css" rel="stylesheet"/>
<link href="css/gestaoRecebiveis1.0.min.css" rel="stylesheet" type="text/css">
<link href="../css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
<link href="../css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>
<link href="css/otimize.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>


<script>
    function carregarTooltipsterQuestionario(){
        carregarQuestionario(jQuery('.tooltipster'));
    }
    function carregarQuestionario(el) {
        el.tooltipster({
            theme: 'tooltipster-light',
            position: 'bottom',
            animation: 'grow',
            contentAsHTML: true
        });
    }
</script>
<style type="text/css">
    .rich-color-picker-input {
        width: 0px;
        visibility: hidden;
    }
    .rich-tabpanel-content {
        background: none !important;
        border: none !important;
        padding-top: 7px !important;
        padding-left: 0px !important;
        padding-right: 0px !important;
        text-align: left !important;
    }
    .rich-fileupload-list-decor {
        border: none;
        padding: 0 0 0 0;
        margin: 0 0 0 0;
    }

    .rich-fileupload-toolbar-decor {
        background-color: transparent;
        border: none;
    }

    .rich-fileupload-table-td {
        border: none;
    }
    .rich-fileupload-ico-add {
        background-image: url(images/drive-upload.png);
    }

    .rich-fileupload-button {
        background-color: transparent;
        background-image: none;
    }

    .rich-fileupload-button-border {
        border: none;
    }

    .rich-fileupload-button-light, .rich-fileupload-button-press {
        background-image: none;
        background-color: transparent;
    }
    .rich-table {
        background: none !important;
        border: none !important;
        text-align: left !important;
    }

    .rich-table-cell {
        border: none !important;
        text-align: left !important;
    }

    .rich-table-row {
        padding-top: 10px !important;
    }

    .rich-tab-active {
        background: none !important;
        background-color: #ffffff !important;
        border-color: #C0C0C0;
        font-weight: bold;

        border-bottom-color: white;
        border-bottom-width: 1px;
    }

    .rich-tab-inactive {
        background: none !important;
        border: none !important;
    }

    .rich-tab-header {
        font-size: 14px;
        padding: 10px;

        font-family: arial, helvetica, sans-serif;
    }

    .rich-tabhdr-cell-disabled {
        background: none !important;
        border: none !important;
    }

    .rich-tabhdr-cell-inactive {
        background: none !important;
        border: none !important;
    }

    .rich-tabhdr-side-cell {
        background: none !important;
        border: none !important;
    }

    .rich-tabhdr-side-border {
        background: none !important;
        border: none !important;
    }

    .rich-tab-bottom-line {
        border-width: 1px;
        border-color: #C0C0C0;
        background-color: #EEEEEE !important;
    }

    .rich-tab-bottom-line table:first-child {
        width: 100%;
    }
</style>


<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <c:set var="titulo" scope="session" value="${QuestionarioControle.cadastroPesquisa ? msg_aplic.prt_Questionario_Pesquisa_tituloForm : msg_aplic.prt_Questionario_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:${QuestionarioControle.cadastroPesquisa ? 'Pesquisa' : 'Questionario'}"/>
    <style>
        .chk-fa-container input[type="checkbox"] + span {
            margin-top: 4px;
        }

        .contratoSelecionado tbody tr {
            background-color: #ffffff;
        }

        .texto-size-15 {
            font-size: 14px !important;
        }

        .caixainfo .texto-size-14 {
            font-size: 11px !important;
        }
    </style>

    <hr style="border-color: #e6e6e6;"/>

    <rich:modalPanel id="panelPergunta" autosized="true" shadowOpacity="true" width="700" height="300">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_Questionario_perguntaConsulta}"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink3"/>
                <rich:componentControl for="panelPergunta" attachTo="hidelink3" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formPergunta" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%">
                <rich:dataTable id="resultadoConsultaPergunta" width="100%" headerClass="consulta"
                                rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                styleClass="novaModal"
                                value="#{QuestionarioControle.listaPerguntaQuestionario}" rows="6" var="pergunta">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="camposSomenteLeitura" value="#{msg_aplic.prt_Pergunta_codigo}"/>
                        </f:facet>
                        <h:outputText id="codigo" value="#{pergunta.codigo}"/>
                    </rich:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Pergunta_descricao}"/>
                        </f:facet>
                        <h:outputText id="descricao" value="#{pergunta.descricao}"/>
                    </h:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <h:selectBooleanCheckbox id="adicionarPergunta" styleClass="campos"
                                                 value="#{pergunta.adicionarPergunta}">
                            <a4j:support event="onclick" ajaxSingle="true"/>
                        </h:selectBooleanCheckbox>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formPergunta:resultadoConsultaPergunta" maxPages="10"
                                   id="scResultadoPergunta"/>
                <h:panelGrid columns="1" width="100%" headerClass="subordinado" styleClass="novaModal"
                             columnClasses="colunaCentralizada">
                    <a4j:commandButton action="#{QuestionarioControle.adicionarPerguntaQuestionarioLista}"
                                       reRender="form" id="addPerguntaQuestionario"
                                       oncomplete="Richfaces.hideModalPanel('panelPergunta')"
                                       value="#{msg_bt.btn_adicionar}" accesskey="5" styleClass="botoes"
                                       image="./imagens/botaoAdicionar.png"
                                       alt="Adicionar as perguntas selecionadas ao Questionário"/>
                </h:panelGrid>
                <h:panelGrid id="mensagemConsultaPergunta" columns="1" width="100%" styleClass="novaModal">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{QuestionarioControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{QuestionarioControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <title>
        <h:outputText
                value="#{QuestionarioControle.cadastroPesquisa ? 'Pesquisa' : msg_aplic.prt_Questionario_tituloForm}"/>
    </title>

    <h:panelGrid columns="1" width="100%" styleClass="novaModal" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
        <h:form id="form">
            <%--Logica introduzida para remover a TAB quando for questionario e preservar a TAB quando for pesquisa--%>
            <c:choose>
                <c:when test="${QuestionarioControle.cadastroPesquisa}">
                    <rich:tabPanel width="100%">
                        <rich:tab id="formPesquisa"
                                  label="Pesquisa / NPS">
                            <jsp:include page="include_questionarioForm.jsp"/>
                        </rich:tab>
                        <rich:tab rendered="#{QuestionarioControle.cadastroPesquisa}" id="previsualizacaoPesquisa"
                                  label="Pré Visualização" action="#{QuestionarioControle.preVisuzalizacao}">
                            <iframe src="${QuestionarioControle.urlP}" style="width: 100%; height: 600px">

                            </iframe>
                        </rich:tab>
                    </rich:tabPanel>
                </c:when>
                <c:otherwise>
                    <jsp:include page="include_questionarioForm.jsp"/>
                </c:otherwise>
            </c:choose>
            <hr style="border-color: #e6e6e6;"/>
            <h:panelGrid columns="1" width="100%" styleClass="novaModal">
                <h:panelGrid columns="1" width="100%" styleClass="novaModal">
                    <h:panelGrid columns="3" width="100%" styleClass="novaModal">
                        <h:panelGrid columns="1" width="100%" styleClass="novaModal">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton rendered="#{QuestionarioControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{QuestionarioControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%" styleClass="novaModal">
                            <h:outputText styleClass="mensagem" value="#{QuestionarioControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{QuestionarioControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" styleClass="novaModal">

                        <h:panelGroup>
                            <a4j:commandLink id="novo" immediate="true" action="#{QuestionarioControle.novo}"
                                             value="#{msg_bt.btn_novo}" styleClass="botoes nvoBt btSec"/>

                            <h:outputText value="    "/>

                            <a4j:commandLink id="salvar" action="#{QuestionarioControle.gravar}"
                                             oncomplete="#{QuestionarioControle.mensagemNotificar}"
                                             reRender="form"
                                             value="#{msg_bt.btn_gravar}" styleClass="botoes nvoBt"/>

                            <h:outputText value="    "/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandLink id="excluir" reRender="mdlMensagemGenerica"
                                                 oncomplete="#{QuestionarioControle.msgAlert}"
                                                 action="#{QuestionarioControle.confirmarExcluir}"
                                                 value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}"
                                                 accesskey="3" styleClass="botoes nvoBt btSec icon-remove"/>
                            </h:panelGroup>

                            <h:outputText value="    "/>

                            <a4j:commandLink id="consultar" immediate="true"
                                             action="#{QuestionarioControle.inicializarConsultar}"
                                             value="#{msg_bt.btn_voltar_lista}"
                                             styleClass="botoes nvoBt btSec"/>

                            <h:outputText value="    "/>

                            <a4j:commandLink id="log"
                                             action="#{QuestionarioControle.realizarConsultaLogObjetoSelecionado}"
                                             reRender="form"
                                             oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                             title="Visualizar Log" styleClass="botoes nvoBt btSec">
                                <i style="text-decoration: none" class="fa-icon-list"/>
                            </a4j:commandLink>

                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
</f:view>
<script src="script/packJQueryPlugins.min.js" type="text/javascript" ></script>
<script>
    jQuery("p").removeAttr("style");

    jQuery.noConflict();
    let focu = document.getElementById("form:nomeInterno")
    if (focu != null && focu != undefined) {
        document.getElementById("form:nomeInterno").focus();
    }
    carregarTooltipsterQuestionario();
</script>