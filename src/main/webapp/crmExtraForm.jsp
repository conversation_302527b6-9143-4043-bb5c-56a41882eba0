<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    body {
        background-color: #FFFFFF !important;
        margin: 0px;
        padding: 0px;
    }
    table.tabForm{
        background-color: #FFFFFF !important;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_CRMExtra_tituloForm}"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <c:set var="titulo" scope="session" value="${msg_aplic.prt_CRMExtra_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-gerar-uma-meta-de-ligacao-para-o-consultor-diferente-das-que-existem-no-crm-meta-extra/"/>
        <%-- INICIO HEADER --%>
        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="topoReduzido_material_crm.jsp"/>
            </f:facet>
        </h:panelGroup>
        <%-- FIM HEADER --%>


        <h:commandLink action="#{MalaDiretaControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria"
                       style="display: none"/>
        <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">



            <h:panelGroup layout="block" rendered="#{MalaDiretaControle.criarEditarMailing}">
                <jsp:include page="newMalaDiretaForm.jsp"/>
            </h:panelGroup>


            <h:panelGrid id="msgsCRMExtra" columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                    <h:commandButton rendered="#{MalaDiretaControle.sucesso}" image="./imagensCRM/sucesso.png"/>
                    <h:commandButton rendered="#{MalaDiretaControle.erro}" image="./imagensCRM/erro.png"/>
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText id="msgCRMExtra" styleClass="mensagem"
                                      value="#{MalaDiretaControle.mensagem}"/>
                        <h:outputText id="msgCRMExtraDet" styleClass="mensagemDetalhada"
                                      value="#{MalaDiretaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </h:panelGrid>

    <%@include file="includes/include_modal_mensagem_generica.jsp" %>

</f:view>

