<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />

<style type="text/css">
body {
	margin: 0px;
	padding: 0px;
}
</style>

<f:view>
	<jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
	<title><h:outputText value="#{msg_aplic.prt_Agenda_tituloForm}" />
	</title>
	<c:set var="titulo" scope="session" value="${msg_aplic.prt_Agenda_tituloForm}"/>
	<c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}bi-aulas-experimentais-adm/"/>

	<rich:modalPanel id="panelAgenda" autosized="true" shadowOpacity="true"
		width="450" height="250"
		onshow="document.getElementById('formAgenda').focus();">
		<f:facet name="header">
			<h:panelGroup>
				<h:outputText value="#{msg_aplic.prt_Agenda_escolhaAgendamento}" />
			</h:panelGroup>
		</f:facet>
		<f:facet name="controls">
			<h:panelGroup>
				<h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer"
					id="hiperlinkAgenda" />
				<rich:componentControl for="panelAgenda" attachTo="hiperlinkAgenda"
					operation="hide" event="onclick" />
			</h:panelGroup>
		</f:facet>

		<a4j:form id="formAgenda" ajaxSubmit="true">
			<h:panelGrid columns="1" width="100%">
				<h:panelGrid columns="1" columnClasses="colunaAlinhamento"
					width="100%" style="border:1px solid black;">
					<h:panelGroup>
						<h:outputText styleClass="tituloFormulario"
							value="#{msg_aplic.prt_Agenda_tipoAgendamento}" />
						<rich:spacer width="5px" />
						<h:selectOneRadio id="opcoesTipoAgendamento"
							onblur="blurinput(this);" onfocus="focusinput(this);"
							styleClass="form" style="border: none;"
							value="#{AgendaControle.agendaVO.tipoAgendamento}">
							<f:selectItems
								value="#{AgendaControle.listaSelectItemTipoAgendamentoAgenda}" />
							<a4j:support event="onclick" ajaxSingle="true"
								reRender="dadosTelefonar, panelEmail, formAgenda " />
						</h:selectOneRadio>
						<rich:spacer width="5" />


						<h:panelGrid columns="1" width="100%"
							rendered="#{AgendaControle.apresentarInputTextAulaExperimental}">
							<h:panelGroup id="panelModalidade">
								<h:outputText styleClass="tituloCampos"
									value="#{msg_aplic.prt_Agenda_modalidade}" />
								<rich:spacer width="5px" />
								<h:inputText id="textModalidade" size="50"
									styleClass="camposSomenteLeitura"
									value="#{AgendaControle.agendaVO.modalidade.nome}" />
								<rich:suggestionbox height="200" width="200"
									for="textModalidade" fetchValue="#{result.nome}"
									suggestionAction="#{AgendaControle.autocompleteModalidade}"
									minChars="3" rowClasses="20"
									nothingLabel="Nenhuma Modalidade encontrada !" var="result"
									id="suggestionResponsavel">

									<h:column>
										<h:outputText value="#{result.nome}" />
									</h:column>
								</rich:suggestionbox>
							</h:panelGroup>
						</h:panelGrid>


					</h:panelGroup>

					<h:panelGrid columns="1" width="100%">
						<h:panelGroup>
							<a4j:outputPanel layout="block">
								<h:outputText styleClass="tituloCampos"
									value="#{msg_aplic.prt_Agenda_data}" />
								<rich:spacer width="5" />
								<rich:calendar id="modaldataAgendamento"
									oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
									value="#{AgendaControle.agendaVO.dia}" enableManualInput="true"
									popup="true" inputSize="10" datePattern="dd/MM/yyyy"
									showApplyButton="false" cellWidth="24px" cellHeight="24px"
									style="width:200px" inputClass="campos" showFooter="false" />

								<rich:spacer width="20" />
								<h:outputText styleClass="tituloCampos"
									value="#{msg_aplic.prt_Agenda_hora}" />
								<rich:spacer width="5" />
								<h:selectOneMenu value="#{AgendaControle.agendaVO.hora}">
									<f:selectItems value="#{AgendaControle.listaSelectItemHoras}" />
								</h:selectOneMenu>
								<rich:spacer width="20" />
								<h:outputText styleClass="tituloCampos"
									value="#{msg_aplic.prt_Agenda_minuto}" />
								<rich:spacer width="5" />
								<h:selectOneMenu value="#{AgendaControle.agendaVO.minuto}">
									<f:selectItems value="#{AgendaControle.listaSelectItemMinutos}" />
								</h:selectOneMenu>
							</a4j:outputPanel>
						</h:panelGroup>
					</h:panelGrid>

				</h:panelGrid>
			</h:panelGrid>

			<h:panelGrid columns="1" columnClasses="colunaDireita" width="100%">
				<h:panelGroup>
					<a4j:commandButton id="btnOK" reRender="form, formAgenda"
						action="#{AgendaControle.validarStatusTipoAgendamento}"
						oncomplete="#{AgendaControle.manterAbertoRichModalPanelAgenda}"
						styleClass="botoes" image="./imagensCRM/botaoOK.png" />
					<rich:spacer width="10" />
					<a4j:commandButton id="btnCancelar" reRender="form, formAgenda"
						action="#{AgendaControle.cancelarAgendamentoPanelAgenda}"
						oncomplete="#{AgendaControle.fecharRichModalPanelAgenda}"
						styleClass="botoes" image="./imagensCRM/botaoCancelar.png" />
				</h:panelGroup>
			</h:panelGrid>


			<h:panelGrid id="panelGridMensagensAgendamento" columns="1"
				width="100%">
				<h:outputText styleClass="mensagem"
					value="#{AgendaControle.mensagem}" />
				<h:outputText styleClass="mensagemDetalhada"
					value="#{AgendaControle.mensagemDetalhada}" />
			</h:panelGrid>
		</a4j:form>
	</rich:modalPanel>



	<%--Email--%>
	<%--Panel Modelo Mensagem--%>

	<rich:modalPanel id="panelModeloMensagem" autosized="true"
		shadowOpacity="true" width="550" height="300"
		onshow="document.getElementById('formModeloMensagem:consultarModeloMensagem').focus();">
		<f:facet name="header">
			<h:panelGroup>
				<h:outputText
					value="#{msg_aplic.prt_MalaDireta_consultarModeloMensagem}" />
			</h:panelGroup>
		</f:facet>
		<f:facet name="controls">
			<h:panelGroup>
				<h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer"
					id="hiperlinkModeloMensagem" />
				<rich:componentControl for="panelModeloMensagem"
					attachTo="hiperlinkModeloMensagem" operation="hide" event="onclick" />
			</h:panelGroup>
		</f:facet>
		<a4j:form id="formModeloMensagem" ajaxSubmit="true">
			<h:panelGrid columns="1" width="100%">
				<h:panelGrid columns="4" width="100%">
					<h:outputText styleClass="tituloCampos"
						value="#{msg.msg_consultar_por}" />
					<h:selectOneMenu styleClass="campos" id="consultarModeloMensagem"
						value="#{HistoricoContatoControle.campoConsultarModeloMensagem}">
						<f:selectItems
							value="#{HistoricoContatoControle.tipoConsultarComboModeloMensagem}" />
					</h:selectOneMenu>
					<h:inputText id="valorConsultarModeloMensagem" styleClass="campos"
						value="#{HistoricoContatoControle.valorConsultarModeloMensagem}" />
					<a4j:commandButton id="btnConsultarModeloMensagem"
						reRender="formModeloMensagem:consultarModeloMensagem, formModeloMensagem:resultadoConsultaModeloMensagem , formModeloMensagem:scResultadoModeloMensagem , formModeloMensagem"
						action="#{HistoricoContatoControle.consultarModeloMensagem}"
						styleClass="botoes" value="#{msg_bt.btn_consultar}"
						image="./imagensCRM/botaoConsultar.png" />
				</h:panelGrid>
				<rich:dataTable id="resultadoConsultaModeloMensagem" width="100%"
					headerClass="consulta" rowClasses="linhaImpar, linhaPar"
					columnClasses="colunaCentralizada"
					value="#{HistoricoContatoControle.listaConsultarModeloMensagem}" rows="10"
					var="modeloMensagem">
					<rich:column>
						<f:facet name="header">
							<h:outputText value="#{msg_aplic.prt_ModeloMensagem_codigo}" />
						</f:facet>
						<h:outputText value="#{modeloMensagem.codigo}" />
					</rich:column>
					<rich:column>
						<f:facet name="header">
							<h:outputText value="#{msg_aplic.prt_ModeloMensagem_titulo}" />
						</f:facet>
						<h:outputText value="#{modeloMensagem.titulo}" />
					</rich:column>
					<rich:column>
						<f:facet name="header">
							<h:outputText value="#{msg_bt.btn_opcoes}" />
						</f:facet>
						<a4j:commandButton
							action="#{HistoricoContatoControle.selecionarModeloMensagem}"
							focus="modeloMensagem"
							reRender="form, panelEmail, resultadoConsultaModeloMensagem "
							oncomplete="Richfaces.hideModalPanel('panelModeloMensagem')"
							value="#{msg_bt.btn_selecionar}" styleClass="botoes"
							image="./imagensCRM/botaoEditar.png" />
					</rich:column>
				</rich:dataTable>
				<rich:datascroller align="center"
					for="formModeloMensagem:resultadoConsultaModeloMensagem"
					maxPages="10" id="scResultadoModeloMensagem" />
				<h:panelGrid id="mensagemConsultaModeloMensagem" columns="1"
					width="100%" styleClass="tabMensagens">
					<h:panelGrid columns="1" width="100%">
						<h:outputText styleClass="mensagem"
							value="#{HistoricoContatoControle.mensagem}" />
						<h:outputText styleClass="mensagemDetalhada"
							value="#{HistoricoContatoControle.mensagemDetalhada}" />
					</h:panelGrid>
				</h:panelGrid>
			</h:panelGrid>
		</a4j:form>
	</rich:modalPanel>





	<%-- OBJECAO --%>

	<rich:modalPanel id="panelObjecao" autosized="true"
		shadowOpacity="true" width="400" height="300"
		onshow="document.getElementById('formModeloMensagem:consultarObjecao').focus();">
		<f:facet name="header">
			<h:panelGroup>
				<h:outputText value="#{msg_aplic.prt_Objecao_consultarObjecao}" />
			</h:panelGroup>
		</f:facet>
		<f:facet name="controls">
			<h:panelGroup>
				<h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer"
					id="hiperlinkObjecao" />
				<rich:componentControl for="panelObjecao"
					attachTo="hiperlinkObjecao" operation="hide" event="onclick" />
			</h:panelGroup>
		</f:facet>
		<a4j:form id="formObjecao" ajaxSubmit="true">
			<h:panelGrid columns="1" width="100%">
				<h:panelGrid columns="4" width="100%">
					<%--<h:outputText styleClass="tituloCampos"
						value="#{msg.msg_consultar_por}" />
					<h:selectOneMenu styleClass="campos" id="consultarObjecao"
						value="#{AgendaControle.campoConsultarObjecao}">
						<f:selectItems value="#{HistoricoContatoControle.tipoConsultaComboObjecao}" />
					</h:selectOneMenu>
					<h:inputText id="valorConsultarObjecao" styleClass="campos"
						value="#{AgendaControle.valorConsultarObjecao}" />
					<a4j:commandButton id="btnConsultarObjecao"
						reRender="formObjecao:consultarObjecao, formObjecao:resultadoConsultaObjecao , formObjecao:scResultadoObjecao , formObjecao"
						action="#{AgendaControle.consultarObjecao}" styleClass="botoes"
						value="#{msg_bt.btn_consultar}"
						image="./imagensCRM/botaoConsultar.png" />--%>
				</h:panelGrid>
				<rich:dataTable id="resultadoConsultaObjecao" width="100%"
					headerClass="consulta" rowClasses="linhaImpar, linhaPar"
					columnClasses="colunaCentralizada"
					value="#{HistoricoContatoControle.listaConsultaObjecao}" rows="10"
					var="objecao">
					<rich:column>
						<f:facet name="header">
							<h:outputText value="#{msg_aplic.prt_Objecao_codigo}" />
						</f:facet>
						<h:outputText value="#{objecao.codigo}" />
					</rich:column>
					<rich:column>
						<f:facet name="header">
							<h:outputText value="#{msg_aplic.prt_Objecao_descricao}" />
						</f:facet>
						<h:outputText value="#{objecao.descricao}" />
					</rich:column>
					<rich:column>
						<f:facet name="header">
							<h:outputText value="#{msg_aplic.prt_Objecao_grupo}" />
						</f:facet>
						<h:outputText value="#{objecao.grupo}" />
					</rich:column>
					<rich:column>
						<f:facet name="header">
							<h:outputText value="#{msg_bt.btn_opcoes}" />
						</f:facet>
						<a4j:commandButton action="#{HistoricoContatoControle.selecionarObjecao}"
							focus="objecao" reRender="form"
							oncomplete="Richfaces.hideModalPanel('panelObjecao')"
							value="#{msg_bt.btn_selecionar}" styleClass="botoes"
							image="./imagensCRM/botaoEditar.png" />
					</rich:column>
				</rich:dataTable>
				<rich:datascroller align="center"
					for="formObjecao:resultadoConsultaObjecao" maxPages="10"
					id="scResultadoObjecao" />
				<h:panelGrid id="mensagemConsultaObjecao" columns="1" width="100%"
					styleClass="tabMensagens">
					<h:panelGrid columns="1" width="100%">
						<h:outputText styleClass="mensagem"
							value="#{HistoricoContatoControle.mensagem}" />
						<h:outputText styleClass="mensagemDetalhada"
							value="#{HistoricoContatoControle.mensagemDetalhada}" />
					</h:panelGrid>
				</h:panelGrid>
			</h:panelGrid>
		</a4j:form>
	</rich:modalPanel>






	<h:panelGrid columns="1" styleClass="tabForm" width="100%">
		
		<h:panelGrid columns="1"
					style="height:10px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
					columnClasses="colunaCentralizada" width="100%">
					<h:outputText styleClass="tituloFormulario"
						value="#{msg_aplic.prt_Agenda_realizarContato}" />
		</h:panelGrid>
		<f:facet name="controls">
			<h:panelGroup>
				<h:graphicImage value="/imagensCRM/close.png" style="cursor:pointer"
					id="hiperlinkAgendamento" />
				<rich:componentControl for="panelAgendamento"
					attachTo="hiperlinkAgendamento" operation="hide" event="onclick" />
			</h:panelGroup>
		</f:facet>
		<h:form id="form">
			<h:panelGrid columns="1" styleClass="tabForm" width="100%">

				<h:panelGrid columns="1" width="100%" style="border:1px solid black">

					<h:panelGrid columns="2" width="100%"
						style="border:1px solid black" columnClasses="colunaEsquerda">
						<h:panelGrid columns="1" width="50px" cellpadding="0"
							cellspacing="0" columnClasses="colunaEsquerda">
							<a4j:outputPanel id="panelFoto">
								<a4j:mediaOutput element="img" id="imagem1"
                                                                                 rendered="#{!SuperControle.fotosNaNuvem}" 
									style="width:60px;height:80px " cacheable="false"
									session="true" createContent="#{ClienteControle.paintFoto}"
									value="#{ImagemData}" mimeType="image/jpeg">
									<f:param value="#{SuperControle.timeStamp}" name="time" />
                                                                        <f:param name="largura" value="60"/>
                                                                        <f:param name="altura" value="80"/>
								</a4j:mediaOutput>
                                                                <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}" 
                                                                        width="60" height="80"                                        
                                                                        style="width:60px;height:80px"
                                                                        url="#{ClienteControle.paintFotoDaNuvem}"/>
							</a4j:outputPanel>
						</h:panelGrid>
						<h:panelGrid columns="1" columnClasses="colunaEsquerda"
							cellpadding="0" cellspacing="0" style="text-align: top;"
							width="100%">
							<h:panelGrid columns="4" columnClasses="colunaEsquerda"
								width="100%">
								<h:panelGrid columns="1" columnClasses="colunaEsquerda"
									width="100%">
									<h:panelGroup>
										<h:outputText styleClass="tituloCamposAgenda"
											value="#{msg_aplic.prt_Agenda_aluno}" />
									</h:panelGroup>
									<h:panelGroup>
										<h:outputText styleClass="camposAgenda"
											value="#{HistoricoContatoControle.historicoContatoVO.clienteVO.pessoa.nome}" />
									</h:panelGroup>
								</h:panelGrid>
								<h:panelGrid columns="1" width="100%"
									columnClasses="colunaCentralizada">
									<h:outputText styleClass="tituloCamposAgenda"
										value="#{msg_aplic.prt_Agenda_idade}" />
									<h:outputText styleClass="camposAgenda"
										value="#{HistoricoContatoControle.historicoContatoVO.idade}" />
								</h:panelGrid>
								<h:panelGrid columns="1" width="100%"
									columnClasses="colunaCentralizada">
									<h:outputText styleClass="tituloCamposAgenda"
										value="#{msg_aplic.prt_Agenda_estadoCivil}" />
									<h:outputText styleClass="camposAgenda"
										rendered="#{!HistoricoContatoControle.historicoContatoVO.alinharOutputEstadoCivil}"
										value="#{HistoricoContatoControle.historicoContatoVO.clienteVO.pessoa.estadoCivil_Apresentar}" />
									<rich:spacer height="22px"
										rendered="#{HistoricoContatoControle.historicoContatoVO.alinharOutputEstadoCivil}" />
								</h:panelGrid>
								<h:panelGrid columns="1" columnClasses="colunaCentralizada"
									width="100%">
									<h:outputText styleClass="tituloCamposAgenda"
										value="#{msg_aplic.prt_Agenda_dataCadastro}" />
									<h:outputText styleClass="camposAgenda"
										value="#{HistoricoContatoControle.historicoContatoVO.clienteVO.pessoa.dataCadastro_Apresentar}" />
									<%--<h:panelGroup>
										<h:outputText styleClass="dataLancamento"
											value="#{msg_aplic.prt_Agenda_dataLancamento}" />
									</h:panelGroup>
									<h:panelGroup>
										<h:outputText
											value="#{HistoricoContatoControle.historicoContatoVO.dia_Apresentar}"
											styleClass="dataLancamento" />
									</h:panelGroup>--%>
								</h:panelGrid>
							</h:panelGrid>

							<rich:spacer width="30px;" />

							<h:panelGroup>
								<h:panelGrid columns="7" width="100%">

									<h:panelGrid columns="1" width="100%"
										columnClasses="colunaCentralizada">
										<h:outputText styleClass="tituloCamposAgenda"
											value="#{msg_aplic.prt_Agenda_diasUltAcesso}" />
										<h:outputText styleClass="camposAgenda"
											rendered="#{!HistoricoContatoControle.historicoContatoVO.alinharOutputDiasUltAcesso}"
											value="#{HistoricoContatoControle.historicoContatoVO.diasUltAcesso_Apresentar}" />
										<rich:spacer height="15px"
											rendered="#{HistoricoContatoControle.historicoContatoVO.alinharOutputDiasUltAcesso}" />
									</h:panelGrid>

									<h:panelGrid columns="1" width="100%"
										columnClasses="colunaCentralizada">
										<h:outputText styleClass="tituloCamposAgenda"
											value="#{msg_aplic.prt_Agenda_diasCadastrado}" />
										<h:outputText styleClass="camposAgenda"
											value="#{HistoricoContatoControle.historicoContatoVO.diasCadastrado}" />
									</h:panelGrid>

									<h:panelGrid columns="1" width="100%"
										columnClasses="colunaCentralizada">
										<h:outputText styleClass="tituloCamposAgenda"
											value="#{msg_aplic.prt_Agenda_ligacoes}" />
										<h:outputText styleClass="camposAgenda"
											value="#{HistoricoContatoControle.historicoContatoVO.totalLigacao}" />
									</h:panelGrid>
									<h:panelGrid columns="1" width="100%"
										columnClasses="colunaCentralizada">
										<h:outputText styleClass="tituloCamposAgenda"
											value="#{msg_aplic.prt_Agenda_qtdEmail}" />
										<h:outputText styleClass="camposAgenda"
											value="#{HistoricoContatoControle.historicoContatoVO.qtdEmail}" />
									</h:panelGrid>
									<h:panelGrid columns="1" width="100%"
										columnClasses="colunaCentralizada">
										<h:outputText styleClass="tituloCamposAgenda"
											value="#{msg_aplic.prt_Agenda_qtdPessoal}" />
										<h:outputText styleClass="camposAgenda"
											value="#{HistoricoContatoControle.historicoContatoVO.qtdPessoal}" />
									</h:panelGrid>
								</h:panelGrid>
							</h:panelGroup>
						</h:panelGrid>

					</h:panelGrid>

					<h:panelGrid columns="3" width="100%"
						style="border:1px dotted black">
						<h:panelGrid columns="1" width="100%"
							columnClasses="colunaCentralizada">
							<h:outputText styleClass="tituloCamposAgenda"
								value="#{msg_aplic.prt_Agenda_faseAtual}" />
							<rich:spacer height="20px"
								rendered="#{HistoricoContatoControle.historicoContatoVO.alinharOutputs}" />
						</h:panelGrid>

						<h:panelGrid columns="1" width="100%"
							columnClasses="colunaCentralizada">
							<h:panelGroup>
								<h:outputText styleClass="tituloCamposAgenda"
									value="#{msg_aplic.prt_Agenda_tipoContato}" />
								<rich:spacer width="5px" />
								<h:selectOneRadio id="tipoContato" onblur="blurinput(this);"
									onfocus="focusinput(this);" styleClass="form"
									style="border: none;"
									value="#{HistoricoContatoControle.contatoVO.tipoContato}">
									<f:selectItems
										value="#{HistoricoContatoControle.listaSelectItemTipoContato}" />
									<a4j:support event="onclick" ajaxSingle="true"
										reRender="dadosTelefonar, panelEmail, panelGridSituacaoContato" />
								</h:selectOneRadio>
							</h:panelGroup>
						</h:panelGrid>
						<h:panelGrid id="panelGridSituacaoContato" columns="1"
							width="100%" columnClasses="colunaCentralizada">
							<h:panelGrid
								rendered="#{AgendaControle.contatoVO.apresentarSituacaoContato}"
								columns="1" width="100%" columnClasses="colunaCentralizada">
								<h:panelGroup>
									<h:outputText styleClass="tituloCamposAgenda"
										value="#{msg_aplic.prt_Agenda_situacaoContato}" />
									<rich:spacer width="5px" />
									<h:selectOneMenu
										value="#{HistoricoContatoControle.contatoVO.situacaoContato}">
										<f:selectItems
											value="#{HistoricoContatoControle.historicoContatoVO.tipoConsultaComboCliente}" />
										<a4j:support event="onchange" ajaxSingle="true"
											action="#{HistoricoContatoControle.limparCamposAgendaEHistoricoContato}"
											reRender="dadosTelefonar, panelEmail, situacaoContato, panelGridSituacaoContato" />
									</h:selectOneMenu>
								</h:panelGroup>
							</h:panelGrid>
						</h:panelGrid>
					</h:panelGrid>

					<h:panelGrid id="dadosTelefonar" columns="1" width="100%">
						<h:panelGrid columns="1"
							style="height:10px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
							columnClasses="colunaCentralizada" width="100%"
							rendered="#{HistoricoContatoControle.apresentarTelefone}">
							<h:outputText styleClass="tituloFormulario"
								value="#{msg_aplic.prt_Agenda_telefones}" />

							<rich:spacer height="10px" />
							<h:dataTable id="resTelefone" width="90%"
								headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
								columnClasses="colunaCentralizada"
								value="#{HistoricoContatoControle.historicoContatoVO.clienteVO.pessoa.telefoneVOs}"
								rendered="#{!empty HistoricoContatoControle.historicoContatoVO.clienteVO.pessoa.telefoneVOs}"
								var="telefone">
								<h:column>
									<f:facet name="header">
										<h:outputText value="#{msg_aplic.prt_Agenda_numero}" />
									</f:facet>
									<h:outputText value="#{telefone.numero}" />
								</h:column>
								<h:column>
									<f:facet name="header">
										<h:outputText value="#{msg_aplic.prt_Agenda_tipoTelefone}" />
									</f:facet>
									<h:outputText value="#{telefone.tipoTelefone_Apresentar}" />
								</h:column>
							</h:dataTable>

							<rich:spacer width="20px" />

							<h:panelGrid columns="1" columnClasses="colunaCentralizada"
								width="100%"
								rendered="#{empty HistoricoContatoControle.historicoContatoVO.clienteVO.pessoa.telefoneVOs}">
								<h:outputText
									value="#{msg_aplic.prt_Agenda_telefoneNaoEncontrado}"
									styleClass="mensagemTelefoneNaoEncontrado" />
							</h:panelGrid>
						</h:panelGrid>

						<h:panelGrid id="mensagemEmail" columns="1" width="100%">
							<h:panelGrid columns="1"
								style="height:10px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
								columnClasses="colunaCentralizada" width="100%"
								rendered="#{HistoricoContatoControle.apresentarComentarioNaoPossueEmail}">
								<h:outputText styleClass="tituloFormulario"
									value="#{msg_aplic.prt_Agenda_emails}" />

								<rich:spacer width="20px" />

								<h:panelGrid columns="1" columnClasses="colunaCentralizada"
									width="100%"
									rendered="#{HistoricoContatoControle.apresentarComentarioNaoPossueEmail}">
									<h:outputText value="#{msg_aplic.prt_Agenda_naoPossueEmail}"
										styleClass="mensagemTelefoneNaoEncontrado" />
								</h:panelGrid>
							</h:panelGrid>
						</h:panelGrid>

						<h:panelGrid columns="1"
							style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
							columnClasses="colunaCentralizada" width="100%"
							rendered="#{HistoricoContatoControle.contatoVO.apresentarComentario}">
							<h:outputText styleClass="tituloFormulario"
								value="#{msg_aplic.prt_Agenda_comentario}" />
							<rich:spacer height="10px" />
							<h:inputTextarea id="comentario"
								value="#{HistoricoContatoControle.contatoVO.observacao}"
								cols="80" rows="5" />
						</h:panelGrid>

						<h:panelGrid id="dadosAgendado"
							rendered="#{AgendaControle.agendaVO.apresentarDadosAgendado}"
							columns="2" width="100%">
							<h:panelGrid columns="1" columnClasses="colunaDireita"
								width="100%">
								<h:outputText styleClass="camposAgendado"
									value="#{msg_aplic.prt_Agenda_agendado}" />
							</h:panelGrid>
							<h:panelGrid columns="1" columnClasses="colunaCentralizada"
								width="100%">
								<h:panelGroup>
									<a4j:outputPanel layout="block">
										<h:outputText styleClass="tituloCampos"
											value="#{msg_aplic.prt_Agenda_dataAgendada}" />
										<rich:spacer width="5" />
										<rich:calendar id="dataAgendado"
											oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
											value="#{AgendaControle.agendaVO.dia}"
											enableManualInput="true" popup="true" inputSize="10"
											datePattern="dd/MM/yyyy" showApplyButton="false"
											cellWidth="24px" cellHeight="24px" style="width:200px"
											inputClass="campos" showFooter="false" readonly="true" />

										<rich:spacer width="20" />
										<h:outputText styleClass="tituloCampos"
											value="#{msg_aplic.prt_Agenda_horaAgendada}" />
										<rich:spacer width="5" />
										<h:inputText readonly="true" size="6"
											value="#{AgendaControle.agendaVO.apresentarMinutoEmHora}" />
									</a4j:outputPanel>
								</h:panelGroup>
							</h:panelGrid>
						</h:panelGrid>

						<h:panelGrid id="dadosObjecao"
							rendered="#{HistoricoContatoControle.contatoVO.apresentarDadosObjecao}"
							columns="2" width="100%">
							<h:panelGrid columns="1" columnClasses="colunaDireita"
								width="100%">
								<h:outputText styleClass="camposAgendado"
									value="#{msg_aplic.prt_Objecao_objecao}" />
							</h:panelGrid>
							<h:panelGrid columns="1" columnClasses="colunaCentralizada"
								width="100%">
								<h:panelGroup>
									<a4j:outputPanel layout="block">
										<h:outputText styleClass="campos"
											value="#{msg_aplic.prt_Objecao_descricaoObjecao}" />
										<rich:spacer width="5" />
										<h:outputText styleClass="campos"
											value="#{HistoricoContatoControle.historicoContatoVO.objecaoVO.descricao}" />
										<rich:spacer width="25" />
										<a4j:commandButton id="btnLimparObjecao"
											title="#{msg.msg_limpar_objecao}"
											image="./imagensCRM/limpar.gif"
											action="#{HistoricoContatoControle.limparObjecao}"
											reRender="form:dadosObjecao, form" />
									</a4j:outputPanel>

								</h:panelGroup>
							</h:panelGrid>
						</h:panelGrid>

						<h:panelGrid columns="1" columnClasses="colunaCentralizada"
							width="100%"
							rendered="#{HistoricoContatoControle.contatoVO.apresentarComentario}">
							<h:panelGroup>
								<a4j:commandButton id="btnIndicacao"
									image="./imagensCRM/botaoIndicacao.png" />
								<rich:spacer width="10" />

								<a4j:commandButton id="btnHistorico"
									reRender="panelGridMensagens"
									oncomplete="#{HistoricoContatoControle.msgAlert}"
									image="./imagensCRM/botaoHistorico.png" />
								<rich:spacer width="10" />

								<a4j:commandButton id="btnAgendar"
									image="./imagensCRM/botaoAgendar.png"
									oncomplete="Richfaces.showModalPanel('panelAgenda')"
									rendered="#{HistoricoContatoControle.contatoVO.apresentarContato}"
									reRender="panelAgenda" />
								<rich:spacer width="10" />

								<a4j:commandButton id="btnObjecao" action="#{HistoricoContatoControle.consultarObjecao}"
									oncomplete="Richfaces.showModalPanel('panelObjecao')"
									rendered="#{HistoricoContatoControle.apresentarBotaoObjecao}"
									image="./imagensCRM/botaoObjecao.png" reRender="panelObjecao" />
								<rich:spacer width="10" />

								<a4j:commandButton id="btnConcluir"
									reRender="panelGridMensagens"
									oncomplete="#{HistoricoContatoControle.msgAlert}"
									action="#{HistoricoContatoControle.gravarHistoricoContatoAgenda}"
									image="./imagensCRM/botaoConcluir.png" />


							</h:panelGroup>
						</h:panelGrid>




						<%--------------------Email-------------------%>

						<h:panelGrid id="panelEmail" columnClasses="colunaDireita"
							columns="1" width="100%" cellpadding="0" cellspacing="0">
							<h:commandLink
								action="#{HistoricoContatoControle.liberarBackingBeanMemoria}"
								id="idLiberarBackingBeanMemoria" style="display: none" />
							<h:panelGrid columnClasses="colunaDireita" columns="1"
								styleClass="tabForm" width="100%"
								rendered="#{HistoricoContatoControle.apresentarEmail}">
								<h:panelGrid columns="1"
									style="height:10px; background-image:url('./imagensCRM/fundoBarraTopo.png');background-repeat: repeat-x;"
									columnClasses="colunaCentralizada" width="100%">
									<h:outputText styleClass="tituloFormulario"
										value="#{msg_aplic.prt_Agenda_enviarEmail}" />
								</h:panelGrid>

								<rich:tabPanel width="100%" activeTabClass="true">
									<rich:tab id="dadosEmail" label="Dados E-mail">
										<h:panelGrid columns="2" styleClass="tabForm" width="100%">

											<h:outputText styleClass="tituloCampos"
												value="#{msg_aplic.prt_MalaDireta_remetente}" />
											<h:panelGroup id="panelRemetente">
												<h:inputText id="textColaboradorResponsavel" size="50"
													readonly="true" styleClass="camposSomenteLeitura"
													value="#{HistoricoContatoControle.malaDiretaVO.remetente.nome}" />
												<rich:suggestionbox height="200" width="200"
													for="textColaboradorResponsavel"
													fetchValue="#{result.nome}"
													suggestionAction="#{HistoricoContatoControle.autocompleteRemetente}"
													minChars="3" rowClasses="20"
													nothingLabel="Nenhum Responsável encontrado !" var="result"
													id="suggestionResponsavel">

													<h:column>
														<h:outputText value="#{result.nome}" />
													</h:column>
												</rich:suggestionbox>
											</h:panelGroup>
											<h:outputText styleClass="tituloCampos"
												value="#{msg_aplic.prt_MalaDireta_modeloMensagem}" />
											<h:panelGroup>
												<h:inputText id="modeloMensagem" size="70" maxlength="100"
													readonly="#{!HistoricoContatoControle.malaDiretaVO.novoObj}"
													styleClass="camposSomenteLeitura"
													value="#{HistoricoContatoControle.malaDiretaVO.modeloMensagem.titulo}" />
												<a4j:commandButton
													oncomplete="Richfaces.showModalPanel('panelModeloMensagem')"
													rendered="#{HistoricoContatoControle.malaDiretaVO.novoObj}"
													image="imagensCRM/informacao.gif"
													alt="#{msg_aplic.prt_MalaDireta_consultarModeloMensagem}" />
												<rich:spacer width="5" />
												<a4j:commandButton id="limparModeloMensagem"
													rendered="#{HistoricoContatoControle.malaDiretaVO.novoObj}"
													action="#{HistoricoContatoControle.limparCampoModeloMensagem}"
													image="imagensCRM/limpar.gif"
													alt="#{msg_aplic.prt_limparCampo}"
													reRender="modeloMensagem" />
											</h:panelGroup>

											<h:outputText styleClass="tituloCampos"
												value="#{msg_aplic.prt_MalaDireta_titulo}" />
											<h:panelGroup>
												<h:inputText id="titulo" size="70" maxlength="100"
													readonly="#{!HistoricoContatoControle.malaDiretaVO.novoObj}"
													styleClass="camposObrigatorios"
													value="#{HistoricoContatoControle.malaDiretaVO.titulo}" />
											</h:panelGroup>
										</h:panelGrid>

										<h:panelGrid columns="1" columnClasses="colunaCentralizada"
											width="100%">
											<h:outputText styleClass="tituloCampos"
												value="#{msg_aplic.prt_MalaDireta_mensagem}" />

											<rich:editor configuration="editorpropriedades"
												viewMode="visual" styleClass="colunaCentralizada"
												readonly="#{!HistoricoContatoControle.malaDiretaVO.novoObj}"
												theme="advanced" id="imputMensagem" height="400" width="250"
												value="#{HistoricoContatoControle.malaDiretaVO.mensagem}" />
										</h:panelGrid>
									</rich:tab>

									<rich:tab id="emailsEnviados" label="Enviar Para:"
										switchType="Client">
										<h:panelGrid columns="1" width="100%"
											headerClass="subordinado" columnClasses="colunaCentralizada">
											<f:facet name="header">
												<h:outputText value="#{msg_aplic.prt_Agenda_listaEmail}" />
											</f:facet>
											<h:panelGrid columns="2" width="100%"
												styleClass="tabFormSubordinada"
												footerClass="colunaCentralizada">
												<h:panelGrid columns="1" width="100%">

													<rich:dataTable id="resultadoConsultaListaEmail"
														width="100%" headerClass="consulta"
														rowClasses="linhaImpar, linhaPar"
														columnClasses="colunaCentralizada"
														value="#{HistoricoContatoControle.listaEmail}" rows="10"
														var="malaDireta">
														<rich:column>
															<f:facet name="header">
																<h:outputText value="#{msg_aplic.prt_Agenda_email}" />
															</f:facet>
															<h:outputText value="#{malaDireta.email}"
																styleClass="campos" />
														</rich:column>
														<h:column>
															<f:facet name="header">
																<h:outputText value="#{msg_bt.btn_opcoes}" />
															</f:facet>
															<h:panelGroup>
																<h:selectBooleanCheckbox id="selecionarEmail"
																	styleClass="campos"
																	value="#{HistoricoContatoControle.historicoContatoVO.escolherEmail}">
																</h:selectBooleanCheckbox>
															</h:panelGroup>
														</h:column>
													</rich:dataTable>
												</h:panelGrid>
											</h:panelGrid>
										</h:panelGrid>
									</rich:tab>
								</rich:tabPanel>
							</h:panelGrid>
						</h:panelGrid>

						<h:panelGrid columns="1"
							rendered="#{HistoricoContatoControle.apresentarEmail}"
							columnClasses="colunaCentralizada" width="100%"
							styleClass="tabMensagens">
							<a4j:commandButton id="btnConcluirEmail"
								reRender="panelGridMensagens"
								oncomplete="#{HistoricoContatoControle.msgAlert}"
								action="#{HistoricoContatoControle.gravarHistoricoContatoAgenda}"
								image="./imagensCRM/botaoConcluir.png" />
						</h:panelGrid>

						<h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
							<h:panelGrid id="panelGridMensagens" columns="1" width="100%">
								<h:outputText styleClass="mensagem"
									value="#{HistoricoContatoControle.mensagem}" />
								<h:outputText styleClass="mensagemDetalhada"
									value="#{HistoricoContatoControle.mensagemDetalhada}" />
							</h:panelGrid>
						</h:panelGrid>
					</h:panelGrid>
				</h:panelGrid>
			</h:panelGrid>
		</h:form>
	</h:panelGrid>
</f:view>
