<%--
    Document   : atestadoContrato
    Created on : 03/08/2009, 09:21:27
    Author     : pedro
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<style type="text/css">
    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        *vertical-align:middle
    }
</style>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-lancar-ferias-para-aluno/"/>
    <c:set var="titulo" scope="session" value="Retorno Atestado"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
    </h:panelGrid>

    <rich:modalPanel id="panel" width="350" height="100"
                     showWhenRendered="#{RetornoAtestadoContratoControle.atestadoContratoVO.mensagemErro}">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Atenção!"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink"/>
                <rich:componentControl for="panel" attachTo="hidelink" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
            <h:panelGroup>
                <h:graphicImage value="/imagens/erro.png"/>
                <rich:spacer width="10" />
                <h:outputText styleClass="mensagemDetalhada" value="#{RetornoAtestadoContratoControle.mensagemDetalhada}"/>
            </h:panelGroup>
        </h:panelGrid>
    </rich:modalPanel>

    <h:form id="form" styleClass="overflow-visible">
        <h:panelGrid columns="2"  width="100%">
            <h:panelGrid columns="1" cellpadding="0" style="margin-left: auto; margin-right: auto; width: 70%;">
                <h:outputText value="NOME DO CLIENTE: " styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold" />
                <h:outputText id="nomeAlunoCar" styleClass="texto-size-14-real texto-cor-cinza-2 texto-font" value="#{RetornoAtestadoContratoControle.atestadoContratoVO.contratoVO.pessoa.nome}"/>
                <br>
                <h:panelGroup styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold">DETALHES DO CONTRATO</h:panelGroup>

                <rich:dataTable id="carencia" width="100%" rowClasses="tablelistras textsmall" columnClasses="col-text-align-left" styleClass="tabelaSimplesCustom" headerClass="col-text-align-left"
                                value="#{RetornoAtestadoContratoControle.listaContratoVOs}" var="contrato">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_numeroContrato}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_plano}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.plano.descricao}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"  value="#{msg_aplic.prt_cancelamentoTrancamento_consultarContrato_dataInicio}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.vigenciaDe_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_trancamentoContrato_consultarContrato_dataTermino}"/>
                        </f:facet>
                        <h:outputText rendered="#{contrato.situacao == 'AT'}" styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.vigenciaAteAjustada_Apresentar}"/>
                        <h:outputText rendered="#{contrato.situacao == 'IN'}" styleClass="red texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.vigenciaAteAjustada_Apresentar}*"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case" value="#{msg_aplic.prt_trancamentoContrato_consultarContrato_valorBaseContrato}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{contrato.valorBaseCalculo}">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </rich:column>
                </rich:dataTable>
                <h:outputText  rendered="#{RetornoAtestadoContratoControle.atestadoContratoVO.contratoVencido}" id="msgContratoVencido" styleClass="fonteTrebuchet" style="color: red"  value="#{msg_aplic.prt_afastamentoContrato_Atestado_ContratoVencido}"/>
                <P> </P>
                <table width="100%" border="0" align="left" cellpadding="0" cellspacing="0" style="margin-right:20px;margin-bottom:5px;padding:5px;">

                    <tr></tr>
                </table>

                <h:panelGrid id="panelBotoes" width="100%" columns="1"  >
                    <h:panelGrid width="100%" style="text-align: right">
                        <h:panelGroup
                                rendered="#{RetornoAtestadoContratoControle.apresentarBotoes && !RetornoAtestadoContratoControle.processandoOperacao}">
                            <h:panelGroup layout="block" styleClass="container-botoes">
                                <a4j:commandLink id="confirmar" reRender="form,panel,panelAutorizacaoFuncionalidade"
                                                 action="#{RetornoAtestadoContratoControle.validarDadosAtestado}"
                                                 styleClass="botaoPrimario texto-size-16-real">
                                    <i class="fa-icon-ok"></i>&nbsp;Confirmar
                                </a4j:commandLink>
                                <rich:spacer width="7"/>
                                <h:commandLink id="cancelar" onclick="fecharJanela();executePostMessage({close: true});"
                                               styleClass="botaoSecundario texto-size-16-real">
                                    <i class="fa-icon-remove"></i>&nbsp;Cancelar
                                </h:commandLink>


                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup
                                rendered="#{!RetornoAtestadoContratoControle.apresentarBotoes && !RetornoAtestadoContratoControle.processandoOperacao}">
                            <h:panelGroup layout="block" styleClass="container-botoes">
                                <h:commandLink id="fechar" onclick="fecharJanela();executePostMessage({close: true});"
                                               styleClass="botaoSecundario texto-size-14-real"
                                               value="Fechar">

                                </h:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>

                    </h:panelGrid>
                </h:panelGrid>

            </h:panelGrid>

        </h:panelGrid>



        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
    </h:form>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
<script>
    window.addEventListener("load", function (event) {
        executePostMessage({loaded: true, message: 'window load'})
    });
    document.getElementById("form:dataTrancamento").focus();
</script>
