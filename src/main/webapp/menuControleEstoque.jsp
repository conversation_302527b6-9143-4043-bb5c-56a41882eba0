<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>


<h:panelGroup layout="block" styleClass="menuLateral">
    <!-- Menu -->
    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-file-alt"></i> Controle de Estoque
        </h:panelGroup>

        <!-- Balan�o -->    
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.cadastrarBalanco or LoginControle.permissaoAcessoMenuVO.cancelarBalanco}">
            <a4j:commandLink value="#{msg_menu.Menu_balanco}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="BALANCO" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Controle_de_Estoque:Balan%C3%A7o"
                          title="Clique e saiba mais: Balan�o" target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>
        <!-- Balan�o -->   
        
        <!-- Compra -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.cadastrarCompra or LoginControle.permissaoAcessoMenuVO.cancelarCompra}">
            <a4j:commandLink value="#{msg_menu.Menu_compra}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="COMPRA" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-cadastrar-as-compras-de-produtos-de-estoque/"
                          title="Clique e saiba mais: Compra" target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>        
        <!-- //Compra -->
        
        <!-- Cardex -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarCardex}">
            <a4j:commandLink value="#{msg_menu.Menu_cardex}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CARDEX" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-acompanhar-as-movimentacoes-do-meu-estoque/"
                          title="Clique e saiba mais: Cardex" target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>                  
        <!-- //Cardex -->
        
        <!-- Produto Estoque -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.configurarProdutoEstoque}">
            <a4j:commandLink value="#{msg_menu.Menu_produtoEstoque}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CONFIGURAR_PRODUTO_ESTOQUE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Controle_de_Estoque:Configurar_Produto_Estoque"
                          title="Clique e saiba mais: Configura��o Produto Estoque" target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>           
        <!-- //Produto Estoque -->
        
        <!-- Posi��o Estoque -->
        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarPosicaoEstoque}">
            <a4j:commandLink value="#{msg_menu.Menu_posicaoEstoque}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="POSICAO_DO_ESTOQUE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-acompanhar-a-quantidade-de-produtos-em-estoque-lancados-no-sistema/"
                          title="Clique e saiba mais: Posi��o do Estoque" target="_blank" >
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>           
        <!-- //Posi��o do Estoque -->
        
        
    </h:panelGroup>   
    <!-- //Menu -->    
</h:panelGroup>

