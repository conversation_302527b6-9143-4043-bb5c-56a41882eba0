<%--
    Author                           : <PERSON>�<PERSON>                             : 20/07/2011
    Objetivo da Tela                 : Pesquisar planos de conta com AutoComplete.
    Em qual tela pode ser usada      : Nas telas onde � necess�rio pesquisar planos de conta com AutoComplete.
    Exemplo 1 para importar esta tela: 
				<%@include file="include_SuggestionPlanoConta.jsp"%>
    Ex. p/ recuperar o produto selecionado: (PlanoContaTO)JSFUtilities.getManagedBeanValue("PlanoContasControle.planoEscolhido")
--%>

<h:inputText id="nomePlanoSelecionado"
           size="50"
           maxlength="50"
           onblur="blurinput(this);"
           onfocus="focusinput(this);"
           styleClass="form"
           value="#{PlanoContasControle.planoNome}" >
    <a4j:support event="onchange" action="#{PlanoContasControle.setarPlanoPaiVazio}" reRender="form"/>
</h:inputText>
<rich:suggestionbox   height="200" width="400"
                      for="nomePlanoSelecionado"
                      status="statusInComponent"
                      immediate="true"
                      suggestionAction="#{PlanoContasControle.executarAutocompletePesqPlanoContas}"
                      minChars="1"
                      rowClasses="linhaImpar, linhaPar"
                      var="result"  id="suggestionResponsavel" >
    <a4j:support event="onselect"
                 reRender="form"
                 action="#{PlanoContasControle.selecionarPlanoContas}" oncomplete="#{rich:element('nomeCentroSelecionado')}.focus();">
    </a4j:support>
    <h:column>
        <f:facet name="header">
            <h:outputText value="Nome"  styleClass="textverysmall"/>
        </f:facet>
        <h:outputText styleClass="textverysmall" value="#{result.descricaoCurta}" />
    </h:column>
    <h:column >
        <f:facet name="header">
            <h:outputText value="Tipo" styleClass="textverysmall"/>
        </f:facet>
        <h:outputText styleClass="textverysmall" value="#{result.tipoPadrao.descricao}" />
    </h:column>
</rich:suggestionbox>
