<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="${contexto}/css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="${contexto}/css/ce.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">var contexto = '${contexto}';</script>
<script type="text/javascript" language="javascript" src="${contexto}/script/ajuda.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<style type="text/css">
    .chk-fa-container-disabled{
         background-image: url('../imagens_flat/checkbox-disabled.png');
    }
</style>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_ItensCampanha_tituloForm}"/>
    </title>
    
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ItensCampanha_tituloForm}"/>
    
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
        <h:form id="form" style="height: auto;overflow: visible;">
            <h:panelGrid columns="1" width="100%" >
                <hr style="border-color: #e6e6e6;">
            </h:panelGrid>
            <h:panelGroup id="panelListaItemCampanha"  layout="block" style="margin-left: 15px;">
                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" style="margin-left: 13px;" value="Campanha Selecionada:" />
                <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" style="margin-left: 5px;" value="#{ItemCampanhaControle.campanhaDuracaoSelecionado.nome}" />
                <rich:dataTable style="width: 100%;margin-top: 15px;" styleClass="tabelaSimplesCustom"
                               id="listaCampanha2"
                               value="#{ItemCampanhaControle.listaItemSemCampanha}"
                               var="itemcampanha">
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{itemcampanha.nomeItem}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.prt_GestaoPersonal_nome}" />
                        </f:facet>
                        <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{itemcampanha.nomeItem}" />
                    </rich:column>
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{itemcampanha.planoDuracaoReferencia.plano}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Codigo Plano Dura��o" />
                        </f:facet>
                        <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{itemcampanha.planoDuracaoReferencia.plano}" />
                    </rich:column>
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{itemcampanha.pontos}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.PONTOS}" />
                        </f:facet>
                        <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{itemcampanha.pontos}" />
                    </rich:column>
                     <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" sortBy="#{itemcampanha.tipoItemNome}" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.tipo_itemCampanha}" />
                        </f:facet>
                        <h:outputText  styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{itemcampanha.tipoItemNome}" />
                    </rich:column>
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="#{msg_aplic.SELECIONAR}" />
                        </f:facet>
                        <h:panelGroup styleClass="chk-fa-container">
                            <h:selectBooleanCheckbox value="#{itemcampanha.selecionarItem}" style="margin-top: -13px;"/>
                            <span/>
                        </h:panelGroup>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="1" width="100%" style="margin-top: 13px;">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="btnAtualizaListaItem" title="Recarregar dados do cliente"
                                               value="Atualizar"
                                               style="display: none !important;"
                                               reRender="panelListaItemCampanha"
                                               action="#{ItemCampanhaControle.atualizarListaItemCampanha}">
                            </a4j:commandButton>
                            <h:outputText value="    "/>
                            <a4j:commandLink reRender="form"
                                             id="salvar"
                                             styleClass="pure-button pure-button-primary"
                                             accesskey="2"
                                             action="#{ItemCampanhaControle.gravarItensNaCampanha}"
                                             oncomplete="#{ItemCampanhaControle.mensagemNotificar}">
                                Gravar
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
                                
    <rich:modalPanel id="mdlExcluirCampanha" styleClass="novaModal" autosized="true" shadowOpacity="true" width="450" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Confirmar Exclus�o Campanha"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formAviso">
            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                
                <h:panelGroup layout="block">
                    <a4j:commandLink value="Sim" action="#{ClubeVantagensControle.excluirCampanha}" reRender="form"
                                       id="confirmacaoOpercaoSim"
                                       oncomplete="#{ClubeVantagensControle.mensagemNotificar};#{ClubeVantagensControle.oncomplete};fireElementFromAnyParent('form:btnAtualizaCampanhaDuracao');"
                                       styleClass="pure-button pure-button-primary"/>
                    <h:outputText value="    "/>
                    <a4j:commandLink value="N�o" onclick="Richfaces.hideModalPanel('mdlExcluirCampanha');" reRender="mdlExcluirCampanha" id="confirmacaoOpercaoNao" styleClass="pure-button"/>
                </h:panelGroup>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
</f:view>
