<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
    setTimeout(setInterval(function () {
        getDocumentCookie('popupsImportante') == 'close' ? this.close() : '';
    }, 500), 500);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Acerto de Débito"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <c:set var="titulo" scope="session" value="Acerto de Débito"/>
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
        <div styleClass="col-md-12">
            <hr class="dividerFundoClaro"/>
        </div>
    </h:panelGrid>

    <h:form id="form">
        <h:panelGrid columns="1" width="100%" styleClass="font-size-em-max">
            <h:panelGrid columns="2" styleClass="font-size-em-max">
                <h:outputText value="NOME DO CLIENTE: "
                              styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"/>
                <h:outputText id="clienteNomeAuto" styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              value="#{ClienteControle.clienteVO.pessoa.nome}"/>
            </h:panelGrid>
            <div styleClass="col-md-12">
                <hr class="dividerFundoClaro"/>
            </div>
            <h:panelGrid columns="3" cellpadding="2" styleClass="font-size-em-max">
                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta"
                              value="#{msg_aplic.prt_GerarParcelaContaCorrenteCliente_valorMaximo}"/>
                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                              value=" #{MovimentoContaCorrenteClienteControle.empresaLogado.moeda} "/>
                <h:outputText id="valorFinal" styleClass="texto-size-14 texto-font" style="color: red"
                              value="#{MovimentoContaCorrenteClienteControle.movimentoContaCorrenteClienteVO.saldoAtual}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
            </h:panelGrid>
            <div styleClass="col-md-12">
                <hr class="dividerFundoClaro"/>
            </div>
            <rich:spacer height="5px"/>
            <h:outputText styleClass="texto-size-14-real texto-font texto-bold" value="VALOR DO ACERTO DE DÉBITO"
                          rendered="#{MovimentoContaCorrenteClienteControle.possuiPermissaoEditarSaldoContaCorrente}"/>
            <rich:spacer height="5px"/>
            <h:panelGrid rendered="#{MovimentoContaCorrenteClienteControle.possuiPermissaoEditarSaldoContaCorrente}"
                         id="panelGroupValorGerarParcela" columns="1" styleClass="font-size-em-max">
                <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold tituloCaixaAlta"
                              value="#{msg_aplic.prt_GerarParcelaContaCorrenteCliente_valorParcela}"/>
                <h:inputText id="valorGerarParcela" size="7"
                             onkeypress="return formatar_moeda(this,'.',',',event);" maxlength="10"
                             value="#{MovimentoContaCorrenteClienteControle.movimentoContaCorrenteClienteVO.valorParcelaDebito}"
                             onfocus="focusinput(this);" styleClass="inputTextClean">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:inputText>
            </h:panelGrid>

            <h:panelGrid id="msgGrPar" columns="2" width="100%" styleClass="font-size-em-max">
                <h:panelGrid columns="1" width="100%">
                    <h:commandButton rendered="#{MovimentoContaCorrenteClienteControle.erro}"
                                     image="./imagens/erro.png"/>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%">
                    <h:outputText id="msgGerarParcelaVerde" styleClass="tituloCamposVerde texto-size-14  texto-font"
                                  value="#{MovimentoContaCorrenteClienteControle.mensagem}"/>
                    <h:outputText id="msgGerarParcelaDeta"
                                  styleClass="tituloCamposNegritoMaiorVermelho texto-size-14 texto-font"
                                  value="#{MovimentoContaCorrenteClienteControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>

            <h:panelGrid id="panelBotoes" width="350px" columns="2" style="text-align:center;"
                         styleClass="font-size-em-max">
                <h:panelGrid width="300px"></h:panelGrid>
                <h:panelGrid width="50px">
                    <h:panelGroup rendered="#{MovimentoContaCorrenteClienteControle.apresentarBoteos}">
                        <rich:spacer width="7"/>
                        <a4j:commandLink id="confirmar"
                                         reRender="form,panelAutorizacaoFuncionalidade,msgGrPar,panelBotoes"
                                         action="#{MovimentoContaCorrenteClienteControle.richConfirmacaoGerarParcela}"
                                         styleClass="pure-button pure-button-primary texto-font"
                                         oncomplete="#{MovimentoContaCorrenteClienteControle.mensagemNotificar}#{MovimentoContaCorrenteClienteControle.msgAlert}">
                            <i class="fa-icon-ok"></i>&nbsp;Confirmar
                        </a4j:commandLink>

                    </h:panelGroup>
                    <h:panelGroup rendered="#{!MovimentoContaCorrenteClienteControle.apresentarBoteos}">
                        <a4j:commandLink id="fechar" value="Fechar Janela" onclick="fecharJanela();"
                                         styleClass="pure-button"/>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </h:form>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
