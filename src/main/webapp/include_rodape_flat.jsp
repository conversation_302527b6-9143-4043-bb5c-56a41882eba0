<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>

<%
    response.setHeader("Cache-Control", "private");
    response.setDateHeader("Expires", System.currentTimeMillis() + 86400000L); // um dia
    response.setDateHeader("Age", System.currentTimeMillis() + 86400000L);
%>

<c:if test="${!SuperControle.menuZwUi}">
<h:panelGroup layout="block" styleClass="caixaRodape">
    <h:panelGroup layout="block" styleClass="caixaRodapeInfo">
        <h:panelGroup layout="block" styleClass="infoInstancia" style="float: left; margin-top: 8px;margin-right: 10px; ">
            <h:panelGrid columns="1" border="0" style="text-align:left;" cellpadding="0">
                <h:panelGroup >
                    <i class="fa-icon-desktop" style="margin-left: 10px; color: #ffffff;font-size: 12px;"></i>
                    <c:if test="${!empty LoginControle.empresa.nome}">
                        <h:outputText rendered="#{LoginControle.empresa.nome != ''}" styleClass="rotuloSmall"
                                      value=" #{LoginControle.empresa.nome}" style="color: #ffffff;"/>
                    </c:if>
                </h:panelGroup>
            </h:panelGrid>
            <h:panelGrid columns="1" border="0" style="text-align:left; color: #ffffff; margin-left: 0px;width: 300px; margin-right: 0px" cellpadding="1">
                <h:panelGroup style="width: 260px;">
                    <label style="color: #ffffff;  margin-right: 3px; padding-right: 0px " class="rotuloSmall"> Vers&atilde;o do Sistema:</label>
                    <h:outputText style="color: #ffffff;  margin-right: 3px; padding-right: 0px "
                                  styleClass="rotuloSmall"
                                  title="#{SuperControle.commitTime}"
                                  value=" #{SuperControle.versaoSistema_Apresentar} #{SuperControle.shortCommitHash}"/>

                       <h:outputText rendered="#{SuperControle.usandoMC != ''}" styleClass="fa-icon-coffee" style="margin-left: -3px; color: #ffffff; font-size:12px;"  title="Memcached ativado"/>

                </h:panelGroup>
            </h:panelGrid>
            <h:panelGrid columns="2" border="0" style="text-align:left; color: #ffffff;" cellpadding="2">
                <h:panelGroup>

                    <h:outputLink styleClass="rotuloSmall" style="color: #ffffff; margin-left: 8px;"
                                   target="_blank" value="#{SuperControle.urlWikiVersaoAtual}">
                        O que h&aacute; de novo?
                    <a4j:support event="onclick" action="#{SuperControle.notificarOAMDDRoodape}"  />
                    </h:outputLink>

                </h:panelGroup>
                <h:panelGroup>
                    <div style="background-color: #ff6e1e; width: 80px; height: 14px; border-radius: 20px;">

                        <h:outputLink styleClass="rotuloSmall" style="color: #ffffff; margin-left: 8px;"
                                      target="_blank" value="#{SuperControle.urlWikiVersaoAtual}">
                            NOVIDADE
                            <a4j:support event="onclick" action="#{SuperControle.notificarOAMDDRoodape}"  />
                        </h:outputLink>
                    </div>

                </h:panelGroup>
            </h:panelGrid>

        </h:panelGroup>
        <h:panelGroup layout="block" styleClass="infoInstancia" style=" float: left;margin-top: 10px;">
            <h:panelGrid columns="1" border="0" style="text-align:left;" cellpadding="0">
                <h:panelGroup>
                    <h:outputText style=" color: #FFFFFF; width: 20px; padding-right:0px" styleClass="rotuloSmall" value="IP: "/>
                    <h:outputText style=" color: #FFFFFF;padding-left:0px" styleClass="rotuloSmall" value="#{SuperControle.ipCliente}"/>
                </h:panelGroup>
                <h:panelGroup>
                    <label style=" color: #FFFFFF;" class="rotuloSmall">Inst&acirc;ncia: </label>
                    <h:outputText style=" color: #FFFFFF;" rendered="#{SuperControle.instance != ''}" styleClass="rotuloSmall"
                                  value="#{SuperControle.instance}"/>
                </h:panelGroup>
            </h:panelGrid>

        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup layout="block" styleClass="caixaRodapeMidia" style="float:right;width: 350px;">
        <h:panelGroup styleClass="caixaMidiasPacto" style="width: 0px; margin-top: 35px; margin-right: 0px; ">
            <h:panelGroup layout="block" style="margin:5% 0 0 5%; width: 275px;">
                <label style="color: #FFFFFF;" class="tituloDireitos rotuloSmall">&copy; 2022 - Pacto Solu&ccedil;&otilde;es Tecnol&oacute;gicas LTDA</label>
            </h:panelGroup>
        </h:panelGroup>
        <h:panelGroup layout="block" styleClass="caixaSitePacto" style="margin-left: 5px; margin-right: 5px; width: 130px;">
            <h:outputLink value="#{SuperControle.linkSite}" target="_blank">
                <img src="${root}/imagens_flat/pct-icone-fundo-pacto.svg" style="width: 146px;height: 32px;margin-left: 10%;margin-top: 25px;"/>
            </h:outputLink>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>
</c:if>
