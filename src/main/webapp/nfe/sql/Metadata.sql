Use DBNFSe
GO

/*

Use DBNFSe
drop table NotasCanceladas
drop table RetornoLote
drop table DeducaoRPS
drop table ItemRPS
drop table LogOperacaoRPS
drop table OperacaoRPS
drop table RPS
drop table Lote
drop table Usuario
drop table PerfilUsuario
drop table Empresa
drop table Cidade
drop table Arquivo
drop table Codigo

*/

Create Table Codigo(
  Id_Codigo integer identity (1, 1) primary key,
  Identificador varchar(50),
  ProximoCodigo bigint
)

GO

Create Table Arquivo(
  Id_Arquivo integer identity (1, 1) primary key,
  Id_PKRef integer,
  Identificador varchar(50),
  Arquivo image
)

GO

Create Table Cidade(
  Id_Cidade integer identity(1, 1) primary key,
  CodSIAFI integer,
  CodIBGE integer,
  Nome varchar(100),
  UF varchar(2),
  Homologada bit not null default 0
)

INSERT INTO Cidade(CodSIAFI, CodIBGE, Nome, UF, Homologada) VALUES(6001, 3304557, 'RIO DE JANEIRO', 'RJ', 1);
INSERT INTO Cidade(CodSIAFI, CodIBGE, Nome, UF, Homologada) VALUES(9051, 5002704, 'CAMPO GRANDE', 'MS', 0);

GO

Create Table Empresa(
  Id_Empresa integer identity(1, 1) primary key,
  Id_Cidade integer,
  Chave varchar(100),
  CPFCNPJ varchar(15),
  InscricaoMunicipal varchar(20),
  RazaoSocial varchar(120),
  Ativa bit not null default 1,
  DDDTelefone integer,
  Telefone varchar(10),
  SenhaCertificado varchar(100),
  RegimeEspecialTributacao int,
  OptanteSimplesNacional bit,
  IncentivadorCultural bit
)

Alter Table Empresa add Constraint FK_Empresa_Cidade Foreign Key(Id_Cidade) References Cidade(Id_Cidade)

INSERT INTO Empresa (Id_Cidade, Chave, CPFCNPJ, InscricaoMunicipal, RazaoSocial, Ativa, DDDTelefone, Telefone, SenhaCertificado)
Values (1, '6b86b273ff34fce19d6b804eff5a3f5747ada4eaa22f1d49c01e52ddb7875b4b', '99999999999999', '12345678', 'Empresa do NFSe', 1, 62, '30031234', '12345678');

GO

Create Table PerfilUsuario(
  Id_PerfilUsuario integer identity(1, 1) primary key,
  Id_Empresa integer,
  Nome varchar(100),
  PermissaoCRUDUsuario bit not null default 0,
  PermissaoIncluirEmpresa bit not null default 0,
  PermissaoAlterarEmpresa bit not null default 0,
  PermissaoAlterarEmpresaBasico bit not null default 0,
  PermissaoCancelamentoNota bit not null default 0
  PermissaoCRUDPerfil bit not null default 0
)

Alter Table PerfilUsuario add Constraint FK_PerfilUsuario_Empresa Foreign Key(Id_Empresa) References Empresa(Id_Empresa)

GO

Create Table Usuario(
  Id_Usuario integer identity(1, 1) primary key,
  Id_PerfilUsuario integer,
  Nome varchar(100),
  Usuario varchar(30),
  Senha varchar(100),
  Status varchar(1)
)

Alter Table Usuario add Constraint
FK_Usuario_PerfilUsuario Foreign Key(Id_PerfilUsuario) References PerfilUsuario(Id_PerfilUsuario)

INSERT INTO Usuario (Nome, Usuario, Senha, Status) VALUES ('Administrador', 'admin', 'cb7da04de21e', 1);

GO

Create Table Lote(
  Id_Lote integer primary key,
  Id_Empresa integer,
  DataIni DateTime,
  DataFim DateTime,
  QtdRPS integer,
  ValorTotalServicos numeric(18, 2),
  ValorTotalDeducoes numeric(18, 2)
)

Alter Table Lote add Constraint FK_Lote_Empresa Foreign Key(Id_Empresa) References Empresa(Id_Empresa)

INSERT INTO Lote (Id_Lote ,Id_Empresa, DataIni, DataFim, QtdRPS, ValorTotalServicos, ValorTotalDeducoes)
VALUES (1, 1, GETDATE(), GETDATE(), 1, 1000, 100);

GO

Create Table RPS(
  Id_RPS integer primary key,
  Id_Lote integer,  
  Status varchar(20),
  DataEmissao DateTime,
  NaturezaOperacao varchar(1),
  SerieRPS varchar(10),
  NumeroRPS integer,
  NumeroNota integer,
  DataProcessamento DateTime,
  InscricaoMunicipalCons varchar(20),
  CPFCNPJCons varchar(15),
  RazaoSocialCons varchar(120),
  TipoLogradouroCons varchar(10),
  LogradouroCons varchar(50),
  NumeroEnderecoCons varchar(9),
  ComplementoEnderecoCons varchar(30),
  TipoBairroCons varchar(10),
  BairroCons varchar(50),
  Id_CidadeCons integer,
  CidadeDescricaoCons varchar(50),
  CEPCons varchar(8),
  EmailCons varchar(60),
  CodAtividade varchar(20),
  ItemListaServico varchar(5),
  CodigoCnae varchar(7),
  CodigoTributacaoMunicipio varchar(20),
  AliquotaAtividade numeric(18, 4),
  TipoRecolhimento varchar(1),
  Id_CidadePrest integer,
  CidadeDescricao varchar(50),
  Tributacao varchar(1),
  ValorServicos numeric(18, 2),
  ValorDeducoes numeric(18, 2),
  ValorTotal numeric(18, 2),
  ValorLiquido numeric(18, 2),
  ValorPIS numeric(18, 2),
  ValorCOFINS numeric(18, 2),
  ValorINSS numeric(18, 2),
  ValorIR numeric(18, 2),
  ValorCSLL numeric(18, 2),
  AliquotaPIS numeric(18, 4),
  AliquotaCOFINS numeric(18, 4),
  AliquotaINSS numeric(18, 4),
  AliquotaIR numeric(18, 4),
  AliquotaCSLL numeric(18, 4),
  ISSRetido int,
  OutrasRetencoes numeric(18, 2),
  BaseCalculo numeric(18, 2),
  ValorISSRetido numeric(18, 2),
  DescontoIncondicionado numeric(18, 2),
  DescontoCondicionado numeric(18, 2),
  Descricao varchar(2000)
)

Alter Table RPS add Constraint
FK_RPS_Lote Foreign Key (Id_Lote) References Lote(Id_Lote)

Alter Table RPS add Constraint
FK_RPS_CidadeCons Foreign Key (Id_CidadeCons) References Cidade(Id_Cidade)

Alter Table RPS add Constraint
FK_RPS_CidadePrest Foreign Key (Id_CidadePrest) References Cidade(Id_Cidade)

INSERT INTO RPS(Id_RPS, Id_Lote, Status, Id_CidadeCons, Id_CidadePrest, SerieRPS, RazaoSocialCons, DataEmissao, DataProcessamento, NumeroNota, ValorTotal, CPFCNPJCons)
VALUES (1, 1, 'Autorizado', 1, 1, 'NF', 'Raz�o Social', GETDATE(), GETDATE(), 1, 1500, '99999999000191');

GO

Create Table OperacaoRPS(
  Id_OperacaoRPS integer identity(1, 1) primary key,
  Id_RPS integer,
  Id_Usuario integer,
  DataHora datetime,
  Operacao varchar(30),
  Descricao varchar(1500),
  Finalizado bit NOT NULL default 0
)

Alter Table OperacaoRPS add Constraint
FK_OperacaoRPS_RPS Foreign Key (Id_RPS) References RPS(Id_RPS)

CREATE INDEX IX_OperacaoRPS_Operacao ON OperacaoRPS(
  Id_RPS ASC,
  Operacao ASC,
  DataHora ASC,
  Sucesso ASC
)

GO

Create Table LogOperacaoRPS(
  Id_LogOperacaoRPS integer identity(1, 1) primary key,
  Id_OperacaoRPS integer,
  DataHora datetime,
  Resultado varchar(8000)
)

Alter Table LogOperacaoRPS add Constraint
FK_LogOperacaoRPS_OperacaoRPS Foreign Key (Id_OperacaoRPS) References OperacaoRPS(Id_OperacaoRPS)

CREATE INDEX IX_LogOperacaoRPS ON LogOperacaoRPS(
  Id_OperacaoRPS ASC,
  DataHora DESC
)

GO

Create Table ItemRPS(
  Id_ItemRPS integer primary key,
  Id_RPS integer,
  Ordem integer,
  ValorUnitario numeric(18, 4),
  Quantidade numeric(18, 4),
  Descricao varchar(250),
  Tributavel varchar(1) default 'S'
)

Alter Table ItemRPS add Constraint
FK_ItemRPS_RPS Foreign Key (Id_RPS) References RPS(Id_RPS)

GO

Create Table DeducaoRPS(
  Id_DeducaoRPS integer primary key,
  Id_RPS integer,
  Ordem integer,
  DeducaoPor varchar(10),
  TipoDeducao varchar(255),
  CPFCNPJReferencia varchar(15),
  NrNFReferencia varchar(10),
  ValorTotalReferencia numeric(18, 4),
  PercentualDeduzir numeric(18, 4),
  ValorDeduzir numeric(18, 4)
)

Alter Table DeducaoRPS add Constraint
FK_DeducaoRPS_RPS Foreign Key (Id_RPS) References RPS(Id_RPS)

GO

Create Table RetornoLote(
	Id_RetornoLote int identity(1,1) primary key,
	Id_RPS int,
	DataHora datetime,
	Status varchar(1),
	NrLote int,
	Protocolo varchar(50),
	Mensagem varchar(200)
)

Alter Table RetornoLote add Constraint
FK_RetornoLote_RPS Foreign Key (Id_RPS) References RPS(Id_RPS)

GO

Create Table NotasCanceladas(
  Id_NotaCancelada integer identity(1, 1) primary key,
  Id_RPS integer,
  MotivoCancelamento varchar(80),
  CodVerificacao varchar(255)
)

Alter Table NotasCanceladas add Constraint
FK_NotasCanceladas_Notas Foreign Key (Id_RPS) references RPS(Id_RPS)

GO

/*

insert into Empresa(
  Id_Cidade,
  CPFCNPJ,
  InscricaoMunicipal,
  RazaoSocial,
  DDDTelefone,
  Telefone)
values(1, '98576194104', '123', 'FRANCISCO GONTIJO RODRIGUES', '62', '3251-5820')

select * from Cidade order by Nome

set identity_insert empresa on
Insert Into Empresa (Id_Empresa, Id_Cidade, Ativa) Values (1,1,1);
set identity_insert empresa off

set identity_insert PerfilUsuario on
Insert Into PerfilUsuario(Id_PerfilUsuario, Id_Empresa, Administrador, PermissaoCRUDEmpresas, PermissaoCRUDUsuario, PermissaoCancelamentoNota) Values (1,1,1,1,1,1);
set identity_insert PerfilUsuario off

set identity_insert Usuario on
Insert Into Usuario (Id_Usuario, Id_PerfilUsuario, Nome, Usuario, Senha, Status) Values (1,1,'admin','admin','d96b8eb876ab',1);
set identity_insert Usuario off


SELECT UF, Nome
FROM


DECLARE TABLE @Cidades(
  UF varchar(2),
  Nome varchar(100)
SELECT E.NOME_UF UF, UPPER(M.Nome_Munic�pio) Nome
  FROM OPENROWSET( 'MSDASQL'
                 , 'Driver={Microsoft Access Text Driver (*.txt, *.csv)};DBQ=D:\XiquiN\Documentos\Pacto\NFSe;'
                 , 'SELECT * from [Tabela de municipios IBGE.csv]') M
INNER JOIN OPENROWSET( 'MSDASQL'
                 , 'Driver={Microsoft Access Text Driver (*.txt, *.csv)};DBQ=D:\XiquiN\Documentos\Pacto\NFSe;'
                 , 'SELECT * from [Tabela de UF IBGE.csv]') E ON M.UF = E.UF

UNION

SELECT UF, Nome
  FROM OPENROWSET( 'MSDASQL'
                 , 'Driver={Microsoft Access Text Driver (*.txt, *.csv)};DBQ=D:\XiquiN\Documentos\Pacto\NFSe;'
                 , 'SELECT * from Tabela_Municipios_SIAFI.csv')
)


SELECT *
  FROM OPENROWSET( 'MSDASQL'
                 , 'Driver={Microsoft Access Text Driver (*.txt, *.csv)};DBQ=D:\XiquiN\Documentos\Pacto\NFSe;'
                 , 'SELECT * from [Tabela de municipios IBGE.csv]')
WHERE UF_MUNIC NOT IN (
SELECT UF_MUNIC
  FROM OPENROWSET( 'MSDASQL'
                 , 'Driver={Microsoft Access Text Driver (*.txt, *.csv)};DBQ=D:\XiquiN\Documentos\Pacto\NFSe;'
                 , 'SELECT * from Tabela_Municipios_SIAFI.csv') S
INNER JOIN (SELECT E.UF, E.NOME_UF, M.UF_MUNIC, M.Nome_Munic�pio
  FROM OPENROWSET( 'MSDASQL'
                 , 'Driver={Microsoft Access Text Driver (*.txt, *.csv)};DBQ=D:\XiquiN\Documentos\Pacto\NFSe;'
                 , 'SELECT * from [Tabela de municipios IBGE.csv]') M
INNER JOIN OPENROWSET( 'MSDASQL'
                 , 'Driver={Microsoft Access Text Driver (*.txt, *.csv)};DBQ=D:\XiquiN\Documentos\Pacto\NFSe;'
                 , 'SELECT * from [Tabela de UF IBGE.csv]') E ON M.UF = E.UF) I ON S.NOME = I.Nome_Munic�pio AND S.UF = I.NOME_UF
)

SELECT *
  FROM OPENROWSET( 'MSDASQL'
                 , 'Driver={Microsoft Access Text Driver (*.txt, *.csv)};DBQ=D:\XiquiN\Documentos\Pacto\NFSe;'
                 , 'SELECT * from Tabela_Municipios_SIAFI.csv')

SELECT *
  FROM OPENROWSET( 'MSDASQL'
                 , 'Driver={Microsoft Access Text Driver (*.txt, *.csv)};DBQ=D:\XiquiN\Documentos\Pacto\NFSe;'
                 , 'SELECT * from [Tabela de municipios IBGE.csv]')

SELECT *
  FROM OPENROWSET( 'MSDASQL'
                 , 'Driver={Microsoft Access Text Driver (*.txt, *.csv)};DBQ=D:\XiquiN\Documentos\Pacto\NFSe;'
                 , 'SELECT * from [Tabela de UF IBGE.csv]')
*/
