<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<f:view>
    <jsp:include page="include_head.jsp" flush="true"/>

    <h:form id="form" style="margin-bottom: 0">

        <h:panelGroup layout="block" styleClass="caixaCorpo">
            <h:panelGroup layout="block" style="height: 80%;width: 100%">
                <h:outputText value="#{HealthControle.healthText}"/>
            </h:panelGroup>
        </h:panelGroup>

    </h:form>
</f:view>

