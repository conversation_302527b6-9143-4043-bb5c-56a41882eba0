<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />

<style type="text/css">
    .pure-button{
        font-family: sans-serif;
        font-size:100%;
        margin:0;
        vertical-align:baseline;
        *vertical-align:middle
    }
</style>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Atestado de Aptidão Física"/>
    </title>
<a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />

<h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
    <c:set var="titulo" scope="session" value="Atestado de Aptidão Física"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <div styleClass="col-md-12"><hr class="dividerFundoClaro"/></div>

    <h:form id="form">
        <a4j:keepAlive beanName="AtestadoControle"/>
        <h:panelGrid columns="1" cellpadding="0" styleClass="font-size-em-max">

            <h:outputText value="NOME DO CLIENTE: " styleClass=" texto-size-14 texto-cor-cinza texto-font texto-bold"/>
            <h:outputText id="nomeSolicitanteAtestado"  styleClass="texto-size-14-real texto-cor-cinza texto-font"
                          value="#{AtestadoControle.cliente.pessoa}"/>
            <rich:spacer height="10px"/>
            <h:panelGrid styleClass="font-size-em-max">
                <h:outputText value="DADOS DO ATESTADO" styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>
                   <h:panelGrid columns="1" id="produto" styleClass="font-size-em-max">
                    <h:outputText styleClass=" texto-size-14 texto-cor-cinza texto-font texto-bold" value="PRODUTO"/>
                       <h:panelGroup styleClass="font-size-em-max">
                       <div class="cb-container margenVertical" style="width: 300px;">
                            <h:selectOneMenu id="produtoAtes"
                                             styleClass="form texto-size-12-real texto-cor-cinza texto-font" onblur="blurinput(this);"  onfocus="focusinput(this);" disabled="#{AtestadoControle.edicaoDeAtestado}" value="#{AtestadoControle.atestado.codigo}">
                                <f:selectItems value="#{AtestadoControle.produtosAtestados}"/>
                            </h:selectOneMenu>
                       </div>
                       </h:panelGroup>
                </h:panelGrid>
                <h:panelGrid columns="3" id="datas">
                    <h:panelGrid columns="1" styleClass="font-size-em-max">
                        <h:outputText styleClass=" texto-size-14 texto-cor-cinza texto-font texto-bold" value="DATA INÍCIO"/>
                        <h:panelGroup>
                            <h:panelGroup styleClass="dateTimeCustom">
                                <rich:calendar id="dataInicio"
                                               value="#{AtestadoControle.dataInicio}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               onchanged="gerarPeriodoRetorno(#{rich:component('dataInicio')}.getCurrentDate(),#{rich:component('dataTermino')}.getCurrentDate(),0);return mascara(this.form, this.id, '99/99/9999', event);"
                                               oninputchange="gerarPeriodoRetorno(#{rich:component('dataInicio')}.getCurrentDate(),#{rich:component('dataTermino')}.getCurrentDate(),0);return mascara(this.form, this.id, '99/99/9999', event);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               zindex="2"
                                               showWeeksBar="false">
                                </rich:calendar>
                            </h:panelGroup>
                            <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                            <%--ajax function para fazer o binding das datas inicio e fim executando os javascripts antes de cada componente--%>
                            <a4j:jsFunction name="gerarPeriodoRetorno"
                                            action="#{AtestadoControle.gerarPeriodoRetornoAtestado}"
                                            reRender="datas">
                                <a4j:actionparam name="dtinicio" assignTo="#{AtestadoControle.dataInicio}"/>
                                <a4j:actionparam name="dtfim" assignTo="#{AtestadoControle.dataFinal}"/>
                                <a4j:actionparam name="numero" assignTo="#{AtestadoControle.nrDias}"/>
                            </a4j:jsFunction>
                        </h:panelGroup>
                    </h:panelGrid>
                    <h:panelGrid columns="1" styleClass="font-size-em-max">
                        <h:outputText styleClass=" texto-size-14 texto-cor-cinza texto-font texto-bold" value="NÚMERO DE DIAS"/>
                        <h:panelGroup>
                            <h:inputText size="12" maxlength="3"
                                         value="#{AtestadoControle.nrDias}"
                                         onblur="gerarPeriodoRetorno(#{rich:component('dataInicio')}.getCurrentDate(),#{rich:component('dataTermino')}.getCurrentDate(),document.getElementById('form:nrDias'));"
                                         id="nrDias"
                                         styleClass="inputTextClean">
                            </h:inputText>
                        </h:panelGroup>
                    </h:panelGrid>
                    <h:panelGrid columns="1" styleClass="font-size-em-max">
                    <h:outputText styleClass=" texto-size-14 texto-cor-cinza texto-font texto-bold" value="DATA FIM"/>
                        <h:panelGroup>
                            <h:panelGroup styleClass="dateTimeCustom">
                                <rich:calendar id="dataTermino"
                                               value="#{AtestadoControle.dataFinal}"
                                               inputSize="10"
                                               inputClass="form"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               onchanged="gerarPeriodoRetorno(#{rich:component('dataInicio')}.getCurrentDate(),#{rich:component('dataTermino')}.getCurrentDate(),0);"
                                               oninputchange="gerarPeriodoRetorno(#{rich:component('dataInicio')}.getCurrentDate(),#{rich:component('dataTermino')}.getCurrentDate(),0);"
                                               oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                               datePattern="dd/MM/yyyy"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false">
                                </rich:calendar>
                            </h:panelGroup>
                            <h:message for="dataTermino" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                       </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
            <h:panelGrid columns="1">
                <h:outputText value="OBSERVAÇÃO" styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>
                <h:inputTextarea id="observacao" styleClass="texto-size-14-real texto-cor-cinza texto-font" disabled="#{AtestadoControle.edicaoDeAtestado}" value="#{AtestadoControle.observacao}" rows="4" cols="115"/>
            </h:panelGrid>
            <rich:spacer height="10px"/>
            <h:panelGroup id="pnlUpload" layout="block" style="margin-left: 4px">
                <h:panelGroup rendered="#{!AtestadoControle.apresentarUploadArquivo}">
                    <h:outputText value="ATESTADO ANEXADO: "
                                  rendered="#{AtestadoControle.existeArquivo}"
                                  styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>
                    <h:commandLink id="atestado" style="margin-left: 12px"
                                   rendered="#{AtestadoControle.existeArquivo}"
                                     actionListener="#{AtestadoControle.downloadAtestadoListener}"
                                     value="#{AtestadoControle.downloadAtestado}"/>

                </h:panelGroup>

                <h:panelGroup rendered="#{AtestadoControle.apresentarUploadArquivo}">
                    <h:outputText value="ANEXAR ATESTADO"
                                  styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>
                    <rich:fileUpload id="upload"
                                     listHeight="60"
                                     listWidth="825"
                                     noDuplicate="false"
                                     fileUploadListener="#{AtestadoControle.upload}"
                                     maxFilesQuantity="1"
                                     allowFlash="false"
                                     immediateUpload="true"
                                     acceptedTypes="jpg, jpeg, gif, png, bmp, pdf, JPG, JPEG, GIF, PNG, BMP, PDF"
                                     addControlLabel="Adicionar"
                                     cancelEntryControlLabel="Cancelar"
                                     doneLabel="Pronto"
                                     sizeErrorLabel="Arquivo não Enviado. Excedeu o tamanho permitido."
                                     progressLabel="Enviando"
                                     clearControlLabel="Limpar"
                                     clearAllControlLabel="Limpar todos"
                                     stopControlLabel="Parar"
                                     uploadControlLabel="Enviar"
                                     transferErrorLabel="Falha de Transmissão"
                                     stopEntryControlLabel="Parar">
                        <a4j:support event="onadd" reRender="panelMensagem"/>
                        <a4j:support event="onerror" reRender="pnlUpload, panelMensagem"/>
                        <a4j:support event="onupload" reRender="panelMensagem"/>
                        <a4j:support event="onuploadcomplete" reRender="panelMensagem"/>
                        <a4j:support event="onclear" reRender="pnlUpload"/>
                    </rich:fileUpload>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGrid id="panelMensagem" columns="3" width="100%">
                <h:panelGrid columns="1" width="100%">
                    <h:outputText value=" "/>
                </h:panelGrid>
                <h:commandButton rendered="#{AtestadoControle.sucesso}" image="./imagens/sucesso.png"/>
                <h:commandButton rendered="#{AtestadoControle.erro}" image="./imagens/erro.png"/>
                <h:panelGrid columns="1" width="100%">
                    <h:outputText id="msgAtesMedico" styleClass="mensagem"
                                  value="#{AtestadoControle.mensagem}"/>
                    <h:outputText id="msgAtesMedicoDet" styleClass="mensagemDetalhada"
                                  value="#{AtestadoControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>
            <h:panelGrid id="panelBotoes" width="400px" columns="2">
                <h:panelGrid width="350px">
                    <h:panelGroup rendered="#{AtestadoControle.apresentarBotoes}">
                        <a4j:commandLink  id="confirmar"
                                          reRender="panelAutorizacaoFuncionalidade, form"
                                          action="#{AtestadoControle.acaoLancarAtestado}" styleClass="pure-button pure-button-primary">
                            <i class="fa-icon-ok"></i>&nbsp;Confirmar
                        </a4j:commandLink>

                        <rich:spacer width="7"/>
                        <h:commandLink id="cancelar" onclick="fecharJanela();" styleClass="pure-button">
                            <i class="fa-icon-remove"></i>&nbsp;Cancelar
                        </h:commandLink>
                    </h:panelGroup>
                    <h:panelGroup rendered="#{!AtestadoControle.apresentarBotoes}">

                        <a4j:commandLink id="imprimirParQ"
                                         rendered="#{AtestadoControle.atestadoVO.avaliacaoFisicaTW != 0}"
                                         reRender="panelAutorizacaoFuncionalidade, form"
                                         oncomplete="#{AtestadoControle.onCompleteImprimir}"
                                         action="#{AtestadoControle.imprimirParQ}"
                                         styleClass="pure-button pure-button-primary">
                            <i class="fa-icon-print"></i>&nbsp;Imprimir
                        </a4j:commandLink>

                        <h:commandLink id="fechar" title="Fechar Janela" onclick="fecharJanela();" styleClass="pure-button">
                            <i class="fa-icon-remove"></i>&nbsp;Fechar
                        </h:commandLink>
                    </h:panelGroup>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
    </h:form>

</h:panelGrid>
<%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
<script>
    document.getElementById("form:dataInicio").focus();
</script>

