<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style>
    .imprimirRelatorio:hover {
        color: #29abe2 !important;
    }
</style>


<f:view>   
    <%@include file="includes/include_carregando_ripple.jsp" %>
    <title>
        <h:outputText value="Relatório Estoque de Produto"/>
    </title>
    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ConfiguracaoEstoque_relEstoqueProduto}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-acompanhar-a-quantidade-de-produtos-em-estoque-lancados-no-sistema/"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:commandLink action="#{RelatorioEstoqueProdutoControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />

            <h:panelGrid columns="1"  width="100%" >
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">


                        <h:outputText styleClass="tituloCampos"  value="Posição do Estoque:" ></h:outputText>
                        <h:selectOneRadio id="posicaoEstoque" styleClass="tituloCampos"  value="#{RelatorioEstoqueProdutoControle.tipoVisualizacao}" >
                            <f:selectItem itemLabel="Todos Produtos" itemValue="1"></f:selectItem>
                            <f:selectItem itemLabel="Somente Produtos com estoque mínimo" itemValue="2"></f:selectItem>
                       </h:selectOneRadio>

                       <h:outputText styleClass="tituloCampos" value="Valor Impresso"></h:outputText>
                       <h:selectOneRadio id="valorImpresso" styleClass="tituloCampos" value="#{RelatorioEstoqueProdutoControle.valorImpresso}">
                           <f:selectItem itemLabel="Preço de Venda" itemValue="1"></f:selectItem>
                           <f:selectItem itemLabel="Custo da última Compra" itemValue="2"></f:selectItem>
                       </h:selectOneRadio>

                        <h:outputText  style="padding-right:5px; padding-left:5px; vertical-align: middle; "  styleClass="tituloCampos" value="Categoria:" />
                        <h:selectOneMenu  id="comboCategoria" onblur="blurinput(this);"
                                          onfocus="focusinput(this);"
                                          style = "vertical-align: middle; width: 150px;"
                                          styleClass="tituloCampos esquerda"
                                          value="#{RelatorioEstoqueProdutoControle.codigoCategoria}" >
                            <f:selectItems  value="#{RelatorioEstoqueProdutoControle.listaSelectItemCategoria}" />
                        </h:selectOneMenu>

                      <h:outputText  style="padding-right:5px; padding-left:5px; vertical-align: middle; "  styleClass="tituloCampos" value="Status Produto:" />
                        <h:selectOneMenu  id="comboStatus" onblur="blurinput(this);"
                                          onfocus="focusinput(this);"
                                          style = "vertical-align: middle; width: 150px;"
                                          styleClass="tituloCampos esquerda"
                                          value="#{RelatorioEstoqueProdutoControle.statusProduto}" >
                            <f:selectItem itemLabel="Todos" itemValue="0"></f:selectItem>
                            <f:selectItem itemLabel="Ativo" itemValue="1"></f:selectItem>
                            <f:selectItem itemLabel="Inativo" itemValue="2"></f:selectItem>
                        </h:selectOneMenu>
                      <h:outputText  style="padding-right:5px; padding-left:5px; vertical-align: middle; "  styleClass="tituloCampos" value="Ordenar Por:" />
                        <h:selectOneMenu  id="comboOrdenacao" onblur="blurinput(this);"
                                          onfocus="focusinput(this);"
                                          style = "vertical-align: middle; width: 150px;"
                                          styleClass="tituloCampos esquerda"
                                          value="#{RelatorioEstoqueProdutoControle.ordenacao}" >
                            <f:selectItem itemLabel="Nome do Produto" itemValue="1"></f:selectItem>
                            <f:selectItem itemLabel="Código do Produto" itemValue="2"></f:selectItem>
                            <f:selectItem itemLabel="Quantidade de Estoque" itemValue="3"></f:selectItem>
                        </h:selectOneMenu>

                        <h:outputText  style="padding-right:5px; padding-left:5px; vertical-align: middle; " rendered="#{RelatorioEstoqueProdutoControle.usuarioLogado.administrador}" styleClass="tituloCampos" value="Empresa:" />
                        <h:panelGroup id="panelEmpresa">
                            <h:selectOneMenu  id="codigoEmpresa" onblur="blurinput(this);"
                                              rendered="#{RelatorioEstoqueProdutoControle.usuarioLogado.administrador}"
                                              onfocus="focusinput(this);"
                                              style = "vertical-align: middle; width: 150px;"
                                              styleClass="tituloCampos esquerda"
                                              value="#{RelatorioEstoqueProdutoControle.codigoEmpresa}" >
                                <f:selectItems  value="#{RelatorioEstoqueProdutoControle.listaSelectItemEmpresa}" />
                            </h:selectOneMenu>
                            <a4j:commandButton  rendered="#{RelatorioEstoqueProdutoControle.usuarioLogado.administrador}" id="atualizar_Balanco_empresa" style="vertical-align: middle; " action="#{RelatorioEstoqueProdutoControle.montarListaSelectItemEmpresa}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:balanco_empresa"/>
                        </h:panelGroup>


                </h:panelGrid>

                
                <h:panelGrid id ="panelMensagem"  columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton  rendered="#{RelatorioEstoqueProdutoControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{RelatorioEstoqueProdutoControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{RelatorioEstoqueProdutoControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{RelatorioEstoqueProdutoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
                

                    <h:panelGroup layout="block" styleClass="container-botoes ">
                        <a4j:commandLink id="imprimirRelatorio"
                                         title="Imprimir Relatório"
                                         styleClass="botaoPrimario texto-cor-branco texto-size-16"
                                         reRender="panelMensagem,imprimirRelatorio"
                                         disabled="#{RelatorioEstoqueProdutoControle.processandoOperacao}"
                                         action="#{RelatorioEstoqueProdutoControle.imprimirRelatorioPDF}"
                                         oncomplete="#{!RelatorioEstoqueProdutoControle.erro} ? abrirPopupPDFImpressao('relatorio/#{RelatorioEstoqueProdutoControle.nomeArquivoRelatorioGeradoAgora}','', 780, 595) : '' ;">
                            <i class="fa-icon-print"></i>
                        </a4j:commandLink>

                    </h:panelGroup>

            </h:panelGrid>


        </h:form>
    </h:panelGrid>
</f:view>


